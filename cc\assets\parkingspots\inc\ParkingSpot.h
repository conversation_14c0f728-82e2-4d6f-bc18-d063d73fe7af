//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ParkingSpot.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_PARKINGSPOTS_PARKINGSPOT_H
#define CC_ASSETS_PARKINGSPOTS_PARKINGSPOT_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Geode>
#include <osg/MatrixTransform>
#include <osg/NodeCallback>
#include <osgAnimation/EaseMotion>


namespace cc
{
namespace assets
{
namespace parkingspots
{

  enum ESpotSide : vfc::uint8_t
  {
    LEFTSIDE = 0u,
    RIGHTSIDE= 1u
  };

class ParkingSpot;

class ParkingSpotSettings;
extern pc::util::coding::Item<ParkingSpotSettings> g_settings;

osg::Vec4f computeAtlasTexCoord(const osg::Vec2ui& f_gridSize, const osg::Vec2ui& f_selection);

//!
//! ParkingSpotNode interface
//!
class ParkingSpotNode
{
public:

  virtual ~ParkingSpotNode() {}

  virtual void update(ParkingSpot* f_parkingSpot) = 0;

};

//!
//! ParkingSpotPlane
//!
class ParkingSpotPlane : public osg::MatrixTransform, public ParkingSpotNode
{
public:

  ParkingSpotPlane();

  ParkingSpotPlane(const ParkingSpotPlane& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::parkingspots, ParkingSpotPlane);  // PRQA S 2504

  void setSpotType(vfc::uint8_t f_type)
  {
    m_spotType = f_type;
  }

  void setTextureOriginalState(vfc::uint8_t f_state)
  {
    m_textureState = f_state;
  }

  void setSpotSide(ESpotSide f_side)
  {
    m_side = f_side;
  }

  virtual void update(ParkingSpot* f_parkingSpot);

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  bool m_visible;

  vfc::uint8_t m_spotType;

  vfc::uint8_t m_textureState;

  ESpotSide m_side;

};

//!
//! ParkingSpot
//!
class ParkingSpot : public osg::MatrixTransform
{
public:

  enum SelectionState : unsigned int
  {
    POTENTIAL,
    SELECTABLE,
    SELECTED,
    BLOCKED
  };

  ParkingSpot();

  ParkingSpot(const ParkingSpot& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::parkingspots, ParkingSpot);  // PRQA S 2504

  const osg::Vec2f& getPosition() const
  {
    return m_position;
  }

  const osg::Vec2f& getMiddle() const
  {
    return m_middle;
  }

  void setPosition(const osg::Vec2f& f_position)
  {
    m_position = f_position;
    //update the middle of the parking slot
    m_middle = m_position + (m_size/2.f);
  }

  const osg::Vec2f& getSize() const
  {
    return m_size;
  }

  void setSize(const osg::Vec2f& f_size)
  {
    m_size = f_size;
  }

  ESpotSide getSpotID() const
  {
    return m_side;
  }

  void setSpotID(ESpotSide f_side)
  {
    m_side = f_side;
  }

  vfc::uint8_t getType() const
  {
    return m_spotType;
  }

  void setType(vfc::uint8_t f_type)
  {
    m_spotType = f_type;
  }

  ESpotSide getSide() const
  {
    return m_side;
  }

  void setSide(ESpotSide f_side)
  {
    m_side = f_side;
  }

  unsigned int getSelectionState() const
  {
    //if one of them is selectable or selected
    //the the parking spot is considered as selectable or selected
    if ( (m_selectionStateBwd == ParkingSpot::SELECTED) ||
        (m_selectionStateFwd == ParkingSpot::SELECTED)
        )
    {
      return (ParkingSpot::SELECTED);
    }
    else if ( (m_selectionStateBwd == ParkingSpot::SELECTABLE) ||
              (m_selectionStateFwd == ParkingSpot::SELECTABLE)
            )
    {
      return (ParkingSpot::SELECTABLE);
    }
    else
    {
      //the bacward state has priority
      return (m_selectionStateBwd);
    }
  }

  void setSelectionState(unsigned int f_selectionStateFwd, unsigned int f_selectionStateBwd)
  {
    m_selectionStateFwd = f_selectionStateFwd;
    m_selectionStateBwd = f_selectionStateBwd;
  }

  void setSpotState(vfc::uint8_t f_state)
  {
    m_spotState = f_state;
  }

  vfc::uint8_t getSpotState()
  {
    return m_spotState;
  }

  void dirty()
  {
    m_dirty = true;
  }

  bool getVisibility()
  {
    return (m_visible);
  }

  void setVisibility(bool f_visible)
  {
    m_visible = f_visible;
  }

  float getGroundLevel() const;

  virtual void traverse(osg::NodeVisitor& f_nv);

private:

  ESpotSide m_side;
  vfc::uint8_t m_spotType;
  unsigned int m_selectionStateFwd;
  unsigned int m_selectionStateBwd;
  vfc::uint8_t m_spotState;
  osg::Vec2f m_position;
  osg::Vec2f m_middle;
  osg::Vec2f m_size;
  bool m_dirty;
  bool m_visible;

};

enum EParkingSpotAttributes : vfc::uint8_t
{
  PARKSLOT_SELECTABLE_PARALLEL_L = 0u,
  PARKSLOT_SELECTABLE_PARALLEL_R = 1u,
  PARKSLOT_SELECTABLE_CROSS_L    = 2u,
  PARKSLOT_SELECTABLE_CROSS_R    = 3u,
  PARKSLOT_SELECTED_PARALLEL_L   = 4u,
  PARKSLOT_SELECTED_PARALLEL_R   = 5u,
  PARKSLOT_SELECTED_CROSS_L      = 6u,
  PARKSLOT_SELECTED_CROSS_R      = 7u,
  PARKSLOT_SELECTED_DIAGONAL_L   = 8u,
  PARKSLOT_SELECTED_DIAGONAL_R   = 9u,
  PARKSLOT_SELECTABLE_DIAGONAL_L = 10u,
  PARKSLOT_SELECTABLE_DIAGONAL_R = 11u,
  PARKINGSPOTSNUM                = 12u
};

void setSpotPlaneAttributes(std::array<osg::ref_ptr<ParkingSpotPlane>, 12> &f_spotsPlaneArray, vfc::uint8_t position);
void setParkingSpotIconPath(std::array<std::string, PARKINGSPOTSNUM> &f_spotsAddrs);

} // namespace parkingspots
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKINGSPOT_H
