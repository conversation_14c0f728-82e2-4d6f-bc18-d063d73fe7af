//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomImageOverlays.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/assets/uielements/inc/Utils.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h" // PRQA S 1060
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060



using pc::util::logging::g_OSGContext;

namespace cc
{
namespace assets
{
namespace uielements
{

class CustomCameraCallback : public osg::NodeCallback // PRQA S 2113 // PRQA S 2119
{
public:

  explicit CustomCameraCallback(CustomImageOverlays* f_imageOverlays)
    : m_imageOverlays{f_imageOverlays}
    , m_flashingCounter{0u}
    , m_numberOfDelayTraversalCycle{50u}
  {
  }

  void operator () (osg::Node* f_node, osg::NodeVisitor* f_nv) override
  {
    if ((f_node == nullptr) || (f_nv == nullptr))
    {
        return;
    }
    switch (f_nv->getVisitorType())
    {
    case osg::NodeVisitor::CULL_VISITOR:
    {
      cull(f_node->asCamera(), f_nv);
      break;
    }
    case osg::NodeVisitor::UPDATE_VISITOR:
    {
      update(f_node->asCamera(), f_nv);
      break;
    }
    default:
    {
      traverse(f_node, f_nv);
      break;
    }
    }
  }

  void cull(osg::Camera* f_cam, osg::NodeVisitor* f_nv)
  {
    if ((f_cam == nullptr) || (f_nv == nullptr))
    {
        return;
    }
    const vfc::uint32_t l_numChildren = f_cam->getNumChildren();
    for (vfc::uint32_t i = 0; i < l_numChildren; ++i)
    {
      osg::Node* const l_node = f_cam->getChild(i);
      CustomIcon* const l_icon = dynamic_cast<CustomIcon*> (l_node);  // PRQA S 3400
      if (l_icon != nullptr)
      {
        //! only draw icon if it is enabled and in a valid state
        if (l_icon->isValid() && l_icon->getEnabled())
        {
          if (l_icon->getAnimation() == cc::assets::uielements::CustomIcon::AnimationStyle::FLASHING_EFFECT)
          {

            if ( (m_flashingCounter % m_numberOfDelayTraversalCycle) < (m_numberOfDelayTraversalCycle>>1))
            {
               l_icon->accept(*f_nv);
            }

            m_flashingCounter ++;

          }
          else
          {
            l_icon->accept(*f_nv);
          }
        }
      }
      else
      {
        //! not an icon, handle like a group node would do
        l_node->accept(*f_nv);
      }
    }
  }


  void update(osg::Camera* f_cam, osg::NodeVisitor* f_nv) // PRQA S 4211
  {
    if ((f_cam == nullptr) || (f_nv == nullptr))
    {
        return;
    }
    pc::core::Viewport l_viewport(osg::Vec2i(0, 0), pc::core::g_systemConf->m_mainViewport.m_size);
    osg::Camera* const l_referenceView = m_imageOverlays->getReferenceView();
    if (l_referenceView != nullptr)
    {
      osg::Viewport* const l_osgViewport = l_referenceView->getViewport();
      if (l_osgViewport != nullptr)
      {
        l_viewport.m_origin = osg::Vec2i(static_cast<vfc::int32_t> (l_osgViewport->x()), static_cast<vfc::int32_t> (l_osgViewport->y())); // PRQA S 3016
        l_viewport.m_size = osg::Vec2i(static_cast<vfc::int32_t> (l_osgViewport->width()), static_cast<vfc::int32_t> (l_osgViewport->height())); // PRQA S 3016
      }
    }
    f_cam->setViewport(l_viewport.m_origin.x(), l_viewport.m_origin.y(), l_viewport.m_size.x(), l_viewport.m_size.y());
    f_cam->setProjectionMatrixAsOrtho2D(0.0, l_viewport.m_size.x(), 0.0, l_viewport.m_size.y()); // PRQA S 3011

    const vfc::uint32_t l_numChildren = f_cam->getNumChildren();
    for (vfc::uint32_t i = 0; i < l_numChildren; ++i)
    {
      osg::Node* const l_node = f_cam->getChild(i);
      CustomIcon* const l_icon = dynamic_cast<CustomIcon*> (l_node);  // PRQA S 3400
      if (l_icon != nullptr)
      {
        //! special case updating of icon nodes
        if (l_icon->isValid())
        {
          l_icon->update(l_viewport);

          if ( (l_icon->getAnimation() == cc::assets::uielements::CustomIcon::AnimationStyle::AUGMENTED_WAVE_EFFECT) ||
               (l_icon->getAnimation() == cc::assets::uielements::CustomIcon::AnimationStyle::FADEIN_FADEOUT_EFFECT) )
          {
            l_icon->updateShaderUniform();
          }
          else
          {

          }
        }
      }
      //! normal updating
      if (0 < l_node->getNumChildrenRequiringUpdateTraversal())
      {
        l_node->accept(*f_nv);
      }
    }
  }

  void setNumberOfDelayTraversalCycle(vfc::uint32_t f_numberOfDelayTraversalCycle)
  {
    m_numberOfDelayTraversalCycle = f_numberOfDelayTraversalCycle;
  }

private:

  CustomImageOverlays* m_imageOverlays;

  vfc::uint32_t m_flashingCounter;
  vfc::uint32_t m_numberOfDelayTraversalCycle;

};
osg::Geometry* CustomIcon::createGeometry() const
{
  osg::Geometry* const l_geometry = new osg::Geometry;
  l_geometry->setUseDisplayList(false);
  l_geometry->setUseVertexBufferObjects(true);
  l_geometry->setVertexArray(new osg::Vec3Array(4));

  osg::Vec4Array* const l_colors = new osg::Vec4Array(1);
  (*l_colors)[0] = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
  l_geometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);

  osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4);
  l_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, 6u);
  (*l_indices)[0] = 0u;
  (*l_indices)[1] = 1u;
  (*l_indices)[2] = 2u;
  (*l_indices)[3] = 2u;
  (*l_indices)[4] = 1u;
  (*l_indices)[5] = 3u;
  l_geometry->addPrimitiveSet(l_indices);  // PRQA S 3803

  return l_geometry;
}


//!
//! CustomImageOverlays
//!
CustomImageOverlays::CustomImageOverlays(cc::core::AssetId f_assetId, osg::Camera* f_referenceView)
  : pc::assets::ImageOverlays{f_assetId, f_referenceView}
{
}

CustomImageOverlays::CustomImageOverlays(cc::core::AssetId f_assetId, osg::Camera* f_referenceView, cc::assets::uielements::CustomIcon::AnimationStyle f_animationStyle, vfc::uint32_t f_numberOfDelayTraversalCycle)
  : pc::assets::ImageOverlays{f_assetId, f_referenceView}
{
  if ( f_animationStyle == cc::assets::uielements::CustomIcon::AnimationStyle::FLASHING_EFFECT )
  {
    osg::Camera* const l_hudCamera =  dynamic_cast<osg::Camera*>(this->getAsset());  // PRQA S 3400
    CustomCameraCallback* const l_cameraCallback = new CustomCameraCallback(this);
    l_cameraCallback->setNumberOfDelayTraversalCycle(f_numberOfDelayTraversalCycle);
    l_hudCamera->setUpdateCallback(l_cameraCallback);
    l_hudCamera->setCullCallback(l_cameraCallback);
  }
  else if (( f_animationStyle == cc::assets::uielements::CustomIcon::AnimationStyle::FADEIN_FADEOUT_EFFECT ) ||
           ( f_animationStyle == cc::assets::uielements::CustomIcon::AnimationStyle::AUGMENTED_WAVE_EFFECT ))
  {
    osg::Camera* const l_hudCamera =  dynamic_cast<osg::Camera*>(this->getAsset());  // PRQA S 3400
    CustomCameraCallback* const l_cameraCallback = new CustomCameraCallback(this);
    l_cameraCallback->setNumberOfDelayTraversalCycle(f_numberOfDelayTraversalCycle);
    l_hudCamera->setUpdateCallback(l_cameraCallback);
  }
  else
  {
    // do nothing
  }
}

void CustomImageOverlays::CustomSetRenderOrder(vfc::uint32_t f_customRenderOrder)
{
  osg::Camera* l_hudCamera = new osg::Camera();  // PRQA S 3802  #code looks fine
  l_hudCamera = static_cast<osg::Camera*>(this->getAsset());
  l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, static_cast<vfc::int32_t>(f_customRenderOrder));
}


void CustomImageOverlays::CustomSetRenderOrder(vfc::uint32_t f_customRenderOrder, bool f_isPreRender)
{
  osg::Camera* l_hudCamera = new osg::Camera();  // PRQA S 3802  #code looks fine
  l_hudCamera = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076

  if (f_isPreRender)
  {
    l_hudCamera->setRenderOrder(osg::Camera::PRE_RENDER, static_cast<vfc::int32_t>(f_customRenderOrder));
  }
  else
  {
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, static_cast<vfc::int32_t>(f_customRenderOrder));
  }

}

CustomImageOverlays::~CustomImageOverlays() = default;




} // namespace uielements
} // namespace assets
} // namespace cc

