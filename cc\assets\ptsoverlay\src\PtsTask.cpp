//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/ptsoverlay/inc/PtsTask.h"
#include "cc/assets/ptsoverlay/inc/PtsSettings.h"
#include "cc/assets/ptsoverlay/inc/PtsUtils.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "cc/worker/core/inc/CustomTaskManager.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include <cassert>
#include "vfc/core/vfc_types.hpp"


/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace ptsoverlay
{
using pc::util::logging::g_workerContext;

//!
//! PtsExtractor
//!
PtsExtractor::PtsExtractor(pc::vehicle::AbstractZoneLayout* f_zoneLayout)
  : m_zoneLayout{f_zoneLayout}
  , m_defaultDistance{1.0f}
  , m_origins{}
  , m_directions{}
  , m_pasZoneMappings{}
  , m_distances{}
  , m_cutoffDistances{}
{
  init();
}


vfc::float32_t PtsExtractor::getDefaultDistance() const
{
  return m_defaultDistance;
}


const pc::util::FloatList PtsExtractor::getDistances() const
{
  return m_distances;
}


void PtsExtractor::setDistances(const pc::util::FloatList& f_distances)
{
  assert(f_distances.size() == m_distances.size());
  m_distances = f_distances;
}


const pc::util::FloatList PtsExtractor::getCutoffDistances() const
{
  return m_cutoffDistances;
}


void PtsExtractor::update(const pc::vehicle::UltrasonicData& f_ussData)
{
  const vfc::uint32_t l_numSegments = static_cast<vfc::uint32_t> (m_distances.size());
  const vfc::float32_t l_minDistance = g_ptsSettings->m_extractorMinDistance;
  for (vfc::uint32_t i = 0u; i < l_numSegments; ++i)
  {
    const UByteList::value_type l_pasZone = m_pasZoneMappings[i];
    const vfc::float32_t l_distance = f_ussData[l_pasZone].getDistance();
    m_distances[i] = std::max(l_minDistance, std::min(l_distance, m_defaultDistance));
  }
}


const PtsExtractor::UByteList PtsExtractor::getPasZoneMappings() const
{
  return m_pasZoneMappings;
}


pc::util::Polygon2D PtsExtractor::toPolygon2D() const
{
  const vfc::uint32_t l_numSegments = static_cast<vfc::uint32_t> (m_distances.size());
  pc::util::Polygon2D l_polygon(l_numSegments);
  for (vfc::uint32_t i = 0u; i < l_numSegments; ++i)
  {
    l_polygon[i] = m_origins[i] + (m_directions[i] * std::min(m_distances[i], m_cutoffDistances[i]));
  }
  return l_polygon;
}


void PtsExtractor::init()
{
  //! in order to solely depend on the PAS zone layout (and not mechanical data),
  //! we need to find the bounding box of the inner base points
  pc::util::Box2D l_bounds(
    std::numeric_limits<vfc::float32_t>::max(),
    std::numeric_limits<vfc::float32_t>::max(),
    std::numeric_limits<vfc::float32_t>::min(),
    std::numeric_limits<vfc::float32_t>::min());

  //! compute origin and direction for each segment
  const pc::util::Polygon2D l_vehicleContour = cc::core::g_vehicleContour->toPolygon2D();

  //! the target segment length needs to have a certain minimum in order to protect agains div by zero or insane amounts of segments
  const vfc::float32_t l_targetSegmentLength = std::max(0.01f, g_ptsSettings->m_targetSegmentLength);
  for (vfc::uint32_t i = 0u; i < cc::target::sysconf::E_ULTRASONIC_NUM_ZONES; ++i)
  {
    const pc::vehicle::LineData& l_leftBorder = m_zoneLayout->getLeftBorderLine(i);
    const pc::vehicle::LineData& l_rightBorder = m_zoneLayout->getRightBorderLine(i);

    const osg::Vec2 l_innerDiv = l_leftBorder.m_innerPoint- l_rightBorder.m_innerPoint;
    const osg::Vec2 l_outerDiv = l_leftBorder.m_outerPoint - l_rightBorder.m_outerPoint;
    //! compute the number of segments depending on the zone size and the given target segment length (must be at least one)
    const vfc::float32_t l_zoneSize = l_outerDiv.length();
    const vfc::uint32_t l_numSegments = std::max(1u, pc::util::round2uInt(0.5f + (l_zoneSize / l_targetSegmentLength))); // PRQA S 3016
    for (vfc::uint32_t j = 0u; j < l_numSegments; ++j)
    {
      const vfc::float32_t l_scalingFactor = (0.5f / l_numSegments) + (j * (1.0f / static_cast<vfc::float32_t> (l_numSegments)));
      osg::Vec2 l_innerPoint = l_rightBorder.m_innerPoint + (l_innerDiv * l_scalingFactor);
      const osg::Vec2 l_outerPoint = l_rightBorder.m_outerPoint + (l_outerDiv * l_scalingFactor);
      //! test line from inner to outer point against vehicle contour for intersection
      osg::Vec2 l_contourPoint;
      const bool l_intersectionFound = pc::util::findIntersectionWithPolygon(
        l_vehicleContour,
        pc::util::LineSegment2D(l_innerPoint, l_outerPoint),
        l_contourPoint);
      //! if an intersection was found, use intersection point which lays on the contour as new inner point
      if (l_intersectionFound)
      {
        l_innerPoint = l_contourPoint;
      }
      else
      {
        // XLOG_WARN_OS(g_workerContext) << "PtsExtractor::init(): " <<
        //   "could not find vehicle contour intersection point for segment origin point (" <<
        //   l_innerPoint.x() << ", " << l_innerPoint.y() << ")" << XLOG_ENDL;
      }
      l_bounds.expand(l_innerPoint); // expand bounding box to find min/max of all origin points
      osg::Vec2 l_direction = l_outerPoint - l_innerPoint;
      l_direction.normalize();  // PRQA S 3803
      m_origins.push_back(l_innerPoint);
      m_directions.push_back(l_direction);
      m_pasZoneMappings.push_back(static_cast<UByteList::value_type> (i)); // store the corresponding PAS zone index
    }
  }



  //! expand the bounding box by pts ranges which will yield the bounding box of the undeformed PTS spline
  l_bounds.setPMax(l_bounds.getPMax() + osg::Vec2(g_ptsSettings->getRangeFront(), g_ptsSettings->getRangeSide()));
  l_bounds.setPMin(l_bounds.getPMin() + osg::Vec2(-g_ptsSettings->getRangeRear(), -g_ptsSettings->getRangeSide()));

  //! copy the PTS bounding box and shrink it by the corner radius in order to find the origins of the corner arcs
  pc::util::Box2D l_cornerArcPoints = l_bounds;
  l_cornerArcPoints.expandBy(-g_ptsSettings->m_cornerRadius);

  //! the actual shape and range of the undeformed PTS spline is described by four boundary lines (front, left, ... etc.).
  LineSegment l_boundaryFront(l_bounds.getXMaxAsLineSegment());
  LineSegment l_boundaryLeft(l_bounds.getYMaxAsLineSegment());
  LineSegment l_boundaryRear(l_bounds.getXMinAsLineSegment());
  LineSegment l_boundaryRight(l_bounds.getYMinAsLineSegment());
  //! in addition to that we need to add a quater of a circle in each corner in order to account for the rounded corners
  CornerArc l_cornerRearRight(
    getCorner(l_cornerArcPoints, 0u),
    g_ptsSettings->m_cornerRadius, CornerArc::CORNER_ARC_X_NEG_Y_NEG);
  CornerArc l_cornerFrontRight(
    getCorner(l_cornerArcPoints, X_AXIS_BIT),
    g_ptsSettings->m_cornerRadius, CornerArc::CORNER_ARC_X_POS_Y_NEG);
  CornerArc l_cornerFrontLeft(
    getCorner(l_cornerArcPoints, static_cast<vfc::uint32_t>(X_AXIS_BIT) | static_cast<vfc::uint32_t>(Y_AXIS_BIT)),
    g_ptsSettings->m_cornerRadius, CornerArc::CORNER_ARC_X_POS_Y_POS);
  CornerArc l_cornerRearLeft(
    getCorner(l_cornerArcPoints, Y_AXIS_BIT),
    g_ptsSettings->m_cornerRadius, CornerArc::CORNER_ARC_X_NEG_Y_POS);

  //! create a list of all geometric shapes
  typedef std::array<const GeometricShape*, 8> GeometricShapeArray;
  const GeometricShapeArray l_ptsBoundaries = {
    {&l_boundaryFront,
    &l_boundaryLeft,
    &l_boundaryRear,
    &l_boundaryRight,
    &l_cornerRearRight,
    &l_cornerFrontRight,
    &l_cornerFrontLeft,
    &l_cornerRearLeft}
  };

  const std::size_t l_numSegments = m_origins.size();
  m_distances.resize(l_numSegments);
  m_cutoffDistances.resize(l_numSegments);

  const vfc::float32_t l_maxDistance = osg::Vec2f(std::max(g_ptsSettings->getRangeRear(), g_ptsSettings->getRangeFront()), g_ptsSettings->getRangeSide()).length();
  std::fill(m_cutoffDistances.begin(), m_cutoffDistances.end(), l_maxDistance);

  GeometricShape::PointList l_intersections;
  for (vfc::uint32_t i = 0u; i < l_numSegments; ++i)
  {
    bool l_intersectionFound = false;
    //! create a line segment which acts as a ray to trace the undeformed PTS spline shape (multiplied by 3 just to ensure the segment is long enough)
    const pc::util::LineSegment2D l_testLine(m_origins[i], m_origins[i] + (m_directions[i] * (3.0f * l_maxDistance)));
    //! interate over all geometric shapes and find the shortest intersection point with the boundary elements
    for (vfc::uint32_t j = 0u; j < std::tuple_size<GeometricShapeArray>::value; ++j)
    {
      l_intersections.clear();
      l_intersections = l_ptsBoundaries[j]->intersect(l_testLine);
      for (auto l_itr = l_intersections.begin(); l_itr != l_intersections.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
      {
        l_intersectionFound = true;
        const osg::Vec2f l_originToIntersection = (*l_itr) - m_origins[i];
        const vfc::float32_t l_distanceToIntersection = l_originToIntersection.length();
        if (l_distanceToIntersection < m_cutoffDistances[i])
        {
          m_cutoffDistances[i] = l_distanceToIntersection;
        }
      }
    }
    if (!l_intersectionFound)
    {
      XLOG_ERROR(g_workerContext, "PtsExtractor::init() could not find an intersection withing PTS range for segment " << i << ". Check coding paramters!");
    }
  }

  const auto l_res = std::max_element(m_cutoffDistances.begin(), m_cutoffDistances.end());
  if (l_res != m_cutoffDistances.end())
  {
    m_defaultDistance = *l_res;
  }
  std::fill(m_distances.begin(), m_distances.end(), m_defaultDistance);
}



//!
//! @brief Customized Gaussion spatial filter
//!
//!
class CustomGaussianFilter : public pc::util::GaussianFilter<pc::util::FloatList>
{
public:

  typedef pc::util::GaussianFilter<pc::util::FloatList> BaseGaussianFilter;

  explicit CustomGaussianFilter(const pc::util::SpatialFilterData& f_data)
    : BaseGaussianFilter{f_data.m_kernelSize, f_data.m_gaussianSigma}
    , m_tempBuffer{}
  {
  }

  void filter(const pc::util::FloatList& f_input, pc::util::FloatList& f_output, vfc::uint32_t f_numValues) override
  {
    m_tempBuffer.resize(f_numValues);
    //! first smoothing pass
    BaseGaussianFilter::filter(f_input, m_tempBuffer, f_numValues);
    //! restore minima in order to presere closes distance values
    for (vfc::uint32_t i = 0u; i < f_numValues; ++i)
    {
      m_tempBuffer[i] = std::min(f_input[i], m_tempBuffer[i]);
    }
    BaseGaussianFilter::filter(m_tempBuffer, f_output, f_numValues);
  }

private:

  pc::util::FloatList m_tempBuffer;

};

namespace
{

//!
//! @brief The distances values used for computing the colors of the PTS spline need some special treatment because
//! the side should remain blue at a shorter distance then the front and the back
//!
//! @param f_distances actual measured distances
//! @param f_cutoffs values which describe the valid range per segment - these distances describe the initial PTS spline state (pill-shape)
//! @param f_coloringDistances the distance values which will be used for evaluating the color of the spline segmet
//! @param f_overrideValue the value which will be assigned if the a distance value is above it's corresponding cutoff value
//!
void computeColoringDistances(
  const pc::util::FloatList& f_distances,
  const pc::util::FloatList& f_cutoffs,
  pc::util::FloatList& f_coloringDistances,
  vfc::float32_t f_overrideValue)
{
  const std::size_t l_size = std::min(f_distances.size(), f_cutoffs.size());
  f_coloringDistances.resize(l_size);
  for (std::size_t i = 0u; i < l_size; ++i)
  {
    f_coloringDistances[i] = (f_distances[i] > f_cutoffs[i]) ? f_overrideValue : f_distances[i];
  }
}

} // namespace


//!
//! ProcessingTask
//!
ProcessingTask::ProcessingTask(PtsOverlay* f_ptsOverlay, pc::vehicle::AbstractZoneLayout* f_zoneLayout)
  : pc::worker::core::Task{"PtsOverlayTask"}
  , m_ptsOverlay{f_ptsOverlay}
  , m_extractor{f_zoneLayout}
  , m_distanceFilter{new CustomGaussianFilter(g_ptsSettings->m_distanceFilter)}
  , m_updateVisitor{new PtsUpdateVisitor}
  , m_distancesCurrent{}
  , m_distancesTarget{}
  , m_distancesColoring{}
{
}


bool ProcessingTask::onRun(pc::worker::core::TaskManager* f_taskManager)
{
  if (f_taskManager == nullptr)
  {
    return false;
  }
  cc::worker::core::CustomTaskManager* const l_taskManager = f_taskManager->asCustomTaskManager();

  //! check PTS HMI state
  cc::daddy::PtsHmiStateOutput l_ptsHmiStateCurrent; // PRQA S 4102
  l_ptsHmiStateCurrent.m_pashmiState = static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_NO_WARNING);
  l_ptsHmiStateCurrent.m_errorReaction = false;
  l_ptsHmiStateCurrent.m_disturbanceReactionFront = false;
  l_ptsHmiStateCurrent.m_disturbanceReactionRear = false;
  l_ptsHmiStateCurrent.m_sideSegmentActivation.m_activationMask = cc::daddy::PtsSideSegmentActivation::ALL_SEGMENTS_MASK;
  // const cc::daddy::PtsHmiStateOutputDaddy* l_ptsHmiState = l_taskManager->m_ptsHmiStateOutputReceiver.getData();
  // if (l_ptsHmiState)
  // {
  //   l_ptsHmiStateCurrent = l_ptsHmiState->m_Data;
  // }
  if (l_taskManager->m_customTaskPasStatus_ReceiverPort.hasData())
  {
    const cc::daddy::PasStatusDaddy_t* const l_pasStatus = l_taskManager->m_customTaskPasStatus_ReceiverPort.getData();
    l_ptsHmiStateCurrent.m_pashmiState = l_ptsHmiStateCurrent.convertFromPasStatus(l_pasStatus->m_Data);
  }

  //! check user settings
  constexpr bool l_flanksEnabled = false;  // disable flank since there is only front/rear warning.
  // const cc::daddy::UserSettingsDaddy* l_userSettings = l_taskManager->m_userSettingsReceiver.getData();
  // if (l_userSettings)
  // {
  //   l_flanksEnabled = l_userSettings->m_Data.m_ptsWarnSide;
  // }

  //! check door state
  bool l_doorOpenLeft = false;
  bool l_doorOpenRight = false;
  const pc::daddy::DoorStateDaddy* const l_doorState = l_taskManager->m_doorStateReceiver.getData();
  if (l_doorState != nullptr)
  {
//     using namespace pc::daddy;
    l_doorOpenLeft = ((l_doorState->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]  == pc::daddy::CARDOORSTATE_OPEN) || (/*cc::core::g_vehicleData->hasLeftRearDoor() &&  */l_doorState->m_Data[pc::daddy::CARDOOR_REAR_LEFT] == pc::daddy::CARDOORSTATE_OPEN));
    l_doorOpenRight = ((l_doorState->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT] == pc::daddy::CARDOORSTATE_OPEN) || (/*cc::core::g_vehicleData->hasRightRearDoor() && */l_doorState->m_Data[pc::daddy::CARDOOR_REAR_RIGHT] == pc::daddy::CARDOORSTATE_OPEN));
  }

  //! check trailer connection state
  bool l_trailerConnected = false;
  const pc::daddy::TrailerConnectedDaddy* const l_trailerConnectionState = l_taskManager->m_trailerConnectedReceiver.getData();
  if (l_trailerConnectionState != nullptr)
  {
    l_trailerConnected = (pc::daddy::TRAILER_CONNECTED == l_trailerConnectionState->m_Data);
  }

  //! init distance lists
  if (m_distancesCurrent.empty())
  {
    m_distancesCurrent = m_extractor.getDistances();
    m_distancesTarget = m_extractor.getDistances();
    m_distancesColoring =  m_extractor.getDistances();
  }

  //! do extraction
  if (l_taskManager->m_ultrasonicDataReceiver.hasNewData())
  {
    const pc::daddy::UltrasonicDataDaddy* const l_ussData = l_taskManager->m_ultrasonicDataReceiver.getData();
    m_extractor.update(l_ussData->m_Data);
    m_distancesTarget = m_extractor.getDistances();
    m_distancesTarget = m_distanceFilter->applyFilter(m_distancesTarget);
  }

  //! animate
  const bool l_converged = pc::util::converge(pc::util::Divide(8.0f), m_distancesCurrent, m_distancesTarget, 0.001f);
  static_cast<void> (l_converged); // ingore // PRQA S 3242

  //! do updating of geometry
  osg::ref_ptr<PtsOverlay> l_ptsOverlay;
  if (m_ptsOverlay.lock(l_ptsOverlay))
  {
    PtsOverlayComposite* const l_ptsOverlayNode = static_cast<PtsOverlayComposite*> (l_ptsOverlay->reserve()); // PRQA S 3076
    if (l_ptsOverlayNode != nullptr)
    {
      m_extractor.setDistances(m_distancesCurrent); //! the filtered values are re-assigned to the extractor, used for the toPolygon2D computation
      computeColoringDistances(m_extractor.getDistances(), m_extractor.getCutoffDistances(), m_distancesColoring, m_extractor.getDefaultDistance());

      m_updateVisitor->setData(
        m_extractor.toPolygon2D(),
        m_distancesColoring,
        m_extractor.getPasZoneMappings(),
        DegradationData(l_ptsHmiStateCurrent, l_flanksEnabled, l_doorOpenLeft, l_doorOpenRight, l_trailerConnected)
        );

      l_ptsOverlayNode->accept(*m_updateVisitor.get());

      l_ptsOverlayNode->dirty();
      l_ptsOverlay->deliver();
    }
  }
  else
  {
    //! if acquiring the pts overlay fails this task is done
    return true;
  }

  return false;
}



} // namespace ptsoverlay
} // namespace assets
} // namespace cc
