//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC GAC
/// @file  StreetOverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_STREETOVERLAY_STREETOVERLAY_H
#define CC_ASSETS_STREETOVERLAY_STREETOVERLAY_H

#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Texture2D>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace streetoverlay
{

//!
//! StreetOverlayCodingParams
//!
class StreetOverlayCodingParams : public pc::util::coding::ISerializable
{
public:

  StreetOverlayCodingParams()
    : m_gridFadeOutBegin(9.0f)
    , m_gridFadeOutEnd(16.0f)
    , m_gridXOffset(5.0f)
    , m_gridYOffset(3.7f)
    , m_gridWidth(4.0f)
    , m_gridHeight(-0.01f)
    // , m_waveHeight(0.02f)
    , m_gridTileSize(1.0f, 2.0f)
    , m_gridTileOffset(0.5f, 0.0f)
    // , m_gridNotFoundTexture("cc/vehicle_model/ui/10_parking_bar_slotNotFound.png")
    // , m_gridFoundTexture("cc/vehicle_model/ui/10_parking_bar_slotFound.png")
    // , m_innerWaveTexture("cc/vehicle_model/ui/12_searching_wave2.png")
    // , m_outerWaveTexture("cc/vehicle_model/ui/11_searching_wave1.png")
    , m_gridTexMinFilterMode(2u)
    , m_roadBegin(-10.5f)
    , m_roadEnd(6.0f)
    , m_roadHeight(-0.02f)
    , m_roadWidth(8.0f)
    , m_roadFadeOutFront(20.0f)
    , m_roadFadeOutRear(5.0f)
    , m_roadTexture("cc/vehicle_model/ui/park_road.png")
    , m_roadTexMinFilterMode(0u)
    , m_roadTexCoordExp(3.0f)
  {
  }

  SERIALIZABLE(StreetOverlayCodingParams)
  {
    ADD_FLOAT_MEMBER(gridFadeOutBegin);
    ADD_FLOAT_MEMBER(gridFadeOutEnd);
    ADD_FLOAT_MEMBER(gridXOffset);
    ADD_FLOAT_MEMBER(gridYOffset);
    ADD_FLOAT_MEMBER(gridWidth);
    ADD_FLOAT_MEMBER(gridHeight);
    // ADD_FLOAT_MEMBER(waveHeight);
    ADD_MEMBER(osg::Vec2f, gridTileSize);
    ADD_MEMBER(osg::Vec2f, gridTileOffset);
    // ADD_STRING_MEMBER(gridNotFoundTexture);
    // ADD_STRING_MEMBER(gridFoundTexture);
    // ADD_STRING_MEMBER(innerWaveTexture);
    // ADD_STRING_MEMBER(outerWaveTexture);
    ADD_UINT32_MEMBER(gridTexMinFilterMode);
    ADD_FLOAT_MEMBER(roadBegin);
    ADD_FLOAT_MEMBER(roadEnd);
    ADD_FLOAT_MEMBER(roadHeight);
    ADD_FLOAT_MEMBER(roadWidth);
    ADD_FLOAT_MEMBER(roadFadeOutFront);
    ADD_FLOAT_MEMBER(roadFadeOutRear);
    ADD_STRING_MEMBER(roadTexture);
    ADD_UINT32_MEMBER(roadTexMinFilterMode);
    ADD_FLOAT_MEMBER(roadTexCoordExp);
  }

  static osg::Texture::FilterMode getFilterMode(unsigned int f_value)
  {
    switch (f_value)
    {
      default:
      case 0:
        return osg::Texture::LINEAR;
      case 1:
        return osg::Texture::LINEAR_MIPMAP_LINEAR;
      case 2:
        return osg::Texture::LINEAR_MIPMAP_NEAREST;
      case 3:
        return osg::Texture::NEAREST;
      case 4:
        return osg::Texture::NEAREST_MIPMAP_LINEAR;
      case 5:
        return osg::Texture::NEAREST_MIPMAP_NEAREST;
    }
  }

  float        m_gridFadeOutBegin;
  float        m_gridFadeOutEnd;
  float        m_gridXOffset;
  float        m_gridYOffset;
  float        m_gridWidth;
  float        m_gridHeight;
  // float        m_waveHeight;
  osg::Vec2f    m_gridTileSize;
  osg::Vec2f    m_gridTileOffset;
  // std::string  m_gridNotFoundTexture;
  // std::string  m_gridFoundTexture;
  // std::string  m_innerWaveTexture;
  // std::string  m_outerWaveTexture;
  unsigned int m_gridTexMinFilterMode;
  float        m_roadBegin;
  float        m_roadEnd;
  float        m_roadHeight;
  float        m_roadWidth;
  float        m_roadFadeOutFront;
  float        m_roadFadeOutRear;
  std::string  m_roadTexture;
  unsigned int m_roadTexMinFilterMode;
  float        m_roadTexCoordExp;

};

extern pc::util::coding::Item<StreetOverlayCodingParams> g_settings;

class StreetOverlayUpdateCallback: public osg::NodeCallback
{

public:
  StreetOverlayUpdateCallback(
                                osg::ref_ptr<osg::Geode> f_geodeNotFound, 
                                osg::ref_ptr<osg::Geode> f_geodeFound, 
                                pc::core::Framework* f_pFramework, 
                                const bool f_isLeft
                                );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
  virtual ~StreetOverlayUpdateCallback();

private:
  //! Copy constructor is not permitted.
  StreetOverlayUpdateCallback (const StreetOverlayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  StreetOverlayUpdateCallback& operator=(const StreetOverlayUpdateCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_geodeNotFound;
  osg::ref_ptr<osg::Geode> m_geodeFound;
  pc::core::Framework* m_pFramework;
  bool m_isLeft;
};

//!
//! streetoverlay
//!
class StreetOverlay : public osg::MatrixTransform
{
public:

    enum GeometryData
    {
        POINTS_PER_LINE = 40,
        POINTS_FOR_FADE_IN = 5,
        POINTS_FOR_FADE_OUT = 30
    };

    StreetOverlay(pc::core::Framework* f_framework);

    bool getAnimXDirection()
    {
      return (m_animXDirection);
    }
    void setAnimXDirection(bool f_dirX)
    {
       m_animXDirection = f_dirX;
    }

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    void init();

    virtual ~StreetOverlay();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    float m_lastUpdateTime;
    float m_drivenDistance;
    bool m_animXDirection;
    osg::ref_ptr<osg::Switch> m_streetOverlaySwitch;
    osg::ref_ptr<osg::Geode> m_gridNotFoundGeodeRight;
    osg::ref_ptr<osg::Geode> m_gridNotFoundGeodeLeft;
    osg::ref_ptr<osg::Geode> m_gridFoundGeodeRight;
    osg::ref_ptr<osg::Geode> m_gridFoundGeodeLeft;

private:
  //! Copy constructor is not permitted.
  StreetOverlay (const StreetOverlay& other); // = delete
  //! Copy assignment operator is not permitted.
  StreetOverlay& operator=(const StreetOverlay& other); // = delete

  void update();
  bool isViewIncludeStreetOverlay();

};

osg::Texture2D* loadTexture(const std::string& f_filename, unsigned int f_minFilterMode);

template<class T>
void generateColors(osg::Geometry* f_geometry, const T& f_operator);

} // namespace streetoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_STREETOVERLAY_STREETOVERLAY_H
