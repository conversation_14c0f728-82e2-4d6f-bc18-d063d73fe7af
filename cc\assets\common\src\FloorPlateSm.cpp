//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  FloorPlateSm.cpp
/// @brief
//=============================================================================
#include "cc/assets/common/inc/FloorPlateSm.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "vfc/core/vfc_types.hpp"
using pc::util::logging::g_EngineContext;

#define STB_LOG(component_name) \
  if (g_stbSmLogSettings->m_log##component_name) XLOG_INFO_OS(g_EngineContext)
namespace cc {
namespace assets {
namespace common {

pc::texfloor::odometry::Pose getCurrentRelativeToPrevious(const pc::texfloor::odometry::Pose& f_currentPose, const pc::texfloor::odometry::Pose& f_lastPose)
{
  const pc::texfloor::odometry::Pose l_relativePose = f_currentPose - f_lastPose;
  const osg::Vec3f l_relativeVector = osg::Vec3f(l_relativePose.m_x, l_relativePose.m_y, 0.0f);

  // to compute the position with regard to the reference position (last position), compensate for the yaw of
  // the reference position by rotating the relative vector negatively by the yaw at the reference position.
  // yaw rotations are about Z-axis
  osg::Vec3f l_positionInPrevious = l_relativeVector * osg::Matrix::rotate( -osg::inDegrees(f_lastPose.m_yaw), osg::Z_AXIS);

  pc::texfloor::odometry::Pose l_resultantPose;
  l_resultantPose.m_x   = l_positionInPrevious.x();
  l_resultantPose.m_y   = l_positionInPrevious.y();
  l_resultantPose.m_yaw = f_currentPose.m_yaw - f_lastPose.m_yaw;
  return l_resultantPose;
}
pc::texfloor::odometry::Pose getPreviousRelativeToCurrent(const pc::texfloor::odometry::Pose& f_currentPose, const pc::texfloor::odometry::Pose& f_lastPose)
{
  return getCurrentRelativeToPrevious(f_lastPose, f_currentPose);
}

pc::util::coding::Item<StbSmData> g_stbSmData("FloorPlateSm");

pc::util::coding::Item<StbSmLogSettings> g_stbSmLogSettings("StbSmLogSettings");

FloorPlateMainCallback::FloorPlateMainCallback( osg::Uniform* f_pOnOffToggleUniform, // PRQA S 4206
                                                osg::Uniform* f_pBlurringMixUniform,
                                                cc::core::CustomFramework* f_pFramework )
  : m_gbcViewEnabled{false}
  , m_pStb{ new StbMainCallback( f_pFramework ) }
  , m_pBlur{ new BlurrinessMainCallback(f_pFramework) }
  , m_pTransp{ new TransparencyMainCallback(f_pFramework) }
  , m_pOnOffToggleUniform{ f_pOnOffToggleUniform }
  , m_pBlurringMixUniform{ f_pBlurringMixUniform }
  , m_pFramework{ f_pFramework }
{
  pc::util::coding::CodingManager* const codingManager = pc::util::coding::getCodingManager();
  cc::core::VehicleCcf* const l_ccf = dynamic_cast<cc::core::VehicleCcf*>(codingManager->getItem("CCF")); // PRQA S 3077  // PRQA S 3400
  if (nullptr != l_ccf)
  {
    if(cc::core::PERSPECTVIEW_PLANKERBGLASSBOTTOM == l_ccf->m_ccfPerspectiveViews)
    {
      m_pBlur->setCcfEnabled(true);
      m_pTransp->setCcfEnabled(true);
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "FloorPlateMainCallback: CCF-GBC ENABLED");
    }
    else
    {
      m_pBlur->setCcfEnabled(false);
      m_pTransp->setCcfEnabled(false);
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "FloorPlateMainCallback: CCF-GBC DISABLED");
    }
  }
  else
  {
    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "FloorPlateMainCallback:could not read CCF!");
  }

}

FloorPlateMainCallback::~FloorPlateMainCallback() = default;

void FloorPlateMainCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{

  const bool l_doorsClosed = allDoorsClosed();

  bool l_camWorking  = true;
  const pc::daddy::CameraDegradationMaskDaddy* const l_camDegradationMaskDaddy = m_pFramework->m_degradationMaskReceiver.getData();
  if (nullptr != l_camDegradationMaskDaddy)
  {
    //! Camera Degradation mask - Each bit corresponds to a camera. bit value 1 => degraded, 0 => nominal
    if(0U != l_camDegradationMaskDaddy->m_Data)
    {
      l_camWorking = false;
    }
  }

  bool l_odoWorking  = false;
  // ! check the odometry status, to be implemented later
  const cc::daddy::DegradationFid_t* const l_pDegFidDaddy =  m_pFramework->m_degradationFid_ReceiverPort.getData();
  if (nullptr != l_pDegFidDaddy)
  {
    if(1U == l_pDegFidDaddy->m_Data.m_FiMFID_SVSOdo)
    {
        l_odoWorking = true;
    }
  }

  // always call step from all SMs -> they need to keep their internal states even if no views are active
  m_pStb->step(l_doorsClosed, l_camWorking, l_odoWorking);
  m_pBlur->step(l_doorsClosed, l_camWorking, l_odoWorking);
  m_pTransp->step(l_doorsClosed, l_camWorking, l_odoWorking);

  // all SM's have an updated state by now, and are aware of degradation, etc

  // based on selected view,  now we set the right uniform values for the floor plate

  // Handle special case for See Through Bonnet - offroad mode without floor plate degradation
  if( static_cast<vfc::uint32_t>(EScreenID_SINGLE_STB) == m_pFramework->getCurrentScreenId() )
  {
    // Floor plate uniforms corresponding to STB
    m_pOnOffToggleUniform->set( m_pStb->isPlateOn() );  // PRQA S 3803
    m_pBlurringMixUniform->set( 1.0f );  // PRQA S 3803
    traverse(f_node, f_nv);
    return;
  }
  else // all non STB-Views behave the same way...
  {
    // Floor Plate uniforms
    m_pOnOffToggleUniform->set( m_pBlur->isPlateOn() );  // PRQA S 3803
    m_pBlurringMixUniform->set( m_pBlur->getBlurLevel() );  // PRQA S 3803

    // Vehicle Model Transparency uniform
    if ( cc::daddy::CustomDaddyPorts::sm_GbcVehicleTransparency_SenderPort.isConnected() )
    {
        cc::daddy::GbcVehicleTransparency_t& l_container = cc::daddy::CustomDaddyPorts::sm_GbcVehicleTransparency_SenderPort.reserve();
        l_container.m_Data = m_pTransp->getVehOpacityLevel();
        cc::daddy::CustomDaddyPorts::sm_GbcVehicleTransparency_SenderPort.deliver();
    }

    // Wheel Model Transparency uniform
    if ( cc::daddy::CustomDaddyPorts::sm_GbcWheelTransparency_SenderPort.isConnected() )
    {
        cc::daddy::GbcWheelTransparency_t& l_container = cc::daddy::CustomDaddyPorts::sm_GbcWheelTransparency_SenderPort.reserve();
        l_container.m_Data = m_pTransp->getWhlOpacityLevel();
        cc::daddy::CustomDaddyPorts::sm_GbcWheelTransparency_SenderPort.deliver();
    }

    traverse(f_node, f_nv);
    return;
  }

  traverse(f_node, f_nv); // PRQA S 2880
}

bool FloorPlateMainCallback::allDoorsClosed() // PRQA S 4211
{
    // check door animation status
    const pc::daddy::DoorAnimationStateDaddy* const l_pData = m_pFramework->m_doorAnimationStateReceiver.getData();
    bool l_allDoorsClosed = true;

    if (nullptr!=l_pData)
    {
        if (  l_pData->m_Data.AnimationOngoingOrOpenFL          ||
              l_pData->m_Data.AnimationOngoingOrOpenFR          ||
              l_pData->m_Data.AnimationOngoingOrOpenRL          ||
              l_pData->m_Data.AnimationOngoingOrOpenRR          ||
              l_pData->m_Data.AnimationOngoingOrOpenTrunk       ||
              l_pData->m_Data.AnimationOngoingOrOpenMirrorLeft  ||
              l_pData->m_Data.AnimationOngoingOrOpenMirrorRight  )
              {
                  l_allDoorsClosed = false;
              }
    }

    // check actual door status signal
    const pc::daddy::DoorStateDaddy* const l_doorStateDaddy = m_pFramework->m_doorStateReceiver.getData();

    if (nullptr != l_doorStateDaddy)
    {
        l_allDoorsClosed = l_allDoorsClosed &&
                           (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) &&
                           (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT ]) &&
                           (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK      ]) &&
                           (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT ]) &&
                           (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT  ]) ;
    }

    // check actual mirror status signal
    const pc::daddy::MirrorStateDaddy* const l_mirrorState = m_pFramework->m_mirrorStateReceiver.getData();

    if (nullptr != l_mirrorState)
    {
        l_allDoorsClosed = l_allDoorsClosed &&
                           (pc::daddy::MIRRORSTATE_NOT_FLAPPED == l_mirrorState->m_Data[pc::daddy::SIDEMIRROR_LEFT]) &&    // PRQA S 3143  #code looks fine.
                           (pc::daddy::MIRRORSTATE_NOT_FLAPPED == l_mirrorState->m_Data[pc::daddy::SIDEMIRROR_RIGHT]) ;    // PRQA S 3143  #code looks fine.
    }

    return l_allDoorsClosed;
}


//!
//! StbMainCallback
//!
StbMainCallback::StbMainCallback(cc::core::CustomFramework* f_pFramework)
  : m_state{STB_INIT}
  , m_pFramework{ f_pFramework}
  , m_initialPose{0.0f, 0.0f, 0.0f}
  , m_plateOn{ false }
{
}

StbMainCallback::~StbMainCallback() = default;

void StbMainCallback::goToOff()
{
  const pc::daddy::OdometryDataDaddy* const l_pDataDaddy =  m_pFramework->m_odometryReceiver.getData();
  if (nullptr != l_pDataDaddy)
  {
    m_initialPose.m_x   = l_pDataDaddy->m_Data.m_xPos.value(); //meter
    m_initialPose.m_y   = l_pDataDaddy->m_Data.m_yPos.value(); //meter
    m_initialPose.m_yaw = osg::RadiansToDegrees(l_pDataDaddy->m_Data.m_yawAngle.value()); //degree
    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "STB: ODOMETRY initial Pose Updated: x: "<<m_initialPose.m_x<< " y: " << m_initialPose.m_y<< " yaw: " <<  m_initialPose.m_yaw);
  }
  else
  {
    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "STB: ODOMETRY not available");
  }

  // Inform SM, so BCC signal can be sent to PIVI
  if ( cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.isConnected() )
  {
      cc::daddy::NFSM_ViewBufferStatus_t& l_container = cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.reserve();
      l_container.m_Data.m_ViewBufferStatus_u8 = 0u;
      cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.deliver();
  }

  m_plateOn = false;
  m_state = STB_OFF;
}

void StbMainCallback::goToOn()
{

  // Temporary solution, so SM receives this information.
  // Inform SM, so BCC signal can be sent to PIVI
  if ( cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.isConnected() )
  {
      cc::daddy::NFSM_ViewBufferStatus_t& l_container = cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.reserve();
      l_container.m_Data.m_ViewBufferStatus_u8 = 1u;
      cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.deliver();
  }

  m_plateOn = true;
  m_state = STB_ON;
}

bool StbMainCallback::inDegradation() // PRQA S 4211
{
  bool l_degradation = false;

  // check cam error
  if (false == m_camWorking)
  {
    l_degradation = true;
    // pf code. #code looks fine
    // STB_LOG( StbOdoCam ) << "STB: Cam not working" << XLOG_ENDL;
  }

  // check odo error
  if (false == m_odoWorking)
  {
    l_degradation = true;
    // pf code. #code looks fine
    // STB_LOG( StbOdoCam ) << "STB: ODO not working" << XLOG_ENDL;
  }

  // check doors open
  if (false == m_doorsClosed)
  {
    l_degradation = true;
  }

  return l_degradation;
}

bool StbMainCallback::inReverseGear() // PRQA S 4211
{
  bool l_inReverseGear = false;

  // check for R-Gear
  if( m_pFramework->m_gearReceiver.isConnected() )
  {
    const pc::daddy::GearDaddy* const l_pData = m_pFramework->m_gearReceiver.getData();
    if (nullptr != l_pData)
    {
      if( pc::daddy::GEAR_R == l_pData->m_Data )
      {
        l_inReverseGear = true;
      }
    }
  }

  return l_inReverseGear;
}

bool StbMainCallback::inRollingBack() // PRQA S 4211
{
  bool l_rollingBack = false;

  // check for moving direction Backwards
  if( m_pFramework->m_drivingDirReceiver.isConnected() )
  {
    const pc::daddy::DrivingDirDaddy* const l_pData = m_pFramework->m_drivingDirReceiver.getData();
    if (nullptr != l_pData)
    {
      if( pc::daddy::DRVDIR_BACKWARD == l_pData->m_Data )
      {
        l_rollingBack = true;
      }
    }
  }
  return l_rollingBack;
}


//    We do not need STB in CN customers
//    STB is typically from JLR

void StbMainCallback::step(bool f_doorsClosed, bool f_camWorking, bool f_odoWorking)
{
    //! Store current degradation values
    m_doorsClosed = f_doorsClosed;
    m_camWorking = f_camWorking;
    m_odoWorking = f_odoWorking;

    if (true == inDegradation())
    {
        goToOff();
        return;
    }

    // Check if R is engaged
    if (true == inReverseGear())
    {
      goToOff();
      return;
    }

    // Check if moving backwards, and count for how long.
    static vfc::uint32_t sl_rollingCounter = 0u;
    if (true == inRollingBack())
    {
      // If this point is reached, it means that the car is rolling backwards while R is NOT engaged. Count for how long...

      // Increase counter and go to OFF
      if (sl_rollingCounter > 90u)
      {
        goToOff();
        return;
      }
      else
      {
        sl_rollingCounter++;
      }
    }
    else
    {
      sl_rollingCounter = 0u;
    }

    // If you reached this point, it means you are not in degradation
    switch(m_state) {

        case STB_INIT:
        {
          goToOff();
        } break;

        case STB_OFF:
        {
          if ( minDistanceReached() )
          {
            goToOn();
          }
          else
          {
            // remain in OFF mode
          }
        } break;

        case STB_ON:
        {
          // System SM has been notified on entering this state
          // STB View shall access this information (ViewBufferStatus)

          // depending on GEAR Signal, the floor has to be shown or not in STB
          if( m_pFramework->m_gearReceiver.isConnected() )
          {
            const pc::daddy::GearDaddy* const l_pData = m_pFramework->m_gearReceiver.getData();
            if (nullptr != l_pData)
            {
              if( pc::daddy::GEAR_R == l_pData->m_Data )
              {
                m_plateOn = false;
              }
              else
              {
                m_plateOn = true;
              }
            }
          }
        } break;

        default:
        {
          // empty on purpose
        } break;

    }
}

bool StbMainCallback::minDistanceReached() // PRQA S 4211
{

  bool l_minDistanceReached = false;

  const pc::daddy::OdometryDataDaddy* const l_pDataDaddy =  m_pFramework->m_odometryReceiver.getData();
  if (nullptr != l_pDataDaddy)
  {
    pc::texfloor::odometry::Pose l_newPose;
    l_newPose.m_x   = l_pDataDaddy->m_Data.m_xPos.value(); //meter
    l_newPose.m_y   = l_pDataDaddy->m_Data.m_yPos.value(); //meter
    l_newPose.m_yaw = osg::RadiansToDegrees(l_pDataDaddy->m_Data.m_yawAngle.value()); //degree

    const pc::texfloor::odometry::Pose l_relativePose = getCurrentRelativeToPrevious(l_newPose, m_initialPose);
    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "STB: minDistanceReached relative Pose x: "<<l_relativePose.m_x<< " y: " << l_relativePose.m_y<< " yaw: " <<  l_relativePose.m_yaw);

    if (l_relativePose.m_x > 0.f) // conditions can not be reached by driving backwards. Req. https://rb-alm-13-p-dwa.de.bosch.com:8443/dwa/rm/urn:rational::1-4147106800294823-O-565-000c6840
    {
      // Special use case where driving forward in full-lock
      if ( std::abs( l_relativePose.m_y ) > g_stbSmData->m_minDrivenDistY )
      {
        l_minDistanceReached = true;
      }
      else
      {
        // lateral conditions not met
      }

      // Standard use case where longitudinal min driven conditions are met
      if ( std::abs(l_relativePose.m_x) > g_stbSmData->m_minDrivenDistX )
      {
        l_minDistanceReached = true;
      }
      else
      {
        // longitudinal conditions not met
      }

    }
    else
    {
      // no forward movement detected
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "STB: minDistanceReached() NOT FORWARD");
    }
  }
  else
  {
    // keep waiting, no odo available
  }

  return l_minDistanceReached;
}

bool StbMainCallback::isPlateOn() // PRQA S 4211
{
    return m_plateOn;
}



pc::util::coding::Item<BlurSmData> g_blurSmData("BlurSm");
pc::util::coding::Item<TranspSmData> g_transpSmData("TranspSm");

//!
//! BlurrinessMainCallback
//!
BlurrinessMainCallback::BlurrinessMainCallback(cc::core::CustomFramework* f_pFramework)
  : m_pFramework{ f_pFramework }
  , m_state{ INIT_BLUR }
  , m_initialPose{0.0f, 0.0f, 0.0f}
  , m_cycleTime_ms{33u}
  , m_blurCounter{0u}
  , m_deblurCounter{0u}
  , m_anim2blurCounter{0u}
  , m_anim2normalCounter{0u}
  , m_plateOn{false}
  , m_blurLevel{0.f}
  , m_blurLevelDuringDriving{1.0f}
  , m_blurStatus{true}
  , m_basePlatePdmWasRead{false}
  , m_basePlateStatusInPdm{cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED}
  , m_ccfEnabled{true}
  , m_minDistanceReached{false}
{
    const vfc::float32_t l_nOfStepsAnim2Blur = g_blurSmData->m_anim2blurDuration / 33.0f; // assuming 30fps -> 33ms per frame
    m_anim2blurDelta = (1.0f - g_blurSmData->m_blurredStateAlpha) / l_nOfStepsAnim2Blur;

    const vfc::float32_t l_nOfStepsAnim2Normal = g_blurSmData->m_anim2normalDuration / 33.0f; // assuming 30fps -> 33ms per frame
    m_anim2normalDelta = (1.0f - g_blurSmData->m_blurredStateAlpha) / l_nOfStepsAnim2Normal;

    m_blurLevelDuringDriving = g_blurSmData->m_blurLevelDuringDriving;
}

BlurrinessMainCallback::~BlurrinessMainCallback() = default;

bool BlurrinessMainCallback::isMovementDirectionStatic() // PRQA S 4211
{
  const pc::daddy::SpeedDaddy* const l_pDataDaddy =  m_pFramework->m_speedReceiver.getData();
  if (nullptr != l_pDataDaddy)
  {
    if ( std::abs(l_pDataDaddy->m_Data) < 1e-3) // no movement
    {
      return true;
    }
    else
    {
      return false;
    }
  }

  // if movement direction not available, assume static
  return true;
}

bool BlurrinessMainCallback::isMovementTooFast() // PRQA S 4211
{
  const pc::daddy::SpeedDaddy* const l_pDataDaddy =  m_pFramework->m_speedReceiver.getData();
  if (nullptr != l_pDataDaddy)
  {
    if ( isGreater(std::abs(l_pDataDaddy->m_Data), g_blurSmData->m_movingTooFastThreshold_Kmh) ) // moving too fast
    {
      return true;
    }
    else
    {
      return false;
    }
  }

  // if movement direction not available, assume static
  return false;
}

void BlurrinessMainCallback::startAnimation2Blur()
{
  // Reset blur counter
  m_blurCounter = 0u;

  // Reset animation counter
  m_anim2blurCounter = 0u;

  // Transition to animation state
  m_state = ANIM2BLURRED;
}

void BlurrinessMainCallback::startAnimation2Normal()
{
  // Reset deblur counter
  m_deblurCounter = 0u;

  // Transition to animation state
  m_state = ANIM2NORMAL;
}

void BlurrinessMainCallback::goToBlurred()
{
  // OnEntry()
  m_blurLevel = g_blurSmData->m_blurredStateAlpha;

  // Transition to blur state
  m_state = BLURRED;
}

void BlurrinessMainCallback::goToNormal()
{
  // OnEntry()
  m_blurLevel = m_blurLevelDuringDriving;

  // Transition to normal state
  m_state = NORMAL;
}

bool BlurrinessMainCallback::inDegradation() // PRQA S 4211
{
  bool l_degradation = false;

  // check cam error
  if (false == m_camWorking)
  {
    l_degradation = true;
    // m_minDistanceReached = false;
    // pf code. #code looks fine
    // STB_LOG( BlurOdoCam ) << "Blur: Cam not working" << XLOG_ENDL;
  }

  // check odo error
  if (false == m_odoWorking)
  {
    l_degradation = true;
    // m_minDistanceReached = false;
    // pf code. #code looks fine
    // STB_LOG( BlurOdoCam ) << "Blur: ODO not working" << XLOG_ENDL;
  }

  // check doors open
  // if (false == m_doorsClosed)
  // {
  //   l_degradation = true;
  // }

  return l_degradation;
}

bool BlurrinessMainCallback::isBlurStatusOn()
{
  //return m_blurStatus;

  //BYD's requirement: the base plate remains transparent state all the time
  return true;

}

void BlurrinessMainCallback::updateBlurStatus()  // PRQA S 6041
{
  // BYD's requirement:
  // Base Plate Texture function always keep in transparent.
  // but the base plate is in non-transparent once ECU initialization without any movement. this is due to
  // transparent depends on vehicle moving/passby area ground texture, then it can fill the texture into the ground below the vehicle body

  // Note: there is no setting item to set on/off the baseplate. That means the function is always ON.

  m_blurStatus = true;


  // // We will follow these steps:
  // // 1. Check if new data is available from HeadUnit(HU)
  // // 2. Check if there is a pdm value available
  // // 3. Set Status based on 1. and 2.
  // // 4. Update Pdm if needed

  // cc::core::CustomFramework* l_framework = m_pFramework->asCustomFramework();

  // // Check if data available from HU, it has the highest priority
  // bool l_newDataFromHU = false;

  // if (l_framework->m_VehTransparenceStsFromSM_Receiver.isConnected())
  // {
  //   const cc::daddy::SVSVehTransStsDaddy_t* l_pData = l_framework->m_VehTransparenceStsFromSM_Receiver.getData();
  //   if (0 != l_pData)
  //   {
  //     if (1u == l_pData->m_Data)          // open
  //     {
  //       m_blurStatus = true;
  //       l_newDataFromHU = true;
  //     }
  //     else if (2u == l_pData->m_Data)     // close
  //     {
  //       m_blurStatus = false;
  //       l_newDataFromHU = true;
  //     }
  //   }
  // }

  // // Read pdm only if it was not read yet
  // if(false == m_basePlatePdmWasRead)
  // {
  //   if ( l_framework->m_pdmBasePlateSts_Receiver.isConnected() )
  //   {
  //     static int l_ctr = -1 ;
  //     const cc::daddy::PdmVehTransStsDaddy_t* l_pData = l_framework->m_pdmBasePlateSts_Receiver.getData();
  //     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
  //     {
  //       l_ctr = l_pData->m_sequenceNumber;
  //       m_basePlateStatusInPdm = l_pData->m_Data;
  //       m_basePlatePdmWasRead = true;
  //     }
  //   }
  // }

  // // Set the status based on Pdm value, only if no pivi data is available
  // if ( (false == l_newDataFromHU) && (true == m_basePlatePdmWasRead) && (cc::target::common::EPdmSvsSetting::PDMSVSSETTING_NONE != m_basePlateStatusInPdm) )
  // {
  //   if (cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED == m_basePlateStatusInPdm)
  //   {
  //     m_blurStatus = true;
  //   }
  //   else
  //   {
  //     m_blurStatus = false;
  //   }
  // }

  // // do we need to update the pdm value?
  // bool l_writeToPdmNeeded = false;
  // if (true == l_newDataFromHU)
  // {
  //   if (cc::target::common::EPdmSvsSetting::PDMSVSSETTING_NONE == m_basePlateStatusInPdm || cc::target::common::EPdmSvsSetting::PDMSVSSETTING_INHIBIT == m_basePlateStatusInPdm) // no value available from pdm
  //   {
  //     l_writeToPdmNeeded = true;
  //   }
  //   else // some value was previously stored in pdm
  //   {
  //     // trigger a write event ONLY if the new value is different from the previous
  //     if( ((cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED==m_basePlateStatusInPdm) && (false==m_blurStatus)) ||
  //         ((cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED==m_basePlateStatusInPdm) && (true==m_blurStatus)) )
  //     {
  //       l_writeToPdmNeeded = true;
  //     }
  //     else
  //     {
  //       l_writeToPdmNeeded = false;
  //     }
  //   }
  // }
  // else
  // {
  //   l_writeToPdmNeeded = false;
  // }

  // // if there is a value change, it must be stored to pdm
  // if ( true == l_writeToPdmNeeded )
  // {
  //   cc::daddy::PdmBasePlateStsDaddy_t& l_rContainer = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_BasePlateStsDaddy_SenderPort.reserve();
  //   if (true == m_blurStatus)
  //   {
  //     l_rContainer.m_Data = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED;
  //     m_basePlateStatusInPdm = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED;
  //   }
  //   else
  //   {
  //     l_rContainer.m_Data = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED;
  //     m_basePlateStatusInPdm = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED;
  //   }
  //   cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_BasePlateStsDaddy_SenderPort.deliver() ;
  // }

  // //! val out
  // if ( true == cc::daddy::CustomDaddyPorts::sm_SVSBasePlateStsDaddy_SenderPort.isConnected() )
  // {
  //   cc::daddy::SVSBasePlateStsDaddy_t& l_rData = cc::daddy::CustomDaddyPorts::sm_SVSBasePlateStsDaddy_SenderPort.reserve();
  //   if ( true == m_blurStatus )
  //   {
  //     l_rData.m_Data = 1u;
  //   }
  //   else
  //   {
  //     l_rData.m_Data = 2u;
  //   }
  //   cc::daddy::CustomDaddyPorts::sm_SVSBasePlateStsDaddy_SenderPort.deliver();
  // }

}

bool BlurrinessMainCallback::minDistanceReached()
{
  const pc::daddy::OdometryDataDaddy* const l_pDataDaddy =  m_pFramework->m_odometryReceiver.getData();
  if ((nullptr != l_pDataDaddy) && (m_minDistanceReached == false))
  {
    pc::texfloor::odometry::Pose l_newPose;
    l_newPose.m_x   = l_pDataDaddy->m_Data.m_xPos.value(); //meter
    l_newPose.m_y   = l_pDataDaddy->m_Data.m_yPos.value(); //meter
    l_newPose.m_yaw = osg::RadiansToDegrees(l_pDataDaddy->m_Data.m_yawAngle.value()); //degree

    const pc::texfloor::odometry::Pose l_relativePose = getCurrentRelativeToPrevious(l_newPose, m_initialPose);

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "Transp: minDistanceReached relative Pose x: "<<l_relativePose.m_x<< " y: " << l_relativePose.m_y<< " yaw: " <<  l_relativePose.m_yaw);
    if (( std::abs(l_relativePose.m_x) > g_transpSmData->m_minDrivenDistX ) || ( std::abs( l_relativePose.m_y ) > g_transpSmData->m_minDrivenDistY ))
    {
      m_minDistanceReached = true;
    }
    else
    {
      // lateral conditions not met
    }
  }
  else
  {
    // keep waiting, no odo available or the minimum of moving distance is already reached
  }
  return m_minDistanceReached;
}

void BlurrinessMainCallback::step(bool f_doorsClosed, bool f_camWorking, bool f_odoWorking)  // PRQA S 6040
{
    vfc::uint32_t l_blurDurnThreshold_ms = g_blurSmData->m_blurDurnThreshold_ms;
    vfc::uint32_t l_deblurDurnThreshold_ms = g_blurSmData->m_deblurDurnThreshold_ms;

    const cc::daddy::ParkStatusDaddy_t* const l_pData = m_pFramework->m_parkHmiParkingStatusReceiver.getData();
    if (nullptr != l_pData)
    {
      if (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_pData->m_Data || cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_pData->m_Data)  // MANOEUVERING
      {
        l_blurDurnThreshold_ms = g_transpSmData->m_opaqueDurnThreshold_Parking_ms;
        l_deblurDurnThreshold_ms = g_transpSmData->m_transpDurnThreshold_Parking_ms;
      }
    }

    //! Store current degradation values
    m_doorsClosed = f_doorsClosed;
    m_camWorking = f_camWorking;
    m_odoWorking = f_odoWorking;

    if ( false == m_ccfEnabled )
    {
      // notify views to use gray color
      m_plateOn = false;    // make it gray

      goToNormal();         // The state to go when getting back to Gbc View

      return;
    }
    else
    {
      m_plateOn = true;
    }

    // keep default value, disable update status
    updateBlurStatus();

    // Initial checks: in degradation or Blur status off?
    if (inDegradation() || !isBlurStatusOn())
    {
      // notify views to use gray color
      m_plateOn = false; // make it gray

      if(minDistanceReached())
      {
        if ((NORMAL != m_state) && (ANIM2NORMAL != m_state))
        {
          // start the Animation 2 Normal to remove the bulr effect
          startAnimation2Normal();
        }
      }
      else
      {
        if ((BLURRED != m_state) && (ANIM2BLURRED != m_state))
        {
          // go to blurred state
          goToBlurred(); // do blur (needed when we leave degradation state)
        }
      }

      return;
    }
    else
    {
      m_plateOn = true;
    }

//  Van Nguyen:
//                                             INIT_BLUR
//                                     (baseplate texture is gray color)
//                                                 |
//                                                 |
//                                                 v
//           (check if blur level > 100%)       NORMAL                    (check if vehicle is stanstill)
//           --------------------------->  (blur level is set to 100%)  -------------------------
//           ^                                                                                  |
//           |                                                                                  |   (if the minDistanceReached, remove this transition)
//           |                                                                                  |
//           |       (check if vehicle is stanstill and !minDistanceReached)                    V
//                                      ---------------------------------->
//       ANIM2NORMAL                                                                      ANIM2BLURRED
// (blur level = blur level + Delta)   <----------------------------------         (blur level = blur level - Delta)
//           ^                           (check if vehicle is moving                            |
//           |                             or minDistanceReached )                              | (check if blur level is equal to
//           |(check if vehicel is moving or minDistanceReached)                                |   anpla value in coding parameter)
//           |                                                                                  V
//           ---------------------------------   BLURRED    <------------------------------------
//
//                     (blur level is set anpla value in coding parameter of blur effect
//                       baseplate texture is transparent by environment texture but have gray blur effect)
//                                                 ^
//               base plate is OFF  -------------->| (baseplate texture is gray color as INIT_BLUR state))
//                                                 |<-------------- in Degradation
//                                                 |

    // If you reached this point, it means you are not in degradation
    switch(m_state) {

        case INIT_BLUR:
        {
          goToNormal();
        } break;

        case NORMAL:
        {
          if( ( !isMovementDirectionStatic() ) && (!isMovementTooFast()))
          {
            m_blurCounter = 0u;
          }
          else
          {
            ////////////   Baseplate transparency during standstill after driving some distance   ////////////
            if ( (minDistanceReached()) && (!isMovementTooFast()))
            {
              // set the blur counter to zero to prevent the Animation 2 burl effect
              //   to remove the bulr effect when during vehicle is standstill or moving state
              m_blurCounter = 0u;
            }
            else
            {
              // keep blur effect if the vehicle's movement in longitudinal or lateral is not enough
               m_blurCounter++;
            }
          }

          // check exit conditions
          // if ( (m_blurCounter * m_cycleTime_ms) > g_blurSmData->m_blurDurnThreshold_ms)
          if ( (m_blurCounter * m_cycleTime_ms) > l_blurDurnThreshold_ms)
          {
            startAnimation2Blur();
          }
          else
          {
            // keep waiting for timeout
          }

        } break;

        case ANIM2BLURRED:
        {
          // Increment
          m_blurLevel = m_blurLevel - m_anim2blurDelta;

          // handle static case where animation may have to be reverted
          if(( !isMovementDirectionStatic() ) && (!isMovementTooFast()))
          {
            m_deblurCounter++;
          }
          else
          {
            ////////////   Baseplate transparency during standstill after driving some distance   ////////////
            if((minDistanceReached()) && (!isMovementTooFast()))
            {
              // set the deblur counter to big enough to start the Animation 2 Normal
              //   to remove the bulr effect when during vehicle is standstill
              m_deblurCounter = 255u;  //
            }
            else
            {
              // keep blur effect if the vehicle's movement in longitudinal or lateral is not enough
              m_deblurCounter = 0u;
            }
          }

          // check exit conditions
          // if ( (m_deblurCounter * m_cycleTime_ms) > g_blurSmData->m_deblurDurnThreshold_ms )
          if ( (m_deblurCounter * m_cycleTime_ms) > l_deblurDurnThreshold_ms )
          {
            startAnimation2Normal();
          }
          else if (m_blurLevel < g_blurSmData->m_blurredStateAlpha)
          {
            goToBlurred();
          }
          else
          {
            // do nothing
          }

        } break;

        case BLURRED:
        {

          if(( !isMovementDirectionStatic() ) && (!isMovementTooFast()))
          {
            m_deblurCounter++;
          }
          else
          {
            ////////////   Baseplate transparency during standstill after driving some distance   ////////////
            if((minDistanceReached())  && (!isMovementTooFast()))
            {
              // set the deblur counter to big enough to start the Animation 2 Normal
              //   to remove the bulr effect when during vehicle is standstill
              m_deblurCounter = 255u;  //
            }
            else
            {
              // keep blur effect if the vehicle's movement in longitudinal or lateral is not enough
              m_deblurCounter = 0u;
            }
          }

          // check exit conditions
          // if ( (m_deblurCounter * m_cycleTime_ms) > g_blurSmData->m_deblurDurnThreshold_ms )
          if ( (m_deblurCounter * m_cycleTime_ms) > l_deblurDurnThreshold_ms )
          {
            startAnimation2Normal();
          }
          else
          {
            // remain in blurred state
          }

        } break;

        case ANIM2NORMAL:
        {
          // Increment
          m_blurLevel = m_blurLevel + m_anim2normalDelta;

          // handle static case where animation may have to be reverted
          if(( !isMovementDirectionStatic() )  && (!isMovementTooFast()))
          {
            m_blurCounter = 0u;
          }
          else
          {
            ////////////   Baseplate transparency during standstill after driving some distance   ////////////
            if((minDistanceReached()) && (!isMovementTooFast()))
            {
              // set the blur counter to zero to prevent the Animation 2 burl effect
              //   to remove the bulr effect when during vehicle is standstill or moving state
              m_blurCounter = 0u;
            }
            else
            {
              // keep blur effect if the vehicle's movement in longitudinal or lateral is not enough
               m_blurCounter++;
            }
          }

          // check exit conditions
          // if ( (m_blurCounter * m_cycleTime_ms) > g_blurSmData->m_blurDurnThreshold_ms)
          if ( (m_blurCounter * m_cycleTime_ms) > l_blurDurnThreshold_ms)
          {
            startAnimation2Blur();
          }
          else if( m_blurLevel >= m_blurLevelDuringDriving ) // Alpha value of 1.0 corresponds to opaque gray floor plate
          {
            goToNormal();
          }
          else
          {
            // do nothing
          }

        } break;

        default:
        {
          // empty on purpose
        } break;
    }
}

bool BlurrinessMainCallback::isPlateOn()
{
    //return m_plateOn;
    //BYD's requirement: the base plate remains transparent state all the time
    return true;
}

vfc::float32_t BlurrinessMainCallback::getBlurLevel()
{
    return m_blurLevel;
}




//!
//! TransparencyMainCallback
//!
TransparencyMainCallback::TransparencyMainCallback(cc::core::CustomFramework* f_pFramework)
  : m_pFramework{f_pFramework}
  , m_state{INIT_TRANSP}
  , m_initialPose{0.0f, 0.0f, 0.0f}
  , m_opaqueCounter{0u}
  , m_transpCounter{0u}
  , m_vehOpacityLevel{1.f}
  , m_whlOpacityLevel{1.f}
  , m_cycleTime_ms{33u}
  , m_gbcStatus{true}
  , m_pdmWasRead{false}
  , m_GbcStatusInPdm{cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED}
  , m_ccfEnabled{true}
  , m_minDistanceReached{false}
{
  const vfc::float32_t l_nOfStepsTransn2Opaque = g_transpSmData->m_transition2opaqueDuration/ 33.0f; // assuming 30fps -> 33ms per frame
  m_vehtransp2opaqueDelta = (1.f - g_transpSmData->m_vehicleOpacity) / l_nOfStepsTransn2Opaque;
  m_whltransp2opaqueDelta = (1.f - g_transpSmData->m_wheelOpacity) / l_nOfStepsTransn2Opaque;

  const vfc::float32_t l_nOfStepsTransn2Transp = g_transpSmData->m_transition2transpDuration/ 33.0f; // assuming 30fps -> 33ms per frame
  m_vehopaque2transpDelta = (1.f - g_transpSmData->m_vehicleOpacity) / l_nOfStepsTransn2Transp;
  m_whlopaque2transpDelta = (1.f - g_transpSmData->m_wheelOpacity) / l_nOfStepsTransn2Transp;

  const vfc::float32_t l_nOfStepsTransn2Opaque_Parking = g_transpSmData->m_transition2opaqueDuration_Parking/ 33.0f; // assuming 30fps -> 33ms per frame
  m_vehtransp2opaqueDelta_Parking = (1.f - g_transpSmData->m_vehicleOpacity) / l_nOfStepsTransn2Opaque_Parking;
  m_whltransp2opaqueDelta_Parking = (1.f - g_transpSmData->m_wheelOpacity) / l_nOfStepsTransn2Opaque_Parking;

  const vfc::float32_t l_nOfStepsTransn2Transp_Parking = g_transpSmData->m_transition2transpDuration_Parking/ 33.0f; // assuming 30fps -> 33ms per frame
  m_vehopaque2transpDelta_Parking = (1.f - g_transpSmData->m_vehicleOpacity) / l_nOfStepsTransn2Transp_Parking;
  m_whlopaque2transpDelta_Parking = (1.f - g_transpSmData->m_wheelOpacity) / l_nOfStepsTransn2Transp_Parking;
}

TransparencyMainCallback::~TransparencyMainCallback() = default;

void TransparencyMainCallback::startTransition2Transparent()
{
  // Reset transparent counter
  m_transpCounter = 0u;

  // Transition to Transparent state
  m_state = OPAQUE2TRANSP;

}

void TransparencyMainCallback::startTransition2Opaque()
{
  // Reset Opaque counter
  m_opaqueCounter = 0u;

  // Transition to Opaque state
  m_state = TRANSP2OPAQUE;

}


bool TransparencyMainCallback::isMovementDirectionStatic() // PRQA S 4211
{
  const pc::daddy::SpeedDaddy* const l_pDataDaddy =  m_pFramework->m_speedReceiver.getData();
  if (nullptr != l_pDataDaddy)
  {
    //if ( ( std::abs(l_pDataDaddy->m_Data) < ( 1e-3 + g_stbSmData->m_speedTrigVehTrans_lower_kph ) ) || ( std::abs(l_pDataDaddy->m_Data) > g_stbSmData->m_speedTrigVehTrans_upper_kph ) ) // no movement or speed too high
    if ( std::abs(l_pDataDaddy->m_Data) < ( 1e-3 + g_stbSmData->m_speedTrigVehTrans_lower_kph ) ) // no movement
    {
      return true;
    }
    else
    {
      return false;
    }
  }

  // if movement direction not available, assume static
  return true;
}

bool TransparencyMainCallback::updateInitialPosition()
{
  const pc::daddy::OdometryDataDaddy* const l_pDataDaddy =  m_pFramework->m_odometryReceiver.getData();
  if (nullptr != l_pDataDaddy)
  {
    m_initialPose.m_x   = l_pDataDaddy->m_Data.m_xPos.value(); //meter
    m_initialPose.m_y   = l_pDataDaddy->m_Data.m_yPos.value(); //meter
    m_initialPose.m_yaw = osg::RadiansToDegrees(l_pDataDaddy->m_Data.m_yawAngle.value()); //degree

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "TRANSP: ODOMETRY initial Pose Updated: x: "<<m_initialPose.m_x<< " y: " << m_initialPose.m_y<< " yaw: " <<  m_initialPose.m_yaw );
    return true;
  }
  return false;
}

bool TransparencyMainCallback::minDistanceReached()
{
  const pc::daddy::OdometryDataDaddy* const l_pDataDaddy =  m_pFramework->m_odometryReceiver.getData();
  if ((nullptr != l_pDataDaddy) && (m_minDistanceReached == false))
  {
    pc::texfloor::odometry::Pose l_newPose;
    l_newPose.m_x   = l_pDataDaddy->m_Data.m_xPos.value(); //meter
    l_newPose.m_y   = l_pDataDaddy->m_Data.m_yPos.value(); //meter
    l_newPose.m_yaw = osg::RadiansToDegrees(l_pDataDaddy->m_Data.m_yawAngle.value()); //degree

    const pc::texfloor::odometry::Pose l_relativePose = getCurrentRelativeToPrevious(l_newPose, m_initialPose);

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "Transp: minDistanceReached relative Pose x: "<<l_relativePose.m_x<< " y: " << l_relativePose.m_y<< " yaw: " <<  l_relativePose.m_yaw );
    if (( std::abs(l_relativePose.m_x) > g_transpSmData->m_minDrivenDistX ) || ( std::abs( l_relativePose.m_y ) > g_transpSmData->m_minDrivenDistY ))
    {
      m_minDistanceReached = true;
    }
    else
    {
      // lateral conditions not met
    }
  }
  else
  {
    // keep waiting, no odo available or the minimum of moving distance is already reached
  }
  return m_minDistanceReached;
}

bool TransparencyMainCallback::inDegradation() // PRQA S 4211
{
  bool l_degradation = false;

  // check cam error
  if (false == m_camWorking)
  {
    l_degradation = true;
    // m_minDistanceReached = false;
    // pf code. #code looks fine
    // STB_LOG( TranspOdocam ) << "Transp: Cam not working" << XLOG_ENDL;
  }

  // check odo error
  if (false == m_odoWorking)
  {
    l_degradation = true;
    // m_minDistanceReached = false;
    // pf code. #code looks fine
    // STB_LOG( TranspOdocam ) << "Transp: ODO not working" << XLOG_ENDL;
  }

  // check doors open
  if (false == m_doorsClosed)
  {
    l_degradation = true;
  }

  return l_degradation;
}

void TransparencyMainCallback::goToOpaque(bool updatePos)
{
  if(updatePos)
  {
    updateInitialPosition();  // Update Odometry initial position on Init or when in degradation  // PRQA S 3804
  }

  // make opaque
  m_vehOpacityLevel = 1.0f;
  m_whlOpacityLevel = 1.0f;

  // Transition to opaque state
  m_state = OPAQUE;
}

void TransparencyMainCallback::goToTransp()
{
  // OnEntry()
  m_vehOpacityLevel = g_transpSmData->m_vehicleOpacity;    // set the opacity value as obtained from Coding
  m_whlOpacityLevel = g_transpSmData->m_wheelOpacity;      // set the opacity value as obtained from Coding

  // Transition to transparent state
  m_state = TRANSPARENT;
}

bool TransparencyMainCallback::isOverlayStatusOn()
{
  constexpr bool l_overlayStatus = true;
  // remained, may be needed afterwards
  return l_overlayStatus;
}

bool TransparencyMainCallback::isGBCStatusOn() // PRQA S 4211
{
  return m_gbcStatus;
}

bool TransparencyMainCallback::trailerBasedDegradation() // PRQA S 4211
{
  // Returns if there is degradation in GBC when a Trailer is connected

  bool l_gearInReverse = false;
  if( m_pFramework->m_gearReceiver.isConnected() )
  {
    const pc::daddy::GearDaddy* const l_pGearData = m_pFramework->m_gearReceiver.getData();
    if (nullptr != l_pGearData)
    {
      l_gearInReverse = ( pc::daddy::GEAR_R == l_pGearData->m_Data );
    }
  }
  else{
    return true;    // No info on Gear; hence degrade vehicle transparency
  }

  if (m_pFramework->m_trailerConnectionReceiver.isConnected())
  {
    const pc::daddy::TrailerConnectedDaddy* const l_pTrailerConnected = m_pFramework->m_trailerConnectionReceiver.getData();

    if (nullptr != l_pTrailerConnected)
    {
        if(pc::daddy::TRAILER_DISCONNECTED == l_pTrailerConnected->m_Data)
        {
          return false; // No trailer and hence no degradation due to trailer
        }
        else
        {
          if(true == l_gearInReverse)
          {
            return true;    // Trailer Connected and Vehicle in Reverse -> GBC degradation
          }
          else
          {
            return false;   // Trailer Connected but Vehicle not in Reverse -> No GBC degradation
          }
        }
    }
  }
  else{
    return true;    // No info on trailer connection, so degrade the transparency
  }

  // If code reached here, there are problems in data within the DADDY port, hence degrade transparency
  return true;
}

void TransparencyMainCallback::updateGbcStatus()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{
  // We will follow these steps:
  // 1. Check if new data is available from HU
  // 2. Check if there is a pdm value available
  // 3. Set GBC Status based on 1. and 2.
  // 4. Update Pdm if needed

  cc::core::CustomFramework* const l_framework = m_pFramework->asCustomFramework();

  // Check if data available from Pivi, it has the highest priority
  bool l_newDataFromPivi = false;

  if (l_framework->m_VehTransparenceStsFromSM_Receiver.isConnected())
  {
    const cc::daddy::SVSVehTransStsDaddy_t* const l_pData = l_framework->m_VehTransparenceStsFromSM_Receiver.getData();

    if ( nullptr != l_pData )
    {
      //status can be 0 = Disabled or 1 = Enabled
      if (1u == l_pData->m_Data)          // open
      {
        m_gbcStatus = true;
        l_newDataFromPivi = true;
      }
      else if (2u == l_pData->m_Data)     // close
      {
        m_gbcStatus = false;
        l_newDataFromPivi = true;
      }
      else
      {
        // Do nothing
      }
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "### [GbcStatus] New Data From Pivi: " << static_cast<vfc::uint32_t>(m_gbcStatus));
    }

  }

  // Read pdm only if it was not read yet
  if(false == m_pdmWasRead)
  {
    if ( l_framework->m_pdmVehTransSts_Receiver.isConnected() )
    {
      static vfc::int32_t l_ctr = -1 ;
      const cc::daddy::PdmVehTransStsDaddy_t* const l_pData = l_framework->m_pdmVehTransSts_Receiver.getData();
      if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
      {
        l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber);
        m_GbcStatusInPdm = l_pData->m_Data;
        m_pdmWasRead = true;
        // pf code. #code looks fine
        XLOG_INFO(g_EngineContext, "[GbcStatus] Reading value from PDM: " << static_cast<vfc::uint32_t>(m_GbcStatusInPdm));
      }
    }
  }

  // Set the gbc status based on Pdm value, only if no pivi data is available
  if ( (false == l_newDataFromPivi) && (true == m_pdmWasRead) && (cc::target::common::EPdmSvsSetting::PDMSVSSETTING_NONE != m_GbcStatusInPdm) )
  {
    if (cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED == m_GbcStatusInPdm)
    {
      m_gbcStatus = true;
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "### [GbcStatus] Enabled by PDM");
    }
    else
    {
      m_gbcStatus = false;
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "### [GbcStatus] Disabled by PDM");
    }
  }

  // do we need to update the pdm value?
  bool l_writeToPdmNeeded = false;
  if (true == l_newDataFromPivi)
  {
    if (cc::target::common::EPdmSvsSetting::PDMSVSSETTING_NONE == m_GbcStatusInPdm || cc::target::common::EPdmSvsSetting::PDMSVSSETTING_INHIBIT == m_GbcStatusInPdm) // no value available from pdm
    {
      l_writeToPdmNeeded = true;
      // pf code. #code looks fine
      XLOG_INFO(g_EngineContext, "[GbcStatus] Write to Pdm triggered because nothing was written before, and there's a new status from pivi: " << static_cast<vfc::uint32_t>(m_gbcStatus));
    }
    else // some value was previously stored in pdm
    {
      // trigger a write event ONLY if the new value is different from the previous
      if( ((cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED==m_GbcStatusInPdm) && (false==m_gbcStatus)) ||
          ((cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED==m_GbcStatusInPdm) && (true==m_gbcStatus)) )
      {
        l_writeToPdmNeeded = true;
        // pf code. #code looks fine
        XLOG_INFO(g_EngineContext, "[GbcStatus] Write to Pdm triggered because new and stored values do not match: " << static_cast<vfc::uint32_t>(m_gbcStatus));
      }
      else
      {
        l_writeToPdmNeeded = false;
      }
    }
  }
  else
  {
    l_writeToPdmNeeded = false;
  }


  // if there is a value change, it must be stored to pdm
  if ( true == l_writeToPdmNeeded )
  {
    cc::daddy::PdmVehTransStsDaddy_t& l_rContainer = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.reserve();
    if (true == m_gbcStatus)
    {
      l_rContainer.m_Data = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED;
      m_GbcStatusInPdm = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED;
    }
    else
    {
      l_rContainer.m_Data = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED;
      m_GbcStatusInPdm = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED;
    }
    cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.deliver() ;
  }

  //! val out
  // if ( true == cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.isConnected() )
  // {
  //   cc::daddy::SVSVehTransStsDaddy_t& l_rData = cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.reserve();
  //   if ( true == m_gbcStatus )
  //   {
  //     l_rData.m_Data = 1u;
  //   }
  //   else
  //   {
  //     l_rData.m_Data = 2u;
  //   }
  //   cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.deliver();
  // }
}

void TransparencyMainCallback::step(bool f_doorsClosed, bool f_camWorking, bool f_odoWorking)  // PRQA S 6040  // PRQA S 6041
{
    vfc::uint32_t l_transpDurnThreshold_ms = g_transpSmData->m_transpDurnThreshold_ms;
    vfc::uint32_t l_opaqueDurnThreshold_ms = g_transpSmData->m_opaqueDurnThreshold_ms;
    vfc::float32_t l_vehtransp2opaqueDelta = m_vehtransp2opaqueDelta;
    vfc::float32_t l_vehopaque2transpDelta = m_vehopaque2transpDelta;
    vfc::float32_t l_whltransp2opaqueDelta = m_whltransp2opaqueDelta;
    vfc::float32_t l_whlopaque2transpDelta = m_whlopaque2transpDelta;

    const cc::daddy::ParkStatusDaddy_t* const l_pData = m_pFramework->m_parkHmiParkingStatusReceiver.getData();
    if (nullptr != l_pData)
    {
      if (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_pData->m_Data || cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_pData->m_Data)  // MANOEUVERING
      {
        l_transpDurnThreshold_ms = g_transpSmData->m_transpDurnThreshold_Parking_ms;
        l_opaqueDurnThreshold_ms = g_transpSmData->m_opaqueDurnThreshold_Parking_ms;
        l_vehtransp2opaqueDelta = m_vehtransp2opaqueDelta_Parking;
        l_vehopaque2transpDelta = m_vehopaque2transpDelta_Parking;
        l_whltransp2opaqueDelta = m_whltransp2opaqueDelta_Parking;
        l_whlopaque2transpDelta = m_whlopaque2transpDelta_Parking;
      }
    }

    //! Store current degradation values
    m_doorsClosed = f_doorsClosed;
    m_camWorking = f_camWorking;
    m_odoWorking = f_odoWorking;

    if( false == m_ccfEnabled )
    {
      goToOpaque(true); // goto Opaque and update initial position for odometry
      return;
    }

    // keep default value, disable update status
    updateGbcStatus();

    if(inDegradation())
    {
      if((updateInitialPosition()) && (OPAQUE != m_state) && (OPAQUE2TRANSP != m_state))
      {
        startTransition2Opaque();
      }
    }

    if( !isOverlayStatusOn() ||
       (!isGBCStatusOn() && m_state!= TRANSP2OPAQUE && m_state!= OPAQUE) )
    {
      startTransition2Opaque();
    }

//  Van Nguyen:
//                                                    INIT_TRANSP
//                                                        |
//                      base plate is OFF  -------------->|
//                                                        |<-------------- in Degradation
//                                                        v
//           (check if Opacity level > 100%)            OPAQUE                    [check if vehicle is moving and minDistanceReached]
//           --------------------------->  (Opacity level is set to 100%)   -------------------------
//           ^                                                                                      |
//           |                                                                                      |
//           |                                                                                      |
//           |                          [check if vehicle is moving and the minDistanceReached]     V
//                                           ---------------------------------->
//       TRANSP2OPAQUE                                                                          OPAQUE2TRANSP
// (Opacity level = Opacity level + Delta)   <----------------------------------             (Opacity level = Opacity level - Delta)
//           ^                                [check if vehicle is stanstill]                       |
//           |                                                                                      | [check if Opacity level is less than
//           |[check if vehicel is stanstill]                                                       |   Opacity value in coding parameter]
//           |                                                                                      V
//           ---------------------------------   TRANSPARENT    <------------------------------------
//                     (Opacity level is set to anpla value in coding parameter of vehicle model transparent effect)
//
//  []: and base plate is ON


    switch(m_state){

        case INIT_TRANSP:
        {
          // By default be in Opaque state and update initial Odometry position
          goToOpaque(true);
        } break;

        case OPAQUE:
        {
          if( (!isMovementDirectionStatic()) && isOverlayStatusOn() && isGBCStatusOn() && (!inDegradation()))
          {
            m_transpCounter++;
          }
          else
          {
            m_transpCounter = 0u;
          }

          // check exit conditions
          // if (( (m_transpCounter * m_cycleTime_ms) > g_transpSmData->m_transpDurnThreshold_ms ) && !inDegradation())
          if (( (m_transpCounter * m_cycleTime_ms) > l_transpDurnThreshold_ms ) && (!inDegradation()))
          {
            if(minDistanceReached())
            {
              startTransition2Transparent();
            }
            else
            {
              // keep waiting for timeout
            }
          }
        }
        break;

        case OPAQUE2TRANSP:
        {
          if(isOverlayStatusOn() && isGBCStatusOn())
          {
            // decrement opacity level
              // m_vehOpacityLevel = m_vehOpacityLevel - m_vehopaque2transpDelta;
              // m_whlOpacityLevel = m_whlOpacityLevel - m_whlopaque2transpDelta;
              m_vehOpacityLevel = m_vehOpacityLevel - l_vehopaque2transpDelta;
              m_whlOpacityLevel = m_whlOpacityLevel - l_whlopaque2transpDelta;
          }

          // Handle static case where transition may have to be reverted
          if( !isMovementDirectionStatic() )
          {
            m_opaqueCounter = 0u;
          }
          else if (isOverlayStatusOn() && isGBCStatusOn())
          {
            m_opaqueCounter++;
          }
          else
          {
            // do nothing
          }


          // check exit conditions
          // if (( (m_opaqueCounter * m_cycleTime_ms) > g_transpSmData->m_opaqueDurnThreshold_ms ) || inDegradation())
          if (( (m_opaqueCounter * m_cycleTime_ms) > l_opaqueDurnThreshold_ms ) || inDegradation())
          {
            startTransition2Opaque();
          }
          else if ( (m_vehOpacityLevel < g_transpSmData->m_vehicleOpacity) && isOverlayStatusOn() && isGBCStatusOn() )
          {
            goToTransp();
          }
          else
          {
            // do nothing
          }

        } break;

        case TRANSPARENT:
        {
          if( !isMovementDirectionStatic() )
          {
            m_opaqueCounter = 0u;
          }
          else if (isOverlayStatusOn() && isGBCStatusOn())
          {
            m_opaqueCounter++;
          }
          else
          {
            // do nothing
          }

          // check exit conditions
          // if ( ( (m_opaqueCounter * m_cycleTime_ms) > g_transpSmData->m_opaqueDurnThreshold_ms) || inDegradation() )
          if ( ( (m_opaqueCounter * m_cycleTime_ms) > l_opaqueDurnThreshold_ms) || inDegradation() )
          {
            startTransition2Opaque();
          }
          else
          {
            // remain in Transparent state
          }

        } break;

        case TRANSP2OPAQUE:
        {
          // Increment opacity levels
          // Opacity levels not to be increased above 100%
          if(m_vehOpacityLevel < 1.0f)
          {
            // m_vehOpacityLevel = m_vehOpacityLevel + m_vehtransp2opaqueDelta;
            m_vehOpacityLevel = m_vehOpacityLevel + l_vehtransp2opaqueDelta;
          }
          if(m_whlOpacityLevel < 1.0f)
          {
            // m_whlOpacityLevel = m_whlOpacityLevel + m_whltransp2opaqueDelta;
            m_whlOpacityLevel = m_whlOpacityLevel + l_whltransp2opaqueDelta;
          }

          // Handle static case where transition may have to be reverted
          if( (!isMovementDirectionStatic()) && isOverlayStatusOn() && isGBCStatusOn() && (!inDegradation()) )
          {
            m_transpCounter++;
          }
          else
          {
            m_transpCounter = 0u;
          }

          // check exit conditions
          // if (( (m_transpCounter * m_cycleTime_ms) > g_transpSmData->m_transpDurnThreshold_ms ) && !inDegradation())
          if (( (m_transpCounter * m_cycleTime_ms) > l_transpDurnThreshold_ms ) && (!inDegradation()))
          {
            if(minDistanceReached())
            {
              startTransition2Transparent();
            }
            else
            {
              // keep waiting for timeout
            }
          }
          else if ( m_vehOpacityLevel >= 1.0f )
          {
            goToOpaque();
          }
          else
          {
            // do nothing
          }

        } break;

        default:
        {
          // empty on purpose
        } break;
    }
}

vfc::float32_t TransparencyMainCallback::getVehOpacityLevel()
{
    return m_vehOpacityLevel;
}

vfc::float32_t TransparencyMainCallback::getWhlOpacityLevel()
{
    return m_whlOpacityLevel;
}

} // namespace common
} // namespace assets
} // namespace cc

