//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: YRN1WX Yang Rui (BCSC-EPA1)
//  Department: BCSC-EPA1
//=============================================================================
/// @swcomponent SVS BYD
/// @file  HoriParkingView.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_PARKINGVIEW_H
#define CC_ASSETS_UIELEMENTS_PARKINGVIEW_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"
#include <osg/Matrixf>
#include <osgDB/ReadFile>
#include <map>

namespace cc
{

namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{


// ! enum values for parking modes
enum ParkingProgressType : unsigned int
{
  // parking searching
  //PARKING_SEARCHING_BACKGROUND,
  PARKING_SEARCHING_AUTO_PIC,
  PARKING_SEARCHING_SLOT_SEARCHING,
  PARKING_SEARCHING_AT_LOW_SPEED,
  PARKING_SEARCHING_TEXT_TIME_OUT,
  PARKING_SEARCHING_TIME_OUT,
  PARKING_SEARCHING_TEXT_SLOW_DOWN,
  PARKING_SEARCHING_SLOW_DOWN,
  PARKING_SEARCHING_FIND_SLOT_STOP,
  PARKING_SEARCHING_POC_DIREC_SELECT_VEHICLE,
  PARKING_SEARCHING_POC_DIREC_SELECT_VEHICLE_TRANS,
  PARKING_SEARCHING_POC_DIREC_SELECT_STEERING_WHEEL,
  PARKING_SEARCHING_POC_DIREC_SELECT_STEERING_WHEEL_TRANS,
  PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_TOP,
  PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_BOTTOM,
  PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_BOTTOM_TRANS,
  PARKING_SEARCHING_TOP_VIEW,
  PARKING_SEARCHING_DOOR_FRONT_RIGHT_OPEN,
  PARKING_SEARCHING_DOOR_FRONT_LEFT_OPEN,
  PARKING_SEARCHING_TRUNK_OPEN,
  PARKING_SEARCHING_DOOR_REAR_RIGHT_OPEN,
  PARKING_SEARCHING_DOOR_REAR_LEFT_OPEN,
  PARKING_SEARCHING_HOOD_OPEN,
  PARKING_SEARCHING_DOORS_OPEN,
  PARKING_SEARCHING_SELECT_PARKOUT_DIRECTION,

  // start parking
  START_PARKING_BUTTON_START_PARK_IN,
  START_PARKING_BUTTON_START_PARK_OUT,
  START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE,
  START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE,
  START_PARKING_PARKING_IN,

  // guidance activate
  PARKING_GUIDANCE_GEAR_D,
  PARKING_GUIDANCE_GEAR_N,
  PARKING_GUIDANCE_GEAR_R,
  PARKING_GUIDANCE_GEAR_P,
  PARKING_GUIDANCE_APA_PARKING_IN,
  PARKING_GUIDANCE_APA_PARKING_OUT,
  PARKING_GUIDANCE_NOTICE_SUR,
  PARKING_GUIDANCE_CONTINUE_DRIVE_DISTANCE,
  PARKING_GUIDANCE_MOVES_LEFT_NUMBER,
  PARKING_GUIDANCE_TEXT_RELEASE_BRAKE_AND_STEERING,
  PARKING_GUIDANCE_BUTTON_SUSPEND,
  PARKING_GUIDANCE_PARKINGOUT_PAYATTENTION_ENV,

  // park finished
  PARKING_COMPLETE_FINISHED,
  PARKING_COMPLETE_TAKEOVER,

  // suspend
  PARKING_SUSPEND_TEXT_PARKING_PAUSE,
  PARKING_SUSPEND_QUIT_IN30S,
  PARKING_SUSPEND_BUTTON_CONTINUE,
  PARKING_SUSPEND_BUTTON_CONTINUE_GRAY,
  PARKING_SUSPEND_TEXT_OBJECT_ON_PATH,
  PARKING_SUSPEND_OBJECT_ON_PATH,
  PARKING_SUSPEND_OBJECT_ON_PATH_ICON,
  PARKING_SUSPEND_TEXT_DOOR_OPEN,
  PARKING_SUSPEND_TEXT_BRAKE_PEDAL,
  PARKING_SUSPEND_BRAKE_PEDAL,
  PARKING_SUSPEND_TEXT_MIRROR_FOLD,
  PARKING_SUSPEND_MIRROR_FOLD,
  PARKING_SUSPEND_TEXT_HOOD_OPEN,
  PARKING_SUSPEND_HOOD_OPEN,
  PARKING_SUSPEND_TEXT_TRUNK_OPEN,
  PARKING_SUSPEND_TRUNK_OPEN,
  PARKING_SUSPEND_TEXT_USER_PAUSE,
  PARKING_SUSPEND_GUIDELINE_CROSSSLOT_LEFT,
  PARKING_SUSPEND_GUIDELINE_CROSSSLOT_RIGHT,

  // assist standby
  PARKING_ASSIST_STANDBY_RESPONSE_TIMEOUT,

  // quit
  PARKING_QUIT_APA_QUIT_TAKE_OVER,
  PARKING_QUIT_TEXT_BELT_UNBUCKLE,
  PARKING_QUIT_BELT_UNBUCKLE,
  PARKING_QUIT_TEXT_EXCESSIVE_SLOP,
  PARKING_QUIT_EXCESSIVE_SLOP,
  PARKING_QUIT_TEXT_DRIVER_OVERRIDE,
  PARKING_QUIT_DRIVER_OVERRIDE,
  PARKING_QUIT_TEXT_ROUTE_PLANNING_FAILURE,
  PARKING_QUIT_ROUTE_PLANNING_FAILURE,
  PARKING_QUIT_TEXT_VEHICLE_SPEED_OVER_THRESHOLD,
  PARKING_QUIT_VEHICLE_SPEED_OVER_THRESHOLD,
  PARKING_QUIT_TEXT_APS_TIMEOUT,
  PARKING_QUIT_APS_TIMEOUT,
  PARKING_QUIT_TEXT_CURRENT_STEP_NUMBER_OVER_THRESHOLD,
  PARKING_QUIT_CURRENT_STEP_NUMBER_OVER_THRESHOLD,
  PARKING_QUIT_TEXT_SPACE_IS_LIMITED_IN_PARK_OUT_MODE,
  PARKING_QUIT_SPACE_IS_LIMITED_IN_PARK_OUT_MODE,
  PARKING_QUIT_TEXT_EPB_FAILURE,
  PARKING_QUIT_EPB_FAILURE,
  PARKING_QUIT_TEXT_SCU_FAILURE,
  PARKING_QUIT_SCU_FAILURE,
  PARKING_QUIT_TEXT_APA_FAILURE,
  PARKING_QUIT_APA_FAILURE,
  PARKING_QUIT_TEXT_EXTERNAL_ECU_FAILURE,
  PARKING_QUIT_EXTERNAL_ECU_FAILURE,
  PARKING_QUIT_TEXT_ABS_TCS_ESP_ACC_AEB_ACTIVE,
  PARKING_QUIT_ABS_TCS_ESP_ACC_AEB_ACTIVE,
  PARKING_QUIT_TEXT_ESC_FAILURE,
  PARKING_QUIT_ESC_FAILURE,
  PARKING_QUIT_TEXT_EPS_FAILURE,
  PARKING_QUIT_EPS_FAILURE,
  PARK_QUIT_TEXT_VEHICLE_BLOCK,
  PARK_QUIT_VEHICLE_BLOCK,
  PARK_QUIT_TEXT_INTERRUPT_NUMBER_OVER_THRESHOLD,
  PARK_QUIT_INTERRUPT_NUMBER_OVER_THRESHOLD,
  PARK_QUIT_TEXT_RADAR_DIRTY,
  PARK_QUIT_RADAR_DIRTY,
  PARK_QUIT_TEXT_EPB_ACTIVE,
  PARK_QUIT_EPB_ACTIVE,
  PARK_QUIT_TEXT_DRIVE_MODE_UNSUITABLE,
  PARK_QUIT_DRIVE_MODE_UNSUITABLE,
  PARK_QUIT_TEXT_TRAILER_HITCH_CONNECTED,
  PARK_QUIT_TRAILER_HITCH_CONNECTED,

  //park comfirming
  PARKING_COMFIRMING_TEXT_SEAT_BELT,
  PARKING_COMFIRMING_TEXT_PRESS_BRAKE_PEDAL,
  PARKING_COMFIRMING_PRESS_BRAKE_PEDAL,
  PARKING_COMFIRMING_TEXT_CLOSE_DOOR,
  PARKING_COMFIRMING_TEXT_EXPANDED_MIRROR,
  PARKING_COMFIRMING_EXPANDED_MIRROR,
  PARKING_COMFIRMING_TEXT_CLOSE_TRUNK,
  PARKING_COMFIRMING_TEXT_CLOSE_HOOD,
  PARKING_COMFIRMING_TEXT_STOP,
  PARKING_COMFIRMING_TEXT_FRONT_IS_CLEAR_PARKING_PATH,
  PARKING_COMFIRMING_FRONT_IS_CLEAR_PARKING_PATH,
  PARKING_COMFIRMING_TEXT_FRONT_IS_CLEAR_HANDOVER,
  PARKING_COMFIRMING_FRONT_IS_CLEAR,
  PARKING_COMFIRMING_TEXT_SMALL_PARK_SLOT,
  PARKING_COMFIRMING_SMALL_PARK_SLOT,
  PARKING_COMFIRMING_TEXT_HOLD_BRAKE_AND_START_PARKING,
  PARKING_CONFIRMING_TEXT_APA_QUIT_TAKE_OVER,
  PARKING_COMFIRMING_TEXT_KEEP_BRAKE_PEDAL,

  // Free Parking
  FREE_PARKING_TEXT_CHOOSE_PARKING_SPACE_TYPE,
  FREE_PARKING_TEXT_DESCRIPTION_TOP,
  FREE_PARKING_TEXT_DESCRIPTION_BOTTOM,
  FREE_PARKING_TEXT_STOP,
  FREE_PARKING_SELECTED_PARKING_INSTRUCTIONS_BUTTON,
  FREE_PARKING_CONFIRM_BUTTON,

  // RPA
  RPA_OTHERS_BLUETOOTH_MOBILE_PHONE,
  RPA_OTHERS_BLUETOOTH_DISCONNECT,
  RPA_TEXT_BLUETOOTH_CONNECTED_PROMPT,
  RPA_TEXT_BLUETOOTH_CONNECTED,
  RPA_TEXT_PLEASE_PARKING,
  RPA_TEXT_CLICK_ON_THE_PHONE_TO_START_PARKING,
  RPA_TEXT_OPEN_THE_APP_AND_CONNECT_TO_BLUETOOTH,
  RPA_TEXT_REMOTE_PARKING_IS_NOT_AVAILABLE,
  RPA_TEXT_REMOTE_PARKING,
  RPA_TEXT_DRIVER_REPONSE_TIMEOUT,
  RPA_TEXT_PLEASE_LEAVE_THE_CAR,
  RPA_TEXT_SUSPEND,
  RPA_TEXT_BLUETOOTH_DISCONNECT,
  RPA_TEXT_TERMINATE,
  RPA_TEXT_USE_PHONE,

  // button
  PARKING_BUTTON_FUNCTION_SELECTION_PARKIN,
  PARKING_BUTTON_FUNCTION_SELECTION_PARKOUT,
  PARKING_BUTTON_FUNCTION_SELECTION_FREE_PARKING,
  PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_APA,
  PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_RPA,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_UNSELECT,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_SELECTED,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_UNSELECT,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_SELECTED,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_UNSELECT_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_SELECTED_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_UNSELECT_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_SELECTED_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_UNSELECT,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_SELECTED,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_UNSELECT,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_SELECTED,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_UNSELECT_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_SELECTED_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_UNSELECT_TRANS,
  PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_SELECTED_TRANS,
  PARKING_BUTTON_QUIT,

  // Free Parking
  FREE_PARKING_CONFIRM_BUTTON_GREY,
  FREE_PARKING_SELECTED_PARKING_INSTRUCTIONS_BUTTON_GREY,
  FREE_PARKING_SELECTED_CROSS_PARKING_INSTRUCTIONS_BUTTON,
  FREE_PARKING_SELECTED_CROSS_PARKING_INSTRUCTIONS_BUTTON_GREY,
  FREE_PARKING_SELECTED_DIAGONAL_PARKING_INSTRUCTIONS_BUTTON,
  FREE_PARKING_SELECTED_DIAGONAL_PARKING_INSTRUCTIONS_BUTTON_GREY,
  FREE_PARKING_TEXT_DESCRIPTION_BOTTOM_TRANS,

  NUMBER_OF_ICON, //! always keep this at last entry
};

//!
//! CustomIconData
//!
class CustomIconData
{
public:

  CustomIconData(std::string f_string, osg::Vec2f f_iconPos, osg::Vec2f f_iconSize)
    : m_iconPath(f_string)
    , m_iconPos(f_iconPos)
    , m_iconSize(f_iconSize)
    , m_enable(false)
    , m_added(false)
    , m_iconPtr(nullptr)
  {
  }

  CustomIconData(std::string f_string, osg::Vec2f f_iconPos)
    : m_iconPath(f_string)
    , m_iconPos(f_iconPos)
    , m_iconSize(0.0f, 0.0f)
    , m_enable(false)
    , m_added(false)
    , m_iconPtr(nullptr)
  {
    static const osg::Vec2f denzaPlanview = {428.0f, 720.0f};
    static const osg::Vec2f idcPlanview = {588.0f, 990.0f};
    osg::ref_ptr <osg::Image> l_image = osgDB::readImageFile(m_iconPath);
    if (l_image)
    {
      m_iconSize = osg::Vec2f(
        static_cast<float>(l_image->s()) * (idcPlanview.x() / denzaPlanview.x()),
        static_cast<float>(l_image->t()) * (idcPlanview.y() / denzaPlanview.y()));
    }
    else
    {
      m_iconSize = osg::Vec2f(0.0f, 0.0f);
    }
  }

  void setEnabled(bool f_enable) { m_enable = f_enable; }
  const bool isEnabled() const { return m_enable; }
  void setAdded(bool f_added) { m_added = f_added; }
  const bool isAdded() const { return m_added; }
  const std::string getFilePath() const { return m_iconPath; }
  const osg::Vec2f getIconPos() const { return m_iconPos; }
  const osg::Vec2f getIconSize() const { return m_iconSize; }

  void setIconSize(osg::Vec2f f_size) { m_iconSize = f_size; }
  void setIconPtr(pc::assets::Icon* f_iconPtr) { m_iconPtr = f_iconPtr; }
  pc::assets::Icon* getIconPtr() { return m_iconPtr; }

private:
  std::string m_iconPath;
  osg::Vec2f   m_iconPos;
  osg::Vec2f   m_iconSize;
  bool        m_enable;
  bool        m_added;
  pc::assets::Icon* m_iconPtr; // use for remove icon
};

//!
//! CustomIconGroup
//!
class CustomIconGroup
{
public:
  typedef std::map<ParkingProgressType, CustomIconData* > CustomIconDataList;

  CustomIconGroup() {};
  void setAllEnabled(bool f_enable);
  void addIcon(ParkingProgressType f_index, const std::string& f_filepath, osg::Vec2f f_iconPos);
  void addIcon(ParkingProgressType f_index, const std::string& f_filepath, osg::Vec2f f_iconPos, osg::Vec2f f_iconSize);
  unsigned int getNumIcons() const;
  CustomIconDataList getIconList() const {return m_icons;}
  CustomIconData* getIcon(ParkingProgressType f_index);

private:
  CustomIconDataList m_icons;
};

//!
//! ParkingIconManager
//!
class ParkingViewManager
{
public:
  ParkingViewManager();
  virtual ~ParkingViewManager();

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);
  void update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework);
  void clearIcon();

  void displayGear(const core::CustomFramework* f_framework);
  void displayFunctionSelectionButton(const core::CustomFramework* f_framework);
  void displayParkinAPARPAButton(const core::CustomFramework* f_framework);
  bool isAvailableParkingSlot(const core::CustomFramework* f_framework);
  void displayFunctionButton(
    const core::CustomFramework* f_framework,
    cc::target::common::EPARKDriverIndSearchR2L f_curAPADriverReq_Search,
    cc::target::common::EParkngTypeSeld f_curParkngTypeSeld,
    cc::target::common::EPARKDriverIndExtR2L f_curparkDriverIndExt,
    cc::target::common::EAPAPARKMODE f_ParkAPARPAParkMode,
    cc::target::common::EPARKStatusR2L f_curparkStatus);

  void displayParkOutSideButton(const core::CustomFramework* f_framework);
  void displayParkOutSideTransButton(const core::CustomFramework* f_framework);
  void displayContinueButton(cc::target::common::EPARKDriverIndR2L f_curparkDriverInd);
  void displayParkPauseButton();
  void displayContentOfGuidanceSuspend(const core::CustomFramework* f_framework);
  void CleanButtonDispSts();
  bool DeliverButtonDispSts(const core::CustomFramework* f_framework);
  void setDoorsTrunkHoodOpenLogic(vfc::uint8_t  f_curDoorState[pc::daddy::NUMBER_OF_CARDOORS], bool f_checkTrunk);
  void updateIcons(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);

private:
  //! Copy constructor is not permitted.
  ParkingViewManager (const ParkingViewManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingViewManager& operator=(const ParkingViewManager& other); // = delete

  osg::Vec2f transferToBottomLeft(const osg::Vec2f f_iconPos);
  //unsigned int m_lastConfigUpdate;

  CustomIconGroup                     m_settingParkView; // store icon data
  cc::daddy::EParkModeDisp2Touch      m_ViewButtonParkModeDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkStartDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkContinueDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkPauseDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkQuitDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkOutDirectionDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonFreeParkingConfirmDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonFreeParkingSpaceTypeDispSts;
  unsigned int m_lastConfigUpdate;
  bool m_mat_b;
};

//!
//! ParkingSearching
//!
class ParkingView: public cc::assets::uielements::CustomImageOverlays
{
public:
  ParkingView(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~ParkingView();
  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  ParkingView (const ParkingView& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingView& operator=(const ParkingView& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ParkingViewManager m_HoriManager;
  // ParkingViewManager m_VertManager;
  cc::target::common::EThemeTypeHU m_theme;
};


class ParkingViewBackgroundManager
{
public:
  ParkingViewBackgroundManager();
  virtual ~ParkingViewBackgroundManager();

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);
  void update(const cc::assets::uielements::CustomImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework);
  void clearIcon();

private:
  //! Copy constructor is not permitted.
  ParkingViewBackgroundManager (const ParkingViewBackgroundManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingViewBackgroundManager& operator=(const ParkingViewBackgroundManager& other); // = delete

  osg::Vec2f transferToBottomLeft(const osg::Vec2f f_iconPos);

  //unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_settingBackgroundParkView;
};

class ParkingViewBackground: public cc::assets::uielements::CustomImageOverlays
{
public:
  ParkingViewBackground(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~ParkingViewBackground();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  ParkingViewBackground (const ParkingViewBackground& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingViewBackground& operator=(const ParkingViewBackground& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ParkingViewBackgroundManager m_HoriBackgroundManager;
  ParkingViewBackgroundManager m_VertBackgroundManager;
  cc::target::common::EThemeTypeHU m_theme;
};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_PARKINGVIEW_H

