//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#include "cc/assets/settingpageoverlay/inc/BrightnessSliderEntryButton.h"
#include "cc/assets/settingpageoverlay/inc/SettingPageOverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "vfc/core/vfc_types.hpp"
#include <osg/Group>
namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

pc::util::coding::Item<BrightnessSliderEntryButtonSettings>
    static g_brightnessSliderEntryButtonSettings("BrightnessSliderEntryButton");

BrightnessSliderEntryButton::BrightnessSliderEntryButton( // PRQA S 4207
    cc::core::AssetId                                       f_assetId,
    pc::core::Framework*                                    f_framework,
    cc::assets::settingpageoverlay::BrightnessSliderSwitch* f_brightnessSliderSwitch,
    osg::Camera*                                            f_referenceView = nullptr)
    : Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_brightnessSliderEnabledState{false}
    , m_brightnessSliderSwitch{f_brightnessSliderSwitch}
    , m_showStatus{false}
    , m_timer{}
{
    setState(AVAILABLE);
    setName("SettingPage Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(g_brightnessSliderEntryButtonSettings->m_horiPos);
}

void BrightnessSliderEntryButton::onInvalid()
{
    setIconEnable(false);
}

void BrightnessSliderEntryButton::onUnavailable()
{
    setIconEnable(false);
}

void BrightnessSliderEntryButton::onAvailable()
{
    setIconEnable(true);
    setTexturePath(g_brightnessSliderEntryButtonSettings->m_buttonTexture.m_AvailableTexturePath);
}

void BrightnessSliderEntryButton::onPressed()
{
    setIconEnable(true);
    setTexturePath(g_brightnessSliderEntryButtonSettings->m_buttonTexture.m_AvailableTexturePath);
    if (!m_brightnessSliderEnabledState && m_brightnessSliderSwitch->getBrightnessSliderStatus() == false)
    {
        m_brightnessSliderSwitch->openBrightnessSlider();
        // osg::Timer_t l_tick = osg::Timer::instance()->tick();
        // m_lastUpdate = l_tick;
    }
    else
    {
        m_brightnessSliderSwitch->closeBrightnessSlider();
    }
}

void BrightnessSliderEntryButton::onReleased()
{
    setIconEnable(true);
}

void BrightnessSliderEntryButton::update()
{
    const osg::Timer_t l_tick = osg::Timer::instance()->tick();
    setSettingModifiedCount(g_brightnessSliderEntryButtonSettings->getModifiedCount());
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData) 
    
    
    
    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)
    
    GET_PORT_DATA(showReqContainer, m_framework->asCustomFramework()->m_showReq_ReceiverPort, showReqPortHaveData)

    
    if (showReqPortHaveData)
    {
        const bool showReq = showReqContainer->m_Data;
        if (showReq == false && m_showStatus!= false)
        {
            m_brightnessSliderSwitch->closeBrightnessSlider();
        }
        m_showStatus = showReq;
    }
    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

#ifdef SUP_HSAE
    if ((getTouchStatus() == TOUCH_MOVE ||getTouchStatus() == TOUCH_DOWN)&&
#endif
#ifdef SUP_MEGA
    if (getTouchStatus() == PRESSED &&
#endif  
        !checkTouchInsideTargetClickArea(
            g_brightnessSliderOverlaySettings->m_brightnessSliderBackground.m_brightnessSliderBackgroundPos,
            g_brightnessSliderOverlaySettings->m_brightnessSliderBackgroundSize) &&
        !checkTouchInsideResponseArea() && m_brightnessSliderSwitch->getBrightnessSliderStatus())
    {
        m_brightnessSliderSwitch->closeBrightnessSlider();
    }

    // vfc::float64_t l_delta = osg::Timer::instance()->delta_m(m_lastUpdate, l_tick);
    // if (l_delta > g_brightnessSliderEntryButtonSettings->m_exitDelay)
    // {
    //     m_brightnessSliderSwitch->closeBrightnessSlider();
    // }

    // if (getTouchStatus() == PRESSED &&
    //     checkTouchInsideTargetClickArea(
    //         g_brightnessSliderOverlaySettings->m_brightnessSliderBackground.m_brightnessSliderBackgroundPos,
    //         g_brightnessSliderOverlaySettings->m_brightnessSliderBackgroundSize) )
    // {
    //     m_lastUpdate = l_tick;
    // }

    if (g_brightnessSliderEntryButtonSettings->getModifiedCount() != getSettingModifiedCount())
    {
        setSettingModifiedCount(g_brightnessSliderEntryButtonSettings->getModifiedCount());
    }
    const bool        touchInsideResponseArea = checkTouchInsideResponseArea();
//    const TouchStatus touchSts                = touchStatus(); // PRQA S 3803

    ButtonState currentState = getState();

    switch (currentState)
    {
    case AVAILABLE:
    {
        if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
        {
            currentState = PRESSED;
        }
        break;
    }
    case PRESSED:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = RELEASED;
        }
        break;
    }
    case RELEASED:
    {
        currentState = AVAILABLE;
        break;
    }
    case INVALID:
    case UNAVAILABLE:
    default:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = AVAILABLE;
        }
        break;
    }
    }

    setState(currentState);

    switch (getState())
    {
    case INVALID:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    case UNAVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
        break;
    }
    case AVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
        break;
    }
    case PRESSED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
        break;
    }
    case RELEASED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "RELEASED");
        break;
    }
    default: // PRQA S 4012
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    }
}

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc // PRQA S 1041
