//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityObject.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/VirtualRealityObject.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"

#include <osgDB/ReadFile>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace virtualreality
{

pc::util::coding::Item<VirtualRealityObjectSettings> g_virtualRealityObjectSetting("VirtualRealityObject");

VirtualRealityObject::VirtualRealityObject()
  : m_dirty{false}
  , m_visible{false}
{
}

VirtualRealityObject::VirtualRealityObject(const VirtualRealityObject& f_other, const osg::CopyOp& f_copyOp)
  : osg::MatrixTransform{f_other, f_copyOp}
  , m_position{f_other.m_position}
  , m_dirty{f_other.m_dirty}
  , m_visible{f_other.m_visible}
{
}

VirtualRealityObject::~VirtualRealityObject() = default;



void VirtualRealityObject::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (m_dirty)
    {
      updateObjectNode();
      // rotate, then translate
      setMatrix(osg::Matrix::rotate(m_position.m_phi, osg::Z_AXIS)
                * osg::Matrix::scale(g_virtualRealityObjectSetting->m_vehicleModelScaleFactor)
                * osg::Matrix::translate(osg::Vec3(m_position.m_x, m_position.m_y, m_position.m_z)));
      m_dirty = false;
    }
    osg::MatrixTransform::traverse(f_nv);
  }
  else if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    if (m_visible)
    {
      osg::MatrixTransform::traverse(f_nv);
    }
  }
  else
  {
    osg::MatrixTransform::traverse(f_nv);
  }
}

} // namespace virtualreality
} // namespace assets
} // namespace cc
