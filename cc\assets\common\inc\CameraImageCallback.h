//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS SAAP
//  Target systems: SAAP
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (XC-AS/EPF2-CN)
//  Department: XC-AS/EPF
//=============================================================================
/// @swcomponent SVS SAAP
/// @file  CameraImage.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_COMMON_CAMERAIMAGECALLBACK_H
#define CC_ASSETS_COMMON_CAMERAIMAGECALLBACK_H

#include "cc/daddy/inc/CustomDaddyPorts.h"

namespace cc
{
namespace assets
{
namespace common
{

//!
//! CameraBrightCallback
//!
class CameraBrightCallback : public osg::Uniform::Callback
{
public:
CameraBrightCallback()
{
    cc::daddy::CustomDaddyPorts::sm_HU_NightModeDaddy_SenderPort.connect( m_indexReceiver );
}
~CameraBrightCallback()
{
    cc::daddy::CustomDaddyPorts::sm_HU_NightModeDaddy_SenderPort.disconnect( m_indexReceiver );
}
void operator () (osg::Uniform* f_uniform, osg::NodeVisitor* f_nv)
{
    m_indexReceiver.update();
    update(f_uniform, f_nv);
    m_indexReceiver.cleanup();
}
void update(osg::Uniform* f_uniform, osg::NodeVisitor* /* f_nv */); // PRQA S 6043

private:

CameraBrightCallback (const CameraBrightCallback& other) = delete;
CameraBrightCallback& operator=(const CameraBrightCallback& other) = delete;

::daddy::TLatestReceiverPort <cc::daddy::HUNightModeStsDaddy_t> m_indexReceiver;
};


} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_CAMERAIMAGECALLBACK_H