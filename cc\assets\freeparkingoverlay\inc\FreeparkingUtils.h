//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_FREEPARKINGUTILS_H
#define CC_ASSETS_FREEPARKINGOVERLAY_FREEPARKINGUTILS_H

#include <array>
#include <osg/observer_ptr>

namespace pc
{
namespace core
{
class Framework;
class Viewport;
} // namespace core
} // namespace pc

namespace cc
{

namespace assets
{

namespace freeparkingoverlay
{

bool isSlotHeadingUp(vfc::float32_t f_angle);
bool isSlotHeadingDown(vfc::float32_t f_angle);
bool isSlotHeadingLeft(vfc::float32_t f_angle);
bool isSlotHeadingRight(vfc::float32_t f_angle);

bool isInsideViewport(osg::Vec2f f_touchPosition, const pc::core::Viewport* f_viewport);

bool isInTranslationArea(const osg::Vec3f& f_Center_vertex_screen, APSlitherStartEndPos& f_SlitherPos);
bool isInTranslationArea(
    const osg::Vec3f&           f_FrontLeft_vertex_screen,
    const osg::Vec3f&           f_FrontRight_vertex_screen,
    const osg::Vec3f&           f_RearLeft_vertex_screen,
    const osg::Vec3f&           f_RearRight_vertex_screen,
    const APSlitherStartEndPos& f_SlitherPos);
bool isInTranslationArea(
    const osg::Vec3f& f_FrontLeft_vertex_screen,
    const osg::Vec3f& f_FrontRight_vertex_screen,
    const osg::Vec3f& f_RearLeft_vertex_screen,
    const osg::Vec3f& f_RearRight_vertex_screen,
    const osg::Vec2f& f_Pos0,
    const osg::Vec2f& f_Pos1);
bool isInRotationArea(
    const osg::Vec3f&     f_FrontLeft_vertex_screen,
    const osg::Vec3f&     f_FrontRight_vertex_sreen,
    const osg::Vec3f&     f_RearLeft_vertex_screen,
    const osg::Vec3f&     f_RearRight_vertex_screen,
    APSlitherStartEndPos& f_SlitherPos);
} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_FREEPARKINGUTILS_H
