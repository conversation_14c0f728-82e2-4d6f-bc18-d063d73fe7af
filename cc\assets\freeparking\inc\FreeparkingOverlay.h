//-------------------------------------------------------------------------------
// Copyright (c) 2025 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_FREE_PARKING_OVERLAY_HPP
#define CC_ASSETS_FREE_PARKING_OVERLAY_HPP

#include "cc/core/inc/CustomFramework.h"
#include "cc/util/touchmanipulator/inc/TouchEventHandler.h"
#include "pc/generic/util/coding/inc/ISerializable.h"

#include <osg/Camera>
#include <osg/MatrixTransform>
#include <osg/StateSet>
#include <osg/Texture>
#include <osg/Vec3>
#include <osg/Vec4>
#include <osg/ref_ptr>

#include <string>

namespace cc
{
namespace assets
{
namespace freeparking
{

class FreeParkingOverlaySettings : public pc::util::coding::ISerializable
{
public:
    FreeParkingOverlaySettings()
        : m_startingPos{osg::Vec3f(0.0, -5.0, 0.0)}
        , m_availableColor{osg::Vec4f(0.19, 0.47, 0.96, 1.0)}
        , m_movingColor{osg::Vec4f(0.84, 0.84, 0.84, 1.0)}
        , m_unavailableColor{osg::Vec4f(0.89, 0.31, 0.31, 1.0)}
        , m_contourTexturePath{"resources/common/freeparking/anywhere_parking_rect_unsuitable.png"}
        , m_rotateTexturePath{"resources/common/freeparking/free_park_rotate.png"}
        , m_flipTexturePath{"resources/common/freeparking/free_park_flip.png"}
        , m_vehicleContourTexturePath{"resources/common/freeparking/free_parking_contour.png"}
    {
    }

    SERIALIZABLE(FreeParkingOverlaySettings)
    {
        ADD_MEMBER(osg::Vec3f, startingPos);
        ADD_MEMBER(osg::Vec4f, availableColor);
        ADD_MEMBER(osg::Vec4f, movingColor);
        ADD_MEMBER(osg::Vec4f, unavailableColor);
        ADD_STRING_MEMBER(contourTexturePath);
        ADD_STRING_MEMBER(rotateTexturePath);
        ADD_STRING_MEMBER(flipTexturePath);
        ADD_STRING_MEMBER(vehicleContourTexturePath);
        ADD_FLOAT_MEMBER(topPercentage);
        ADD_FLOAT_MEMBER(bottomPercentage);
    }

    osg::Vec3f  m_startingPos;
    osg::Vec4f  m_availableColor;
    osg::Vec4f  m_movingColor;
    osg::Vec4f  m_unavailableColor;
    std::string m_contourTexturePath;
    std::string m_rotateTexturePath;
    std::string m_flipTexturePath;
    std::string m_vehicleContourTexturePath;
    vfc::float32_t m_topPercentage{0.15f};
    vfc::float32_t m_bottomPercentage{0.15f};
};

extern pc::util::coding::Item<FreeParkingOverlaySettings> g_freeParkingOverlaySettings;

class FreeParkingOverlay : public osg::MatrixTransform
{
public:
    FreeParkingOverlay(osg::Camera* f_view, pc::core::Framework* f_framework);

    void changeColor(const osg::Vec4f& f_color);
    void updatePosition(const osg::Vec3f& f_position);
    void updateRotation(float f_rotation);

    bool handleClick(const cc::util::touchmanipulator::TouchEventHandler::TouchPoint& f_touch);

    osg::Vec3f getPosition() const;
    float      getRotation() const;
    void       updateSlotStatus();
    bool       isBecomeVisible();
    void       updateMatrix();
    void       resetState();

    void traverse(osg::NodeVisitor& f_nv) override;

private:
    enum TouchAction
    {
        NONE,
        ROTATE,
        MOVE
    };

#ifdef TARGET_STANDALONE
private:
    void debug();
#endif

private:
    void       createWarningGeometry();
    void       touchEnded();
    osg::Vec3f clickToRealWorldPosition(const osg::Vec2f& f_clickPosition) const;
    bool       isRotationClicked(const osg::Vec3f& f_position) const;
    bool       isFlipClicked(const osg::Vec3f& f_position) const;

    class WarningGeodes : public osg::Group
    {
    public:
        enum WarningArea : vfc::uint32_t
        {
            TopLeft = 0,
            MiddleLeft,
            BottomLeft,
            TopRight,
            MiddleRight,
            BottomRight,
            NumArea
        };

        WarningGeodes()
            : m_geodes{}
        {
            init();
        }

        void setEnable(WarningArea f_area, bool f_enable)
        {
            getGeode(f_area)->setNodeMask(f_enable ? ~0u : 0u);
        }

        void setAllEnable(bool f_enable)
        {
            for (auto geode : m_geodes)
            {
                geode->setNodeMask(f_enable ? ~0u : 0u);
            }
        }

    private:
        void init();
        osg::Geode* getGeode(WarningArea f_warnArea)
        {
            return m_geodes.at(static_cast<std::size_t>(f_warnArea));
        }

        std::array<osg::ref_ptr<osg::Geode>, WarningArea::NumArea> m_geodes;
    };


    osg::ref_ptr<osg::Geometry>        m_quadGeometry;
    osg::ref_ptr<WarningGeodes>        m_warningGeodes;
    osg::ref_ptr<osg::MatrixTransform> m_quadMatrixTransform;
    osg::ref_ptr<osg::MatrixTransform> m_rotateMatrixTransform;
    osg::ref_ptr<osg::MatrixTransform> m_flipMatrixTransform;

    osg::Camera* m_view;

    bool                                               m_clicked;
    osg::Vec3f                                         m_position;
    osg::Vec3f                                         m_grabbedPosition;
    osg::Vec3f                                         m_scale;
    float                                              m_startRotation;
    float                                              m_rotation;
    TouchAction                                        m_touchAction;
    vfc::uint32_t                                      m_lastFrameCount{~0u};
    std::chrono::time_point<std::chrono::system_clock> m_lastEventTime;

    // rbp::vis_dongfeng::CApaHmi         m_apaHmi;
    // rbp::vis_dongfeng::AWPObstacleArea m_obstacleArea;

    pc::core::Framework* m_framework;
};

class FreeParkingOverlayTouchEventHandler : public cc::util::touchmanipulator::TouchEventHandler
{
public:
    FreeParkingOverlayTouchEventHandler();

    bool handle(
        const osgGA::GUIEventAdapter& f_ea,
        osgGA::GUIActionAdapter&      f_aa,
        osg::Object*                  f_obj,
        osg::NodeVisitor*             f_nv) override;

private:
    bool handleEvents(const osgGA::GUIEventAdapter& f_ea, FreeParkingOverlay* f_freeParkingOverlay);
};

} // namespace freeparking
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FISHEYE_TRANSITION_FISHEYE_TRANSITION_OVERLAY_HPP