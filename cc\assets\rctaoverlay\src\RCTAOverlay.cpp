//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  RCTAOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/rctaoverlay/inc/RCTAOverlay.h"
#include "vfc/core/vfc_types.hpp"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace rctaoverlay
{

//!
//! RctaOverlayCodingParams
//!
class RctaOverlayCodingParams : public pc::util::coding::ISerializable
{
public:

  RctaOverlayCodingParams()
    : m_leftWarningTexture("cc/vehicle_model/ui/40_left_warning.png") // PRQA S 4052
    , m_rightWarningTexture("cc/vehicle_model/ui/39_right_warning.png")
  {
  }

  SERIALIZABLE(RctaOverlayCodingParams) // PRQA S 3401
  {
    if (f_descriptor == nullptr)
    {
        return;
    }
    ADD_STRING_MEMBER(leftWarningTexture);
    ADD_STRING_MEMBER(rightWarningTexture);
  }

  std::string  m_leftWarningTexture;
  std::string  m_rightWarningTexture;

};

pc::util::coding::Item<RctaOverlayCodingParams> g_settings("RctaOverlay");


osg::Texture2D* loadTexture(const std::string& f_filename)
{
  osg::Image* const l_image = osgDB::readImageFile(f_filename);
  if (l_image == nullptr)
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "RctaOverlay::loadTexture(): Could not load " << f_filename);
  }
  osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
  l_texture->setDataVariance(osg::Object::STATIC);
  l_texture->setUnRefImageDataAfterApply(true);
  // l_texture->setFilter(osg::Texture::MIN_FILTER,RctaOverlayCodingParams::getFilterMode(f_minFilterMode));
  l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
  l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::REPEAT);
  return l_texture;
}



//! utility function for iterating over the vertex array of a given geometry and generate color values depending on the vertex position
template <class T>
void generateColors(osg::Geometry* f_geometry, const T& f_operator)
{
  const osg::Vec3Array* l_vertices = dynamic_cast<const osg::Vec3Array*> (f_geometry->getVertexArray());
  if (l_vertices)
  {
    const unsigned int l_numVertices = l_vertices->size();
    osg::Vec4ubArray* l_colors = new osg::Vec4ubArray(l_numVertices);
    l_colors->setNormalize(true);
    for (unsigned int i = 0; i < l_numVertices; ++i)
    {
      f_operator.compute((*l_vertices)[i], (*l_colors)[i]);
    }
    l_colors->dirty();
    f_geometry->setColorArray(l_colors, osg::Array::BIND_PER_VERTEX);
  }
}


//!
//! RctaOverlay
//!
RctaOverlay::RctaOverlay(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_FCTALeftGeode{}
  , m_FCTARightGeode{}
  , m_RCTALeftGeode{}
  , m_RCTARightGeode{}
{
  setName("RctaOverlay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


RctaOverlay::~RctaOverlay() = default;


void RctaOverlay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (g_settings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      addUpdateCallback(new RctaOverlayUpdateCallback(m_FCTALeftGeode, m_FCTARightGeode, m_RCTALeftGeode, m_RCTARightGeode ,m_framework));
      m_settingsModifiedCount = g_settings->getModifiedCount();
    }

    //! Update background geode uniform
    {
      // osg::StateSet* l_stateSet = m_backgroundGeode->getOrCreateStateSet();
      // osg::StateSet* l_stateSetMirrored = m_backgroundGeodeMirrored->getOrCreateStateSet();
      // osg::Vec2f l_uvScale = osg::componentDivide(osg::Vec2f(1.0f, 1.0f), g_settings->m_gridTileSize);
      // osg::Vec2f l_uvOffset = osg::componentMultiply(g_settings->m_gridTileOffset, l_uvScale);

      // if (false == m_animXDirection)
      // {
      //   l_uvOffset.y() += std::fmod(m_drivenDistance * l_uvScale.y(), 1.0f);
      // }
      // else
      // {
      //   l_uvOffset.x() -= std::fmod(m_drivenDistance * l_uvScale.x(), 1.0f);
      // }
      // osg::Uniform* l_uvOffsetUniform = l_stateSet->getOrCreateUniform("u_uvOffset", osg::Uniform::FLOAT_VEC2);
      // l_uvOffsetUniform->set(l_uvOffset);
      // osg::Uniform* l_uvOffsetUniformMirrored = l_stateSetMirrored->getOrCreateUniform("u_uvOffset", osg::Uniform::FLOAT_VEC2);
      // l_uvOffsetUniformMirrored->set(l_uvOffset);
    }
  }
  osg::Group::traverse(f_nv);
}


void RctaOverlay::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803
  constexpr vfc::float32_t l_offset_x = 1.5f;
  constexpr vfc::float32_t l_offset_y = 0.8f;


  //! FCTA Left overlay ************************************************************************************************************
  m_FCTALeftGeode = new osg::Geode;
  addChild(m_FCTALeftGeode);    // PRQA S 3803
  {
    const osg::Vec3f l_frontleft = osg::Vec3f(pc::vehicle::g_mechanicalData->m_wheelbase + pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront+l_offset_x, pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f+l_offset_y, 0.0f);
    // osg::Vec3f l_endPoint  = osg::Vec3f(g_settings->m_roadEnd,   -g_settings->m_gridYOffset - g_settings->m_gridWidth - l_gridOffsetRight, g_settings->m_gridHeight);
    const osg::Vec3f l_width = osg::Vec3f(0.0f, 0.6f, 0.0f);
    const osg::Vec3f l_height = osg::Vec3f(0.6f, 0.0f, 0.0f);

    osg::Geometry* const l_FCTALeft = pc::util::osgx::createTexturePlane(l_frontleft, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
    // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
    osg::StateSet* const l_FCTALeftStateSet = l_FCTALeft->getOrCreateStateSet();
    osg::Texture2D* const l_FCTALeftTexture = loadTexture(g_settings->m_leftWarningTexture);
    l_FCTALeftStateSet->setTextureAttribute(0u, l_FCTALeftTexture);
    m_FCTALeftGeode->addDrawable(l_FCTALeft);    // PRQA S 3803
  }


  //! FCTA right overlay ************************************************************************************************************
  m_FCTARightGeode = new osg::Geode;
  addChild(m_FCTARightGeode);    // PRQA S 3803
  {
    // osg::Vec3f l_endPoint = osg::Vec2f(g_settings->m_roadEnd, -g_settings->m_gridYOffset - g_settings->m_gridWidth- l_gridOffsetRight, g_settings->m_gridHeight);
    const osg::Vec3f l_width = osg::Vec3f(0.0f, 0.6f, 0.0f);
    const osg::Vec3f l_height = osg::Vec3f(0.6f, 0.0f, 0.0f);

    const osg::Vec3f l_frontright = osg::Vec3f(pc::vehicle::g_mechanicalData->m_wheelbase + pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront+l_offset_x, -pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f-l_offset_y,0.0f)-l_width;

    osg::Geometry* const l_FCTARight = pc::util::osgx::createTexturePlane(l_frontright, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
    // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
    osg::StateSet* const l_FCTARightStateSet = l_FCTARight->getOrCreateStateSet();
    osg::Texture2D* const l_FCTARightTexture = loadTexture(g_settings->m_rightWarningTexture);
    l_FCTARightStateSet->setTextureAttribute(0u, l_FCTARightTexture);
    m_FCTARightGeode->addDrawable(l_FCTARight);    // PRQA S 3803
  }


  //! RCTA left overlay ************************************************************************************************************
  m_RCTALeftGeode = new osg::Geode;
  addChild(m_RCTALeftGeode);    // PRQA S 3803
  {
    const osg::Vec3f l_rearleft = osg::Vec3f(-pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear-l_offset_x, pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f+l_offset_y, 0.0f);
    // osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, g_settings->m_gridYOffset + g_settings->m_gridWidth + l_gridOffsetRight, g_settings->m_gridHeight);
    const osg::Vec3f l_width = osg::Vec3f(0.0f, 0.6f, 0.0f);
    const osg::Vec3f l_height = osg::Vec3f(0.6f, 0.0f, 0.0f);

    osg::Geometry* const l_RCTALeft = pc::util::osgx::createTexturePlane(l_rearleft, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
    // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
    osg::StateSet* const l_RCTALeftStateSet = l_RCTALeft->getOrCreateStateSet();
    osg::Texture2D* const l_RCTALeftTexture = loadTexture(g_settings->m_leftWarningTexture);
    l_RCTALeftStateSet->setTextureAttribute(0u, l_RCTALeftTexture);
    m_RCTALeftGeode->addDrawable(l_RCTALeft);    // PRQA S 3803
  }

  //! RCTA right overlay ************************************************************************************************************
  m_RCTARightGeode = new osg::Geode;
  addChild(m_RCTARightGeode);    // PRQA S 3803
  {
    // osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, g_settings->m_gridYOffset + g_settings->m_gridWidth + l_gridOffsetRight, g_settings->m_gridHeight);
    const osg::Vec3f l_width = osg::Vec3f(0.0f, 0.6f, 0.0f);
    const osg::Vec3f l_height = osg::Vec3f(0.6f, 0.0f, 0.0f);

    const osg::Vec3f l_rearright = osg::Vec3f(-pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear-l_offset_x, -pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f-l_offset_y, 0.0f)-l_width;

    osg::Geometry* const l_RCTARight = pc::util::osgx::createTexturePlane(l_rearright, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
    // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
    osg::StateSet* const l_RCTARightStateSet = l_RCTARight->getOrCreateStateSet();
    osg::Texture2D* const l_RCTARightTexture = loadTexture(g_settings->m_rightWarningTexture);
    l_RCTARightStateSet->setTextureAttribute(0u, l_RCTARightTexture);
    m_RCTARightGeode->addDrawable(l_RCTARight);    // PRQA S 3803
  }

  m_FCTALeftGeode->setNodeMask(0u);
  m_FCTARightGeode->setNodeMask(0u);
  m_RCTALeftGeode->setNodeMask(0u);
  m_RCTARightGeode->setNodeMask(0u);

  // addUpdateCallback(new RctaOverlayUpdateCallback(m_FCTALeftGeode, m_FCTARightGeode, m_RCTALeftGeode, m_RCTARightGeode ,m_framework));

  osg::StateSet* const l_groupStateSet = getOrCreateStateSet();
  osg::Depth* const l_depth = new osg::Depth;
  l_depth->setWriteMask(false);
  l_groupStateSet->setAttribute(l_depth);
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  l_basicTexShader.apply(l_groupStateSet);    // PRQA S 3803
  l_groupStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_groupStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_baseplate + 1, "RenderBin");
  //l_groupStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);


}

RctaOverlayUpdateCallback::RctaOverlayUpdateCallback(
                                                          osg::ref_ptr<osg::Geode> f_FCTALeftGeode,
                                                          osg::ref_ptr<osg::Geode> f_FCTARightGeode,
                                                          osg::ref_ptr<osg::Geode> f_RCTALeftGeode,
                                                          osg::ref_ptr<osg::Geode> f_RCTARightGeode,
                                                          pc::core::Framework* f_pFramework
                                                          )
  : m_FCTALeftGeode{f_FCTALeftGeode}
  , m_FCTARightGeode{f_FCTARightGeode}
  , m_RCTALeftGeode{f_RCTALeftGeode}
  , m_RCTARightGeode{f_RCTARightGeode}
  , m_pFramework{f_pFramework}
  , m_lastTime_2Hz{0.0f}
  , m_lastTime_4Hz{0.0f}
{

}

void RctaOverlayUpdateCallback::updatein2Hz(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{
  const vfc::float32_t l_currentTime = static_cast<vfc::float32_t> (f_nv.getFrameStamp()->getReferenceTime());

  if (m_lastTime_2Hz < 0.0f)
  {
    m_lastTime_2Hz = l_currentTime;
  }

  const vfc::float32_t m_elapsedTime = l_currentTime - m_lastTime_2Hz;

  if (m_elapsedTime < 0.5f)
  {
    f_Geode->setNodeMask(~0u);
  }
  else if (m_elapsedTime < 1.0f)
  {
    f_Geode->setNodeMask(0u);
  }
  else
  {
      m_lastTime_2Hz = l_currentTime;
  }

}

void RctaOverlayUpdateCallback::updatein4Hz(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{
  const vfc::float32_t l_currentTime = static_cast<vfc::float32_t> (f_nv.getFrameStamp()->getReferenceTime());

  if (m_lastTime_4Hz < 0.0f)
  {
    m_lastTime_4Hz = l_currentTime;
  }

  const vfc::float32_t m_elapsedTime = l_currentTime - m_lastTime_4Hz;

  if (m_elapsedTime < 0.25f)
  {
    f_Geode->setNodeMask(~0u);
  }
  else if (m_elapsedTime < 0.5f)
  {
    f_Geode->setNodeMask(0u);
  }
  else
  {
      m_lastTime_4Hz = l_currentTime;
  }
}

RctaOverlayUpdateCallback::~RctaOverlayUpdateCallback() = default;

void RctaOverlayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if ((f_node == nullptr) || (f_nv == nullptr))
  {
      return;
  }
  if (m_pFramework->asCustomFramework()->m_FCTARight_ReceiverPort.hasData())
  {
    const cc::daddy::FCTARightDaddy_t*  const l_pFCTARight = m_pFramework->asCustomFramework()->m_FCTARight_ReceiverPort.getData();

    if (1U == static_cast<vfc::uint8_t>(l_pFCTARight->m_Data))
    {
        updatein2Hz(*f_nv, m_FCTARightGeode);
    }
    else if (2U == static_cast<vfc::uint8_t>(l_pFCTARight->m_Data))
    {
      updatein4Hz(*f_nv, m_FCTARightGeode);
    }
    else
    {
      m_FCTARightGeode->setNodeMask(0u);
    }
  }

  if (m_pFramework->asCustomFramework()->m_FCTALeft_ReceiverPort.hasData())
  {
    const cc::daddy::FCTALeftDaddy_t*  const l_pFCTALeft = m_pFramework->asCustomFramework()->m_FCTALeft_ReceiverPort.getData();

    if (1U == static_cast<vfc::uint8_t>(l_pFCTALeft->m_Data))
    {
      updatein2Hz(*f_nv, m_FCTALeftGeode);
    }
    else if (2U == static_cast<vfc::uint8_t>(l_pFCTALeft->m_Data))
    {
      updatein4Hz(*f_nv, m_FCTALeftGeode);
    }
    else
    {
      m_FCTALeftGeode->setNodeMask(0u);
    }
  }

  if (m_pFramework->asCustomFramework()->m_RCTARight_ReceiverPort.hasData())
  {
    const cc::daddy::RCTARightDaddy_t*  const l_pRCTARight = m_pFramework->asCustomFramework()->m_RCTARight_ReceiverPort.getData();

    if (1U == static_cast<vfc::uint8_t>(l_pRCTARight->m_Data))
    {
      updatein2Hz(*f_nv, m_RCTARightGeode);
    }
    else if (2U == static_cast<vfc::uint8_t>(l_pRCTARight->m_Data))
    {
      updatein4Hz(*f_nv, m_RCTARightGeode);
    }
    else
    {
      m_RCTARightGeode->setNodeMask(0u);
    }
  }

  if (m_pFramework->asCustomFramework()->m_RCTALeft_ReceiverPort.hasData())
  {
    const cc::daddy::RCTALeftDaddy_t*  const l_pRCTALeft = m_pFramework->asCustomFramework()->m_RCTALeft_ReceiverPort.getData();

    if (1U == static_cast<vfc::uint8_t>(l_pRCTALeft->m_Data))
    {
      updatein2Hz(*f_nv, m_RCTALeftGeode);
    }
    else if (2U == static_cast<vfc::uint8_t>(l_pRCTALeft->m_Data))
    {
      updatein4Hz(*f_nv, m_RCTALeftGeode);
    }
    else
    {
      m_RCTALeftGeode->setNodeMask(0u);
    }
  }

  traverse(f_node, f_nv);
}



} // namespace rctaoverlay
} // namespace assets
} // namespace cc
