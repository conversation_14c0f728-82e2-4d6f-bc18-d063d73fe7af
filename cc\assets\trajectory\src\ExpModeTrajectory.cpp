//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/ExpModeTrajectory.h"

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/assets/trajectory/inc/Helper.h"
#include "cc/util/beziercurve/inc/BezierCurve.h" // PRQA S 1060
#include "cc/daddy/inc/CustomDaddyTypes.h"

#include "osg/Texture2D"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

//!
//! ExpModeTrajectorySettings
//!
class ExpModeTrajectorySettings : public pc::util::coding::ISerializable
{
public:

  ExpModeTrajectorySettings()
    : m_height{0.002f}
    , m_fadingLength{50.0f} // number of splinePoints used for fading
    , m_normalColorMul{1.0f}
    , m_segmentsPerMeter{10u}
  {
  }

  SERIALIZABLE(ExpModeTrajectorySettings) // PRQA S 3401
  {
    if (f_descriptor == nullptr)
    {
        return;
    }
    ADD_FLOAT_MEMBER(height);
    ADD_FLOAT_MEMBER(fadingLength);
    ADD_FLOAT_MEMBER(normalColorMul);
    ADD_UINT32_MEMBER(segmentsPerMeter);
  }

  vfc::float32_t          m_height;
  vfc::float32_t          m_fadingLength;
  vfc::float32_t          m_normalColorMul;

  vfc::uint32_t   m_segmentsPerMeter;
};

pc::util::coding::Item<ExpModeTrajectorySettings> g_trajectorySettings("ExpModeTrajectory");


//!
//! ExpModeTrajectoryCullCallback
//!
class ExpModeTrajectoryCullCallback : public osg::Drawable::CullCallback // PRQA S 2113
{
public:

  explicit ExpModeTrajectoryCullCallback(ExpModeTrajectory* f_trajectory)
    : m_trajectory{f_trajectory}
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override    // PRQA S 2120
  {
    if (m_trajectory->isCulled())
    {
      return true;
    }
    if (0.0f < m_trajectory->getAlpha())
    {
      return false;
    }
    return true;
  }

private:

  ExpModeTrajectory* m_trajectory;

};


//!
//! ExpModeTrajectory
//!
ExpModeTrajectory::ExpModeTrajectory(
  vfc::uint32_t f_renderBinOrder,
  bool f_depthTest,
  bool f_depthBufferWrite,
  const cc::assets::trajectory::TrajectoryParams_st & f_trajParams)
  : m_cull{false}
  , m_trajParams{f_trajParams}
  , m_splinePointCount{0u}
  , m_splineMaxPointCount{g_trajectorySettings->m_segmentsPerMeter * 20u}
  , m_alphaUniform{new osg::Uniform("u_alpha", 0.3f)}
  , m_splineVertexArray{}
  , m_vertexData{}
  , m_geometry{}
{
  setName("ExpModeTrajectory");
  osg::Depth* const l_depthStateAttrib = new osg::Depth(osg::Depth::LESS);
  l_depthStateAttrib->setWriteMask(f_depthBufferWrite);
  const osg::StateAttribute::GLModeValue l_depthTest = f_depthTest ? osg::StateAttribute::ON : osg::StateAttribute::OFF;

  m_vertexData.Vertices  = new osg::Vec3Array;
  m_vertexData.Normals   = new osg::Vec3Array(1u);
  m_vertexData.Colors    = new osg::Vec4Array;
  m_vertexData.TexCoords = new osg::Vec2Array;
  m_vertexData.Indices   = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES));
  m_splineVertexArray    = new osg::Vec2Array;

  resizeArrays(m_splineMaxPointCount);

  m_geometry = new osg::Geometry;
  m_geometry->setCullCallback(new ExpModeTrajectoryCullCallback(this));
  m_geometry->setUseDisplayList(false);
  m_geometry->setVertexArray(m_vertexData.Vertices);
  m_geometry->setNormalArray(m_vertexData.Normals, osg::Array::BIND_OVERALL);
  m_geometry->setColorArray(m_vertexData.Colors, osg::Array::BIND_PER_VERTEX);
  m_geometry->setTexCoordArray(0u, m_vertexData.TexCoords);
  m_geometry->addPrimitiveSet(m_vertexData.Indices);  // PRQA S 3804  // PRQA S 3803
  osg::Geode::addDrawable(m_geometry);  // PRQA S 3803

  osg::StateSet* const l_stateSet = m_geometry->getOrCreateStateSet();
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
  l_stateSet->setMode(GL_BLEND,  osg::StateAttribute::ON); //PRQA S 3143
  l_stateSet->setMode(GL_DEPTH_TEST, l_depthTest); //PRQA S 3143
  l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_stateSet->setRenderBinDetails(static_cast<vfc::int32_t>(f_renderBinOrder), "RenderBin");
  l_stateSet->setAttributeAndModes(l_depthStateAttrib);
  l_stateSet->addUniform(m_alphaUniform);

  loadTexture();
  constructTrajectories();
}


ExpModeTrajectory::~ExpModeTrajectory() = default;


void ExpModeTrajectory::resizeArrays(vfc::uint32_t f_newSplinePointCount)
{
  m_splineMaxPointCount = f_newSplinePointCount;
  m_vertexData.Vertices ->resize(m_splineMaxPointCount * 4u);
  m_vertexData.Colors   ->resize(m_splineMaxPointCount * 4u);
  m_vertexData.TexCoords->resize(m_splineMaxPointCount * 4u);
  m_vertexData.Indices  ->resize((m_splineMaxPointCount - 1u) * 2u * 3u * 2u);
  m_splineVertexArray   ->resize(m_splineMaxPointCount);
}


osg::Image* ExpModeTrajectory::create1DTexture()  // PRQA S 6043
{
  constexpr vfc::uint32_t  lc_imageWidth  = 256u; // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;   // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;   // Image depth in pixels, in case of a 3D image.
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1);

  // This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
  constexpr vfc::float32_t lc_extraWidthForBlurMul = 1.2f; // (1 <= )


  const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_halfWholeWidth = m_trajParams.ParkingTraj_Width_Whole * 0.5f; // Half of the whole width of the wheel track
  std::array<vfc::float32_t, 5> l_absDistancesFromCenter; // 0..4: From outermost to innermost
  std::array<vfc::float32_t, 10> l_normalizedPositions;   // 0..9: From left to right

  l_absDistancesFromCenter[0u] = lc_halfWholeWidth + lc_halfGradientWidth + m_trajParams.ParkingTraj_Width_Shadow;
  l_absDistancesFromCenter[1u] = lc_halfWholeWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[2u] = lc_halfWholeWidth - lc_halfGradientWidth;
  l_absDistancesFromCenter[3u] = lc_halfWholeWidth - m_trajParams.ParkingTraj_Width_BorderLine + lc_halfGradientWidth;
  l_absDistancesFromCenter[4u] = lc_halfWholeWidth - m_trajParams.ParkingTraj_Width_BorderLine - lc_halfGradientWidth;

  const vfc::float32_t lc_halfGeometryWidth = l_absDistancesFromCenter[0u] * lc_extraWidthForBlurMul;
  m_lineGeometryWidth              = lc_halfGeometryWidth * 2.0f;

  l_normalizedPositions[0u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0u]) / m_lineGeometryWidth;
  l_normalizedPositions[1u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1u]) / m_lineGeometryWidth;
  l_normalizedPositions[2u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[2u]) / m_lineGeometryWidth;
  l_normalizedPositions[3u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[3u]) / m_lineGeometryWidth;
  l_normalizedPositions[4u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[4u]) / m_lineGeometryWidth;
  l_normalizedPositions[5u] = 1.0f - l_normalizedPositions[4u];
  l_normalizedPositions[6u] = 1.0f - l_normalizedPositions[3u];
  l_normalizedPositions[7u] = 1.0f - l_normalizedPositions[2u];
  l_normalizedPositions[8u] = 1.0f - l_normalizedPositions[1u];
  l_normalizedPositions[9u] = 1.0f - l_normalizedPositions[0u];

  const osg::Vec4ub l_lineColor_Inside = pc::util::osgx::toVec4ub(m_trajParams.ParkingTraj_Color_Inside);
  const osg::Vec4ub l_lineColor_BorderLine =  pc::util::osgx::toVec4ub(m_trajParams.ParkingTraj_Color_BorderLine);
  const osg::Vec4ub l_lineColor_Shadow = osg::Vec4ub(51u, 51u, 51u, 230u);
  osg::Vec4ub l_lineColor_Outside = l_lineColor_Shadow;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(static_cast<vfc::int32_t>(lc_imageWidth), static_cast<vfc::int32_t>(lc_imageHeight), static_cast<vfc::int32_t>(lc_imageDepth), static_cast<GLenum>(GL_RGBA), static_cast<GLenum>(GL_UNSIGNED_BYTE));
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < l_normalizedPositions[0u])
      || (l_x_normalized > l_normalizedPositions[9u]) )
    {
      // Outside the wheel track
      (*l_data) = l_lineColor_Outside;
    }
    else if ( (l_x_normalized > l_normalizedPositions[4u])
           && (l_x_normalized < l_normalizedPositions[5u]) )
    {
      // Middle of the wheel track
      (*l_data) = l_lineColor_Inside;
    }
    else if ( (l_x_normalized > l_normalizedPositions[2u])
           && (l_x_normalized < l_normalizedPositions[3u]) )
    {
      // Middle of the left border line
      (*l_data) = l_lineColor_BorderLine;
    }
    else if ( (l_x_normalized > l_normalizedPositions[6u])
           && (l_x_normalized < l_normalizedPositions[7u]) )
    {
      // Middle of the right border line
      (*l_data) = l_lineColor_BorderLine;
    }
    else
    {
      // Gradient
      if (l_x_normalized <= l_normalizedPositions[1u])
      {
        // Left shadow
        (*l_data) = trajectory::helper::smoothstep_GGX_Vec4ub( // PRQA S 2759
          l_lineColor_Shadow, l_lineColor_Outside, l_normalizedPositions[1u], l_normalizedPositions[0u], l_x_normalized);
      }
      else if (l_x_normalized <= l_normalizedPositions[2u])
      {
        // Left border line, left gradient
        (*l_data) = trajectory::helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Shadow, l_lineColor_BorderLine, l_normalizedPositions[1u], l_normalizedPositions[2u], l_x_normalized);
      }
      else if (l_x_normalized <= l_normalizedPositions[4u])
      {
        // Left border line, right gradient
        (*l_data) = trajectory::helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_BorderLine, l_lineColor_Inside, l_normalizedPositions[3u], l_normalizedPositions[4u], l_x_normalized);
      }
      else if (l_x_normalized <= l_normalizedPositions[6u])
      {
        // Right border line, left gradient
        (*l_data) = trajectory::helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_BorderLine, l_normalizedPositions[5u], l_normalizedPositions[6u], l_x_normalized);
      }
      else if (l_x_normalized <= l_normalizedPositions[8u])
      {
        // Right border line, right gradient
        (*l_data) = trajectory::helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_BorderLine, l_lineColor_Shadow, l_normalizedPositions[7u], l_normalizedPositions[8u], l_x_normalized);
      }
      else
      {
        // Right shadow
        (*l_data) = trajectory::helper::smoothstep_GGX_Vec4ub( // PRQA S 2759
          l_lineColor_Shadow, l_lineColor_Outside, l_normalizedPositions[8u], l_normalizedPositions[9u], l_x_normalized);
      }
    }
    ++l_data;
  }

  return l_image;
}


void ExpModeTrajectory::loadTexture()
{
  osg::Image* const l_texImage = create1DTexture();
  osg::Texture2D* const l_tex2D = new osg::Texture2D(l_texImage);
  l_tex2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_tex2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_tex2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_tex2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  l_tex2D->setResizeNonPowerOfTwoHint(false);
  l_tex2D->setUnRefImageDataAfterApply(true);

  osg::StateSet* const l_stateSet = m_geometry->getOrCreateStateSet();
  l_stateSet->setTextureAttribute(0u, l_tex2D);
}


void ExpModeTrajectory::applyFading(
  vfc::uint32_t f_splinePointIndex_Opaque,
  vfc::uint32_t f_splinePointIndex_Transparent,
  vfc::float32_t f_startValue,
  vfc::float32_t f_endValue)
{
  for (vfc::uint32_t pointIndex = f_splinePointIndex_Opaque; pointIndex <= f_splinePointIndex_Transparent; ++pointIndex)
  {
    const vfc::float32_t alphaMul = trajectory::helper::smoothstep(
      f_startValue, f_endValue,
      static_cast<vfc::float32_t>(f_splinePointIndex_Opaque),
      static_cast<vfc::float32_t>(f_splinePointIndex_Transparent),
      static_cast<vfc::float32_t>(pointIndex));

    const vfc::uint32_t l_leftWT_VertexIndexTemp  = m_splinePointCount * 0u + pointIndex * 2u;
    const vfc::uint32_t l_rightWT_VertexIndexTemp = m_splinePointCount * 2u + pointIndex * 2u;

    (*m_vertexData.Colors)[l_leftWT_VertexIndexTemp  + 0u].a() *= alphaMul;
    (*m_vertexData.Colors)[l_leftWT_VertexIndexTemp  + 1u].a() *= alphaMul;
    (*m_vertexData.Colors)[l_rightWT_VertexIndexTemp + 0u].a() *= alphaMul;
    (*m_vertexData.Colors)[l_rightWT_VertexIndexTemp + 1u].a() *= alphaMul;
  }
}


void ExpModeTrajectory::constructTrajectories()
{
  // start point in 10 meters behind the car - direction forward
  ControlPoint CP1;
  CP1.pos = osg::Vec2f(-10.0f, 0.0f);
  CP1.startDir = osg::Vec2f(1.0f, 0.0f);
  CP1.endDir = -CP1.startDir;

  // end point 15 meters in front of car  - direction forward
  ControlPoint CP2;
  CP2.pos = osg::Vec2f(15.0f, 0.0f);
  CP2.startDir = osg::Vec2f(1.0f, 0.0f);
  CP2.endDir = -CP2.startDir;

  // create segment
  Segment CP12;
  CP12.length = std::abs(CP1.pos.x() - CP2.pos.x());
  CP12.segmentCount = pc::util::round2uInt(CP12.length * static_cast<vfc::float32_t>(g_trajectorySettings->m_segmentsPerMeter) + 0.5f);

  m_splinePointCount = CP12.segmentCount + 1u;

  if (m_splineMaxPointCount < m_splinePointCount)
  {
    resizeArrays(m_splinePointCount);
  }

  // create spline with the controllPoints
  // cc::util::beziercurve::ControlPoint curve_CP12_start = cc::util::beziercurve::ControlPoint(CP1.pos, CP1.pos + CP1.startDir);
  // cc::util::beziercurve::ControlPoint curve_CP12_end   = cc::util::beziercurve::ControlPoint(CP2.pos, CP2.pos + CP2.endDir);
  // unsigned int curve_CP12_startIndex = 0;
  // cc::util::beziercurve::BezierCurve curve_CP12 = cc::util::beziercurve::BezierCurve(curve_CP12_start, curve_CP12_end, CP12.segmentCount, curve_CP12_startIndex, m_splineVertexArray);
  // curve_CP12.generateVertices(true);


  //create left and right trajectory vertices
  // Left perpendicular vectors
  osg::Vec2f l_segment0_RightPerpVec;
  osg::Vec2f l_segment1_RightPerpVec;
  // Normals
  osg::Vec2f l_leftNormal;
  osg::Vec2f l_rightNormal;
  // Transform to the middle of the left or the right wheel tracks (from the trajectory center spline)
  osg::Vec2f l_toLeftSpline;
  osg::Vec2f l_toRightSpline;
  // Transform to the left or right edge of the wheel track (from the wheel track center spline)
  osg::Vec2f l_toWheelTrackLeftEdge;
  osg::Vec2f l_toWheelTrackRightEdge;


  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < m_splinePointCount; ++l_vertexIndex)
  {
    if (0u == l_vertexIndex)
    {
      // First spline vertex
      l_segment1_RightPerpVec = (*m_splineVertexArray)[l_vertexIndex + 1u]
                              - (*m_splineVertexArray)[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment1_RightPerpVec;
      l_rightNormal.normalize();  // PRQA S 3804  // PRQA S 3803
    }
    else if ((m_splinePointCount - 1u) == l_vertexIndex)
    {
      // Last spline vertex
      l_segment0_RightPerpVec = (*m_splineVertexArray)[l_vertexIndex + 0u]
                              - (*m_splineVertexArray)[l_vertexIndex - 1u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec;
      l_rightNormal.normalize();  // PRQA S 3804  // PRQA S 3803
    }
    else
    {
      l_segment0_RightPerpVec = (*m_splineVertexArray)[l_vertexIndex + 0u]
                              - (*m_splineVertexArray)[l_vertexIndex - 1u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_segment1_RightPerpVec = (*m_splineVertexArray)[l_vertexIndex + 1u]
                              - (*m_splineVertexArray)[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec + l_segment1_RightPerpVec;
      l_rightNormal.normalize();  // PRQA S 3804  // PRQA S 3803
    }
    l_leftNormal = -l_rightNormal;


    const vfc::float32_t l_rearTrack = pc::vehicle::g_mechanicalData->m_trackRear;

    l_toLeftSpline  = l_leftNormal  * l_rearTrack * 0.5f;
    l_toRightSpline = l_rightNormal * l_rearTrack * 0.5f;
    l_toWheelTrackLeftEdge  = l_leftNormal  * m_lineGeometryWidth * 0.5f;
    l_toWheelTrackRightEdge = l_rightNormal * m_lineGeometryWidth * 0.5f;


    const vfc::uint32_t l_leftWT_VertexIndexTemp  = m_splinePointCount * 0u + l_vertexIndex * 2u;
    const vfc::uint32_t l_rightWT_VertexIndexTemp = m_splinePointCount * 2u + l_vertexIndex * 2u;
    osg::Vec4f l_vertexColor;


    l_vertexColor = osg::Vec4f(g_trajectorySettings->m_normalColorMul, g_trajectorySettings->m_normalColorMul, g_trajectorySettings->m_normalColorMul, 1.0f);

    // Left wheel track
    (*m_vertexData.Vertices) [l_leftWT_VertexIndexTemp  + 0u].set( osg::Vec3f( (*m_splineVertexArray)[l_vertexIndex] + l_toLeftSpline  + l_toWheelTrackLeftEdge,  g_trajectorySettings->m_height ) );
    (*m_vertexData.Vertices) [l_leftWT_VertexIndexTemp  + 1u].set( osg::Vec3f( (*m_splineVertexArray)[l_vertexIndex] + l_toLeftSpline  + l_toWheelTrackRightEdge, g_trajectorySettings->m_height ) );
    (*m_vertexData.Colors)   [l_leftWT_VertexIndexTemp  + 0u].set( l_vertexColor.r(), l_vertexColor.g(), l_vertexColor.b(), l_vertexColor.a() );
    (*m_vertexData.Colors)   [l_leftWT_VertexIndexTemp  + 1u].set( l_vertexColor.r(), l_vertexColor.g(), l_vertexColor.b(), l_vertexColor.a() );
    (*m_vertexData.TexCoords)[l_leftWT_VertexIndexTemp  + 0u].set( 0.0f, 0.5f );
    (*m_vertexData.TexCoords)[l_leftWT_VertexIndexTemp  + 1u].set( 1.0f, 0.5f );
    // Right wheel track
    (*m_vertexData.Vertices) [l_rightWT_VertexIndexTemp + 0u].set( osg::Vec3f( (*m_splineVertexArray)[l_vertexIndex] + l_toRightSpline + l_toWheelTrackLeftEdge,  g_trajectorySettings->m_height ) );
    (*m_vertexData.Vertices) [l_rightWT_VertexIndexTemp + 1u].set( osg::Vec3f( (*m_splineVertexArray)[l_vertexIndex] + l_toRightSpline + l_toWheelTrackRightEdge, g_trajectorySettings->m_height ) );
    (*m_vertexData.Colors)   [l_rightWT_VertexIndexTemp + 0u].set( l_vertexColor.r(), l_vertexColor.g(), l_vertexColor.b(), l_vertexColor.a() );
    (*m_vertexData.Colors)   [l_rightWT_VertexIndexTemp + 1u].set( l_vertexColor.r(), l_vertexColor.g(), l_vertexColor.b(), l_vertexColor.a() );
    (*m_vertexData.TexCoords)[l_rightWT_VertexIndexTemp + 0u].set( 0.0f, 0.5f );
    (*m_vertexData.TexCoords)[l_rightWT_VertexIndexTemp + 1u].set( 1.0f, 0.5f );
  }
  (*m_vertexData.Normals)[0u] = osg::Vec3f(0.0f, 0.0f, 1.0f);

  const vfc::uint32_t l_lastSplinePoint = m_splinePointCount - 1u;
  applyFading(0u, static_cast<vfc::uint32_t>(g_trajectorySettings->m_fadingLength), 0.0f, 1.0f); // PRQA S 3016
  applyFading(l_lastSplinePoint - static_cast<vfc::uint32_t>(g_trajectorySettings->m_fadingLength), l_lastSplinePoint, 1.0f, 0.0f); // PRQA S 3016

  const vfc::uint32_t l_vertexIndexTemp_RightOffset = m_splinePointCount * 2u;

  m_vertexData.Indices->clear();

  for (vfc::uint32_t l_sideIndex = 0u; l_sideIndex < 2u; l_sideIndex++) // 0: Left WT, 1: Right WT
  {
    for (vfc::uint32_t l_quadIndex = 0u; l_quadIndex < (m_splinePointCount - 1u); ++l_quadIndex)
    {
      typedef osg::DrawElementsUShort::value_type UShort;
      const vfc::uint32_t l_vertexIndexTemp = (l_sideIndex * l_vertexIndexTemp_RightOffset) + (l_quadIndex * 2u);
      // Top left triangle
      m_vertexData.Indices->push_back(static_cast<UShort> (l_vertexIndexTemp + 1u));
      m_vertexData.Indices->push_back(static_cast<UShort> (l_vertexIndexTemp + 0u));
      m_vertexData.Indices->push_back(static_cast<UShort> (l_vertexIndexTemp + 2u));
      // Bottom right triangle
      m_vertexData.Indices->push_back(static_cast<UShort> (l_vertexIndexTemp + 2u));
      m_vertexData.Indices->push_back(static_cast<UShort> (l_vertexIndexTemp + 3u));
      m_vertexData.Indices->push_back(static_cast<UShort> (l_vertexIndexTemp + 1u));
    }
  }
  makeDirty();
}


vfc::float32_t ExpModeTrajectory::getAlpha() const
{
  vfc::float32_t l_alpha = 0.0f;
  m_alphaUniform->get(l_alpha);  // PRQA S 3803
  return l_alpha;
}


void ExpModeTrajectory::setAlpha(vfc::float32_t f_alpha)
{
  m_alphaUniform->set(f_alpha);  // PRQA S 3804  // PRQA S 3803
}


void ExpModeTrajectory::makeDirty() // PRQA S 4211
{
  m_vertexData.Vertices->dirty();
  m_vertexData.Colors->dirty();
  m_vertexData.Indices->dirty();
  m_vertexData.Normals->dirty();
  m_vertexData.TexCoords->dirty();
  m_geometry->dirtyBound();
}


} // namespace trajectory
} // namespace assets
} // namespace cc
