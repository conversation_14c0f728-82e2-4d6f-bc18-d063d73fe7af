//
// File: ViewModexViewStateMachine_R2015b.h
//
// Code generated for Simulink model 'ViewModexViewStateMachine_R2015b'.
//
// Model version                  : 11.419
// Simulink Coder version         : 9.6 (R2021b) 14-May-2021
// C/C++ source code generated on : Fri Aug  1 18:05:47 2025
//
// Target selection: ert.tlc
// Embedded hardware selection: ARM Compatible->ARM Cortex
// Code generation objectives:
//    1. Execution efficiency
//    2. RAM efficiency
//    3. ROM efficiency
//    4. MISRA C:2012 guidelines
//    5. Debugging
//    6. Safety precaution
// Validation result: Passed (29), Warnings (4), Error (0)
//
#ifndef RTW_HEADER_ViewModexViewStateMachine_R2015b_h_
#define RTW_HEADER_ViewModexViewStateMachine_R2015b_h_
#include <cmath>
#include "rtwtypes.h"
#include "ViewModexViewStateMachine_R2015b_types.h"

// Macros for accessing real-time model data structure
#ifndef rtmGetErrorStatus
#define rtmGetErrorStatus(rtm)         ((rtm)->errorStatus)
#endif

#ifndef rtmSetErrorStatus
#define rtmSetErrorStatus(rtm, val)    ((rtm)->errorStatus = (val))
#endif

#define ViewModexViewStateMachine_R2015b_M (rtM)

// user code (top of header file)
//BEGIN EXTERN FUN

//note: to read the stateflow generated doc, start at the file below:
//MATLAB/xViewStateMachine_ert_rtw/html/xViewStateMachine_codegen_rpt.html
// 20161014, caf8si sage:
// Forward definitions, Matlab uses stubs from my_header.(hpp|cpp)
// for model validation during generation.
// You have to compile and then link to something that implements these externals.
extern void changeCam( const int ) ;
extern void hideView( const int ) ;
extern void showView( const int ) ;

// END EXTERN FUN

// Block signals and states (default storage) for system '<Root>'
struct D_Work
{
  ExitDelay BusConversion_InsertedFor_Mai_d;
  AnimationState BusConversion_InsertedFor_Mai_m;
  real_T l_lowerLimitX;                // '<S1>/FreeModeHandling'
  real_T l_upperLimitY;                // '<S1>/FreeModeHandling'
  real_T l_upperLimitX;                // '<S1>/FreeModeHandling'
  uint32_T temporalCounter_i2;         // '<S1>/MainSM_Lev2'
  uint32_T temporalCounter_i3;         // '<S1>/MainSM_Lev2'
  uint32_T temporalCounter_i4;         // '<S1>/MainSM_Lev2'
  uint32_T temporalCounter_i5;         // '<S1>/MainSM_Lev2'
  uint32_T temporalCounter_i6;         // '<S1>/MainSM_Lev2'
  uint32_T in_campositionX_start;      // '<S1>/FreeModeHandling'
  uint32_T in_campositionY_start;      // '<S1>/FreeModeHandling'
  uint32_T in_CoorX_start;             // '<S1>/FreeModeHandling'
  uint32_T in_CoorY_start;             // '<S1>/FreeModeHandling'
  uint32_T temporalCounter_i1;         // '<S1>/CpcViewHandling'
  EScreenID UnitDelay;                 // '<S1>/Unit Delay'
  EScreenID UnitDelay5;                // '<S1>/Unit Delay5'
  EScreenID out_SVSLastView;           // '<S1>/LastViewHandling'
  EScreenID out_freemodeviewID;        // '<S1>/FreeModeHandling'
  EScreenID UnitDelay_DSTATE;          // '<S1>/Unit Delay'
  EScreenID UnitDelay5_DSTATE;         // '<S1>/Unit Delay5'
  EScreenID in_HUViewReq_start;        // '<S1>/MainSM_Lev2'
  EScreenID l_lastView;                // '<S1>/LastViewHandling'
  EScreenID in_displayedView_start;    // '<S1>/LastViewHandling'
  EScreenID in_displayedVewL_start;    // '<S1>/FreeModeHandling'
  EScreenID in_SVSCurrentView_start;   // '<S1>/CpcHuSwitch'
  ECalibStatus in_calibStatus_start;   // '<S1>/MainSM_Lev2'
  ESystemStr in_SystemStr_start;       // '<S1>/MainSM_Lev2'
  ESVSViewMode out_SVSViewModelLast;   // '<S1>/LastViewHandling'
  ESVSViewMode UnitDelay1_DSTATE;      // '<S1>/Unit Delay1'
  ESVSViewMode in_HUSVSMode_start;     // '<S1>/MainSM_Lev2'
  ESVSViewMode l_lastViewMode;         // '<S1>/LastViewHandling'
  EViewModeGroup UnitDelay7;           // '<S1>/Unit Delay7'
  EViewModeGroup UnitDelay7_DSTATE;    // '<S1>/Unit Delay7'
  ESVSScreenType UnitDelay9_DSTATE;    // '<S1>/Unit Delay9'
  uint16_T temporalCounter_i2_i;       // '<S1>/CpcHuSwitch'
  uint16_T temporalCounter_i3_g;       // '<S1>/CpcHuSwitch'
  EVoiceDockFb out_voiceDockFeedback_b;// '<S1>/MainSM_Lev2'
  ECompeteReqStatus UnitDelay6_DSTATE; // '<S1>/Unit Delay6'
  uint8_T is_active_c7_ViewModexViewState;// '<S1>/voiceDockFeedbackHandling'
  uint8_T is_c7_ViewModexViewStateMachine;// '<S1>/voiceDockFeedbackHandling'
  uint8_T temporalCounter_i1_e;        // '<S1>/voiceDockFeedbackHandling'
  uint8_T is_active_c9_ViewModexViewState;// '<S1>/screenTypeHandling'
  uint8_T is_c9_ViewModexViewStateMachine;// '<S1>/screenTypeHandling'
  uint8_T is_active_c18_ViewModexViewStat;// '<S1>/PasWarnToneHandling'
  uint8_T is_RadarActivHandling;       // '<S1>/PasWarnToneHandling'
  uint8_T is_active_c5_ViewModexViewState;// '<S1>/ObstacleAndSteeringrHandling' 
  uint8_T is_SteeringActivateHandling; // '<S1>/ObstacleAndSteeringrHandling'
  uint8_T is_ObstacleActivateHandling; // '<S1>/ObstacleAndSteeringrHandling'
  uint8_T is_SonarActivate_Handling;   // '<S1>/ObstacleAndSteeringrHandling'
  uint8_T is_NarrowLaneActivateHandling1;// '<S1>/ObstacleAndSteeringrHandling'
  uint8_T is_NarrowLaneActivate_Handling;// '<S1>/ObstacleAndSteeringrHandling'
  uint8_T is_active_c4_ViewModexViewState;// '<S1>/ManualChangeAvailHandling'
  uint8_T is_c4_ViewModexViewStateMachine;// '<S1>/ManualChangeAvailHandling'
  uint8_T is_active_c1_ViewModexViewState;// '<S1>/MainSM_Lev2'
  uint8_T is_SuperState;               // '<S1>/MainSM_Lev2'
  uint8_T is_SuperStateL2;             // '<S1>/MainSM_Lev2'
  uint8_T is_Unavailable;              // '<S1>/MainSM_Lev2'
  uint8_T is_spdtoohigh;               // '<S1>/MainSM_Lev2'
  uint8_T is_Power_Save_Mode;          // '<S1>/MainSM_Lev2'
  uint8_T is_AVM_Error;                // '<S1>/MainSM_Lev2'
  uint8_T is_Available;                // '<S1>/MainSM_Lev2'
  uint8_T is_FullScreen_R;             // '<S1>/MainSM_Lev2'
  uint8_T is_FloatingView;             // '<S1>/MainSM_Lev2'
  uint8_T is_FloatingFrontViews_NotPark;// '<S1>/MainSM_Lev2'
  uint8_T is_FloatingRearViews_NotPark;// '<S1>/MainSM_Lev2'
  uint8_T is_FloatingViews_Park;       // '<S1>/MainSM_Lev2'
  uint8_T is_ParkingPlan_FloatView;    // '<S1>/MainSM_Lev2'
  uint8_T is_Park_View;                // '<S1>/MainSM_Lev2'
  uint8_T is_NotRMode;                 // '<S1>/MainSM_Lev2'
  uint8_T is_Voice_Views;              // '<S1>/MainSM_Lev2'
  uint8_T is_Voice_View_Succeed;       // '<S1>/MainSM_Lev2'
  uint8_T is_FullScreenViews_From_FloatSc;// '<S1>/MainSM_Lev2'
  uint8_T is_ManualChange;             // '<S1>/MainSM_Lev2'
  uint8_T is_ViewModeChanged;          // '<S1>/MainSM_Lev2'
  uint8_T is_LastViewModeGroup;        // '<S1>/MainSM_Lev2'
  uint8_T is_ViewGroup_WheelView;      // '<S1>/MainSM_Lev2'
  uint8_T is_ViewGroup_SignleView;     // '<S1>/MainSM_Lev2'
  uint8_T is_ViewGroup_WideView;       // '<S1>/MainSM_Lev2'
  uint8_T is_Screen_Compete_Request;   // '<S1>/MainSM_Lev2'
  uint8_T is_Has_Compete_Feedback;     // '<S1>/MainSM_Lev2'
  uint8_T is_durationNoSonarWarning;   // '<S1>/MainSM_Lev2'
  uint8_T is_durationNoChange;         // '<S1>/MainSM_Lev2'
  uint8_T is_ActiveState;              // '<S1>/MainSM_Lev2'
  uint8_T is_Acivate_Through_Compete;  // '<S1>/MainSM_Lev2'
  uint8_T is_GearNotR_Activated_Compete_R;// '<S1>/MainSM_Lev2'
  uint8_T is_GearR_Activated_Compete_Requ;// '<S1>/MainSM_Lev2'
  uint8_T is_Normal_activate;          // '<S1>/MainSM_Lev2'
  uint8_T is_Steering_exitHandling;    // '<S1>/MainSM_Lev2'
  uint8_T is_Sonar_Activated;          // '<S1>/MainSM_Lev2'
  uint8_T is_Sonar_exitHandling;       // '<S1>/MainSM_Lev2'
  uint8_T is_NarrowLane_Activated;     // '<S1>/MainSM_Lev2'
  uint8_T is_Sonar_exitHandling_i;     // '<S1>/MainSM_Lev2'
  uint8_T is_ParkActiveHandling;       // '<S1>/MainSM_Lev2'
  uint8_T is_Exit_Handling;            // '<S1>/MainSM_Lev2'
  uint8_T is_freeModeLogic;            // '<S1>/MainSM_Lev2'
  uint8_T is_freeModeAct;              // '<S1>/MainSM_Lev2'
  uint8_T temporalCounter_i1_b;        // '<S1>/MainSM_Lev2'
  uint8_T in_calibActive_start;        // '<S1>/MainSM_Lev2'
  uint8_T is_active_c6_ViewModexViewState;// '<S1>/FreeModeHandling'
  uint8_T is_freemodeActive;           // '<S1>/FreeModeHandling'
  uint8_T is_isViewChangeAvl;          // '<S1>/FreeModeHandling'
  uint8_T is_ViewID;                   // '<S1>/FreeModeHandling'
  uint8_T temporalCounter_i1_p;        // '<S1>/FreeModeHandling'
  uint8_T is_active_c11_ViewModexViewStat;// '<S1>/DegradationHandling'
  uint8_T is_FrontCamDeg;              // '<S1>/DegradationHandling'
  uint8_T is_RightCamDeg;              // '<S1>/DegradationHandling'
  uint8_T is_LeftCamDeg;               // '<S1>/DegradationHandling'
  uint8_T is_RearCamDeg;               // '<S1>/DegradationHandling'
  uint8_T is_SVSDegSts;                // '<S1>/DegradationHandling'
  uint8_T is_active_c16_ViewModexViewStat;// '<S1>/CpcViewHandling'
  uint8_T is_c16_ViewModexViewStateMachin;// '<S1>/CpcViewHandling'
  uint8_T is_true;                     // '<S1>/CpcViewHandling'
  uint8_T is_active_c2_ViewModexViewState;// '<S1>/CpcHuSwitch'
  uint8_T is_status;                   // '<S1>/CpcHuSwitch'
  uint8_T is_timesTop;                 // '<S1>/CpcHuSwitch'
  uint8_T is_counterTopView;           // '<S1>/CpcHuSwitch'
  uint8_T is_timesFront;               // '<S1>/CpcHuSwitch'
  uint8_T is_counterFrontView;         // '<S1>/CpcHuSwitch'
  uint8_T l_counterTopView;            // '<S1>/CpcHuSwitch'
  uint8_T l_counterFrontView;          // '<S1>/CpcHuSwitch'
  uint8_T temporalCounter_i1_bn;       // '<S1>/CpcHuSwitch'
  EDockAvmButtonPress in_dockAvmButtonPress_prev;// '<S1>/MainSM_Lev2'
  EDockAvmButtonPress in_dockAvmButtonPress_start;// '<S1>/MainSM_Lev2'
  EFloatViewType in_FloatViewChange_prev;// '<S1>/MainSM_Lev2'
  EFloatViewType in_FloatViewChange_start;// '<S1>/MainSM_Lev2'
  EGear in_gear_prev;                  // '<S1>/MainSM_Lev2'
  EGear in_gear_start;                 // '<S1>/MainSM_Lev2'
  ESRActiveSts in_SRIsActive_prev;     // '<S1>/MainSM_Lev2'
  ESRActiveSts in_SRIsActive_start;    // '<S1>/MainSM_Lev2'
  EVoiceDockReq in_voiceDockRequest_prev;// '<S1>/MainSM_Lev2'
  EVoiceDockReq in_voiceDockRequest_start;// '<S1>/MainSM_Lev2'
  boolean_T UnitDelay8;                // '<S1>/Unit Delay8'
  boolean_T out_narrowLaneAct;         // '<S1>/ObstacleAndSteeringrHandling'
  boolean_T out_obstacleAct;           // '<S1>/ObstacleAndSteeringrHandling'
  boolean_T out_steeringAct;           // '<S1>/ObstacleAndSteeringrHandling'
  boolean_T out_touchCoorChange;       // '<S1>/FreeModeHandling'
  boolean_T UnitDelay8_DSTATE;         // '<S1>/Unit Delay8'
  boolean_T in_parkingsts_prev;        // '<S1>/MainSM_Lev2'
  boolean_T in_parkingsts_start;       // '<S1>/MainSM_Lev2'
  boolean_T in_androidIconActive_prev; // '<S1>/MainSM_Lev2'
  boolean_T in_androidIconActive_start;// '<S1>/MainSM_Lev2'
  boolean_T in_steeringWheelButtonPressed_p;// '<S1>/MainSM_Lev2'
  boolean_T in_steeringWheelButtonPressed_s;// '<S1>/MainSM_Lev2'
  boolean_T in_obstacleAct_prev;       // '<S1>/MainSM_Lev2'
  boolean_T in_obstacleAct_start;      // '<S1>/MainSM_Lev2'
  boolean_T in_narrowLaneAct_prev;     // '<S1>/MainSM_Lev2'
  boolean_T in_narrowLaneAct_start;    // '<S1>/MainSM_Lev2'
  boolean_T in_steeringAct_prev;       // '<S1>/MainSM_Lev2'
  boolean_T in_steeringAct_start;      // '<S1>/MainSM_Lev2'
  boolean_T in_EnlargeButtonPressed_prev;// '<S1>/MainSM_Lev2'
  boolean_T in_EnlargeButtonPressed_start;// '<S1>/MainSM_Lev2'
  boolean_T in_PowerSaveMode_prev;     // '<S1>/MainSM_Lev2'
  boolean_T in_PowerSaveMode_start;    // '<S1>/MainSM_Lev2'
  boolean_T in_AVMError_prev;          // '<S1>/MainSM_Lev2'
  boolean_T in_AVMError_start;         // '<S1>/MainSM_Lev2'
  boolean_T in_closeButtonPressed_start;// '<S1>/MainSM_Lev2'
  boolean_T in_competeQuit_start;      // '<S1>/MainSM_Lev2'
  boolean_T in_isFreeParking_prev;     // '<S1>/MainSM_Lev2'
  boolean_T in_isFreeParking_start;    // '<S1>/MainSM_Lev2'
  boolean_T doneDoubleBufferReInit;    // '<S1>/LastViewHandling'
};

// External inputs (root inport signals with default storage)
struct ExternalInputs
{
  EScreenID In_HUViewReq;              // '<Root>/In_HUViewReq'
  ESVSViewMode In_HUSVSMode;           // '<Root>/In_HUSVSMode'
  uint32_T In_HUTouchCoorX;            // '<Root>/In_HUTouchCoorX'
  uint32_T In_HUTouchCoorY;            // '<Root>/In_HUTouchCoorY'
  real_T In_vehSpeed;                  // '<Root>/In_vehSpeed'
  EGear In_gear;                       // '<Root>/In_gear'
  boolean_T In_parkstatus;             // '<Root>/In_parkstatus'
  ETouchStatus In_TouchSt;             // '<Root>/In_TouchSt'
  uint32_T In_parkingScreenDelay;      // '<Root>/In_parkingScreenDelay'
  AnimationState In_animationState;    // '<Root>/In_animationState'
  boolean_T In_IMB_FrontSt;            // '<Root>/In_IMB_FrontSt'
  boolean_T In_IMB_LeftSt;             // '<Root>/In_IMB_LeftSt'
  boolean_T In_IMB_RearSt;             // '<Root>/In_IMB_RearSt'
  boolean_T In_IMB_RightSt;            // '<Root>/In_IMB_RightSt'
  real_T In_speedTrigIn;               // '<Root>/In_speedTrigIn'
  real_T In_speedTrigOut;              // '<Root>/In_speedTrigOut'
  uint32_T In_threatDuration;          // '<Root>/In_threatDuration'
  uint32_T In_campositionX;            // '<Root>/In_campositionX'
  uint32_T In_campositionY;            // '<Root>/In_campositionY'
  EPowerMode In_powerMode;             // '<Root>/In_powerMode'
  boolean_T In_FID_SVSEcuInternalStatus;// '<Root>/In_FID_SVSEcuInternalStatus'
  uint32_T In_parkingGuidanceScreenDelay;
                                      // '<Root>/In_parkingGuidanceScreenDelay'
  uint32_T In_smDelay;                 // '<Root>/In_smDelay'
  EPasWarnTone In_pasWarnTone;         // '<Root>/In_pasWarnTone'
  EHuImageWorkMode In_HUImageWorkMode; // '<Root>/In_HUImageWorkMode'
  EHuDisplayModeSwitch In_HUDislayModeSwitch;// '<Root>/In_HUDislayModeSwitch'
  EHuDisplayModeExpand In_HUDislayModeExpand;// '<Root>/In_HUDislayModeExpand'
  EHuDisplayModeExpandNew In_HUDislayModeExpandNew;// '<Root>/In_HUDislayModeExpandNew' 
  boolean_T In_isCpcActive;            // '<Root>/In_isCpcActive'
  uint32_T In_cpcDelay;                // '<Root>/In_cpcDelay'
  boolean_T In_isCpcFuncOn;            // '<Root>/In_isCpcFuncOn'
  ESettingSts In_huPasAct;             // '<Root>/In_huPasAct'
  ESettingSts In_huSteeringAct;        // '<Root>/In_huSteeringAct'
  ESettingSts In_huDGearAct;           // '<Root>/In_huDGearAct'
  boolean_T In_isFirstDGear;           // '<Root>/In_isFirstDGear'
  boolean_T In_HUShowReq;              // '<Root>/In_HUShowReq'
  ECalibStatus In_calibStatus;         // '<Root>/In_calibStatus'
  uint8_T In_calibActive;              // '<Root>/In_calibActive'
  boolean_T In_androidIconActive;      // '<Root>/In_androidIconActive'
  boolean_T In_closeButtonPressed;     // '<Root>/In_closeButtonPressed'
  ESonarTrigLevel In_distTrigLevel;    // '<Root>/In_distTrigLevel'
  real32_T In_huSteeringAngleTrigIn;   // '<Root>/In_huSteeringAngleTrigIn'
  real32_T In_huSteeringAngleTrigOut;  // '<Root>/In_huSteeringAngleTrigOut'
  real_T In_huSteeringAngleFront;      // '<Root>/In_huSteeringAngleFront'
  EVoiceDockReq In_voiceDockRequest;   // '<Root>/In_voiceDockRequest'
  ESRActiveSts In_SRIsActive;          // '<Root>/In_SRIsActive'
  HmiUIElements In_CPCSwitch_Layouts;  // '<Root>/In_CPCSwitch_Layouts'
  ECompeteActiveAllow In_competeActiveAllow;// '<Root>/In_competeActiveAllow'
  boolean_T In_competeQuit;            // '<Root>/In_competeQuit'
  boolean_T In_ignoreCompete;          // '<Root>/In_ignoreCompete'
  boolean_T In_backkeyEvent;           // '<Root>/In_backkeyEvent'
  boolean_T In_steeringWheelButtonPressed;
                                      // '<Root>/In_steeringWheelButtonPressed'
  boolean_T In_ModWarning;             // '<Root>/In_ModWarning'
  ESystemStr In_SystemStr;             // '<Root>/In_SystemStr'
  boolean_T In_AVMError;               // '<Root>/In_AVMError'
  boolean_T In_PowerSaveMode;          // '<Root>/In_PowerSaveMode'
  boolean_T In_isFirstRGear;           // '<Root>/In_isFirstRGear'
  SonarDistLevel In_sonarDistLevel;    // '<Root>/In_sonarDistLevel'
  ExitDelay In_exitDelay;              // '<Root>/In_exitDelay'
  EDockAvmButtonPress In_dockAvmButtonPress;// '<Root>/In_dockAvmButtonPress'
  real_T In_timeStepScaleFactor;       // '<Root>/In_timeStepScaleFactor'
  boolean_T In_SRIsTopActivity;        // '<Root>/In_SRIsTopActivity'
  boolean_T In_HaveEverActivated;      // '<Root>/In_HaveEverActivated'
  EFloatViewType In_FloatViewChange;   // '<Root>/In_FloatViewChange'
  boolean_T In_EnlargeButtonPressed;   // '<Root>/In_EnlargeButtonPressed'
  boolean_T In_ParkingSearch;          // '<Root>/In_ParkingSearch'
  boolean_T In_settingNarrowLaneActivate;// '<Root>/In_settingNarrowLaneActivate' 
  boolean_T in_isFreeParking;          // '<Root>/in_isFreeParking'
};

// External outputs (root outports fed by signals with default storage)
struct ExternalOutputs
{
  ESystem Out_systemAvailable;         // '<Root>/Out_systemAvailable'
  boolean_T Out_systemActive;          // '<Root>/Out_systemActive'
  EScreenID Out_displayedView;         // '<Root>/Out_displayedView'
  EScreenID Out_SVSCurrentView;        // '<Root>/Out_SVSCurrentView'
  boolean_T Out_SVSShowReq;            // '<Root>/Out_SVSShowReq'
  ESVSViewMode Out_SVSViewModeSts;     // '<Root>/Out_SVSViewModeSts'
  ENotActiveReason Out_SVSUnavlMsgs;   // '<Root>/Out_SVSUnavlMsgs'
  uint8_T Out_SVSFrViewSts;            // '<Root>/Out_SVSFrViewSts'
  uint8_T Out_SVSRiViewSts;            // '<Root>/Out_SVSRiViewSts'
  uint8_T Out_SVSReViewSts;            // '<Root>/Out_SVSReViewSts'
  uint8_T Out_SVSLeViewSts;            // '<Root>/Out_SVSLeViewSts'
  boolean_T Out_isFreeMode;            // '<Root>/Out_isFreeMode'
  boolean_T Out_isFreeModeAct;         // '<Root>/Out_isFreeModeAct'
  ESVSScreenType Out_SVSScreenType;    // '<Root>/Out_SVSScreenType'
  EVoiceDockFb Out_voiceDockFeedback;  // '<Root>/Out_voiceDockFeedback'
  boolean_T Out_HuCPCActive;           // '<Root>/Out_HuCPCActive'
  ESVSScreenType Out_competeScreenTypeReq;// '<Root>/Out_competeScreenTypeReq'
  ECompeteReqStatus Out_competeReqStatus;// '<Root>/Out_competeReqStatus'
  EViewModeGroup Out_ViewModeGroup;    // '<Root>/Out_ViewModeGroup'
  EShowReqMode Out_ShowReqMod;         // '<Root>/Out_ShowReqMod'
};

// Real-time Model Data Structure
struct tag_RTM
{
  const char_T * volatile errorStatus;
};

// Class declaration for model ViewModexViewStateMachine_R2015b
class ViewModeStateFlowStateMachineModelClass
{
  // public data and function members
 public:
  // Real-Time Model get method
  RT_MODEL * getRTM();

  // Root inport: '<Root>/In_HUViewReq' set method
  void setIn_HUViewReq(EScreenID localArgInput);

  // Root inport: '<Root>/In_HUSVSMode' set method
  void setIn_HUSVSMode(ESVSViewMode localArgInput);

  // Root inport: '<Root>/In_HUTouchCoorX' set method
  void setIn_HUTouchCoorX(uint32_T localArgInput);

  // Root inport: '<Root>/In_HUTouchCoorY' set method
  void setIn_HUTouchCoorY(uint32_T localArgInput);

  // Root inport: '<Root>/In_vehSpeed' set method
  void setIn_vehSpeed(real_T localArgInput);

  // Root inport: '<Root>/In_gear' set method
  void setIn_gear(EGear localArgInput);

  // Root inport: '<Root>/In_parkstatus' set method
  void setIn_parkstatus(boolean_T localArgInput);

  // Root inport: '<Root>/In_TouchSt' set method
  void setIn_TouchSt(ETouchStatus localArgInput);

  // Root inport: '<Root>/In_parkingScreenDelay' set method
  void setIn_parkingScreenDelay(uint32_T localArgInput);

  // Root inport: '<Root>/In_animationState' set method
  void setIn_animationState(AnimationState localArgInput);

  // Root inport: '<Root>/In_IMB_FrontSt' set method
  void setIn_IMB_FrontSt(boolean_T localArgInput);

  // Root inport: '<Root>/In_IMB_LeftSt' set method
  void setIn_IMB_LeftSt(boolean_T localArgInput);

  // Root inport: '<Root>/In_IMB_RearSt' set method
  void setIn_IMB_RearSt(boolean_T localArgInput);

  // Root inport: '<Root>/In_IMB_RightSt' set method
  void setIn_IMB_RightSt(boolean_T localArgInput);

  // Root inport: '<Root>/In_speedTrigIn' set method
  void setIn_speedTrigIn(real_T localArgInput);

  // Root inport: '<Root>/In_speedTrigOut' set method
  void setIn_speedTrigOut(real_T localArgInput);

  // Root inport: '<Root>/In_threatDuration' set method
  void setIn_threatDuration(uint32_T localArgInput);

  // Root inport: '<Root>/In_campositionX' set method
  void setIn_campositionX(uint32_T localArgInput);

  // Root inport: '<Root>/In_campositionY' set method
  void setIn_campositionY(uint32_T localArgInput);

  // Root inport: '<Root>/In_powerMode' set method
  void setIn_powerMode(EPowerMode localArgInput);

  // Root inport: '<Root>/In_FID_SVSEcuInternalStatus' set method
  void setIn_FID_SVSEcuInternalStatus(boolean_T localArgInput);

  // Root inport: '<Root>/In_parkingGuidanceScreenDelay' set method
  void setIn_parkingGuidanceScreenDelay(uint32_T localArgInput);

  // Root inport: '<Root>/In_smDelay' set method
  void setIn_smDelay(uint32_T localArgInput);

  // Root inport: '<Root>/In_pasWarnTone' set method
  void setIn_pasWarnTone(EPasWarnTone localArgInput);

  // Root inport: '<Root>/In_HUImageWorkMode' set method
  void setIn_HUImageWorkMode(EHuImageWorkMode localArgInput);

  // Root inport: '<Root>/In_HUDislayModeSwitch' set method
  void setIn_HUDislayModeSwitch(EHuDisplayModeSwitch localArgInput);

  // Root inport: '<Root>/In_HUDislayModeExpand' set method
  void setIn_HUDislayModeExpand(EHuDisplayModeExpand localArgInput);

  // Root inport: '<Root>/In_HUDislayModeExpandNew' set method
  void setIn_HUDislayModeExpandNew(EHuDisplayModeExpandNew localArgInput);

  // Root inport: '<Root>/In_isCpcActive' set method
  void setIn_isCpcActive(boolean_T localArgInput);

  // Root inport: '<Root>/In_cpcDelay' set method
  void setIn_cpcDelay(uint32_T localArgInput);

  // Root inport: '<Root>/In_isCpcFuncOn' set method
  void setIn_isCpcFuncOn(boolean_T localArgInput);

  // Root inport: '<Root>/In_huPasAct' set method
  void setIn_huPasAct(ESettingSts localArgInput);

  // Root inport: '<Root>/In_huSteeringAct' set method
  void setIn_huSteeringAct(ESettingSts localArgInput);

  // Root inport: '<Root>/In_huDGearAct' set method
  void setIn_huDGearAct(ESettingSts localArgInput);

  // Root inport: '<Root>/In_isFirstDGear' set method
  void setIn_isFirstDGear(boolean_T localArgInput);

  // Root inport: '<Root>/In_HUShowReq' set method
  void setIn_HUShowReq(boolean_T localArgInput);

  // Root inport: '<Root>/In_calibStatus' set method
  void setIn_calibStatus(ECalibStatus localArgInput);

  // Root inport: '<Root>/In_calibActive' set method
  void setIn_calibActive(uint8_T localArgInput);

  // Root inport: '<Root>/In_androidIconActive' set method
  void setIn_androidIconActive(boolean_T localArgInput);

  // Root inport: '<Root>/In_closeButtonPressed' set method
  void setIn_closeButtonPressed(boolean_T localArgInput);

  // Root inport: '<Root>/In_distTrigLevel' set method
  void setIn_distTrigLevel(ESonarTrigLevel localArgInput);

  // Root inport: '<Root>/In_huSteeringAngleTrigIn' set method
  void setIn_huSteeringAngleTrigIn(real32_T localArgInput);

  // Root inport: '<Root>/In_huSteeringAngleTrigOut' set method
  void setIn_huSteeringAngleTrigOut(real32_T localArgInput);

  // Root inport: '<Root>/In_huSteeringAngleFront' set method
  void setIn_huSteeringAngleFront(real_T localArgInput);

  // Root inport: '<Root>/In_voiceDockRequest' set method
  void setIn_voiceDockRequest(EVoiceDockReq localArgInput);

  // Root inport: '<Root>/In_SRIsActive' set method
  void setIn_SRIsActive(ESRActiveSts localArgInput);

  // Root inport: '<Root>/In_CPCSwitch_Layouts' set method
  void setIn_CPCSwitch_Layouts(HmiUIElements localArgInput);

  // Root inport: '<Root>/In_competeActiveAllow' set method
  void setIn_competeActiveAllow(ECompeteActiveAllow localArgInput);

  // Root inport: '<Root>/In_competeQuit' set method
  void setIn_competeQuit(boolean_T localArgInput);

  // Root inport: '<Root>/In_ignoreCompete' set method
  void setIn_ignoreCompete(boolean_T localArgInput);

  // Root inport: '<Root>/In_backkeyEvent' set method
  void setIn_backkeyEvent(boolean_T localArgInput);

  // Root inport: '<Root>/In_steeringWheelButtonPressed' set method
  void setIn_steeringWheelButtonPressed(boolean_T localArgInput);

  // Root inport: '<Root>/In_ModWarning' set method
  void setIn_ModWarning(boolean_T localArgInput);

  // Root inport: '<Root>/In_SystemStr' set method
  void setIn_SystemStr(ESystemStr localArgInput);

  // Root inport: '<Root>/In_AVMError' set method
  void setIn_AVMError(boolean_T localArgInput);

  // Root inport: '<Root>/In_PowerSaveMode' set method
  void setIn_PowerSaveMode(boolean_T localArgInput);

  // Root inport: '<Root>/In_isFirstRGear' set method
  void setIn_isFirstRGear(boolean_T localArgInput);

  // Root inport: '<Root>/In_sonarDistLevel' set method
  void setIn_sonarDistLevel(SonarDistLevel localArgInput);

  // Root inport: '<Root>/In_exitDelay' set method
  void setIn_exitDelay(ExitDelay localArgInput);

  // Root inport: '<Root>/In_dockAvmButtonPress' set method
  void setIn_dockAvmButtonPress(EDockAvmButtonPress localArgInput);

  // Root inport: '<Root>/In_timeStepScaleFactor' set method
  void setIn_timeStepScaleFactor(real_T localArgInput);

  // Root inport: '<Root>/In_SRIsTopActivity' set method
  void setIn_SRIsTopActivity(boolean_T localArgInput);

  // Root inport: '<Root>/In_HaveEverActivated' set method
  void setIn_HaveEverActivated(boolean_T localArgInput);

  // Root inport: '<Root>/In_FloatViewChange' set method
  void setIn_FloatViewChange(EFloatViewType localArgInput);

  // Root inport: '<Root>/In_EnlargeButtonPressed' set method
  void setIn_EnlargeButtonPressed(boolean_T localArgInput);

  // Root inport: '<Root>/In_ParkingSearch' set method
  void setIn_ParkingSearch(boolean_T localArgInput);

  // Root inport: '<Root>/In_settingNarrowLaneActivate' set method
  void setIn_settingNarrowLaneActivate(boolean_T localArgInput);

  // Root inport: '<Root>/in_isFreeParking' set method
  void setin_isFreeParking(boolean_T localArgInput);

  // Root outport: '<Root>/Out_systemAvailable' get method
  ESystem getOut_systemAvailable() const;

  // Root outport: '<Root>/Out_systemActive' get method
  boolean_T getOut_systemActive() const;

  // Root outport: '<Root>/Out_displayedView' get method
  EScreenID getOut_displayedView() const;

  // Root outport: '<Root>/Out_SVSCurrentView' get method
  EScreenID getOut_SVSCurrentView() const;

  // Root outport: '<Root>/Out_SVSShowReq' get method
  boolean_T getOut_SVSShowReq() const;

  // Root outport: '<Root>/Out_SVSViewModeSts' get method
  ESVSViewMode getOut_SVSViewModeSts() const;

  // Root outport: '<Root>/Out_SVSUnavlMsgs' get method
  ENotActiveReason getOut_SVSUnavlMsgs() const;

  // Root outport: '<Root>/Out_SVSFrViewSts' get method
  uint8_T getOut_SVSFrViewSts() const;

  // Root outport: '<Root>/Out_SVSRiViewSts' get method
  uint8_T getOut_SVSRiViewSts() const;

  // Root outport: '<Root>/Out_SVSReViewSts' get method
  uint8_T getOut_SVSReViewSts() const;

  // Root outport: '<Root>/Out_SVSLeViewSts' get method
  uint8_T getOut_SVSLeViewSts() const;

  // Root outport: '<Root>/Out_isFreeMode' get method
  boolean_T getOut_isFreeMode() const;

  // Root outport: '<Root>/Out_isFreeModeAct' get method
  boolean_T getOut_isFreeModeAct() const;

  // Root outport: '<Root>/Out_SVSScreenType' get method
  ESVSScreenType getOut_SVSScreenType() const;

  // Root outport: '<Root>/Out_voiceDockFeedback' get method
  EVoiceDockFb getOut_voiceDockFeedback() const;

  // Root outport: '<Root>/Out_HuCPCActive' get method
  boolean_T getOut_HuCPCActive() const;

  // Root outport: '<Root>/Out_competeScreenTypeReq' get method
  ESVSScreenType getOut_competeScreenTypeReq() const;

  // Root outport: '<Root>/Out_competeReqStatus' get method
  ECompeteReqStatus getOut_competeReqStatus() const;

  // Root outport: '<Root>/Out_ViewModeGroup' get method
  EViewModeGroup getOut_ViewModeGroup() const;

  // Root outport: '<Root>/Out_ShowReqMod' get method
  EShowReqMode getOut_ShowReqMod() const;

  // model initialize function
  void initialize();
  void ModelExternalOutputInit();

  // model step function
  void step();

  // model terminate function
  void terminate();

  // Constructor
  ViewModeStateFlowStateMachineModelClass();

  // Destructor
  ~ViewModeStateFlowStateMachineModelClass();

  // protected data and function members
 protected:
  // External inputs
  ExternalInputs rtU;

  // External outputs
  ExternalOutputs rtY;

  // private data and function members
 private:
  // Block states
  D_Work rtDWork;

  // private member function(s) for subsystem '<Root>'
  void enter_internal_ViewID(const ESVSViewMode *UnitDelay1);
  void ViewID(const ESVSViewMode *UnitDelay1, const uint32_T
              *in_campositionY_prev, const EScreenID *in_displayedVewL_prev);
  void durationNoChange(const ESVSViewMode *in_HUSVSMode_prev, const EScreenID
                        *in_HUViewReq_prev);
  void enter_internal_Unavailable(void);
  void exit_internal_Unavailable(void);
  void temp_RR(void);
  void enter_internal_Park_View(void);
  void enter_internal_ParkingPlan_Floa(void);
  void enter_internal_FloatingRearView(void);
  void enter_internal_FloatingFrontVie(void);
  void FloatingView(void);
  void ManualChange(void);
  void Voice_Views(void);
  void temp_FL(void);
  void temp_FR(void);
  void temp_PFR(void);
  void temp_PLE(void);
  void temp_PRE(void);
  void temp_PRI(void);
  void temp_RL(void);
  void exit_internal_Available(void);
  void enter_internal_ViewModeChanged(void);
  void enter_internal_NotRMode(void);
  void enter_internal_FullScreen_R(void);
  void enter_internal_Voice_Views(void);
  void Available(const boolean_T *out_manualChangeAvail, const boolean_T
                 *out_isViewChangeAvl, const boolean_T *out_freemodeviewAct,
                 const boolean_T *out_isCpcShow, const ESVSViewMode
                 *in_HUSVSMode_prev, const EScreenID *in_HUViewReq_prev);
  void SuperState(const boolean_T *out_manualChangeAvail, const boolean_T
                  *out_isViewChangeAvl, const boolean_T *out_freemodeviewAct,
                  const boolean_T *out_isCpcShow, const ESVSViewMode
                  *in_HUSVSMode_prev, const EScreenID *in_HUViewReq_prev);
  void Steering_Activated(void);
  void Gear_Activate_SVS(void);
  void NarrowLane_Activated(void);
  void Sonar_Activated(void);
  void Normal_activate(const ESVSScreenType *UnitDelay9, const ESystemStr
                       *in_SystemStr_prev, const ECalibStatus
                       *in_calibStatus_prev, const uint8_T *in_calibActive_prev,
                       const boolean_T *in_closeButtonPressed_prev, const
                       boolean_T *in_competeQuit_prev);
  void GearNotR_Activated_Compete_Requ(void);
  void GearR_Activated_Compete_Request(void);
  void Manually_Activated_Compete_Requ(void);
  void NarrowLane_Activated_Compete_Re(void);
  void Steering_Activated_Compete_Requ(void);
  void Acivate_Through_Compete(void);
  void ActiveState(const ESVSScreenType *UnitDelay9, const ESystemStr
                   *in_SystemStr_prev, const ECalibStatus *in_calibStatus_prev,
                   const uint8_T *in_calibActive_prev, const boolean_T
                   *in_closeButtonPressed_prev, const boolean_T
                   *in_competeQuit_prev);
  void enter_internal_c1_ViewModexView(void);
  void enter_internal_SVSScreenType_no(void);
  void enter_internal_SVSScreenType_Sh(void);
  boolean_T checkCoorInResponseArea(uint32_T f_CoorX, uint32_T f_CoorY, uint16_T
    f_HmiLayout_iconCenter_x, uint16_T f_HmiLayout_iconCenter_y, uint16_T
    f_HmiLayout_responseArea_x, uint16_T f_HmiLayout_responseArea_y,
    ETouchStatus f_TouchSt);

  // Real-Time Model
  RT_MODEL rtM;
};

//-
//  The generated code includes comments that allow you to trace directly
//  back to the appropriate location in the model.  The basic format
//  is <system>/block_name, where system is the system number (uniquely
//  assigned by Simulink) and block_name is the name of the block.
//
//  Use the MATLAB hilite_system command to trace the generated code back
//  to the model.  For example,
//
//  hilite_system('<S3>')    - opens system 3
//  hilite_system('<S3>/Kp') - opens and selects block Kp which resides in S3
//
//  Here is the system hierarchy for this model
//
//  '<Root>' : 'ViewModexViewStateMachine_R2015b'
//  '<S1>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1'
//  '<S2>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/CpcHuSwitch'
//  '<S3>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/CpcViewHandling'
//  '<S4>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/DegradationHandling'
//  '<S5>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/FreeModeHandling'
//  '<S6>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/LastViewHandling'
//  '<S7>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/MainSM_Lev2'
//  '<S8>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/ManualChangeAvailHandling'
//  '<S9>'   : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/ObstacleAndSteeringrHandling'
//  '<S10>'  : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/PasWarnToneHandling'
//  '<S11>'  : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/screenTypeHandling'
//  '<S12>'  : 'ViewModexViewStateMachine_R2015b/MainSM_Lev1/voiceDockFeedbackHandling'

#endif                        // RTW_HEADER_ViewModexViewStateMachine_R2015b_h_

//
// File trailer for generated code.
//
// [EOF]
//
