//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  InfoPanels.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_INFOPANELS_H
#define CC_ASSETS_INFOPANELS_H

#include <osg/Camera>
#include <osg/Geode>
#include <osg/Vec2i>

#include "pc/svs/core/inc/View.h"


namespace cc
{
namespace assets
{
namespace infopanel
{


//======================================================
// InfoPanel
//------------------------------------------------------
/// Fake head unit overlays.
/// Deprecated.
/// <AUTHOR>
//======================================================
class InfoPanel : public pc::core::View
{
public:

  InfoPanel(
    const std::string& f_name,
    const pc::core::Viewport& f_viewport,
    const std::string& f_bitmap,
    float f_alpha = 1.0f);

protected:

  virtual ~InfoPanel();

private:

  osg::Geode* createInfoPanelGroup(const std::string &f_bitmap, float f_alpha);

};


} // namespace infopanel
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_INFOPANELS_H