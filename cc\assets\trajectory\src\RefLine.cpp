//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/RefLine.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "vfc/core/vfc_types.hpp"
/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

RefLine::RefLine(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  vfc::uint32_t f_numOfVerts)
  : GeneralTrajectoryLine{
      f_framework,
      assets::trajectory::commontypes::Middle_enm,
      2u,
      f_height,
      f_trajParams,
      false}
  , mc_numOfVerts{f_numOfVerts}
{
  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804  // PRQA S 3803  

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray()); // PRQA S 3076
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);
}


RefLine::~RefLine() = default;


void RefLine::generateVertexData()
{
  generateVertexData_withoutTexture();
}


void RefLine::generateVertexData_withoutTexture()
{
  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  constexpr vfc::float32_t lc_halfWidth = 0.0125f;

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
    m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Radius
                           + lc_halfWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Radius
                           + lc_halfWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

    m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
    m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
  }
  else
  {
    m_frameLateralOffsets[0u] = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.y()
                                 + lc_halfWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
    m_frameLateralOffsets[1u] = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.y()
                                 + lc_halfWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

    m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
    m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
  }

  m_frame.setBumperLineAngle(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
  m_frame.setBumperLinePos  (0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());
  const osg::Vec4f l_color = osg::Vec4f(0.0f, 0.75f, 1.0f, 0.9f);
  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint; // PRQA S 4102


  const vfc::float32_t l_startAngle = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle;
  const vfc::float32_t l_startPos   = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x();

  const vfc::float32_t l_endAngle =
      m_frame.getBumperLineAngle(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * (m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);
  const vfc::float32_t l_endPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.Length;

  l_controlPoint.Angle = l_startAngle;
  l_controlPoint.LongitudinalPos = l_startPos;
  l_controlPoint.Color = l_color;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  l_controlPoint.Color = l_color;
  m_frame.addControlPoint(1u, l_controlPoint);

  m_frame.setFadeIn(0u, false);
  m_frame.setFadeIn(1u, false);


  l_controlPoint.Angle = l_endAngle;
  l_controlPoint.LongitudinalPos = l_endPos;
  l_controlPoint.Color = l_color;
  m_frame.addControlPoint(0u, l_controlPoint);
  l_controlPoint.Color = l_color;
  m_frame.addControlPoint(1u, l_controlPoint);

  const vfc::float32_t l_fadeOutStartAngle =
      m_frame.getBumperLineAngle(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * 0.5f * m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius;

  const vfc::float32_t l_fadeOutStartPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * 0.5f * m_trajParams.Length;

  const vfc::float32_t l_fadeOutEndAngle =
      m_frame.getBumperLineAngle(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius;

  const vfc::float32_t l_fadeOutEndPos =
      m_frame.getBumperLinePos(0u)
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        * m_trajParams.Length;

  m_frame.setFadeOutStartAngle(0u, l_fadeOutStartAngle);
  m_frame.setFadeOutStartAngle(1u, l_fadeOutStartAngle);

  m_frame.setFadeOutStartPos(0u, l_fadeOutStartPos);
  m_frame.setFadeOutStartPos(1u, l_fadeOutStartPos);

  m_frame.setFadeOutEndAngle(0u, l_fadeOutEndAngle);
  m_frame.setFadeOutEndAngle(1u, l_fadeOutEndAngle);

  m_frame.setFadeOutEndPos(0u, l_fadeOutEndPos);
  m_frame.setFadeOutEndPos(1u, l_fadeOutEndPos);

  // *** 2. Create vertices (and colors) ***
  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray()); // PRQA S 3076
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray()); // PRQA S 3076
  l_colors->clear();

  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  m_frame.generateVertices(  // PRQA S 3803
      0u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);

  m_frame.generateVertices(  // PRQA S 3803
      1u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);


  // *** 3. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u)); // PRQA S 3076
  l_indices->clear();
  m_frame.generateIndices(0u, 0u, 1u, 0u, mc_numOfVerts, l_indices);

  l_vertices->dirty();
  l_colors->dirty();
  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


} // namespace trajectory
} // namespace assets
} // namespace cc
