//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityDataHandler.cpp
/// @brief
//=============================================================================

#include "cc/assets/parkingspots/inc/ParkingSpotUtil.h"
#include "cc/assets/virtualreality/inc/VirtualRealityDataHandler.h"
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"
#include "pc/svs/util/math/inc/FloatComp.h"


#define DATA_REFIEN_OVERLAP 0

namespace cc
{
namespace assets
{
namespace virtualreality
{

pc::util::coding::Item<VirtualRealityDataHandlerSettings> g_dataHandlerSetting("VirtualDataHandler");

VirtualRealityDataHandler::VirtualRealityDataHandler(pc::core::Framework* f_framework)
: m_framework{f_framework}
{
}

VirtualRealityDataHandler::~VirtualRealityDataHandler() = default;



void VirtualRealityDataHandler::updateData()
{
  updateDataSlot();
  updateDataPedestrian();
}

void VirtualRealityDataHandler::updateDataSlot()
{
  cc::core::CustomFramework* const l_customFramework = m_framework->asCustomFramework();

  cc::target::common::EPARKStatusR2L                l_curParkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::StrippedParkhmiTargetPosition l_curFinalEndPosition = {0, 0, 0};

  if(l_customFramework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = l_customFramework->m_parkHmiParkingStatusReceiver.getData();
    l_curParkStatus = l_parkStatus->m_Data;
  }

  if (l_customFramework->m_parkFinalEndPositionReceiver.hasData())
  {
    const cc::daddy::ParkFinalEndPositionDaddy_t* const l_finalEndPosition = l_customFramework->m_parkFinalEndPositionReceiver.getData();
    l_curFinalEndPosition = l_finalEndPosition->m_Data;
  }

  if (l_customFramework->m_parkSlotReceiver.hasData())
  {
    cc::daddy::ParkSlotDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkSlotRefinedDaddy_SenderPort.reserve();

    const cc::daddy::ParkSlotDaddy_t* const l_parkSlot = l_customFramework->m_parkSlotReceiver.getData();

//    constexpr bool l_leftBit  = false;
//    constexpr bool l_rightBit = false;

//    constexpr Position_st l_currPosition     = {0.0f, 0.0f, 0.0f, 0.0f};
//    constexpr Position_st l_preRightPosition = {0.0f, 0.0f, 0.0f, 0.0f};
//    constexpr Position_st l_preLeftPosition  = {0.0f, 0.0f, 0.0f, 0.0f};

    for (vfc::uint8_t i = 0u; i < cc::target::common::g_parkSlotCommandQuantity; i++) // PRQA S 4687
    {
      l_container.m_Data[i].m_parkSlotId              = l_parkSlot->m_Data[i].m_parkSlotId             ;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotAvailableStatus = l_parkSlot->m_Data[i].m_parkSlotAvailableStatus;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotOrientationType = l_parkSlot->m_Data[i].m_parkSlotOrientationType;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotEntryType       = l_parkSlot->m_Data[i].m_parkSlotEntryType      ;  // PRQA S 3013

      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_x   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_x;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_y   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_y;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_phi = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_phi;  // PRQA S 3013

      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner1.m_x   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotCorner1.m_x;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner1.m_y   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotCorner1.m_x;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner1.m_phi = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotCorner1.m_phi;  // PRQA S 3013

      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner2.m_x   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotCorner2.m_x;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner2.m_y   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotCorner2.m_y;  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner2.m_phi = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotCorner2.m_phi;  // PRQA S 3013

#if DATA_REFIEN_OVERLAP  // overlap handling
      l_currPosition.m_x   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_x;
      l_currPosition.m_y   = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_y;
      l_currPosition.m_phi = l_parkSlot->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_phi;
      cc::target::common::EParkSlotOrientationType l_orientationType    = l_parkSlot->m_Data[i].m_parkSlotOrientationType;

      avoidBothSideSlotOverlapUtil(l_orientationType, l_currPosition, l_preRightPosition, l_preLeftPosition, l_leftBit, l_rightBit );

      // update data in daddy
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_x = l_currPosition.m_x;
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_y = l_currPosition.m_y;
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_phi = l_currPosition.m_phi;
#endif

      // set one slot from occupied to selected if this slot position is the same as target position during guidance
      const cc::target::common::ParkSlotPoint_st l_curSlot = l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter;
      if ((cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curParkStatus) || (cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_curParkStatus) )
      {
        if ( (std::abs(l_curSlot.m_x - l_curFinalEndPosition.m_x) < 1024)  // 1024 is 1 meter, could be tuned
          && (std::abs(l_curSlot.m_y - l_curFinalEndPosition.m_y) < 1024)
          && (cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_OCCUPIED == l_container.m_Data[i].m_parkSlotAvailableStatus))
        {
          l_container.m_Data[i].m_parkSlotAvailableStatus = cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_SELECTED;
        }
      }

    }
    cc::daddy::CustomDaddyPorts::sm_ParkSlotRefinedDaddy_SenderPort.deliver();
  }
}

void VirtualRealityDataHandler::updateDataPedestrian()
{

  cc::core::CustomFramework* const l_customFramework = m_framework->asCustomFramework();
  if (l_customFramework->m_fusionObject_ReceiverPort.hasData())
  {
    const cc::daddy::FusionObjectDaddy_t* const l_fusBoxObjects = l_customFramework->m_fusionObject_ReceiverPort.getData();
    cc::daddy::PedestrianDaddy_t& l_pedestrianContainer = cc::daddy::CustomDaddyPorts::sm_pedestrianObj_SenderPort.reserve();
    //Go through pedestrian object info container and set all display flag "m_isShow" into false
    for(vfc::uint8_t i = 0u; i < g_pedestrianDisplayQuantity; i++) // PRQA S 4687
    {
      l_pedestrianContainer.m_Data[i].m_isShow = false;
    }
    vfc::uint8_t l_pedestrianCount =  0u;

    //Go through received fusion objects and pick pedestrian objects
    for(vfc::uint8_t i = 0u; i < g_fusionObjectQuantity; i++) // PRQA S 4687
    {
      //We use g_pedestrianDisplayQuantity to control the max number of displayed pedestrian.
      if (!(l_pedestrianCount < g_pedestrianDisplayQuantity))
      {
        break;
      }
      // Step 1 filter: pick pedestrian object from all fusion objects
      //
      // Object has high probability of existence
      const bool l_isObjConfident_b = isGreater(l_fusBoxObjects->m_Data.m_fusionObject[i].m_confidenceObject.value(), g_dataHandlerSetting->m_objectConfidenceThreshold);//!< True if object existence probability is over the threshold

      // Object has high probability to be a pedestrian
      const bool l_isPedConfident_b = isGreater(l_fusBoxObjects->m_Data.m_fusionObject[i].m_confidencePedestrian.value(), g_dataHandlerSetting->m_pedestrianConfidenceThreshold);//!< True if object is recognized as a valid pedestrian

      // The variances stand for confidence to trust this position and velocity's value, the smaller the value, the higher the reliability
      const bool l_isObjVarianceOK_b =
          (  isLess(l_fusBoxObjects->m_Data.m_fusionObject[i].m_positionCovariance(0, 0).value(), g_dataHandlerSetting->m_objectPositionCovXThreshold)//!< True if the variances of the object's position and velocity are below the threshold
          && isLess(l_fusBoxObjects->m_Data.m_fusionObject[i].m_positionCovariance(1, 1).value(), g_dataHandlerSetting->m_objectPositionCovYThreshold)
          && isLess(l_fusBoxObjects->m_Data.m_fusionObject[i].m_velocityCovariance(0, 0).value(), g_dataHandlerSetting->m_objectVelocityCovXThreshold)
          && isLess(l_fusBoxObjects->m_Data.m_fusionObject[i].m_velocityCovariance(1, 1).value(), g_dataHandlerSetting->m_objectVelocityCovYThreshold));

      const bool l_isPedValid_b = l_isObjConfident_b && l_isPedConfident_b && l_isObjVarianceOK_b;

      //Step 2 assign: if the object is a pedestrian, we will assign the correspond value to the pedestrian struct
      if (true == l_isPedValid_b)
      {
        l_pedestrianContainer.m_Data[l_pedestrianCount].m_isShow = true;
        l_pedestrianContainer.m_Data[l_pedestrianCount].m_objectID = l_fusBoxObjects->m_Data.m_fusionObject[i].m_objectID;
        l_pedestrianContainer.m_Data[l_pedestrianCount].m_pedesPosition.m_x = l_fusBoxObjects->m_Data.m_fusionObject[i].m_position.m_x;
        l_pedestrianContainer.m_Data[l_pedestrianCount].m_pedesPosition.m_y = l_fusBoxObjects->m_Data.m_fusionObject[i].m_position.m_y;
        if (0 == l_fusBoxObjects->m_Data.m_fusionObject[i].m_velocity.m_x_v.value()) // PRQA S 3270
        {
          l_pedestrianContainer.m_Data[l_pedestrianCount].m_pedesPosition.m_phi = vfc::CSI::si_radian_f32_t(0.0F);
        }
        else{
          l_pedestrianContainer.m_Data[l_pedestrianCount].m_pedesPosition.m_phi = vfc::CSI::si_radian_f32_t(std::atan(l_fusBoxObjects->m_Data.m_fusionObject[i].m_velocity.m_y_v.value() / l_fusBoxObjects->m_Data.m_fusionObject[i].m_velocity.m_x_v.value()));
        }

        if (l_customFramework->m_sitOcp_ReceiverPort.hasData())
        {
        const cc::daddy::SitOcpDaddy_t* const l_sitOcpObjects = l_customFramework->m_sitOcp_ReceiverPort.getData();
        if (l_pedestrianContainer.m_Data[l_pedestrianCount].m_objectID == l_sitOcpObjects->m_Data.m_objectID)
        {
          l_pedestrianContainer.m_Data[l_pedestrianCount].m_criticalObj = true;
        }
        else
        {
          l_pedestrianContainer.m_Data[l_pedestrianCount].m_criticalObj = false;
        }

        }

        l_pedestrianCount++;

      }
    }
    cc::daddy::CustomDaddyPorts::sm_pedestrianObj_SenderPort.deliver();
  }
}

void VirtualRealityDataHandler::avoidBothSideSlotOverlapUtil(cc::target::common::EParkSlotOrientationType f_orientationType, Position_st &f_currPosition, Position_st &f_preRightPosition, Position_st &f_preLeftPosition, bool &f_leftBit, bool &f_rightBit )
{
  //Leftside slots overlap
  if(f_currPosition.m_y >= 0u)
  {
    if(false != f_leftBit)
    {
      avoidOverlapAlgorithm(f_orientationType, f_currPosition, f_preLeftPosition);
    }
    f_leftBit = true;
    f_preLeftPosition.m_x = f_currPosition.m_x;
  }
  else
  {
    if(false != f_rightBit)
    {
      avoidOverlapAlgorithm(f_orientationType, f_currPosition, f_preRightPosition);
    }
    f_rightBit = true;
    f_preRightPosition.m_x = f_currPosition.m_x;
  }
}

void VirtualRealityDataHandler::avoidOverlapAlgorithm(cc::target::common::EParkSlotOrientationType f_orientationType, Position_st &f_currPosition, Position_st &f_prePosition) // PRQA S 4283
{
  // 5.5m for PARALLEL slot, 2.6m for CROSS slot and 6.3m for DIAGONAL slot  0.2 for space buffer between two slots
  if(cc::target::common::EParkSlotOrientationType::PARKSLOTTYPE_PARALLEL == f_orientationType)
  {
    if ( 5500.0f > f_prePosition.m_x-f_currPosition.m_x)
    {
      f_currPosition.m_x = f_prePosition.m_x - 5500.0f-200.0f;
    }
  }
  else if(cc::target::common::EParkSlotOrientationType::PARKSLOTTYPE_CROSS == f_orientationType)
  {
    if ( 2600.0f > f_prePosition.m_x-f_currPosition.m_x)
    {
      f_currPosition.m_x = f_prePosition.m_x - 2600.0f-200.0f;
    }
  }
  else if(cc::target::common::EParkSlotOrientationType::PARKSLOTTYPE_DIAGONAL == f_orientationType)
  {
    if ( 6300.0f > f_prePosition.m_x-f_currPosition.m_x)
    {
      f_currPosition.m_x = f_prePosition.m_x - 6300.0f-200.0f;
    }
  }
  else
  {
    //do nothing
  }

}

void VirtualRealityDataHandler::getSelectedParkingSpot(const osg::Matrixf& f_MVPmatrix)
{
  cc::core::CustomFramework* const l_customFramework = m_framework->asCustomFramework();

  if (l_customFramework->m_parkSlotRefinedReceiver.hasData())
  {
    const cc::daddy::ParkSlotDaddy_t* const l_parkSlots = l_customFramework->m_parkSlotRefinedReceiver.getData();
    if (l_customFramework->m_HUTouchTypeReceiver.hasData() && l_customFramework->m_hmiDataReceiver.hasData())
    {
      const cc::daddy::HUtouchEvenTypeDaddy_t* const l_touchEventData = l_customFramework->m_HUTouchTypeReceiver.getData();
      const cc::daddy::HmiData_Daddy* const l_hmiData = l_customFramework->m_hmiDataReceiver.getData();

      cc::daddy::ParkSlotSelectedStDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkSlotSelectedStDaddy_SenderPort.reserve();
      l_container.m_Data = 0u;

      if (1u == l_touchEventData->m_Data || 3u == l_touchEventData->m_Data) //since when do click or move, we will all think that somebody is clicking somewhere
      {

        for (vfc::uint8_t i = 0u; i < cc::target::common::g_parkSlotQuantity; i++) // PRQA S 4687
        {
          const cc::target::common::ParkSlot_st& l_parkSlot = l_parkSlots->m_Data[i];
          //Judge that the spot should be selectable and then we will check it is selected or not
          if (cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_SELECTABLE == l_parkSlot.m_parkSlotAvailableStatus)
          {
            if (parkingSpotIsSelected(l_parkSlot, f_MVPmatrix, l_hmiData->m_Data.m_huX, l_hmiData->m_Data.m_huY))
            {
                // l_container.m_Data = static_cast<EPARkSlot>(i);
                l_container.m_Data = l_parkSlot.m_parkSlotId;
                break;
            }
          }
        }
      }
      cc::daddy::CustomDaddyPorts::sm_ParkSlotSelectedStDaddy_SenderPort.deliver();
    }
  }

}

} // namespace virtualreality
} // namespace assets
} // namespace cc
