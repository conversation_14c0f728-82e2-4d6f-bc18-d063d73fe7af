//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Vehicle2dIcon.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/Vehicle2dIcon.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class Vehicle2dIconType : vfc::uint32_t
{
  DOOR_OPEN_PLAN_VIEW,
  DOOR_OPEN_PARKING_VIEW
};

static pc::util::coding::Item<Vehicle2dIconSettings> g_vehicle2dIconSettings("Vehicle2dIconSettings");

//!
//! @brief Construct a new Vehicle2dIcon Manager:: Vehicle2dIcon Manager object
//!
//! @param f_config
//!
Vehicle2dIconManager::Vehicle2dIconManager()
  : m_lastConfigUpdate{~0u}
  , m_Vehicle2dIcons{}
  , m_mat_b{false}
  , m_theme{cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI}
  , m_parkActive{false}
{
}



Vehicle2dIconManager::~Vehicle2dIconManager() = default;



static osg::Vec2f calculateIconSize2dIcon(bool f_parkActive)
{
  vfc::float32_t l_viewPortWidthSizePixel = 0.f;
  vfc::float32_t l_viewPortWidthSizeMeters = 0.f;
  if (!f_parkActive) // OFF/SEARCHING
  {
    l_viewPortWidthSizePixel  = static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.x());
    l_viewPortWidthSizeMeters = cc::core::g_planView->m_widthMeters;
  }
  else
  {
    l_viewPortWidthSizePixel  = static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_size.x());
    l_viewPortWidthSizeMeters = cc::core::g_planView->m_widthMetersParkingHori;
  }
  // float l_iconWidth = 428.f * 1.94f / (7.0f - 0.8f);
  const vfc::float32_t l_iconWidth = l_viewPortWidthSizePixel * (pc::vehicle::g_mechanicalData->m_width) / /* 428.f * 1.94f / (7.0f - 0.8f) */
                      (l_viewPortWidthSizeMeters - cc::core::g_planView->m_widthMetersVeh2dDiff);
  osg::Vec2f l_iconSize;
  l_iconSize.x() = std::floor(l_iconWidth /  g_vehicle2dIconSettings->m_size.x() * g_vehicle2dIconSettings->m_size.y()); // width / 146 * 400
  l_iconSize.y() = l_iconSize.x();
  return l_iconSize;
}

void Vehicle2dIconManager::setVehicleIconSize(core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return;
  }
  if(f_framework->m_parkHmiParkingStatusReceiver.isConnected())
  {
    if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
    {
      cc::target::common::EPARKStatusR2L l_curparkStatus = cc::target::common::EPARKStatusR2L::PARK_Off;
      const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
      l_curparkStatus = l_parkStatus->m_Data;
      bool l_curParkActive = false;
      switch(l_curparkStatus)
      {
        case cc::target::common::EPARKStatusR2L::PARK_Searching:
        case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
        case cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend:
        case cc::target::common::EPARKStatusR2L::PARK_Terminated:
        case cc::target::common::EPARKStatusR2L::PARK_Completed:
        case cc::target::common::EPARKStatusR2L::PARK_Failure:
        case cc::target::common::EPARKStatusR2L::PARK_AssistStandby:
        {
          l_curParkActive = true;
          break;
        }
        case cc::target::common::EPARKStatusR2L::PARK_Off:
        case cc::target::common::EPARKStatusR2L::PARK_Standby:
        default:
        {
          l_curParkActive = false;
          break;
        }
      }
      if (m_parkActive != l_curParkActive)
      {
        const osg::Vec2f l_iconSize = calculateIconSize2dIcon(l_curParkActive);
        IconList l_iconlist = m_Vehicle2dIcons.getIconList();
        for (IconList::const_iterator l_itr = l_iconlist.begin(); l_itr != l_iconlist.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
        {
          pc::assets::Icon* const l_icon = l_itr->get();
          l_icon->setSize(l_iconSize, pc::assets::Icon::UnitType::Pixel);
        }
        // XLOG_DEBUG_OS(g_AppContext) << "Vehicle 2D Overlay size updated" << XLOG_ENDL;
        m_parkActive = l_curParkActive;
      }
    }
  }
}

void Vehicle2dIconManager::init(pc::assets::ImageOverlays* f_imageOverlays, cc::target::common::EThemeTypeHU f_theme)
{
  // ! init Vehicle2dIcon icons
  m_Vehicle2dIcons.clear(f_imageOverlays);
  const osg::Vec2f l_iconSize = calculateIconSize2dIcon(m_parkActive);

  // ! HORIZONTAL
  if (f_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI || f_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE)
  {
    m_Vehicle2dIcons.addIcon(f_imageOverlays, createIconTopLeft(
      g_vehicle2dIconSettings->m_texturePathDoorOpen,
      g_vehicle2dIconSettings->m_planViewPos,
      l_iconSize));

    // m_Vehicle2dIcons.addIcon(f_imageOverlays, createIconTopLeft(
    //   g_vehicle2dIconSettings->m_texturePathDoorOpen,
    //   g_vehicle2dIconSettings->m_parkingPlanViewPos,
    //   l_iconSizeParkingView));
  }

}


void Vehicle2dIconManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)
{
  if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
  {
      return;
  }
  // ! check if config has changed
  if (g_vehicle2dIconSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays, m_theme);
    m_lastConfigUpdate = g_vehicle2dIconSettings->getModifiedCount();
  }
//  pc::assets::IconGroup* const l_Vehicle2dIcons = &m_Vehicle2dIcons;
  EGear l_gear                = pc::daddy::GEAR_P;
  bool l_showTransparent      = false;
//  constexpr bool l_frontLeftDoorOpen    = false;
//  constexpr bool l_frontRightDoorOpen   = false;
//  constexpr bool l_hoodOpen             = false;
//  constexpr bool l_rearLeftDoorOpen     = false;
//  constexpr bool l_rearRightDoorOpen    = false;
//  constexpr bool l_trunkOpen            = false;
//  constexpr bool l_vehicleBodyOpen      = false;
  bool l_allDoorsClosed       = true;
  m_Vehicle2dIcons.setAllEnabled(false);

  setVehicleIconPosition(f_framework);
  setVehicleIconSize(f_framework);

  const pc::daddy::GearDaddy* const l_gearStateDaddy = f_framework->m_gearReceiver.getData();
  if (l_gearStateDaddy != nullptr)
  {
    l_gear = static_cast<vfc::uint8_t>(l_gearStateDaddy->m_Data);
  }

  if(f_framework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = f_framework->m_SVSRotateStatusDaddy_Receiver.getData();
    const cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899
    if (l_curThemeType != m_theme)
    {
      m_theme = l_curThemeType;
      init(f_imageOverlays, m_theme);
    }
  }

  //! DOORS
  // check actual door status signal
//   if (f_framework->m_doorStateReceiver.isConnected())
//   {
//     const pc::daddy::DoorStateDaddy * l_pDoorStateDaddy =  f_framework->m_doorStateReceiver.getData();
//     if (0 != l_pDoorStateDaddy)
//     {
//       l_frontRightDoorOpen = ((pc::daddy::CARDOORSTATE_CLOSED != l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) ? true : false);
//       l_frontLeftDoorOpen  = ((pc::daddy::CARDOORSTATE_CLOSED != l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT])  ? true : false);
//       l_rearRightDoorOpen  = ((pc::daddy::CARDOORSTATE_CLOSED != l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT])  ? true : false);
//       l_rearLeftDoorOpen   = ((pc::daddy::CARDOORSTATE_CLOSED != l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT])   ? true : false);

//       l_allDoorsClosed = (!l_frontRightDoorOpen && !l_frontLeftDoorOpen  && !l_rearRightDoorOpen  && !l_rearLeftDoorOpen);
//     }
//   }
  if (f_framework->m_doorAnimationStateReceiver.isConnected())
  {
    const pc::daddy::DoorAnimationStateDaddy* const l_pData = f_framework->m_doorAnimationStateReceiver.getData();

    if (nullptr!=l_pData)
    {
        if (  l_pData->m_Data.AnimationOngoingOrOpenFL          ||
              l_pData->m_Data.AnimationOngoingOrOpenFR          ||
              l_pData->m_Data.AnimationOngoingOrOpenRL          ||
              l_pData->m_Data.AnimationOngoingOrOpenRR )
        {
          l_allDoorsClosed = false;
        }
    }
  }
  else
  {
      XLOG_ERROR(g_AppContext, "Door state is not available !");
  }

  //! MIRRORS
  // if(f_framework->m_mirrorStateReceiver.isConnected())
  // {
  //     const pc::daddy::MirrorStateDaddy * l_pMirrosStateDaddy = f_framework->m_mirrorStateReceiver.getData();
  //     if(0 != l_pMirrosStateDaddy)
  //     {
  //         if(pc::daddy::MIRRORSTATE_NOT_FLAPPED != l_pMirrosStateDaddy->m_Data[pc::daddy::SIDEMIRROR_LEFT])
  //         {
  //           // TODO wait for update
  //         }
  //         if(pc::daddy::MIRRORSTATE_NOT_FLAPPED != l_pMirrosStateDaddy->m_Data[pc::daddy::SIDEMIRROR_RIGHT])
  //         {
  //           // TODO wait for update
  //         }
  //     }
  // }
  // else
  // {
  //     XLOG_ERROR_OS(g_AppContext) << "Mirror state is not available !"<< XLOG_ENDL;
  // }

  if (f_framework->m_VehTransparenceStsFromSM_Receiver.hasData())
  {
    const cc::daddy::SVSVehTransStsDaddy_t*   const l_vehTransStatus = f_framework->m_VehTransparenceStsFromSM_Receiver.getData();

    if (nullptr!=l_vehTransStatus)
    {
      if ((1u == l_vehTransStatus->m_Data))
      {
        l_showTransparent = true;
      }
      else
      {
        l_showTransparent = false;
      }
    }
  }

  if(l_allDoorsClosed && (l_gear == pc::daddy::GEAR_P) && (l_gearStateDaddy != nullptr) && (!m_parkActive))
  {
    m_Vehicle2dIcons.setAllEnabled(true);
  }
}


void Vehicle2dIconManager::setVehicleIconPosition(core::CustomFramework* f_framework) // PRQA S 4211
{
  if (f_framework == nullptr)
  {
      return;
  }
  if (f_framework->m_displayedView_ReceiverPort.hasData())
  {
    const cc::daddy::SVSDisplayedViewDaddy_t*   const l_displayid = f_framework->m_displayedView_ReceiverPort.getData();
    if (nullptr!=l_displayid)
    {
      IconList l_iconlist = m_Vehicle2dIcons.getIconList();
      if ((EScreenID_HORI_PARKING == l_displayid->m_Data))
      {
        for (IconList::const_iterator l_itr = l_iconlist.begin(); l_itr != l_iconlist.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
        {
          pc::assets::Icon* const l_icon = l_itr->get();
          l_icon->setPosition(g_vehicle2dIconSettings->m_parkingPlanViewPos, pc::assets::Icon::UnitType::Pixel);
        }
      }
      else
      {
        for (IconList::const_iterator l_itr = l_iconlist.begin(); l_itr != l_iconlist.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
        {
          pc::assets::Icon* const l_icon = l_itr->get();
          l_icon->setPosition(g_vehicle2dIconSettings->m_planViewPos, pc::assets::Icon::UnitType::Pixel);
        }
      }
    }
  }
}

//!
//! @brief Construct a new Vehicle2dIcon:: Vehicle2dIcon object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
Vehicle2dIcon::Vehicle2dIcon(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_manager{}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

  //! render order
  //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
  CustomSetRenderOrder(500u, true);

}


Vehicle2dIcon::~Vehicle2dIcon() = default;




void Vehicle2dIcon::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc // PRQA S 1041
