//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_COMMONTYPES_H
#define CC_ASSETS_TRAJECTORY_COMMONTYPES_H

#include <osg/ref_ptr>
#include <osg/Geometry>
#include <vfc/core/vfc_siunits_convenienttypes.hpp> // PRQA S 0034

namespace cc
{
namespace assets
{
namespace trajectory
{

enum RenderOrder : int
{
  RENDERBIN_ORDER_TRAJECTORY_COVER_PLATE          = 160,
  RENDERBIN_ORDER_TRAJECTORY_OUTERMOST_LINE       = 165,
  RENDERBIN_ORDER_TRAJECTORY_EXTRA_OUTERMOST_LINE = 166,
  RENDERBIN_ORDER_TRAJECTORY_WHEELTRACK           = 167,
  RENDERBIN_ORDER_TRAJECTORY_EXTRA_WHEELTRACK     = 169,
  RENDERBIN_ORDER_TRAJECTORY_ACTION_POINT         = 170,
  RENDERBIN_ORDER_TRAJECTORY_DL1                  = 172,
  RENDERBIN_ORDER_TRAJECTORY_REFLINE              = 173,
  RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_LINE   = 174,
  RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_CIRCLE = 171,
};



namespace commontypes
{


struct VertexData_st
{
  osg::ref_ptr<osg::Vec3Array> Vertices;
  osg::ref_ptr<osg::Vec3Array> Normals;
  osg::ref_ptr<osg::Vec4Array> Colors;
  osg::ref_ptr<osg::Vec2Array> TexCoords;
  osg::DrawElementsUShort*     Indices;
};

struct ControlPoint_st
{
  typedef float float_angle_radian_t;
  float_angle_radian_t Angle; // Angle measured from the Ackermann point. [rad] //TODO use vfc type
  float LongitudinalPos;      // Longitudinal (x) position value of the point. [m]
  osg::Vec4f   Color;         // RGBA value.
  unsigned int Index;         // Index in the vertex array.
};

enum Side_en
{
  Left_enm,
  Middle_enm,
  Right_enm
};

enum TurningDirection_en
{
  ToLeft_enm,
  ToRight_enm
};

enum VehicleMovementType_en
{
  Rotation_enm,
  Translation_enm
};


} // namespace commontypes
} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_COMMONTYPES_H
