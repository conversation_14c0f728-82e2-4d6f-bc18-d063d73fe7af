//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_SLIDER_H
#define CC_ASSETS_BUTTON_SLIDER_H

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/core/inc/Viewport.h"
#include <osg/Geode>
#include <osg/Texture2D>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace button
{

class SliderColorSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SliderColorSettings)
    {
        ADD_MEMBER(osg::Vec4f, foregroundColor);
        ADD_MEMBER(osg::Vec4f, backgroundColor);
        ADD_MEMBER(osg::Vec4f, digitalColor);
        ADD_MEMBER(osg::Vec4f, moveLineColor);
    }

    osg::Vec4f m_foregroundColor = {0.96f, 0.63f, 0.38f, 1.0f};
    osg::Vec4f m_backgroundColor = {0.88f, 0.88f, 0.89f, 1.0f};
    osg::Vec4f m_digitalColor    = {0.84f, 0.86f, 0.86f, 0.96f};
    osg::Vec4f m_moveLineColor   = {0.898f, 0.898f, 0.898f, 1.0f};
};

class SliderSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SliderSettings)
    {
        ADD_MEMBER(osg::Vec2f, size);
        ADD_MEMBER(osg::Vec2f, position);
        ADD_FLOAT_MEMBER(baseActivationRadius);
        ADD_FLOAT_MEMBER(speedFactor);
        ADD_FLOAT_MEMBER(deliverThreshold);
        ADD_MEMBER(SliderColorSettings, day);
        ADD_MEMBER(SliderColorSettings, night);
        ADD_MEMBER(osg::Vec3f, brightnessValuePos);
        ADD_FLOAT_MEMBER(brightnessValueSize);
        ADD_INT_MEMBER(maxBrightnessValue);
        ADD_STRING_MEMBER(fontType);
        ADD_INT_MEMBER(sliderTopValue);
        ADD_MEMBER(osg::Vec2f, moveLineSize);
        ADD_FLOAT_MEMBER(leftMoveLineOffset);
        ADD_FLOAT_MEMBER(rightMoveLineOffset);
    }

    osg::Vec2f          m_size                 = {200.0f, 30.0f};
    osg::Vec2f          m_position             = {500.0f, 500.0f};
    float               m_baseActivationRadius = 8.0f; // Initial activation radius
    float               m_speedFactor          = 0.7f; // Adjustment factor
    float               m_deliverThreshold     = 0.5f;
    SliderColorSettings m_day;
    SliderColorSettings m_night;
    osg::Vec3f          m_brightnessValuePos;
    float               m_brightnessValueSize;
    int                 m_maxBrightnessValue;
    std::string         m_fontType;
    int                 m_sliderTopValue = 49;
    osg::Vec2f          m_moveLineSize   = {2.0f, 12.0f};
    float               m_leftMoveLineOffset;
    float               m_rightMoveLineOffset;
};

extern pc::util::coding::Item<SliderSettings> g_defaultSliderSettings;

class SliderIcon : public osg::Geode
{
public:
    enum class Origin
    {
        TopLeft,
        BottomLeft
    };

    enum class Alignment
    {
        Left,
        Top,
        Center,
        Right,
        Bottom
    };

    enum class UnitType
    {
        Pixel,
        Percentage
    };

    enum class TouchStatus : vfc::uint8_t
    {
        TOUCH_INVALID = 0u,
        TOUCH_DOWN    = 1u,
        TOUCH_UP      = 2u,
        TOUCH_MOVE    = 3u
    };

public:
    SliderIcon(
        const SliderSettings* f_settings  = g_defaultSliderSettings.get(),
        pc::core::Framework*  f_framework = nullptr,
        bool                  f_enabled   = true);
    SliderIcon(const SliderIcon& f_other, const osg::CopyOp& f_copyOp);
    META_Node(cc::assets::button, SliderIcon);

    virtual void update(const pc::core::Viewport& f_viewport);

    bool getEnabled() const;
    void setEnabled(bool f_state);

    void setForegroundColor(osg::Vec4f f_color);
    void setBackgroundColor(osg::Vec4f f_color);
    void setMoveLinesColor(osg::Vec4f f_color);
    void updateColor(cc::target::common::EThemeTypeDayNight f_dayNightTheme);
    void getPosition(osg::Vec2f& f_position, UnitType& f_unitType) const;
    void setPosition(const osg::Vec2f& f_position, UnitType f_unitType);

    void getSize(osg::Vec2f& f_size) const;
    void setSize(const osg::Vec2f& f_size);

    Origin getOrigin() const;
    void   setOrigin(Origin f_origin);

    Alignment getAlignmentHorizontal() const;
    void      setAlignmentHorizontal(Alignment f_alignment);

    Alignment getAlignmentVertical() const;
    void      setAlignmentVertical(Alignment f_alignment);

    // void  setPercentage(float f_percentage);
    void  sendBrightnessValue(float f_percentage);
    float getPercentage() const
    {
        return m_percentage;
    }

    bool touchInsideResponseArea(int f_x, int f_y) const;

    TouchStatus getTouchStatus() const
    {
        return m_touchStatus;
    }
    void setTouchStatus(TouchStatus f_touchStatus)
    {
        m_touchStatus = f_touchStatus;
    }

    osg::Vec2i getTouchPosition() const
    {
        return m_touchPosition;
    }
    void setTouchPosition(osg::Vec2i f_touchPosition)
    {
        m_touchPosition = f_touchPosition;
    }

protected:
    osg::Geometry* createGeometry() const;

    osg::Geometry* getBackgroundGeometry();

    osg::Geometry* getForegroundGeometry();

    osg::Geometry* getMoveLeftLine();

    osg::Geometry* getMoveRightLine();

    void updateGeometry(osg::Geometry* f_geometry, const osg::Vec2f& f_origin, const osg::Vec2f& f_size) const;

    void updatePercentage();

    void updateRealPercentage();

    void updateValue(int f_sliderValue);

private:
    float                 m_percentage    = 0.0f;
    cc::target::common::EThemeTypeDayNight    m_dayNightTheme = cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT;
    UnitType              m_positionType  = UnitType::Pixel;
    pc::core::Viewport    m_viewport;
    Alignment             m_alignmentHorizontal = Alignment::Left;
    Alignment             m_alignmentVertical   = Alignment::Top;
    osg::Vec2f            m_position;
    osg::Vec2f            m_size;
    Origin                m_origin        = Origin::TopLeft;
    bool                  m_dirty         = true;
    bool                  m_enabled       = true;
    osg::Vec2f            m_topLeftCorner = {0.0f, 0.0f};
    TouchStatus           m_touchStatus   = TouchStatus::TOUCH_INVALID;
    osg::Vec2i            m_touchPosition = {0, 0};
    osg::Vec2i            m_touchCurrent  = {0, 0};
    vfc::int32_t          m_lastTouchX;
    pc::core::Framework*  m_framework;
    vfc::uint32_t         m_modifiedCount = ~0u;
    const SliderSettings* m_settings;
    bool                  m_isDragging;
};

class Slider : public osg::Group
{
public:
    Slider(pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);
    osg::Camera* getReferenceView() const
    {
        return m_referenceView.get();
    }

    void setReferenceView(osg::Camera* f_referenceView)
    {
        m_referenceView = f_referenceView;
    }

    void addSlider(SliderIcon* f_sliderIcon);

    void removeSlider(SliderIcon* f_sliderIcon);

protected:
    virtual ~Slider() = default;

    osg::ref_ptr<osg::Camera> m_referenceView;

private:
    osg::ref_ptr<osg::Camera> m_hudCamera;
    pc::core::Framework*      m_framework;
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_SLIDER_H
