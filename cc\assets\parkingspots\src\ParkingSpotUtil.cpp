//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ParkingSpotUtil.cpp
/// @brief This file defined useful functions for ParkingSpotManager.cpp
//=============================================================================

#include "cc/assets/parkingspots/inc/ParkingSpotUtil.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "vfc/core/vfc_types.hpp"
namespace cc
{
namespace assets
{
namespace parkingspots
{

void avoidOverlappedParkingSpots(ParkingSpot* f_parkingSpotCurr, const  ParkingSpot* f_parkingSpotPrev)
{
  //CurrentPos set to texture is base on Corner1
  osg::Vec2f l_currPos             = f_parkingSpotCurr->getPosition();
  const vfc::float32_t l_xCurCorner1              = f_parkingSpotCurr->getPosition().x();
  const vfc::float32_t l_xPrevCorner1             = f_parkingSpotPrev->getPosition().x();
  const vfc::float32_t l_thresholdOverlapDistance = f_parkingSpotCurr->getSize().x();

  if ( l_thresholdOverlapDistance + g_managerSettings->m_adjustmentValueOfOverlapDistance > l_xPrevCorner1-l_xCurCorner1)
  {
    if(f_parkingSpotCurr->getType() == static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL) && f_parkingSpotPrev->getType() == static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL))
    {
      l_currPos.x() = l_xPrevCorner1 - f_parkingSpotCurr->getSize().x()-0.05f;
    }
    else
    {
      l_currPos.x() = l_xPrevCorner1 - f_parkingSpotCurr->getSize().x()-0.2f;
    }
    f_parkingSpotCurr->setPosition(l_currPos);
  }
  f_parkingSpotCurr->dirty();
}

void updateParkingSpot(ParkingSpot* f_parkingSpot, cc::target::common::StrippedEAPAParkSpace f_data, const osg::Vec2f& f_spotSize, vfc::uint8_t f_side)
{

  // f_parkingSpot->setSpotID(f_data.m_uid);
  //set spot size
  osg::Vec2f l_spotSize;
  const vfc::float32_t l_parkingSpotAngle= f_data.m_APA_PrkgSlotSta_f32;
  switch (f_data.m_APA_PSType)
  {
    case cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL:
      {l_spotSize = f_spotSize;
      f_parkingSpot->setType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL));
      break;}
    case cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL:
      {l_spotSize = osg::Vec2f(g_managerSettings->m_diagonalSpotLength,g_managerSettings->m_diagonalSpotWidth);
      f_parkingSpot->setType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL));
      break;}
    case cc::target::common::EFAPAParkSlotType::APASLOT_CROSS:
      {if (isGreater(l_parkingSpotAngle, cc::assets::parkingspace::g_parkingSpaceSettings->m_parkingDiagSlotAngleLowerLimit)
        && isLess(l_parkingSpotAngle, cc::assets::parkingspace::g_parkingSpaceSettings->m_parkingDiagSlotAngleUpperLimit))
      {
        l_spotSize = osg::Vec2f(g_managerSettings->m_diagonalSpotLength,g_managerSettings->m_diagonalSpotWidth);
        f_parkingSpot->setType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL));
      }
      else
      {
        l_spotSize = osg::Vec2f(f_spotSize.y(), f_spotSize.x());
        f_parkingSpot->setType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS));
      }
      break;}
    default:
      {l_spotSize = f_spotSize;
      break;}
  }
  f_parkingSpot->setSize(l_spotSize);

  //set spot position
  osg::Vec2f l_position;
  const vfc::float32_t l_positionRatio = g_managerSettings->m_parkingSpotHmiWidth / g_managerSettings->m_realSpotWidth;
  const vfc::float32_t l_temp = cm2m(static_cast<vfc::float32_t>(f_data.m_APA_PSCorner2X_i16 )) * l_positionRatio - l_spotSize.x();
  if (LEFTSIDE == f_side)
  {
    f_parkingSpot->setSide(LEFTSIDE);
    l_position = osg::Vec2f(l_temp, g_managerSettings->m_parkOutParaSpotYPos);
  }
  else
  {
    f_parkingSpot->setSide(RIGHTSIDE);
    l_position = osg::Vec2f(l_temp, -(g_managerSettings->m_parkOutParaSpotYPos + l_spotSize.y()));
  }

  f_parkingSpot->setPosition(l_position);
  //f_parkingSpot->setType(f_data.m_APA_PSType);
  f_parkingSpot->setSpotState(static_cast<vfc::uint8_t>(f_data.m_APA_PrkgSlot));

  f_parkingSpot->dirty();
}

//! Returns a percentage value in the range of 0..100 which reflects
//! how much the area of a parking spot is visible on the screen.
vfc::uint32_t getParkingSpotCoverage(const ParkingSpot* f_parkingSpot, const osg::Matrixf& f_MVPmatrix)
{
  constexpr vfc::uint32_t lc_pointCount = 10u; // This defines the number of samples along a parking spot.
  std::array<osg::Vec4f, lc_pointCount> l_points;

/*  if (DIAGONAL == f_parkingSpot->getType())
  {
    // Do this once the way of the diagonal spot definition is clear.
  }
  else*/
  {
    l_points[0u]                 = osg::Vec4f(f_parkingSpot->getPosition().x() + f_parkingSpot->getSize().x(),
                                             f_parkingSpot->getPosition().y() + f_parkingSpot->getSize().y() * 0.5f, 0.0f, 1.0f);
    l_points[lc_pointCount - 1u] = osg::Vec4f(f_parkingSpot->getPosition().x(),
                                             f_parkingSpot->getPosition().y() + f_parkingSpot->getSize().y() * 0.5f, 0.0f, 1.0f);
  }

  const osg::Vec4f l_diff = l_points[lc_pointCount - 1u] - l_points[0u];
  for (vfc::uint32_t i = 1u; i <= (lc_pointCount - 2u); ++i)
  {
    const vfc::float32_t i_normalized = static_cast<vfc::float32_t>(i) / static_cast<vfc::float32_t>(lc_pointCount - 1);
    l_points[i] = l_points[0u] + (l_diff * i_normalized);
    l_points[i].z() = 0.0f;
    l_points[i].w() = 1.0f;
  }

  const pc::util::Box2D l_NDC = pc::util::Box2D(-1.0f, -1.0f, 1.0f, 1.0f);
  vfc::uint32_t l_inRangePointCount = 0u;
  for (vfc::uint32_t i = 0u; i < lc_pointCount; ++i)
  {
    l_points[i] = l_points[i] * f_MVPmatrix;
    l_points[i] /= l_points[i].w();

    if (l_NDC.inRange(osg::Vec2f(l_points[i].x(), l_points[i].y())))
    {
      ++l_inRangePointCount;
    }
  }

  return (100u * l_inRangePointCount) / lc_pointCount;
}

UISpotData getParkingSpotVertexandSize(const ParkingSpot* f_parkingSpot, const osg::Matrixf& f_MVPmatrix, vfc::uint8_t f_index)
{
  //!               /-----/ near_vertex
  //               /  .  /
  //              /     /
  // far_vertex  /-----/
  //
  osg::Vec4f l_near_vertex = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
  osg::Vec4f l_far_vertex  = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
  osg::Vec4f l_spot_center = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
  //osg::Vec4f l_spot_center_of_B_Area = osg::Vec4f(0.f, 0.f, 0.f, 0.f);
  UISpotData l_spotData;

  if( static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS) == f_parkingSpot->getType())
  {
    //Leftside ParkingSpot
    if (0 < f_parkingSpot->getPosition().y())
    {
      l_spot_center = osg::Vec4f(
        f_parkingSpot->getMiddle().x(),f_parkingSpot->getMiddle().y(),
        0.0f,
        1.0f);
      l_near_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() + g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        f_parkingSpot->getMiddle().y() - g_managerSettings->m_parkingSpotHmiLength/2.0f,
        0.0f,
        1.0f);
      l_far_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() - g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        f_parkingSpot->getMiddle().y() + g_managerSettings->m_parkingSpotHmiLength/2.0f,
        0.0f,
        1.0f);
    }
    //Rightside ParkingSpot
    else
    {
      l_spot_center = osg::Vec4f(
        f_parkingSpot->getMiddle().x(),
        f_parkingSpot->getMiddle().y(),
        0.0f,
        1.0f);
      l_near_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() + g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        f_parkingSpot->getMiddle().y() + g_managerSettings->m_parkingSpotHmiLength/2.0f,
        0.0f,
        1.0f);
      l_far_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() - g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        f_parkingSpot->getMiddle().y() - g_managerSettings->m_parkingSpotHmiLength/2.0f,
        0.0f,
        1.0f);
    }

    l_spotData.m_spotType = cc::target::common::EFAPAParkSlotType::APASLOT_CROSS;
  }
  else if (static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL) == f_parkingSpot->getType())
  {
    if ( 0 < f_parkingSpot->getPosition().y())
    {
      l_spot_center = osg::Vec4f(
        f_parkingSpot->getMiddle().x(),
        f_parkingSpot->getMiddle().y(),
        0.0f,
        1.0f);
      l_near_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() + g_managerSettings->m_parkingSpotHmiLength/2.0f,
        f_parkingSpot->getMiddle().y() - g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        0.0f,
        1.0f);
      l_far_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() - g_managerSettings->m_parkingSpotHmiLength/2.0f,
        f_parkingSpot->getMiddle().y() + g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        0.0f,
        1.0f);
    }
    else
    {
      l_spot_center = osg::Vec4f(
        f_parkingSpot->getMiddle().x(),
        f_parkingSpot->getMiddle().y(),
        0.0f,
        1.0f);
      l_near_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() + g_managerSettings->m_parkingSpotHmiLength/2.0f,
        f_parkingSpot->getMiddle().y() + g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        0.0f,
        1.0f);
      l_far_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() - g_managerSettings->m_parkingSpotHmiLength/2.0f,
        f_parkingSpot->getMiddle().y() - g_managerSettings->m_parkingSpotHmiWidth/2.0f,
        0.0f,
        1.0f);
    }

    l_spotData.m_spotType = cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL;
  }
  else if (static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL) == f_parkingSpot->getType())
  {
    if ( 0 < f_parkingSpot->getPosition().y())
    {
      l_spot_center = osg::Vec4f(
        f_parkingSpot->getMiddle().x(),f_parkingSpot->getMiddle().y(),
        0.0f,
        1.0f);
      l_near_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() + g_managerSettings->m_diagonalSpotWidth/2.0f,
        f_parkingSpot->getMiddle().y() - g_managerSettings->m_diagonalSpotLength/2.0f,
        0.0f,
        1.0f);
      l_far_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() - g_managerSettings->m_diagonalSpotWidth/2.0f,
        f_parkingSpot->getMiddle().y() + g_managerSettings->m_diagonalSpotLength/2.0f,
        0.0f,
        1.0f);
    }
    else
    {
      l_spot_center = osg::Vec4f(
        f_parkingSpot->getMiddle().x(),f_parkingSpot->getMiddle().y(),
        0.0f,
        1.0f);
      l_near_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() + g_managerSettings->m_diagonalSpotWidth/2.0f,
        f_parkingSpot->getMiddle().y() + g_managerSettings->m_diagonalSpotLength/2.0f,
        0.0f,
        1.0f);
      l_far_vertex = osg::Vec4f(
        f_parkingSpot->getMiddle().x() - g_managerSettings->m_diagonalSpotWidth/2.0f,
        f_parkingSpot->getMiddle().y() - g_managerSettings->m_diagonalSpotLength/2.0f,
        0.0f,
        1.0f);
    }

    l_spotData.m_spotType = cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL;
  }
  else
  {
    //Do nothing
  }

  l_spot_center = adaptOsgCoordToViewCoord(l_spot_center, f_MVPmatrix);
  l_near_vertex = adaptOsgCoordToViewCoord(l_near_vertex, f_MVPmatrix);
  l_far_vertex  = adaptOsgCoordToViewCoord(l_far_vertex, f_MVPmatrix);

  l_spotData.setSpotCenter(l_spot_center);

  l_spotData.setSpotOrder(f_index);

  if ( 0u <  f_parkingSpot->getPosition().y())
  {
      l_spotData.setSpotResponSize(l_near_vertex.x()-l_far_vertex.x(), l_far_vertex.y()-l_near_vertex.y());
  }
  else
  {
    l_spotData.setSpotResponSize(l_far_vertex.x()-l_near_vertex.x(), l_far_vertex.y()-l_near_vertex.y());
  }

  return l_spotData;

}

osg::Vec4f adaptOsgCoordToViewCoord(osg::Vec4f f_point, const osg::Matrixf& f_MVPmatrix)
{
  // ParkingSpot is shown in B area. Touch point coord is from whole head unit screen
  //                     MR  HEAD UNIT
  //          222       588                 1110
  //        -----------------------------------------------
  //        |     |             |                         |
  //        |     |             |                         |
  //        |     |             |                         |
  //        |  A  |      B      |990         C            |
  //        |     |             |                         |
  //        |     |             |                         |
  //        |     |             |                         |
  //        |     |---------------------------------------|
  //        |     |                    D                  |90
  //        -----------------------------------------------

  //                     ST HEAD UNIT
  //          296       784                 1480
  //        -----------------------------------------------
  //        |     |             |                         |
  //        |     |             |                         |
  //        |     |             |                         |
  //        |  A  |      B      |1320         C           |
  //        |     |             |                         |
  //        |     |             |                         |
  //        |     |             |                         |
  //        |     |---------------------------------------|
  //        |     |                    D                  |120
  //        -----------------------------------------------
    if ( STEX == g_stateMachineParams->m_vehicleModels || STHX == g_stateMachineParams->m_vehicleModels)
    {
      f_point = f_point * f_MVPmatrix;
      f_point /= f_point.w();
      f_point.x() = f_point.x()*392.0f + 392.0f + 296.0f;
      f_point.y() = f_point.y()*(-660.0f) + 660.0f;
      return f_point;
    }
    else
    {
      f_point = f_point * f_MVPmatrix;
      f_point /= f_point.w();
      f_point.x() = f_point.x()*294.0f + 294.0f + 222.0f;
      f_point.y() = f_point.y()*(-495.0f) + 495.0f;
      return f_point;
    }
}


} // namespace parkingspots
} // namespace assets
} // namespace cc

