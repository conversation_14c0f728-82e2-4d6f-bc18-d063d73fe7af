//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Floor.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_COMMON_FLOOR_H
#define CC_ASSETS_COMMON_FLOOR_H

#include "pc/svs/assets/floorplate/inc/FloorPlateRenderer.h"
#include "pc/svs/core/inc/Asset.h"
#include "cc/assets/common/inc/FloorSingleCam.h"
#include "cc/assets/dynamicwheelmask/inc/DynamicWheelMask.h"
#include "cc/assets/customfloorplategenerator/inc/CustomFloorPlateGenerator.h"
//! forward declarations
namespace pc { namespace assets { class FloorPlateRenderer; } }
namespace pc
{
namespace worker
{
namespace bowlshaping
{
class PolarBowlLayout;
} // namespace bowlshaping
} // namespace worker
namespace core
{
class Framework;
} // namespace core

namespace texfloor {
namespace core{
class MovingFloorPlateGenerator;
} // namespace core
} // namespace texfloor

namespace factory
{
class Floor;
class SV3DNode;

} // namespace factory

namespace texfloor
{
namespace core
{
  class FloorGenerator;
} // namespace core
} // namespace texfloor

} // namespace pc

namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace common
{
class AudiFloorPlateStateHandlerSettings : public pc::util::coding::ISerializable
{
public:
  AudiFloorPlateStateHandlerSettings()
  : m_maxStandstillVelocity(1E-2f)//!< Consider the car to be stationary if odometry velocity drops below the given value (in m/s)
  , m_maxHistoryStandstillDuration(3.0f)//!< History texture will be reset after given standstill duration (in seconds) //SysVS_SVS_3034
  , m_renderHistoryTextureImmediately(true)
  , m_enableHistoricGroundplane(true)
  , m_enableSolidGroundplane(false)
  {
  }

  SERIALIZABLE(AudiFloorPlateStateHandlerSettings)
  {
    ADD_FLOAT_MEMBER(maxStandstillVelocity);
    ADD_FLOAT_MEMBER(maxHistoryStandstillDuration);
    ADD_BOOL_MEMBER(renderHistoryTextureImmediately);
    ADD_BOOL_MEMBER(enableHistoricGroundplane);
    ADD_BOOL_MEMBER(enableSolidGroundplane);
  }

  float m_maxStandstillVelocity;
  float m_maxHistoryStandstillDuration; //SysVS_SVS_935

  /**
   * If true, will switch to history texture as soon as preconditions for history accumulation are met (e.g. min. speed reached, mirrors and cameras nominal)
   * if false, will switch to history only after history texture is fully initialized (e.g. after driving for full car length)
   */
  bool m_renderHistoryTextureImmediately;
  bool m_enableHistoricGroundplane; //SysVS_SVS_2310
  bool m_enableSolidGroundplane; //SysVS_SVS_972
};

extern pc::util::coding::Item<AudiFloorPlateStateHandlerSettings> g_audiFloorPlateStateHandlerSettings;

//!
//! Floor
//!
class Floor : public pc::core::Asset
{
public:

  Floor(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, const osg::Vec2f& f_offset, const bool f_enableFOVBasedCulling);
  Floor(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, const pc::worker::bowlshaping::PolarBowlLayout& f_pLayout, const bool f_enableFOVBasedCulling);
  Floor(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, const pc::worker::bowlshaping::PolarBowlLayout& f_pLayout, cc::assets::common::FloorType f_floorType, const bool f_enableFOVBasedCulling);

  osg::ref_ptr<pc::core::Asset> getBasePlateAsset() const;

  osg::ref_ptr<pc::factory::Floor> getSV3DNode() const;

  void addDynamicWheelMaskDependency(cc::assets::DynWheelMaskAsset* f_dynMaskNode);

protected:

  virtual void traverse(osg::NodeVisitor& f_nv) override;

  void updateBaseplateNode();

private:

  osg::ref_ptr<pc::core::Framework> m_framework;

  osg::ref_ptr<pc::factory::Floor>                      m_floor;
  osg::ref_ptr<pc::core::Asset>                         m_basePlateAsset;
  osg::ref_ptr<pc::assets::FloorPlateRenderer>          m_floorPlateRenderer;
  osg::ref_ptr<cc::assets::floorplate::CustomFloorPlateGenerator>   m_floorPlateGenerator;

  osg::ref_ptr<osg::Geode> m_carshadowGeode;

};

} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_FLOOR_H