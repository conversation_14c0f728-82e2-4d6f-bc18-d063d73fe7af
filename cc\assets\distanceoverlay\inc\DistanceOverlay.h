//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#ifndef CC_ASSETS_DISTANCEOVERLAY
#define CC_ASSETS_DISTANCEOVERLAY

#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include <osgText/Text>
namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace distanceoverlay
{

class IconSettings;
class DigitalIconsSettings;
class DistanceOverlayAsset : public pc::assets::ImageOverlays
{
public:
    enum DistanceSectorId : vfc::uint8_t
    {
        FRONT_LEFT_SIDE = 0u,
        FRONT_LEFT,
        FRONT_MIDDLE,
        FRONT_RIGHT,
        FRONT_RIGHT_SIDE,
        REAR_LEFT_SIDE,
        REAR_LEFT,
        REAR_MIDDLE,
        REAR_RIGHT,
        REAR_RIGHT_SIDE,
            NUM_SECTOR, // keep last
    };

    enum SonarDegrationDIRCETION : vfc::uint8_t
    {
        DEGRATION_FRONT,
        DEGRATION_REAR,
        DEGRATION_WARN,
        NUM_DEGRATION
    };

    enum DistanceWarningLevel : vfc::uint8_t
    {
        FAR_GREEN = 0u,
        GREEN,
        YELLOW,
        RED,
        NUM_WARNING, // keep last
        NO_WARNING = 255,
    };

    enum DistanceDigitalValue : vfc::uint8_t
    {
        DISTANCE_DIGITAL_STOP,
        DISTANCE_DIGITAL_CM_30,
        DISTANCE_DIGITAL_CM_40,
        DISTANCE_DIGITAL_CM_50,
        DISTANCE_DIGITAL_CM_60,
        DISTANCE_DIGITAL_CM_70,
        DISTANCE_DIGITAL_CM_80,
        DISTANCE_DIGITAL_CM_90,
        DISTANCE_DIGITAL_CM_100,
        DISTANCE_DIGITAL_CM_110,
        DISTANCE_DIGITAL_CM_120,
        DISTANCE_DIGITAL_CM_130,
        DISTANCE_DIGITAL_CM_140,
        DISTANCE_DIGITAL_CM_150,
        DISTANCE_DIGITAL_CM_160,
        DISTANCE_DIGITAL_CM_170,
        DISTANCE_DIGITAL_CM_180,
        DISTANCE_DIGITAL_CM_190,
        DISTANCE_DIGITAL_CM_200,
        NUM_DIGITANCE_DIGITAL
    };

    enum DigitalDistanceDisplayId : vfc::uint8_t
    {
        DIGITAL_DISPLAY_FRONT = 0u,
        DIGITAL_DISPLAY_REAR,
        NUM_DIGITAL_DISPLAY, // keep last
    };

public:
    DistanceOverlayAsset(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

    void enableMiniVehicle2d(bool f_enable)
    {
        if (m_enableMiniVehicle2d != f_enable)
        {
            m_enableMiniVehicle2d = f_enable;
            m_initialized         = false;
        }
    }

    void setTextSize(vfc::float32_t f_textSize)
    {
        m_textSize = f_textSize;
    }

    void traverse(osg::NodeVisitor& nv) override;

    void updateDegrationWarning();

private:
    void init();

    void initMiniVehicle2d();

    bool isMiddleSector(DistanceSectorId f_sectorId)
    {
        return (f_sectorId == FRONT_MIDDLE) || (f_sectorId == REAR_MIDDLE);
    }

    pc::assets::Icon*
    createDistanceLevelIcon(const IconSettings* f_settings, DistanceWarningLevel f_level, bool f_isMiddleSector);
    pc::assets::Icon* createDistanceDigitalIcon(const DigitalIconsSettings* f_settings, DistanceSectorId f_value);

    pc::assets::Icon*
    createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize);

    osg::Vec2f getDistanceDisplayPositon(DistanceSectorId f_sectorID);

    void setIconSize(pc::assets::Icon* f_icon);

    void updateWarningLevels(const cc::target::common::SonarAPPData_st& f_SonarAPPData);

    void updateWarningLevel(DistanceSectorId f_sector, DistanceWarningLevel f_warningLevel);

    void updateDistanceDisplay(const cc::target::common::SonarAPPData_st& f_SonarAPPData);

    void sonarDegrationHandling(bool f_isFrontSonarsError, bool f_isRearSonarsError, bool f_showReq);

    std::string getSectorNameFromIndex(DistanceSectorId f_id);

    void setIconsEnabled(bool enabled);

private:
    bool                                          m_initialized         = false;
    bool                                          m_enableMiniVehicle2d = false;
    std::array<pc::assets::IconGroup, NUM_SECTOR> m_sectors             = {};
    pc::assets::IconGroup                         m_distanceDisplay     = {};
    pc::assets::IconGroup                         m_degrationOverlays;
    pc::assets::IconGroup                         m_vehicle2d     = {};
    vfc::uint32_t                                 m_modifiedCount = ~0u;
    std::array<const IconSettings*, NUM_SECTOR>   m_pSettings     = {};
    std::array<DistanceWarningLevel, NUM_SECTOR>  m_warningLevels = {};
    pc::core::Framework*                          m_framework     = nullptr;
    vfc::float32_t                                m_textSize      = 30.0f;
    static osg::Timer                             s_timer;
    static double                                 s_startTime;
    static bool                                   s_isFlashing;
    static bool                                   s_showIcons;
    static bool                                   s_frontSonarsError;
    static bool                                   s_rearSonarsError;
    static bool                                   s_showReq;
};

} // namespace distanceoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_DISTANCEOVERLAY
