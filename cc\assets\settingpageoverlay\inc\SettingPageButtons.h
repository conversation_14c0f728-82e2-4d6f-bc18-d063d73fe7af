
//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#ifndef CC_ASSETS_SETTINGPAGE_BUTTONS_H
#define CC_ASSETS_SETTINGPAGE_BUTTONS_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/assets/button/inc/CustomButtons.h"
#include "cc/assets/button/inc/Dialog.h"
#include "cc/assets/button/inc/TestButton.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/util/pdmwriter/inc/PdmWriter.h"
namespace cc
{
namespace assets
{
namespace settingpageoverlay
{
class SettingPageSwitchButtonTexturePath : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageSwitchButtonTexturePath)
    {
        ADD_STRING_MEMBER(EnabledTexturePath);
        ADD_STRING_MEMBER(DisabledTexturePath);
        ADD_STRING_MEMBER(EnabledUnavailableTexturePath);
        ADD_STRING_MEMBER(DisabledUnavailableTexturePath);
    }

    std::string m_EnabledTexturePath;
    std::string m_DisabledTexturePath;
    std::string m_EnabledUnavailableTexturePath;
    std::string m_DisabledUnavailableTexturePath;
};

class SettingPageInfoButtonTexturePath : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageInfoButtonTexturePath)
    {
        ADD_STRING_MEMBER(InfoButtonTexturePath);
    }

    std::string m_InfoButtonTexturePath;
};


class SettingPageFlipArea : public pc::util::coding::ISerializable
{
    public:
    SERIALIZABLE(SettingPageFlipArea)
    {
        ADD_MEMBER(osg::Vec2f, FlipAreaCenter);
        ADD_MEMBER(osg::Vec2f, FlipResponseArea);
    }

    osg::Vec2f  m_FlipAreaCenter;
    osg::Vec2f  m_FlipResponseArea;
};

class SettingPageFlipButtonTexturePath : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageFlipButtonTexturePath)
    {
        ADD_STRING_MEMBER(FlipButtonGroupOneTexutrePath);
        ADD_STRING_MEMBER(FlipButtonGroupTwoTexutrePath);
    }

    std::string m_FlipButtonGroupOneTexutrePath;
    std::string m_FlipButtonGroupTwoTexutrePath;
};

template <typename SettingPageButtonTexturePath>
class SettingPageButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageButtonSettings)
    {
        ADD_MEMBER(SettingPageButtonTexturePath, day);
        ADD_MEMBER(SettingPageButtonTexturePath, night);
    }

    SettingPageButtonTexturePath m_day;
    SettingPageButtonTexturePath m_night;
};

class SettingPageSonarLevelSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageSonarLevelSettings)
    {
        ADD_MEMBER(osg::Vec2f, CloseSonarTrigLevelButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, CloseSonarTrigLevelButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, MiddleSonarTrigLevelButtonPos);
        ADD_MEMBER(
            SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, MiddleSonarTrigLevelButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, FarSonarTrigLevelButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, FarSonarTrigLevelButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, SonarTrigLevelBackgroundPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, SonarTrigLevelBackgroundTexturePath);
    }
    osg::Vec2f                                                    m_CloseSonarTrigLevelButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_CloseSonarTrigLevelButtonTexturePath;
    osg::Vec2f                                                    m_MiddleSonarTrigLevelButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_MiddleSonarTrigLevelButtonTexturePath;
    osg::Vec2f                                                    m_FarSonarTrigLevelButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_FarSonarTrigLevelButtonTexturePath;
    osg::Vec2f                                                    m_SonarTrigLevelBackgroundPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_SonarTrigLevelBackgroundTexturePath;
};

class SettingPageVehColorButtonSetting : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageVehColorButtonSetting)
    {
        ADD_MEMBER(osg::Vec2f, VehicleColorWhiteButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, VehicleColorWhiteButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, VehicleColorGreyButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, VehicleColorGreyButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, VehicleColorBlackButtonPos);
        ADD_MEMBER(
            SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, VehicleColorBlackButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, VehicleColorPinkButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, VehicleColorPinkButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, VehicleColorBlueButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, VehicleColorBlueButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, VehicleColorPurpleButtonPos);
        ADD_MEMBER(SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>, VehicleColorPurpleButtonTexturePath);
    }
    osg::Vec2f                                                    m_VehicleColorWhiteButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_VehicleColorWhiteButtonTexturePath;
    osg::Vec2f                                                    m_VehicleColorGreyButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_VehicleColorGreyButtonTexturePath;
    osg::Vec2f                                                    m_VehicleColorBlackButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_VehicleColorBlackButtonTexturePath;
    osg::Vec2f                                                    m_VehicleColorPinkButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_VehicleColorPinkButtonTexturePath;
    osg::Vec2f                                                    m_VehicleColorBlueButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_VehicleColorBlueButtonTexturePath;
    osg::Vec2f                                                    m_VehicleColorPurpleButtonPos;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_VehicleColorPurpleButtonTexturePath;
};
class SettingPageSwitchButton : public cc::assets::button::Button
{
public:
    SettingPageSwitchButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

    void updateSwitch(bool f_switch)
    {
        m_buttonSwitch = f_switch;
    }

    bool getSwitchStatus() const
    {
        return m_buttonSwitch;
    }

protected:
    void                 update() override;
    bool                 m_buttonSwitch;
    pc::core::Framework* m_framework;

private:
    virtual void settingPageButtonUpdate() = 0;
    void         onInvalid() override;
    void         onUnavailable() override;
    void         onAvailable() override;
    void         onReleased() override;
};

class SettingPageMODStatusButton : public SettingPageSwitchButton
{
public:
    SettingPageMODStatusButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    void settingPageButtonUpdate();
};

class SettingPageDGearActStatusButton : public SettingPageSwitchButton
{
public:
    SettingPageDGearActStatusButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    void settingPageButtonUpdate() override;
};

class SettingPageSteerActStatusButton : public SettingPageSwitchButton
{
public:
    SettingPageSteerActStatusButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    void settingPageButtonUpdate() override;
};

class SettingPageVehTransStatusButton : public SettingPageSwitchButton
{
public:
    SettingPageVehTransStatusButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    void settingPageButtonUpdate() override;
};

class SonarTrigLevelEnableInterface
{
public:
    virtual void setSonarTrigLevelButtonEnabled(const bool f_sonarTrigLevelButtonEnabled) = 0;
};

class SettingPageSonarTrigLevelButton : public SettingPageSwitchButton, public SonarTrigLevelEnableInterface
{
public:
    SettingPageSonarTrigLevelButton(
        cc::core::AssetId                                                    f_assetId,
        osg::Vec2f                                                           f_buttonPos,
        const SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>& f_settingPageButtonSetting,
        cc::daddy::SonarDistTrigLevel                                        f_sonarDistTrigLevel,
        pc::core::Framework*                                                 f_framework,
        osg::Camera*                                                         f_referenceView = nullptr);
    static cc::daddy::SonarDistTrigLevel getCurrentSonarLevel()
    {
        return s_curSonarLevel;
    }

    static void modifyCurrentSonarLevel(cc::daddy::SonarDistTrigLevel f_curSonarLevel)
    {
        s_curSonarLevel = f_curSonarLevel;
    }

    cc::daddy::SonarDistTrigLevel getButtonSonarLevel() const
    {
        return m_buttonSonarLevel;
    }

    void modifyButtonSonarLevel(cc::daddy::SonarDistTrigLevel f_buttonSonarLevel)
    {
        m_buttonSonarLevel = f_buttonSonarLevel;
    }

    void setSonarTrigLevelButtonEnabled(const bool f_sonarTrigLevelButtonEnabled) override
    {
        if (m_sonarTrigLevelButtonEnabled != f_sonarTrigLevelButtonEnabled)
        {
            m_sonarTrigLevelButtonEnabled = f_sonarTrigLevelButtonEnabled;
        }
    }

    bool getButtonEnabled() const
    {
        return m_sonarTrigLevelButtonEnabled;
    }

private:
    void                                                          onPressed() override;
    void                                                          settingPageButtonUpdate() override;
    pc::core::Framework*                                          m_framework;
    cc::daddy::SonarDistTrigLevel                                 m_buttonSonarLevel;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_sonarTrigLevelButtonTexturePath;
    bool                                                          m_sonarTrigLevelButtonEnabled;
    static cc::daddy::SonarDistTrigLevel                          s_curSonarLevel;
};

class SettingPageSonarTrigLevelBackground : public SettingPageSwitchButton, public SonarTrigLevelEnableInterface
{
public:
    SettingPageSonarTrigLevelBackground(
        cc::core::AssetId                                                    f_assetId,
        osg::Vec2f                                                           f_buttonPos,
        const SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>& f_settingPageButtonSetting,
        pc::core::Framework*                                                 f_framework,
        osg::Camera*                                                         f_referenceView = nullptr);
    void setSonarTrigLevelButtonEnabled(const bool f_sonarTrigLevelButtonEnabled) override
    {
        if (m_sonarTrigLevelBackgroundEnabled != f_sonarTrigLevelButtonEnabled)
        {
            m_sonarTrigLevelBackgroundEnabled = f_sonarTrigLevelButtonEnabled;
        }
    }
    bool getBackgroundEnabled() const
    {
        return m_sonarTrigLevelBackgroundEnabled;
    }

private:
    void settingPageButtonUpdate() override;
    void onPressed() override
    {
    }
    bool                                                          m_sonarTrigLevelBackgroundEnabled;
    pc::core::Framework*                                          m_framework;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_sonarTrigLevelBackgroundTexturePath;
};

class SettingPageSonarTrigLevelButtonGroup : public cc::assets::button::ButtonGroup
{
public:
    SettingPageSonarTrigLevelButtonGroup(
        cc::core::AssetId                    f_assetId,
        const SettingPageSonarLevelSettings& f_settingPageSonarLevelButtonSetting,
        pc::core::Framework*                 f_framework,
        osg::Camera*                         f_referenceView);

    void setSonarTrigLevelEnabled( bool f_sonarTrigLevelEnabled)
    {
        static bool hasInited = false;
        if (!hasInited || m_sonarTrigLevelEnabled != f_sonarTrigLevelEnabled)
        {
            m_sonarTrigLevelEnabled = f_sonarTrigLevelEnabled;
            for (unsigned int i = 0u; i < osg::Group::getNumChildren(); i++)
            {
                osg::Node* child = osg::Group::getChild(i);
                if (child != nullptr)
                {
                    static_cast<SettingPageSonarTrigLevelButton*>(child)->setSonarTrigLevelButtonEnabled(
                        f_sonarTrigLevelEnabled);
                }
            }
            hasInited = true;
        }
    }

protected:
    cc::daddy::SonarDistTrigLevel m_storedButtonSonarLevel;
    bool                          m_sonarTrigLevelEnabled;
    SettingPageSonarLevelSettings m_settingPageSonarLevelButtonSetting;
    void                          update() override;
};

class SettingPagePasActStatusButton : public SettingPageSwitchButton
{
public:
    SettingPagePasActStatusButton(
        cc::core::AssetId                     f_assetId,
        osg::Vec2f                            f_buttonPos,
        pc::core::Framework*                  f_framework,
        SettingPageSonarTrigLevelButtonGroup* f_settingPageSonarTrigLevelButtonGroup,
        osg::Camera*                          f_referenceView = nullptr);

private:
    void                                  onPressed() override;
    void                                  settingPageButtonUpdate() override;
    bool                                  m_isInactived;
    SettingPageSonarTrigLevelButtonGroup* m_settingPageSonarTrigLevelButtonGroup;
};

class SettingPageNightModeStatusButton : public SettingPageSwitchButton
{
public:
    SettingPageNightModeStatusButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    void settingPageButtonUpdate() override;
};

class SettingPageNarrowLaneActButton : public SettingPageSwitchButton
{
public:
    SettingPageNarrowLaneActButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    bool m_isInactived;
    void settingPageButtonUpdate() override;
};

class SettingPageVehColorButton : public SettingPageSwitchButton
{
public:
    SettingPageVehColorButton(
        cc::core::AssetId                                                    f_assetId,
        osg::Vec2f                                                           f_buttonPos,
        const SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>& f_settingPageButtonSetting,
        cc::daddy::EColorCode                                                f_color,
        pc::core::Framework*                                                 f_framework,
        osg::Camera*                                                         f_referenceView = nullptr);
    static cc::daddy::EColorCode getCurrentVehColor()
    {
        return s_curVehColor;
    }

    static void modifyCurrentVehColor(cc::daddy::EColorCode f_curVehColor)
    {
        s_curVehColor = f_curVehColor;
    }

    cc::daddy::EColorCode getButtonVehColor() const
    {
        return m_buttonVehColor;
    }

    void modifyButtonVehColor(cc::daddy::EColorCode f_buttonVehColor)
    {
        m_buttonVehColor = f_buttonVehColor;
    }

private:
    void                                                          onPressed() override;
    void                                                          settingPageButtonUpdate() override;
    pc::core::Framework*                                          m_framework;
    cc::daddy::EColorCode                                         m_buttonVehColor;
    SettingPageButtonSettings<SettingPageSwitchButtonTexturePath> m_vehColorButtonTexturePath;
    static cc::daddy::EColorCode                                  s_curVehColor;
};

class SettingPageVehColorButtonGroup : public cc::assets::button::ButtonGroup
{
public:
    SettingPageVehColorButtonGroup(
        cc::core::AssetId                       f_assetId,
        const SettingPageVehColorButtonSetting& f_settingPageVehColorButtonSetting,
        pc::core::Framework*                    f_framework,
        osg::Camera*                            f_referenceView = nullptr);

protected:
    cc::daddy::EColorCode            m_storedButtonVehColor;
    SettingPageVehColorButtonSetting m_settingPageVehColorButtonSetting;
    void                             update() override;
};

class SettingPageInfoButton : public cc::assets::button::CustomButton
{
public:
    SettingPageInfoButton(
        cc::core::AssetId                   f_assetId,
        pc::core::Framework*                f_framework,
        osg::Vec2f                          f_buttonPos,
        cc::assets::button::DialogID        f_dialogID,
        cc::assets::button::IDialogTrigger* f_dialogTrigger,
        osg::Camera*                        f_referenceView = nullptr);

private:
    void onPressed();
    void update();

private:
    cc::assets::button::DialogID        m_dialogID;
    cc::assets::button::IDialogTrigger* m_diaLogTrigger;
};

enum SettingPageGroupID
{
    PAGE_ONE,
    PAGE_TWO
};
class SettingPageGroupHandler
{
public:
    static void turnOver(SettingPageGroupID f_targePage)
    {
        s_curPage = f_targePage;
    }

    static SettingPageGroupID getCurPage()
    {
        return s_curPage;
    }

private:
    static SettingPageGroupID s_curPage;
};

class SettingPageFlippingButton : public SettingPageSwitchButton
{
public:
    SettingPageFlippingButton(
        cc::core::AssetId    f_assetId,
        osg::Vec2f           f_buttonPos,
        SettingPageFlipArea  f_lastPageFlipArea,
        SettingPageFlipArea  f_nextPageFlipArea,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void onPressed() override;
    void settingPageButtonUpdate() override;
    SettingPageFlipArea  m_lastPageFlipArea;
    SettingPageFlipArea  m_nextPageFlipArea;

};

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_SETTINGPAGE_BUTTONS_H