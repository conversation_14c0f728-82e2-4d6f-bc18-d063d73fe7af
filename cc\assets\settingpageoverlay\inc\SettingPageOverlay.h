#ifndef CC_ASSETS_SETTINGPAGE_OVERLAY_H
#define CC_ASSETS_SETTINGPAGE_OVERLAY_H
#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/assets/button/inc/Dialog.h"
#include "cc/assets/settingpageoverlay/inc/SettingPageButtons.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

class SettingPageOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageOverlaySettings)
    {
        ADD_STRING_MEMBER(settingPageBackgroundTextureGroupOneNightPath);
        ADD_STRING_MEMBER(settingPageBackgroundTextureGroupOneDayPath);
        ADD_STRING_MEMBER(settingPageBackgroundTextureGroupTwoNightPath);
        ADD_STRING_MEMBER(settingPageBackgroundTextureGroupTwoDayPath);
        ADD_STRING_MEMBER(settingPageSonarDeactiveFrontgroundDayTexturePath);
        ADD_STRING_MEMBER(settingPageSonarDeactiveFrontgroundNightTexturePath);
        ADD_MEMBER(osg::Vec2f, settingPageSonarDeactiveFrontgroundPos);
        ADD_MEMBER(osg::Vec2f, settingPageBackgroundPos);
        ADD_MEMBER(osg::Vec2f, settingPageBackgroundSize);
        ADD_MEMBER(osg::Vec2f, MODSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, MODInfoButtonPos);
        ADD_MEMBER(osg::Vec2f, DGearActSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, DGearActInfoButtonPos);
        ADD_MEMBER(osg::Vec2f, PasActSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, PasActInfoButtonPos);
        ADD_MEMBER(osg::Vec2f, NarrowLaneActSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, NarrowLaneActInfoButtonPos);
        ADD_MEMBER(SettingPageSonarLevelSettings, sonarLevelSettings);
        ADD_MEMBER(osg::Vec2f, VehTransSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, VehTransInfoButtonPos);
        ADD_MEMBER(osg::Vec2f, SteerActSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, SteerActInfoButtonPos);
        ADD_MEMBER(osg::Vec2f, NightModeSwichButtonPos);
        ADD_MEMBER(osg::Vec2f, NightModeInfoButtonPos);
        ADD_MEMBER(osg::Vec2f, SettingPageFlippingButtonPos);
        ADD_MEMBER(SettingPageFlipArea, SettingPageLastPageFlipArea);
        ADD_MEMBER(SettingPageFlipArea, SettingPageNextPageFlipArea);
        ADD_MEMBER(SettingPageVehColorButtonSetting, vehicleColorSettings);
    }

    std::string                      m_settingPageBackgroundTextureGroupOneNightPath;
    std::string                      m_settingPageBackgroundTextureGroupOneDayPath;
    std::string                      m_settingPageBackgroundTextureGroupTwoNightPath;
    std::string                      m_settingPageBackgroundTextureGroupTwoDayPath;
    std::string                      m_settingPageSonarDeactiveFrontgroundDayTexturePath;
    std::string                      m_settingPageSonarDeactiveFrontgroundNightTexturePath;
    osg::Vec2f                       m_settingPageSonarDeactiveFrontgroundPos;
    osg::Vec2f                       m_settingPageBackgroundPos;
    osg::Vec2f                       m_settingPageBackgroundSize;
    osg::Vec2f                       m_MODSwichButtonPos;
    osg::Vec2f                       m_MODInfoButtonPos;
    osg::Vec2f                       m_DGearActSwichButtonPos;
    osg::Vec2f                       m_DGearActInfoButtonPos;
    osg::Vec2f                       m_PasActSwichButtonPos;
    osg::Vec2f                       m_PasActInfoButtonPos;
    osg::Vec2f                       m_NarrowLaneActSwichButtonPos;
    osg::Vec2f                       m_NarrowLaneActInfoButtonPos;
    osg::Vec2f                       m_VehTransSwichButtonPos;
    osg::Vec2f                       m_VehTransInfoButtonPos;
    osg::Vec2f                       m_SteerActSwichButtonPos;
    osg::Vec2f                       m_SteerActInfoButtonPos;
    osg::Vec2f                       m_NightModeSwichButtonPos;
    osg::Vec2f                       m_NightModeInfoButtonPos;
    osg::Vec2f                       m_SettingPageFlippingButtonPos;
    SettingPageFlipArea              m_SettingPageLastPageFlipArea;
    SettingPageFlipArea              m_SettingPageNextPageFlipArea;
    SettingPageSonarLevelSettings    m_sonarLevelSettings;
    SettingPageVehColorButtonSetting m_vehicleColorSettings;
};

extern pc::util::coding::Item<SettingPageOverlaySettings> g_settingPageOverlaySettings;

class SettingPageSwitch
{
public:
    virtual void openSettingPage() = 0;
    virtual void closeSettingPage() = 0;
    virtual bool getSettingPageStatus() = 0;
};

class SettingPageBackground : public cc::assets::button::Button // use Button for background temperly
{
public:
    SettingPageBackground(
        cc::core::AssetId    f_assetId,
        SettingPageGroupID   f_settingPageGroupID,
        pc::core::Framework* f_framework,
        SettingPageSwitch*   f_settingPageSwtich,
        osg::Camera*         f_referenceView = nullptr);
    virtual ~SettingPageBackground();

protected:
    void update() override;

private:
    void onInvalid() override
    {
    }
    void onUnavailable() override
    {
    }
    void onAvailable() override;
    void onPressed() override
    {
    }
    void onReleased() override
    {
    }

private:
    //! Copy constructor is not permitted.
    SettingPageBackground(const SettingPageBackground& other); // = delete
    //! Copy assignment operator is not permitted.
    SettingPageBackground& operator=(const SettingPageBackground& other); // = delete
    pc::core::Framework*   m_framework;
    SettingPageGroupID     m_settingPageGroupID;
    SettingPageSwitch*     m_settingPageSwitch;
};


class SettingPageSonarDeactiveFrontground : public cc::assets::button::Button
{
public:
    SettingPageSonarDeactiveFrontground(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);
    virtual ~SettingPageSonarDeactiveFrontground();

protected:
    void update() override;

private:
    void onInvalid() override
    {
        setIconEnable(false);
    }
    void onUnavailable() override
    {
        setIconEnable(false);
    }
    void onAvailable() override
    {
        setIconEnable(true);
    }
    void onPressed() override
    {
        setIconEnable(true);

    }
    void onReleased() override
    {
        setIconEnable(true);
    }

private:
    //! Copy constructor is not permitted.
    SettingPageSonarDeactiveFrontground(const SettingPageSonarDeactiveFrontground& other); // = delete
    //! Copy assignment operator is not permitted.
    SettingPageSonarDeactiveFrontground& operator=(const SettingPageSonarDeactiveFrontground& other); // = delete
    pc::core::Framework*   m_framework;
};



class SettingPageOverlay : public cc::assets::button::ButtonGroup, public SettingPageSwitch
{
public:
    SettingPageOverlay(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView);
    virtual ~SettingPageOverlay();
    void  openSettingPage() override
    {
        m_isSettingPageEnabled = true;
    }

    void closeSettingPage() override
    {
        m_isSettingPageEnabled = false;
    }

    bool getSettingPageStatus() override
    {
        return m_isSettingPageEnabled;
    }
private:
    //! Copy constructor is not permitted.
    SettingPageOverlay(const SettingPageOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    SettingPageOverlay&  operator=(const SettingPageOverlay& other); // = delete
    void                 update() override;
    pc::core::Framework* m_framework;
    bool                 m_isSettingPageEnabled;
};

class SettingPageGroup : public cc::assets::button::ButtonGroup, public SettingPageGroupHandler
{
public:
    SettingPageGroup(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        SettingPageGroupID   f_groupID,
        osg::Camera*         f_referenceView);
    virtual ~SettingPageGroup();

private:
    //! Copy constructor is not permitted.
    SettingPageGroup(const SettingPageGroup& other); // = delete
    //! Copy assignment operator is not permitted.
    SettingPageGroup& operator=(const SettingPageGroup& other); // = delete
    void              update()
    {
        if (m_groupID != SettingPageGroupHandler::getCurPage())
        {
            m_enabled = false;
        }
        else
        {
            m_enabled = true;
        }
    }
    pc::core::Framework* m_framework;
    SettingPageGroupID   m_groupID;
};

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc

#endif