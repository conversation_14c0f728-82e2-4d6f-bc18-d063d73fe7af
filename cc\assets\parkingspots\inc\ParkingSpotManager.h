//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ParkingSpotManager.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_PARKINGSPOTS_PARKINGSPOTMANAGER_H
#define CC_ASSETS_PARKINGSPOTS_PARKINGSPOTMANAGER_H

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/parkingspots/inc/ParkingSpot.h"
#include <osg/Group>
#include <osg/Timer>

//! forward declaration
namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace parkingspots
{

class ParkingSpot;
class TouchscreenCallback;


// Parking Spot Manager State
enum parkingSpotManagerState
{
  PARK_ON_HOLD,
  PARK_OUT,
  PARK_IN
};

enum slotSelectOverlayState
{
  TRAJECTORY_INIT,
  SET_NEW_TRAJECTORY,
  FADE_IN_ANIM_IN_PROGRESS,
  TRAJECTORY_IDLE
};


enum LightWaveAnimState
{
  TRAJ_1_ANIM_IN_PROGRESS,
  //WAIT,
  TRAJ_2_ANIM_IN_PROGRESS
};

//!
//! ParkingSpotManagerSettings
//!
class ParkingSpotManagerSettings : public pc::util::coding::ISerializable
{
public:

  ParkingSpotManagerSettings()
    : m_speedLimit(0.001f)
    // , m_fixYPos(true)
    , m_parkOutCrossSpotYPos(2.5f)
    , m_parkOutCrossFrontSpotXPos(6.2f)
    , m_parkOutCrossRearSpotXPos(-6.f)
    , m_parkOutParaSpotYPos(2.0f)
    , m_parkOutParaSpotXPos(7.5f)
    // , m_hmiTimeoutMS(20) //rendering each 33ms --> ca. 660ms for the master/slave timeout
    , m_parkingSpotHmiLength(3.5f)
    , m_parkingSpotHmiWidth(2.2f)
    , m_realSpotLength(5.0f)
    , m_realSpotWidth(2.5f)
    // , m_spotOnScreenThresholdPercentage(55) //make sure the center of the parking spot is visible for the touch coordinates
    , m_spotOffsetX(5.0f)
    , m_leftfreeParkSpotID(0u)
    , m_rightfreeParkSpotID(0u)
    , m_diagonalSpotLength(3.5f)
    , m_diagonalSpotWidth(2.2f)
    , m_adjustmentValueOfOverlapDistance(0.2f)
  {
  }

  SERIALIZABLE(ParkingSpotManagerSettings)
  {
    ADD_FLOAT_MEMBER(speedLimit);
    // ADD_BOOL_MEMBER(fixYPos);
    ADD_FLOAT_MEMBER(parkOutCrossSpotYPos);
    ADD_FLOAT_MEMBER(parkOutCrossFrontSpotXPos);
    ADD_FLOAT_MEMBER(parkOutCrossRearSpotXPos);
    ADD_FLOAT_MEMBER(parkOutParaSpotYPos);
    ADD_FLOAT_MEMBER(parkOutParaSpotXPos);
    // ADD_UINT32_MEMBER(hmiTimeoutMS);
    ADD_FLOAT_MEMBER(parkingSpotHmiLength);
    ADD_FLOAT_MEMBER(parkingSpotHmiWidth);
    ADD_FLOAT_MEMBER(realSpotLength);
    ADD_FLOAT_MEMBER(realSpotWidth);
    // ADD_UINT32_MEMBER(spotOnScreenThresholdPercentage);
    ADD_FLOAT_MEMBER(spotOffsetX);
    ADD_UINT32_MEMBER(leftfreeParkSpotID);
    ADD_UINT32_MEMBER(rightfreeParkSpotID);
    ADD_FLOAT_MEMBER(diagonalSpotLength);
    ADD_FLOAT_MEMBER(diagonalSpotWidth);
    ADD_FLOAT_MEMBER(adjustmentValueOfOverlapDistance);
  }

  float m_speedLimit;
  // bool  m_fixYPos;
  float m_parkOutCrossSpotYPos;
  float m_parkOutCrossFrontSpotXPos;
  float m_parkOutCrossRearSpotXPos;
  float m_parkOutParaSpotYPos;
  float m_parkOutParaSpotXPos;
  // unsigned int m_hmiTimeoutMS;
  float m_parkingSpotHmiLength;
  float m_parkingSpotHmiWidth;
  float m_realSpotLength;
  float m_realSpotWidth;
  // unsigned int m_spotOnScreenThresholdPercentage;
  float m_spotOffsetX;
  unsigned int m_leftfreeParkSpotID;
  unsigned int m_rightfreeParkSpotID;
  float m_diagonalSpotLength;
  float m_diagonalSpotWidth;
  float m_adjustmentValueOfOverlapDistance;
};

class UISpotData
{
public:

  UISpotData()
    : m_spotCenter(0, 0)
    , m_spotresponseArea(0, 0)
    , m_spotType(cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT)
    , m_spotIndex(0u)
  {
  }

  void setSpotCenter(const osg::Vec4f& f_position)
  {
    m_spotCenter.x() = f_position.x();
    m_spotCenter.y() = f_position.y();
  }

  void setSpotResponSize(const float f_dis_x , const float f_dis_y )
  {
    m_spotresponseArea.x() = f_dis_x;
    m_spotresponseArea.y() = f_dis_y;
  }

  void setSpotOrder(vfc::uint8_t f_index)
  {
    m_spotIndex = f_index;
  }

  osg::Vec2f m_spotCenter;
  osg::Vec2f m_spotresponseArea;
  cc::target::common::EFAPAParkSlotType m_spotType;
  vfc::uint8_t m_spotIndex;
};

//length of the parking spot depends on
//the vehicle lenght + the minimal distance for parking
//right now this is 40cm see DOORs Req.  https://rb-alm-13-p-dwa.de.bosch.com:8443/dwa/rm/urn:rational::1-4147106800294823-O-1775-0007a9c0?doors.view=00000006
// static const float APC_MinLengthParkingSlot_Parallel = 0.40f;
//the height correspond to the minimal distance between two parking lines
//see DOORs Req. https://rb-alm-13-p-dwa.de.bosch.com:8443/dwa/rm/urn:rational::1-4147106800294823-O-1778-0007a9c0?doors.view=00000006
//to ensure there is not overlapping, this value is reduced from 2.3m (specification) to 2.2m
// static const float APC_MinWidthParkingSlot_Perpendicular_Lines = 2.2f;

typedef std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> StrippedEAPAParkSpot;

//!
//! ParkingSpotManager
//!
class ParkingSpotManager : public osg::Group
{
public:

  ParkingSpotManager(pc::core::Framework* f_framework);

  const StrippedEAPAParkSpot& getParkingSpotData() const
  {
    return m_parkingSpotData;
  }

  void initManager();

  // void partialInitManager();

  void invalidateParkingSlots();

  float getParkOutCrossFrontSpotXPos();
  float getParkOutCrossRearSpotXPos();
  float getParkOutParaSpotXPos();
  float getParkOutParaSpotYPos();

  osg::Vec2f getSpotSize();

  void changeState(cc::target::common::EParkngTypeSeld f_state) {m_parkingSpotManagerState = f_state;}

  cc::target::common::EParkngTypeSeld getState() {return (m_parkingSpotManagerState);}

  ParkingSpot* getParkingSpot(vfc::uint8_t f_index);

  bool isParkingSpotActive(unsigned int f_index) const;

  bool isParkingSpotShown(const cc::core::CustomFramework* f_framework);

  void setParkingSpotVisiblity(ParkingSpot* f_parkingSpot, const cc::core::CustomFramework* f_customFramework);

  void setIconCenterAndResponseArea(vfc::uint8_t f_index, cc::daddy::ParkUISpotData_t& f_rParkSpotUIDataContainer, UISpotData f_spotUIData);

  void CleanButtonDispSts();

  bool DeliverButtonDispSts(const core::CustomFramework* f_framework);

  virtual void traverse(osg::NodeVisitor& f_nv);

protected:

  virtual ~ParkingSpotManager();

private:

  //! Copy constructor is not permitted.
  ParkingSpotManager (const ParkingSpotManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSpotManager& operator=(const ParkingSpotManager& other); // = delete

  void update();

  void feedWithPerpandicularOut(const cc::daddy::ParkingSpot& f_parkingSpot);
  void feedWithParallelOut(const cc::daddy::ParkingSpot& f_parkingSpot);


  //! Map gac parking slots information to PF Interfaces
  void mapParkingSlotInterfaces(cc::daddy::ParkingSpot& f_parkingslot,
                                const bool f_isLeft,
                                const vfc::float32_t f_angle,
                                const vfc::uint8_t f_valid,
                                const vfc::float32_t f_obj2X);

  pc::core::Framework* m_framework;

  cc::daddy::ParkingSpot::ETypeOfChanges m_hasNewParkingSlots;
  StrippedEAPAParkSpot m_parkingSpotData;
  osg::ref_ptr<osg::Switch> m_parkingSpotAssets;
  float m_impAlphaStartVal;
  ParkingSpot* m_lastSelectedParkingSpot;
  bool m_parkDirChanged;
  unsigned int m_lastUpdate;

  cc::target::common::EParkngTypeSeld m_parkingSpotManagerState;
  //state machine for the selection overlay
  slotSelectOverlayState m_slotSelectOverlayState;
  LightWaveAnimState m_animState;
  float m_time;
  const float mc_time_inc;
  bool m_MVPmatrixValid;

  osg::Matrixf m_MVPmatrix;
  osg::Matrixf m_Viewmatrix;
  osg::Vec2f m_spotSize;

  cc::daddy::EParkDisp2Touch          m_ViewButtonParkInSlotsSelectingDispSts;
};

extern pc::util::coding::Item<ParkingSpotManagerSettings> g_managerSettings;

} // namespace parkingspots
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKINGSPOTMANAGER_H