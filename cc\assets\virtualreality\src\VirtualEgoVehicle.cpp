//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualEgoVehicle.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/VirtualEgoVehicle.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"

#include <osgDB/ReadFile>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace virtualreality
{

pc::util::coding::Item<VirtualEgoVehicleSettings> g_egoVehicleSetting("VirtualEgoVehicle");

VirtualEgoVehicle::VirtualEgoVehicle() // PRQA S 4054
{
  setNumChildrenRequiringUpdateTraversal(1u);

  addObjectNode();
}

VirtualEgoVehicle::VirtualEgoVehicle(const VirtualEgoVehicle& f_other, const osg::CopyOp& f_copyOp)
  : VirtualRealityObject{f_other, f_copyOp}
{
}

VirtualEgoVehicle::~VirtualEgoVehicle() = default;



void VirtualEgoVehicle::addObjectNode()
{
  const osg::ref_ptr<osg::Node> l_vehicle = osgDB::readNodeFile(g_egoVehicleSetting->m_vehicleModelFilename);
  if (l_vehicle.valid())
  {
    osg::Group::addChild(l_vehicle.get()); // PRQA S 3803
  }
}

} // namespace virtualreality
} // namespace assets
} // namespace cc
