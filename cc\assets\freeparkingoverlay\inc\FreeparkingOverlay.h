//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname:
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EPF2-CN)
//  Department: CC-DA/EPF
//=============================================================================
/// @swcomponent
/// @file  freeparking_overlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_OVERLAY_H
#define CC_ASSETS_FREEPARKINGOVERLAY_OVERLAY_H

/* ---------------Image Overlay----------------------------
#include "cc/daddy/CustomDaddyPorts.h"
#include "pc/svs/assets/imageoverlays/ImageOverlays.h"

#include <osg/Matrixf>
----------------------------------------------------------*/
#include "cc/assets/freeparkingoverlay/inc/FreeparkingInterface.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingNode.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingRotateButton.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingUtils.h"
#include "cc/assets/parkingspots/inc/ParkingSpot.h"
// #include "cc/target/common/commonInterface.h"
#include "pc/generic/util/coding/inc/ISerializable.h"

#include <osg/Geode>
#include <osg/MatrixTransform>
#include <osg/NodeCallback>
#include <osgAnimation/EaseMotion>

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

class FreeparkingOverlay;

//======================================================
// FreeparkingSettings
//------------------------------------------------------
/// Setting class for freeparking
/// <AUTHOR>
//======================================================
class FreeparkingSettings : public pc::util::coding::ISerializable
{
public:
    FreeparkingSettings()
        : m_defalutTexture("cc/resources/icons/freeparking/freeparking_default.png")
        , m_movingTexture("cc/resources/icons/freeparking/freeparking_moving.png")
        , m_availableTexture("cc/resources/icons/freeparking/freeparking_available.png")
        , m_unavailableTexture("cc/resources/icons/freeparking/freeparking_unavailable.png")
        , m_judgingTexuture("cc/resources/icons/freeparking/freeparking_judging.png")
        , m_groundLevel(0.1f)
        , m_mipmapBias(-0.2f)
        , m_pSize(0.65f, 0.45f) // picture height, width
    {
    }

    SERIALIZABLE(FreeparkingSettings)
    {
        ADD_STRING_MEMBER(defalutTexture);
        ADD_STRING_MEMBER(movingTexture);
        ADD_STRING_MEMBER(availableTexture);
        ADD_STRING_MEMBER(unavailableTexture);
        ADD_STRING_MEMBER(judgingTexuture);
        ADD_FLOAT_MEMBER(groundLevel);
        ADD_FLOAT_MEMBER(mipmapBias);
        ADD_MEMBER(osg::Vec2f, pSize);
        ADD_FLOAT_MEMBER(upperPercentage);
        ADD_FLOAT_MEMBER(lowerPercentage);
    }

    std::string    m_defalutTexture;
    std::string    m_movingTexture;
    std::string    m_availableTexture;
    std::string    m_unavailableTexture;
    std::string    m_judgingTexuture;
    vfc::float32_t m_groundLevel;
    vfc::float32_t m_mipmapBias;
    osg::Vec2f     m_pSize;
    vfc::float32_t m_upperPercentage{0.15f};
    vfc::float32_t m_lowerPercentage{0.15f};
};

extern pc::util::coding::Item<FreeparkingSettings> g_freeparkingSettings;

class RotateButton;

//!
//! FreeparkingPlane
//!
class FreeparkingPlane : public osg::MatrixTransform, public FreeparkingNode
{
public:
    FreeparkingPlane();
    FreeparkingPlane(const FreeparkingPlane& f_other, const osg::CopyOp& f_copyOp);

    META_Node(cc::assets::freeparkingoverlay, FreeparkingPlane); // PRQA S 2504

    void setSpotType(vfc::uint32_t f_type)
    {
        m_spotType = f_type;
    }
    void setSpotSide(vfc::uint32_t f_Side)
    {
        m_spotSide = f_Side;
    }
    void setTextureOriginalState(vfc::uint32_t f_state)
    {
        m_textureState = f_state;
    }

    virtual void update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft) override;
    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
    virtual ~FreeparkingPlane();

    bool m_visible;

private:
    FreeparkingPlane(const FreeparkingPlane& other);      // = delete
    FreeparkingPlane& operator=(const FreeparkingPlane&); // delete

    vfc::uint32_t m_spotType;
    vfc::uint32_t m_spotSide;
    vfc::uint32_t m_textureState;
};

class FrontPlane : public FreeparkingPlane
{
public:
    FrontPlane();

    virtual void update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft) override;

protected:
    ~FrontPlane() = default;
};


class FreeparkingWarningOverlay : public osg::MatrixTransform, FreeparkingNode
{
public:
    enum WarningArea : vfc::uint32_t
    {
        TopLeft = 0,
        MiddleLeft,
        BottomLeft,
        TopRight,
        MiddleRight,
        BottomRight,
        NumArea
    };

public:
    FreeparkingWarningOverlay(osg::Vec2f f_size={5.0f, 2.5f});
    void update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft) override;

private:
    void init();
    osg::Geode* createGeode(osg::Vec4f f_textureSelection) const;
    osg::StateSet* createStateSet(osg::Vec4f f_textureSelection) const;
    osg::Geometry* createGeometry() const;
    osg::Vec4f getTextureSelection(WarningArea f_area) const;
    void updateGeometry();

private:
    osg::Vec2f m_size;
    osg::ref_ptr<osg::Geode> m_topLeftGeode;
    osg::ref_ptr<osg::Geode> m_middleLeftGeode;
    osg::ref_ptr<osg::Geode> m_bottomLeftGeode;
    osg::ref_ptr<osg::Geode> m_topRightGeode;
    osg::ref_ptr<osg::Geode> m_middleRightGeode;
    osg::ref_ptr<osg::Geode> m_bottomRightGeode;
    osg::ref_ptr<osg::Texture2D> m_texture;
};

//======================================================
// FreeparkingOverlay
//------------------------------------------------------
/// Freeparking overlay is displayed on the TopView
/// Shows the parking space position drawn by user.
/// <AUTHOR>
//======================================================
class FreeparkingOverlay : public cc::assets::parkingspots::ParkingSpot
{
public:
    FreeparkingOverlay();
    FreeparkingOverlay(
        const FreeparkingOverlay& f_other,
        const osg::CopyOp&        f_copyOp = osg::CopyOp::SHALLOW_COPY); // PRQA S 3143

    META_Node(cc::assets::freeparkingoverlay, FreeparkingOverlay); // PRQA S 2504

    virtual ~FreeparkingOverlay();
    virtual void traverse(osg::NodeVisitor& f_nv) override;

    void dirty()
    {
        m_dirty = true;
    }

    void setParkingPlanState(ParkingPlaneState f_parkingPlaneState)
    {
        m_parkingPlaneState = f_parkingPlaneState;
    }

    ParkingPlaneState getParkingPlanState() const
    {
        return m_parkingPlaneState;
    }

    const osg::Vec2f& getPosition() const
    {
        return m_position;
    }
    const osg::Vec2f& getMiddle() const
    {
        return m_middle;
    }

    void setSlitherActionType(APSlitherActionType f_slitherActionType)
    {
        m_slitherActionType = f_slitherActionType;
    }
    APSlitherActionType getSlitherActionType() const
    {
        return m_slitherActionType;
    }

    void setRotateButtonVisibility(bool f_visible)
    {
        m_rotateButtonVisible = f_visible;
    }
    bool getRotateButtonVisibility() const
    {
        return m_rotateButtonVisible;
    }

    void setRotateButtonDirection(ButtonDirection f_direction)
    {
        m_rotateButton->setDirection(f_direction);
    }

    ButtonDirection getRotateButtonDirection() const
    {
        return m_rotateButton->getDirection();
    }

    void setMousePressed(bool f_mousePressed)
    {
        m_mousePressed = f_mousePressed;
    }
    bool getMousePressed() const
    {
        return m_mousePressed;
    }

    vfc::float32_t getRotateButtonOffsetToResponseCenter() const
    {
        return m_rotateButton->getOffsetToResponseCenter();
    }

    osg::Vec3f getRotateButtonCenter() const
    {
        return m_rotateButton->getRotateCenter();
    }

    void setParkable(EFreeParkingSlotState f_parkable)
    {
        m_parkable = f_parkable;
    }
    EFreeParkingSlotState getParkable() const
    {
        return m_parkable;
    }

    void setAngle(const vfc::float32_t f_angle)
    {
        m_angle = f_angle;
    }

    vfc::float32_t getAngle() const
    {
        return m_angle;
    }

    void setIsLeftSide(const bool f_isLeft)
    {
        m_isLeft = f_isLeft;
    }

    void setPosition(const osg::Vec2f& f_position)
    {
        m_position = f_position;
        m_middle   = m_position + (getSize() / 2.f); // update the middle of the parking slot
    }

private:
    FreeparkingOverlay& operator=(const FreeparkingOverlay&); // delete
    vfc::uint32_t                  m_type;
    osg::Vec2f                     m_position;
    osg::Vec2f                     m_middle;
    bool                           m_dirty;
    bool                           m_rotateButtonVisible;
    vfc::float32_t                 m_angle;
    bool                           m_isLeft;
    bool                           m_mousePressed;
    APSlitherActionType            m_slitherActionType;
    EFreeParkingSlotState          m_parkable;
    osg::ref_ptr<FreeparkingPlane> m_freeparkingPlane;
    osg::ref_ptr<RotateButton>     m_rotateButton;
    osg::ref_ptr<FrontPlane>       m_frontPlane;
    osg::ref_ptr<FreeparkingWarningOverlay> m_warnOverlay;
    ParkingPlaneState              m_parkingPlaneState;
};

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_OVERLAY_H
