//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CalibOverlay.cpp
/// @brief 
//=============================================================================

#include "cc/assets/caliboverlay/inc/CalibOverlay.h"
#include "cc/assets/caliboverlay/inc/CalibSpline.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/worker/core/inc/CustomTaskManager.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/math/inc/Box2D.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/worker/fusion/inc/FusionTask.h"


#include <cassert>
#include <osg/Depth>
#include <osg/Geometry>

#include <osg/LineWidth>
#include <osg/MatrixTransform>
#include <osg/Math>
#include <osgAnimation/EaseMotion>
#include <osgUtil/CullVisitor>


namespace cc
{
namespace assets
{
namespace caliboverlay
{


pc::util::coding::Item<CalibSettings> g_calibSettings("CalibOverlay");

//!
//! CalibOverlayComposite
//!
CalibOverlayComposite::CalibOverlayComposite(pc::core::Framework* f_framework)
  : m_framework(f_framework)
  , m_idxSpline2D(~0u)

{

  m_idxSpline2D = getNumChildren();
  osg::Group::addChild(new CalibSpline2D);  // PRQA S 3803
  osg::StateSet* l_commonStateSet = getOrCreateStateSet();
  l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_commonStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");

}


CalibOverlayComposite::CalibOverlayComposite(const CalibOverlayComposite& f_other, const osg::CopyOp& f_copyOp)
  : osg::MatrixTransform(f_other, f_copyOp)
  , m_framework(f_other.m_framework)
  , m_idxSpline2D(f_other.m_idxSpline2D)
{
}


CalibOverlayComposite::~CalibOverlayComposite()
{
}


void CalibOverlayComposite::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    assert(nullptr != m_framework);
    //! check if CALIB overlay shall be displayed
    cc::core::CustomFramework* l_framework = m_framework->asCustomFramework();

    if (0U == g_calibSettings->m_calibSwitch)
    {
        return;
    }

    const cc::daddy::C2WCalibStatusDaddy_t* l_c2wCalibStatus 
                                          = l_framework->m_camCalibrationStatus_ReceiverPort.getData();
    if (0 != l_c2wCalibStatus)
    {
        if(   (GCM_CALIB_ERROR_NONE     == l_c2wCalibStatus->m_Data.m_calibstatus[0].m_calibrationError)
          && ((GCM_PARTIALLY_CALIBRATED == l_c2wCalibStatus->m_Data.m_calibstatus[0].m_calibrationState)
          ||  (GCM_FULLY_CALIBRATED     == l_c2wCalibStatus->m_Data.m_calibstatus[0].m_calibrationState))
          &&  (GCM_CALIB_ERROR_NONE     == l_c2wCalibStatus->m_Data.m_calibstatus[1].m_calibrationError)
          && ((GCM_PARTIALLY_CALIBRATED == l_c2wCalibStatus->m_Data.m_calibstatus[1].m_calibrationState)
          ||  (GCM_FULLY_CALIBRATED     == l_c2wCalibStatus->m_Data.m_calibstatus[1].m_calibrationState))
          &&  (GCM_CALIB_ERROR_NONE     == l_c2wCalibStatus->m_Data.m_calibstatus[2].m_calibrationError)
          && ((GCM_PARTIALLY_CALIBRATED == l_c2wCalibStatus->m_Data.m_calibstatus[2].m_calibrationState)
          ||  (GCM_FULLY_CALIBRATED     == l_c2wCalibStatus->m_Data.m_calibstatus[2].m_calibrationState))
          &&  (GCM_CALIB_ERROR_NONE     == l_c2wCalibStatus->m_Data.m_calibstatus[3].m_calibrationError)
          && ((GCM_PARTIALLY_CALIBRATED == l_c2wCalibStatus->m_Data.m_calibstatus[3].m_calibrationState)
          ||  (GCM_FULLY_CALIBRATED     == l_c2wCalibStatus->m_Data.m_calibstatus[3].m_calibrationState)) )
      {
        return;
      }
    }

    // osgUtil::CullVisitor* l_cv = static_cast<osgUtil::CullVisitor*> (&f_nv);
    /* Fix QAC */
    //osg::Vec3f l_center(pc::vehicle::g_mechanicalData->getCenter(), 0.0f);
    //osg::Camera* l_cam = l_cv->getCurrentCamera();
    //osg::Vec3f l_eye = l_cam->getInverseViewMatrix().getTrans();
    //osg::Vec3f l_ev = l_eye - l_center;
    //l_ev.normalize();
    //float l_camAngle = osg::RadiansToDegrees(osg::PI_2 - std::acos(l_ev * osg::Z_AXIS));

    getChild(m_idxSpline2D)->accept(f_nv);
  }
  else
  {
    osg::Group::traverse(f_nv);
  }
}

//!
//! CalibOverlay
//!
CalibOverlay::CalibOverlay(pc::core::Framework* f_framework)
{
  setPrototype(new CalibOverlayComposite(f_framework));

  ProcessingTask* l_processingTask = new ProcessingTask(this);
  pc::worker::core::enqueueEvent(new pc::worker::core::AddTaskEvent(l_processingTask));
}


CalibOverlay::~CalibOverlay()
{
}


bool convergeDistance(float& f_currentDistance, float f_targetDistance)
{
  const float l_distanceDelta = std::abs(f_targetDistance - f_currentDistance);
  if (l_distanceDelta < 0.005f)
  {
    //! converged
    return true;
  }
  else if(l_distanceDelta > 0.5f)
  {
    f_currentDistance = f_targetDistance;
  }
  else if (f_currentDistance < f_targetDistance)
  {
    f_currentDistance += (l_distanceDelta / 8.0f);
  }
  else
  {
    f_currentDistance -= (l_distanceDelta / 8.0f);
  }
  return false;
}


//!
//! ProcessingTask
//!
ProcessingTask::ProcessingTask(CalibOverlay* f_calibOverlay)
 : pc::worker::core::Task("CalibOverlayTask", static_cast<unsigned int>(pc::worker::core::Task::POST_PROCESSING))
 , m_calibOverlay(f_calibOverlay)
 , m_staticStateReached(false)
{
}


ProcessingTask::~ProcessingTask()
{
}


bool ProcessingTask::onRun(pc::worker::core::TaskManager* f_taskManager)
{
  osg::ref_ptr<CalibOverlay> l_CalibOverlay;
  if (m_calibOverlay.lock(l_CalibOverlay))
  {
    CalibOverlayComposite* l_CalibOverlayNode = static_cast<CalibOverlayComposite*> (l_CalibOverlay->reserve());
    if (0 != l_CalibOverlayNode)
    {
      CalibSpline::UpdateVisitor l_updateVisitor;
      l_CalibOverlayNode->accept(l_updateVisitor);
      l_CalibOverlay->deliver();
    }
  }
  else
  {
    // ! if acquiring the calib overlay fails this task is done
    return true;
  }

  return false;
}

} // namespace caliboverlay
} // namespace assets
} // namespace cc
