//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  ParkingSpace.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_PARKINGSPACE_H
#define CC_ASSETS_PARKINGSPACE_H

#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"

#include <osg/Matrixf>

#define PARKINGSPACE_SEARCHING_DEBUG 0

namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace parkingspace
{

//======================================================
// ParkingSpaceSettings
//------------------------------------------------------
/// Setting class for parkingspace
/// <AUTHOR>
//======================================================

enum ESpaceSide : vfc::uint8_t
  {
    LEFTSIDE = 0u,
    RIGHTSIDE= 1u
  };

class ParkingSpaceSettings : public pc::util::coding::ISerializable
{
public:

  ParkingSpaceSettings()
    : m_isEnabled(true)
    , m_enablePSMode(15u)  //show parkspace or not. bit 0: scanning, bit 1: selected, bit 2: guidance at parkin, bit 3: guidance at parkout. Eg. 1111B=15D means show all the time.
    , m_iconSize(osg::Vec2f(423.0f, 203.0f))
    , m_iconSizeVert(osg::Vec2f(423.0f, 203.0f))
    , m_texturePathSelectableCrossSlot("cc/resources/icons/parkslot_selectable_cross.png")
    , m_texturePathSelectableParaSlot("cc/resources/icons/parkslot_selectable_para.png")
    , m_texturePathSelectedCrossSlot("cc/resources/icons/parkslot_selected_cross.png")
    , m_texturePathSelectedParaSlot("cc/resources/icons/parkslot_selected_para.png")
    , m_texturePathNextCrossSlot("cc/resources/icons/parkslot_next_cross.png")
    , m_texturePathNextParaSlot("cc/resources/icons/parkslot_next_para.png")
    , m_texturePathTargetCrossSlot("cc/resources/icons/parkslot_target_cross.png")
    , m_texturePathTargetParaSlot("cc/resources/icons/parkslot_target_para.png")
    , m_texturePathTargetCrossSlotFinal("cc/resources/icons/parkslot_target_cross_final.png")
    , m_texturePathTargetParaSlotFinal("cc/resources/icons/parkslot_target_para_final.png")
    , m_texturePathObjectCorner1("cc/resources/icons/parkobj1.png")
    , m_texturePathObjectCorner2("cc/resources/icons/parkobj2.png")
    , m_slotCrossX(2.6f)
    , m_slotParallelX(5.5f)
    , m_slotOffset(osg::Vec2f(0.0f, 0.0f))
    , m_seachingSpacewidth_cm(260u)
    , m_seachingSpaceDepthOffset_cm(0u)
    , m_manoeuverShowDelay(0.0f)
    , m_parkingDiagSlotAngleLowerLimit(0.4f)
    , m_parkingDiagSlotAngleUpperLimit(1.7f)
    , m_parkIconNumberOfDelayTraversalCycle(50u)
    , m_pldDelayDuration(0.12f)
  {
  }

  SERIALIZABLE(ParkingSpaceSettings)
  {
    ADD_BOOL_MEMBER(isEnabled);
    ADD_UINT32_MEMBER(enablePSMode);
    ADD_MEMBER(osg::Vec2f, iconSize);
    ADD_MEMBER(osg::Vec2f, iconSizeVert);
    ADD_STRING_MEMBER(texturePathSelectableCrossSlot);
    ADD_STRING_MEMBER(texturePathSelectableParaSlot);
    ADD_STRING_MEMBER(texturePathSelectedCrossSlot);
    ADD_STRING_MEMBER(texturePathSelectedParaSlot);
    ADD_STRING_MEMBER(texturePathNextCrossSlot);
    ADD_STRING_MEMBER(texturePathNextParaSlot);
    ADD_STRING_MEMBER(texturePathTargetCrossSlot);
    ADD_STRING_MEMBER(texturePathTargetParaSlot);
    ADD_STRING_MEMBER(texturePathTargetCrossSlotFinal);
    ADD_STRING_MEMBER(texturePathTargetParaSlotFinal);
    ADD_STRING_MEMBER(texturePathObjectCorner1);
    ADD_STRING_MEMBER(texturePathObjectCorner2);
    ADD_FLOAT_MEMBER(slotCrossX);
    ADD_FLOAT_MEMBER(slotParallelX);
    ADD_MEMBER(osg::Vec2f, slotOffset);
    ADD_UINT32_MEMBER(seachingSpacewidth_cm);
    ADD_UINT32_MEMBER(seachingSpaceDepthOffset_cm);
    ADD_FLOAT_MEMBER(manoeuverShowDelay);
    ADD_FLOAT_MEMBER(parkingDiagSlotAngleLowerLimit);
    ADD_FLOAT_MEMBER(parkingDiagSlotAngleUpperLimit);
    ADD_UINT32_MEMBER(parkIconNumberOfDelayTraversalCycle);
    ADD_FLOAT_MEMBER(pldDelayDuration);
  }

  bool  m_isEnabled;
  unsigned int m_enablePSMode;
  osg::Vec2f m_iconSize;
  osg::Vec2f m_iconSizeVert;
  std::string m_texturePathSelectableCrossSlot;
  std::string m_texturePathSelectableParaSlot;
  std::string m_texturePathSelectedCrossSlot;
  std::string m_texturePathSelectedParaSlot;
  std::string m_texturePathNextCrossSlot;
  std::string m_texturePathNextParaSlot;
  std::string m_texturePathTargetCrossSlot;
  std::string m_texturePathTargetParaSlot;
  std::string m_texturePathTargetCrossSlotFinal;
  std::string m_texturePathTargetParaSlotFinal;
  std::string m_texturePathObjectCorner1;
  std::string m_texturePathObjectCorner2;
  float m_slotCrossX;
  float m_slotParallelX;
  osg::Vec2f m_slotOffset;
  unsigned int m_seachingSpacewidth_cm;
  unsigned int m_seachingSpaceDepthOffset_cm;
  float m_manoeuverShowDelay;  // in second
  float m_parkingDiagSlotAngleLowerLimit;
  float m_parkingDiagSlotAngleUpperLimit;
  unsigned int m_parkIconNumberOfDelayTraversalCycle;
  float m_pldDelayDuration;

};
extern pc::util::coding::Item<ParkingSpaceSettings> g_parkingSpaceSettings;
//======================================================
// ParkingSpaceManager
//------------------------------------------------------
/// Parking space update logic
/// Handle how the parking space position show up.
/// <AUTHOR>
//======================================================
class ParkingSpaceManager
{
public:

  enum EParkingSpaceMode : unsigned int
  {
    ENUM_PS_MODE_NONE = 0u,
    ENUM_PS_MODE_SCANING = 1u,
    ENUM_PS_MODE_SELECTING = 2u,
    ENUM_PS_MODE_MANOEUVER = 3u
  };

  enum ECorrectedPSMode : unsigned int
  {
    ENUM_CPS_UNCLEAR = 0u,
    ENUM_CPS_SPACE_OBJ1 = 1u,
    ENUM_CPS_SPACE_OBJ2 = 2u,
    ENUM_CPS_SPACE_DUAL = 3u,
    ENUM_CPS_LINE = 4u
  };

  ParkingSpaceManager(cc::views::planview::PlanViewCullCallback* f_cullcallback, const pc::core::Viewport& f_viewport, const bool& f_isHoriScreen);

  virtual ~ParkingSpaceManager();

  void updatePositionSearching( bool& f_isParallel,
                                bool& f_isLeft,
                                cc::target::common::StrippedParkhmiPositionSearching& f_position,
                                const std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_parkSpace,
                                const vfc::uint8_t f_side,
                                const vfc::uint8_t f_number);

  void updateParkingSpotSearching(vfc::uint8_t f_index, cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
                                  const cc::target::common::StrippedParkhmiPositionSearching& f_position,
                                  const bool f_isParallel,
                                  const bool f_isLeft,
                                  const core::CustomFramework* f_framework);

#if PARKINGSPACE_SEARCHING_DEBUG
  void updateParkingObjSearching(cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
                                  const cc::target::common::StrippedParkhmiPositionSearching& f_position,
                                  const bool f_isParallel,
                                  const bool f_isLeft,
                                  const std::string& f_strTexturePath);
#endif //PARKINGSPACE_SEARCHING_DEBUG
  void updateTargetPositionManoeuvering(cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
                                        const cc::target::common::StrippedParkhmiTargetPosition& f_targetPosition,
                                        const cc::assets::uielements::CustomIcon::AnimationStyle f_animationEffectOfIcon);

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);

  void update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework, const osg::NodeVisitor* f_nv);

  unsigned int checkAPAStatus(const vfc::uint8_t f_status, const std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_parkSpace);

  void checkShowSts(
    const vfc::uint8_t f_parkType,         // parkin, parkout
    const vfc::uint8_t f_parkMode,         // apa, rpa
    const bool         f_freeParkActive,   // inactive, active
    bool&              f_isShowAtSearching,
    bool&              f_isShowAtSelected,
    bool&              f_isShowAtGuidanceParkin,
    bool&              f_isShowAtGuidanceParkout);

  pc::assets::Icon* createIcon(const std::string& f_iconPath, const bool& f_isLeft) const;

  virtual pc::assets::Icon* createIcon(
    const std::string& f_iconPath,
    const osg::Vec2f& f_iconPos,
    const osg::Vec2f& f_iconSize,
    const bool& f_flipHorinzontal,
    const bool& f_flipVertical,
    const bool& f_isLeft,
    const cc::assets::uielements::CustomIcon::AnimationStyle f_animationStyle,
    const cc::assets::uielements::CustomIcon::AnimationDir f_animationDir,
    const float& f_angle) const;

private:

  //! Copy constructor is not permitted.
  ParkingSpaceManager (const ParkingSpaceManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSpaceManager& operator=(const ParkingSpaceManager& other); // = delete

  unsigned int m_lastConfigUpdate;

  pc::assets::IconGroup m_iconParkingSpace;
  pc::assets::IconGroup m_iconParkingSpaceGuidance;

  cc::views::planview::PlanViewCullCallback* m_planViewCullCall;

  osg::Vec2f m_spotSize;

  //! Default settings for manoeuvering mode
  std::string m_strTexturePath;
  osg::Vec2f m_manoeuveringIconSize;
  bool m_flipH_b;
  bool m_flipV_b;
  osg::Vec2f m_parkingSpotOffset;
  float m_radOffset;
  float m_veh_alpha;  // rad
  float m_veh_length;  // m

  float m_initTime;
  bool  m_isInitTimeUpdated;

  const pc::core::Viewport& m_viewport;
  const bool  m_isHoriScreen;
  const float m_planViewWidth_hori;
  const float m_planViewWidth_vert;

  osg::ref_ptr<osg::Texture2D> m_textureInterface;
  osg::ref_ptr<osg::Texture2D> m_textureParaSelectable;
  osg::ref_ptr<osg::Texture2D> m_textureParaSelected;
  osg::ref_ptr<osg::Texture2D> m_textureCrossSelectable;
  osg::ref_ptr<osg::Texture2D> m_textureCrossSelected;

};


//======================================================
// ParkingSpace
//------------------------------------------------------
/// On the Top View parking space are displayed.
/// Shows the parking space position around the car.
/// <AUTHOR>
//======================================================
class ParkingSpace: public cc::assets::uielements::CustomImageOverlays
{
public:

  ParkingSpace(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId, osg::Camera* f_view, const pc::core::Viewport& f_viewport, const bool& f_isHoriScreen, cc::views::planview::PlanViewCullCallback* f_cullcallback);

  virtual ~ParkingSpace();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  //! Copy constructor is not permitted.
  ParkingSpace (const ParkingSpace& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSpace& operator=(const ParkingSpace& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ParkingSpaceManager m_manager;

};


} // namespace parkingspace
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKINGSPACE_H
