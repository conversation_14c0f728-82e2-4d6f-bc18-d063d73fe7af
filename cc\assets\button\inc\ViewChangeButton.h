
//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_VIEW_CHANGE_BUTTON_H
#define CC_ASSETS_VIEW_CHANGE_BUTTON_H

#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/assets/button/inc/Button.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core

} // namespace pc

namespace cc
{
namespace assets
{
namespace button
{
namespace viewchangebutton
{

class ViewChangeButtonSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(ViewChangeButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTexturePath, buttonTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_INT_MEMBER(displayDelay);
    }

    ButtonTexturePath m_buttonTexture;
    osg::Vec2f m_horiPos = osg::Vec2f(0.0f, 100.0f);
    int  m_displayDelay = 3;
};

class PerspectiveViewButtonBackground : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(PerspectiveViewButtonBackground) // PRQA S 2428
    {
        ADD_STRING_MEMBER(topBackroundTexturePath);
        ADD_MEMBER(osg::Vec2f, topBackroundTexturPos);
        ADD_STRING_MEMBER(buttomBackroundTexturePath);
        ADD_MEMBER(osg::Vec2f, buttomBackroundTexturPos);
        ADD_STRING_MEMBER(fullBackroundTexturePath);
        ADD_MEMBER(osg::Vec2f, fullBackroundTexturPos);
        ADD_INT_MEMBER(displayDelay);
    }

    std::string m_topBackroundTexturePath;
    osg::Vec2f  m_topBackroundTexturPos;
    std::string m_buttomBackroundTexturePath;
    osg::Vec2f  m_buttomBackroundTexturPos;
    std::string m_fullBackroundTexturePath;
    osg::Vec2f  m_fullBackroundTexturPos;
    int  m_displayDelay = 3;
};

class ViewChangeButton : public Button
{
public:
    ViewChangeButton(cc::core::AssetId f_assetId, ViewChangeButtonSettings* f_settings, EScreenID f_screenId, osg::Camera* f_referenceView=nullptr);


protected:
    void onInvalid() override;
    void onUnavailable() override;
    void onAvailable() override;
    void onSelected() override;
    void onReleased() override;
    std::string getViewChangeButtonName() const
    {
        switch (m_screenId)
        {
        case EScreenID::EScreenID_SINGLE_FRONT_NORMAL:
            return "SINGLE_FRONT_NORMAL Button";
            break;
        case EScreenID::EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
            return "SINGLE_REAR_NORMAL_ON_ROAD Button";
            break;
        case EScreenID::EScreenID_WHEEL_FRONT_DUAL:
            return "WHEEL_FRONT_DUAL Button";
            break;
        case EScreenID::EScreenID_WHEEL_REAR_DUAL:
            return "WHEEL_REAR_DUAL Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_PFR:
            return "PERSPECTIVE_PFR Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_FL:
            return "PERSPECTIVE_FL Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_FR:
            return "PERSPECTIVE_FR Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_PRE:
            return "PERSPECTIVE_PRE Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_RL:
            return "PERSPECTIVE_RL Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_RR:
            return "PERSPECTIVE_RR Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_KL:
            return "PERSPECTIVE_KL Button";
            break;
        case EScreenID::EScreenID_PERSPECTIVE_KR:
            return "PERSPECTIVE_KR Button";
            break;
        default:
            return "Invalid Button";
            break;
        }
    }

    EScreenID getScreenId() const
    {
        return m_screenId;
    }

protected:
    ViewChangeButtonSettings* m_settings;
    EScreenID m_screenId;
    int m_delayCounter;
    bool m_gearChangedToP;
};

class SingleViewChangeButton : public ViewChangeButton
{
public:
    SingleViewChangeButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, ViewChangeButtonSettings* f_settings,  EScreenID f_screenId, osg::Camera* f_referenceView=nullptr);

private:
    void update() override;
    void onPressed() override;

    pc::core::Framework* m_framework;
    ESVSViewMode m_preViewMode;
    EGear        m_preGear;
};

class PerspectiveViewChangeButtonBackground : public cc::assets::button::Button
{
public:

    enum BackgroundVisulableRange
    {
        NONE,
        TOP,
        BUTTOM,
        FULL
    };
    PerspectiveViewChangeButtonBackground(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);
protected:
    void update() override;
private:
    void onInvalid() override
    {
        setIconEnable(false);
    }

    void onUnavailable() override
    {
        setIconEnable(false);
    }

    void onAvailable() override
    {
        setIconEnable(true);
    }

    void onPressed() override
    {
        setIconEnable(true);
    }

    void onReleased() override
    {
        setIconEnable(true);
    }

    void setBackgroundVisulableRange(BackgroundVisulableRange f_backgroundVisulableRange)
    {
        m_backgroundVisulableRange = f_backgroundVisulableRange;
    }

    BackgroundVisulableRange getBackgroundVisulableRange() const
    {
        return m_backgroundVisulableRange;
    }
    void initFrontPointIcons();
    void initMidPointIcons();
    void initRearPointIcons();
    void enableFrontPointIcons(bool f_enable);
    void enableMidPointIcons(bool f_enable);
    void enableRearPointIcons(bool f_enable);
private:
    BackgroundVisulableRange m_backgroundVisulableRange;
    pc::core::Framework* m_framework;
    EGear        m_preGear;
    int m_delayCounter;
    bool m_gearChangedToP;
    pc::assets::IconGroup m_frontPointIcons;
    pc::assets::IconGroup m_midPointIcons;
    pc::assets::IconGroup m_rearPointIcons;
};

class PerspectiveViewChangeButton : public ViewChangeButton
{
public:
    PerspectiveViewChangeButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, ViewChangeButtonSettings* f_settings,  EScreenID f_screenId, osg::Camera* f_referenceView=nullptr);

private:
    void update() override;
    void onPressed() override;
    pc::core::Framework* m_framework;
    vfc::uint32_t m_modifiedCount = ~0u;
    ESVSViewMode m_preViewMode;
    EGear        m_preGear;
};
class ViewChangeButtonGroup : public ButtonGroup
{
public:
    ViewChangeButtonGroup(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView=nullptr);

protected:
    void update() override;
};

extern pc::util::coding::Item<ViewChangeButtonSettings> g_FrontButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings> g_RearButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveFrontButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveFrontLeftButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveFrontRightButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveRearButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveRearLeftButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveRearRightButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveLeftButtonSettings;
extern pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveRightButtonSettings;
extern pc::util::coding::Item<PerspectiveViewButtonBackground>
    g_PerspectiveViewButtonBackground;

} // namespace viewchangebutton
} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIEW_CHANGE_BUTTON_H