//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  RCTAOverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_RCTAOVERLAY_RCTAOVERLAY_H
#define CC_ASSETS_RCTAOVERLAY_RCTAOVERLAY_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>
#include <osg/Texture2D>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace rctaoverlay
{

osg::Texture2D* loadTexture(const std::string& f_filename);

template <class T>
void generateColors(osg::Geometry* f_geometry, const T& f_operator);

class RctaOverlayCodingParams;
extern pc::util::coding::Item<RctaOverlayCodingParams> g_settings;

class RctaOverlayUpdateCallback: public osg::NodeCallback
{

public:
  RctaOverlayUpdateCallback(
                                osg::ref_ptr<osg::Geode> f_FCTALeftGeode, 
                                osg::ref_ptr<osg::Geode> f_FCTARightGeode, 
                                osg::ref_ptr<osg::Geode> f_RCTALeftGeode, 
                                osg::ref_ptr<osg::Geode> f_RCTARightGeode, 
                                pc::core::Framework* f_pFramework
                                );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;
  
  void updatein2Hz(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);
  void updatein4Hz(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);

protected:
  virtual ~RctaOverlayUpdateCallback();

private:

  //! Copy constructor is not permitted.
  RctaOverlayUpdateCallback (const RctaOverlayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  RctaOverlayUpdateCallback& operator=(const RctaOverlayUpdateCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_FCTALeftGeode;
  osg::ref_ptr<osg::Geode> m_FCTARightGeode;
  osg::ref_ptr<osg::Geode> m_RCTALeftGeode;
  osg::ref_ptr<osg::Geode> m_RCTARightGeode;

  pc::core::Framework* m_pFramework;

  float m_lastTime_2Hz;
  float m_lastTime_4Hz;
};

//!
//! rctaoverlay
//!
class RctaOverlay : public osg::MatrixTransform
{
public:

    RctaOverlay(pc::core::Framework* f_framework);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    virtual void init();

    virtual ~RctaOverlay();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_FCTALeftGeode;
    osg::ref_ptr<osg::Geode> m_FCTARightGeode;
    osg::ref_ptr<osg::Geode> m_RCTALeftGeode;
    osg::ref_ptr<osg::Geode> m_RCTARightGeode;

private:
    //! Copy constructor is not permitted.
    RctaOverlay (const RctaOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    RctaOverlay& operator=(const RctaOverlay& other); // = delete

};

} // namespace rctaoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_RCTAOVERLAY_RCTAOVERLAY_H
