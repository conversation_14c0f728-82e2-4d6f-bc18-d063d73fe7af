//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_SVS_ANIMATION_CAMERAANIMATIONFACTORY_H
#define CC_SVS_ANIMATION_CAMERAANIMATIONFACTORY_H

#include "pc/svs/animation/inc/CameraAnimationFactory.h"

namespace cc
{

namespace core
{
class CustomFramework;
} // namespace core

namespace animation
{
/**
 * CameraAnimationFactory
 */
class CameraAnimationFactory : public pc::animation::CameraAnimationFactory
{
public:

  CameraAnimationFactory(cc::core::CustomFramework* f_customFramework);


  /**
   * \brief Creates a dynamic camera orbit animation which animates to a given end position.
   * It is dynamic in the sense that the flight path is generated at the time the animation is started and therefore does not need
   * to start at predetermined camera position or stitching state.
   */
  pc::animation::Animation* createCameraOrbitAnimation(pc::core::View* f_view, pc::factory::RenderManager* f_renderManager, cc::virtcam::VirtualCamEnum f_target) const;

protected:

  virtual ~CameraAnimationFactory() = default;

  osg::ref_ptr<pc::virtcam::CameraFlightPathGenerator> m_flightPathGenerator, m_orbitFlightPathGenerator;

};

} // namespace animation
} // namespace cc

#endif // CC_SVS_ANIMATION_CAMERAANIMATIONFACTORY_H
