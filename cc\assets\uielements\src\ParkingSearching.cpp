//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ParkingSearching.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ParkingSearching.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"
#include "cc/assets/uielements/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for parking modes
enum class ParkingSearchingType : vfc::uint8_t
{
  PARKING_SEARCHING_AUTO_PIC,
  PARKING_SEARCHING_HALO
};

//!
//! @brief Construct a new ParkingSearching Manager:: ParkingSearching Manager object
//!
//! @param f_config
//!
ParkingSearchingManager::ParkingSearchingManager()
  : m_lastConfigUpdate{~0u}
  , m_settingParkSearching{}
{
}

ParkingSearchingManager::~ParkingSearchingManager() = default;



void ParkingSearchingManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_settingParkSearching.clear(f_imageOverlays);
  m_settingParkSearching.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkHalo,                    (g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter),                       getImageSize(g_uiSettings->m_texturePathParkHalo)));
  m_settingParkSearching.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkAutoPic,                 transferToBottomLeftHori(g_uiSettings->m_settingPARKAutoPic.m_iconCenter),           getImageSize(g_uiSettings->m_texturePathParkAutoPic))); // PRQA S 2759
}

void ParkingSearchingManager::update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)    // PRQA S 6043 // PRQA S 6040 // PRQA S 6041
{
  if ((f_imageOverlays == nullptr) || (f_framework == f_framework))
  {
      return;
  }
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }
  m_settingParkSearching.setAllEnabled(false);

  // ! read all the signals
  cc::target::common::EPARKStatusR2L                l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld               l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L             l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndExtR2L          l_curparkDriverIndExt     = cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest;
  cc::target::common::EPARKRecoverIndR2L            l_curparkSuspend          = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  cc::target::common::EPARKQuitIndR2L               l_curparkQuitInd          = cc::target::common::EPARKQuitIndR2L::PARKQUIT_None;
  cc::target::common::EPARKDriverIndSearchR2L       l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                          l_curFreeParkingActive    = false;
  cc::target::common::rbp_Type_ParkManeuverType_en  l_curparkPSDirection      = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;

  cc::target::common::EAPAPARKMODE                  l_curParkAPARPAParkMode   = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::RPAAvailable                  l_curParkRPAAvaliable     = cc::target::common::RPAAvailable::RPA_NotAvailable;

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndExtReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndExtDaddy_t* const l_parkDriverIndExt = f_framework->m_parkHmiParkDriverIndExtReceiver.getData();
    l_curparkDriverIndExt = l_parkDriverIndExt->m_Data;
  }

  if (f_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
  {
    const cc::daddy::ParkRecoverIndDaddy_t* const l_parkSuspend = f_framework->m_parkHmiParkingRecoverIndReceiver.getData();
    l_curparkSuspend = l_parkSuspend->m_Data;
  }

  if (f_framework->m_parkPSDirectionSelectedReceiver.hasData())
  {
    const cc::daddy::ParkPSDirectionSelected_t* const l_parkPSDirection = f_framework->m_parkPSDirectionSelectedReceiver.getData();
    l_curparkPSDirection = l_parkPSDirection->m_Data;
  }

  if (f_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = f_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (f_framework->m_parkHmiParkingQuitIndReceiver.hasData())
  {
    const cc::daddy::ParkQuitIndDaddy_t* const l_parkQuitInd = f_framework->m_parkHmiParkingQuitIndReceiver.getData();
    l_curparkQuitInd = l_parkQuitInd->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
    l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curParkAPARPAParkMode = l_parkAPAPARKMODE->m_Data;
  }

  if (f_framework->m_parkHmiRPAAvailableReceiver.hasData())
  {
    const cc::daddy::ParkRPAAvaliableDaddy_t* const l_ParkRPAAvaliable = f_framework->m_parkHmiRPAAvailableReceiver.getData();
    l_curParkRPAAvaliable = l_ParkRPAAvaliable->m_Data;
  }
  bool l_showCar = false;

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curParkAPARPAParkMode && cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_SearchingProcess:
    {
      l_showCar = true;
      break;
      }
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
    {
      if (false == l_curFreeParkingActive)
      {
        l_showCar = true;
      }
      break;
    }
    default:
    {
      break;
    }
    }
    // if (cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleSlowdown == l_curAPADriverReq_Search)
    // {
    //   l_showCar = true;
    // }
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curParkAPARPAParkMode && cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor: // req_493
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror: // req_497
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk: // req_386
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood: // req_387
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot: // req_371
    {
      break;
    }
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt:
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:
    {
      l_showCar = true;
      break;
    }
    default:
    {
      break;
    }
    }
    if (cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear == l_curAPADriverReq_Search)
    {
        l_showCar = true;
    }
  }

  #ifdef USE_VIRTUAL_OBJECT
  if (cc::assets::virtualreality::isShow(f_framework))
  {
    l_showCar = false;
  }
  #endif

  if ( l_showCar )
  {
    m_settingParkSearching.getIcon(static_cast<vfc::uint32_t>(ParkingSearchingType::PARKING_SEARCHING_AUTO_PIC))->setEnabled(true);
    m_settingParkSearching.getIcon(static_cast<vfc::uint32_t>(ParkingSearchingType::PARKING_SEARCHING_HALO))->setEnabled(true);
  }
  else
  {
    m_settingParkSearching.getIcon(static_cast<vfc::uint32_t>(ParkingSearchingType::PARKING_SEARCHING_AUTO_PIC))->setEnabled(false);
    m_settingParkSearching.getIcon(static_cast<vfc::uint32_t>(ParkingSearchingType::PARKING_SEARCHING_HALO))->setEnabled(false);
  }
}


//!
//! @brief Construct a new ParkingSearching:: ParkingSearching object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ParkingSearching::ParkingSearching(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_manager{}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


ParkingSearching::~ParkingSearching() = default;


void ParkingSearching::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc

