//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualParkSlot.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/VirtualParkSlot.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/ShaderManager.h"
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"

#include <osgDB/ReadFile>
#include <osg/Geometry>
#include <osg/Texture2D>

using pc::util::logging::g_AppContext;

#define USE_PARALLELOGRAM_SLOT 0

namespace cc
{
namespace assets
{
namespace virtualreality
{



VirtualParkSlot::VirtualParkSlot() // PRQA S 4054 // PRQA S 4207
{
  setNumChildrenRequiringUpdateTraversal(1u);

  addObjectNode();
}

VirtualParkSlot::VirtualParkSlot(const VirtualParkSlot& f_other, const osg::CopyOp& f_copyOp)
  : VirtualRealityObject{f_other, f_copyOp}
{
}

VirtualParkSlot::~VirtualParkSlot() = default;



void VirtualParkSlot::addObjectNode()
{
  osg::VertexBufferObject* const l_vbo = new osg::VertexBufferObject;
  l_vbo->setUsage(GL_DYNAMIC_DRAW_ARB);    // PRQA S 3143
  osg::Geometry* const l_geometry = new osg::Geometry;
  l_geometry->setUseDisplayList(false);
  l_geometry->setUseVertexBufferObjects(true);

  osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
  l_vertices->setVertexBufferObject(l_vbo);
  // (*l_vertices)[0u] = osg::Vec3(0.0f, 0.0f, 0.0f);
  // (*l_vertices)[1u] = osg::Vec3(1.0f, 0.0f, 0.0f);
  // (*l_vertices)[2u] = osg::Vec3(1.0f, 1.0f, 0.0f);
  // (*l_vertices)[3u] = osg::Vec3(0.0f, 1.0f, 0.0f);
  (*l_vertices)[0u] = osg::Vec3(-g_virtualRealityObjectSetting->m_slotRear2RearAxelDistance,   g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
  (*l_vertices)[1u] = osg::Vec3(-g_virtualRealityObjectSetting->m_slotRear2RearAxelDistance,  -g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
  (*l_vertices)[2u] = osg::Vec3( g_virtualRealityObjectSetting->m_slotFront2RearAxelDistance, -g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
  (*l_vertices)[3u] = osg::Vec3( g_virtualRealityObjectSetting->m_slotFront2RearAxelDistance,  g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
  l_geometry->setVertexArray(l_vertices);

  osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
  // (*l_texCoords)[0u] = osg::Vec2(1.0f, 0.0f);
  // (*l_texCoords)[1u] = osg::Vec2(1.0f, 1.0f);
  // (*l_texCoords)[2u] = osg::Vec2(0.0f, 1.0f);
  // (*l_texCoords)[3u] = osg::Vec2(0.0f, 0.0f);
  (*l_texCoords)[0u] = osg::Vec2(0.0f, 1.0f);
  (*l_texCoords)[1u] = osg::Vec2(1.0f, 1.0f);
  (*l_texCoords)[2u] = osg::Vec2(1.0f, 0.0f);
  (*l_texCoords)[3u] = osg::Vec2(0.0f, 0.0f);
  l_geometry->setTexCoordArray(0u, l_texCoords);

  osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
  (*l_colours)[0u] = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
  l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

  osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
  (*l_normals)[0u] = osg::Z_AXIS;
  l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

  osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
  (*l_indices)[0u] = 1u;
  (*l_indices)[1u] = 0u;
  (*l_indices)[2u] = 2u;
  (*l_indices)[3u] = 2u;
  (*l_indices)[4u] = 0u;
  (*l_indices)[5u] = 3u;
  l_geometry->addPrimitiveSet(l_indices);    // PRQA S 3803

  g_planeGeode = new osg::Geode;
  g_planeGeode->addDrawable(l_geometry);    // PRQA S 3803

  osg::Group::addChild(g_planeGeode.get());    // PRQA S 3803

  const osg::observer_ptr<osg::StateSet> l_slotState     = new osg::StateSet;
  osg::Image*                      const l_slotImage     = osgDB::readImageFile(g_virtualRealityObjectSetting->m_filenameSlotOccupied);
  osg::Texture2D*                  const l_slotTexture2D = new osg::Texture2D(l_slotImage);
  pc::core::TextureShaderProgramDescriptor l_slotShader("advancedTex");

  l_slotTexture2D->setDataVariance(osg::Object::DYNAMIC);
  l_slotTexture2D->setUnRefImageDataAfterApply(true);
  l_slotTexture2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_slotTexture2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_slotTexture2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_slotTexture2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

  l_slotState->setTextureAttribute(0u, l_slotTexture2D);
  l_slotState->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
  l_slotState->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_slotState->setRenderBinDetails(pc::core::g_renderOrder->m_carShadow - 1, "RenderBin");

  l_slotState->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));    // PRQA S 3803
  l_slotState->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f);    // PRQA S 3803
  l_slotState->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);    // PRQA S 3803

  l_slotShader.apply(l_slotState.get());    // PRQA S 3803

  g_planeGeode->setStateSet(l_slotState.get());
}

void VirtualParkSlot::updateObjectNode()
{
#if USE_PARALLELOGRAM_SLOT  // parallelogram for diagonal, rectangle for others

  osg::Geometry* l_geometry  = g_planeGeode->getDrawable(0u)->asGeometry();
  osg::Vec3Array* l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray());

  if(m_slotOrientationType == cc::target::common::EParkSlotOrientationType::cc::target::common::EParkSlotOrientationType::PARKSLOTTYPE_DIAGONAL)
  {
    /*
    //Using l_corner_2 to calculate slot position
    cc::target::common::ParkSlotPoint_st l_corner_2 = m_slotCornerPostion.m_slotCorner2;
    //For left side parallelogram
    if(l_corner_2.m_y > 0)
    {
      //Slot length is set to 5.5m, width is set to 2.6m, slotRear2RearAxelDistance = 1.2m(1.2/1.41=0.85 )
      (*l_vertices)[0u] = osg::Vec3(l_corner_2.m_x-2.6f-3.9f, l_corner_2.m_y+3.9, 0.0f);
      (*l_vertices)[1u] = osg::Vec3(l_corner_2.m_x-2.6f, l_corner_2.m_y, 0.0f);
      (*l_vertices)[2u] = osg::Vec3(l_corner_2.m_x, l_corner_2.m_y, 0.0f);
      (*l_vertices)[3u] = osg::Vec3(l_corner_2.m_x-3.9f, l_corner_2.m_y+3.9, 0.0f);
    }
    else
    {

    }
    */
    // Using vehicle center to calculate slot position
    // For left side parallelogram
    Position_st l_position = getPosition();
    if(l_position.m_y > 0)
    {
      //Slot length is set to 5.5m, width is set to 2.6m, slotRear2RearAxelDistance = 1.2m(1.2/1.41=0.85 )
      float leftBottomSlotP_x = l_position.m_x;
      float leftBottomSlotP_y = l_position.m_y;
      (*l_vertices)[0u] = osg::Vec3(leftBottomSlotP_x,           leftBottomSlotP_y, 0.0f);
      (*l_vertices)[1u] = osg::Vec3(leftBottomSlotP_x+3.9f,      leftBottomSlotP_y-3.9f, 0.0f);
      (*l_vertices)[2u] = osg::Vec3(leftBottomSlotP_x+3.9f+5.5f, leftBottomSlotP_y-3.9f, 0.0f);
      (*l_vertices)[3u] = osg::Vec3(leftBottomSlotP_x+5.5f,      leftBottomSlotP_y, 0.0f);
    }
    else
    {

    }

    l_vertices->dirty();
  }
  else
  {
    (*l_vertices)[0u] = osg::Vec3(-g_virtualRealityObjectSetting->m_slotRear2RearAxelDistance,   g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
    (*l_vertices)[1u] = osg::Vec3(-g_virtualRealityObjectSetting->m_slotRear2RearAxelDistance,  -g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
    (*l_vertices)[2u] = osg::Vec3( g_virtualRealityObjectSetting->m_slotFront2RearAxelDistance, -g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
    (*l_vertices)[3u] = osg::Vec3( g_virtualRealityObjectSetting->m_slotFront2RearAxelDistance,  g_virtualRealityObjectSetting->m_slotHalfWidth, 0.0f);
    l_vertices->dirty();
  }

#endif

  const osg::observer_ptr<osg::StateSet> l_spotsState     = new osg::StateSet;
  osg::ref_ptr<osg::Image>   l_image;

  switch (m_slotType)
  {
  case cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_OCCUPIED:
      {l_image = osgDB::readImageFile(g_virtualRealityObjectSetting->m_filenameSlotOccupied);
  break;}
  case cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_NOT_SELECTABLE:
      {l_image = osgDB::readImageFile(g_virtualRealityObjectSetting->m_filenameSlotNotSelectable);
  break;}
  case cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_SELECTABLE:
      {l_image = osgDB::readImageFile(g_virtualRealityObjectSetting->m_filenameSlotSelectable);
  break;}
  case cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_SELECTED:
      {l_image = osgDB::readImageFile(g_virtualRealityObjectSetting->m_filenameSlotSelected);
  break;}
  default:
      {l_image = osgDB::readImageFile(g_virtualRealityObjectSetting->m_filenameSlotNotSelectable);
  break;}
  }

  if (l_image.valid())
  {
      const osg::ref_ptr<osg::Texture2D> l_texture = new osg::Texture2D(l_image.get());
      l_texture->setResizeNonPowerOfTwoHint(false);
      l_texture->setUnRefImageDataAfterApply(true);
      l_texture->setDataVariance(osg::Object::DYNAMIC);
      l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
      l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
      l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
      l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

      l_spotsState->setTextureAttribute(0u, l_texture);
      l_spotsState->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
      l_spotsState->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
      l_spotsState->setRenderBinDetails(pc::core::g_renderOrder->m_carShadow - 1, "RenderBin");

      l_spotsState->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));    // PRQA S 3803
      l_spotsState->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f);    // PRQA S 3803
      l_spotsState->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);    // PRQA S 3803

      pc::core::TextureShaderProgramDescriptor l_slotShader("advancedTex");
      l_slotShader.apply(l_spotsState.get());    // PRQA S 3803
      g_planeGeode->setStateSet(l_spotsState.get());
  }
}

} // namespace virtualreality
} // namespace assets
} // namespace cc
