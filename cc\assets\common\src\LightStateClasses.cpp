#include "cc/assets/common/inc/LightStateClasses.h"
#include "pc/generic/rapidjson/document.h"
#include "pc/generic/rapidjson/istreamwrapper.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "vfc/core/vfc_types.hpp"
#include <unordered_map>
#include <fstream>


using pc::util::logging::g_AppContext;


namespace cc
{
namespace assets
{
namespace common
{
namespace lightstate
{


//!
//! BlinkingIndicatorSettings
//!
class BlinkingIndicatorSettings : public pc::util::coding::ISerializable
{
public:

    BlinkingIndicatorSettings()
    : m_blinkPeriod{10u}
    {
    }

    SERIALIZABLE(BlinkingIndicatorSettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
          return;
        }
        ADD_UINT32_MEMBER(blinkPeriod);
    }

    vfc::uint32_t m_blinkPeriod;
};

pc::util::coding::Item<BlinkingIndicatorSettings> g_settings("BlinkingIndicator");

namespace
{

osg::Node* findSceneGraphNode(const std::string& f_nodeName, osg::Group* f_graph)
{
    if (f_graph != nullptr)
    {
      pc::util::osgx::NodeFinder l_nodeFinder(f_nodeName);
      f_graph->accept(l_nodeFinder);
      osg::ref_ptr<osg::Node> l_result = l_nodeFinder.getFoundNode();
      return l_result.release();
    }
    return nullptr;
}

} // namespace


///
/// SignalWrapper
///
SignalWrapper::SignalWrapper(vfc::uint32_t f_value)
    : m_value{f_value}
{
}


bool SignalWrapper::update(pc::core::Framework* f_framework)
{
    const vfc::uint32_t l_newValue = querySignal(f_framework);
    if (l_newValue != m_value)
    {
        m_value = l_newValue;
        return true;
    }
    return false;
}


///
/// HeadlightWrapper
///
static const std::unordered_map<std::string, HeadlightWrapper::State> s_valueNameHeadlightWrapperMapping = {
        { "None",     HeadlightWrapper::State::None } ,
        { "LowBeam",  HeadlightWrapper::State::LowBeam },
        { "HighBeam", HeadlightWrapper::State::HighBeam },
        { "Both",     HeadlightWrapper::State::Both },
        { "Daylight", HeadlightWrapper::State::Daylight }
};
vfc::uint32_t HeadlightWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameHeadlightWrapperMapping, f_stringValue);
}


vfc::uint32_t HeadlightWrapper::querySignal(pc::core::Framework* f_framework) const // PRQA S 6043
{
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();
        if (l_lightState != nullptr)
        {
            if ( true == l_lightState->m_Data.m_mainBeamIndication )
            {
                return static_cast<vfc::uint32_t> (State::HighBeam);
            }
            else if ( true == l_lightState->m_Data.m_lowBeamIndication )
            {
                return static_cast<vfc::uint32_t> (State::LowBeam);
            }
            else
            {
                return static_cast<vfc::uint32_t> (State::None);
            }
        }
    }
    return static_cast<vfc::uint32_t> (State::None);
}

///
/// DayWrapper
///
static const std::unordered_map<std::string, DayWrapper::State> s_valueNameDayWrapperMapping = { 
        { "Off", DayWrapper::State::Off },
        { "On",  DayWrapper::State::On },
        { "DayLight_L", DayWrapper::State::DayLight_L},
        { "DayLight_R", DayWrapper::State::DayLight_R}
};
vfc::uint32_t DayWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameDayWrapperMapping, f_stringValue);
}


vfc::uint32_t DayWrapper::querySignal(pc::core::Framework* f_framework) const
{
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_DayLightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();
        if (l_DayLightState != nullptr)
        {
            if ( 1u == l_DayLightState->m_Data.m_daytimeRunningLights )
            {
                return static_cast<vfc::uint32_t>(State::On);
            }
            else
            {
                return static_cast<vfc::uint32_t>(State::Off);
            }
        }
    }
    return static_cast<vfc::uint32_t> (State::Off);
}

///
/// RearPosWrapper
///
static const std::unordered_map<std::string, RearPosWrapper::State> s_valueNameRearPosWrapperMapping = { 
        { "Off", RearPosWrapper::State::Off },
        { "On",  RearPosWrapper::State::On },
};
vfc::uint32_t RearPosWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameRearPosWrapperMapping, f_stringValue);
}


vfc::uint32_t RearPosWrapper::querySignal(pc::core::Framework* f_framework) const
{
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_RearPosState = l_pCustomFramework->m_VehicleLightsReceiver.getData();
        if (l_RearPosState != nullptr)
        {
            if ( 1u == l_RearPosState->m_Data.m_rearPosLightState )
            {
                return static_cast<vfc::uint32_t>(State::On);
            }
            else
            {
                return static_cast<vfc::uint32_t>(State::Off);
            }
        }
    }
    return static_cast<vfc::uint32_t> (State::Off);
}

///
/// BrakeLightWrapper
///
static const std::unordered_map<std::string, BrakeLightWrapper::State> s_valueNameBrakeLightWrapperMapping = { 
        { "Off", BrakeLightWrapper::State::Off },
        { "On",  BrakeLightWrapper::State::On },
};
vfc::uint32_t BrakeLightWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameBrakeLightWrapperMapping, f_stringValue);
}

vfc::uint32_t BrakeLightWrapper::querySignal(pc::core::Framework* f_framework) const
{
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_brakeLightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();
        if (l_brakeLightState != nullptr)
        {
            if ( 1u == l_brakeLightState->m_Data.m_brakeLampOnStatus )
            {
                return static_cast<vfc::uint32_t>(State::On);
            }
            else
            {
                return static_cast<vfc::uint32_t>(State::Off);
            }
        }
    }
    return static_cast<vfc::uint32_t> (State::Off);
}


///
/// ReverseGearWrapper
///
static const std::unordered_map<std::string, ReverseGearWrapper::State> s_valueNameReverseGearWrapperMapping = { 
        { "Off", ReverseGearWrapper::State::Off },
        { "On",  ReverseGearWrapper::State::On },
};
vfc::uint32_t ReverseGearWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameReverseGearWrapperMapping, f_stringValue);
}

vfc::uint32_t ReverseGearWrapper::querySignal(pc::core::Framework* f_framework) const
{
    if (f_framework != nullptr)
    {
      const pc::daddy::GearDaddy* const l_reverseGearState = f_framework->m_gearReceiver.getData();
      if (l_reverseGearState != nullptr)
      {
          if (pc::daddy::GEAR_R == l_reverseGearState->m_Data)
          {
              return static_cast<vfc::uint32_t> (State::On);
          }
      }
      return static_cast<vfc::uint32_t> (State::Off);
    }
    return static_cast<vfc::uint32_t> (State::Off);
}


///
/// IndicatorLeftWrapper
///
static const std::unordered_map<std::string, IndicatorLeftWrapper::State> s_valueNameIndicatorLeftWrapperMapping = { 
        { "Off", IndicatorLeftWrapper::State::Off },
        { "On",  IndicatorLeftWrapper::State::On },
};
vfc::uint32_t IndicatorLeftWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameIndicatorLeftWrapperMapping, f_stringValue);
}

vfc::uint32_t IndicatorLeftWrapper::querySignal(pc::core::Framework* f_framework) const
{
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_light = l_pCustomFramework->m_VehicleLightsReceiver.getData();
        if (l_light != nullptr)
        {
            if ( 1u == l_light->m_Data.m_leftIndicatorBlinkState )
            {
                return static_cast<vfc::uint32_t>(State::On);
            }
            else
            {
                return static_cast<vfc::uint32_t>(State::Off);
            }
        }
    }
    return static_cast<vfc::uint32_t> (State::Off);
}


///
/// IndicatorRightWrapper
///
static const std::unordered_map<std::string, IndicatorRightWrapper::State> s_valueNameIndicatorRightWrapperMapping = { 
        { "Off", IndicatorRightWrapper::State::Off },
        { "On",  IndicatorRightWrapper::State::On },
};
vfc::uint32_t IndicatorRightWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameIndicatorRightWrapperMapping, f_stringValue);
}

vfc::uint32_t IndicatorRightWrapper::querySignal(pc::core::Framework* f_framework) const
{
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_light = l_pCustomFramework->m_VehicleLightsReceiver.getData();
        if (l_light != nullptr)
        {
            if ( 2u == l_light->m_Data.m_rightIndicatorBlinkState )
            {
                return static_cast<vfc::uint32_t>(State::On);
            }
            else
            {
                return static_cast<vfc::uint32_t>(State::Off);
            }
        }
    }
    return static_cast<vfc::uint32_t> (State::Off);
}


///
/// IndicatorWrapper
///
static const std::unordered_map<std::string, IndicatorWrapper::State> s_valueNameIndicatorWrapperMapping = { 
        { "Idle",  IndicatorWrapper::State::Idle },
        { "Left",  IndicatorWrapper::State::Left },
        { "Right", IndicatorWrapper::State::Right },
        { "Warn",  IndicatorWrapper::State::Warn }
};
vfc::uint32_t IndicatorWrapper::toValue(const std::string& f_stringValue) const
{
    return findOrInvalid(s_valueNameIndicatorWrapperMapping, f_stringValue);
}

vfc::uint32_t IndicatorWrapper::querySignal(pc::core::Framework* f_framework) const
{
    if (f_framework != nullptr)
    {
      cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
      if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
      {
          const cc::daddy::CustomVehicleLightsDaddy* const l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();
          if (nullptr != l_pCustomData)
          {
              if (1u == l_pCustomData->m_Data.m_hazardLightState)
              {
                  return static_cast<vfc::uint32_t> (State::Warn);
              }
          }
          return static_cast<vfc::uint32_t> (State::Idle);
      }
      const pc::daddy::IndicatorStateDaddy* const l_indicatorState = f_framework->m_indicatorStateReceiver.getData();
      if (l_indicatorState != nullptr)
      {
          switch (l_indicatorState->m_Data)
          {
              case pc::daddy::INDICATOR_OFF:
                  {return static_cast<vfc::uint32_t> (State::Idle);}
              case pc::daddy::INDICATOR_LEFT:
                  {return static_cast<vfc::uint32_t> (State::Left);}
              case pc::daddy::INDICATOR_RIGHT:
                  {return static_cast<vfc::uint32_t> (State::Right);}
              case pc::daddy::INDICATOR_WARN:
                  {return static_cast<vfc::uint32_t> (State::Warn);}
              default: {break;}
          }
      }
      return static_cast<vfc::uint32_t> (State::Idle);
    }
    return static_cast<vfc::uint32_t> (State::Idle);
}


///
/// SideIndicatorWrapper
///
static const std::unordered_map<std::string, SideIndicatorWrapper::State> s_valueNameSideIndicatorWrapperMapping = { 
    { "Idle",  SideIndicatorWrapper::State::Idle },
    { "Left",  SideIndicatorWrapper::State::Left },
    { "Right", SideIndicatorWrapper::State::Right },
    { "Warn",  SideIndicatorWrapper::State::Warn }
  };
vfc::uint32_t SideIndicatorWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameSideIndicatorWrapperMapping, f_stringValue);
}


vfc::uint32_t SideIndicatorWrapper::querySignal(pc::core::Framework* f_framework) const // PRQA S 6043
{
    if (f_framework != nullptr)
    {
      vfc::uint32_t l_indicator = 0u;
      cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework);
      if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
      {
          const cc::daddy::CustomVehicleLightsDaddy* const l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();

          if (nullptr != l_pCustomData)
          {
              if (1u == l_pCustomData->m_Data.m_hazardLightState)
              {
                  l_indicator = static_cast<vfc::uint32_t> (State::Warn);
              }
              else
              {
                  const pc::daddy::IndicatorStateDaddy* const l_indicatorState = f_framework->m_indicatorStateReceiver.getData();
                  if (l_indicatorState != nullptr)
                  {
                      switch (l_indicatorState->m_Data)
                      {
                      case pc::daddy::INDICATOR_OFF:
                      {
                        l_indicator = static_cast<vfc::uint32_t> (State::Idle);
                        break;
                      }
                      case pc::daddy::INDICATOR_LEFT:
                      {
                        l_indicator = static_cast<vfc::uint32_t> (State::Left);
                        break;
                      }
                      case pc::daddy::INDICATOR_RIGHT:
                      {
                        l_indicator = static_cast<vfc::uint32_t> (State::Right);
                        break;
                      }
                      case pc::daddy::INDICATOR_WARN:
                      {
                        l_indicator = static_cast<vfc::uint32_t> (State::Warn);
                        break;
                      }
                      default:
                      {
                        break;
                      }
                      }
                  }
              }
          }
      }

      static vfc::uint32_t l_progress;
      if (l_indicator != static_cast<vfc::uint32_t> (State::Idle))
      {
          l_progress += 1u;
          if (l_progress <= g_settings->m_blinkPeriod)
          {
              // toggle off
              l_indicator = static_cast<vfc::uint32_t> (State::Idle);
          }
          else if (l_progress <= 2u * g_settings->m_blinkPeriod)
          {
              // toggle on
          }
          else if (l_progress >= 2u * g_settings->m_blinkPeriod)
          {
              // reset
              l_progress = 0u;
          }
          else
          {
              // Do nothing
          }
      }

      return l_indicator;
    }
    return 0u;
}

///
/// CornerWrapper
///
static const std::unordered_map<std::string, CornerWrapper::State> s_valueNameCornerWrapperMapping = { 
    { "Idle", CornerWrapper::State::Idle },
    { "Left",  CornerWrapper::State::Left },
    { "Right",  CornerWrapper::State::Right },
};
vfc::uint32_t CornerWrapper::toValue(const std::string& f_stringValue) const
{
  return findOrInvalid(s_valueNameCornerWrapperMapping, f_stringValue);
}


vfc::uint32_t CornerWrapper::querySignal(pc::core::Framework* f_framework) const
{
  cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(f_framework); // PRQA S 3076
  if ( l_pCustomFramework->m_driverSteeringWheelAngleReceiver.isConnected() )
  {
  const cc::daddy::DriverSteeringWheelAngleDaddy_t* const l_pData = l_pCustomFramework->m_driverSteeringWheelAngleReceiver.getData();

  if (nullptr != l_pData)
    {
      const vfc::float32_t l_value = l_pData->m_Data;

      if ( isLess(l_value, -30.0f) )
      {
        return static_cast<vfc::uint32_t>(State::Right);
      }
      else if (isGreater(l_value, 30.0f))
      {
        return static_cast<vfc::uint32_t>(State::Left);
      }
      else
      {
        return static_cast<vfc::uint32_t> (State::Idle);
      }
    }
  }
  return static_cast<vfc::uint32_t> (State::Idle);
}


///
/// SignalWrapperRegistry
///
SignalWrapperRegistry::SignalWrapperRegistry(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_signalWrappers()
{
}


bool SignalWrapperRegistry::update()
{
  bool l_signalsChanged = false;
  for (auto l_itr = m_signalWrappers.begin(); l_itr != m_signalWrappers.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
  {
    if (l_itr->second)
    {
      l_signalsChanged = (l_itr->second->update(m_framework) || l_signalsChanged);
    }
  }
  return l_signalsChanged;
}


void SignalWrapperRegistry::registerSignal(const std::string& f_name, const std::shared_ptr<SignalWrapper>& f_singalWrapper)
{
  m_signalWrappers[f_name] = f_singalWrapper;
}


std::shared_ptr<SignalWrapper> SignalWrapperRegistry::getSignalWrapper(const std::string& f_name) const
{
  const auto l_res = m_signalWrappers.find(f_name);
  if (l_res != m_signalWrappers.end())
  {
    return l_res->second;
  }
  return std::shared_ptr<SignalWrapper>(nullptr);
}


///
/// SignalReq
///
SignalReq::SignalReq(
  const std::shared_ptr<SignalWrapper>& f_signal,
  vfc::uint32_t f_referenceValue,
  bool f_not)
  : m_signal{f_signal}
  , m_referenceValue{f_referenceValue}
  , m_not{f_not}
{
}


bool SignalReq::check() const
{
  return (static_cast<vfc::uint32_t>(m_referenceValue == m_signal->getValue())) != static_cast<vfc::uint32_t>(m_not);
}


///
/// Condition
///
void Condition::addSignalReq(const std::shared_ptr<SignalReq>& f_item)
{
  m_signalReqs.push_back(f_item);
}


bool Condition::evaluate() const
{
  for (const auto& l_req : m_signalReqs)
  {
    if (l_req)
    {
      if (!l_req->check())
      {
        return false; // logic AND if a single requirement is false
      }
    }
  }
  return true;
}


///
/// LightNode
///
LightNode::LightNode(osg::Node* f_lightNode)
  : m_lightNode{f_lightNode}
  , m_conditions()
{
}


void LightNode::addCondition(const std::shared_ptr<Condition>& f_condition)
{
  m_conditions.push_back(f_condition);
}


void LightNode::update()
{
  if (!m_lightNode.valid())
  {
    return;
  }

  bool l_result = false;
  for (const auto& l_cond : m_conditions)
  {
    if (l_cond->evaluate()) // logical OR a single condition is sufficient
    {
      l_result = true;
      break;
    }
  }

  if (l_result)
  {
    m_lightNode->setNodeMask(~0u);
  }
  else
  {
    m_lightNode->setNodeMask(0u);
  }
}


///
/// LightNodeUpdateCallback
///
LightNodeUpdateCallback::LightNodeUpdateCallback(const std::shared_ptr<SignalWrapperRegistry>& f_registry)
  : m_registry{f_registry}
  , m_lightNodes{}
{
}


void LightNodeUpdateCallback::operator() (osg::Node* f_node, osg:: NodeVisitor* f_nv)
{
  if (m_registry->update())
  {
    for (auto& l_lightNode : m_lightNodes)
    {
      l_lightNode->update();
    }
  }
  traverse(f_node, f_nv);
}


void LightNodeUpdateCallback::addLightNode(const std::shared_ptr<LightNode>& f_lightNode)
{
  m_lightNodes.push_back(f_lightNode);
}


///
/// LightStateJsonParser
///
namespace
{
constexpr const char* JSON_LIGHTNODES_STR  = "LightNodes";
constexpr const char* JSON_CONDITIONS_STR  = "Conditions";
constexpr const char* JSON_SIGNALREQS_STR  = "SignalRequirements";
constexpr const char* JSON_NAME_STR        = "Name";
constexpr const char* JSON_VALUE_STR       = "Value";
constexpr const char* JSON_VALUENOT_STR    = "ValueNot";
constexpr const char* JSON_HINT_STR        = "Hint";


bool checkMember(
  const rapidjson::Value& f_jsonObject,
  const std::string&      f_memberName,
  const rapidjson::Type   f_expectedType)
{
  if (!f_jsonObject.HasMember(f_memberName.c_str()))
  {
    return false;
  }
  if (f_expectedType != f_jsonObject[f_memberName.c_str()].GetType())
  {
    return false;
  }
  return true;
}


bool parseSignalRequirements(
  const rapidjson::Value& f_jsonObject,
  const std::shared_ptr<Condition>& f_condition,
  const LightNodeUpdateCallback* f_callback)
{
  if (f_callback == nullptr)
  {
      return false;
  }
   // Get the signal name
  if (!checkMember(f_jsonObject, JSON_NAME_STR, rapidjson::kStringType))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() Member \"" << JSON_NAME_STR <<
      "\" could not be found or is not a string");
    return false;
  }

  const std::string l_signalName = f_jsonObject[JSON_NAME_STR].GetString();
  auto l_signalWrapper = f_callback->getRegistry()->getSignalWrapper(l_signalName);
  if (l_signalWrapper == nullptr)
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() \"" << l_signalName <<
      "\" does not name a registred signal");
    return false;
  }

  // Check for the keys "Value" or "ValueNot"
  bool l_valueNot = false;
  if (!checkMember(f_jsonObject, JSON_VALUE_STR, rapidjson::kStringType))
  {
    l_valueNot = true;
    if(!checkMember(f_jsonObject, JSON_VALUENOT_STR, rapidjson::kStringType))
    {
      // pf code. #code looks fine
      XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() Attribute \"" << JSON_VALUE_STR <<
        "\" or " << "\"" << JSON_VALUENOT_STR << " \" is missing or is not a string value");
      return false;
    }
  }
  // Get the expected value
  const std::string l_signalValueStr = f_jsonObject[l_valueNot ? JSON_VALUENOT_STR : JSON_VALUE_STR].GetString();
  const vfc::uint32_t l_signalValue = l_signalWrapper->toValue(l_signalValueStr);
  if (l_signalValue == SignalWrapper::INVALID_VALUE)
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignalRequirements() The value \"" << l_signalValueStr <<
      "\" does not map to a valid state of the \"" << l_signalName << "\" signal");
    return false;
  }

  // Add the signal name and the expected value to a signal request object
  const std::shared_ptr<SignalReq> l_signalReq = std::make_shared<SignalReq>(l_signalWrapper, l_signalValue, l_valueNot); // PRQA S 4126
  f_condition->addSignalReq(l_signalReq);
  // pf code. #code looks fine
  // XLOG_DEBUG_OS(g_AppContext) << "LightStateJsonParser::parseSignalRequirements() Added \"" << l_signalName <<
  //   "\" " << (l_valueNot ? "!=" : "==") << " \"" << l_signalValueStr << "\"" << XLOG_ENDL;
  return true;
}


bool parseSignals(
  const rapidjson::Value& f_jsonObject,
  const std::shared_ptr<Condition>& f_condition,
  const LightNodeUpdateCallback* f_callback)
{
  // Check if a condition is valid
  if (!checkMember(f_jsonObject, JSON_SIGNALREQS_STR, rapidjson::kArrayType))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseSignals(): Member \"" << JSON_SIGNALREQS_STR <<
      "\" could not be found or is not an array");
    return false;
  }
  // Get the signals requirements and walk through them
  const rapidjson::Value& l_jsonSignals = f_jsonObject[JSON_SIGNALREQS_STR];
  for (rapidjson::Value::ConstValueIterator l_it = l_jsonSignals.Begin(); l_it != l_jsonSignals.End(); ++l_it)
  {
    if (!parseSignalRequirements(*l_it, f_condition, f_callback))
    {
      return false;
    }
  }
  return true;
}


bool parseConditions(
  const rapidjson::Value&  f_jsonObject,
  const std::shared_ptr<LightNode>& f_lightNode,
  const LightNodeUpdateCallback* f_callback)
{
  // Check for array of conditions
  if (!checkMember(f_jsonObject, JSON_CONDITIONS_STR, rapidjson::kArrayType))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser::parseConditions(): Attribute \"" << JSON_CONDITIONS_STR <<
      "\" could not be found or is not an array");
    return false;
  }
  // Get the conditions and walk through them
  const rapidjson::Value& l_jsonConditions = f_jsonObject[JSON_CONDITIONS_STR];
  for (rapidjson::Value::ConstValueIterator l_it = l_jsonConditions.Begin(); l_it != l_jsonConditions.End(); ++l_it)
  {
    const auto l_condition = std::make_shared<Condition>();
    if (!parseSignals(*l_it, l_condition, f_callback))
    {
      return false;
    }
    f_lightNode->addCondition(l_condition);
  }
  return true;
}


bool parseLightNode(
  LightStateJsonParser& f_parser,
  const rapidjson::Value&  f_jsonObject,
  LightNodeUpdateCallback* f_callback,
  osg::Group* f_sceneGraph)
{
  if (f_callback == nullptr)
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: LightNodeUpdateCallback pointer is invalid!");
    return false;
  }

  // Check for the string attribute "Name"
  if (!checkMember(f_jsonObject, JSON_NAME_STR, rapidjson::kStringType))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: Attribute \"Name\" could not be found or is not a string");
    return false;
  }
  const std::string l_nodeName = f_jsonObject[JSON_NAME_STR].GetString();
  // Try to find the light node in the scene graph and setup a proxy for updating it
  osg::Node* l_sceneGraphNode = findSceneGraphNode(l_nodeName, f_sceneGraph);
  if (l_sceneGraphNode != nullptr)
  {
    const auto l_lightNode = std::make_shared<LightNode>(l_sceneGraphNode);
    if (!parseConditions(f_jsonObject, l_lightNode, f_callback))
    {
      return false;
    }
    f_callback->addLightNode(l_lightNode);
    // check for hints
    if (checkMember(f_jsonObject, JSON_HINT_STR, rapidjson::kStringType))
    {
      const std::string l_hint = f_jsonObject[JSON_HINT_STR].GetString();
      f_parser.onHint(l_hint, l_sceneGraphNode);
    }
    // pf code. #code looks fine
    XLOG_DEBUG(g_AppContext, "LightStateJsonParser:: successfully parsed light node \"" << l_nodeName << "\"");
  }
  else
  {
    // pf code. #code looks fine
    XLOG_WARN(g_AppContext, "LightStateJsonParser: The light node \"" << l_nodeName << "\" could not be found in the vehicle model");
  }
  return true;
}

} // namespace


LightStateJsonParser::LightStateJsonParser(const std::shared_ptr<SignalWrapperRegistry>& f_registry)
  : m_registry{f_registry}
{
}


LightNodeUpdateCallback* LightStateJsonParser::parse(const std::string& f_filename, osg::Group* f_sceneGraph)
{
  // Read JSON file
  std::ifstream l_jsonFile(f_filename);
  if (!l_jsonFile.is_open())
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: File can't be opened: \"" <<
      f_filename << "\". Parsing failed!");
    return nullptr;
  }

  rapidjson::IStreamWrapper l_streamWrapper(l_jsonFile);

  // Parse JSON
  rapidjson::Document l_doc;
  l_doc.ParseStream(l_streamWrapper); // PRQA S 3803

  // Check the validity of the JSON document
  if (l_doc.HasParseError())
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: Invalid JSON: \"" <<
      f_filename << "\". Parsing failed!");
    return nullptr;
  }

  if (!checkMember(l_doc, JSON_LIGHTNODES_STR, rapidjson::kArrayType))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "LightStateJsonParser: JSON file \"" <<
      f_filename << "\" has unexpected structure");
    return nullptr;
  }

  // Create the whole structure
  osg::ref_ptr<LightNodeUpdateCallback> l_callback = new LightNodeUpdateCallback(m_registry);

  // Get the array from the JSON
  const rapidjson::Value& jsonLightNodes = l_doc[JSON_LIGHTNODES_STR].GetArray();
  for (rapidjson::Value::ConstValueIterator it = jsonLightNodes.Begin(); it != jsonLightNodes.End(); ++it)
  {
    if (!parseLightNode(*this, *it, l_callback.get(), f_sceneGraph))
    {
      return nullptr;
    }
  }
  // pf code. #code looks fine
  XLOG_INFO(g_AppContext, "LightStateJsonParser: Light States parsed successfully from \"" <<
    f_filename << "\"");

  return l_callback.release();
}


void LightStateJsonParser::onHint(const std::string& /* f_hint */, osg::Node* /* f_node */)
{
  // empty per default
}



} // namespace lightstate
} // namespace common
} // namespace assets
} // namespace cc

