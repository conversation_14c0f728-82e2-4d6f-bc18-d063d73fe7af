//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#ifndef CC_ASSETS_SETTINGPAGE_ENTRYBUTTON_H
#define CC_ASSETS_SETTINGPAGE_ENTRYBUTTON_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/settingpageoverlay/inc/SettingPageOverlay.h"

namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

class SettingPageEntryButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(SettingPageEntryButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(cc::assets::button::ButtonTexturePath, buttonTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
    }

    cc::assets::button::ButtonTexturePath m_buttonTexture;
    osg::Vec2f                            m_horiPos = osg::Vec2f(0.0f, 100.0f);
};

class SettingPageEntryButton : public cc::assets::button::Button
{
public:
    SettingPageEntryButton(
        cc::core::AssetId                                  f_assetId,
        pc::core::Framework*                               f_framework,
        cc::assets::settingpageoverlay::SettingPageSwitch* f_settingPageSwtich,
        osg::Camera*                                       f_referenceView);

protected:
    void update() override;

private:
    void onInvalid() override;
    void onUnavailable() override;
    void onAvailable() override;
    void onPressed() override;
    void onReleased() override;

private:
    pc::core::Framework*                               m_framework;
    bool                                               m_settingPageEnabledState;
    cc::assets::settingpageoverlay::SettingPageSwitch* m_settingPageSwitch;
    bool                                               m_showStatus;
};

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_SETTINGPAGE_ENTRYBUTTON_H