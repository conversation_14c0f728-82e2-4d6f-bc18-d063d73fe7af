//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/SystemConf.h"

#include "cc/assets/button/inc/EllipticalSlidingBar.h"
#include "cc/core/inc/CustomFramework.h"
// #include "cc/imgui/inc/imgui_manager.h"
#include "cc/util/logging/inc/LoggingContexts.h"

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ViewChangeButton.h"
#include <fstream>
#include <iostream>
#include <osg/Geometry>
#include <osgText/Text>

using pc::util::logging::g_AppContext;

// #define GET_PORT_DATA(dataDaddy, port, PortHaveDataFlag) \
//     auto const dataDaddy = port.getData(); \
//     const bool PortHaveDataFlag = (dataDaddy != nullptr);\
//     if (dataDaddy == nullptr) { XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n"); }

namespace cc
{
namespace assets
{
namespace button
{
static pc::util::coding::Item<EllipticalSlidingBarSettings> g_ellipticalSlidingBarSettings("EllipticalSlidingBar");
static pc::util::coding::Item<ViewChangeOverlaySetting> g_viewChangeOverlaySettings("ViewChangeOverlay");

EllipticalSlidingBar::EllipticalSlidingBar(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : cc::assets::button::ButtonGroup(f_assetId)
    , m_referenceView{f_referenceView}
    , m_viewChangeOverlay{nullptr}
    , m_framework{f_framework}
    , m_buttonArea{BUTTON_INVALID}
    , m_y{0.0f}
    , m_slideChangeLimit{100.0f}
    , m_moveChangeLimit{g_ellipticalSlidingBarSettings->m_moveChangeLimit}
    , m_isAnimating(false)
    , m_a{0}
    , m_b{0}
    , m_midX{0}
    , m_midY{0}
    , m_width{0}
{
    if ((f_framework != nullptr) && (f_referenceView != nullptr))
    {
        setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
        setCullingActive(false);

        m_viewChangeOverlay=new ViewChangeOverlay(cc::core::AssetId::EASSETS_VIEW_CHANGE_OVERLAY,f_framework->asCustomFramework(),f_referenceView);

        addButton(m_viewChangeOverlay);
    }
}

void EllipticalSlidingBar::traverse(osg::NodeVisitor& f_nv)
{
    update();
    osg::Group::traverse(f_nv);
}

void EllipticalSlidingBar::update()
{
    if( m_framework->asCustomFramework()->m_animationState_ReceiverPort.hasData() )
    {
        const  cc::daddy::ViewAnimationCompleted_t* const l_pData = m_framework->asCustomFramework()->m_animationState_ReceiverPort.getData() ;
        m_isAnimating=((EAnimationState)l_pData->m_Data.m_state==EAnimationState_ANIM_ONGOING);
    }
    updateRealCamera();
    updateCamera();
}
void EllipticalSlidingBar::getPointOnEllipse(vfc::float32_t ratio, vfc::float32_t& f_x, vfc::float32_t& f_y)
{

    const vfc::float32_t l_PI = 3.1415926f;

    const vfc::float32_t l_x0 = g_ellipticalSlidingBarSettings->m_midX;
    const vfc::float32_t l_y0 = g_ellipticalSlidingBarSettings->m_midY;

    const vfc::float32_t l_a = g_ellipticalSlidingBarSettings->m_a;
    const vfc::float32_t l_b = g_ellipticalSlidingBarSettings->m_b;

    const vfc::float32_t theta = (2.0f * ratio-1.0f) * l_PI;

    f_x = l_x0 + l_a * std::cos(theta);
    f_y = l_y0 + l_b * std::sin(theta);
}

void EllipticalSlidingBar::updateRealCamera()
{
    if ( m_framework->asCustomFramework()->m_cameraPosition_ReceiverPort.isConnected() )
    {
        static vfc::int32_t l_ctr = -1 ;
        const auto* const l_pData = m_framework->asCustomFramework()->m_cameraPosition_ReceiverPort.getData().front() ;
        if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
        {
            this->m_y=l_pData->m_Data.hemisphere3D.y();
            this->m_angleRatio=this->m_y/m_maxY;
            vfc::float32_t l_x=0.0f,l_y=0.0f;
            getPointOnEllipse(this->m_angleRatio,l_x,l_y);
            this->m_viewChangeOverlay->setY(this->m_y);
            this->m_viewChangeOverlay->setIconCenter(l_x,l_y);
                SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID_NO_CHANGE);
            l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
        }
    }
}

bool EllipticalSlidingBar::checkTouchInsideResponseArea(const vfc::float32_t f_x,const vfc::float32_t f_y)
{
    const vfc::float32_t l_x0 = g_ellipticalSlidingBarSettings->m_midX;
    const vfc::float32_t l_y0 = g_ellipticalSlidingBarSettings->m_midY;

    const vfc::float32_t l_a = g_ellipticalSlidingBarSettings->m_a;
    const vfc::float32_t l_b = g_ellipticalSlidingBarSettings->m_b;

    const vfc::float32_t l_R=100.0f;

    const vfc::float32_t outerA = l_a + l_R;
    const vfc::float32_t outerB = l_b + l_R;
    const vfc::float32_t outerF = ((f_x - l_x0) * (f_x - l_x0) / (outerA * outerA)) + ((f_y - l_y0) * (f_y - l_y0) / (outerB * outerB)) - 1.0f;

    const vfc::float32_t innerA = l_a - l_R;
    const vfc::float32_t innerB = l_b - l_R;
    const vfc::float32_t innerF = ((f_x - l_x0) * (f_x - l_x0) / (innerA * innerA)) + ((f_y - l_y0) * (f_y - l_y0) / (innerB * innerB)) - 1.0f;

    return innerF > 0.0f && outerF < 0.0f;
}

bool EllipticalSlidingBar::checkTouchInsideButtonResponseArea(const vfc::float32_t f_x,const vfc::float32_t f_y,const osg::Vec2f& f_buttonCenter,const osg::Vec2f& f_responseArea)
{
    const vfc::float32_t lowerX = (f_buttonCenter.x() - f_responseArea.x() * 0.5f);
    const vfc::float32_t upperX = (f_buttonCenter.x() + f_responseArea.x() * 0.5f);
    const vfc::float32_t lowerY = (f_buttonCenter.y() - f_responseArea.y() * 0.5f);
    const vfc::float32_t upperY = (f_buttonCenter.y() + f_responseArea.y() * 0.5f);
    if ((lowerX <= f_y) && (f_y <= upperX) && (lowerY <= f_x) && (f_x <= upperY))
    {
        return true;
    }
    return false;
}

void EllipticalSlidingBar::closestPointOnEllipse(vfc::float32_t pointX, vfc::float32_t pointY, vfc::float32_t& ellipseX, vfc::float32_t& ellipseY) {
    const vfc::float32_t l_x0 = g_ellipticalSlidingBarSettings->m_midX;
    const vfc::float32_t l_y0 = g_ellipticalSlidingBarSettings->m_midY;
    const vfc::float32_t l_a = g_ellipticalSlidingBarSettings->m_a;
    const vfc::float32_t l_b = g_ellipticalSlidingBarSettings->m_b;

    const vfc::int32_t l_MAX_ITERATIONS = 100;
    const vfc::float32_t l_TOLERANCE = static_cast<vfc::float32_t>(1e-6);

    ellipseX = pointX;
    ellipseY = pointY;

    for (vfc::int32_t i = 0; i < l_MAX_ITERATIONS; ++i) {
        const vfc::float32_t dx = ellipseX - l_x0;
        const vfc::float32_t dy = ellipseY - l_y0;
        const vfc::float32_t u = dx / l_a;
        const vfc::float32_t v = dy / l_b;
        const vfc::float32_t r = std::sqrt(u * u + v * v);

        if (std::abs(r - 1.0f) < l_TOLERANCE) {
            break;
        }

        const vfc::float32_t scale = 1.0f / r;
        ellipseX = l_x0 + dx * scale;
        ellipseY = l_y0 + dy * scale;
    }
}

vfc::float32_t EllipticalSlidingBar::calculateAngleRatio(vfc::float32_t pointX, vfc::float32_t pointY) {
    const vfc::float32_t l_PI = 3.1415926f;

    const vfc::float32_t l_x0 = g_ellipticalSlidingBarSettings->m_midX;
    const vfc::float32_t l_y0 = g_ellipticalSlidingBarSettings->m_midY;

    const vfc::float32_t l_a = g_ellipticalSlidingBarSettings->m_a;
    const vfc::float32_t l_b = g_ellipticalSlidingBarSettings->m_b;

    const vfc::float32_t dx = pointX - l_x0;
    const vfc::float32_t dy = pointY - l_y0;

    const vfc::float32_t theta = std::atan2(dy / l_b, dx / l_a);

    const vfc::float32_t ratio = 0.5f * (theta / l_PI+1.0f);

    return ratio;
}

bool EllipticalSlidingBar::checkAllButtons(const vfc::float32_t f_x,const vfc::float32_t f_y)
{
    // EGear        l_curGear        = EGear_Init;
    // if (m_framework->m_gearReceiver.isConnected())
    // {
    //     const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
    //     if (nullptr != l_pData)
    //     {
    //         l_curGear = l_pData->m_Data;
    //     }
    // }

    //Left
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveLeftButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_LT;
        return true;
    }
    //FL
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveFrontLeftButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_FL;
        return true;
    }
    //Front
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveFrontButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_FT;
        return true;
    }
    //FR
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveFrontRightButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_FR;
        return true;
    }
    //Right
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveRightButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_RT;
        return true;
    }
    //RR
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveRearRightButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_RR;
        return true;
    }
    //Rear
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveRearButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_R;
        return true;
    }
    //RL
    if(checkTouchInsideButtonResponseArea(f_x,f_y,viewchangebutton::g_PerspectiveRearLeftButtonSettings.get()->m_horiPos,m_viewChangeOverlay->getIconSize()))
    {
        m_buttonArea=BUTTON_RL;
        return true;
    }
    return false;
}

bool EllipticalSlidingBar::checkViewChangeForButton(const vfc::float32_t f_x,const vfc::float32_t f_y,vfc::uint8_t f_touchEvent,const vfc::float32_t f_angleY)
{
    if (true == m_framework->asCustomFramework()->m_displayedView_ReceiverPort.isConnected())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_pDataDaddy = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
        if (nullptr != l_pDataDaddy)
        {
            switch (static_cast<EScreenID>(l_pDataDaddy->m_Data))
            {
                case EScreenID::EScreenID_PERSPECTIVE_PLE:
                case EScreenID::EScreenID_PERSPECTIVE_FL:
                case EScreenID::EScreenID_PERSPECTIVE_PFR:
                case EScreenID::EScreenID_PERSPECTIVE_FR:
                case EScreenID::EScreenID_PERSPECTIVE_PRI:
                case EScreenID::EScreenID_PERSPECTIVE_RR:
                case EScreenID::EScreenID_PERSPECTIVE_PRE:
                case EScreenID::EScreenID_PERSPECTIVE_RL:
                {
                    break;
                }
                default:
                {
                    return true;
                }
            }
        }
    }

    static const vfc::float32_t s_lastX=-1.0f;
    static const vfc::float32_t s_lastY=-1.0f;
    static const vfc::uint8_t   s_lastTouch=60u;
    // static vfc::float32_t s_lastAngleY=-1.0f;
    static const bool           s_waitingRelease=false;
    static ButtonArea     s_buttonArea= BUTTON_INVALID;
    if(static_cast<TouchStatus>(f_touchEvent)==TOUCH_DOWN||static_cast<TouchStatus>(f_touchEvent)==TOUCH_MOVE)
    {
        if(std::abs(m_y-f_angleY)>m_slideChangeLimit && !( f_angleY>m_maxY-m_slideChangeLimit&&m_y<m_slideChangeLimit || m_y>m_maxY-m_slideChangeLimit&&f_angleY<m_slideChangeLimit ))
        {
            if(checkAllButtons(f_x,f_y))
            {
                s_buttonArea=m_buttonArea;
                return true;
            }
        }
        else
        {
            s_buttonArea=m_buttonArea;
            return false;
        }
    }
    else if(static_cast<TouchStatus>(f_touchEvent)==TOUCH_UP)
    {
        if(checkAllButtons(f_x,f_y)&&s_buttonArea!=BUTTON_INVALID&&s_buttonArea==m_buttonArea)
        {
            EScreenID l_screenID=EScreenID_NO_CHANGE;
            switch (m_buttonArea)
            {
            case BUTTON_LT:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_PLE;
                break;
            }
            case BUTTON_FL:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_FL;
                break;
            }
            case BUTTON_FT:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_PFR;
                break;
            }
            case BUTTON_FR:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_FR;
                break;
            }
            case BUTTON_RT:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_PRI;
                break;
            }
            case BUTTON_RR:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_RR;
                break;
            }
            case BUTTON_R:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_PRE;
                break;
            }
            case BUTTON_RL:
            {
                l_screenID = EScreenID::EScreenID_PERSPECTIVE_RL;
                break;
            }
            default:
            {
                break;
            }
            }
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, l_screenID);
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_CameraPositionChange_SenderPort,true);
            this->m_y=this->getPreviewidToAxis(l_screenID);
            this->m_angleRatio=this->m_y/m_maxY;
            vfc::float32_t l_x=0.0f,l_y=0.0f;
            getPointOnEllipse(this->m_angleRatio,l_x,l_y);
            this->m_viewChangeOverlay->setY(this->m_y);
            this->m_viewChangeOverlay->setIconCenter(l_x,l_y);
            m_buttonArea=BUTTON_INVALID;
            s_buttonArea=m_buttonArea;
            return true;
        }
    }
    else{}

    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID_NO_CHANGE);
    s_buttonArea=m_buttonArea;
    return false;
}

void EllipticalSlidingBar::updateCamera()
{
    // static ESVSViewMode s_lastViewMode=ESVSViewMode::ESVSViewMode_VM_Default;
    // ESVSViewMode l_curViewMode=ESVSViewMode::ESVSViewMode_VM_Default;
    // if ( m_framework->asCustomFramework()->m_HUselSVSMode_ReceiverPort.isConnected())
    // {
    //     const auto l_pData = m_framework->asCustomFramework()->m_HUselSVSMode_ReceiverPort.getData();
    //     if (nullptr != l_pData)
    //     {
    //         l_curViewMode=static_cast<ESVSViewMode>(l_pData->m_Data);
    //     }
    // }
    m_y=m_viewChangeOverlay->m_y;
    static bool s_lastViewIs3D=false;
    bool l_curViewIs3D=false;
    EScreenID l_curScreen= EScreenID::EScreenID_NO_CHANGE;
    static EScreenID s_lastScreen= EScreenID::EScreenID_NO_CHANGE;
    static EScreenID s_lastFRScreen=  EScreenID::EScreenID_PERSPECTIVE_PRE;
    if (true == m_framework->asCustomFramework()->m_displayedView_ReceiverPort.isConnected())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_pDataDaddy = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
        if (nullptr != l_pDataDaddy)
        {
            l_curScreen=static_cast<EScreenID>(l_pDataDaddy->m_Data);
            switch (static_cast<EScreenID>(l_pDataDaddy->m_Data))
            {
                case EScreenID::EScreenID_PERSPECTIVE_PLE:
                case EScreenID::EScreenID_PERSPECTIVE_FL:
                case EScreenID::EScreenID_PERSPECTIVE_PFR:
                case EScreenID::EScreenID_PERSPECTIVE_FR:
                case EScreenID::EScreenID_PERSPECTIVE_PRI:
                case EScreenID::EScreenID_PERSPECTIVE_RR:
                case EScreenID::EScreenID_PERSPECTIVE_PRE:
                case EScreenID::EScreenID_PERSPECTIVE_RL:{ l_curViewIs3D=true;break;}
                default:
                {
                    l_curViewIs3D=false;
                    if(s_lastViewIs3D)
                    {
                        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID_NO_CHANGE);
                    }
                    break;
                }
            }
        }
    }

    const cc::daddy::SVSDisplayedViewmodeGroupDaddy_t* const l_viewmodeGroup = m_framework->asCustomFramework()->m_displayedViewmodeGroup_Receiver.getData();

    if(l_viewmodeGroup->m_Data==EViewModeGroup::VIEWMODE_REAR)
    {
        s_lastFRScreen=EScreenID_PERSPECTIVE_PFR;
    }
    else
    {
        s_lastFRScreen=EScreenID_PERSPECTIVE_PRE;
    }

    static  EGear        s_lastGear  = EGear_Init;
    static  EGear        s_lastRDGear  = EGear_Init;
    EGear                l_curGear     = EGear_Init;
    static bool          s_gearChanged = false;
    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = l_pData->m_Data;
        }
    }
    if(l_curGear==EGear_P)
    {
        s_lastRDGear= EGear_Init;
    }
    if(l_curScreen!=s_lastScreen&&l_curViewIs3D)
    {
        // s_gearChanged=true;
        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID::EScreenID_NO_CHANGE);
    }
    if(s_lastGear!=l_curGear&&l_curViewIs3D)
    {
        s_gearChanged=true;
    }
    bool l_setAlways=false;
    if(s_lastGear==EGear_N&&(l_curGear==EGear_D||l_curGear==EGear_R)&&l_curViewIs3D)
    {
        if(l_curGear==EGear_D&&(this->m_viewChangeOverlay->m_y!=0))
        {
            s_gearChanged=true;
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID::EScreenID_PERSPECTIVE_PRE);
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_CameraPositionChange_SenderPort,true);
            if(l_curScreen==EScreenID::EScreenID_PERSPECTIVE_PRE)
            {
                l_setAlways=true;
            }
        }
        else if(l_curGear==EGear_R&&(this->m_viewChangeOverlay->m_y!=900))
        {
            s_gearChanged=true;
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID::EScreenID_PERSPECTIVE_PFR);
            SEND_PORT(cc::daddy::CustomDaddyPorts::sm_CameraPositionChange_SenderPort,true);
            if(l_curScreen==EScreenID::EScreenID_PERSPECTIVE_PFR)
            {
                l_setAlways=true;
            }
        }
    }
    if(s_lastGear!=EGear_N&&l_curGear==EGear_N&&l_curViewIs3D)
    {
        s_gearChanged=true;
        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, s_lastFRScreen);
        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_CameraPositionChange_SenderPort,true);
        if(l_curScreen==s_lastFRScreen)
        {
            l_setAlways=true;
        }
    }


    if(s_gearChanged)
    {
        if( s_lastScreen!=l_curScreen||l_setAlways)
        {
            this->m_y=this->getPreviewidToAxis(l_curScreen);
            this->m_angleRatio=this->m_y/m_maxY;
            vfc::float32_t l_x=0.0f,l_y=0.0f;
            getPointOnEllipse(this->m_angleRatio,l_x,l_y);
            this->m_viewChangeOverlay->setY(this->m_y);
            this->m_viewChangeOverlay->setIconCenter(l_x,l_y);
            s_gearChanged=false;
        }
    }
    if(s_lastViewIs3D!=l_curViewIs3D)
    {
        switch (l_curScreen)
        {
            case EScreenID::EScreenID_PERSPECTIVE_PFR:
            case EScreenID::EScreenID_PERSPECTIVE_PRE:
            {
                this->m_y=this->getPreviewidToAxis(l_curScreen);
                this->m_angleRatio=this->m_y/m_maxY;
                vfc::float32_t l_x=0.0f,l_y=0.0f;
                getPointOnEllipse(this->m_angleRatio,l_x,l_y);
                this->m_viewChangeOverlay->setY(this->m_y);
                this->m_viewChangeOverlay->setIconCenter(l_x,l_y);
                s_lastViewIs3D=l_curViewIs3D;
                break;
            }
            default:
            {
                break;
            }
        }
        if(!l_curViewIs3D)
        {
            s_lastViewIs3D=l_curViewIs3D;
        }
    }
    if(l_curGear==EGear_D)
    {
        s_lastRDGear=EGear_D;
    }
    else if(l_curGear==EGear_R)
    {
        s_lastRDGear=EGear_R;
    }
    s_lastScreen=l_curScreen;
    s_lastGear=l_curGear;

    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)
    GET_PORT_DATA(touchStatusContainer,m_framework->asCustomFramework()->m_HUTouchTypeReceiver,touchStatusPortHaveData)
    if(!hmiDataPortHaveData||!static_cast<bool>(touchStatusContainer))
    {
        return;
    }
    static vfc::float32_t s_lastX=-1.0f;
    static vfc::float32_t s_lastY=-1.0f;
    static vfc::uint8_t   s_lastTouch=60u;

    const vfc::float32_t l_x     = static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y())-static_cast<vfc::float32_t>(hmiDataContainer->m_Data.m_huY);
    const vfc::float32_t l_y     = static_cast<vfc::float32_t>(hmiDataContainer->m_Data.m_huX);
    const vfc::uint8_t   l_touch = touchStatusContainer->m_Data;

    if((s_lastX!=l_x || s_lastY != l_y || s_lastTouch != l_touch)&&checkTouchInsideResponseArea(l_x,l_y))
    {
        s_lastX=l_x;
        s_lastY=l_y;
        s_lastTouch=touchStatusContainer->m_Data;
        vfc::float32_t l_ellipseX = 0.0f;
        vfc::float32_t l_ellipseY = 0.0f;
        closestPointOnEllipse(l_x,l_y,l_ellipseX,l_ellipseY);

        const vfc::float32_t l_ratio=calculateAngleRatio(l_ellipseX,l_ellipseY);
       //! reserve daddy port
        const vfc::float32_t l_sendY=1800.0f*l_ratio;
        if(checkViewChangeForButton(l_x,l_y,l_touch,l_sendY) || m_isAnimating)
        {
            return;
        }

        m_buttonArea=BUTTON_INVALID;
        if(std::abs(l_sendY-m_y)>m_slideChangeLimit && !( l_sendY>m_maxY-m_slideChangeLimit&&m_y<m_slideChangeLimit || m_y>m_maxY-m_slideChangeLimit&&l_sendY<m_slideChangeLimit ))
        {
            return;
        }
        EGear        l_curGear        = EGear_Init;
        if (m_framework->m_gearReceiver.isConnected())
        {
            const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
            if (nullptr != l_pData)
            {
                l_curGear = l_pData->m_Data;
            }
        }
        // if(l_curGear==EGear_D&&!this->m_viewChangeOverlay->allowShowInGearD(l_sendY)||l_curGear==EGear_R&&!this->m_viewChangeOverlay->allowShowInGearR(l_sendY))
        // {
        //     return;
        // }
        cc::daddy::CamPosAxis2RqDaddy_t& l_rCamPosAxis2RqContainer =
        cc::daddy::CustomDaddyPorts::sm_CamPosAxis2RqDaddySenderPort.reserve() ;

        l_rCamPosAxis2RqContainer.m_Data=l_sendY;
        //! deliver daddy port
        cc::daddy::CustomDaddyPorts::sm_CamPosAxis2RqDaddySenderPort.deliver();

        this->m_viewChangeOverlay->setY(l_sendY);

        this->m_viewChangeOverlay->setIconCenter(l_ellipseX,l_ellipseY);
    }
}

vfc::float32_t EllipticalSlidingBar::getPreviewidToAxis(const EScreenID f_previewid)
{
    switch(f_previewid)
    {
    case EScreenID_PERSPECTIVE_RL :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_RL :
    {
        return 162.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_PLE :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_PLE :
    {
        return 450.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_FL :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_FL :
    {
        return 738.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_PFR :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_PFR :
    {
        return 900.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_FR :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_FR :
    {
        return 1062.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_PRI :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_PRI :
    {
        return 1350.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_RR :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_RR :
    {
        return 1638.0f;
        break;
    }
    case EScreenID_PERSPECTIVE_PRE :
    case EScreenID_PARK_COMPLETED_PERSPECTIVE_PRE :
    {
        return 0.0f;
        break;
    }
    default :
    {
        return 0.0f;
        break;
    }
    }
}


ViewChangeOverlay::ViewChangeOverlay(cc::core::AssetId f_assetId, cc::core::CustomFramework* f_framework, osg::Camera* f_referenceView)
    : Button(f_assetId,f_referenceView)
    , m_icon{nullptr}
    , m_iconCenter{421.0f,314.0f}
    , m_framework{f_framework}
    , m_preViewMode{}
    , m_preGear{}
    , m_screenId{}
    , m_delayCounter{0}
    , m_gearChangedToP{false}
    , m_settings{const_cast<ViewChangeOverlaySetting*>(g_viewChangeOverlaySettings.get())}
    , m_y{0}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
    init();
}
void ViewChangeOverlay::init()
{
    removeIcon(m_icon);
    m_icon = new cc::assets::uielements::RotateIcon{m_settings->m_overlayPath};
    m_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    m_icon->setPosition(m_iconCenter, pc::assets::Icon::UnitType::Pixel);
    m_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    m_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    addIcon(m_icon);
    const auto l_texture = const_cast<osg::Texture2D*>(m_icon->getTexture());
    if (l_texture == nullptr)
    {
        XLOG_WARN(g_AppContext, "DistanceOverlayAsset: texture not found! - createDistanceDigitalIcon");
        return ;
    }
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
}

vfc::float32_t ViewChangeOverlay::calculateRotationAngle(vfc::float32_t f_x, vfc::float32_t f_y) {
    const vfc::float32_t l_PI = 3.1415926f;

    const vfc::float32_t l_x0 = g_ellipticalSlidingBarSettings->m_midX;
    const vfc::float32_t l_y0 = g_ellipticalSlidingBarSettings->m_midY;
    const vfc::float32_t l_a  = g_ellipticalSlidingBarSettings->m_a;
    const vfc::float32_t l_b  = g_ellipticalSlidingBarSettings->m_b;

    // (f_x - l_x0)^2 / a^2 + (f_y - l_y0)^2 / b^2 = 1
    // 2(f_x - l_x0) / a^2 + 2(f_y - l_y0) / b^2 * dy/dx = 0
    // dy/dx = - (b^2 * (f_x - l_x0)) / (a^2 * (f_y - l_y0))
    vfc::float32_t l_tangentSlope = 0.0f;
    if (std::abs(f_y-l_y0)<1e-6) {
        l_tangentSlope = std::numeric_limits<vfc::float32_t>::infinity();
    } else {
        l_tangentSlope = - (l_b * l_b * (f_x - l_x0)) / (l_a * l_a * (f_y - l_y0));
    }

    vfc::float32_t l_normalSlope = 0.0f;
    if (std::abs(l_tangentSlope)<1e-6) {
        l_normalSlope = std::numeric_limits<vfc::float32_t>::infinity();
    } else if (std::isinf(l_tangentSlope)) {
        l_normalSlope = 0.0f;
    } else {
        l_normalSlope = -1.0f / l_tangentSlope;
    }

    const vfc::float32_t dx = -1.0f;
    const vfc::float32_t dy = l_normalSlope;

    vfc::float32_t angle = std::atan2(dy, dx);

    if (angle < 0) {
        angle += 2.0f * l_PI;
    }

    if (f_x < l_x0) {
        angle += l_PI;
        if (angle >= 2.0f * l_PI) {
            angle -= 2.0f * l_PI;
        }
    }
    if(std::abs(f_y-189.0f)<0.001f)
    {
        angle=l_PI*1.5f;
    }
    return angle;
}
void ViewChangeOverlay::setIconCenter(vfc::float32_t f_x,vfc::float32_t f_y)
{
    const cc::assets::button::Button::ButtonState l_state=getState();
    osg::Vec2f l_point={f_y,f_x};
    m_iconCenter.x()=l_point.x();
    m_iconCenter.y()=l_point.y();
    m_icon->setPosition(m_iconCenter, pc::assets::Icon::UnitType::Pixel);
    const vfc::float32_t l_rotateAngle=calculateRotationAngle(f_x,f_y)/3.1415926f*180.0f;
    m_icon->setRotateAngle(l_rotateAngle);
}

void  ViewChangeOverlay::update()
{
    GET_PORT_DATA(displayedViewContainer,m_framework->asCustomFramework()->m_displayedView_ReceiverPort,displayedViewPortHaveData)
    GET_PORT_DATA(viewModeStsContainer,m_framework->asCustomFramework()->m_viewModeStatus_ReceiverPort,viewModeStsPortHaveData)

    EScreenID    l_displayedView  = EScreenID_NO_CHANGE;
    ESVSViewMode l_curViewModeSts = ESVSViewMode_VM_Default;
    EGear        l_curGear        = EGear_Init;
    if (displayedViewPortHaveData)
    {
        l_displayedView = displayedViewContainer->m_Data;
    }

    if (viewModeStsPortHaveData)
    {
        l_curViewModeSts = viewModeStsContainer->m_Data;
    }

    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = l_pData->m_Data;
        }
    }

    const auto touchInsideViewport     = checkTouchInsideViewport();
    const bool touchInsideResponseArea = checkTouchInsideResponseArea();
    const bool viewModeChanged         = (m_preViewMode != l_curViewModeSts);
    const bool gearChanged             = (m_preGear != l_curGear);
    m_preViewMode                = l_curViewModeSts;

    if( m_preGear != EGear_P && l_curGear == EGear_P )
    {
        m_gearChangedToP = true; // Gear Change to P
    }else if (m_preGear == EGear_P &&  l_curGear != EGear_P)
    {
        m_gearChangedToP = false; // Gear Change from P
    }
    else{}

    m_preGear                    = l_curGear;

    const bool allowShowInGearD =(this->allowShowInGearD(m_y) || (gearChanged&&l_curGear==EGear_D));

    const bool allowShowInGearR =(this->allowShowInGearR(m_y) || (gearChanged&&l_curGear==EGear_R));

    ButtonState currentState = getState();

    if (l_curViewModeSts != ESVSViewMode::ESVSViewMode_VM_Perspective)
    {
        currentState = INVALID;
    }
    // else if ((l_curGear == EGear_D && !allowShowInGearD) || (l_curGear == EGear_R && !allowShowInGearR))
    // {
    //     currentState = UNAVAILABLE;
    // }
    else
    {
        switch (currentState)
        {
        case AVAILABLE:
        {
            if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
            {
                currentState = PRESSED;
            }
            else
            {
                if (l_displayedView == getScreenId())
                {
                    currentState = SELECTED;
                }
            }
            break;
        }
        case INVALID:
        case UNAVAILABLE:
        default:
        {
            if ((l_curGear == EGear_D && !allowShowInGearD) || (l_curGear == EGear_R && !allowShowInGearR))
            {
                currentState = UNAVAILABLE;
            }
            else  if ( m_gearChangedToP )
            {
                m_delayCounter++;
                if ( m_delayCounter > m_settings->m_displayDelay)
                {
                    currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
                    m_gearChangedToP = false;
                    m_delayCounter = 0;
                }else
                {
                    // Do nothing
                }
            }
            else
            {
                currentState = AVAILABLE;
            }

            break;
        }
        }
    }
    setState(currentState);

}

} // namespace button
} // namespace assets
} // namespace cc
