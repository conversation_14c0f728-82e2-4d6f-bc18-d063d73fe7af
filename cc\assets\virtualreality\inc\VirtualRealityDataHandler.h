//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityDataHandler.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYDATAHANDLER_H
#define CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYDATAHANDLER_H

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/virtualreality/inc/VirtualRealityObject.h"

namespace cc
{
namespace assets
{
namespace virtualreality
{

class VirtualRealityDataHandlerSettings : public pc::util::coding::ISerializable
{
public:
  VirtualRealityDataHandlerSettings()
    : m_objectConfidenceThreshold(60.0f)
    , m_pedestrianConfidenceThreshold(80.0f)
    , m_objectPositionCovXThreshold(2.0f)
    , m_objectPositionCovYThreshold(1.0f)
    , m_objectVelocityCovXThreshold(3.0f)
    , m_objectVelocityCovYThreshold(2.0f)
  {
  }

  SERIALIZABLE(VirtualRealityDataHandlerSettings)
  {
    ADD_MEMBER(float, objectConfidenceThreshold);
    ADD_MEMBER(float, pedestrianConfidenceThreshold);
    ADD_MEMBER(float, objectPositionCovXThreshold);
    ADD_MEMBER(float, objectPositionCovYThreshold);
    ADD_MEMBER(float, objectVelocityCovXThreshold);
    ADD_MEMBER(float, objectVelocityCovYThreshold);
  }

  float m_objectConfidenceThreshold;
  float m_pedestrianConfidenceThreshold;
  float m_objectPositionCovXThreshold;
  float m_objectPositionCovYThreshold;
  float m_objectVelocityCovXThreshold;
  float m_objectVelocityCovYThreshold;
};


class VirtualRealityDataHandler
{
public:
  VirtualRealityDataHandler(pc::core::Framework* f_framework);

  void updateData();

  void updateDataSlot();

  void updateDataPedestrian();

  void avoidBothSideSlotOverlapUtil(cc::target::common::EParkSlotOrientationType f_orientationType, Position_st &f_currPosition, Position_st &f_preRightPosition, Position_st &f_preLeftPosition, bool &f_leftBit, bool &f_rightBit );

  void avoidOverlapAlgorithm(cc::target::common::EParkSlotOrientationType f_orientationType, Position_st &f_currPosition, Position_st &f_prePosition);

  void getSelectedParkingSpot(const osg::Matrixf& f_MVPmatrix);

protected:
  ~VirtualRealityDataHandler();

private:
  pc::core::Framework* m_framework;
};



} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYDATAHANDLER_H
