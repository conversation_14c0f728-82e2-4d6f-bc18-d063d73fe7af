//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/TrajectoryAssets.h"
#include "cc/assets/trajectory/inc/ActionPoint.h"
#include "cc/assets/trajectory/inc/CoverPlate.h"
#include "cc/assets/trajectory/inc/ExtraOutermostLine.h" // PRQA S 1060
#include "cc/assets/trajectory/inc/ParkingCallback.h"
#include "cc/assets/trajectory/inc/RefLine.h"
// #include "cc/assets/trajectory/inc/TrailerHitchTrajectory.h"
#include "cc/assets/trajectory/inc/TrailerAssistLine.h"
#include "cc/assets/trajectory/inc/WheelTrack.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "vfc/core/vfc_types.hpp"
#include "osg/Texture2D"

namespace cc
{
namespace assets
{
namespace trajectory
{

constexpr vfc::float32_t        g_height                 = 0.005f;
constexpr vfc::uint32_t g_numVerticesWheelTracks = 48u;

TrajectoryParams_st g_trajParams{};
DIDescriptor_st     g_DIDescriptor{};
DIDescriptor_st     g_DIColorfulDescriptor{};

namespace
{

osg::Texture2D* createTexture(osg::Image* f_image)
{
    osg::Texture2D* const l_tex2D = new osg::Texture2D(f_image);
    l_tex2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_tex2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_tex2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_tex2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_tex2D->setResizeNonPowerOfTwoHint(false);
    l_tex2D->setUnRefImageDataAfterApply(true);
    return l_tex2D;
}

} // namespace

OutermostLinesAsset::OutermostLinesAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params,
    const DIDescriptor_st&     f_diDescriptor)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
    , m_left{}
    , m_right{}
{
    m_left = new OutermostLine(f_framework, commontypes::Left_enm, g_height, f_params, f_diDescriptor, 22u, 4u, 25u);
    m_left->setName("OutermostLineLeft");
    osg::Group::addChild(m_left.get()); // PRQA S 3804  // PRQA S 3803

    m_right = new OutermostLine(f_framework, commontypes::Right_enm, g_height, f_params, f_diDescriptor, 22u, 4u, 25u);
    m_right->setName("OutermostLineRight");
    osg::Group::addChild(m_right.get()); // PRQA S 3804  // PRQA S 3803

    // ExtraOutermostLine* l_extra = new ExtraOutermostLine(
    //     f_framework,
    //     g_height,
    //     f_params, 25);
    // l_extra->setName("ExtraOutermostLine");
    // osg::Group::addChild(l_extra);  // PRQA S 3804  // PRQA S 3803

    //! texture is identical for all outermost lines so only create once
    osg::Image*     const l_texImage = m_left->create1DTexture();
    osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth(); // PRQA S 4262 // PRQA S 4264
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_OUTERMOST_LINE, "RenderBin");
}

OutermostLinesAssetColorful::OutermostLinesAssetColorful(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params,
    const DIDescriptor_st&     f_diDescriptor)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
    , m_left{}
    , m_right{}
{
    m_left =
        new OutermostLineColorful(f_framework, commontypes::Left_enm, g_height, f_params, f_diDescriptor, 22u, 4u, 25u);
    m_left->setName("OutermostLineLeft");
    osg::Group::addChild(m_left.get()); // PRQA S 3804  // PRQA S 3803

    m_right = new OutermostLineColorful(
        f_framework, commontypes::Right_enm, g_height, f_params, f_diDescriptor, 22u, 4u, 25u);
    m_right->setName("OutermostLineRight");
    osg::Group::addChild(m_right.get()); // PRQA S 3804  // PRQA S 3803

    // ExtraOutermostLine* l_extra = new ExtraOutermostLine(
    //     f_framework,
    //     g_height,
    //     f_params, 25);
    // l_extra->setName("ExtraOutermostLine");
    // osg::Group::addChild(l_extra);  // PRQA S 3804  // PRQA S 3803

    //! texture is identical for all outermost lines so only create once
    osg::Image*     const l_texImage = m_left->create1DTexture();
    osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth(); // PRQA S 4262 // PRQA S 4264
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_OUTERMOST_LINE, "RenderBin");
}

//!
//! DistanceLineAsset
//!
DistanceLineAsset::DistanceLineAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params,
    const OutermostLine*       f_leftOutermostLine,
    const OutermostLine*       f_rightOutermostLine,
    vfc::uint32_t               f_numLayoutPoints)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
    , m_distanceLine{}
{
    m_distanceLine =
        new DL1(f_framework, g_height + 0.003f, f_params, f_leftOutermostLine, f_rightOutermostLine, f_numLayoutPoints); // PRQA S 2759
    m_distanceLine->setName("DL1");
    osg::Group::addChild(m_distanceLine.get()); // PRQA S 3803

    osg::Image*     const l_texImage = m_distanceLine->create1DTexture();
    osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet);                           // PRQA S 3803
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_DL1, "RenderBin");
}

//!
//! WheelTracksAsset
//!
WheelTracksAsset::WheelTracksAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params,
    const DL1*                 f_distanceLine)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
{
    const auto l_parkingCallbackWheelTrackAuto   = new ParkingCallback(ParkingCallback::WHEELTRACK_AUTO, f_framework); // PRQA S 4262 // PRQA S 4264
    const auto l_parkingCallbackWheelTrackManual = new ParkingCallback(ParkingCallback::WHEELTRACK_MANUAL, f_framework);

    { // WheelTracks close
        WheelTrack* const l_leftWheelTrack_Close = new WheelTrack( // PRQA S 2759
            f_framework,
            commontypes::Left_enm,
            g_height,
            f_params,
            f_distanceLine,
            g_numVerticesWheelTracks,
            WheelTrack::CLOSER_PART);
        l_leftWheelTrack_Close->setName("WheelTrackCloseLeft");

        WheelTrack* const l_rightWheelTrack_Close = new WheelTrack( // PRQA S 2759
            f_framework,
            commontypes::Right_enm,
            g_height,
            f_params,
            f_distanceLine,
            g_numVerticesWheelTracks,
            WheelTrack::CLOSER_PART);
        l_rightWheelTrack_Close->setName("WheelTrackCloseRight");

        osg::Group* const l_wheelTracksClose = new osg::Group;
        l_wheelTracksClose->setName("WheelTracksClose");
        l_wheelTracksClose->addCullCallback(l_parkingCallbackWheelTrackAuto);
        l_wheelTracksClose->addChild(l_leftWheelTrack_Close);  // PRQA S 3804  // PRQA S 3803
        l_wheelTracksClose->addChild(l_rightWheelTrack_Close); // PRQA S 3804  // PRQA S 3803
        osg::Group::addChild(l_wheelTracksClose);              // PRQA S 3804  // PRQA S 3803

        //! texture is identical for left and right close wheeltrack so only create once
        osg::Image*     const l_texImage = l_leftWheelTrack_Close->create1DTexture();
        osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

        osg::StateSet* const l_stateSet = l_wheelTracksClose->getOrCreateStateSet();
        l_stateSet->setTextureAttribute(0u, l_tex2D);
    }

    { // WheelTracks far
        WheelTrack* const l_leftWheelTrack_Far = new WheelTrack( // PRQA S 2759
            f_framework,
            commontypes::Left_enm,
            g_height,
            f_params,
            f_distanceLine,
            g_numVerticesWheelTracks,
            WheelTrack::FURTHER_PART);
        l_leftWheelTrack_Far->setName("WheelTrackFarLeft");

        WheelTrack* const l_rightWheelTrack_Far = new WheelTrack( // PRQA S 2759
            f_framework,
            commontypes::Right_enm,
            g_height,
            f_params,
            f_distanceLine,
            g_numVerticesWheelTracks,
            WheelTrack::FURTHER_PART);
        l_rightWheelTrack_Far->setName("WheelTrackFarRight");

        osg::Group* const l_wheelTracksFar = new osg::Group;
        l_wheelTracksFar->setName("WheelTracksFar");
        l_wheelTracksFar->addCullCallback(l_parkingCallbackWheelTrackAuto);
        l_wheelTracksFar->addChild(l_leftWheelTrack_Far);  // PRQA S 3804  // PRQA S 3803
        l_wheelTracksFar->addChild(l_rightWheelTrack_Far); // PRQA S 3804  // PRQA S 3803
        osg::Group::addChild(l_wheelTracksFar);            // PRQA S 3804  // PRQA S 3803

        //! texture is identical for left and right far wheeltrack so only create once
        osg::Image*     const l_texImage = l_leftWheelTrack_Far->create1DTexture();
        osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

        osg::StateSet* const l_stateSet = l_wheelTracksFar->getOrCreateStateSet();
        l_stateSet->setTextureAttribute(0u, l_tex2D);
    }

    { // WheelTracks whole
        WheelTrack* const l_leftWheelTrack_Whole = new WheelTrack( // PRQA S 2759
            f_framework,
            commontypes::Left_enm,
            g_height,
            f_params,
            f_distanceLine,
            g_numVerticesWheelTracks,
            WheelTrack::WHOLE);
        l_leftWheelTrack_Whole->setName("WheelTrackWholeLeft");

        WheelTrack* const l_rightWheelTrack_Whole = new WheelTrack( // PRQA S 2759
            f_framework,
            commontypes::Right_enm,
            g_height,
            f_params,
            f_distanceLine,
            g_numVerticesWheelTracks,
            WheelTrack::WHOLE);
        l_rightWheelTrack_Whole->setName("WheelTrackWholeRight");

        osg::Group* const l_wheelTracksWhole = new osg::Group;
        l_wheelTracksWhole->setName("WheelTracksWhole");
        l_wheelTracksWhole->addCullCallback(l_parkingCallbackWheelTrackManual);
        l_wheelTracksWhole->addChild(l_leftWheelTrack_Whole);  // PRQA S 3804  // PRQA S 3803
        l_wheelTracksWhole->addChild(l_rightWheelTrack_Whole); // PRQA S 3804  // PRQA S 3803
        osg::Group::addChild(l_wheelTracksWhole);              // PRQA S 3804  // PRQA S 3803

        //! texture is identical for left and right whole wheeltrack so only create once
        osg::Image*     const l_texImage = l_leftWheelTrack_Whole->create1DTexture();
        osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

        osg::StateSet* const l_stateSet = l_wheelTracksWhole->getOrCreateStateSet();
        l_stateSet->setTextureAttribute(0u, l_tex2D);
    }

    // { // WheelTracks extra manual
    //   WheelTrack* l_leftWheelTrack_Extra_Manual = new WheelTrack(
    //       f_framework,
    //       commontypes::Left_enm, g_height,
    //       f_params, f_distanceLine, g_numVerticesWheelTracks, WheelTrack::EXTRA_MANUAL);
    //   l_leftWheelTrack_Extra_Manual->setName("WheelTrackExtraManualLeft");

    //   WheelTrack* l_rightWheelTrack_Extra_Manual = new WheelTrack(
    //       f_framework,
    //       commontypes::Right_enm, g_height,
    //       f_params, f_distanceLine, g_numVerticesWheelTracks, WheelTrack::EXTRA_MANUAL);
    //   l_rightWheelTrack_Extra_Manual->setName("WheelTrackExtraManualRight");

    //   osg::Group* l_wheelTracksExtraManual = new osg::Group;
    //   l_wheelTracksExtraManual->setName("WheelTracksExtraManual");
    //   l_wheelTracksExtraManual->addCullCallback(l_parkingCallbackWheelTrackManual);
    //   l_wheelTracksExtraManual->addChild(l_leftWheelTrack_Extra_Manual);  // PRQA S 3804  // PRQA S 3803
    //   l_wheelTracksExtraManual->addChild(l_rightWheelTrack_Extra_Manual);  // PRQA S 3804  // PRQA S 3803
    //   osg::Group::addChild(l_wheelTracksExtraManual);  // PRQA S 3804  // PRQA S 3803

    //   //! texture is identical for left and right whole wheeltrack so only create once
    //   osg::Image* l_texImage = l_leftWheelTrack_Extra_Manual->create1DTexture();
    //   osg::Texture2D* l_tex2D = createTexture(l_texImage);

    //   osg::StateSet* l_stateSet = l_wheelTracksExtraManual->getOrCreateStateSet();
    //   l_stateSet->setTextureAttribute(0u, l_tex2D);
    // }

    // { // WheelTracks extra auto
    //   WheelTrack* l_leftWheelTrack_Extra_Auto = new WheelTrack(
    //       f_framework,
    //       commontypes::Left_enm, g_height,
    //       f_params, f_distanceLine, g_numVerticesWheelTracks, WheelTrack::EXTRA_AUTO);
    //   l_leftWheelTrack_Extra_Auto->setName("WheelTrackExtraAutoLeft");

    //   WheelTrack* l_rightWheelTrack_Extra_Auto = new WheelTrack(
    //       f_framework,
    //       commontypes::Right_enm, g_height,
    //       f_params, f_distanceLine, g_numVerticesWheelTracks, WheelTrack::EXTRA_AUTO);
    //   l_rightWheelTrack_Extra_Auto->setName("WheelTrackExtraAutoRight");

    //   osg::Group* l_wheelTracksExtraAuto = new osg::Group;
    //   l_wheelTracksExtraAuto->setName("WheelTracksExtra");
    //   l_wheelTracksExtraAuto->addCullCallback(l_parkingCallbackWheelTrackAuto);
    //   l_wheelTracksExtraAuto->addChild(l_leftWheelTrack_Extra_Auto);  // PRQA S 3804  // PRQA S 3803
    //   l_wheelTracksExtraAuto->addChild(l_rightWheelTrack_Extra_Auto);  // PRQA S 3804  // PRQA S 3803
    //   osg::Group::addChild(l_wheelTracksExtraAuto);  // PRQA S 3804  // PRQA S 3803

    //   //! texture is identical for left and right whole wheeltrack so only create once
    //   osg::Image* l_texImage = l_leftWheelTrack_Extra_Auto->create1DTexture();
    //   osg::Texture2D* l_tex2D = createTexture(l_texImage);

    //   osg::StateSet* l_stateSet = l_wheelTracksExtraAuto->getOrCreateStateSet();
    //   l_stateSet->setTextureAttribute(0u, l_tex2D);
    // }

    osg::StateSet*                           const l_stateSet = getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_WHEELTRACK, "RenderBin");
}

//!
//! TrailerAssistLineAsset
//!
TrailerAssistLineAsset::TrailerAssistLineAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
{
    // auto l_parkingCallbackWheelTrackManual = new ParkingCallback(ParkingCallback::WHEELTRACK_MANUAL, f_framework);

    { // WheelTracks close
        TrailerAssistLine* const l_trailerAssistLine = // PRQA S 4262 // PRQA S 4264
            new TrailerAssistLine(f_framework, g_height, f_params, g_numVerticesWheelTracks);
        l_trailerAssistLine->setName("TrailerAssistLine");

        osg::Group* const l_trailerAssist = new osg::Group;
        l_trailerAssist->setName("TrailerAssist");
        // l_trailerAssist->addCullCallback(l_parkingCallbackWheelTrackManual);
        l_trailerAssist->addChild(l_trailerAssistLine); // PRQA S 3804  // PRQA S 3803
        osg::Group::addChild(l_trailerAssist);          // PRQA S 3804  // PRQA S 3803

        //! texture is identical for left and right close wheeltrack so only create once
        osg::Image*     const l_texImage = l_trailerAssistLine->create1DTexture();
        osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

        osg::StateSet* const l_stateSet = l_trailerAssist->getOrCreateStateSet();
        l_stateSet->setTextureAttribute(0u, l_tex2D);
    }

    osg::StateSet*                           const l_stateSet = getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_WHEELTRACK, "RenderBin");
}

//!
//! ActionPointsAsset
//!
ActionPointsAsset::ActionPointsAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params,
    const DL1*                 f_distanceLine)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
{
    ActionPoint* const l_leftActionPoint = // PRQA S 4262 // PRQA S 4264
        new ActionPoint(f_framework, commontypes::Left_enm, g_height, f_params, f_distanceLine, 3u); // PRQA S 2759
    l_leftActionPoint->setName("leftActionPoint");

    ActionPoint* const l_rightActionPoint =
        new ActionPoint(f_framework, commontypes::Right_enm, g_height, f_params, f_distanceLine, 3u); // PRQA S 2759
    l_rightActionPoint->setName("rightActionPoint");

    const auto        l_parkingCullCallback = new ParkingCallback(ParkingCallback::ACTIONPOINT, f_framework);
    osg::Group* const l_actionPointGroup    = new osg::Group;
    l_actionPointGroup->setName("ActionPointGroup");
    l_actionPointGroup->addCullCallback(l_parkingCullCallback);
    l_actionPointGroup->addChild(l_leftActionPoint);  // PRQA S 3804  // PRQA S 3803
    l_actionPointGroup->addChild(l_rightActionPoint); // PRQA S 3804  // PRQA S 3803
    osg::Group::addChild(l_actionPointGroup);         // PRQA S 3804  // PRQA S 3803

    //! texture is identical for left and right action point lines
    osg::Image*     const l_texImage = l_leftActionPoint->create2DTexture();
    osg::Texture2D* const l_tex2D    = createTexture(l_texImage);

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setTextureAttribute(0u, l_tex2D);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Depth* const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_ACTION_POINT, "RenderBin");
}

//!
//! CoverPlateAsset
//!
CoverPlateAsset::CoverPlateAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params,
    vfc::uint32_t               f_numOfVerts)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
{
    CoverPlate* const l_coverPlate = new CoverPlate(f_framework, g_height + 0.001f, f_params, f_numOfVerts); // PRQA S 4262 // PRQA S 4264
    l_coverPlate->setName("CoverPlate");
    osg::Group::addChild(l_coverPlate); // PRQA S 3804  // PRQA S 3803

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_COVER_PLATE, "RenderBin");
}

//!
//! RefLineAsset
//!
RefLineAsset::RefLineAsset(
    cc::core::AssetId          f_assetId,
    pc::core::Framework*       f_framework,
    const TrajectoryParams_st& f_params)
    : pc::core::Asset{f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY}
{
    RefLine* const l_refLine = new RefLine(f_framework, g_height, f_params, 50u); // PRQA S 4262 // PRQA S 4264
    l_refLine->setName("RefLine");
    osg::Group::addChild(l_refLine); // PRQA S 3804  // PRQA S 3803

    osg::StateSet* const l_stateSet         = getOrCreateStateSet();
    osg::Depth*    const l_depthStateAttrib = new osg::Depth();
    l_depthStateAttrib->setWriteMask(false);
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);      // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_REFLINE, "RenderBin");
}

//!
//! TrailerHitchAsset
//!
// TrailerHitchTrajectoryAsset::TrailerHitchTrajectoryAsset(
//   cc::core::AssetId f_assetId,
//   pc::core::Framework* f_framework,
//   const TrajectoryParams_st& f_params)
//   : pc::core::Asset(f_assetId, pc::core::Asset::ASSET_TYPE_3D_OVERLAY)
// {
//   TrailerHitchTrajectory* l_trailerHitchTrajectory = new TrailerHitchTrajectory(
//     f_params, 20, f_framework->asCustomFramework());
//   l_trailerHitchTrajectory->setName("TrailerHitchTrajectory");
//   addChild(l_trailerHitchTrajectory);  // PRQA S 3804

//   osg::StateSet* l_stateSet = getOrCreateStateSet();
//   osg::Depth * l_depthStateAttrib = new osg::Depth();
//   l_depthStateAttrib->setWriteMask(false);
//   l_stateSet->setAttributeAndModes(l_depthStateAttrib);
//   l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
//   l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
//   l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
//   l_stateSet->setRenderBinDetails(RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_LINE, "RenderBin");
// }

} // namespace trajectory
} // namespace assets
} // namespace cc
