//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#ifndef CC_ASSETS_CUSTOMBUTTON_H
#define CC_ASSETS_CUSTOMBUTTON_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

namespace cc
{
namespace assets
{
namespace button
{

class FloatViewChangeButtonTexturePath : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(FloatViewChangeButtonTexturePath)
    {
        ADD_STRING_MEMBER(EnabledTexturePath);
        ADD_STRING_MEMBER(DisabledTexturePath);
    }

    std::string m_EnabledTexturePath;
    std::string m_DisabledTexturePath;
};

class FloatViewChangeButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(FloatViewChangeButtonSettings)
    {
        ADD_MEMBER(osg::Vec2f, FloatPlanViewPlanButtonPos);
        ADD_MEMBER(osg::Vec2f, FloatFRViewPlanButtonPos);
        ADD_MEMBER(FloatViewChangeButtonTexturePath, FloatPlanViewButtonTexturePath);
        ADD_MEMBER(osg::Vec2f, FloatPlanViewFRButtonPos);
        ADD_MEMBER(osg::Vec2f, FloatFRViewFRButtonPos);
        ADD_MEMBER(FloatViewChangeButtonTexturePath, FloatFrontViewButtonTexturePath);
        ADD_MEMBER(FloatViewChangeButtonTexturePath, FloatRearViewButtonTexturePath);
    }
    osg::Vec2f                                                    m_FloatPlanViewPlanButtonPos;
    osg::Vec2f                                                    m_FloatFRViewPlanButtonPos;
    osg::Vec2f                                                    m_FloatPlanViewFRButtonPos;
    osg::Vec2f                                                    m_FloatFRViewFRButtonPos;
    FloatViewChangeButtonTexturePath                              m_FloatPlanViewButtonTexturePath;
    FloatViewChangeButtonTexturePath                              m_FloatFrontViewButtonTexturePath;
    FloatViewChangeButtonTexturePath                              m_FloatRearViewButtonTexturePath;
};

class CustomButtonSetting : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(CustomButtonSetting) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTextureSettings, buttonTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_MEMBER(osg::Vec2f, buttonSize);
    }

    ButtonTextureSettings m_buttonTexture;
    osg::Vec2f            m_horiPos = osg::Vec2f(0.0f, 100.0f);
    osg::Vec2f            m_buttonSize;
};

class CustomButtonsSetting : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(CustomButtonsSetting) // PRQA S 2428
    {
        ADD_MEMBER(CustomButtonSetting, closeButtonSetting);
        ADD_MEMBER(CustomButtonSetting, floatCloseButtonSetting);
        ADD_MEMBER(CustomButtonSetting, parkButtonSetting);
        ADD_MEMBER(CustomButtonSetting, SonarLoudSpeakerButtonMutedSetting);
        ADD_MEMBER(CustomButtonSetting, floatSonarLoudSpeakerButtonMutedSetting);
        ADD_MEMBER(CustomButtonSetting, SonarLoudSpeakerButtonUnMutedSetting);
        ADD_MEMBER(CustomButtonSetting, EnlargePlanButtonSetting);
        ADD_MEMBER(CustomButtonSetting, EnlargeFRButtonSetting);
        ADD_MEMBER(FloatViewChangeButtonSettings,FloatViewChangeButtonSettings);
        ADD_INT_MEMBER(displayDelay);
    }

    CustomButtonSetting             m_closeButtonSetting;
    CustomButtonSetting             m_floatCloseButtonSetting;
    CustomButtonSetting             m_parkButtonSetting;
    CustomButtonSetting             m_SonarLoudSpeakerButtonMutedSetting;
    CustomButtonSetting             m_floatSonarLoudSpeakerButtonMutedSetting;
    CustomButtonSetting             m_SonarLoudSpeakerButtonUnMutedSetting;
    CustomButtonSetting             m_EnlargePlanButtonSetting;
    CustomButtonSetting             m_EnlargeFRButtonSetting;
    FloatViewChangeButtonSettings   m_FloatViewChangeButtonSettings;
    int  m_displayDelay = 3;
};

class CustomButton : public Button
{
public:
    CustomButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);

protected:
    virtual void onInvalid();
    virtual void onUnavailable();
    virtual void onAvailable();
    virtual void onPressed();
    virtual void onReleased();
    void         handleTouch();
    bool         isPressed(ButtonState f_buttonState);
    virtual void deliverPressed(bool f_isPressed)
    {
    }
    pc::core::Framework* m_framework;
};

//! Close Button
class CloseButton : public cc::assets::button::CustomButton
{
public:
    CloseButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);

private:
    void  update() override;
    void  deliverPressed(bool f_isPressed) override;
    EGear m_preGear;
    int   m_delayCounter;
    bool  m_gearChangedToP;
};

//! Enlarge Button
class EnlargeButton : public cc::assets::button::CustomButton
{
public:
    EnlargeButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);

private:
    void  update() override;
    void  deliverPressed(bool f_isPressed) override;
    EGear m_preGear;
    int   m_delayCounter;
    bool  m_gearChangedToP;
};

//! Park Button
class ParkButton : public cc::assets::button::CustomButton
{
public:
    ParkButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);

private:
    void onUnavailable() override;
    void onAvailable() override;
    void onPressed() override;
    void update() override;
    void deliverPressed(bool f_isPressed) override;
    bool m_showStatus;
};

//! SonarLoudSpeakerButton
class SonarLoudSpeakerButton : public cc::assets::button::CustomButton
{
public:
    SonarLoudSpeakerButton(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView = nullptr);

private:
    void update() override;
    void deliverPressed(bool f_isPressed) override;
};

//FloatViewChangeButton
class FloatViewChangeButton : public cc::assets::button::CustomButton
{
public:
    FloatViewChangeButton(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView);
    static cc::daddy::EFloatViewType getCurrentFloatViewType()
    {
        return s_curFloatViewType;
    }

    static void modifyCurrentFloatViewType(cc::daddy::EFloatViewType f_curFloatViewType)
    {
        s_curFloatViewType = f_curFloatViewType;
    }

    cc::daddy::EFloatViewType getButtonFloatViewType() const
    {
        return m_buttonFloatViewType;
    }

    void modifyButtonFloatViewType(cc::daddy::EFloatViewType f_buttonFloatViewType)
    {
        m_buttonFloatViewType = f_buttonFloatViewType;
    }
protected:
    virtual void update() override;
    void  updateFloatPlanViewButton();
    void  updateFloatFRViewButton();
private:
    void                                                          deliverPressed(bool f_isPressed);
    pc::core::Framework*                                          m_framework;
    cc::daddy::EFloatViewType                                     m_buttonFloatViewType;
    FloatViewChangeButtonTexturePath                              m_FloatViewTypeButtonTexturePath;
    static cc::daddy::EFloatViewType                              s_curFloatViewType;
};

class FloatViewChangeButtonGroup : public cc::assets::button::ButtonGroup
{
public:
    FloatViewChangeButtonGroup(
        cc::core::AssetId                       f_assetId,
        pc::core::Framework*                    f_framework,
        osg::Camera*                            f_referenceView = nullptr);

protected:
    cc::daddy::EFloatViewType        m_storedButtonFloatViewType;
    FloatViewChangeButtonSettings    m_FloatViewChangeButtonSetting;
    pc::core::Framework*             m_framework;
    void                             update() override;
};
} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_CUSTOMBUTTON_H