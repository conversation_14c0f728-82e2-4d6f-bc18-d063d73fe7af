//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  ParkingSpaceMark.cpp
/// @brief
//=============================================================================

#include "cc/assets/parkingspace/inc/ParkingSpaceMark.h"
#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/uielements/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h" // PRQA S 1060
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "CustomSystemConf.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;
using cc::assets::uielements::g_uiSettings;

namespace cc
{
namespace assets
{
namespace parkingspace
{

// ! enum values for seeting bar icons
enum class ParkingIconType : vfc::uint32_t
{
  PARKINGICON_LEFT_HORI,
  PARKINGICON_RIGHT_HORI,
  PARKINGICON_LEFT_VERT,
  PARKINGICON_RIGHT_VERT
};

//!
//! @brief Construct a new ParkingIcon Manager:: ParkingIcon Manager object
//!
//! @param f_config
//!
ParkingSpaceMarkManager::ParkingSpaceMarkManager()
  : m_lastConfigUpdate{~0u}
  , m_mat_b{false}
  , m_planViewWidth_hori{cc::core::g_planView->m_widthMetersParkingHori}
  , m_planViewWidth_vert{cc::core::g_planView->m_widthMetersParkingVert}
  , m_distanceX_hori{ (cc::core::g_planView->m_widthMetersParkingHori / 2.36f)        // 2.36 = (hori view rate 848 / 720) * 2
    - ( pc::vehicle::g_mechanicalData->getLength() / 2.0f + pc::vehicle::g_mechanicalData->getBumperCenterXRear() ) }
  , m_distanceX_vert{ (cc::core::g_planView->m_widthMetersParkingVert / 2.0f)
    - ( pc::vehicle::g_mechanicalData->getLength() / 2.0f + pc::vehicle::g_mechanicalData->getBumperCenterXRear() ) }
  , m_distanceY_hori{(cc::core::g_planView->m_widthMetersParkingHori / 2.0f - cc::assets::parkingspace::g_parkingSpaceSettings->m_slotOffset.y())}
  , m_distanceY_vert{(cc::core::g_planView->m_widthMetersParkingVert * 0.776f - cc::assets::parkingspace::g_parkingSpaceSettings->m_slotOffset.y())} /// 0.776 = (vert view rate 720 / 464 / 2)
  , m_isLeftSlotNotShown{false}
  , m_isRightSlotNotShown{false}
{
}


ParkingSpaceMarkManager::~ParkingSpaceMarkManager() = default;

void ParkingSpaceMarkManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init ParkingIcon icons
  m_parkingIcons.clear(f_imageOverlays);

  m_parkingIcons.addIcon(f_imageOverlays, cc::assets::uielements::createIconTopLeft(g_uiSettings->m_texturePathParkingPlanIcon,      cc::assets::uielements::transferToBottomLeftHoriHU(g_uiSettings->m_settingParkingPlanIconLeft.m_iconCenter),       g_uiSettings->m_settingParkingPlanIconLeft.m_iconSize)); // PRQA S 2759
  m_parkingIcons.addIcon(f_imageOverlays, cc::assets::uielements::createIconTopLeft(g_uiSettings->m_texturePathParkingPlanIcon,      cc::assets::uielements::transferToBottomLeftHoriHU(g_uiSettings->m_settingParkingPlanIconRight.m_iconCenter),      g_uiSettings->m_settingParkingPlanIconRight.m_iconSize)); // PRQA S 2759
  m_parkingIcons.addIcon(f_imageOverlays, cc::assets::uielements::createIconTopLeft(g_uiSettings->m_texturePathParkingPlanIcon_vert, cc::assets::uielements::transferToBottomLeftVert(g_uiSettings->m_settingParkingPlanIconLeft_vert.m_iconCenter),  g_uiSettings->m_settingParkingPlanIconLeft_vert.m_iconSize)); // PRQA S 2759
  m_parkingIcons.addIcon(f_imageOverlays, cc::assets::uielements::createIconTopLeft(g_uiSettings->m_texturePathParkingPlanIcon_vert, cc::assets::uielements::transferToBottomLeftVert(g_uiSettings->m_settingParkingPlanIconRight_vert.m_iconCenter), g_uiSettings->m_settingParkingPlanIconRight_vert.m_iconSize)); // PRQA S 2759
}


void ParkingSpaceMarkManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)    // PRQA S 6041
{
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }

  // ! check the icon status
  m_parkingIcons.getIcon(static_cast<unsigned int>(ParkingIconType::PARKINGICON_LEFT_HORI))->setEnabled(false);
  m_parkingIcons.getIcon(static_cast<unsigned int>(ParkingIconType::PARKINGICON_RIGHT_HORI))->setEnabled(false);
  m_parkingIcons.getIcon(static_cast<unsigned int>(ParkingIconType::PARKINGICON_LEFT_VERT))->setEnabled(false);
  m_parkingIcons.getIcon(static_cast<unsigned int>(ParkingIconType::PARKINGICON_RIGHT_VERT))->setEnabled(false);

  cc::target::common::EThemeTypeHU                           l_theme             = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
  cc::target::common::EPARKStatusR2L                         l_curparkStatus     = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld                        l_curParkngTypeSeld = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  bool                                   l_curFreeParkActive = false;
  cc::target::common::StrippedParkhmiTargetPosition          l_finalEndPosition  = {0};
  std::array<std::array<cc::target::common::StrippedEAPAParkSpace,            cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> l_curparkSpace; // PRQA S 4102
  std::array<std::array<cc::target::common::StrippedParkhmiPositionSearching, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> l_parkSpaceMark; // PRQA S 4102

  if (f_framework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = f_framework->m_SVSRotateStatusDaddy_Receiver.getData();
    l_theme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899
  }

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_freeParkActive = f_framework->m_freeparkingActiveReceiver.getData();
    l_curFreeParkActive = l_freeParkActive->m_Data;
  }

  if(f_framework->m_parkAPASlotsReceiver.hasData())
  {
    const cc::daddy::ParkAPA_ParkSpace_t *const l_parkSpace = f_framework->m_parkAPASlotsReceiver.getData();
    for(vfc::uint16_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
      for(vfc::uint16_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
      {
        l_curparkSpace[l_side][l_numberPerside] = l_parkSpace->m_Data[l_side][l_numberPerside];
        // XLOG_INFO_OS(g_AppContext) << "!!! local variable l_curparkSpace has been updated "  << XLOG_ENDL;
      }
    }
  }

  if(f_framework->m_parkAPASlotsMarkReceiver.isConnected() && f_framework->m_parkAPASlotsMarkReceiver.hasData())
  {
    const cc::daddy::ParkAPA_ParkSpaceMark_t*      const l_pParkSpaceMark       = f_framework->m_parkAPASlotsMarkReceiver.getData();
    for(vfc::uint16_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
      for(vfc::uint16_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
      {
        l_parkSpaceMark[l_side][l_numberPerside] = l_pParkSpaceMark->m_Data[l_side][l_numberPerside];
      }
    }
  }

  if(f_framework->m_parkFinalEndPositionReceiver.isConnected() && f_framework->m_parkFinalEndPositionReceiver.hasData())
  {
    const cc::daddy::ParkFinalEndPositionDaddy_t*  const l_pFinalEndPosition   = f_framework->m_parkFinalEndPositionReceiver.getData();
    l_finalEndPosition = l_pFinalEndPosition->m_Data;
  }

  if (((cc::target::common::EPARKStatusR2L::PARK_Searching       == l_curparkStatus) || (cc::target::common::EPARKStatusR2L::PARK_AssistStandby    == l_curparkStatus) ||
       (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curparkStatus) || (cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_curparkStatus) ||
       (cc::target::common::EPARKStatusR2L::PARK_Completed       == l_curparkStatus))
    && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld && false == l_curFreeParkActive)
  {
    if (cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI == l_theme)
    {
      isSlotNotDisplayed(l_curparkSpace, m_distanceX_hori, m_distanceY_hori, l_curparkStatus, l_parkSpaceMark, l_finalEndPosition); // PRQA S 2963
      m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_LEFT_VERT))->setEnabled(false);
      m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_RIGHT_VERT))->setEnabled(false);
      if (m_isLeftSlotNotShown == true)
      {
        m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_LEFT_HORI))->setEnabled(true);
      }
      else
      {
        // do nothing
      }
      if (m_isRightSlotNotShown == true)
      {
        m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_RIGHT_HORI))->setEnabled(true);
      }
      else
      {
        // do nothing
      }
    }
    else
    {
      isSlotNotDisplayed(l_curparkSpace, m_distanceX_vert, m_distanceY_vert, l_curparkStatus, l_parkSpaceMark, l_finalEndPosition);
      m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_LEFT_HORI))->setEnabled(false);
      m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_RIGHT_HORI))->setEnabled(false);
      if (m_isLeftSlotNotShown == true)
      {
        m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_LEFT_VERT))->setEnabled(true);
      }
      else
      {
        // do nothing
      }
      if (m_isRightSlotNotShown == true)
      {
        m_parkingIcons.getIcon(static_cast<unsigned>(ParkingIconType::PARKINGICON_RIGHT_VERT))->setEnabled(true);
      }
      else
      {
        // do nothing
      }
    }
  }

}

void ParkingSpaceMarkManager::isSlotNotDisplayed(    // PRQA S 6043
  std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace,
  vfc::float32_t f_distanceX,
  vfc::float32_t f_distanceY,
  cc::target::common::EPARKStatusR2L f_curparkStatus,
  std::array<std::array<cc::target::common::StrippedParkhmiPositionSearching, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_parkSpaceMark,
  cc::target::common::StrippedParkhmiTargetPosition f_finalEndPosition)
{
  m_isLeftSlotNotShown = false;
  m_isRightSlotNotShown= false;

  for(vfc::uint16_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++)
  {
    for(vfc::uint16_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
    {
      const cc::target::common::EPARKSlotStsR2L l_parkSlotStatus = f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlot;
      const vfc::float32_t l_distanceCorner2X = static_cast<vfc::float32_t>(f_parkSpaceMark[l_side][l_numberPerside].m_x) / 100.0f; // change to meter
      constexpr vfc::float32_t l_offset = 0.0f;
      if((cc::target::common::EPARKStatusR2L::PARK_Guidance_active  == f_curparkStatus || cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == f_curparkStatus || cc::target::common::EPARKStatusR2L::PARK_Completed == f_curparkStatus) && (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED == l_parkSlotStatus))
      {
        vfc::float32_t xt=0.0f, yt=0.0f, length=0.0f, alpha=0.0f, x0=0.0f, y0=0.0f, x1=0.0f, y1=0.0f, phi=0.0f, x_center=0.0f, y_center=0.0f, xt2=0.0f, yt2=0.0f, length2=0.0f, alpha2=0.0f, x2=0.0f, y2=0.0f, x3=0.0f, y3=0.0f;    // PRQA S 2504  #code looks fine
        x_center = static_cast<vfc::float32_t>(f_finalEndPosition.m_x)/1024.0f; // 2^-10 m -> m
        y_center = static_cast<vfc::float32_t>(f_finalEndPosition.m_y)/1024.0f; // 2^-10 m -> m
        phi = static_cast<vfc::float32_t>(f_finalEndPosition.m_phi)/4096.0f; // rad

        //! Old method to calculate xt, yt
        // xt = cc::assets::parkingspace::g_parkingSpaceSettings->m_slotParallelX - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear
        //   - (cc::assets::parkingspace::g_parkingSpaceSettings->m_slotParallelX - pc::vehicle::g_mechanicalData->m_wheelbase
        //    - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear) / 2.0f;
        // yt = cc::assets::parkingspace::g_parkingSpaceSettings->m_slotCrossX / 2.0f;
        //! Using offset to calculate xt, yt
        xt = cc::assets::parkingspace::g_parkingSpaceSettings->m_slotParallelX - cc::assets::parkingspace::g_parkingSpaceSettings->m_slotOffset.x();
        yt = cc::assets::parkingspace::g_parkingSpaceSettings->m_slotCrossX / 2.0f - cc::assets::parkingspace::g_parkingSpaceSettings->m_slotOffset.y();  // m
        length = sqrt((xt * xt) + (yt * yt));
        alpha = std::atan(yt / xt);
        xt2 = cc::assets::parkingspace::g_parkingSpaceSettings->m_slotParallelX - xt;
        yt2 = yt;
        length2 = sqrt((xt2 * xt2) + (yt2 * yt2));
        alpha2 = std::atan(yt2 / xt2);

        x0 = x_center + length * std::sin(static_cast<vfc::float32_t>(osg::PI_2) - phi - alpha);
        x1 = x_center + length * std::cos(phi - alpha);
        x2 = x_center - length2 * std::cos(phi - alpha2);
        x3 = x_center - length2 * std::sin(phi + alpha2 - static_cast<vfc::float32_t>(osg::PI_2));

        y0 = y_center + length * std::cos(static_cast<vfc::float32_t>(osg::PI_2) - phi - alpha);
        y1 = y_center + length * std::sin(phi - alpha);
        y2 = y_center - length2 * std::sin(phi - alpha2);
        y3 = y_center - length2 * std::cos(phi + alpha2 - static_cast<vfc::float32_t>(osg::PI_2));

        if(((isGreater(std::abs(x0), f_distanceX) && isNegative(x0)) &&
            (isGreater(std::abs(x1), f_distanceX) && isNegative(x1)) &&
            (isGreater(std::abs(x2), f_distanceX) && isNegative(x2)) &&
            (isGreater(std::abs(x3), f_distanceX) && isNegative(x3))) ||
           ((isGreater(std::abs(y0), f_distanceY - l_offset)) &&
            (isGreater(std::abs(y1), f_distanceY - l_offset)) &&
            (isGreater(std::abs(y2), f_distanceY - l_offset)) &&
            (isGreater(std::abs(y3), f_distanceY - l_offset))))
        {
          if (l_side == 0u) { m_isLeftSlotNotShown  = true; }
          if (l_side == 1u) { m_isRightSlotNotShown = true; }
        }
      }
      else if(
        // SELECTED shall be displayed in both confirming and searching
        (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED   == l_parkSlotStatus && (f_curparkStatus == cc::target::common::EPARKStatusR2L::PARK_AssistStandby || f_curparkStatus == cc::target::common::EPARKStatusR2L::PARK_Searching)) ||
        // in searching, SELECTABLE shall be displayed
        (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE == l_parkSlotStatus &&  f_curparkStatus == cc::target::common::EPARKStatusR2L::PARK_Searching)
      )
      {
        // in case distance is positive and larger than f_distanceX, the slot is visible
        if(std::abs(l_distanceCorner2X) >= f_distanceX && isNegative(l_distanceCorner2X))
        {
          if (l_side == 0u) { m_isLeftSlotNotShown  = true; }
          if (l_side == 1u) { m_isRightSlotNotShown = true; }
        }
      }
      else {} // do nothing
    }
  }
}


//!
//! @brief Construct a new ParkingSpaceMarkSymbols:: ParkingSpaceMarkSymbols object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ParkingSpaceMarkSymbols::ParkingSpaceMarkSymbols(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}  // PRQA S 2966 
  , m_customFramework{f_customFramework}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

  //! render order
  //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
  constexpr vfc::uint32_t l_renderOrder = 10u;
  cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder);

}


ParkingSpaceMarkSymbols::~ParkingSpaceMarkSymbols() = default;


void ParkingSpaceMarkSymbols::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace parkingspace
} // namespace assets
} // namespace cc

