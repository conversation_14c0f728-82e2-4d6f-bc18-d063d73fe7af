//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PTSOVERLAY_PTSSETTINGS_H
#define CC_ASSETS_PTSOVERLAY_PTSSETTINGS_H

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/math/inc/FilterSpatial.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/core/inc/CustomMechanicalData.h"

#include <osg/Vec4i>
#include <cassert>

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace ptsoverlay
{

enum { NUM_COLORS = 6 };

//!
//! ColorValues
//!
class ColorValues : public pc::util::coding::ISerializable
{
public:

  ColorValues()
    : m_r2(255,   2,   0, 255)
    , m_r1(252,  60,   0, 255)
    , m_o2(253, 107,  13, 255)
    , m_o1(253, 158,  60, 255)
    , m_y2(242, 252, 100, 255)
    , m_y1(252, 253, 163, 255)
  {
  }

  SERIALIZABLE(ColorValues)
  {
    ADD_MEMBER(osg::Vec4i, r2);
    ADD_MEMBER(osg::Vec4i, r1);
    ADD_MEMBER(osg::Vec4i, o2);
    ADD_MEMBER(osg::Vec4i, o1);
    ADD_MEMBER(osg::Vec4i, y2);
    ADD_MEMBER(osg::Vec4i, y1);
  }

  const osg::Vec4i& getColor(unsigned int f_index) const
  {
    switch (f_index)
    {
      case 0:
        return m_r2;
      case 1:
        return m_r1;
      case 2:
        return m_o2;
      case 3:
        return m_o1;
      case 4:
        return m_y2;
      case 5:
        return m_y1;
      default:
        assert(f_index < NUM_COLORS);
        return m_y1;
    }
  }

  osg::Vec4i m_r2;
  osg::Vec4i m_r1;
  osg::Vec4i m_o2;
  osg::Vec4i m_o1;
  osg::Vec4i m_y2;
  osg::Vec4i m_y1;
};


//!
//! DistanceValues
//!
class DistanceValues : public pc::util::coding::ISerializable
{
public:

  DistanceValues()
    : m_r2(0.2f)
    , m_r1(0.36f)
    , m_o2(0.52f)
    , m_o1(0.68f)
    , m_y2(0.84f)
    , m_y1(0.98f)
    , m_default(1.0f)
  {
  }

  DistanceValues(float f_r2,
                 float f_r1,
                 float f_o2,
                 float f_o1,
                 float f_y2,
                 float f_y1,
                 float f_default)
    : m_r2(f_r2)
    , m_r1(f_r1)
    , m_o2(f_o2)
    , m_o1(f_o1)
    , m_y2(f_y2)
    , m_y1(f_y1)
    , m_default(f_default)
  {
  }

  SERIALIZABLE(DistanceValues)
  {
    ADD_FLOAT_MEMBER(r2);
    ADD_FLOAT_MEMBER(r1);
    ADD_FLOAT_MEMBER(o2);
    ADD_FLOAT_MEMBER(o1);
    ADD_FLOAT_MEMBER(y2);
    ADD_FLOAT_MEMBER(y1);
  }

  float getDistance(unsigned int f_index) const
  {
    switch (f_index)
    {
      case 0:
        return m_r2;
      case 1:
        return m_r1;
      case 2:
        return m_o2;
      case 3:
        return m_o1;
      case 4:
        return m_y2;
      case 5:
        return m_y1;
      default:
        assert(f_index < NUM_COLORS);
        return m_y1;
    }
  }

  float getNear() const
  {
    return m_r2;
  }

  float getFar() const
  {
    return m_y1;
  }

  float getDefault() const
  {
    return m_default;
  }

  //!
  //! @brief maps a distance value to a normalized range x <= near = 0, x >= default = 1
  //!
  //! @param f_value distance value
  //! @return float normalized value
  //!
  float normalize(float f_value) const
  {
    float l_result = (f_value - getNear()) / (getDefault() - getNear());
    return std::min(1.0f, std::max(0.0f, l_result)); // clamp
  }


  //!
  //! @brief maps a normalized input value to the valid distance range, 0 = near, 1 = default
  //!
  //! @param f_value Expected to be in the range from 0 to 1
  //! @return float mapped value
  //!
  float denormalize(float f_value) const
  {
    return (f_value * (getDefault() - getNear())) + getNear();
  }

  float m_r2;
  float m_r1;
  float m_o2;
  float m_o1;
  float m_y2;
  float m_y1;
  float m_default;
};

//!
//! PtsVanSettings
//!
struct PtsVanSettings : public pc::util::coding::ISerializable
{
  PtsVanSettings()
  : m_rangeFrontVan(1.1f)
  , m_rangeRearVan(1.1f)
  , m_rangeSideVan(0.78f)
  , m_distancesVan(0.3f, 0.46f, 0.62f, 0.78f, 0.94f, 1.1f, 1.1f)
  {
  }

  SERIALIZABLE(PtsVanSettings)
  {
    ADD_FLOAT_MEMBER(rangeFrontVan);
    ADD_FLOAT_MEMBER(rangeRearVan);
    ADD_FLOAT_MEMBER(rangeSideVan);
    ADD_MEMBER(DistanceValues, distancesVan);
  }

  float m_rangeFrontVan;
  float m_rangeRearVan;
  float m_rangeSideVan;
  DistanceValues m_distancesVan;
};

//!
//! PtsSettings
//!
class PtsSettings : public pc::util::coding::ISerializable
{
public:

  PtsSettings()
    : m_rangeFront(1.0f)
    , m_rangeSide(0.68f)
    , m_rangeRear(1.0f)
    , m_targetSegmentLength(0.2f)
    , m_cornerRadius(1.0f)
    , m_extractorMinDistance(0.f)
    , m_heightOverGround(0.01f)
    , m_projectionTransitionCamAngle(55.0f, 65.0f)
    , m_heightMinThreshold(0.25f)
    , m_shadowRadius(0.15f)
    , m_spline2DWidthInnerNear(0.05f)
    , m_spline2DWidthInnerFar(0.04f)
    , m_spline2DWidthOuter(0.025f)
    , m_spline3DAlphaTop(0.75f)
    , m_spline3DAlphaBottom(1.0f)
    , m_spline3DHeight(0.015f)
    , m_contourAlpha(0.6f)
    , m_contourHeight(0.02f)
    , m_shieldAlphaInner(0.1f)
    , m_shieldAlphaOuter(0.4f)
    , m_shieldHeight(0.5f)
    , m_hairlineWidth(1.0f)
    , m_hairlineAlpha(0.4f)
    , m_hairlineBlooming(0.1f)
    , m_smoothingGradientSize(0.2f)
    , m_colorOn(0, 147, 255, 255)
    , m_colorOutline(15, 47, 63, 180)
    , m_colorShadow(26, 26, 26, 89)
    , m_distanceFilter(2, 5, 1.0f)
  {}

  float getRangeFront() const
  {
    // if(cc::core::g_vehicleData->m_vanPtsSplineDistanceEnabled)
    // {
    //   return m_ptsVanSettings.m_rangeFrontVan;
    // }
    // else
    // {
      return m_rangeFront;
    // }
  }

  float getRangeRear() const
  {
    // if(cc::core::g_vehicleData->m_vanPtsSplineDistanceEnabled)
    // {
    //   return m_ptsVanSettings.m_rangeRearVan;
    // }
    // else
    // {
      return m_rangeRear;
    //}
  }

  float getRangeSide() const
  {
    // if(cc::core::g_vehicleData->m_vanPtsSplineDistanceEnabled)
    // {
    //   return m_ptsVanSettings.m_rangeSideVan;
    // }
    // else
    // {
      return m_rangeSide;
    // }
  }

  DistanceValues getDistances() const
  {
    // if(cc::core::g_vehicleData->m_vanPtsSplineDistanceEnabled)
    // {
    //   return m_ptsVanSettings.m_distancesVan;
    // }
    // else
    // {
      return m_distances;
    // }
  }

  SERIALIZABLE(PtsSettings)
  {
    ADD_FLOAT_MEMBER(rangeFront);
    ADD_FLOAT_MEMBER(rangeSide);
    ADD_FLOAT_MEMBER(rangeRear);
    ADD_FLOAT_MEMBER(targetSegmentLength);
    ADD_FLOAT_MEMBER(extractorMinDistance);
    ADD_FLOAT_MEMBER(heightOverGround);
    ADD_MEMBER(osg::Vec2f, projectionTransitionCamAngle);
    ADD_FLOAT_MEMBER(heightMinThreshold);
    ADD_FLOAT_MEMBER(contourAlpha);
    ADD_FLOAT_MEMBER(contourHeight);
    ADD_FLOAT_MEMBER(shadowRadius);
    ADD_FLOAT_MEMBER(spline2DWidthInnerNear);
    ADD_FLOAT_MEMBER(spline2DWidthInnerFar);
    ADD_FLOAT_MEMBER(spline2DWidthOuter);
    ADD_FLOAT_MEMBER(shieldAlphaInner);
    ADD_FLOAT_MEMBER(shieldAlphaOuter);
    ADD_FLOAT_MEMBER(spline3DAlphaTop);
    ADD_FLOAT_MEMBER(spline3DAlphaBottom);
    ADD_FLOAT_MEMBER(spline3DHeight);
    ADD_FLOAT_MEMBER(shieldHeight);
    ADD_FLOAT_MEMBER(hairlineWidth);
    ADD_FLOAT_MEMBER(hairlineAlpha);
    ADD_FLOAT_MEMBER(hairlineBlooming);
    ADD_FLOAT_MEMBER(smoothingGradientSize);
    ADD_MEMBER(osg::Vec4i, colorOn);
    ADD_MEMBER(osg::Vec4i, colorOutline);
    ADD_MEMBER(osg::Vec4i, colorShadow);
    ADD_MEMBER(ColorValues, colors);
    ADD_MEMBER(DistanceValues, distances);
    ADD_MEMBER(pc::util::SpatialFilterData, distanceFilter);
    ADD_MEMBER(PtsVanSettings, ptsVanSettings);
  }

  float m_rangeFront;
  float m_rangeSide;
  float m_rangeRear;
  float m_targetSegmentLength;
  float m_cornerRadius;
  float m_extractorMinDistance;

  float m_heightOverGround;
  osg::Vec2f m_projectionTransitionCamAngle;
  float m_heightMinThreshold;

  float m_shadowRadius;

  float m_spline2DWidthInnerNear;
  float m_spline2DWidthInnerFar;
  float m_spline2DWidthOuter;

  float m_spline3DAlphaTop;
  float m_spline3DAlphaBottom;
  float m_spline3DHeight;

  float m_contourAlpha;
  float m_contourHeight;

  float m_shieldAlphaInner;
  float m_shieldAlphaOuter;
  float m_shieldHeight;

  float m_hairlineWidth;
  float m_hairlineAlpha;
  float m_hairlineBlooming;

  float m_smoothingGradientSize;

  osg::Vec4i m_colorOn;
  osg::Vec4i m_colorOutline;
  osg::Vec4i m_colorShadow;
  ColorValues m_colors;
  DistanceValues m_distances;
  pc::util::SpatialFilterData m_distanceFilter;
  PtsVanSettings m_ptsVanSettings;

};

extern pc::util::coding::Item<PtsSettings> g_ptsSettings;

} // namespace ptsoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTSOVERLAY_PTSSETTINGS_H
