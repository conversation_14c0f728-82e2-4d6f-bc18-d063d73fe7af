//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_ACTIONPOINT
#define CC_ASSETS_TRAJECTORY_SUBASSETS_ACTIONPOINT

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/assets/trajectory/inc/DL1.h"


namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic



class ActionPoint : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:
  ActionPoint(
    pc::core::Framework* f_framework,
    cc::assets::trajectory::commontypes::Side_en f_side,
    float f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    const cc::assets::trajectory::DL1* const f_distanceLine,
    unsigned int f_numOfVerts);

  osg::Image* create2DTexture() const;

  virtual void generateVertexData();

  void animate();

  float getActionPointNearX() const;

protected:

  virtual ~ActionPoint();

  void generateVertexData_usingTexture();

  const unsigned int mc_numOfVerts;
  float m_geomHeight;
  float m_actionPointFarToMidDist;
  float m_currActionPointDist;
  float m_lastActionPointDist;
  float m_actionPointNearX;
  float m_startDist;
  float m_targetDist;
  float m_startTime;
  bool m_animate;

private:

  //! Copy constructor is not permitted.
  ActionPoint (const ActionPoint& other); // = delete
  //! Copy assignment operator is not permitted.
  ActionPoint& operator=(const ActionPoint& other); // = delete

  const osg::ref_ptr<const assets::trajectory::DL1> m_distanceLine;
};


} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_ACTIONPOINT
