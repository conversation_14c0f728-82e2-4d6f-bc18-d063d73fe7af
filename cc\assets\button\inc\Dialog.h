//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_DIALOG_H
#define CC_ASSETS_BUTTON_DIALOG_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/assets/button/inc/TestButton.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/Framework.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"

namespace cc
{
namespace assets
{
namespace button
{
class DialogSettings : public pc::util::coding::ISerializable
{
public:
    DialogSettings()
        : m_dialogMaskPos(1280, 660)
        , m_diagMaskTexturePath("cc/resources/mask.png")
    {
    }

    SERIALIZABLE(DialogSettings)
    {
        ADD_MEMBER(osg::Vec2f, dialogMaskPos);
        ADD_STRING_MEMBER(diagMaskTexturePath);
        ADD_MEMBER(ButtonTextureSettings, diagContentModTexture);
        ADD_MEMBER(osg::Vec2f, diagContentModPos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentModClickArea);
        ADD_MEMBER(ButtonTextureSettings, diagContentDGearActTexture);
        ADD_MEMBER(osg::Vec2f, diagContentDGearActPos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentDGearClickArea);
        ADD_MEMBER(ButtonTextureSettings, diagContentSonarActTexture);
        ADD_MEMBER(osg::Vec2f, diagContentSonarActPos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentSonarClickArea);
        ADD_MEMBER(ButtonTextureSettings, diagContentNarrowLaneActTexture);
        ADD_MEMBER(osg::Vec2f, diagContentNarrowLaneActPos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentNarrowLaneClickArea);
        ADD_MEMBER(ButtonTextureSettings, diagContentVehTransTexture);
        ADD_MEMBER(osg::Vec2f, diagContentVehTransPos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentVehTransClickArea);
        ADD_MEMBER(ButtonTextureSettings, diagContentSteerActTexture);
        ADD_MEMBER(osg::Vec2f, diagContentSteerActPos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentSteerClickArea);
        ADD_MEMBER(ButtonTextureSettings, diagContentNightModeTexture);
        ADD_MEMBER(osg::Vec2f, diagContentNightModePos);
        ADD_MEMBER(cc::assets::uielements::UIData, diagContentNightClickArea);
    }

    osg::Vec2f  m_dialogMaskPos;
    std::string m_diagMaskTexturePath;
    ButtonTextureSettings m_diagContentModTexture;
    osg::Vec2f  m_diagContentModPos;
    cc::assets::uielements::UIData m_diagContentModClickArea;
    ButtonTextureSettings m_diagContentDGearActTexture;
    osg::Vec2f  m_diagContentDGearActPos;
    cc::assets::uielements::UIData m_diagContentDGearClickArea;
    ButtonTextureSettings m_diagContentSonarActTexture;
    osg::Vec2f  m_diagContentSonarActPos;
    cc::assets::uielements::UIData m_diagContentSonarClickArea;
    ButtonTextureSettings m_diagContentNarrowLaneActTexture;
    osg::Vec2f  m_diagContentNarrowLaneActPos;
    cc::assets::uielements::UIData m_diagContentNarrowLaneClickArea;
    ButtonTextureSettings m_diagContentVehTransTexture;
    osg::Vec2f  m_diagContentVehTransPos;
    cc::assets::uielements::UIData m_diagContentVehTransClickArea;
    ButtonTextureSettings m_diagContentSteerActTexture;
    osg::Vec2f  m_diagContentSteerActPos;
    cc::assets::uielements::UIData m_diagContentSteerClickArea;
    ButtonTextureSettings m_diagContentNightModeTexture;
    osg::Vec2f  m_diagContentNightModePos;
    cc::assets::uielements::UIData m_diagContentNightClickArea;
};

extern pc::util::coding::Item<DialogSettings> g_DialogSettings;

class DialogMask : public Button
{
public:
    DialogMask(cc::core::AssetId f_assetId, osg::Camera* f_referenceView = nullptr);

protected:
    void update() override
    {
        setState(ButtonState::AVAILABLE);
        m_dirty = true;
    }

private:
    void onInvalid() override
    {
        setIconEnable(false);
    }
    void onUnavailable() override
    {
        setIconEnable(false);
    }
    void onAvailable() override
    {
        setIconEnable(true);
    }
    void onPressed() override
    {
        setIconEnable(true);
    }
    void onReleased() override
    {
        setIconEnable(true);
    }

private:
    // pc::core::Framework* m_framework;
    vfc::uint32_t m_modifiedCount = ~0u;
};

enum DialogID
{
    DIALOG_MOD,
    DIALOG_DGEARACT,
    DIALOG_SONARACT,
    DIALOG_NARROWLANEACT,
    DIALOG_VEHTRANS,
    DIALOG_STEERACT,
    DIALOG_NIGHTMODE
};
class DialogContent : public Button
{
public:
    DialogContent(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);
    void changeContent(DialogID f_dialogID);
    bool getTouchInside()
    {
        return m_touchInside;
    }
    void resetTouchInside()
    {
        m_touchInside=true;
        m_newTime=true;
    }
protected:
    void update() override;
private:
    void onInvalid() override
    {
        setIconEnable(false);
    }
    void onUnavailable() override
    {
        setIconEnable(false);
    }
    void onAvailable() override
    {
        setIconEnable(true);
    }
    void onPressed() override
    {
        setIconEnable(true);
    }
    void onReleased() override
    {
        setIconEnable(true);
    }

private:
    cc::target::common::EThemeTypeDayNight    m_dayNightTheme = cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT;
    pc::core::Framework* m_framework;
    vfc::uint32_t m_modifiedCount = ~0u;
    bool m_touchInside =true;
    bool m_newTime=true;
};

class IDialogTrigger
{
public:
    virtual void tirgeDialog(DialogID f_dialogID) = 0;
};

class Dialog : public ButtonGroup, public IDialogTrigger
{
public:
    Dialog(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
        : ButtonGroup(f_assetId)
        , m_showDialog(false)
        , m_framework(f_framework)
        , m_showStatus(false)
    {
        addButton(new DialogMask(f_assetId, f_referenceView));
        m_dialogContent =
            new cc::assets::button::DialogContent(cc::core::AssetId::EASSETS_DIALOG_CONTENT, f_framework, f_referenceView);
        m_dialogContent->setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView));
        m_dialogContent->setVertReferenceView(static_cast<pc::core::View*>(f_referenceView));
        addButton(m_dialogContent);
    }

    void tirgeDialog(DialogID f_dialogID) override
    {
        m_dialogContent->changeContent(f_dialogID);
        m_showDialog = true;
        cc::assets::button::ButtonPopController::registerPopButton(static_cast<Button*>(m_dialogContent));
    }

    bool isDialogShow() const
    {
        return m_showDialog;
    }

protected:
    void update() override;
    bool m_showDialog;
    DialogContent * m_dialogContent;
    pc::core::Framework* m_framework;
    bool m_showStatus;
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_DIALOG_H
