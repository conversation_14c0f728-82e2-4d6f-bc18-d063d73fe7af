//-------------------------------------------------------------------------------
// Copyright (c) 2019 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/debugoverlay/inc/DebugOverlay.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include "cc/core/inc/CustomUltrasonic.h"


#include <osg/Geode>
#include <osg/LineWidth>


using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace debugoverlay
{

  // helpers
  float getPosY(DebugOverlay::EDebugLayer f_layer);
  float getWidth(DebugOverlay::EDebugLayer f_layer);
  osg::Vec4f getColor(DebugOverlay::EDebugLayer f_layer);
  osg::Geometry* createOverlayFromPoints(const osg::Vec2Array* cf_points, DebugOverlay::EDebugLayer f_layer, bool f_mirrorPointsOnAxisX, bool f_isLoop); //defined at enf of file


///!
///! UssDebugOverlaySettings
///!
class UssDebugOverlaySettings : public pc::util::coding::ISerializable
{
  public:

  UssDebugOverlaySettings()
  : m_layerWidth(0.02f)                   // meters
  , m_layerPosZ(0.001f)                   // meters
  , m_color(0.0f, 1.0f, 0.2f, 0.3f)       // green
  , m_colorDelimitator(0.0f, 0.0f, 0.0f, 0.6f)       // black
  , m_colorDistanceToOutline(0.5f, 0.0f, 0.1f, 0.3f) // brown
  , m_layerPosZ_toOutline(0.002f)         // meters
  , m_maxDistance(6.0f)                   // meters
  {
  }

  SERIALIZABLE(UssDebugOverlaySettings)
  {
      ADD_FLOAT_MEMBER(layerWidth);
      ADD_FLOAT_MEMBER(layerPosZ);
      ADD_MEMBER(osg::Vec4f, color);
      ADD_MEMBER(osg::Vec4f, colorDelimitator);
      ADD_MEMBER(osg::Vec4f, colorDistanceToOutline);
      ADD_FLOAT_MEMBER(layerPosZ_toOutline);
      ADD_FLOAT_MEMBER(maxDistance);
  }

  float m_layerWidth;         // meters
  float m_layerPosZ;          // meters
  osg::Vec4f m_color;
  osg::Vec4f m_colorDelimitator;
  osg::Vec4f m_colorDistanceToOutline;
  float m_layerPosZ_toOutline; // meters
  float m_maxDistance;        // meters
};

///!
///! VehicleContourDebugOverlaySettings
///!
class VehicleContourDebugOverlaySettings : public pc::util::coding::ISerializable
{
public:

  VehicleContourDebugOverlaySettings()
  : m_layerWidth(0.03f)               // meters
  , m_layerPosZ(0.003f)               // meters
  , m_color(0.8f, 0.0f, 1.0f, 0.4f)   // magenta
  {
  }

  SERIALIZABLE(VehicleContourDebugOverlaySettings)
  {
    ADD_FLOAT_MEMBER(layerWidth);
    ADD_FLOAT_MEMBER(layerPosZ);
    ADD_MEMBER(osg::Vec4f, color);
  }

  float m_layerWidth;         // meters
  float m_layerPosZ;          // meters
  osg::Vec4f m_color;
};


///!
///! VehicleSizeDebugOverlaySettings
///!
class VehicleSizeDebugOverlaySettings : public pc::util::coding::ISerializable
{
public:

  VehicleSizeDebugOverlaySettings()
   : m_layerWidth(0.02f)                   // meters
  , m_totalDistance(2.0f)             // meters
  , m_layerPosZ(0.003f)               // meters
  , m_color(0.5f, 0.7f, 1.0f, 0.5f)   // blue
  {
  }

  SERIALIZABLE(VehicleSizeDebugOverlaySettings)
  {
    ADD_FLOAT_MEMBER(layerWidth);
    ADD_FLOAT_MEMBER(totalDistance);
    ADD_FLOAT_MEMBER(layerPosZ);
    ADD_MEMBER(osg::Vec4f, color);
  }

  float m_layerWidth;         // meters
  float m_totalDistance;  // meters
  float m_layerPosZ;      // meters
  osg::Vec4f m_color;
};

//!
//! DebugOverlay coding parameters
//!
class DebugOverlayCodingParams : public pc::util::coding::ISerializable
{
public:
  DebugOverlayCodingParams()
   : m_enabled(false)
   , m_uss()
   , m_vehicleContour()
   , m_vehicleSize()
  {
  }


  SERIALIZABLE(DebugOverlayCodingParams)
  {
    ADD_BOOL_MEMBER(enabled);
    ADD_MEMBER(UssDebugOverlaySettings, uss);
    ADD_MEMBER(VehicleContourDebugOverlaySettings, vehicleContour);
    ADD_MEMBER(VehicleSizeDebugOverlaySettings, vehicleSize);
  }

  bool m_enabled;
  UssDebugOverlaySettings m_uss;
  VehicleContourDebugOverlaySettings m_vehicleContour;
  VehicleSizeDebugOverlaySettings m_vehicleSize;
};

pc::util::coding::Item<DebugOverlayCodingParams> g_codingParams("DebugOverlay");


// --------------------------------------------- USS OVERLAY ------------------------------------------------//

UssDebugOverlay::UssDebugOverlay(
  cc::core::CustomFramework* f_pCustomFramework,
  pc::vehicle::AbstractZoneLayout* f_zoneLayout,
  bool f_showDistanceToOutline)
    : m_pCustomFramework(f_pCustomFramework)
    , m_ussLayout(f_zoneLayout)
    , m_lastUpdateUss(0u)
    , m_showDistanceToOutline(f_showDistanceToOutline)
{
  size_t contourSize = static_cast<size_t>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES);

  osg::ref_ptr<osg::Vec2Array> l_contourPoints = new osg::Vec2Array(contourSize);
  for(size_t i = 0; i < contourSize; i++)
  {
    (*l_contourPoints)[i] = m_ussLayout->getLeftBorderLine(i).m_innerPoint;
  }

  bool mirrorPointsOnAxisX = false;
  bool isLoop = true;

  m_ultrasonicGeode = new osg::Geode();
  osg::Group::addChild(m_ultrasonicGeode);

  // the contour
  auto l_layer = f_showDistanceToOutline ? DebugOverlay::DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE  : DebugOverlay::DEBUG_LAYER_ULTRASONIC;
  osg::Geometry* l_ussGeom = createOverlayFromPoints(l_contourPoints, l_layer , mirrorPointsOnAxisX, isLoop);
  m_ultrasonicGeode->addDrawable(l_ussGeom);

  //! separator/delimitator
  osg::Geode* l_zoneLayoutGeode = new osg::Geode;
  osg::Group::addChild(l_zoneLayoutGeode);
  bool l_drawOutline = true;
  float l_posZ = g_codingParams->m_uss.m_layerPosZ + 0.01f;
  for (unsigned int i = 0; i < static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES); ++i)
  {
    l_zoneLayoutGeode->addDrawable(createZone(i, l_drawOutline, g_codingParams->m_uss.m_colorDelimitator, l_posZ));
  }
  osg::StateSet* l_zoneLayoutStateSet = l_zoneLayoutGeode->getOrCreateStateSet();
  l_zoneLayoutStateSet->setAttribute(new osg::LineWidth(2));
  
  setNumChildrenRequiringUpdateTraversal(1u);
}

void UssDebugOverlay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    const pc::daddy::UltrasonicDataDaddy* l_uss = m_pCustomFramework->m_customUltrasonicDataReceiver.getData();
    if ((nullptr != l_uss) && (l_uss->m_sequenceNumber != m_lastUpdateUss))
    {
      m_lastUpdateUss = l_uss->m_sequenceNumber;
      
      if(m_showDistanceToOutline)
      {
        osg::Geode* l_distancesToOutlineGeode = getOrCreateDistancesToOutlineGeode();
        for (unsigned int i = 0; i < static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES); ++i)
        {
          osg::Geometry* l_distancesToOutlineGeometry = l_distancesToOutlineGeode->getDrawable(i)->asGeometry();
          osg::Vec3Array* l_vertices = static_cast<osg::Vec3Array*> (l_distancesToOutlineGeometry->getVertexArray());
          float l_distance = std::min(l_uss->m_Data[i].getDistanceToOutline(), g_codingParams->m_uss.m_maxDistance);         //DO NOT COMMIT
          setDistance(l_vertices, i, l_distance, g_codingParams->m_uss.m_layerPosZ + 0.005f);
          l_distancesToOutlineGeometry->dirtyBound();
        }
      }
      else
      {
        osg::Geode* l_distancesGeode = getOrCreateDistancesGeode();
        for (unsigned int i = 0; i < static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES); ++i)
        {
          osg::Geometry* l_distanceGeometry = l_distancesGeode->getDrawable(i)->asGeometry();
          osg::Vec3Array* l_vertices = static_cast<osg::Vec3Array*> (l_distanceGeometry->getVertexArray());
          float l_distance = std::min(l_uss->m_Data[i].getDistance(), g_codingParams->m_uss.m_maxDistance);
          setDistance(l_vertices, i, l_distance, g_codingParams->m_uss.m_layerPosZ);
          l_distanceGeometry->dirtyBound();
        }
      }
    }  
    osg::Group::traverse(f_nv);
  }
}

osg::Geode* UssDebugOverlay::getOrCreateDistancesGeode()
{
  if (!m_distancesGeode.valid())
  {
    m_distancesGeode = new osg::Geode;
    addChild(m_distancesGeode);
    bool l_drawOutline = false;
    for (unsigned int i = 0; i < static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES); ++i)
    {
      m_distancesGeode->addDrawable(createZone(i, l_drawOutline,  g_codingParams->m_uss.m_color, g_codingParams->m_uss.m_layerPosZ));
    }
  }
  return m_distancesGeode.get();
}

osg::Geode* UssDebugOverlay::getOrCreateDistancesToOutlineGeode()
{
  if (!m_distancesToOutlineGeode.valid())
  {
    m_distancesToOutlineGeode = new osg::Geode;
    addChild(m_distancesToOutlineGeode);
    bool l_drawOutline = false;
    for (unsigned int i = 0; i < static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES); ++i)
    {
      m_distancesToOutlineGeode->addDrawable(createZone(i, l_drawOutline,  g_codingParams->m_uss.m_colorDistanceToOutline, g_codingParams->m_uss.m_layerPosZ + 0.1));
    }
  }
  return m_distancesToOutlineGeode.get();
}

void UssDebugOverlay::setDistance(osg::Vec3Array* f_vertices, unsigned int f_zoneIdx, float f_distance, float f_zCoord) const
{
  const osg::Vec2f l_refPointL = m_ussLayout->getReferencePointLeft(f_zoneIdx);
  const osg::Vec2f l_refPointR = m_ussLayout->getReferencePointRight(f_zoneIdx);
  (*f_vertices)[0u] = osg::Vec3f(l_refPointL, f_zCoord);
  (*f_vertices)[1u] = osg::Vec3f(l_refPointL + (m_ussLayout->getDirectionLeft(f_zoneIdx) * f_distance), f_zCoord);
  (*f_vertices)[2u] = osg::Vec3f(l_refPointR, f_zCoord);
  (*f_vertices)[3u] = osg::Vec3f(l_refPointR + (m_ussLayout->getDirectionRight(f_zoneIdx) * f_distance), f_zCoord);
  f_vertices->dirty();
}

osg::Geometry* UssDebugOverlay::createZone(unsigned int f_zoneIdx, bool f_outline, const osg::Vec4f& f_color, float f_posZ)
{
  osg::Geometry* l_geometry = pc::util::osgx::createGeometry();

  float l_distance = 1.0f;
  osg::Vec3Array* l_vertices = new osg::Vec3Array(4u);
  setDistance(l_vertices, f_zoneIdx, l_distance, f_posZ);
  l_geometry->setVertexArray(l_vertices);

  osg::Vec4Array* l_colors = new osg::Vec4Array(1u);
  (*l_colors)[0u] = f_color;
  l_geometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);

  if (f_outline)
  {
    l_geometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINES, static_cast<GLint>(0), static_cast<GLsizei>(4)));
  }
  else
  {
    osg::DrawElementsUByte* l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6);
    (*l_indices)[0u] = 0;
    (*l_indices)[1u] = 1;
    (*l_indices)[2u] = 2;
    (*l_indices)[3u] = 2;
    (*l_indices)[4u] = 1;
    (*l_indices)[5u] = 3;
    l_geometry->addPrimitiveSet(l_indices);
  }

  osg::Vec3Array* l_normals = new osg::Vec3Array(1);
  (*l_normals)[0u] = osg::Vec3f(0.0f, 0.0f, 1.0f);
  l_geometry->setNormalArray(l_normals);
  l_geometry->setNormalBinding(osg::Geometry::BIND_OVERALL);
  return l_geometry;
}


// --------------------------------------------- VEHICLE CONTOUR ------------------------------------------------//

VehicleContourDebugOverlay::VehicleContourDebugOverlay(const cc::assets::trajectory::mainlogic::Inputs_st& fc_input)
  : m_inputData(fc_input)
{
  size_t contourSize = m_inputData.External.Car.VehicleContour_Left->size();

  osg::ref_ptr<osg::Vec2Array> l_contourPoints = new osg::Vec2Array(contourSize);
  for(size_t i = 0; i < contourSize; i++)
  {
    (*l_contourPoints)[i] = (*m_inputData.External.Car.VehicleContour_Left)[i];
  }

  bool mirrorPointsOnAxisX = true;
  bool isLoop = true;
  m_vehicleContourGeometry = createOverlayFromPoints(l_contourPoints, DebugOverlay::DEBUG_LAYER_VECHICLE_CONTOUR, mirrorPointsOnAxisX, isLoop);

  m_vehicleContourGeode = new osg::Geode();
  m_vehicleContourGeode->addDrawable(m_vehicleContourGeometry);
  osg::Group::addChild(m_vehicleContourGeode);
}

osg::Vec2f VehicleContourDebugOverlay::getWidestPointAtVehicleContour(bool f_withMirrors)
{
  size_t contourSize = m_inputData.External.Car.VehicleContour_Left->size();

  osg::Vec2f biggestY(0.0f, 0.0f);
  if(contourSize > 0u)
  {
    biggestY = (*m_inputData.External.Car.VehicleContour_Left)[0];
    for(size_t i = 0; i < contourSize; i++)
    {
      if((*m_inputData.External.Car.VehicleContour_Left)[i].y() > biggestY.y())
      {
        //  ignore mirror point if necessary
        if(f_withMirrors || (i != static_cast<size_t>(m_inputData.External.Car.MirrorPointIndex)))
        {
          biggestY = (*m_inputData.External.Car.VehicleContour_Left)[i];
        }
      }
    }
  }

  return biggestY;
}


// --------------------------------------------- VEHICLE SIZE    ------------------------------------------------//

VehicleSizeDebugOverlay::VehicleSizeDebugOverlay(float f_initialDistance)
: m_parallelPlanesCurrentDist(f_initialDistance)
{
  rebuildPlanes();
}

void VehicleSizeDebugOverlay::rebuildPlanes()
{
  XLOG_INFO_OS( g_AppContext ) << "Debug Overlay: distance between planes = "<< m_parallelPlanesCurrentDist << " mts" << XLOG_ENDL;
  deletePlanes();

  m_parallelPlanesGeometryLeft = createParallelPlane(-m_parallelPlanesCurrentDist * 0.5f);
  m_parallelPlanesGeometryRight = createParallelPlane(m_parallelPlanesCurrentDist * 0.5f);

  m_parallelPlanesGeometryLeft_topView = createParallelPlaneTopView(-m_parallelPlanesCurrentDist * 0.5f);
  m_parallelPlanesGeometryRight_topView = createParallelPlaneTopView(m_parallelPlanesCurrentDist * 0.5f);

  m_parallelPlanesGeode = new osg::Geode();
  m_parallelPlanesGeode->addDrawable(m_parallelPlanesGeometryLeft);
  m_parallelPlanesGeode->addDrawable(m_parallelPlanesGeometryRight);
  m_parallelPlanesGeode->addDrawable(m_parallelPlanesGeometryLeft_topView);
  m_parallelPlanesGeode->addDrawable(m_parallelPlanesGeometryRight_topView);
  osg::Group::addChild(m_parallelPlanesGeode);
}

osg::Geometry*  VehicleSizeDebugOverlay::createParallelPlane(float f_distance, float f_height, float f_length)
{
  osg::Geometry* geom =  osg::createTexturedQuadGeometry(
    osg::Vec3f(-f_length * 0.25f,   f_distance,     0.0f),    // bottom corner
    osg::Vec3f(f_length,            0.0f,           0.0f),    // length
    osg::Vec3f(0.0f,                0.0f,           f_height) // height
    );

  osg::Vec4Array* color = new osg::Vec4Array(1u);
  (*color)[0u] = getColor(DebugOverlay::DEBUG_LAYER_PARALLEL_PLANES);
  color->dirty();
  geom->setColorArray(color, osg::Array::BIND_OVERALL);

  return geom;
}

osg::Geometry*  VehicleSizeDebugOverlay::createParallelPlaneTopView(float f_distance, float f_width, float f_length)
{
  float l_center = f_distance - (f_width*0.5f);
  float posY = getPosY(DebugOverlay::DEBUG_LAYER_PARALLEL_PLANES);
  osg::Geometry* geom =  osg::createTexturedQuadGeometry(
    osg::Vec3f(-f_length * 0.25f,  l_center,       posY),    //  bottom corner
    osg::Vec3f(f_length,           0.0f,           0.0f),    //  length
    osg::Vec3f(0.0f,               f_width,        0.0f));   //  width

  osg::Vec4Array* color = new osg::Vec4Array(1u);
  (*color)[0u] = getColor(DebugOverlay::DEBUG_LAYER_PARALLEL_PLANES);
  (*color)[0u].a() = 1.0f;
  color->dirty();
  geom->setColorArray(color, osg::Array::BIND_OVERALL);

  return geom;
}

void VehicleSizeDebugOverlay::deletePlanes()
{
  if(nullptr != m_parallelPlanesGeode)
  {
    if(nullptr != m_parallelPlanesGeometryLeft)
    {
      m_parallelPlanesGeode->removeDrawable(m_parallelPlanesGeometryLeft);
    }

    if(nullptr != m_parallelPlanesGeometryLeft)
    {
      m_parallelPlanesGeode->removeDrawable(m_parallelPlanesGeometryRight);
    }

    if(nullptr != m_parallelPlanesGeometryLeft_topView)
    {
      m_parallelPlanesGeode->removeDrawable(m_parallelPlanesGeometryLeft_topView);
    }

    if(nullptr != m_parallelPlanesGeometryRight_topView)
    {
      m_parallelPlanesGeode->removeDrawable(m_parallelPlanesGeometryRight_topView);
    }
    removeChild(m_parallelPlanesGeode);

    m_parallelPlanesGeometryLeft = nullptr;
    m_parallelPlanesGeometryRight = nullptr;
    m_parallelPlanesGeometryLeft_topView = nullptr;
    m_parallelPlanesGeometryRight_topView = nullptr;
    m_parallelPlanesGeode = nullptr;
  }
}

// --------------------------------------------- DEBUG OVERLAY  ------------------------------------------------//

DebugOverlay::DebugOverlay(cc::core::CustomFramework* f_pCustomFramework,
    pc::vehicle::AbstractZoneLayout* f_zoneLayout,
    const cc::assets::trajectory::mainlogic::Inputs_st& fc_input,
    const osg::Vec2f& f_viewSize,
    const osg::Vec3f& f_cameraPosition,
    uint16_t f_renderBinOrder)
  : m_pCustomFramework(f_pCustomFramework)
{
  m_ussLayer = new UssDebugOverlay(f_pCustomFramework, f_zoneLayout, false);
  m_ussToOtlineLayer = new UssDebugOverlay(f_pCustomFramework, f_zoneLayout, true);
  m_vehicleContourLayer = new VehicleContourDebugOverlay(fc_input);
  m_vehicleSizeLayer = new VehicleSizeDebugOverlay(g_codingParams->m_vehicleSize.m_totalDistance);

  osg::StateSet* l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
  l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_stateSet->setRenderBinDetails(f_renderBinOrder, "RenderBin");

  m_activeLayers[DEBUG_LAYER_ULTRASONIC]                  = false;
  m_activeLayers[DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE]  = false;
  m_activeLayers[DEBUG_LAYER_VECHICLE_CONTOUR]            = false;
  m_activeLayers[DEBUG_LAYER_PARALLEL_PLANES]             = false;
}

void DebugOverlay::traverse(osg::NodeVisitor& f_nv)
{
  checkCommands();
  if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    if (nullptr != m_ussLayer && m_activeLayers[DEBUG_LAYER_ULTRASONIC])
    {
      m_ussLayer->accept(f_nv);
    }
     if (nullptr != m_ussToOtlineLayer && m_activeLayers[DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE])
    {
      m_ussToOtlineLayer->accept(f_nv);
    }
    if (nullptr != m_vehicleContourLayer && m_activeLayers[DEBUG_LAYER_VECHICLE_CONTOUR])
    {
      m_vehicleContourLayer->accept(f_nv);
    }
    if (nullptr != m_vehicleSizeLayer && m_activeLayers[DEBUG_LAYER_PARALLEL_PLANES])
    {
      m_vehicleSizeLayer->accept(f_nv);
    }
  }
  osg::Switch::traverse(f_nv);
}

void DebugOverlay::checkCommands()
{
  static bool ls_isRepeated = false;    // to avoid duplicated actions
  if (true == m_pCustomFramework->m_debugOverlayAction_ReceiverPort.isConnected())
  {
    //  EDebugOverlayAction
    const cc::daddy::DebugOverlayAction_t* l_pDebugOverlayData = m_pCustomFramework->m_debugOverlayAction_ReceiverPort.getData();
    if (0 != l_pDebugOverlayData)
    {
      const EDebugOverlayAction action = l_pDebugOverlayData->m_Data;

      if(ls_isRepeated)
      {
         if(DEBUG_OVRL_AC_NONE == action)
         {
            ls_isRepeated = false;
         }
      }
      else
      {
        debugRunCommands(action);

        if(DEBUG_OVRL_AC_NONE != action)
        {
          ls_isRepeated = true;
          cc::daddy::DebugOverlayAction_t& l_container = cc::daddy::CustomDaddyPorts::sm_FR_DebugOverlayAction_SenderPort.reserve();
          l_container.m_Data = DEBUG_OVRL_AC_NONE;
          cc::daddy::CustomDaddyPorts::sm_FR_DebugOverlayAction_SenderPort.deliver();
        }
      }
    }
  }
}

void DebugOverlay::debugRunCommands(const EDebugOverlayAction f_action)
{
  switch((vfc::uint8_t)f_action)
  {
    case (vfc::uint8_t)DEBUG_OVRL_AC_RESET_AND_SHOW_ALL:
      debugOverlayActionResetAndShowAll();
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_HIDE_ALL:
      hideLayer(DEBUG_LAYER_ULTRASONIC);
      hideLayer(DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE);
      hideLayer(DEBUG_LAYER_VECHICLE_CONTOUR);
      hideLayer(DEBUG_LAYER_PARALLEL_PLANES);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_TOGGLE_VEH_CONTOUR:
      toggleLayer(DEBUG_LAYER_VECHICLE_CONTOUR);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_TOGGLE_USS:
      toggleLayer(DEBUG_LAYER_ULTRASONIC);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_TOGGLE_USS_DIST_TO_LAYOUT:
      toggleLayer(DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_TOGGLE_DIST_PLANES:
      toggleLayer(DEBUG_LAYER_PARALLEL_PLANES);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_GET_CURR_DISTANCE:
      debugOverlayActionGetCurrentDistance();
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_INC_PLANE_DIST_1:
      debugOverlayActionChangePlaneDistance(0.01f);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_INC_PLANE_DIST_5:
      debugOverlayActionChangePlaneDistance(0.05f);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_INC_PLANE_DIST_10:
      debugOverlayActionChangePlaneDistance(0.1f);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_DEC_PLANE_DIST_1:
      debugOverlayActionChangePlaneDistance(-0.01f);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_DEC_PLANE_DIST_5:
      debugOverlayActionChangePlaneDistance(-0.05f);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_DEC_PLANE_DIST_10:
      debugOverlayActionChangePlaneDistance(-0.1f);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_DIST_AT_WIDEST_POINT_MIRRORS:
      debugOverlayActionDistanceAtWidestPointMirrors(true);
      break;

    case (vfc::uint8_t)DEBUG_OVRL_AC_DIST_AT_WIDEST_POINT_NO_MIRRORS:
      debugOverlayActionDistanceAtWidestPointMirrors(false);
      break;
  }
}

void DebugOverlay::debugOverlayActionResetAndShowAll()
{
  if(m_vehicleSizeLayer)
  {
    m_vehicleSizeLayer->m_parallelPlanesCurrentDist = g_codingParams->m_vehicleSize.m_totalDistance;  // mts
    m_vehicleSizeLayer->rebuildPlanes();
  }
  showLayer(DEBUG_LAYER_ULTRASONIC);
  showLayer(DEBUG_LAYER_VECHICLE_CONTOUR);
  showLayer(DEBUG_LAYER_PARALLEL_PLANES);
}

void DebugOverlay::debugOverlayActionGetCurrentDistance() const
{
  if(m_vehicleSizeLayer)
  {
    XLOG_INFO_OS( g_AppContext ) << "Debug Overlay: distance between planes = "<< m_vehicleSizeLayer->m_parallelPlanesCurrentDist << " mts" << XLOG_ENDL;
  }
  else
  {
    XLOG_INFO_OS( g_AppContext ) << "Debug Overlay: distance layer was not initialized correctly." << XLOG_ENDL;
  }
}

void DebugOverlay::debugOverlayActionChangePlaneDistance(const float f_distance)
{
  if(m_vehicleSizeLayer)
  {
    m_vehicleSizeLayer->m_parallelPlanesCurrentDist += f_distance;  // mts
    m_vehicleSizeLayer->rebuildPlanes();
  }
}

void DebugOverlay::debugOverlayActionDistanceAtWidestPointMirrors(const bool f_includeMirrors)
{
  if(m_vehicleSizeLayer && m_vehicleContourLayer)
  {
    float l_widestY = m_vehicleContourLayer->getWidestPointAtVehicleContour(f_includeMirrors).y() * 2.0f;
    m_vehicleSizeLayer->m_parallelPlanesCurrentDist = l_widestY;  // mts
    m_vehicleSizeLayer->rebuildPlanes();
  }
}

void DebugOverlay::showLayer(EDebugLayer f_layer)
{
  m_activeLayers[f_layer] = true;
}


void DebugOverlay::hideLayer(EDebugLayer f_layer)
{
  m_activeLayers[f_layer] = false;
}


void DebugOverlay::toggleLayer(EDebugLayer f_layer)
{
  if(m_activeLayers[f_layer])
  {
    hideLayer(f_layer);
  }
  else
  {
    showLayer(f_layer);
  }
}


// --------------------------------------------- HELPERS  ------------------------------------------------//

osg::Geometry* createOverlayFromPoints(const osg::Vec2Array* cf_points, DebugOverlay::EDebugLayer f_layer, bool f_mirrorPointsOnAxisX, bool f_isLoop)
{
   if(cf_points->empty() || (cf_points->size() < 2u))
  {
    return nullptr;
  }

  cc::assets::trajectory::commontypes::VertexData_st l_vertexData;
  int pointsCount = static_cast<int>(cf_points->size());
  int segmentCount = (f_mirrorPointsOnAxisX?  pointsCount * 2 : pointsCount)-1;
  if(f_isLoop)
  {
    ++segmentCount;
  }
  int vertexCount = 2 * (segmentCount + 1);

  l_vertexData.Vertices  = new osg::Vec3Array(vertexCount);
  l_vertexData.Normals   = new osg::Vec3Array(1);
  l_vertexData.Colors    = new osg::Vec4Array(vertexCount);
  l_vertexData.TexCoords = new osg::Vec2Array(vertexCount);
  l_vertexData.Indices   = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), segmentCount * 6);

  osg::Geometry* l_shape = new osg::Geometry;
  l_shape->setUseDisplayList(false);
  l_shape->setVertexArray(l_vertexData.Vertices);
  l_shape->setNormalArray(l_vertexData.Normals);
  l_shape->setNormalBinding(osg::Geometry::BIND_OVERALL);
  l_shape->setColorArray(l_vertexData.Colors);
  l_shape->setColorBinding(osg::Geometry::BIND_PER_VERTEX);
  l_shape->setTexCoordArray(0u, l_vertexData.TexCoords);
  l_shape->addPrimitiveSet(l_vertexData.Indices);

  // initial points
  std::vector<osg::Vec2f> l_vertexArray(vertexCount);
  for(int i = 0; i < pointsCount; ++i)
  {
    l_vertexArray[(2*i)]    = (*cf_points)[i];
    l_vertexArray[(2*i)+1]  = (*cf_points)[i];
  }

  // points to be mirrored around the X axis
  if(f_mirrorPointsOnAxisX)
  {
    for (int i = 0; i < pointsCount*2; ++i)
    {
      l_vertexArray[i + (pointsCount*2)] = osg::Vec2f(
         l_vertexArray[(pointsCount*2) - i - 1].x(),
        -l_vertexArray[(pointsCount*2) - i - 1].y());
    }
  }

  if(f_isLoop)
  {
    l_vertexArray[vertexCount-2] = (*cf_points)[0];
    l_vertexArray[vertexCount-1] = (*cf_points)[0];
  }

  // Right perpendicular vectors
  osg::Vec2f l_segment0_RightPerpVec;
  osg::Vec2f l_segment1_RightPerpVec;
  // Normals
  osg::Vec2f l_leftNormal;
  osg::Vec2f l_rightNormal;
  // Transform to the left or right edge of the line (from the wheel track center spline)
  osg::Vec2f l_toLineLeftEdge;
  osg::Vec2f l_toLineRightEdge;


  for (int l_vertexIndex = 0; l_vertexIndex < vertexCount; l_vertexIndex += 2)
  {
    if (0 == l_vertexIndex)
    {
      // First spline vertex
      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 2]
                              - l_vertexArray[l_vertexIndex + 0];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment1_RightPerpVec;
      l_rightNormal.normalize();
    }
    else if (vertexCount -2 == l_vertexIndex)
    {
      // Last spline vertex
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0]
                              - l_vertexArray[l_vertexIndex - 2];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec;
      l_rightNormal.normalize();
    }
    else
    {
      //  Every other vertex
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0]
                              - l_vertexArray[l_vertexIndex - 2];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 2]
                              - l_vertexArray[l_vertexIndex + 0];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec + l_segment1_RightPerpVec;
      l_rightNormal.normalize();
    }
    l_leftNormal = -l_rightNormal;

    l_toLineLeftEdge  = l_leftNormal * getWidth(f_layer);
    l_toLineRightEdge = l_rightNormal * 0.0f;

    float l_posY = getPosY(f_layer);
    osg::Vec4f l_color_solid =  getColor(f_layer);
    (*l_vertexData.Colors)  [l_vertexIndex  + 0] = l_color_solid;
    (*l_vertexData.Colors)  [l_vertexIndex  + 1] = l_color_solid;
    (*l_vertexData.Vertices)[l_vertexIndex  + 0].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineLeftEdge,  l_posY) );
    (*l_vertexData.Vertices)[l_vertexIndex  + 1].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineRightEdge, l_posY) );
  }

  (*l_vertexData.Normals)[0u] = osg::Vec3f(0.0f, 0.0f, 1.0f);
  l_vertexData.Indices->clear();

  for (int l_quadIndex = 0; l_quadIndex < segmentCount; l_quadIndex++)
  {
    int l_vertexIndex = l_quadIndex * 2;
    // Top left triangle
    l_vertexData.Indices->push_back(l_vertexIndex + 1);
    l_vertexData.Indices->push_back(l_vertexIndex + 0);
    l_vertexData.Indices->push_back(l_vertexIndex + 2);
    // Bottom right triangle
    l_vertexData.Indices->push_back(l_vertexIndex + 2);
    l_vertexData.Indices->push_back(l_vertexIndex + 3);
    l_vertexData.Indices->push_back(l_vertexIndex + 1);
  }

  return l_shape;
}

osg::Vec4f getColor(DebugOverlay::EDebugLayer f_layer)
{
  switch(f_layer)
  {
    case DebugOverlay::DEBUG_LAYER_ULTRASONIC:                  return  g_codingParams->m_uss.m_color;
    case DebugOverlay::DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE:  return  g_codingParams->m_uss.m_colorDistanceToOutline;
    case DebugOverlay::DEBUG_LAYER_VECHICLE_CONTOUR:            return  g_codingParams->m_vehicleContour.m_color;
    case DebugOverlay::DEBUG_LAYER_PARALLEL_PLANES:             return  g_codingParams->m_vehicleSize.m_color;
    default:                                                    return  osg::Vec4f(1.0f, 1.0f, 1.0f, 0.5f);  //!  white.
  }

  return  osg::Vec4f(1.0f, 1.0f, 1.0f, 0.5f);  //!  white.
}

float getPosY(DebugOverlay::EDebugLayer f_layer)
{
  switch(f_layer)
  {
    case DebugOverlay::DEBUG_LAYER_ULTRASONIC:                  return  g_codingParams->m_uss.m_layerPosZ;
    case DebugOverlay::DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE:  return  g_codingParams->m_uss.m_layerPosZ_toOutline;
    case DebugOverlay::DEBUG_LAYER_VECHICLE_CONTOUR:            return  g_codingParams->m_vehicleContour.m_layerPosZ;
    case DebugOverlay::DEBUG_LAYER_PARALLEL_PLANES:             return  g_codingParams->m_vehicleSize.m_layerPosZ;
    default:                                                    return  0.1f;
  }

  return  0.1f;
}

float getWidth(DebugOverlay::EDebugLayer f_layer)
{
  switch(f_layer)
  {
    case DebugOverlay::DEBUG_LAYER_ULTRASONIC:                  return  g_codingParams->m_uss.m_layerWidth;
    case DebugOverlay::DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE:  return  g_codingParams->m_uss.m_layerWidth;
    case DebugOverlay::DEBUG_LAYER_VECHICLE_CONTOUR:            return  g_codingParams->m_vehicleContour.m_layerWidth;
    case DebugOverlay::DEBUG_LAYER_PARALLEL_PLANES:             return  g_codingParams->m_vehicleSize.m_layerWidth;
    default:                                                    return  0.3f;
  }
  return  0.3f;
}

} // namespace DebugOverlay
} // namespace assets
} // namespace cc
