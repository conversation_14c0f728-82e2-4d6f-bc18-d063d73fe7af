//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityManager.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/VirtualRealityManager.h"
#include "cc/assets/virtualreality/inc/LowpolyPedestrian.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/ShaderManager.h"

#include <memory>
#include <osgDB/ReadFile>
#include <osg/Geometry>
#include <osg/Texture2D>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace virtualreality
{


VirtualRealityManager::VirtualRealityManager(pc::core::Framework* f_framework)
: m_framework{f_framework}
, m_lastUpdate{0u}
{
  setName("VirtualRealityManager");
  setNumChildrenRequiringUpdateTraversal(1u);
  setCullingActive(false);

  //! setup object assets
  m_virtualRealityAssets = new osg::Switch;
  m_virtualRealityAssets->setName("virtualRealityAssets");

  const std::unique_ptr<VirtualRealityFactory> l_factory = std::make_unique<VirtualRealityFactory>();

  for (vfc::uint8_t i = 0u; i < cc::target::common::g_parkSlotCommandQuantity; i++)
  {
    m_virtualRealityAssets->addChild(l_factory->createObject(EObjectType::LOWPOLYVEHICLE), false);  // 0, 2, 4... is vehicle. // PRQA S 3803
    m_virtualRealityAssets->addChild(l_factory->createObject(EObjectType::SLOT), false);  // 1, 3, 5... is slot. // PRQA S 3803
  }
  // ego vehicle
  m_virtualRealityAssets->addChild(l_factory->createObject(EObjectType::EGOVEHICLE), false);  // cc::target::common::g_parkSlotCommandQuantity*2 // PRQA S 3803

  for (vfc::uint8_t i = 0u; i < g_pedestrianDisplayQuantity; i++)
  {
    m_virtualRealityAssets->addChild(l_factory->createObject(EObjectType::LOWPOLYPEDESTRAIN),false); // PRQA S 3803
  }

  osg::Group::addChild(m_virtualRealityAssets.get()); // PRQA S 3803

  m_dataHandler = new VirtualRealityDataHandler(f_framework);
}

VirtualRealityManager::~VirtualRealityManager() = default;



VirtualRealityObject* VirtualRealityManager::getVirtualObject(vfc::uint8_t f_index) // PRQA S 4211
{
  return static_cast<VirtualRealityObject*> (m_virtualRealityAssets->getChild(f_index)); // PRQA S 3076
}

VirtualParkSlot* VirtualRealityManager::getVirtualSlot(vfc::uint8_t f_index) // PRQA S 4211
{
  return static_cast<VirtualParkSlot*> (m_virtualRealityAssets->getChild(f_index)); // PRQA S 3076
}

LowpolyPedestrian* VirtualRealityManager::getVirtualPedestrian(vfc::uint8_t f_index) // PRQA S 4211
{
  return static_cast<LowpolyPedestrian*> (m_virtualRealityAssets->getChild(f_index)); // PRQA S 3076
}

void VirtualRealityManager::update() // PRQA S 2755
{
  m_dataHandler->updateData();

  cc::core::CustomFramework* const l_customFramework = m_framework->asCustomFramework();

  if (l_customFramework->m_parkSlotRefinedReceiver.hasData())
  {
    const cc::daddy::ParkSlotDaddy_t* const l_parkSlots = l_customFramework->m_parkSlotRefinedReceiver.getData();

    Position_st l_position = {0.0f, 0.0f, 0.0f, 0.0f};
    VirtualRealityObject* l_virtualVehicle = getVirtualObject(0u);
    VirtualParkSlot* l_virtualSlot = getVirtualSlot(1u);

    for (vfc::uint8_t i = 0u; i < cc::target::common::g_parkSlotCommandQuantity; i++)
    {
      l_position.m_x   = static_cast<vfc::float32_t>(l_parkSlots->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_x) / 1024.0f;
      l_position.m_y   = static_cast<vfc::float32_t>(l_parkSlots->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_y) / 1024.0f;
      l_position.m_phi = static_cast<vfc::float32_t>(l_parkSlots->m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_phi) / 4096.0f;
      const cc::target::common::EParkSlotOrientationType l_orientationType    = l_parkSlots->m_Data[i].m_parkSlotOrientationType;
      const cc::target::common::ParkSlotPostion_st       l_slotCornerPosition = l_parkSlots->m_Data[i].m_parkSlotPosition;

      if (isShow(m_framework->asCustomFramework())
      && (cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_OCCUPIED == l_parkSlots->m_Data[i].m_parkSlotAvailableStatus))
      {
        // Vehicle
        l_position.m_z = 0.0f;
        l_virtualVehicle = getVirtualObject(i * 2u);
        l_virtualVehicle->setVisibility(true);
        l_virtualVehicle->setPosition(l_position);
        l_virtualVehicle->dirty();
        m_virtualRealityAssets->setValue(i * 2u, true);

        // Slot
        l_position.m_z = 0.01f;
        l_virtualSlot = getVirtualSlot(i * 2u + 1u);
        l_virtualSlot->setVisibility(true);
        l_virtualSlot->setPosition(l_position);
        l_virtualSlot->setSlotAvailableType(l_parkSlots->m_Data[i].m_parkSlotAvailableStatus);
        l_virtualSlot->setSlotOrientationType(l_orientationType);
        l_virtualSlot->setSlotCornerPosition(l_slotCornerPosition);
        l_virtualSlot->dirty();
        m_virtualRealityAssets->setValue(i * 2u + 1u, true);
      }
      else if (isShow(m_framework->asCustomFramework()) &&
              ( (cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_NOT_SELECTABLE == l_parkSlots->m_Data[i].m_parkSlotAvailableStatus)
              ||(cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_SELECTABLE     == l_parkSlots->m_Data[i].m_parkSlotAvailableStatus)
              ||(cc::target::common::EParkSlotAvailableStatus::PARKSLOTAVAIL_SELECTED       == l_parkSlots->m_Data[i].m_parkSlotAvailableStatus)))
      {
        // Vehicle
        l_virtualVehicle = getVirtualObject(i * 2u);
        l_virtualVehicle->setVisibility(false);
        m_virtualRealityAssets->setValue(i * 2u, false);

        // Slot
        l_position.m_z = 0.01f;
        l_virtualSlot = getVirtualSlot(i * 2u + 1u);
        l_virtualSlot->setVisibility(true);
        l_virtualSlot->setPosition(l_position);
        l_virtualSlot->setSlotAvailableType(l_parkSlots->m_Data[i].m_parkSlotAvailableStatus);
        l_virtualSlot->setSlotOrientationType(l_orientationType);
        l_virtualSlot->setSlotCornerPosition(l_slotCornerPosition);
        l_virtualSlot->dirty();
        m_virtualRealityAssets->setValue(i * 2u + 1u, true);
      }
      else
      {
        // Vehicle
        l_virtualVehicle = getVirtualObject(i * 2u);
        l_virtualVehicle->setVisibility(false);
        m_virtualRealityAssets->setValue(i * 2u, false);

        // Slot
        l_virtualSlot = getVirtualSlot(i * 2u + 1u);
        l_virtualSlot->setVisibility(false);
        m_virtualRealityAssets->setValue(i * 2u + 1u, false);
      }
    }

    if (isShow(m_framework->asCustomFramework()))
    {
      l_virtualVehicle = getVirtualObject(cc::target::common::g_parkSlotCommandQuantity * 2u);
      l_virtualVehicle->setVisibility(true);
      l_virtualVehicle->setPosition({0.0f, 0.0f, 0.0f, 0.0f});
      l_virtualVehicle->dirty();
      m_virtualRealityAssets->setValue(cc::target::common::g_parkSlotCommandQuantity * 2u, true);
    }
    else
    {
      l_virtualVehicle = getVirtualObject(cc::target::common::g_parkSlotCommandQuantity * 2u);
      l_virtualVehicle->setVisibility(false);
      m_virtualRealityAssets->setValue(cc::target::common::g_parkSlotCommandQuantity * 2u, false);
    }
  }

  if (l_customFramework->m_pedestrianObj_ReceiverPort.hasData())
  {
      for (vfc::uint8_t i = 0u; i < g_pedestrianDisplayQuantity; i++)
      {
        const cc::daddy::PedestrianDaddy_t* const l_pedestrian = l_customFramework->m_pedestrianObj_ReceiverPort.getData();

        Position_st l_pos = {0.0f, 0.0f, 0.0f, 0.0f};
        bool l_is_critical_obj = false;
        LowpolyPedestrian* l_virtualPedestrian = getVirtualPedestrian(cc::target::common::g_parkSlotCommandQuantity*2 +1);
        l_pos.m_x   = static_cast<vfc::float32_t>(l_pedestrian->m_Data[i].m_pedesPosition.m_x.value());
        l_pos.m_y   = static_cast<vfc::float32_t>(l_pedestrian->m_Data[i].m_pedesPosition.m_y.value());
        l_pos.m_z   = g_lowpolyPestrianSetting->m_heightOverGround;
        l_pos.m_phi = static_cast<vfc::float32_t>(l_pedestrian->m_Data[i].m_pedesPosition.m_phi.value());
        l_is_critical_obj = l_pedestrian->m_Data[i].m_criticalObj;

        if (isShow(m_framework->asCustomFramework()) && (true == l_pedestrian->m_Data[i].m_isShow))
        {
          // Pedestrian
          l_virtualPedestrian = getVirtualPedestrian(cc::target::common::g_parkSlotCommandQuantity*2 + 1 + i);
          l_virtualPedestrian->setVisibility(true);

          l_virtualPedestrian->setPosition(l_pos);
          l_virtualPedestrian->setIsCritical(l_is_critical_obj);

          l_virtualPedestrian->dirty();
          m_virtualRealityAssets->setValue(cc::target::common::g_parkSlotCommandQuantity*2 + 1 + i, true); // PRQA S 3000
        }
        else
        {
          l_virtualPedestrian = getVirtualPedestrian(cc::target::common::g_parkSlotCommandQuantity*2 + 1 + i);
          l_virtualPedestrian->setVisibility(false);
          m_virtualRealityAssets->setValue(cc::target::common::g_parkSlotCommandQuantity*2 + 1 + i, false); // PRQA S 3000
        }

      }
  }

  m_dataHandler->getSelectedParkingSpot(m_MVP_matrix);

}

void VirtualRealityManager::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (m_lastUpdate != f_nv.getFrameStamp()->getFrameNumber())
    {
      m_lastUpdate = f_nv.getFrameStamp()->getFrameNumber();
      update();
    }
  }
  else if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (&f_nv); // PRQA S 3076
    m_MVPW_matrix = *(l_cv->getMVPW());
    m_MVP_matrix = (*(l_cv->getModelViewMatrix())) * (*(l_cv->getProjectionMatrix()));
  }
  else
  {
    // do nothing
  }

  osg::Group::traverse(f_nv);
}

} // namespace virtualreality
} // namespace assets
} // namespace cc
