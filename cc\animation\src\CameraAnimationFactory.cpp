//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS BYD
/// @file  CameraAnimationFactory.cpp
/// @brief This is a copy version from platform's CameraAnimationFactory to
/// customer path to add a coefficient for the camera Orbit flight animation
//=============================================================================

#include "cc/animation/inc/CameraAnimationFactory.h"

#include "pc/svs/animation/src/CameraFlightPathGenerator.h"
#include "cc/animation/inc/CameraOrbitFlightPathGenerator.h"

#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
#include "pc/svs/animation/inc/Action.h"
#include "pc/svs/animation/inc/AnimationQueue.h"
#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/animation/inc/ParallelAnimation.h" // PRQA S 1060
#include "pc/svs/animation/inc/SerialAnimation.h" // PRQA S 1060
#include "pc/svs/core/inc/View.h"
#include "pc/svs/core/inc/CameraUpdater.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
//#include "pc/generic/util/logging/inc/Logging.h"
#include "vfc/core/vfc_types.hpp"
#include "osg/BoundingBox"

//using pc::util::logging::g_AppContext;
namespace cc
{
namespace animation
{

/**
 * A camera flight animation which generates a flight path dynamically to allow for varying starting positions.
 */
class CameraFlightAnimation : public pc::animation::Animation // PRQA S 2119
{
public:

    CameraFlightAnimation(
        pc::core::View* f_view,
        pc::factory::RenderManager* f_renderManager,
        pc::virtcam::CameraFlightPathGenerator* f_generator,
        cc::virtcam::VirtualCamEnum f_targetPosition,
        bool f_isflightTypeCentered)
        : m_animationPath{}
        , m_view{f_view}
        , m_renderManager{f_renderManager}
        , m_flightPathGenerator{f_generator}
        , m_targetPosition{f_targetPosition}
        , m_reenableCameraUpdater{nullptr}
        , m_isDynamicAnimation{true}
        , m_flightType{ f_isflightTypeCentered ? pc::virtcam::FLIGHT_VEHICLE_CENTERED : pc::virtcam::g_cameraFlight->getFlightType()}
        , m_flightTypeSetting{ f_isflightTypeCentered ? pc::virtcam::FLIGHT_VEHICLE_CENTERED : pc::virtcam::g_cameraFlight->getFlightType()}
    {
    }


    // CameraFlightAnimation(
    //     pc::core::View* f_view,
    //     pc::factory::RenderManager* f_renderManager,
    //     pc::virtcam::CameraFlightPathGenerator* f_generator,
    //     cc::virtcam::VirtualCamEnum f_sourcePosition,
    //     cc::virtcam::VirtualCamEnum f_targetPosition)
    //     : m_view(f_view)
    //     , m_renderManager(f_renderManager)
    //     , m_flightPathGenerator(f_generator)
    //     , m_targetPosition(f_targetPosition)
    //     , m_reenableCameraUpdater(nullptr)
    //     , m_isDynamicAnimation(false)
    //     , m_flightType(pc::virtcam::g_cameraFlight->getFlightType())
    // {
    //     setupStaticAnimationPath(f_sourcePosition, f_targetPosition);
    // }


    void reset() override
    {
        Animation::reset();
        m_reenableCameraUpdater = nullptr;
        if (m_isDynamicAnimation)
        {
            m_animationPath = nullptr;
        }
    }


    vfc::float32_t getFixedDuration() const override
    {
        if (!m_animationPath)
        {
            return 0.0f;
        }
        return static_cast<vfc::float32_t> (m_animationPath->getDuration());
    }


    bool hasFixedDuration() const override
    {
        return !m_isDynamicAnimation;
    }


protected:

    ~CameraFlightAnimation() override = default;

    virtual osg::Matrix computeProjectionMatrix(vfc::float64_t f_fov, vfc::float64_t f_aspectRation, vfc::float64_t f_zNear, vfc::float64_t f_zFar) const
    {
        return osg::Matrix::perspective(f_fov, f_aspectRation, f_zNear, f_zFar);
    }


private:

    CameraFlightAnimation(const CameraFlightAnimation&); // delete
    CameraFlightAnimation& operator = (const CameraFlightAnimation&); // delete // PRQA S 2051

    void onBegin() override
    {
        if (!m_animationPath)
        {
            setupDynamicAnimationPath();
        }
        // Disable camera updates
        pc::core::CameraUpdater* const l_cameraUpdater = m_view->getCameraUpdater();
        if ((l_cameraUpdater != nullptr) && l_cameraUpdater->isEnabled())
        {
          m_reenableCameraUpdater = l_cameraUpdater;
          l_cameraUpdater->disable();
        }
        else
        {
          m_reenableCameraUpdater = nullptr;
        }

        //reset cull setting to all per default
        m_view->setCullMask(0xffffffff); // PRQA S 3604

        // Inform stitching line manager of target camera ID, so that stitchingline settings will be interpolated
        if (m_renderManager != nullptr)
        {
            pc::factory::StitchingLinesManager* const l_stitchingLinesManager = m_renderManager->getStitchMng();
            l_stitchingLinesManager->setVirtualCam(&cc::virtcam::g_positions->getPosition(m_targetPosition), false);
        }
    }

    void onEnd(bool) override
    {
      // Re-enable camera updater if we forced it off
      pc::core::CameraUpdater* const l_cameraUpdater = m_view->getCameraUpdater();
      if ((l_cameraUpdater != nullptr) && l_cameraUpdater == m_reenableCameraUpdater)
      {
        l_cameraUpdater->enable();
      }

      //set the target cull setting
      const pc::virtcam::VirtualCamera& target = cc::virtcam::g_positions->getPosition(m_targetPosition);  // PRQA S 2504
      m_view->setCullMask(~target.m_cullingMask);
    }

    bool onUpdate(vfc::float32_t f_animationTime) override
    {
//         using namespace osgAnimation;

        osgAnimation::ChannelList channels = m_animationPath->getChannels();

        vfc::float64_t l_unusedFoV = 0.0;
        vfc::float64_t l_unusedAspectRatio = 0.0;
        vfc::float64_t l_zNear = 0.0;
        vfc::float64_t l_zFar  = 0.0;

        if (!m_view->getProjectionMatrixAsPerspective(l_unusedFoV, l_unusedAspectRatio, l_zNear, l_zFar))
        {
          // set some plausible near/far plane values if current matrix wasn't a valid perspective matrix
          l_zNear = 0.05;
          l_zFar = 20.0;
        }

        vfc::float32_t l_fovy = 0.0f;
        // distinguish between the different flight types, cause regarding the animation there are different channels defined
        if((m_flightType == pc::virtcam::FLIGHT_VEHICLE_CENTERED) && (m_flightTypeSetting == pc::virtcam::FLIGHT_VEHICLE_CENTERED))
        {
          osgAnimation::Vec3LinearSampler*          const l_eyeSampler            = dynamic_cast<osgAnimation::Vec3LinearSampler*>(channels[0u]->getSampler()); // PRQA S 3077 
          osgAnimation::Vec3LinearSampler*          const l_centerSampler         = dynamic_cast<osgAnimation::Vec3LinearSampler*>(channels[1u]->getSampler()); // PRQA S 3077 
          osgAnimation::Vec3LinearSampler*          const l_upSampler             = dynamic_cast<osgAnimation::Vec3LinearSampler*>(channels[2u]->getSampler()); // PRQA S 3077 
          osgAnimation::FloatLinearSampler*         const l_fovySampler           = dynamic_cast<osgAnimation::FloatLinearSampler*>(channels[3u]->getSampler()); // PRQA S 3077 

          osg::Vec3f l_eye, l_center, l_up; // PRQA S 4107

          if((l_eyeSampler != nullptr) && (l_centerSampler != nullptr) && (l_upSampler != nullptr) && (l_fovySampler != nullptr))
          {
            l_eyeSampler->getValueAt(f_animationTime, l_eye);
            l_centerSampler->getValueAt(f_animationTime, l_center);
            l_upSampler->getValueAt(f_animationTime, l_up);
            l_fovySampler->getValueAt(f_animationTime, l_fovy);
          }
          //XLOG_INFO_OS(g_AppContext) << "factory: "<<l_up.x() <<" y: "<<l_up.y() <<" z: "<<l_up.z()<< XLOG_ENDL;
          m_view->setViewMatrixAsLookAt(l_eye, l_center, l_up);
        }
        else
        {
          osgAnimation::Vec3LinearSampler*          const l_eyeSampler            = dynamic_cast<osgAnimation::Vec3LinearSampler*>(channels[0u]->getSampler()); // PRQA S 3077 
          osgAnimation::QuatSphericalLinearSampler* const l_rotSampler            = dynamic_cast<osgAnimation::QuatSphericalLinearSampler*>(channels[1u]->getSampler()); // PRQA S 3077 
          osgAnimation::FloatLinearSampler*         const l_fovySampler           = dynamic_cast<osgAnimation::FloatLinearSampler*>(channels[2u]->getSampler()); // PRQA S 3077 

          osg::Vec3f l_eye;
          osg::Quat l_rot;

          if((l_eyeSampler != nullptr) && (l_rotSampler != nullptr) && (l_fovySampler != nullptr))
          {
            l_eyeSampler->getValueAt(f_animationTime, l_eye);
            l_rotSampler->getValueAt(f_animationTime, l_rot);
            l_fovySampler->getValueAt(f_animationTime, l_fovy);
          }

          osg::Matrix l_viewMatrix;
          l_viewMatrix.setRotate(l_rot);
          l_viewMatrix.setTrans(l_eye);
          m_view->setViewMatrix(osg::Matrixf::inverse(l_viewMatrix));
        }

        const vfc::float64_t l_currentAspect = m_view->getViewport()->aspectRatio();
        m_view->setProjectionMatrix(computeProjectionMatrix(l_fovy, l_currentAspect, l_zNear, l_zFar));

        return f_animationTime >= m_animationPath->getDuration();
    }


    // Allows flight path to be replaced mid-animation (e.g. if the animation is queued as non-blocking animation)
    bool supportsCancellation() const override
    {
        return true;
    }


    pc::virtcam::VirtualCamera getCurrentVirtualCameraState(pc::core::View* f_view) const
    {
        pc::virtcam::VirtualCamera virtualCamera;
        // Get current camera and stitching state
        vfc::float32_t  unusedAspectRatio = 0.0f;
        vfc::float32_t unusedZNear        = 0.0f;
        vfc::float32_t unusedZFar         = 0.0f;
        f_view->getViewMatrixAsLookAt(virtualCamera.m_eye, virtualCamera.m_center, virtualCamera.m_up);
        f_view->getProjectionMatrix().getPerspective(virtualCamera.m_fovy, unusedAspectRatio, unusedZNear, unusedZFar);    // PRQA S 3803
        return virtualCamera;
    }

    bool isLeftSide(const pc::virtcam::VirtualCamera& f_virtCam) const
    {
      bool l_ret = false;

      if (f_virtCam.m_eye.y() > 0.5f)  // Unit: meter
      {
        l_ret = true;
      }

      return l_ret;

    }

    bool isRightSide(const pc::virtcam::VirtualCamera& f_virtCam) const
    {
      bool l_ret = false;

      if (f_virtCam.m_eye.y() < -0.5f) // Unit: meter
      {
        l_ret = true;
      }

      return l_ret;

    }

    bool isRearSide(const pc::virtcam::VirtualCamera& f_virtCam) const
    {
      bool l_ret = false;

      if (f_virtCam.m_eye.x() < pc::vehicle::g_mechanicalData->getCenter().x())
      {
        l_ret = true;
      }

      return l_ret;

    }

    bool isSkipCenterAnimation(const pc::virtcam::VirtualCamera& f_startCam, const pc::virtcam::VirtualCamera& f_endCam) const
    {
      bool l_ret = true;

      if (  ( ((isLeftSide(f_startCam) && isRightSide(f_endCam)) || (isRightSide(f_startCam) && isLeftSide(f_endCam))) &&
              ((!isRearSide(f_startCam) && isRearSide(f_endCam)) || (isRearSide(f_startCam) && !isRearSide(f_endCam))  )    ) ||
            ( ((!isLeftSide(f_startCam) && !isRightSide(f_startCam)) || (!isLeftSide(f_endCam) && !isRightSide(f_endCam))) &&
              ((!isRearSide(f_startCam) && isRearSide(f_endCam)) || (isRearSide(f_startCam) && !isRearSide(f_endCam))  )    )      )
      {
        l_ret = false;
      }

      return l_ret;

    }


    void setupDynamicAnimationPath()
    {
        const pc::virtcam::VirtualCamera  source = getCurrentVirtualCameraState(m_view);
        const pc::virtcam::VirtualCamera& target = cc::virtcam::g_positions->getPosition(static_cast<vfc::uint32_t>(m_targetPosition));  // PRQA S 2504

        m_flightType = isSkipCenterAnimation(source,target) ? pc::virtcam::FLIGHT_ALSO_CIRCULAR : pc::virtcam:: FLIGHT_VEHICLE_CENTERED;
        m_animationPath = m_flightPathGenerator->generatePath(source,target,m_flightType);
    }


    osg::ref_ptr<osgAnimation::Animation>                m_animationPath;
    osg::ref_ptr<pc::core::View>                         m_view;
    osg::ref_ptr<pc::factory::RenderManager>             m_renderManager;
    osg::ref_ptr<pc::virtcam::CameraFlightPathGenerator> m_flightPathGenerator;
    cc::virtcam::VirtualCamEnum                          m_targetPosition;

    pc::core::CameraUpdater* m_reenableCameraUpdater;

    bool m_isDynamicAnimation;

    pc::virtcam::FlightType m_flightType;
    pc::virtcam::FlightType m_flightTypeSetting;
};





/**
 * CameraAnimationFactory
 */
CameraAnimationFactory::CameraAnimationFactory(cc::core::CustomFramework* f_customFramework) // PRQA S 4054
{
  const osg::BoundingBox& vehicleBoundingBox = pc::vehicle::g_mechanicalData->getBoundingBox();
  m_flightPathGenerator = new pc::virtcam::CameraFlightPathGenerator(vehicleBoundingBox);
  m_orbitFlightPathGenerator = new cc::virtcam::CameraOrbitFlightPathGenerator(vehicleBoundingBox, f_customFramework);
}





pc::animation::Animation* CameraAnimationFactory::createCameraOrbitAnimation(pc::core::View* f_view, pc::factory::RenderManager* f_renderManager, cc::virtcam::VirtualCamEnum f_target) const
{
    assert(f_target < cc::virtcam::NUMBER_OF_VIRT_CAMS);
    return  new CameraFlightAnimation(f_view, f_renderManager, m_orbitFlightPathGenerator, f_target, cc::virtcam::g_coefficientSpeedSettings->m_flightTypeCentered);
}

} // namespace animation
} // namespace cc
