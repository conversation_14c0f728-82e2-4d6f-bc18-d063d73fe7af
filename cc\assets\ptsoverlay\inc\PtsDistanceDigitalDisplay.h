//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DistanceDigitalDisplay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_PTS_DISTANCE_DIGITAL_DISPLAY_H
#define CC_ASSETS_PTS_DISTANCE_DIGITAL_DISPLAY_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "pc/svs/views/engineeringview/inc/EngineeringView.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/assets/ptsoverlay/inc/pashmi_output_types.hpp"
#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>

namespace pashmi
{
enum ERadarWallPartition : vfc::uint8_t
{
    RADAR_WALL_FRONT         = 0,
    RADAR_WALL_REAR          = 1,
    RADAR_WALL_LEFT          = 2,
    RADAR_WALL_RIGHT         = 3,
    RADAR_WALL_PARTITION_SUM = 4,
};

enum ERadarWallDistanceDigitalState : vfc::uint8_t
{
    RADAR_WALL_DIGITAL_CLOSE     = 0,
    RADAR_WALL_DIGITAL_OPEN      = 1,
};

typedef std::array<ERadarWallDistanceDigitalState, RADAR_WALL_PARTITION_SUM> ERadarWallState;
}

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace ptsdistancedigitaldisplay
{


enum DigitalDisplayPositionID : vfc::uint8_t
{
  FRONT,
  FRONT_LEFT,
  LEFT,
  REAR_LEFT,
  REAR,
  REAR_RIGHT,
  RIGHT,
  FRONT_RIGHT
};

enum UssSectorId : vfc::uint8_t
{
  USS_FRONT,
  USS_FRONT_LEFT,
  USS_LEFT,
  USS_REAR_LEFT,
  USS_REAR,
  USS_REAR_RIGHT,
  USS_RIGHT,
  USS_FRONT_RIGHT
};

//Uss Sector ID in DAI
enum UssSectorId_40 : vfc::uint8_t
{
  FRONT_LEFT_40 = 5u,
  FRONT_MIDDLE_LEFT_40 = 0u,
  FRONT_MIDDLE_RIGHT_40 = 39u,
  FRONT_RIGHT_40 = 34u,
  REAR_LEFT_40 = 14u,
  REAR_MIDDLE_LEFT_40 = 19u,
  REAR_MIDDLE_RIGHT_40 = 20u,
  REAR_RIGHT_40 = 25u
};


struct DistanceDigitalDisplayProperties
{
  DistanceDigitalDisplayProperties()
    : m_x(0.0f)
    , m_y(0.0f)
    , m_orientation(0.0f)
  {}

  float m_x;                  // inner point x coordinate
  float m_y;                  // inner point y coordinate
  float m_orientation;        // use to display text orientation
};

const float g_defaultDistance = 255.0f;
const int   g_defaultIndex    = 100;

//======================================================
// PtsDistanceDigitalDisplaySettings
//------------------------------------------------------
/// Setting class for distancedigitaldisplay
/// <AUTHOR>
//======================================================
class PtsDistanceDigitalDisplaySettings : public pc::util::coding::ISerializable
{
public:
  PtsDistanceDigitalDisplaySettings()
    : m_offset_front(osg::Vec3f(1.5f, 0.0f, 0.0f))
    , m_offset_front_parking(osg::Vec3f(1.0f, 0.0f, 0.0f))
    , m_offset_rear(osg::Vec3f(0.0f, 0.0f, 0.0f))
    , m_offset_rear_parking(osg::Vec3f(1.3f, 0.0f, 0.0f))
    , m_fontType("cc/resources/Roboto-Regular.ttf")
    , m_stopTextTexture("cc/resources/stop.png")
    , m_fixedFrontPosition(6.0f)
    , m_fixedRearPosition(-2.8f)
    , m_fixedFrontTopPosition(6.7f)
    , m_fixedRearBottomPosition(-4.f)
    , m_fixedFrontPosition_vert_parking(4.7f)
    , m_fixedRearPosition_vert_parking(-1.4f)
    , m_stopTextFrontPosition(osg::Vec2f{5.706f, -0.64f})
    , m_stopTextRearPosition(osg::Vec2f{-3.1f, -0.64f})
    , m_stopTextFrontPosition_vert_parking(osg::Vec2f{4.2f, -0.64f})
    , m_stopTextRearPosition_vert_parking(osg::Vec2f{-2.5f, -0.64f})
    , m_textOffsetPercentageFront(0.6f)
    , m_textOffsetPercentageRear(0.4f)
    , m_splineColorR2(1.0000f, 0.0078f, 0.0000f, 1.0000f)
    , m_splineColorR1(0.9882f, 0.0235f, 0.0000f, 1.0000f)
    , m_splineColorO2(0.9922f, 0.4196f, 0.0510f, 1.0000f)
    , m_splineColorO1(0.9922f, 0.6196f, 0.2353f, 1.0000f)
    , m_splineColorY2(0.9490f, 0.9882f, 0.3922f, 1.0000f)
    , m_splineColorY1(0.9882f, 0.9922f, 0.6392f, 1.0000f)
    , m_splineColorB(0.00000f, 0.14f, 0.8f, 1.0000f)
    , m_distanceR2(0.2f)
    , m_distanceR1(0.36f)
    , m_distanceO2(0.52f)
    , m_distanceO1(0.68f)
    , m_distanceY2(0.84f)
    , m_distanceY1(0.98f)
    , m_distanceB(1.00f)
    , m_rangeFrontConf_AIHC(1.0f)
    , m_rangeSideConf_AIHC(1.0f)
    , m_rangeRearConf_AIHC(1.0f)
  {
  }

  SERIALIZABLE(PtsDistanceDigitalDisplaySettings)
  {
    ADD_MEMBER(osg::Vec3f, offset_front);
    ADD_MEMBER(osg::Vec3f, offset_front_parking);
    ADD_MEMBER(osg::Vec3f, offset_rear);
    ADD_MEMBER(osg::Vec3f, offset_rear_parking);
    ADD_STRING_MEMBER(fontType);
    ADD_STRING_MEMBER(stopTextTexture);
    ADD_FLOAT_MEMBER(fixedFrontPosition);
    ADD_FLOAT_MEMBER(fixedRearPosition);
    ADD_FLOAT_MEMBER(fixedFrontTopPosition);
    ADD_FLOAT_MEMBER(fixedRearBottomPosition);
    ADD_FLOAT_MEMBER(fixedFrontPosition_vert_parking);
    ADD_FLOAT_MEMBER(fixedRearPosition_vert_parking);
    ADD_MEMBER(osg::Vec2f, stopTextFrontPosition);
    ADD_MEMBER(osg::Vec2f, stopTextRearPosition);
    ADD_MEMBER(osg::Vec2f, stopTextFrontPosition_vert_parking);
    ADD_MEMBER(osg::Vec2f, stopTextRearPosition_vert_parking);
    ADD_FLOAT_MEMBER(textOffsetPercentageFront);
    ADD_FLOAT_MEMBER(textOffsetPercentageRear);
    ADD_MEMBER(osg::Vec4f, splineColorR2);
    ADD_MEMBER(osg::Vec4f, splineColorR1);
    ADD_MEMBER(osg::Vec4f, splineColorO2);
    ADD_MEMBER(osg::Vec4f, splineColorO1);
    ADD_MEMBER(osg::Vec4f, splineColorY2);
    ADD_MEMBER(osg::Vec4f, splineColorY1);
    ADD_MEMBER(osg::Vec4f, splineColorB);
    ADD_FLOAT_MEMBER(distanceR2);
    ADD_FLOAT_MEMBER(distanceR1);
    ADD_FLOAT_MEMBER(distanceO2);
    ADD_FLOAT_MEMBER(distanceO1);
    ADD_FLOAT_MEMBER(distanceY2);
    ADD_FLOAT_MEMBER(distanceY1);
    ADD_FLOAT_MEMBER(distanceB);
    ADD_FLOAT_MEMBER(rangeFrontConf_AIHC);
    ADD_FLOAT_MEMBER(rangeSideConf_AIHC);
    ADD_FLOAT_MEMBER(rangeRearConf_AIHC);
  }
  osg::Vec3f m_offset_front;
  osg::Vec3f m_offset_front_parking;
  osg::Vec3f m_offset_rear;
  osg::Vec3f m_offset_rear_parking;
  std::string  m_fontType;
  std::string  m_stopTextTexture;
  float m_fixedFrontPosition;
  float m_fixedRearPosition;
  float m_fixedFrontTopPosition;
  float m_fixedRearBottomPosition;
  float m_fixedFrontPosition_vert_parking;
  float m_fixedRearPosition_vert_parking;
  osg::Vec2f m_stopTextFrontPosition;
  osg::Vec2f m_stopTextRearPosition;
  osg::Vec2f m_stopTextFrontPosition_vert_parking;
  osg::Vec2f m_stopTextRearPosition_vert_parking;
  float m_textOffsetPercentageFront; // percentage of distance based on unit vector
  float m_textOffsetPercentageRear; // percentage of distance based on unit vector
  osg::Vec4f m_splineColorR2;
  osg::Vec4f m_splineColorR1;
  osg::Vec4f m_splineColorO2;
  osg::Vec4f m_splineColorO1;
  osg::Vec4f m_splineColorY2;
  osg::Vec4f m_splineColorY1;
  osg::Vec4f m_splineColorB;
  float m_distanceR2;
  float m_distanceR1;
  float m_distanceO2;
  float m_distanceO1;
  float m_distanceY2;
  float m_distanceY1;
  float m_distanceB;
  float m_rangeFrontConf_AIHC;
  float m_rangeSideConf_AIHC;
  float m_rangeRearConf_AIHC;
};

extern pc::util::coding::Item<PtsDistanceDigitalDisplaySettings> g_ptsDisplaySettings;

class DistanceDigitalDisplayUpdateCallback: public osg::NodeCallback
{

public:
  DistanceDigitalDisplayUpdateCallback(
    osg::ref_ptr<osg::Geode> f_FrontSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_RearSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_FrontSectorWarnImageGeode,
    osg::ref_ptr<osg::Geode> f_RearSectorWarnImageGeode,
    osg::ref_ptr<osg::Geode> f_LeftSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_RightSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_FrontLeftSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_RearLeftSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_RearRightSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_FrontRightSectorDisGeode,
    pc::core::Framework* f_pFramework,
    pc::vehicle::AbstractZoneLayout* f_zoneLayout
  );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

  void updateFrontSector(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);
  void updateRearSector(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);
  void updateLeftSector(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);
  void updateRightSector(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);

protected:
  virtual ~DistanceDigitalDisplayUpdateCallback() = default;
private:
  bool isUpdateUssData();
  void initDigitalDisplayPosition();
  osg::Vec4f getSplineColor(float f_distance) const;
  //! Copy constructor is not permitted.
  DistanceDigitalDisplayUpdateCallback (const DistanceDigitalDisplayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  DistanceDigitalDisplayUpdateCallback& operator=(const DistanceDigitalDisplayUpdateCallback& other); // = delete
  void updateDigitalDisplay(vfc::uint8_t f_ussId, vfc::uint8_t f_digitalPosId, osg::ref_ptr<osg::Geode> f_Geode, float f_displayDistanceThresh);
  bool isShown (vfc::uint8_t f_digitalDisplayPosId);

  osg::ref_ptr<osg::Geode> m_FrontSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_RearSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_FrontSectorWarnImageGeode;
  osg::ref_ptr<osg::Geode> m_RearSectorWarnImageGeode;
  osg::ref_ptr<osg::Geode> m_LeftSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_RightSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_FrontLeftSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_RearLeftSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_RearRightSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_FrontRightSectorDisGeode;
  std::array<float, 8> m_UssSectorShortestDistance;
  std::array<osg::Vec2f,8>     m_textPositions;
  pc::core::Framework*         m_pFramework;
  pc::vehicle::AbstractZoneLayout* m_zoneLayout;
  pashmi::ERadarWallState     m_RadarWallState;
};

//!
//! DigitalDistanceOverlay
//!
class DistanceDigitalDisplay : public osg::MatrixTransform
{
public:

    DistanceDigitalDisplay(pc::core::Framework* f_framework, pc::vehicle::AbstractZoneLayout* f_zoneLayout);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    virtual void init();
    bool updateNeeded();

    virtual ~DistanceDigitalDisplay();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_FrontSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_FrontSectorWarnImageGeode;
    osg::ref_ptr<osg::Geode> m_RearSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_RearSectorWarnImageGeode;
    osg::ref_ptr<osg::Geode> m_LeftSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_RightSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_FrontLeftSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_RearLeftSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_RearRightSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_FrontRightSectorDisGeode;
private:
    //! Copy constructor is not permitted.
    DistanceDigitalDisplay (const DistanceDigitalDisplay& other); // = delete
    //! Copy assignment operator is not permitted.
    DistanceDigitalDisplay& operator=(const DistanceDigitalDisplay& other); // = delete

    EScreenID            m_screenID;
    osg::Vec2f           m_stopTextFrontPositionShow;
    osg::Vec2f           m_stopTextRearPositionShow;
    pc::vehicle::AbstractZoneLayout* m_zoneLayout;
};

inline osg::Texture2D* loadTexture(const std::string& f_filename);

bool checkCornerSector(const unsigned int& f_index);


} // namespace DistanceDigitalDisplay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTS_DISTANCE_DIGITAL_DISPLAY_H
