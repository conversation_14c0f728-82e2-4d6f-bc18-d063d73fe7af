//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_WHEELTRACK
#define CC_ASSETS_TRAJECTORY_SUBASSETS_WHEELTRACK

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/assets/trajectory/inc/DL1.h"


namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic


class WheelTrack : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:

  enum WheelTrackType
  {
    CLOSER_PART,  // defines the green track if the maneuver is active from beginning till the actionpoint
    FURTHER_PART, // defines the grey track if the maneuver is active behind the actionpoint
    WHOLE,        // defines the yellow track if the maneuver is inactive
    EXTRA_MANUAL, // defines the yellow rear(front) wheels tracks if the cars driving direction is forward(backward) and the maneuver is inactive
    EXTRA_AUTO    // defines the green rear(front) wheels tracks if the cars driving direction is forward(backward) and the maneuver is active
  };

  WheelTrack(
    pc::core::Framework* f_framework,
    cc::assets::trajectory::commontypes::Side_en f_side,
    float f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    const cc::assets::trajectory::DL1* const f_distanceLine,
    unsigned int f_numOfVerts,
    WheelTrackType f_wheelTrackType);

  osg::Image* create1DTexture() const;

  virtual void generateVertexData();

  void animate();

  void setLastActionpointDist(float f_lastActionPointDist);

  void setType(WheelTrackType f_wheelTrackType);

protected:

  virtual ~WheelTrack();

  void generateVertexData_usingTexture();

  const unsigned int mc_numOfVerts;
  float m_geomHeight;
  float m_actionPointFarToMidDist;
  float m_currActionPointDist;
  float m_lastActionPointDist;
  WheelTrackType m_wheelTrackType;

private:

  //! Copy constructor is not permitted.
  WheelTrack (const WheelTrack& other); // = delete
  //! Copy assignment operator is not permitted.
  WheelTrack& operator=(const WheelTrack& other); // = delete

  const osg::ref_ptr<const assets::trajectory::DL1> m_distanceLine;
};


} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_WHEELTRACK
