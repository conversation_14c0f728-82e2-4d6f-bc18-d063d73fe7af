//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: YRN1WX Yang Rui (BCSC-EPA1)
//  Department: BCSC-EPA1
//=============================================================================
/// @swcomponent SVS BYD
/// @file  ParkingSlot.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_PARKINGSLOT_H
#define CC_ASSETS_UIELEMENTS_PARKINGSLOT_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"
#include <osg/Matrixf>
#include <osg/Timer>
#include <ctime>

namespace cc
{

namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

struct SelectedSlot
{
  vfc::uint8_t      m_selectedSlotSide;
  vfc::uint8_t      m_selectedSlotId;
  cc::target::common::EFAPAParkSlotType m_selectedSlotType;
  vfc::float32_t    m_slotAngle;
};

struct ParkSpaceInfo
{
  vfc::uint8_t m_runnable_count;
  vfc::uint8_t m_PS_Id;
  vfc::uint8_t m_slot_flash_flag;
};

static cc::target::common::EThemeTypeHU s_theme;

class ParkingSlotSettings : public pc::util::coding::ISerializable
{
public:

  ParkingSlotSettings()
    : m_parkingDiagSlotAngleLowerLimit(0.4f)
    , m_parkingDiagSlotAngleUpperLimit(1.7f)
    , m_parkingSlotflashDeplay(20U)
    , m_parkingCrossSlotConversionCycles(60u)
  {
  }

  SERIALIZABLE(ParkingSlotSettings)
  {
    ADD_FLOAT_MEMBER(parkingDiagSlotAngleLowerLimit);
    ADD_FLOAT_MEMBER(parkingDiagSlotAngleUpperLimit);
    ADD_UINT32_MEMBER(parkingSlotflashDeplay);
    ADD_UINT32_MEMBER(parkingCrossSlotConversionCycles);
  }

  float m_parkingDiagSlotAngleLowerLimit;
  float m_parkingDiagSlotAngleUpperLimit;
  unsigned int m_parkingSlotflashDeplay;
  unsigned int m_parkingCrossSlotConversionCycles;
};

extern pc::util::coding::Item<ParkingSlotSettings> g_managerSettings;

//!
//! ParkingSlotManager
//!
class ParkingSlotManager
{
public:
  ParkingSlotManager();
  virtual ~ParkingSlotManager();

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);
  void update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework);
  void clearIcon();

  void setSlot(
    vfc::uint16_t f_slot,
    std::string f_textureSlot,
    const osg::Vec2f& f_size,
    osg::Vec2f f_position,
    cc::target::common::EFAPAParkSlotType f_slotType,
    cc::daddy::ParkUISpotData_t& f_rParkSpotUIDataContainer);

  void displaySelectedSlot(vfc::uint8_t f_vehicle, vfc::uint8_t f_guideline, vfc::uint8_t f_slot, const osg::Vec2f& f_position);
  void displaySelectedSlot(vfc::uint8_t f_vehicle, vfc::uint8_t f_slot, const osg::Vec2f& f_position);

  void manageDisplayLogicInGuidance(SelectedSlot f_selectedParkSpace, cc::target::common::rbp_Type_ParkManeuverType_en f_curparkPSDirection);
  void manageDisplayLogicInPause(SelectedSlot f_selectedParkSpace, cc::target::common::rbp_Type_ParkManeuverType_en f_curparkPSDirection);
  void manageDisplayLogicInComplete(SelectedSlot f_selectedParkSpace);

  SelectedSlot getSelectedSlot(
    std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace);
  bool isASlotSelected(
    std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace);

  void CleanButtonDispSts();
  bool DeliverButtonDispSts(const core::CustomFramework* f_framework);
  vfc::uint8_t getSlotType( cc::target::common::EFAPAParkSlotType f_generalType, vfc::float32_t f_angle);

private:
  //! Copy constructor is not permitted.
  ParkingSlotManager (const ParkingSlotManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSlotManager& operator=(const ParkingSlotManager& other); // = delete

  osg::Vec2f transferToBottomLeft(const osg::Vec2f f_iconPos);

  unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_settingParkSlot;
  bool m_mat_b;
  ParkSpaceInfo m_parkSpace_info[cc::target::common::l_L_ParkSpace_side][cc::target::common::l_L_ParkSpace_NumberPerside];
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkInSlotsSelectingDispSts;
  cc::daddy::EParkDisp2Touch          m_ViewButtonParkingInTypeDispSts;
};

//!
//! ParkingSlot
//!
class HoriParkingSlot: public cc::assets::uielements::CustomImageOverlays
{
public:

  HoriParkingSlot(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);

  virtual ~HoriParkingSlot();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  //! Copy constructor is not permitted.
  HoriParkingSlot (const HoriParkingSlot& other); // = delete
  //! Copy assignment operator is not permitted.
  HoriParkingSlot& operator=(const HoriParkingSlot& other); // = delete

  cc::core::CustomFramework* m_customFramework;

  ParkingSlotManager m_HoriManager;

  // ParkingSlotManager m_VertManager;

  osg::Vec2f *m_pAutoPicCenter;
  osg::Vec2f *m_pParkOutAutoPicCenter;
  osg::Vec2f *m_pSmallSlotAutoPicCenter;
  osg::Vec2f *m_pSelectedSlotParaRightCenter;
  osg::Vec2f *m_pAutoPicPara;
  osg::Vec2f *m_pAutoPicVertRearInLeft;
  osg::Vec2f *m_pAutoPicVertRearInRight;
  osg::Vec2f *m_pAutoPicVertFrontInLeft;
  osg::Vec2f *m_pAutoPicVertFrontInRight;
  osg::Vec2f *m_pSelectedSlotParaLeft;
  osg::Vec2f *m_pSelectedSlotParaRight;
  osg::Vec2f *m_pSelectedSlotVertLeftRearIn;
  osg::Vec2f *m_pSelectedSlotVertLeftFrontIn;
  osg::Vec2f *m_pSelectedSlotVertRightRearIn;
  osg::Vec2f *m_pSelectedSlotVertRightFrontIn;

  osg::Vec2f *m_pParaSlotSize;
  osg::Vec2f *m_pVertSlotSize;
  osg::Vec2f *m_pDiagSlotSize;

  vfc::uint32_t *m_pParaSlotPoseX;
  vfc::uint32_t *m_pVertSlotPoseX;
  vfc::uint32_t *m_pCalculateLeftSlotPoseX;
  vfc::uint32_t *m_pSlotPositionY;
  vfc::uint32_t *m_pDistanceBetweenPP;
  vfc::uint32_t *m_pDistanceBetweenPC;
  vfc::uint32_t *m_pDistanceBetweenCC;
  vfc::uint32_t *m_pDistanceBetweenDD;
  vfc::uint32_t *m_pMaxHeight;

};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_PARKINGSLOT_H
