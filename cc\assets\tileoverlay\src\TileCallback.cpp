//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin(CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileCallback.cpp
/// @brief 
//=============================================================================

#include "cc/assets/tileoverlay/inc/TileCallback.h"


namespace cc
{
namespace assets
{
namespace tileoverlay
{

//!
//! TileCallback
//!
TileCallback::TileCallback(pc::core::Framework* f_framework, bool f_isSettingBased)
  : m_framework{f_framework}
  , m_tileStatus{true}
  , m_isSettingBased{f_isSettingBased}
{
}


TileCallback::~TileCallback() = default;

void TileCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if ((f_node == nullptr) || (f_nv == nullptr))
  {
      return;
  }
  if ((osg::NodeVisitor::CULL_VISITOR == f_nv->getVisitorType()))
  {
    checkTileStatus();

    // check the tile status
    if(m_tileStatus)
    {
      traverse(f_node, f_nv);
    }
  }
}

void TileCallback::checkTileStatus()
{
  // if (m_isSettingBased)
  // {
  //   if (m_framework->asCustomFramework()->m_overlayDistStsDaddy_ReceiverPort.isConnected())
  //   {
  //     const cc::daddy::SVSOverlayDistStsDaddy_t* l_pData = m_framework->asCustomFramework()->m_overlayDistStsDaddy_ReceiverPort.getData();

  //     if (0 != l_pData)
  //     {
  //       //status can be 0 or 1
  //       if (1u == l_pData->m_Data)
  //       {
  //         m_tileStatus = true;
  //       }
  //       else
  //       {
  //         m_tileStatus = false;
  //       }
  //     }
  //   }
  // }
  // else
  // {
  //   m_tileStatus = true;
  // }

  m_tileStatus = true;
}


} // namespace tileoverlay
} // namespace assets
} // namespace cc
