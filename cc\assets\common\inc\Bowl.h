//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Bowl.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_COMMON_BOWL_H
#define CC_ASSETS_COMMON_BOWL_H

#include "pc/svs/core/inc/Asset.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"

#include "pc/svs/worker/bowlshaping/inc/Bowl.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"
#include "pc/svs/worker/bowlshaping/inc/BowlUpdateVisitor.h"
#include "pc/svs/worker/bowlshaping/inc/PolarBowlLayoutGenerator.h"

//! forward declarations
namespace pc
{
namespace worker
{
namespace bowlshaping
{
class BowlLayoutGenerator;
} // namespace bowlshaping
} // namespace worker
namespace core
{
class Framework;
} // namespace core
namespace factory
{
class SV3DNode;
} // namespace factory
} // namespace pc


namespace cc
{
namespace assets
{
namespace common
{

extern pc::util::coding::Item<pc::worker::bowlshaping::BowlShaperData> g_bowlShaperSmall;
extern pc::util::coding::Item<pc::worker::bowlshaping::BowlShaperData> g_bowlShaperMedium;

pc::worker::bowlshaping::BowlWallGenerator* createBowlWallGenerator(const pc::worker::bowlshaping::BowlShaperData& f_data);

//======================================================
// Bowl
//------------------------------------------------------
/// Creates a bowl as a environment around the vehicle
/// Creates a half circle mesh and displays on it the camera's inputs
/// Adapts the Mesh vertices depending on calibration
/// <AUTHOR> Florian
//======================================================
class Bowl : public pc::core::Asset
{
public:

  Bowl(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, pc::worker::bowlshaping::BowlLayoutGenerator* f_pLayoutGenerator);

  pc::factory::SV3DNode* getSV3DNode() const
  {
    return m_asset.get();
  }

  pc::core::Framework* getFramework() const
  {
    return m_pFramework;
  }

protected:

  osg::ref_ptr<pc::factory::SV3DNode> m_asset;
  pc::core::Framework* m_pFramework;


};

} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_BOWL_H