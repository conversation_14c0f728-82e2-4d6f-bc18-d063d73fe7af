//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Floor.cpp
/// @brief
//=============================================================================

#include "cc/assets/common/inc/Floor.h"

#include "cc/assets/common/inc/FloorSingleCam.h"
#include "cc/assets/customfloorplategenerator/inc/CustomFloorPlateGenerator.h"
#include "cc/assets/common/inc/CustomFloorPlateRenderer.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/common/inc/FloorPlateSm.h"
#include "vfc/core/vfc_types.hpp"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/assets/floorplate/inc/FloorPlateRenderer.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "pc/svs/factory/inc/Floor.h"
#include "pc/svs/factory/inc/FloorUpdateVisitor.h"
#include "pc/svs/factory/inc/SV3DCullCallback.h"
#include "pc/svs/factory/inc/SV3DStateGraph.h"
#include "pc/svs/factory/inc/SV3DUpdateCallback.h"
#include "pc/generic/util/logging/inc/Logging.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "pc/svs/worker/bowlshaping/inc/PolarBowlLayout.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/OrientedBoundingBox2.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "daddy_ifbase.hpp"
#include "daddy_receiverport.hpp"
#include "daddy_senderport.hpp"


#include "osg/Math" // PRQA S 1060
#include "osg/ValueObject"
#include "osgUtil/CullVisitor"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/Vec2f"
#include "osgDB/ReadFile"
#include "osg/Depth"

#include "pc/svs/util/osgx/inc/Utils.h"

using pc::util::logging::g_EngineContext;

/// @deviation NRCS2_081
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace common
{
namespace
{

//!
//! FloorData
//!
class FloorData : public pc::util::coding::ISerializable
{
public:

  FloorData()
    : m_size{18.0f, 14.0f}
    , m_resolution{0.2f, 0.2f}
    , m_stripedBasePlateTransparency{0.25f}
    , m_solidBasePlateColor{0.0f, 1.0f, 0.0f}
    , m_basepaltePath("cc/vehicle_model/basePlate.osg")
  {
  }

  SERIALIZABLE(FloorData) // PRQA S 3401
  {
    if (f_descriptor == nullptr)
    {
      return;
    }
    ADD_MEMBER(osg::Vec2f, size);
    ADD_MEMBER(osg::Vec2f, resolution);
    ADD_FLOAT_MEMBER(stripedBasePlateTransparency);
    ADD_MEMBER(osg::Vec3f, solidBasePlateColor);
    ADD_STRING_MEMBER(basepaltePath);
  }

  osg::Vec2f m_size;
  osg::Vec2f m_resolution;
  vfc::float32_t m_stripedBasePlateTransparency; // TODO: move into platform
  osg::Vec3f m_solidBasePlateColor;
  std::string m_basepaltePath;
};

pc::util::coding::Item<FloorData> g_floorData("Floor");

class AudiFloorPlateStateHandler : public pc::assets::FloorPlateStateHandler
{
public:
  AudiFloorPlateStateHandler(pc::core::Framework* f_framework) // PRQA S 2203
  : pc::assets::FloorPlateStateHandler{}
  , m_historyAccumulationStartOBB{OrientedBoundingBox2(osg::Vec2(0.f, 0.f), 0.f, 0.f, 0.f)}
  , m_historyTextureAccumulated{false}
  , m_framework{f_framework}
  , m_blurredFloorPlateAvailable{true}
  , m_historyTextureAvailable{true}

  {
  }

  void setBlurredFloorPlateAvailable(bool f_blurredFloorPlateAvailable) override
  {
    m_blurredFloorPlateAvailable = f_blurredFloorPlateAvailable;
  }

  void setHistoryTextureAvailable(bool f_historyTextureAvailable) override
  {
    m_historyTextureAvailable = f_historyTextureAvailable;
  }

  using OrientedBoundingBox2 = pc::util::OrientedBoundingBox2<vfc::float32_t>;

  static OrientedBoundingBox2 getVehicleOBB(pc::daddy::OdometryData f_odometry)
  {
    const vfc::float32_t l_yawAngle = f_odometry.m_yawAngle.value();
    return OrientedBoundingBox2(
      osg::Vec2f(f_odometry.m_xPos.value(), f_odometry.m_yPos.value()),
      pc::vehicle::g_mechanicalData->getLength(),
      pc::vehicle::g_mechanicalData->m_width,
      l_yawAngle
    );
  }


  bool isHistoryTextureAccumulated()
  {
    if (m_historyTextureAccumulated)
    {
      return true;
    }

    if (g_audiFloorPlateStateHandlerSettings->m_renderHistoryTextureImmediately)
    {
      return true;
    }

    const pc::daddy::OdometryDataDaddy* const l_odometryDaddy = m_framework->m_odometryReceiver.getData();
    if (nullptr == l_odometryDaddy)
    {
      return false;
    }
    const pc::util::OrientedBoundingBox2<vfc::float32_t> l_currentOBB = getVehicleOBB(l_odometryDaddy->m_Data);
    if (l_currentOBB.intersects(m_historyAccumulationStartOBB))
    {
      return false;
    }
    return true;
  }

  bool areAllCamerasOperational() const
  {
    const pc::daddy::CameraDegradationMaskDaddy* const l_degradationStateDaddy = m_framework->m_degradationMaskReceiver.getData();
    if ((nullptr != l_degradationStateDaddy) && (0u != l_degradationStateDaddy->m_Data))
    {
      return false;
    }

    // const pc::daddy::CameraDeactivationMaskDaddy* const l_deactivationStateDaddy = m_framework->m_deactivationMaskReceiver.getData();
    // if ((nullptr != l_deactivationStateDaddy) && (0u != l_deactivationStateDaddy->m_Data))
    // {
    //   return false;
    // }

    return true;
  }

  bool isCalibOK() const
  {
    // Not calibrated
    if (m_framework->asCustomFramework()->m_calibOrNot_Receiver.isConnected())
    {
        if (m_framework->asCustomFramework()->m_calibOrNot_Receiver.hasData())
        {
            const cc::daddy::CalibOrNotDaddy_t* const l_calibOrNotDaddy = m_framework->asCustomFramework()->m_calibOrNot_Receiver.getData();
            if(l_calibOrNotDaddy->m_Data == true)
            {
                return true;
            } // false represents not calibrated
        }
    }
    return false;
  }

  bool isNotInStr()
  {
    if (m_framework->asCustomFramework()->m_systemStr_ReceiverPort.isConnected())
    {
        if (m_framework->asCustomFramework()->m_systemStr_ReceiverPort.hasData())
        {
            const cc::daddy::SystemStrDaddy_t* l_pSystemStr = m_framework->asCustomFramework()->m_systemStr_ReceiverPort.getData();
            if(l_pSystemStr->m_Data == cc::target::common::ESystemStr::SYSTEM_STR_NONE)  // l_pSystemStr->m_Data == SYSTEM_STR_ENTER, current is str
            {
                return true;
            }
        }
    }
    return false;
  }

  bool areDoorsAndMirrorsNominal() const
  {
    const pc::daddy::DoorStateDaddy* const l_doorStateDaddy = m_framework->m_doorStateReceiver.getData();
    bool l_doorsOpen = false;
    if (nullptr != l_doorStateDaddy)
    {
      //SysVS_SVS_947
      l_doorsOpen = (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) ||
                    (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) ||
                    (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT]) ||
                    (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) ||
                    (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK]);
    }

    const pc::daddy::MirrorStateDaddy* const l_mirrorState = m_framework->m_mirrorStateReceiver.getData();
    bool l_mirrorsUnfolded = true;
    if (nullptr != l_mirrorState)
    {
      //SysVS_SVS_931
      l_mirrorsUnfolded = (pc::daddy::MIRRORSTATE_NOT_FLAPPED == l_mirrorState->m_Data[pc::daddy::SIDEMIRROR_LEFT]) &&
                          (pc::daddy::MIRRORSTATE_NOT_FLAPPED == l_mirrorState->m_Data[pc::daddy::SIDEMIRROR_RIGHT]);
    }
    return !l_doorsOpen && l_mirrorsUnfolded;
  }

  bool isMovementInRange() const
  {
    const pc::daddy::SpeedDaddy* const l_pDataDaddy = m_framework->m_speedReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      if (isLess(std::abs(l_pDataDaddy->m_Data), g_blurSmData->m_movingTooFastThreshold_Kmh))
      {
        return true;
      }
      else
      {
        return false;
      }
    }
    return false;
  }

  bool isVhmValid() const
  {
    bool l_isVhmValid = true;
    if (m_framework->asCustomFramework()->m_svsFid_ReceiverPort.isConnected())
    {
        if (m_framework->asCustomFramework()->m_svsFid_ReceiverPort.hasData())
        {
            const cc::daddy::SvsFidDaddy_t* const l_SvsFiDaddy = m_framework->asCustomFramework()->m_svsFid_ReceiverPort.getData();
            l_isVhmValid = (l_SvsFiDaddy->m_Data.m_FID_RxVhmOverall == 1u); // 0u represents error
        }
    }
    return l_isVhmValid;
  }

  bool isAccumulatingHistory() const override
  {
    switch (m_currentState) // PRQA S 4018
    {
    case State::TEXTURED_FLOORPLATE_ACCUMULATING:
    case State::TEXTURED_FLOORPLATE_ACTIVE:
    {
      return true;
    }
    default:
    {
      return false;
    }
    }
  }
private:
  enum class State : vfc::uint8_t {
    INVALID,
    BLURRED_FLOORPLATE_ACTIVE,
    TEXTURED_FLOORPLATE_ACCUMULATING,
    TEXTURED_FLOORPLATE_ACTIVE
  };

  State m_currentState{State::INVALID};

  //! Updates current internal state, and returns corresponding render state
  FloorPlateState applyAndMapState(State f_state)
  {
    m_currentState = f_state;
    switch (f_state)
    {
    case State::BLURRED_FLOORPLATE_ACTIVE:
    case State::TEXTURED_FLOORPLATE_ACCUMULATING:
    {
      return RENDER_BLURRED_BASEPLATE;
      break;
    } // PRQA S 2880

    case State::TEXTURED_FLOORPLATE_ACTIVE:
    {
      if (g_audiFloorPlateStateHandlerSettings->m_renderHistoryTextureImmediately)
      {
        return RENDER_TEXTURED_OVER_BLURRED_BASEPLATE;
      }
      else
      {
        return RENDER_TEXTURED_BASEPLATE;
      }
      break;
    } // PRQA S 2880

    default:
    {
      return RENDER_UNTEXTURED;
      break;
    } // PRQA S 2880
    }
  }

  FloorPlateState updateState(FloorPlateState /*f_currentState*/) override
  {
    if (!m_historyTextureAvailable || !m_blurredFloorPlateAvailable)
    {
      return applyAndMapState(State::INVALID);
    }
    else if (m_currentState == State::INVALID)
    {
      m_currentState = State::BLURRED_FLOORPLATE_ACTIVE;
    }
    else
    {

    }

    const pc::daddy::OdometryDataDaddy* const l_odometryDaddy = m_framework->m_odometryReceiver.getData();
    if (nullptr == l_odometryDaddy)
    {
      return applyAndMapState(State::BLURRED_FLOORPLATE_ACTIVE);
    }

    if( !(g_audiFloorPlateStateHandlerSettings->m_enableHistoricGroundplane) )
    {
      //disable historical baseplate
      return applyAndMapState(State::BLURRED_FLOORPLATE_ACTIVE);
    }

    // bool l_vehicleMoving = (l_odometryDaddy->m_Data.m_velocity.value() > g_audiFloorPlateStateHandlerSettings->m_maxStandstillVelocity);
    // if (l_vehicleMoving)
    // {
    //   m_lastMovementTimer.setStartTick();
    // }

    const bool l_texturePreconditionMeet = (isMovementInRange() && isVhmValid() && areAllCamerasOperational() && isCalibOK() && isNotInStr() /*& areDoorsAndMirrorsNominal()*/);

    switch (m_currentState) // PRQA S 4609
    {
    case State::BLURRED_FLOORPLATE_ACTIVE:
    {
      if (l_texturePreconditionMeet)
      {
        m_historyAccumulationStartOBB = getVehicleOBB(l_odometryDaddy->m_Data);
        return applyAndMapState(State::TEXTURED_FLOORPLATE_ACCUMULATING);
      }
      break;
    }
    case State::TEXTURED_FLOORPLATE_ACCUMULATING:
    {
      if (!l_texturePreconditionMeet)
      {
        return applyAndMapState(State::BLURRED_FLOORPLATE_ACTIVE);
      }
      if (isHistoryTextureAccumulated() && l_texturePreconditionMeet)
      {
        m_historyTextureAccumulated = true;
        return applyAndMapState(State::TEXTURED_FLOORPLATE_ACTIVE);
      }
      break;
    }
    case State::TEXTURED_FLOORPLATE_ACTIVE:
    {
      if (!l_texturePreconditionMeet)
      {
        m_historyTextureAccumulated = false;
        return applyAndMapState(State::BLURRED_FLOORPLATE_ACTIVE);
      }
      break;
    }
    case State::INVALID: // PRQA S 2880
    default:
    {
      // Invalid state, do nothing
      break;
    }
    }
    return applyAndMapState(m_currentState);
  }

  pc::util::OrientedBoundingBox2<vfc::float32_t> m_historyAccumulationStartOBB;
  bool m_historyTextureAccumulated;

  //osg::Timer m_lastMovementTimer;

  osg::ref_ptr<pc::core::Framework> m_framework;

  bool  m_blurredFloorPlateAvailable;
  bool  m_historyTextureAvailable;
};

} // namespace

pc::util::coding::Item<AudiFloorPlateStateHandlerSettings> g_audiFloorPlateStateHandlerSettings("AudiFloorPlateStateHandler");

//!
//! Floor
//!
Floor::Floor(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, const osg::Vec2f& f_offset, const bool f_enableFOVBasedCulling)
: Floor{f_assetId, f_pFramework, pc::worker::bowlshaping::PolarBowlLayout{f_offset}, f_enableFOVBasedCulling}
{
}

Floor::Floor(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, const pc::worker::bowlshaping::PolarBowlLayout& f_pLayout, const bool f_enableFOVBasedCulling)
: pc::core::Asset{f_assetId}
, m_framework{f_pFramework}
, m_floor{nullptr}
, m_basePlateAsset{nullptr}
, m_floorPlateRenderer{nullptr}
, m_floorPlateGenerator{nullptr}
, m_carshadowGeode{nullptr}
{
  m_floor = new pc::factory::Floor(g_floorData->m_size, g_floorData->m_resolution, f_pLayout.getOffset());

  m_floor->setName("Floor");
  pc::core::Asset::addChild(m_floor);  // PRQA S 3803
  m_asset = m_floor;
  m_floorPlateGenerator=new floorplate::CustomFloorPlateGenerator(f_pFramework);
  // Add floor plate renderer. The floor plate renderer will also take care of setting up SV3DUpdateCallbacks for the floor
  m_floorPlateRenderer = new cc::assets::common::CustomFloorPlateRenderer // PRQA S 2759
  (
    m_floor,
    f_pLayout,
    m_floorPlateGenerator,
    cc::core::AssetId::EASSETS_BASEPLATE,
    f_pFramework,
    f_enableFOVBasedCulling
  );
  m_floorPlateRenderer->setHistoryStripesAlpha(g_floorData->m_stripedBasePlateTransparency);

  // Install custom floor plate state handler
  m_floorPlateRenderer->setFloorPlateStateHandler(new AudiFloorPlateStateHandler(f_pFramework));

  m_basePlateAsset = new pc::core::Asset(cc::core::AssetId::EASSETS_BASEPLATE, m_floorPlateRenderer);

  const osg::ref_ptr<osg::Node> l_geomNode1 = osgDB::readNodeFile(g_floorData->m_basepaltePath);
  if (nullptr != l_geomNode1)
  {
    l_geomNode1->setStateSet(m_floorPlateRenderer->getBlurredBasePlateStateSet());
    osg::Group::addChild(l_geomNode1);  // PRQA S 3803
    osg::StateSet* const l_stateSet = l_geomNode1->getOrCreateStateSet();
    osg::Depth* const l_depth = new osg::Depth; // PRQA S 4262 // PRQA S 4264
    l_depth->setWriteMask(false);
    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_floor+1, "RenderBin");
    l_stateSet->setAttribute(l_depth);
  }

  const osg::ref_ptr<osg::Node> l_geomNode2 = osgDB::readNodeFile(g_floorData->m_basepaltePath);
  if (nullptr != l_geomNode2)
  {
    l_geomNode2->setStateSet(m_floorPlateRenderer->getHistoryStateSet(0.5f, 0.5f));
    osg::Group::addChild(l_geomNode2);  // PRQA S 3803
    osg::StateSet* const l_stateSet = l_geomNode2->getOrCreateStateSet();
    osg::Depth* const l_depth = new osg::Depth;
    l_depth->setWriteMask(false);
    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_floor+2, "RenderBin");
    l_stateSet->setAttribute(l_depth);
  }

}

Floor::Floor(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, const pc::worker::bowlshaping::PolarBowlLayout& f_pLayout, cc::assets::common::FloorType f_floorType, const bool f_enableFOVBasedCulling)
: Asset{f_assetId}
, m_framework{f_pFramework}
, m_floor{}
, m_basePlateAsset{}
, m_floorPlateRenderer{}
, m_floorPlateGenerator{}
, m_carshadowGeode{}
{
  if (f_pFramework != nullptr)
  {
    m_floor = new cc::assets::common::FloorSingleCam(g_floorData->m_size, g_floorData->m_resolution, f_pLayout.getOffset(), f_floorType);

    switch (f_floorType) // PRQA S 4018
    {
    case cc::assets::common::FLOOR_REAR:
    {
      m_floor->setName("RearFloor");
      break;
    }
    default: // FLOOR_FRONT
    {
      m_floor->setName("FrontFloor");
      break;
    }
    }

    pc::core::Asset::addChild(m_floor);  // PRQA S 3803
    m_asset = m_floor;

    pc::factory::FloorUpdateVisitor* const l_floorUpdateVisitor = new pc::factory::FloorUpdateVisitor( // PRQA S 4262 // PRQA S 4264
      f_pFramework->m_cameraCalibrationReceiver,
      f_pFramework->m_cameraMasksReceiver,
      f_pLayout
    );
    l_floorUpdateVisitor->setAdaptMesh(false);
    l_floorUpdateVisitor->enableFOVBasedCulling(f_enableFOVBasedCulling);

    m_floor->accept(*l_floorUpdateVisitor);
    m_floor->addUpdateCallback(new pc::factory::SV3DUpdateCallback(l_floorUpdateVisitor));
  }
}

void Floor::traverse(osg::NodeVisitor &f_nv)
{
  osg::Group::traverse(f_nv);
}

osg::ref_ptr<pc::core::Asset> Floor::getBasePlateAsset() const
{
  return m_basePlateAsset;
}

osg::ref_ptr<pc::factory::Floor> Floor::getSV3DNode() const
{
  return m_floor;
}


void Floor::addDynamicWheelMaskDependency(cc::assets::DynWheelMaskAsset* f_dynMaskNode)
{
    if (f_dynMaskNode == nullptr)
    {
        return;
    }
    /// Floor contains the asset to visualize it in every required view.
    addChild(f_dynMaskNode);

    /// Get StateSet during init. Later runtime update will be implemented.
    /// Dynamic wheel mask can take two StateSets. Currently noCam & history.
    /// Will be rendered in two render pass.
    /// This is required for example in order to first render a noCam pass (black) and then the history pass on top of
    /// the (black) noCam.
    f_dynMaskNode->addTwoStateSets(m_floorPlateRenderer->getBlurredBasePlateStateSet(),m_floorPlateRenderer->getHistoryStateSet(0.5f, 0.5f));

    /// TODO: Eliminate shallow copy.
    /// This part is required for the snapshot cam.
    /// The dynamicWheelMask asset with a special "no cam area StateSet" is reused in order to mask it out from the
    /// sampling.
    const auto l_newGeode = osg_ext::make_ref<osg::Geode>(*f_dynMaskNode->getChild(0)->asGeode(), osg::CopyOp::SHALLOW_COPY);
    const auto l_newNode  = osg_ext::make_ref<osg::Group>();
    l_newNode->addChild(l_newGeode);
    m_floorPlateGenerator->getSnapshotCam()->addChild(l_newNode);

    osg::StateSet*                         const l_stateSet = l_newGeode->getOrCreateStateSet();
    pc::core::BasicShaderProgramDescriptor l_basicTexShaderLine("basicColor");
    l_basicTexShaderLine.apply(l_stateSet); // PRQA S 3803
    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_texBaseplateExclude, "RenderBin");

    // Set a transparent color for the geometry.
    // This is used to discard the areas for floorplate-snapshots
    if (l_newGeode->getNumDrawables() > 0u)
    {
        osg::Geometry* const l_geo = l_newGeode->getDrawable(0u)->asGeometry();
        if (nullptr != l_geo)
        {
            osg::Vec4Array* const color = new osg::Vec4Array(1u);
            (*color)[0u]          = osg::Vec4f();
            l_geo->setColorArray(color, osg::Array::BIND_OVERALL);
        }
    }
}
} // namespace common
} // namespace assets
} // namespace cc
