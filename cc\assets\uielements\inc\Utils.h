//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Ha Thanh Phong (MS/EDA92-XC)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Utils.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_UTILS_H
#define CC_ASSETS_UIELEMENTS_UTILS_H


#include "pc/generic/util/coding/inc/ClassDescriptor.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"

#include <osg/Vec2f>

namespace cc
{
namespace assets
{
namespace uielements
{

class UiUtilSettings : public pc::util::coding::ISerializable
{
public:
  UiUtilSettings()
  : m_baseMainviewLayout(osg::Vec2f{842.0f, 1320.0f})
  , m_basePlanviewLayout(osg::Vec2f{1714.0f, 1320.0f})
  {
  }

  SERIALIZABLE(UiUtilSettings)
  {
    ADD_MEMBER(osg::Vec2f, baseMainviewLayout);
    ADD_MEMBER(osg::Vec2f, basePlanviewLayout);
  }

  osg::Vec2f m_baseMainviewLayout;
  osg::Vec2f m_basePlanviewLayout;
};

extern pc::util::coding::Item<UiUtilSettings> g_uiUtilSettings;

inline osg::Vec2f scaleFactorMainview();
inline osg::Vec2f scaleFactorPlanview();
osg::Vec2f getImageSizeHori(const std::string& f_iconPath);
osg::Vec2f getImageSizeVert(const std::string& f_iconPath);
osg::Vec2f getSlotImageSizeVert(const std::string& f_iconPath);
osg::Vec2f transferToBottomLeftHori(const osg::Vec2f f_iconPos);
osg::Vec2f transferToBottomLeftVert(const osg::Vec2f f_iconPos);
osg::Vec2f transferToBottomLeftHoriHU(const osg::Vec2f f_iconPos);
osg::Vec2f getImageSize(const std::string& f_iconPath);
pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool& f_isHoriScreen=true);
pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen=true);
pc::assets::Icon* createAlphaMaskIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen=true);
pc::assets::Icon* createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool& f_isHoriScreen=true);
pc::assets::Icon* createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen=true);
osg::Vec2f getSurroundViewPosition(const osg::Vec2f& f_iconPos);
osg::Vec2f getSurroundViewIconSize(const osg::Vec2f& f_iconSize);
bool checkCoorInResponseArea(vfc::uint16_t f_coorX, vfc::uint16_t f_coorY, osg::Vec2f f_center, osg::Vec2f f_responseArea); // PRQA S 0621

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_UTILS_H
