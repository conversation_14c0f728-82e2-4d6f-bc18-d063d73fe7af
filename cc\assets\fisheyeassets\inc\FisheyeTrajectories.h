//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: JLR NFS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS JLR
/// @file  FisheyeTrajectories.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_FISHEYEASSETS_FISHEYETRAJECTORIES
#define CC_ASSETS_FISHEYEASSETS_FISHEYETRAJECTORIES

#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "cc/assets/trajectory/inc/WheelTrack.h"
#include "cc/assets/trajectory/inc/OutermostLine.h"
#include "cc/assets/trajectory/inc/OutermostLineColorful.h"
#include "cc/assets/trajectory/inc/CoverPlate.h"
#include "cc/assets/trajectory/inc/DL1.h"
#include "cc/assets/rctaoverlay/inc/RCTAOverlay.h"
#include "cc/assets/trajectory/inc/TrailerAssistLine.h"

#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/views/warpfisheyeview/inc/WarpFisheyeView.h" // Needed for for FisheyeViewSettings

namespace pc
{
namespace views
{
namespace warpfisheye
{
class FisheyeModel;
} // namespace warpfisheye
} // namespace views
} // namespace pc

namespace cc
{
namespace assets
{
namespace fisheyeassets
{

osg::Texture2D* createTexture(osg::Image* f_image);

//======================================================
// FisheyeWheelTrack
//------------------------------------------------------
/// Specialization of class @ref cc.assets.trajectory.subassets.WheelTrack for fisheye views.
/// The base class is extended with an additional vertex array which contains points in fisheye orthographic space.
/// The fisheye transformation depends on a parameter fisheye model, and the orthographic projection is used to match
/// the projection applied in @ref pc.views.warpfisheye.WarpFisheyeView.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup WarpFisheye
//======================================================
class FisheyeWheelTrack : public cc::assets::trajectory::WheelTrack
{
public:
  FisheyeWheelTrack(cc::core::CustomFramework* f_pCustomFramework,
             cc::assets::trajectory::commontypes::Side_en f_side,
             float f_height,
             const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
             const cc::assets::trajectory::DL1* const f_distanceLine,
             unsigned int f_numOfVerts,
             WheelTrackType f_wheelTrackType,
             pc::core::sysconf::Cameras f_camId,
             pc::views::warpfisheye::FisheyeModel* f_pModel,
             pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void generateVertexData() override;

protected:
  virtual ~FisheyeWheelTrack();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeWheelTrack (const FisheyeWheelTrack& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeWheelTrack& operator=(const FisheyeWheelTrack& other); // = delete
};

class FisheyeTrailerAssistLine : public cc::assets::trajectory::TrailerAssistLine
{
public:
  FisheyeTrailerAssistLine(cc::core::CustomFramework* f_pCustomFramework,
             float f_height,
             const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
             unsigned int f_numOfVerts,
             pc::core::sysconf::Cameras f_camId,
             pc::views::warpfisheye::FisheyeModel* f_pModel,
             pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void generateVertexData() override;

protected:
  virtual ~FisheyeTrailerAssistLine();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeTrailerAssistLine (const FisheyeTrailerAssistLine& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeTrailerAssistLine& operator=(const FisheyeTrailerAssistLine& other); // = delete
};

class FisheyeOutermostLine : public cc::assets::trajectory::OutermostLine
{
public:
  FisheyeOutermostLine(
              cc::core::CustomFramework* f_pCustomFramework,
              cc::assets::trajectory::commontypes::Side_en f_side,
              float f_height,
              const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
              const cc::assets::trajectory::DIDescriptor_st& f_DIDescriptor,
              unsigned int f_numOfVerts_BeforeDIs,
              unsigned int f_numOfVerts_BetweenDIs,
              unsigned int f_numOfVerts_AfterDIs,
              pc::core::sysconf::Cameras f_camId,
              pc::views::warpfisheye::FisheyeModel* f_pModel,
              pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void generateVertexData() override;

protected:
  virtual ~FisheyeOutermostLine();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeOutermostLine (const FisheyeOutermostLine& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeOutermostLine& operator=(const FisheyeOutermostLine& other); // = delete
};

class FisheyeOutermostLineColorful : public cc::assets::trajectory::OutermostLineColorful
{
public:
  FisheyeOutermostLineColorful(
              cc::core::CustomFramework* f_pCustomFramework,
              cc::assets::trajectory::commontypes::Side_en f_side,
              float f_height,
              const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
              const cc::assets::trajectory::DIDescriptor_st& f_DIDescriptor,
              unsigned int f_numOfVerts_BeforeDIs,
              unsigned int f_numOfVerts_BetweenDIs,
              unsigned int f_numOfVerts_AfterDIs,
              pc::core::sysconf::Cameras f_camId,
              pc::views::warpfisheye::FisheyeModel* f_pModel,
              pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void generateVertexData() override;

protected:
  virtual ~FisheyeOutermostLineColorful();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeOutermostLineColorful (const FisheyeOutermostLineColorful& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeOutermostLineColorful& operator=(const FisheyeOutermostLineColorful& other); // = delete
};

class FisheyeCoverPlate : public cc::assets::trajectory::CoverPlate
{
public:
  FisheyeCoverPlate(
              cc::core::CustomFramework* f_pCustomFramework,
              float f_height,
              const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
              unsigned int f_numOfVerts,
              pc::core::sysconf::Cameras f_camId,
              pc::views::warpfisheye::FisheyeModel* f_pModel,
              pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void generateVertexData() override;

protected:
  virtual ~FisheyeCoverPlate();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeCoverPlate (const FisheyeCoverPlate& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeCoverPlate& operator=(const FisheyeCoverPlate& other); // = delete
};

class FisheyeDL1 : public cc::assets::trajectory::DL1
{
public:
  FisheyeDL1(
              cc::core::CustomFramework* f_pCustomFramework,
              float f_height,
              const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
              const assets::trajectory::OutermostLine* const f_leftOutermostLine,
              const assets::trajectory::OutermostLine* const f_rightOutermostLine,
              unsigned int f_numLayoutPoints,
              pc::core::sysconf::Cameras f_camId,
              pc::views::warpfisheye::FisheyeModel* f_pModel,
              pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void generateVertexData() override;

protected:
  virtual ~FisheyeDL1();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeDL1 (const FisheyeDL1& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeDL1& operator=(const FisheyeDL1& other); // = delete
};

//======================================================
// FisheyeRctaOverlay
//------------------------------------------------------
/// The base class is extended with an additional vertex array which contains points in fisheye orthographic space.
/// The fisheye transformation depends on a parameter fisheye model, and the orthographic projection is used to match
/// the projection applied in @ref pc.views.warpfisheye.WarpFisheyeView.
/// <AUTHOR> Donny (CC-DA/EPF-CN)
/// @ingroup WarpFisheye
//======================================================
class FisheyeRctaOverlay : public cc::assets::rctaoverlay::RctaOverlay
{
public:
  FisheyeRctaOverlay(cc::core::CustomFramework* f_pCustomFramework,
             pc::core::sysconf::Cameras f_camId,
             pc::views::warpfisheye::FisheyeModel* f_pModel,
             pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &f_settings );

  virtual void init() override;

protected:
  virtual ~FisheyeRctaOverlay();

  pc::core::sysconf::Cameras                                          m_camId;           //!< Used for texture Id and Calibration access
  osg::ref_ptr<pc::views::warpfisheye::FisheyeModel>                  m_pModel;          //!< Fisheye Model to be applied to the trajectory
  cc::core::CustomFramework*                                          m_framework;
  pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> &m_settings;       //!< Fisheye Virtual Camera settings (e.g. pitch, fov, etc)
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeRctaLeftVertices; //!< Actual vertex array used for visualization
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeRctaRightVertices; //!< Actual vertex array used for visualization
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeFctaLeftVertices; //!< Actual vertex array used for visualization
  osg::ref_ptr<osg::Vec3Array>                                        m_fisheyeFctaRightVertices; //!< Actual vertex array used for visualization

private:
  //! Copy constructor is not permitted.
  FisheyeRctaOverlay (const FisheyeRctaOverlay& other); // = delete
  //! Copy assignment operator is not permitted.
  FisheyeRctaOverlay& operator=(const FisheyeRctaOverlay& other); // = delete
};


} // namespace fisheyeassets
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FISHEYEASSETS_FISHEYETRAJECTORIES
