//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_HELPER_H
#define CC_ASSETS_TRAJECTORY_HELPER_H

#include <osg/Geometry>

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace helper
{


enum RGBA_en
{
  R = 0,
  G = 1,
  B = 2,
  A = 3
};

void Rotate2DPoint(osg::Vec2f& f_PointToRotate, float f_cos, float f_sin);
void Rotate2DPoint(osg::Vec2f& f_PointToRotate, float f_Angle);
void Rotate2DPoint(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround, float f_cos, float f_sin);
void Rotate2DPoint(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround, float f_Angle);

void SwapVerticesVec4f(osg::Vec4f & f_Vertex1, osg::Vec4f & f_Vertex2);

float smoothstep(const float f_out_0,
                          const float f_out_1,
                          const float f_in_0,
                          const float f_in_1,
                                float f_in);

osg::Vec4ub smoothstep_Vec4ub(const osg::Vec4ub& f_out_0,
                                       const osg::Vec4ub& f_out_1,
                                       const float f_in_0,
                                       const float f_in_1,
                                             float f_in);
osg::Vec4ub smoothstep_GGX_Vec4ub(const osg::Vec4ub& f_out_0,
                                           const osg::Vec4ub& f_out_1,
                                           const float f_in_0,
                                           const float f_in_1,
                                                 float f_in);
osg::Vec4ub smoothstep_Vec4fIn_Vec4ubOut(const osg::Vec4f& f_out_0,
                                                  const osg::Vec4f& f_out_1,
                                                  const float f_in_0,
                                                  const float f_in_1,
                                                        float f_in);
osg::Vec4f smoothstep_Vec4f(const osg::Vec4f& f_out_0,
                                     const osg::Vec4f& f_out_1,
                                     const float f_in_0,
                                     const float f_in_1,
                                           float f_in);
bool isInfinity(float f_value);


class RotationFunctor
{
public:

  RotationFunctor(float f_angle);

  void rotate(osg::Vec2f& f_PointToRotate) const;
  void rotate(osg::Vec2f& f_PointToRotate, const osg::Vec2f& f_PointToRotateAround) const;

private:

  float m_cos;
  float m_sin;
};


class Comparator
{
public:
  Comparator();
  virtual ~Comparator();
  virtual bool Compare(float f_A, float f_B) const = 0;
};

class A_LessThan_B : public Comparator
{
  virtual bool Compare(float f_A, float f_B) const;
};

class A_GreaterThan_B : public Comparator
{
  virtual bool Compare(float f_A, float f_B) const;
};


} // namespace helper
} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_HELPER_H
