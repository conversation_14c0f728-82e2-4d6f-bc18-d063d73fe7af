//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_EXTRAOUTERMOSTLINE
#define CC_ASSETS_TRAJECTORY_SUBASSETS_EXTRAOUTERMOSTLINE

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"


namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic


class ExtraOutermostLine : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:

  ExtraOutermostLine(
    pc::core::Framework* f_framework,
    float f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    unsigned int f_numOfVerts);

  virtual void generateVertexData();

  osg::Image* create1DTexture() const;

protected:

  virtual ~ExtraOutermostLine();

  void generateVertexData_usingTexture();

  const unsigned int mc_numOfVerts;

private:

  //! Copy constructor is not permitted.
  ExtraOutermostLine (const ExtraOutermostLine& other); // = delete
  //! Copy assignment operator is not permitted.
  ExtraOutermostLine& operator=(const ExtraOutermostLine& other); // = delete

};


} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_EXTRAOUTERMOSTLINE
