//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EPF2-CN)
//  Department: CC-DA/EPF
//=============================================================================
/// @swcomponent CC DAI
/// @file  FreeparkingManager.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_MANAGER_H
#define CC_ASSETS_FREEPARKINGOVERLAY_MANAGER_H

#include "cc/assets/freeparkingoverlay/inc/FreeparkingInterface.h"
#include "cc/util/polygonmath/inc/PolygonMath.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/Switch>
#include <osg/Timer>

#define DEBUG_PARKSPOT_MANAGER 0

//! forward declaration
namespace pc
{
namespace core
{
class Framework;
class Viewport;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

class FreeparkingOverlay;
class TouchscreenCallback;

void clampAngleDegree(vfc::float32_t& f_angle);
void clampAngleRad(vfc::float32_t& f_angle);

class FreeparkingImageProperties : public pc::util::coding::ISerializable
{
public:
    FreeparkingImageProperties()
        : m_slotWidthPixel(200.0f)
        , m_slotHeightPixel(420.0f)
        // , m_rotateIconWidthPixel(37.0f)
        // , m_rotateIconHeightPixel(37.0f)
        , m_rotateIconWidthExtendPixel(11.0f)
        , m_rotateIconHeightExtendPixel(11.0f)
    // , m_imageWidthPixel(222.0f)
    // , m_imageHeightPixel(444.0f)
    {
    }

    SERIALIZABLE
    {
        ADD_MEMBER(slotWidthPixel);
        ADD_MEMBER(slotHeightPixel);
        ADD_MEMBER(rotateIconWidthExtendPixel);
        ADD_MEMBER(rotateIconHeightExtendPixel);
    }

    vfc::float32_t m_slotWidthPixel;              // in pixel
    vfc::float32_t m_slotHeightPixel;             // in pixel
    vfc::float32_t m_rotateIconWidthExtendPixel;  // in pixel
    vfc::float32_t m_rotateIconHeightExtendPixel; // in pixel
};

//!
//! FreeparkingManagerSettings
//!
//--------------------------------------------------------------------------------------------------------------
//! @brief : define the coding parameters that used in anywhere parking
//! @details : more clarification can be find in below beside the variable definition
//--------------------------------------------------------------------------------------------------------------
class FreeparkingManagerSettings : public pc::util::coding::ISerializable
{
public:
    FreeparkingManagerSettings()
        : m_defaultPositionCross(-8.0f, 2.2f)
        , m_defaultPositionParallel(-8.0f, 2.2f)
        , m_defaultPositionDiagonal(-8.0f, 2.2f)
        , m_defaultAngle(0.0f)
        , m_widthOffset(0.6f)
        , m_lengthOffset(1.5f)
        , m_horizontalPad_default(true)
        , m_freeparkingSlotWidth(2.5f)
        , m_rotateButtonRadius(40.0f)
    {
    }

    SERIALIZABLE
    {
        ADD_MEMBER(defaultPositionCross);
        ADD_MEMBER(defaultPositionParallel);
        ADD_MEMBER(defaultPositionDiagonal);
        ADD_MEMBER(defaultAngle);
        ADD_MEMBER(widthOffset);
        ADD_MEMBER(lengthOffset);
        ADD_MEMBER(horizontalPad_default);
        ADD_MEMBER(freeparkingSlotWidth);
        ADD_MEMBER(imageProperties);
        ADD_MEMBER(rotateButtonRadius);
        ADD_MEMBER(viewportBoundbackScale);
        ADD_MEMBER(rotateRange);
    }

    osg::Vec2f                 m_defaultPositionCross;
    osg::Vec2f                 m_defaultPositionParallel;
    osg::Vec2f                 m_defaultPositionDiagonal;
    vfc::float32_t             m_defaultAngle;
    vfc::float32_t             m_widthOffset;
    vfc::float32_t             m_lengthOffset;
    bool                       m_horizontalPad_default;
    vfc::float32_t             m_freeparkingSlotWidth; // meter, default = 2.5m
    FreeparkingImageProperties m_imageProperties;
    vfc::float32_t             m_rotateButtonRadius; // use for determine rotation button is within touch threshold
    vfc::float32_t             m_viewportBoundbackScale = 1.0f;  // scale the boudingbox of the viewport
    vfc::float32_t             m_rotateRange            = 10.0f; // rotatable angle in degree in normal mode
};

//!
//! FreeparkingManager
//!
class FreeparkingManager : public osg::Group
{
public:
    enum TouchStatus : vfc::uint8_t
    {
        Invalid = 0u,
        Down    = 1u,
        Up      = 2u,
        Move    = 3u,
    };

public:
    FreeparkingManager(pc::core::Framework* f_framework, const pc::core::Viewport* f_viewport);

    bool isfreeparkingOverlayActive(vfc::uint32_t f_index) const
    {
        return m_freeparkingOverlayAssets->getValue(f_index);
    }
    virtual void traverse(osg::NodeVisitor& f_nv) override;

    osg::Vec2f getSlotCenter()
    {
        return m_spotRect.getCenterPoint();
    }

    void setTouchData(TouchStatus f_touchStatus, const osg::Vec2f& f_touchPoint);

protected:
    virtual ~FreeparkingManager() = default;

private:
    //! Copy constructor is not permitted.
    FreeparkingManager(const FreeparkingManager& other); // = delete
    //! Copy assignment operator is not permitted.
    FreeparkingManager& operator=(const FreeparkingManager& other); // = delete

private:
    void logInternal();
    void updateInput();
    void update();
    void updateSpotSize();
    void reset();
    void prepareOutput();
    void sendOutput();

    FreeparkingOverlay* getFreeparkingOverlay(vfc::uint32_t f_index);

    void updateFreeparkingOverlay();
    // void updateFreeparkingSpotSize();

    void getSlitherStartEndPoint();
    void getSpotCornerCoorandSlitherType();
    void UpdateSlotOrientation();
    void clampRotateAngleInNormalMode();
    void slotBoundingBoxCheckAndCorrect();

    osg::Vec2f getWorldPointFromScreenPoint(osg::Vec2f f_screenPoint);

private:
    pc::core::Framework*      m_framework;
    osg::ref_ptr<osg::Switch> m_freeparkingOverlayAssets;
    vfc::uint32_t             m_lastUpdate;
    bool                      m_SlitheringFlag;
    osg::Matrixf              m_MVPmatrix;
    bool                      m_MVPmatrixValid;
    osg::Vec2f                m_spotSize; // slot size in meter
    osg::Vec2f                m_centerPosition;
    vfc::float32_t            m_rotateAngle;
    bool                      m_horizontalPad;
    const pc::core::Viewport* m_viewport;
    bool                      m_isUserFinishedMoving;
    bool                      m_firstEntry;
    bool                      m_isSlotMovedByUser      = false;
    bool                      m_isSlotMovingByUser     = false;
    bool                      m_canReset               = false;
    bool                      m_360Rotate              = false;
    bool                      m_previousIsLeft         = false;
    bool                      m_twoFingerRotationValid = false;
    vfc::uint32_t             m_lastConfigUpdate;
    APSlitherStartEndPos      m_SlitherPos;
    APSlitherActionType       m_prevSlitherActionType;
    APSlitherActionType       m_SlitherActionType;
    osg::Vec2f                m_SlitherBeginPos;

    osg::Vec3f m_FrontLeft_vertex_screen = osg::Vec3f(0.f, 0.f, 0.f);
    osg::Vec3f m_FrontRight_vertex_sreen = osg::Vec3f(0.f, 0.f, 0.f);
    osg::Vec3f m_RearLeft_vertex_screen  = osg::Vec3f(0.f, 0.f, 0.f);
    osg::Vec3f m_RearRight_vertex_screen = osg::Vec3f(0.f, 0.f, 0.f);
    osg::Vec3f m_Center_vertex_screen    = osg::Vec3f(0.f, 0.f, 0.f);

    osg::Vec2f m_viewportTopLeft;
    osg::Vec2f m_viewportTopRight;
    osg::Vec2f m_viewportBottomLeft;
    osg::Vec2f m_viewportBottomRight;

    cc::util::polygonmath::RectangleMath m_spotRect;
    APSlotOrientation                    m_SlotOrientation;

    //! Daddy Data
    bool                  m_freeparkingActive = false;
    EFreeParkingSpaceType m_freeparkingType;
    bool                  m_newTouchStatus{false};
    TouchStatus           m_touchStatus;
    osg::Vec2f            m_touchPosition;
    EFreeParkingSlotState m_parkable;
    vfc::int32_t          m_parkingSlotStateCtr;

    //! Output
    vfc::float32_t m_outputAngle;
    osg::Vec2f     m_outputSlotCenter;
    osg::Vec2f     m_outputRearAxleCenter;
};

extern pc::util::coding::Item<FreeparkingManagerSettings> g_managerSettings;

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_MANAGER_H
