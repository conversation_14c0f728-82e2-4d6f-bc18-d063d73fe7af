//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  WheelSeparator.cpp
/// @brief 
//=============================================================================

#include "cc/assets/uielements/inc/WheelSeparator.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

namespace wheelseparatorhorizontal
{
  
  // ! enum values for wheel separator
  enum class WheelSeparatorHorizontalType : vfc::uint32_t
  {
    WHEELSEPARATOR_HORIZONTAL_PLANVIEW,
    WHEELSEPARATOR_HORIZONTAL_REAR,
    WHEELSEPARATOR_HORIZONTAL_FRONT
  };

  //!
  //! @brief Construct a new WheelSeparator Manager:: WheelSeparator Manager object
  //!
  //! @param f_config
  //!
  WheelSeparatorManagerHorizontal::WheelSeparatorManagerHorizontal()
    : m_lastConfigUpdate{~0u}
    , m_wheelSeparatorIcons{}
    , m_mat_b{false}
  {
  }



  WheelSeparatorManagerHorizontal::~WheelSeparatorManagerHorizontal() = default;

  void WheelSeparatorManagerHorizontal::init(pc::assets::ImageOverlays* f_imageOverlays)
  {
    // ! init WheelSeparator icons
    m_wheelSeparatorIcons.clear(f_imageOverlays);
    m_wheelSeparatorIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheelSeparatorHorizontal, g_uiSettings->m_settingWheelSeparatorHorizontal.m_iconCenter, g_uiSettings->m_settingWheelSeparatorHorizontal.m_iconSize));
    m_wheelSeparatorIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheelSeparatorHorizontalRear, g_uiSettings->m_settingWheelSeparatorHorizontal.m_iconCenter, g_uiSettings->m_settingWheelSeparatorHorizontal.m_iconSize));
    m_wheelSeparatorIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheelSeparatorHorizontalFront, g_uiSettings->m_settingWheelSeparatorHorizontal.m_iconCenter, g_uiSettings->m_settingWheelSeparatorHorizontal.m_iconSize));
  }


  void WheelSeparatorManagerHorizontal::update(pc::assets::ImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework)
  {
    if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
    {
        return;
    }
    m_wheelSeparatorIcons.setAllEnabled(false);
    // ! check if config has changed
    if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
    {
      init(f_imageOverlays);
      m_lastConfigUpdate = g_uiSettings->getModifiedCount();
    }

    if (f_framework->m_currentView_ReceiverPort.hasData())
    {
    const cc::daddy::SVSCurrentViewDaddy_t* const l_data = f_framework->m_currentView_ReceiverPort.getData();
    const EScreenID l_curviewid = static_cast<EScreenID>(l_data->m_Data); //PRQA S 3013

    // ! check the displayed view id
    if ( EScreenID_WHEEL_FRONT_DUAL == l_curviewid )
    {
        m_wheelSeparatorIcons.getIcon(static_cast<vfc::uint32_t>(WheelSeparatorHorizontalType::WHEELSEPARATOR_HORIZONTAL_FRONT))->setEnabled(true);
    }
    else if ( EScreenID_WHEEL_REAR_DUAL == l_curviewid)
    {
        m_wheelSeparatorIcons.getIcon(static_cast<vfc::uint32_t>(WheelSeparatorHorizontalType::WHEELSEPARATOR_HORIZONTAL_REAR))->setEnabled(true);
    }
    else if ( EScreenID_PLANVIEW_WITH_SEPARATOR == l_curviewid)
    {
        m_wheelSeparatorIcons.getIcon(static_cast<vfc::uint32_t>(WheelSeparatorHorizontalType::WHEELSEPARATOR_HORIZONTAL_PLANVIEW))->setEnabled(true);
    }
    else
    {
      //do nothing
    }
    }
  }


  //!
  //! @brief Construct a new -:: WheelSeparator object
  //!
  //! @param f_customFramework
  //! @param f_assetId
  //! @param f_view
  //!
  WheelSeparatorHorizontal::WheelSeparatorHorizontal(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
    : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}	// PRQA S 2966 
    , m_customFramework{f_customFramework}
    , m_manager{}
  {
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

    //! render order
    //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
    constexpr vfc::uint32_t l_renderOrder = 10u;
    cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder);

  }


  WheelSeparatorHorizontal::~WheelSeparatorHorizontal() = default;


  void WheelSeparatorHorizontal::traverse(osg::NodeVisitor& f_nv)
  {
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
      m_manager.update(this, m_customFramework);
    }
    pc::assets::ImageOverlays::traverse(f_nv);
  }

} // namespace wheelseparatorhorizontal

namespace wheelseparatorvertical
{
  // ! enum values for wheel separator
  enum class WheelSeparatorVerticalType : vfc::uint32_t
  {
    WHEELSEPARATOR_VERTICAL
  };

  //!
  //! @brief Construct a new WheelSeparator Manager:: WheelSeparator Manager object
  //!
  //! @param f_config
  //!
  WheelSeparatorManagerVertical::WheelSeparatorManagerVertical()
    : m_lastConfigUpdate{~0u}
    , m_wheelSeparatorIcons{}
    , m_mat_b{false}
  {
  }



  WheelSeparatorManagerVertical::~WheelSeparatorManagerVertical() = default;

  void WheelSeparatorManagerVertical::init(pc::assets::ImageOverlays* f_imageOverlays)
  {
    // ! init WheelSeparator icons
    m_wheelSeparatorIcons.clear(f_imageOverlays);
    m_wheelSeparatorIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheelSeparatorVertical, g_uiSettings->m_settingWheelSeparatorVertical.m_iconCenter, g_uiSettings->m_settingWheelSeparatorVertical.m_iconSize));

  }


  void WheelSeparatorManagerVertical::update(pc::assets::ImageOverlays* f_imageOverlays, const core::CustomFramework* /*f_framework*/)
  {
    // ! check if config has changed
    if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
    {
      init(f_imageOverlays);
      m_lastConfigUpdate = g_uiSettings->getModifiedCount();
    }

    // ! check the displayed view id
    m_wheelSeparatorIcons.getIcon(static_cast<vfc::uint32_t>(WheelSeparatorVerticalType::WHEELSEPARATOR_VERTICAL))->setEnabled(true);

  }


  //!
  //! @brief Construct a new -:: WheelSeparator object
  //!
  //! @param f_customFramework
  //! @param f_assetId
  //! @param f_view
  //!
  WheelSeparatorVertical::WheelSeparatorVertical(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
    : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}	// PRQA S 2966 
    , m_customFramework{f_customFramework}
    , m_manager{}
  {
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

    //! render order
    //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
    constexpr vfc::uint32_t l_renderOrder = 10u;
    cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder);

  }


  WheelSeparatorVertical::~WheelSeparatorVertical() = default;


  void WheelSeparatorVertical::traverse(osg::NodeVisitor& f_nv)
  {
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
      m_manager.update(this, m_customFramework);
    }
    pc::assets::ImageOverlays::traverse(f_nv);
  }
} // namespace wheelseparatorvertical

} // namespace uielements
} // namespace assets
} // namespace cc 
 
