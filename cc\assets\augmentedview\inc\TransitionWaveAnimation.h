//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_AUGMENTEDVIEWTRANSITION_SRC_TRANSITIONWAVEANIMATION_H_
#define CC_ASSETS_AUGMENTEDVIEWTRANSITION_SRC_TRANSITIONWAVEANIMATION_H_

#include <cassert>

namespace cc {
namespace assets {


//!
//! AugmentedViewTransitionData
//!
class AugmentedViewTransitionData : public pc::util::coding::ISerializable
{
public:
  AugmentedViewTransitionData()
  : m_backgroundTextureFilename("cc/resources/ui/131_black_pixel.png")
  , m_waveTextureFilename("cc/vehicle_model/ui/132_waveTexture.png")
  , m_transitionWaveAnimationDuration(0.5f)
  , m_transitionWaveMaxRadius(5.0f)
  , m_transitionWaveWidth(0.4f)
  , m_scanWaveAnimationDuration(4.0f)
  , m_scanWaveMaxRadius(2.5f)
  , m_scanWaveWidth(0.15f)
  , m_scanWaveBurstSize(1.0f)
  , m_scanWaveInterBurstTime(1.9f)
  , m_scanWaveIntraBurstTime(1.0f)
  , m_scanWaveTravelStartingPoint(0.5f)
  {
  }

  SERIALIZABLE(AugmentedViewTransitionData)
  {
    ADD_STRING_MEMBER(backgroundTextureFilename);
    ADD_STRING_MEMBER(waveTextureFilename);
    ADD_FLOAT_MEMBER(transitionWaveAnimationDuration);
    ADD_FLOAT_MEMBER(transitionWaveMaxRadius);
    ADD_FLOAT_MEMBER(transitionWaveWidth);
    ADD_FLOAT_MEMBER(scanWaveAnimationDuration);
    ADD_FLOAT_MEMBER(scanWaveMaxRadius);
    ADD_FLOAT_MEMBER(scanWaveBurstSize);
    ADD_FLOAT_MEMBER(scanWaveWidth);
    ADD_FLOAT_MEMBER(scanWaveInterBurstTime);
    ADD_FLOAT_MEMBER(scanWaveIntraBurstTime);
    ADD_FLOAT_MEMBER(scanWaveTravelStartingPoint);
  }

  std::string m_backgroundTextureFilename;
  std::string m_waveTextureFilename;

  float m_transitionWaveAnimationDuration;
  float m_transitionWaveMaxRadius;   //!< Max. radius of final expansion
  float m_transitionWaveWidth;       //!< The "width" of the radar wave

  float m_scanWaveAnimationDuration; //!< Duration of a scan-wave (periodically emitted wave in augmented view)
  float m_scanWaveMaxRadius;         //!< Max. radius of final expansion
  float m_scanWaveWidth;             //!< The "width" of each scan-wave (in meters)
  float m_scanWaveBurstSize;         //!< Number of waves emitted in direct succession
  float m_scanWaveInterBurstTime;    //!< Delay between two bursts
  float m_scanWaveIntraBurstTime;    //!< Delay between two bursts
  float m_scanWaveTravelStartingPoint;  //!< percentage of travel distance
};

extern pc::util::coding::Item<AugmentedViewTransitionData> g_settings;

class TransitionWaveAnimation {
public:
  TransitionWaveAnimation()
  {
    reset(2.0f, 40.0f, true);
  }

  TransitionWaveAnimation(float f_animationDuration, float f_maxRadius, bool f_transitionToAugmentedView)
  {
    reset(f_animationDuration, f_maxRadius, f_transitionToAugmentedView);
  }

  //! Reset animation to non-running state
  void reset(float f_animationDuration, float f_maxRadius, bool f_transitionToAugmentedView)
  {
    m_animationDuration = f_animationDuration;
    m_maxWaveRadius = f_maxRadius;
    m_transitionToAugmentedView = f_transitionToAugmentedView;

    // Non-exposed parameters for now
    m_waveAlphaFadeStartDistance = m_maxWaveRadius * 0.5f;
    m_waveAlphaFadeEndDistance = m_maxWaveRadius * 0.8f;

    m_cameraFadeStartTime = 0.2f * f_animationDuration;
    m_cameraFadeEndTime = 0.65f * f_animationDuration;
    reset();
  }

  //! Reset animation to non-running state
  void reset()
  {
    // Give parameters a value which should be correct in most cases (depending on animation parameters)
    // this is just a defensive measure, because parameters should usually not be used before an update
    m_waveRadius       = m_transitionToAugmentedView ? 0.0f : m_maxWaveRadius;
    m_cameraFadeFactor = m_transitionToAugmentedView ? 0.0f : 1.0f;
    m_waveAlpha        = m_transitionToAugmentedView ? 1.0f : 0.0f;

    m_elapsedTime = 0.0f;
    m_lastTime    = -1.0f; // -1.0 signifies not updated

    m_running = false;
  }

  void start(float f_animationDuration, float f_maxRadius, bool f_transitionToAugmentedView)
  {
    reset(f_animationDuration, f_maxRadius, f_transitionToAugmentedView);
    start();
  }

  void start()
  {
    m_running = true;
  }

  void update(float f_currentTime)
  {
    if (m_lastTime < 0.0f)
    {
      m_lastTime = f_currentTime;
    }

    m_elapsedTime = f_currentTime - m_lastTime;

    m_elapsedTime += m_animationDuration*g_settings->m_scanWaveTravelStartingPoint;
    
    float r = std::min(1.0f, m_elapsedTime/m_animationDuration);
    if (!m_transitionToAugmentedView)
    {
      r = 1.0f - r;
    }
    
    if (r < g_settings->m_scanWaveTravelStartingPoint)
    {
      r = g_settings->m_scanWaveTravelStartingPoint;
    }
    
    r*= r;
    m_waveRadius = m_maxWaveRadius*r;
    m_waveAlpha = 1.0f - std::max(0.f, (m_waveRadius - m_waveAlphaFadeStartDistance)/(m_waveAlphaFadeEndDistance - m_waveAlphaFadeStartDistance));

    // Fade factor based on elapsed time; might require tweaking so that both transition directions look similar
    // ...or switch to a radius based function
    m_cameraFadeFactor =  std::min(1.f, std::max(0.f, (m_elapsedTime-m_cameraFadeStartTime)/(m_cameraFadeEndTime-m_cameraFadeStartTime)));
    if (!m_transitionToAugmentedView)
    {
      m_cameraFadeFactor = 1.0f - m_cameraFadeFactor;
    }


    if (m_elapsedTime >= m_animationDuration)
    {
      m_running = false;
    }
  }

  bool isRunning() const
  {
    return m_running;
  }

  bool hasFinished() const
  {
    return (m_elapsedTime >= m_animationDuration);
  }

  float getWaveRadius() const
  {
    assert(m_lastTime >= 0.0f);
    return m_waveRadius;
  }

  float getWaveAlphaFade() const
  {
    assert(m_lastTime >= 0.0f);
    return m_waveAlpha;
  }

  float getCameraFadeFactor() const
  {
    assert(m_lastTime >= 0.0f);
    return m_cameraFadeFactor;
  }

  bool isTransitioningToAugmentedView() const
  {
    return m_transitionToAugmentedView;
  }
private:
  bool  m_running;
  float m_elapsedTime;

  // Animated values
  float m_waveRadius;                 //!< Current wave radius
  float m_waveAlpha;                  //!< Fade-factor for wave
  float m_cameraFadeFactor;           //!< 0 == fully textured, 1 == fully black

  // Animation parameters
  float m_lastTime;                   //!< Time of last update
  float m_maxWaveRadius;              //!< Final radius at end of animation
  float m_animationDuration;          //!< Total duration of animation
  float m_cameraFadeStartTime;        //!< Start of camera fade
  float m_cameraFadeEndTime;          //!< End of camera fade
  float m_waveAlphaFadeStartDistance; //!< Start distance of additive radar wave fade
  float m_waveAlphaFadeEndDistance;   //!< End distance of additive radar wave fade
  bool  m_transitionToAugmentedView;  //!< True iff transitioning to augmented view, false when transitioning to camera view
};

}
}

#endif

