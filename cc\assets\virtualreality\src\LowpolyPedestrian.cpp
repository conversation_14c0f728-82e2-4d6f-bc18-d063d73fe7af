//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  LowpolyPedestrian.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/LowpolyPedestrian.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"

#include <osgDB/ReadFile>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace virtualreality
{

pc::util::coding::Item<LowpolyPedestrianSettings> g_lowpolyPestrianSetting("LowpolyPedestrian");

//!
//! \brief Custom implementation of OSG's StateSet getOrCreateUniform method
//! which allows setting the override value
//!
static osg::Uniform* getOrCreateUniform(
  osg::StateSet* f_stateSet,
  const std::string& f_uniformName,
  osg::Uniform::Type f_type,
  osg::StateAttribute::OverrideValue f_value)
{
  osg::Uniform* l_uniform = f_stateSet->getUniform(f_uniformName);
  if (l_uniform && (l_uniform->getType() == f_type))
  {
    return l_uniform;
  }
  l_uniform = new osg::Uniform(f_type, f_uniformName);
  f_stateSet->addUniform(l_uniform, f_value);
  return l_uniform;
}

LowpolyPedestrian::LowpolyPedestrian()
: VirtualRealityObject{}
, m_isCritical{false}
, m_isColorUpdated{true}
, m_asset{nullptr}
{
  setNumChildrenRequiringUpdateTraversal(1u);
  this->addObjectNode();
}

LowpolyPedestrian::LowpolyPedestrian(const LowpolyPedestrian& f_other, const osg::CopyOp& f_copyOp)
  : VirtualRealityObject{f_other, f_copyOp}
{
}

LowpolyPedestrian::~LowpolyPedestrian() = default;



void LowpolyPedestrian::addObjectNode()
{
  const osg::ref_ptr<osg::Node> l_pedestrian = osgDB::readNodeFile(g_lowpolyPestrianSetting->m_pedestrianModelFilename);
  if (l_pedestrian.valid())
  {
    m_asset = l_pedestrian;
    
    osg::StateSet* const l_stateSet = l_pedestrian->getOrCreateStateSet();
    osg::Uniform *const l_diffuseColorUniform = l_stateSet->getOrCreateUniform("diffuseColor", osg::Uniform::FLOAT_VEC4);
    l_diffuseColorUniform->set(g_lowpolyPestrianSetting->m_colorNormal); // PRQA S 3803
    osg::Group::addChild(l_pedestrian.get()); // PRQA S 3803
    
  }
}

void LowpolyPedestrian::updateObjectNode()
{
    if (getIsCritical() && (m_isColorUpdated == false))
    {
      osg::StateSet* const l_stateSet = m_asset->getOrCreateStateSet();
      osg::Uniform *const l_diffuseColorUniform = l_stateSet->getOrCreateUniform("diffuseColor", osg::Uniform::FLOAT_VEC4);
      l_diffuseColorUniform->set(g_lowpolyPestrianSetting->m_colorCritical); // PRQA S 3803
      m_isColorUpdated = true;
    }
    else if ( !getIsCritical() && (m_isColorUpdated == false))
    {
      osg::StateSet* const l_stateSet = m_asset->getOrCreateStateSet();
      osg::Uniform *const l_diffuseColorUniform = l_stateSet->getOrCreateUniform("diffuseColor", osg::Uniform::FLOAT_VEC4);
      l_diffuseColorUniform->set(g_lowpolyPestrianSetting->m_colorNormal); // PRQA S 3803
      m_isColorUpdated = true;
    }
    else
    {
      // do nothing
    }

}

} // namespace virtualreality
} // namespace assets
} // namespace cc
