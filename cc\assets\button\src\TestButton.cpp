//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/TestButton.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_manager.h"

namespace cc
{
namespace assets
{
namespace button
{

class TestButtonSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(TestButtonSettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
        return;
        }
        ADD_MEMBER(ButtonTextureSettings, exampleTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_MEMBER(osg::Vec2f, vertPos);
    }

    ButtonTextureSettings m_exampleTexture;
    osg::Vec2f m_horiPos{0.0f, 100.0f};
    osg::Vec2f m_vertPos{100.0f, 0.0f};
};

static pc::util::coding::Item<TestButtonSettings> g_settings("TestButton");


TestButton::TestButton( cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
{
    setName("TestButton");
    setIconAtMiddle(true);
    setPositionHori(g_settings->m_horiPos);
    setPositionVert(g_settings->m_vertPos);
}

void TestButton::onInvalid()
{
    setIconEnable(false);
}

void TestButton::onUnavailable()
{
    setIconEnable(true);
    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    const ButtonTexturePath& settings = (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY) ? g_settings->m_exampleTexture.m_day : g_settings->m_exampleTexture.m_night;
    setTexturePath(settings.m_UnavailableTexturePath);
}

void TestButton::onAvailable()
{
    setIconEnable(true);
    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    const ButtonTexturePath& settings = (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY) ? g_settings->m_exampleTexture.m_day : g_settings->m_exampleTexture.m_night;
    setTexturePath(settings.m_AvailableTexturePath);
}

void TestButton::onPressed()
{
    setIconEnable(true);
    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    const ButtonTexturePath& settings = (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY) ? g_settings->m_exampleTexture.m_day : g_settings->m_exampleTexture.m_night;
    setTexturePath(settings.m_PressedTexturePath);
}

void TestButton::onReleased()
{
    setIconEnable(true);
    XLOG_INFO(g_AppContext, getName() << " button pressed");
    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    const ButtonTexturePath& settings = (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY) ? g_settings->m_exampleTexture.m_day : g_settings->m_exampleTexture.m_night;
    setTexturePath(settings.m_ReleasedTexturePath);
}

void TestButton::update()
{
    setSettingModifiedCount(g_settings->getModifiedCount());
    GET_PORT_DATA(touchStatusContainer,       m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)
    GET_PORT_DATA(huRotateStatusContainer,    m_framework->asCustomFramework()->m_SVSRotateStatusDaddy_Receiver, huRotateStatusPortHaveData)
    GET_PORT_DATA(hmiDataContainer,           m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)
    GET_PORT_DATA(dayNightThemeContainer,     m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)

    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    if (huRotateStatusPortHaveData)
    {
        setRotateTheme(static_cast<cc::target::common::EThemeTypeHU>(huRotateStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

    if (touchStatusPortHaveData)
    {
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    setPositionHori(g_settings->m_horiPos);
    setPositionVert(g_settings->m_vertPos);

    const bool buttonAvailable = IMGUI_GET_CHECKBOX_BOOL("Buttons", getName() + "_TestButtonAvailable");

    const bool touchInsideResponseArea = checkTouchInsideResponseArea();
    const TouchStatus touchSts = touchStatus();
    const ButtonState previousState = getState();
    ButtonState newState = UNAVAILABLE;

    if (buttonAvailable)
    {
        newState = AVAILABLE; // PRQA S 2880
    }
    if (newState == AVAILABLE && (touchSts == TOUCH_DOWN || touchSts == TOUCH_MOVE) && touchInsideResponseArea)
    {
        newState = PRESSED; // PRQA S 2880
    }
    if (touchStatusChanged && touchSts == TOUCH_UP && previousState == PRESSED && touchInsideResponseArea)
    {
        newState = RELEASED;
    }
    setState(newState);

    IMGUI_LOG("Buttons", getName() + "_State",
        getState() == RELEASED ? "RELEASED" :
        getState() == PRESSED ? "PRESSED" :
        getState() == AVAILABLE ? "AVAILABLE" :
        getState() == UNAVAILABLE ? "UNAVAILABLE" : "INVALID"
    );
    IMGUI_LOG("Buttons", getName() + "_TouchStatus",
        touchSts == TOUCH_UP ? "UP" :
        touchSts == TOUCH_DOWN ? "DOWN" :
        touchSts == TOUCH_MOVE ? "MOVE" : "INVALID"
    );
    IMGUI_LOG("Buttons", getName() + "_TouchInsideResponseArea", touchInsideResponseArea);
    IMGUI_LOG("Buttons", getName() + "_RotateStatus",
        getRotateTheme() == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI ? "Horizontal" :
        getRotateTheme() == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT ? "Vertical" : "None"
    );
    IMGUI_LOG("Buttons", getName() + "_DayNightTheme",
        getDayNightTheme() == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY ? "Day" :
        getDayNightTheme() == cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT ? "Night" :
        getDayNightTheme() == cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_RESERVE ? "Reserved" : "Invalid"
    );
}

} // namespace button
} // namespace assets
} // namespace cc // PRQA S 1041
