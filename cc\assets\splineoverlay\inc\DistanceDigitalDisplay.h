//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DistanceDigitalDisplay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_DISTANCE_DIGITAL_DISPLAY_H
#define CC_ASSETS_DISTANCE_DIGITAL_DISPLAY_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "pc/svs/views/engineeringview/inc/EngineeringView.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace distancedigitaldisplay
{


enum UssSectorId : unsigned int
{
  FRONT_LEFT = 1u,
  FRONT_MIDDLE_LEFT = 0u,
  FRONT_MIDDLE_RIGHT = 15u,
  FRONT_RIGHT = 14u,
  REAR_LEFT = 6u,
  REAR_MIDDLE_LEFT = 7u,
  REAR_MIDDLE_RIGHT = 8u,
  REAR_RIGHT = 9u
};


struct DistanceDigitalDisplayProperties
{
  DistanceDigitalDisplayProperties()
    : m_x(0.0f)
    , m_y(0.0f)
    , m_orientation(0.0f)
  {}

  float m_x;                  // inner point x coordinate
  float m_y;                  // inner point y coordinate
  float m_orientation;        // use to display text orientation
};

const float g_defaultDistance = 150.0f;
const int   g_defaultIndex    = 100;

//======================================================
// DistanceDigitalDisplaySettings
//------------------------------------------------------
/// Setting class for distancedigitaldisplay
/// <AUTHOR>
//======================================================
class DistanceDigitalDisplaySettings : public pc::util::coding::ISerializable
{
public:
  DistanceDigitalDisplaySettings()
    : m_offset_front(osg::Vec3f(1.5f, 0.0f, 0.0f))
    , m_offset_front_parking(osg::Vec3f(1.5f, 0.0f, 0.0f))
    , m_offset_rear(osg::Vec3f(1.4f, 0.0f, 0.0f))
    , m_offset_rear_parking(osg::Vec3f(1.4f, 0.0f, 0.0f))
    , m_fontType("cc/resources/Roboto-Regular.ttf")
    , m_stopTextTexture("cc/resources/stop.png")
    , m_fixedFrontPosition(6.0f)
    , m_fixedRearPosition(-6.0f)
    , m_fixedFrontPosition_vert_parking(6.0f)
    , m_fixedRearPosition_vert_parking(-6.0f)
    , m_stopTextFrontPosition(osg::Vec2f{5.506f, -0.64f})
    , m_stopTextRearPosition(osg::Vec2f{-3.418f, -0.64f})
    , m_stopTextFrontPosition_vert_parking(osg::Vec2f{5.506f, -0.64f})
    , m_stopTextRearPosition_vert_parking(osg::Vec2f{-3.418f, -0.64f})
    , m_textOffsetPercentageFront(4.7f)
    , m_textOffsetPercentageRear(4.7f)
  {
  }

  SERIALIZABLE(DistanceDigitalDisplaySettings)
  {
    ADD_MEMBER(osg::Vec3f, offset_front);
    ADD_MEMBER(osg::Vec3f, offset_front_parking);
    ADD_MEMBER(osg::Vec3f, offset_rear);
    ADD_MEMBER(osg::Vec3f, offset_rear_parking);
    ADD_STRING_MEMBER(fontType);
    ADD_STRING_MEMBER(stopTextTexture);
    ADD_FLOAT_MEMBER(fixedFrontPosition);
    ADD_FLOAT_MEMBER(fixedRearPosition);
    ADD_FLOAT_MEMBER(fixedFrontPosition_vert_parking);
    ADD_FLOAT_MEMBER(fixedRearPosition_vert_parking);
    ADD_MEMBER(osg::Vec2f, stopTextFrontPosition);
    ADD_MEMBER(osg::Vec2f, stopTextRearPosition);
    ADD_MEMBER(osg::Vec2f, stopTextFrontPosition_vert_parking);
    ADD_MEMBER(osg::Vec2f, stopTextRearPosition_vert_parking);
    ADD_FLOAT_MEMBER(textOffsetPercentageFront);
    ADD_FLOAT_MEMBER(textOffsetPercentageRear);
  }
  osg::Vec3f m_offset_front;
  osg::Vec3f m_offset_front_parking;
  osg::Vec3f m_offset_rear;
  osg::Vec3f m_offset_rear_parking;
  std::string  m_fontType;
  std::string  m_stopTextTexture;
  float m_fixedFrontPosition;
  float m_fixedRearPosition;
  float m_fixedFrontPosition_vert_parking;
  float m_fixedRearPosition_vert_parking;
  osg::Vec2f m_stopTextFrontPosition;
  osg::Vec2f m_stopTextRearPosition;
  osg::Vec2f m_stopTextFrontPosition_vert_parking;
  osg::Vec2f m_stopTextRearPosition_vert_parking;
  float m_textOffsetPercentageFront; // percentage of distance based on unit vector
  float m_textOffsetPercentageRear; // percentage of distance based on unit vector
};

extern pc::util::coding::Item<DistanceDigitalDisplaySettings> g_displaySettings;

class DistanceDigitalDisplayUpdateCallback: public osg::NodeCallback
{

public:
  DistanceDigitalDisplayUpdateCallback(
    osg::ref_ptr<osg::Geode> f_FrontSectorDisGeode, 
    osg::ref_ptr<osg::Geode> f_RearSectorDisGeode,
    osg::ref_ptr<osg::Geode> f_FrontSectorWarnImageGeode,
    osg::ref_ptr<osg::Geode> f_RearSectorWarnImageGeode,
    pc::core::Framework* f_pFramework
  );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;
  
  void updatefrontsector(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode, osg::ref_ptr<osg::Geode> f_imgGeode);
  void updaterearsector(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode, osg::ref_ptr<osg::Geode> f_imgGeode);

  float getFrontSectorShortestDis();
  float getRearSectorShortestDis();

  bool checkDistanceThreshL0(const float& f_distance, const unsigned int& f_index);
  bool checkDistanceThreshL1(const float& f_distance, const unsigned int& f_index) const;
  bool checkDistanceThreshL2(const float& f_distance, const unsigned int& f_index) const;
  bool checkDistanceThreshL3(const float& f_distance, const unsigned int& f_index) const;

protected:
  
  virtual ~DistanceDigitalDisplayUpdateCallback() = default;
  bool updateOrientation();

private:
  void checkSectorMovingStatus(bool& f_sector, float& f_previousSector, float f_distance, const float& f_upperThresh, const float& f_lowerThresh);
  void calculateShortestDistance();
  void compareFrontDistance(const float& f_distance, const bool& f_isMoving, const int f_index);
  void compareRearDistance(const float& f_distance, const bool& f_isMoving, const int f_index);
  float getMaxDistanceByIndex(const unsigned int f_index);
  bool getMovingStatusByIndex(const unsigned int f_index) const;
  bool isVerticalParking();

  //! Copy constructor is not permitted.
  DistanceDigitalDisplayUpdateCallback (const DistanceDigitalDisplayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  DistanceDigitalDisplayUpdateCallback& operator=(const DistanceDigitalDisplayUpdateCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_FrontSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_RearSectorDisGeode;
  osg::ref_ptr<osg::Geode> m_FrontSectorWarnImageGeode;
  osg::ref_ptr<osg::Geode> m_RearSectorWarnImageGeode;

  bool       m_IsObjMovingL1_Sector_0;
  bool       m_IsObjMovingL1_Sector_1;
  bool       m_IsObjMovingL1_Sector_14;
  bool       m_IsObjMovingL1_Sector_15;
  bool       m_IsObjMovingL1_Sector_6;
  bool       m_IsObjMovingL1_Sector_7;
  bool       m_IsObjMovingL1_Sector_8;
  bool       m_IsObjMovingL1_Sector_9;

  float      m_previousDistanceL1_Sector_0;
  float      m_previousDistanceL1_Sector_1;
  float      m_previousDistanceL1_Sector_14;
  float      m_previousDistanceL1_Sector_15;
  float      m_previousDistanceL1_Sector_6;
  float      m_previousDistanceL1_Sector_7;
  float      m_previousDistanceL1_Sector_8;
  float      m_previousDistanceL1_Sector_9;
  
  bool       m_IsObjMovingL3_Sector_0;
  bool       m_IsObjMovingL3_Sector_1;
  bool       m_IsObjMovingL3_Sector_14;
  bool       m_IsObjMovingL3_Sector_15;
  bool       m_IsObjMovingL3_Sector_6;
  bool       m_IsObjMovingL3_Sector_7;
  bool       m_IsObjMovingL3_Sector_8;
  bool       m_IsObjMovingL3_Sector_9;

  float      m_previousDistanceL3_Sector_0;
  float      m_previousDistanceL3_Sector_1;
  float      m_previousDistanceL3_Sector_14;
  float      m_previousDistanceL3_Sector_15;
  float      m_previousDistanceL3_Sector_6;
  float      m_previousDistanceL3_Sector_7;
  float      m_previousDistanceL3_Sector_8;
  float      m_previousDistanceL3_Sector_9;

  
  bool       m_IsObjMovingL2_Sector_0;
  bool       m_IsObjMovingL2_Sector_15;
  bool       m_IsObjMovingL2_Sector_7;
  bool       m_IsObjMovingL2_Sector_8;
  
  float      m_previousDistanceL2_Sector_0;
  float      m_previousDistanceL2_Sector_15;
  float      m_previousDistanceL2_Sector_7;
  float      m_previousDistanceL2_Sector_8;

  float m_frontSectorShortestDis; // meter
  int m_frontSectorShortestDisIndex;

  float m_rearSectorShortestDis; // meter
  int m_rearSectorShortestDisIndex;

  bool m_initialized;

  // middle point x, middle point y, middle point orientation
  std::vector<DistanceDigitalDisplayProperties>     m_textPositions;
  unsigned int                                      m_ussZoneNum;

  pc::core::Framework* m_pFramework;
};

//!
//! DigitalDistanceOverlay
//!
class DistanceDigitalDisplay : public osg::MatrixTransform
{
public:

    DistanceDigitalDisplay(pc::core::Framework* f_framework);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    virtual void init();
    bool updateNeeded();

    virtual ~DistanceDigitalDisplay();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_FrontSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_FrontSectorWarnImageGeode;
    osg::ref_ptr<osg::Geode> m_RearSectorDisGeode;
    osg::ref_ptr<osg::Geode> m_RearSectorWarnImageGeode;

private:
    //! Copy constructor is not permitted.
    DistanceDigitalDisplay (const DistanceDigitalDisplay& other); // = delete
    //! Copy assignment operator is not permitted.
    DistanceDigitalDisplay& operator=(const DistanceDigitalDisplay& other); // = delete

    EScreenID            m_screenID;
    osg::Vec2f           m_stopTextFrontPositionShow;
    osg::Vec2f           m_stopTextRearPositionShow;

};

inline osg::Texture2D* loadTexture(const std::string& f_filename);

void findBestFitLineCoefficient(const cc::assets::tileoverlay::TileUpdateVisitor::PointArray& f_interpolatePoints, float &f_orientation, unsigned int f_index, float& f_x, float& f_y);

bool checkCornerSector(const unsigned int& f_index);


} // namespace DistanceDigitalDisplay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_DISTANCE_DIGITAL_DISPLAY_H
