//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: YRN1WX Yang Rui (BCSC-EPA1)
//  Department: BCSC-EPA1
//=============================================================================
/// @swcomponent SVS BYD
/// @file  ParkingSlotFixed.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ParkingSlotFixed.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for parking modes
enum ParkingSlotType
{
  // parking slots
  PARKING_LEFT_SLOT_FIRST,
  PARKING_LEFT_SLOT_SECOND,
  PARKING_LEFT_SLOT_THIRD,
  PARKING_LEFT_SLOT_FOURTH,
  PARKING_RIGHT_SLOT_FIRST,
  PARKING_RIGHT_SLOT_SECOND,
  PARKING_RIGHT_SLOT_THIRD,
  PARKING_RIGHT_SLOT_FOURTH,
  //guideline
  GUIDELINE_VERTSLOT_REARIN_RIGHT,
  GUIDELINE_VERTSLOT_REARIN_LEFT,
  GUIDELINE_VERTSLOT_FRONTIN_RIGHT,
  GUIDELINE_VERTSLOT_FRONTIN_LEFT,
  GUIDELINE_PARASLOT_RIGHT,
  GUIDELINE_PARASLOT_LEFT,
  // selected slot
  SELECTED_VERTSLOT_REARIN_RIGHT,
  SELECTED_VERTSLOT_REARIN_LEFT,
  SELECTED_VERTSLOT_FRONTIN_RIGHT,
  SELECTED_VERTSLOT_FRONTIN_LEFT,
  SELECTED_PARASLOT_RIGHT,
  SELECTED_PARASLOT_LEFT,
  // combination of diagonal slots & guideline
  COMBINATION_DIAGSLOT_RIGHT,
  COMBINATION_DIAGSLOT_LEFT,
  // combination of diagonal slot & vehicle in complete
  COMBINATION_DIAGSLOT_REARIN_RIGHT,
  COMBINATION_DIAGSLOT_REARIN_LEFT,
  COMBINATION_DIAGSLOT_FRONTIN_RIGHT,
  COMBINATION_DIAGSLOT_FRONTIN_LEFT,
  // park out slot
  PARKING_OUT_COMBINATION_PARA_RIGHT,
  PARKING_OUT_COMBINATION_PARA_LEFT,
  PARKING_OUT_PARASLOT_RIGHT,
  PARKING_OUT_PARASLOT_LEFT,
  //vehicle icon
  PARKING_VEHICLE_PIC,
  PARKING_VEHICLE_PIC_LEFT,
  PARKING_VEHICLE_PIC_RIGHT,
  // parking type selection
  PARKING_SEARCHING_TEXT_SELECT_TYPE,
  PARKING_SEARCHING_BUTTON_FRONT_IN,
  PARKING_SEARCHING_BUTTON_REAR_IN,

};

pc::util::coding::Item<ParkingSlotFixedSettings> g_managerSettingsFixed("ParkingSlotFixed");

//!
//! @brief Construct a new ParkingSearching Manager:: ParkingSearching Manager object
//!
//! @param f_config
//!
ParkingSlotFixedManager::ParkingSlotFixedManager()
  : m_lastConfigUpdate(~0u)
  , m_ViewButtonParkInSlotsSelectingDispSts(cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE)
  , m_ViewButtonParkingInTypeDispSts(cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE)
{
}

// ! transfer to start from bottom left
osg::Vec2 ParkingSlotFixedManager::transferToBottomLeft(const osg::Vec2 f_iconPos)
{
  if (s_theme_fixed == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
  {
    return osg::Vec2(f_iconPos.y() + static_cast<vfc::float32_t>(cc::core::g_views->m_vertMainViewport.m_size.x()), f_iconPos.x());
  }
  else
  {
    return osg::Vec2(f_iconPos.x() - static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_origin.x()), static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.y()) - f_iconPos.y());
  }

}

ParkingSlotFixedManager::~ParkingSlotFixedManager()
{
}

void ParkingSlotFixedManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_settingParkSlot.clear(f_imageOverlays);
  // parking slots
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft,    g_uiSettings->m_settingHoriSearchingVerticalSlot1L.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft), g_uiSettings->m_settingHoriSearchingVerticalSlot1L.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft,    g_uiSettings->m_settingHoriSearchingVerticalSlot2L.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft), g_uiSettings->m_settingHoriSearchingVerticalSlot2L.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft,    g_uiSettings->m_settingHoriSearchingVerticalSlot3L.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft), g_uiSettings->m_settingHoriSearchingVerticalSlot3L.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft,    g_uiSettings->m_settingHoriSearchingVerticalSlot4L.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft), g_uiSettings->m_settingHoriSearchingVerticalSlot4L.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight,   g_uiSettings->m_settingHoriSearchingVerticalSlot1R.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight), g_uiSettings->m_settingHoriSearchingVerticalSlot1R.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight,   g_uiSettings->m_settingHoriSearchingVerticalSlot2R.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight), g_uiSettings->m_settingHoriSearchingVerticalSlot2R.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight,   g_uiSettings->m_settingHoriSearchingVerticalSlot3R.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight), g_uiSettings->m_settingHoriSearchingVerticalSlot3R.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight,   g_uiSettings->m_settingHoriSearchingVerticalSlot4R.m_iconCenter,      f_imageOverlays->getImageSize(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight), g_uiSettings->m_settingHoriSearchingVerticalSlot4R.m_isHoriScreen));
  // guideline
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriGuidelineVertRightRearIn,  g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,     g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconSize,     g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriGuidelineVertLeftRearIn,   g_uiSettings->m_settingHoriGuidelineVertLeftRearIn.m_iconCenter,      g_uiSettings->m_settingHoriGuidelineVertLeftRearIn.m_iconSize,      g_uiSettings->m_settingHoriGuidelineVertLeftRearIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriGuidelineVertRightFrontIn, g_uiSettings->m_settingHoriGuidelineVertRightFrontIn.m_iconCenter,    g_uiSettings->m_settingHoriGuidelineVertRightFrontIn.m_iconSize,    g_uiSettings->m_settingHoriGuidelineVertRightFrontIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriGuidelineVertLeftFrontIn,  g_uiSettings->m_settingHoriGuidelineVertLeftFrontIn.m_iconCenter,     g_uiSettings->m_settingHoriGuidelineVertLeftFrontIn.m_iconSize,     g_uiSettings->m_settingHoriGuidelineVertLeftFrontIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriGuidelineParaRight,        g_uiSettings->m_settingHoriGuidelineParaRight.m_iconCenter,           g_uiSettings->m_settingHoriGuidelineParaRight.m_iconSize,           g_uiSettings->m_settingHoriGuidelineParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriGuidelineParaLeft,         g_uiSettings->m_settingHoriGuidelineParaLeft.m_iconCenter,            g_uiSettings->m_settingHoriGuidelineParaLeft.m_iconSize,            g_uiSettings->m_settingHoriGuidelineParaLeft.m_isHoriScreen));
  // selected slot
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriSelectedSlotVertRight,     g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn.m_iconCenter,  g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn.m_iconSize,  g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriSelectedSlotVertLeft,      g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn.m_iconCenter,   g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn.m_iconSize,   g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriSelectedSlotVertRight,     g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn.m_iconCenter, g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn.m_iconSize, g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriSelectedSlotVertLeft,      g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn.m_iconCenter,  g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn.m_iconSize,  g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriSelectedSlotParaRight,     g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter,        g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconSize,        g_uiSettings->m_settingHoriSelectedSlotParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriSelectedSlotParaLeft,      g_uiSettings->m_settingHoriSelectedSlotParaLeft.m_iconCenter,         g_uiSettings->m_settingHoriSelectedSlotParaLeft.m_iconSize,         g_uiSettings->m_settingHoriSelectedSlotParaLeft.m_isHoriScreen));
  // combination
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCombinationDiagRightRearIn,g_uiSettings->m_settingHoriCombinationDiagRight.m_iconCenter,         g_uiSettings->m_settingHoriCombinationDiagRight.m_iconSize,         g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCombinationDiagLeftRearIn, g_uiSettings->m_settingHoriCombinationDiagLeft.m_iconCenter,          g_uiSettings->m_settingHoriCombinationDiagLeft.m_iconSize,          g_uiSettings->m_settingHoriCombinationDiagLeft.m_isHoriScreen));
  // combination of diagonal slot & vehicle in complete
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagRightRearIn,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconCenter,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconSize,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagLeftRearIn,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconCenter,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconSize,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagRightFrontIn, g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconCenter,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconSize,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagLeftFrontIn,  g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconCenter,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconSize,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_isHoriScreen));
  // park out slot
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriParkOutGuidanceCombinationParaRight, g_uiSettings->m_settingParkOutGuidanceCombinationParaRight.m_iconCenter,   g_uiSettings->m_settingParkOutGuidanceCombinationParaRight.m_iconSize,     g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationParaLeft,      g_uiSettings->m_settingParkOutGuidanceCombinationParaLeft.m_iconCenter,   g_uiSettings->m_settingParkOutGuidanceCombinationParaLeft.m_iconSize,     g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriParkOutSlotParaRight,                g_uiSettings->m_settingHoriParkOutSlotParaRight.m_iconCenter,   g_uiSettings->m_settingHoriParkOutSlotParaRight.m_iconSize,     g_uiSettings->m_settingHoriParkOutSlotParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutSlotParaLeft,                     g_uiSettings->m_settingParkOutSlotParaLeft.m_iconCenter,        g_uiSettings->m_settingParkOutSlotParaLeft.m_iconSize,     g_uiSettings->m_settingParkOutSlotParaLeft.m_isHoriScreen));
  //vehicle icon
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSmallParkAutoPic,              g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter,                 g_uiSettings->m_settingSmallParkAutoPic.m_iconSize,         g_uiSettings->m_settingSmallParkAutoPic.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSmallParkAutoPicLeft,          g_uiSettings->m_settingSmallParkAutoPicComplete.m_iconCenter,                 g_uiSettings->m_settingSmallParkAutoPicComplete.m_iconSize, g_uiSettings->m_settingSmallParkAutoPic.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSmallParkAutoPicRight,         g_uiSettings->m_settingSmallParkAutoPicComplete.m_iconCenter,                 g_uiSettings->m_settingSmallParkAutoPicComplete.m_iconSize, g_uiSettings->m_settingSmallParkAutoPic.m_isHoriScreen));
  // parking type selection
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSearchingTextBoxSelectType,  g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter,         g_uiSettings->m_settingParkingUITopTextWhite.m_iconSize, g_uiSettings->m_settingParkingUITopTextWhite.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSearchingRearInButton,       g_uiSettings->m_settingSuspendContinueButton.m_iconCenter,        g_uiSettings->m_settingSuspendContinueButton.m_iconSize, g_uiSettings->m_settingSuspendContinueButton.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSearchingFrontInButton,      g_uiSettings->m_settingSuspendQuitButton.m_iconCenter,            g_uiSettings->m_settingSuspendQuitButton.m_iconSize, g_uiSettings->m_settingSuspendQuitButton.m_isHoriScreen));
}

void ParkingSlotFixedManager::update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework)    // PRQA S 6043 // PRQA S 6040  // PRQA S 6041  // PRQA S 4213
{
  // ! check if config has changed
  // if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  // {
  //   init(f_imageOverlays);
  //   m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  // }
  m_settingParkSlot.setAllEnabled(false);
  this->CleanButtonDispSts();

  // ! read all the signals
  cc::target::common::EAPAPARKMODE                 l_curAPAPARKMODE          = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::EPARKStatusR2L               l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld              l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L            l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndExtR2L         l_curparkDriverIndExt     = cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest;
  cc::target::common::EPARKRecoverIndR2L           l_curparkSuspend          = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  cc::target::common::EPARKDriverIndSearchR2L      l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                         l_curFreeParkingActive    = false;
  cc::target::common::rbp_Type_ParkManeuverType_en l_curparkPSDirection      = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;
  cc::target::common::EPARKSideR2L                 l_curParkOutDirection     = cc::target::common::EPARKSideR2L::PARKSIDE_None;
  std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> l_curparkSpace;

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curAPAPARKMODE = l_parkAPAPARKMODE->m_Data;
  }

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndExtReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndExtDaddy_t* l_parkDriverIndExt = f_framework->m_parkHmiParkDriverIndExtReceiver.getData();
    l_curparkDriverIndExt = l_parkDriverIndExt->m_Data;
  }

  if (f_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
  {
    const cc::daddy::ParkRecoverIndDaddy_t* l_parkSuspend = f_framework->m_parkHmiParkingRecoverIndReceiver.getData();
    l_curparkSuspend = l_parkSuspend->m_Data;
  }

  if (f_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* l_APADriverReq_Search = f_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
     const cc::daddy::ParkFreeParkingActive_t* l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
     l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (f_framework->m_parkPSDirectionSelectedReceiver.hasData())
  {
    const cc::daddy::ParkPSDirectionSelected_t* l_parkPSDirection = f_framework->m_parkPSDirectionSelectedReceiver.getData();
    l_curparkPSDirection = l_parkPSDirection->m_Data;
  }

  if(f_framework->m_parkAPASlotsReceiver.hasData())
  {
    const cc::daddy::ParkAPA_ParkSpace_t *l_parkSpace = f_framework->m_parkAPASlotsReceiver.getData();
    for(vfc::uint16_t l_side = 0; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
      for(vfc::uint16_t l_numberPerside = 0; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
      {
        l_curparkSpace[l_side][l_numberPerside] = l_parkSpace->m_Data[l_side][l_numberPerside];
        // XLOG_INFO_OS(g_AppContext) << "!!! local variable l_curparkSpace has been updated "  << XLOG_ENDL;
      }
    }
  }

  if (f_framework->m_parkHmiParkingSideReceiver.hasData())
  {
    const cc::daddy::ParkSideDaddy_t* l_parkOutDirection = f_framework->m_parkHmiParkingSideReceiver.getData();
    l_curParkOutDirection = l_parkOutDirection->m_Data;
  }

  if (l_curAPAPARKMODE == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
  {
    switch (l_curparkStatus) // PRQA S 3139  #code looks fine
    {
    case cc::target::common::EPARKStatusR2L::PARK_Searching:
      switch (l_curparkDriverInd) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
        // This is a temp modification to handle the cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection as cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop to cover the state machine bug from APA.
        // Final solution needs to be defined later

        // m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setPosition(g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter,pc::assets::Icon::UnitType::Pixel);
        // m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setEnabled(true);
        // m_settingParkSlot.getIcon(PARKING_SEARCHING_TEXT_SELECT_TYPE)->setEnabled(true);
        // displayMixedSlots(l_curparkSpace);
        // m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
        // if ( isASlotSelected(l_curparkSpace) )
        // {
        //   SelectedSlotFixed l_selectedSlot = getSelectedSlot(l_curparkSpace);
        //   if (l_curparkSpace[l_selectedSlot.m_selectedSlotSide][l_selectedSlot.m_selectedSlotId].m_APA_ParkManeuverType == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBFwdIn_enm)
        //   {
        //     m_settingParkSlot.getIcon(PARKING_SEARCHING_BUTTON_FRONT_IN)->setEnabled(true);
        //     m_settingParkSlot.getIcon(PARKING_SEARCHING_BUTTON_REAR_IN)->setEnabled(true);
        //     m_ViewButtonParkingInTypeDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
        //   }
        // }
        //break;
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
        if (l_curFreeParkingActive == false && l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)
        {
          m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setPosition(g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter,pc::assets::Icon::UnitType::Pixel);
          m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setEnabled(true);
          displayMixedSlots(l_curparkSpace);
        }
        break;
      }
      break;
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
      switch (l_curParkngTypeSeld) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EParkngTypeSeld::PARKING_NONE:
        // do nothing
      break;
      case cc::target::common::EParkngTypeSeld::PARKING_IN:
        if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding || l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark)
        {
          SelectedSlotFixed l_selectedSlot = getSelectedSlot(l_curparkSpace);
          manageDisplayLogicInGuidance(l_selectedSlot, l_curparkPSDirection);
        }
        break;
      case cc::target::common::EParkngTypeSeld::PARKING_OUT:
        if ((l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding || l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark)
          && l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
        {
          m_settingParkSlot.getIcon(PARKING_OUT_COMBINATION_PARA_RIGHT)->setEnabled(true);
        }
        else if ((l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding || l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark)
          && l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
        {
          m_settingParkSlot.getIcon(PARKING_OUT_COMBINATION_PARA_LEFT)->setEnabled(true);
        }
        else
        {
          // do nothing
        }

        break;
      }
      break;
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend:
      if (l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand && l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN )
      {
        SelectedSlotFixed l_selectedSlot = getSelectedSlot(l_curparkSpace);
        manageDisplayLogicInPause(l_selectedSlot, l_curparkPSDirection);
      }
      else if (l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand && l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT
                && l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
      {
        displaySelectedSlot(PARKING_VEHICLE_PIC, PARKING_OUT_PARASLOT_RIGHT, g_uiSettings->m_settingParkOutSmallAuto.m_iconCenter);
      }
      else if (l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand && l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT
                && l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
      {
        displaySelectedSlot(PARKING_VEHICLE_PIC, PARKING_OUT_PARASLOT_LEFT, g_uiSettings->m_settingParkOutSmallAuto.m_iconCenter);
      }
      else
      {
        //do nothing
      }

      break;
    case cc::target::common::EPARKStatusR2L::PARK_Completed:
        switch (l_curParkngTypeSeld) // PRQA S 3139  #code looks fine
        {
        case cc::target::common::EParkngTypeSeld::PARKING_NONE:
          // do nothing
          break;
        case cc::target::common::EParkngTypeSeld::PARKING_IN:
          if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted)
          {
            SelectedSlotFixed l_selectedSlot = getSelectedSlot(l_curparkSpace);
            manageDisplayLogicInComplete(l_selectedSlot, l_curparkPSDirection);
          }
          break;
        case cc::target::common::EParkngTypeSeld::PARKING_OUT:
          if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted && l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
          {
            displaySelectedSlot(PARKING_VEHICLE_PIC, PARKING_OUT_PARASLOT_RIGHT, g_uiSettings->m_settingParkOutSmallAuto.m_iconCenter);
          }
          else if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted && l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
          {
            displaySelectedSlot(PARKING_VEHICLE_PIC, PARKING_OUT_PARASLOT_LEFT, g_uiSettings->m_settingParkOutSmallAuto.m_iconCenter);
          }
          else
          {
            // do nothing
          }

          break;
        }
      break;
    case  cc::target::common::EPARKStatusR2L::PARK_AssistStandby:
      switch (l_curParkngTypeSeld) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EParkngTypeSeld::PARKING_NONE:
        // do nothing
        break;
      case cc::target::common::EParkngTypeSeld::PARKING_IN:
        switch (l_curparkDriverInd) // PRQA S 3139  #code looks fine
        {
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot:
          m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setPosition(g_uiSettings->m_settingSmallSlotAutoPic.m_iconCenter,pc::assets::Icon::UnitType::Pixel);
          m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setEnabled(true);
          displayMixedSlots(l_curparkSpace);
          m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
          break;
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:
          m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setPosition(g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter,pc::assets::Icon::UnitType::Pixel);
          m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setEnabled(true);
          displayMixedSlots(l_curparkSpace);
          m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
          break;
        default:
          break;
        }
        break;
      case cc::target::common::EParkngTypeSeld::PARKING_OUT:
        // if (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM)          //disable since req changed from customer
        // {
        //   displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_PARASLOT_RIGHT, g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter);
        // }
        break;
      }
      break;
    default:
      break;
    }
  }

  this->DeliverButtonDispSts(f_framework);    // PRQA S 3804

}


void ParkingSlotFixedManager::clearIcon()
{
  m_settingParkSlot.setAllEnabled(false);
}


//!
//! @brief Construct a new HoriParkingSlotFixed:: HoriParkingSlotFixed object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
HoriParkingSlotFixed::HoriParkingSlotFixed(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays(f_assetId, nullptr)
  , m_customFramework(f_customFramework)
{
  pc::util::coding::CodingManager* l_codingManager = pc::util::coding::getCodingManager();
  cc::assets::uielements::UISettings* l_uiSettings = dynamic_cast<cc::assets::uielements::UISettings*>(l_codingManager->getItem("UIElements"));

  l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
  l_uiSettings->dirty();
  m_HoriManager.init(this);

  l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
  l_uiSettings->dirty();
  m_VertManager.init(this);

  m_pAutoPicCenter                 = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter                ));    // PRQA S 3066
  m_pParkOutAutoPicCenter          = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingParkOutSmallAuto.m_iconCenter                ));    // PRQA S 3066
  m_pSmallSlotAutoPicCenter        = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallSlotAutoPic.m_iconCenter                ));    // PRQA S 3066
  m_pSelectedSlotParaRightCenter   = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter       ));    // PRQA S 3066
  m_pAutoPicPara                   = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallAutoPicPara.m_iconCenter                ));    // PRQA S 3066
  m_pAutoPicVertRearInLeft         = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallAutoPicVertRearInLeft.m_iconCenter      ));    // PRQA S 3066
  m_pAutoPicVertRearInRight        = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallAutoPicVertRearInRight.m_iconCenter     ));    // PRQA S 3066
  m_pAutoPicVertFrontInLeft        = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallAutoPicVertFrontInLeft.m_iconCenter     ));    // PRQA S 3066
  m_pAutoPicVertFrontInRight       = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingSmallAutoPicVertFrontInRight.m_iconCenter    ));    // PRQA S 3066
  m_pSelectedSlotParaLeft          = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotParaLeft.m_iconCenter        ));    // PRQA S 3066
  m_pSelectedSlotParaRight         = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter       ));    // PRQA S 3066
  m_pSelectedSlotVertLeftRearIn    = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn.m_iconCenter  ));    // PRQA S 3066
  m_pSelectedSlotVertLeftFrontIn   = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn.m_iconCenter ));    // PRQA S 3066
  m_pSelectedSlotVertRightRearIn   = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn.m_iconCenter ));    // PRQA S 3066
  m_pSelectedSlotVertRightFrontIn  = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn.m_iconCenter));    // PRQA S 3066

  m_pParaSlotSize                  = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingParallelSlot.m_iconSize));    // PRQA S 3066
  m_pVertSlotSize                  = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingVerticalSlot.m_iconSize));    // PRQA S 3066
  m_pDiagSlotSize                  = const_cast<osg::Vec2*>(&(g_uiSettings->m_settingDiagonalSlot.m_iconSize));    // PRQA S 3066

  m_pParaSlotPoseX                 = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingParaSlotPoseX         ));    // PRQA S 3066
  m_pVertSlotPoseX                 = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingVertSlotPoseX         ));    // PRQA S 3066
  m_pCalculateLeftSlotPoseX        = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingCalculateLeftSlotPoseX));    // PRQA S 3066
  m_pSlotPositionY                 = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingSlotPositionY         ));    // PRQA S 3066
  m_pDistanceBetweenPP             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenPP     ));    // PRQA S 3066
  m_pDistanceBetweenPC             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenPC     ));    // PRQA S 3066
  m_pDistanceBetweenCC             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenCC     ));    // PRQA S 3066
  m_pDistanceBetweenDD             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenDD     ));    // PRQA S 3066
  m_pMaxHeight                     = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingMaxHeight             ));    // PRQA S 3066

  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


HoriParkingSlotFixed::~HoriParkingSlotFixed()
{
}


void HoriParkingSlotFixed::traverse(osg::NodeVisitor& f_nv)    // PRQA S 6043
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // ! update theme
    pc::util::coding::CodingManager* l_codingManager = pc::util::coding::getCodingManager();
  cc::assets::uielements::UISettings* l_uiSettings = dynamic_cast<cc::assets::uielements::UISettings*>(l_codingManager->getItem("UIElements"));
    if(m_customFramework->m_SVSRotateStatusDaddy_Receiver.hasData())
    {
      const cc::daddy::SVSRotateStatusDaddy_t* l_themeType = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
      cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data);    // PRQA S 3013

      if (l_curThemeType != s_theme_fixed)
      {
        XLOG_INFO_OS(g_AppContext) << "[THEME] UIElements start theme update" << XLOG_ENDL;

        if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
          {
            m_VertManager.clearIcon();
          }
          else if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
          {
            m_HoriManager.clearIcon();
          }
          else
          {
            m_VertManager.clearIcon();
          }

          s_theme_fixed = l_curThemeType;

        XLOG_INFO_OS(g_AppContext) << "[THEME] UIElements finish theme update" << XLOG_ENDL;
      }
      else
      {
        // Do nothing
      }
    }

    if (s_theme_fixed == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
    {
      m_VertManager.update(this, m_customFramework);
      *m_pAutoPicCenter                = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallParkAutoPic_Vert.m_iconCenter);
      *m_pParkOutAutoPicCenter         = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingParkOutSmallAuto_Vert.m_iconCenter);
      *m_pSmallSlotAutoPicCenter       = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallSlotAutoPic_Vert.m_iconCenter);
      *m_pSelectedSlotParaRightCenter  = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotParaRight_Vert.m_iconCenter);
      *m_pAutoPicPara                  = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicPara_Vert.m_iconCenter);
      *m_pAutoPicVertRearInLeft        = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertRearInLeft_Vert.m_iconCenter);
      *m_pAutoPicVertRearInRight       = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertRearInRight_Vert.m_iconCenter);
      *m_pAutoPicVertFrontInLeft       = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertFrontInLeft_Vert.m_iconCenter);
      *m_pAutoPicVertFrontInRight      = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertFrontInRight_Vert.m_iconCenter);
      *m_pSelectedSlotParaLeft         = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotParaLeft_Vert.m_iconCenter);
      *m_pSelectedSlotParaRight        = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotParaRight_Vert.m_iconCenter);
      *m_pSelectedSlotVertLeftRearIn   = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn_Vert.m_iconCenter);
      *m_pSelectedSlotVertLeftFrontIn  = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn_Vert.m_iconCenter);
      *m_pSelectedSlotVertRightRearIn  = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn_Vert.m_iconCenter);
      *m_pSelectedSlotVertRightFrontIn = l_uiSettings->transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn_Vert.m_iconCenter);

      *m_pParaSlotSize = l_uiSettings->getSlotImageSizeVert(g_uiSettings->m_texturePathHoriUnselectedSlotParaRight);
      *m_pVertSlotSize = l_uiSettings->getSlotImageSizeVert(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight);
      *m_pDiagSlotSize = l_uiSettings->getSlotImageSizeVert(g_uiSettings->m_texturePathHoriUnselectedSlotDiagRight);

      *m_pParaSlotPoseX          = 237;
      *m_pVertSlotPoseX          = 254;
      *m_pCalculateLeftSlotPoseX = static_cast<vfc::uint32_t>(cc::core::g_views->m_vertMainViewport.m_size.y());
      *m_pSlotPositionY          = 214;
      *m_pDistanceBetweenPP      = 118;
      *m_pDistanceBetweenPC      = 93;
      *m_pDistanceBetweenCC      = 63;
      *m_pDistanceBetweenDD      = 73;
      *m_pMaxHeight              = 455;
    }
    else
    {
      m_HoriManager.update(this, m_customFramework);
      *m_pAutoPicCenter                = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallParkAutoPic_Hori.m_iconCenter);
      *m_pParkOutAutoPicCenter         = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingParkOutSmallAuto_Hori.m_iconCenter);
      *m_pSmallSlotAutoPicCenter       = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallSlotAutoPic_Hori.m_iconCenter);
      *m_pSelectedSlotParaRightCenter  = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotParaRight_Hori.m_iconCenter);
      *m_pAutoPicPara                  = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicPara_Hori.m_iconCenter);
      *m_pAutoPicVertRearInLeft        = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertRearInLeft_Hori.m_iconCenter);
      *m_pAutoPicVertRearInRight       = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertRearInRight_Hori.m_iconCenter);
      *m_pAutoPicVertFrontInLeft       = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertFrontInLeft_Hori.m_iconCenter);
      *m_pAutoPicVertFrontInRight      = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertFrontInRight_Hori.m_iconCenter);
      *m_pSelectedSlotParaLeft         = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotParaLeft_Hori.m_iconCenter);
      *m_pSelectedSlotParaRight        = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotParaRight_Hori.m_iconCenter);
      *m_pSelectedSlotVertLeftRearIn   = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn_Hori.m_iconCenter);
      *m_pSelectedSlotVertLeftFrontIn  = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn_Hori.m_iconCenter);
      *m_pSelectedSlotVertRightRearIn  = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn_Hori.m_iconCenter);
      *m_pSelectedSlotVertRightFrontIn = l_uiSettings->transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn_Hori.m_iconCenter);

      *m_pParaSlotSize = l_uiSettings->getImageSizeHori(g_uiSettings->m_texturePathHoriUnselectedSlotParaRight);
      *m_pVertSlotSize = l_uiSettings->getImageSizeHori(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight);
      *m_pDiagSlotSize = l_uiSettings->getImageSizeHori(g_uiSettings->m_texturePathHoriUnselectedSlotDiagRight);

      *m_pParaSlotPoseX = 64;
      *m_pVertSlotPoseX = 91;
      *m_pCalculateLeftSlotPoseX = static_cast<vfc::uint32_t>(cc::core::g_views->m_planViewport.m_size.x());  //the width of ui view
      *m_pSlotPositionY          = 253;                                           // position of the first slot icon in y direction
      *m_pDistanceBetweenPP      = 146;                                           // distance in y direction between slot icons
      *m_pDistanceBetweenPC      = 115;
      *m_pDistanceBetweenCC      = 84;
      *m_pDistanceBetweenDD      = 95;
      *m_pMaxHeight              = 580;
    }
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}

vfc::uint8_t ParkingSlotFixedManager::getSlotType( EFAPAParkSlotType f_generalType, vfc::float32_t f_angle)
{
  vfc::uint8_t l_realSlotType = 255u;
  if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL)
  {
    l_realSlotType = 0u;        // parallel slot
    return l_realSlotType;
  }
  if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL)
  {
    l_realSlotType = 2u;        // diagonal slot
    return l_realSlotType;
  }
  else if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_CROSS &&
  isGreater(f_angle, g_managerSettingsFixed->m_parkingDiagSlotAngleLowerLimit) &&
  isLess(f_angle, g_managerSettingsFixed->m_parkingDiagSlotAngleUpperLimit))
  {
    l_realSlotType = 2u;        // diagonal slot
    return l_realSlotType;
  }
  else if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_CROSS )
  {
    l_realSlotType = 1u;        // cross slot
    return l_realSlotType;
  }
  return l_realSlotType;
}

void ParkingSlotFixedManager::setSlot(vfc::uint16_t f_slot, std::string f_textureSlot,const osg::Vec2& f_size,osg::Vec2 f_position,
                                        cc::target::common::EFAPAParkSlotType f_slotType, cc::daddy::ParkUISpotData_t& f_rParkSpotUIDataContainer)
{
  m_settingParkSlot.getIcon(f_slot)->setImage(f_textureSlot);
  m_settingParkSlot.getIcon(f_slot)->setSize(f_size, pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_slot)->setPosition(transferToBottomLeft(f_position),pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_slot)->setEnabled(true);
  // set slot selecting press center & response area
  if (s_theme_fixed == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
  {
    f_position.x() = ( f_position.x() * 582.0f / 432.0f ) + 228.0f;
    f_position.y() = f_position.y() * 972.0f / 720.0f;
  }
  else
  {
    // For Slots display in searching the height has same direction with y in HMI
    f_position.x() = f_position.x() * 1.5f;                // f_position.x() = f_position.x() * 1080 / 720;
    f_position.y() = ( f_position.y() * 1.5f ) + 696.0f;       // f_position.y() = ( f_position.y() * 852 / 568 ) + 696;
  }
  osg::Vec2 l_comResponseArea;
  if (f_slotType == cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL)
  {
    l_comResponseArea = g_uiSettings->m_settingPARKParaSlotValid.m_responseArea;
  }
  else if (f_slotType == cc::target::common::EFAPAParkSlotType::APASLOT_CROSS)
  {
    l_comResponseArea = g_uiSettings->m_settingPARKVertSlotValid.m_responseArea;
  }
  else if (f_slotType == cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL)
  {
    l_comResponseArea = g_uiSettings->m_settingPARKDiagSlotValid.m_responseArea;
  }

  switch (f_slot) // PRQA S 3139  #code looks fine
  {
  case 0u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_responseArea = l_comResponseArea;
    break;
  case 1u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_responseArea = l_comResponseArea;
    break;
  case 2u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_responseArea = l_comResponseArea;
    break;
  case 3u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_responseArea = l_comResponseArea;
    break;
  case 4u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_responseArea = l_comResponseArea;
    break;
  case 5u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_responseArea = l_comResponseArea;
    break;
  case 6u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_responseArea = l_comResponseArea;
    break;
  case 7u:
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_responseArea = l_comResponseArea;
    break;
  }
}

void ParkingSlotFixedManager::displayMixedSlots(std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curparkSpace)    // PRQA S 6040  // PRQA S 6041  // PRQA S 6043  // PRQA S 6044
{
  cc::daddy::ParkUISpotData_t& l_rParkSpotUIDataContainer =
            cc::daddy::CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort.reserve() ;

  //************************************both sides slots display handling***********************************************************
  vfc::uint32_t  l_maxHeight = g_uiSettings->m_parkingMaxHeight;    // upper limit to avoid the icon display outside the fixed area
  vfc::uint8_t   l_firstSlot = 0U;
  vfc::uint32_t  l_paraSlotPositionX = 0U;
  vfc::uint32_t  l_vertSlotPositionX = 0U;
  vfc::uint8_t   l_curSlotType = 0U;        // 0--Parallel 1--Cross 2--Diagonal
  vfc::uint8_t   l_lastSlotType = 0U;
  for(vfc::uint16_t l_side = 0U; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
  {
    vfc::uint32_t  l_height = g_uiSettings->m_parkingSlotPositionY;       // height of first slot position
    for (vfc::uint16_t l_numberPerside = 0U; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
    {
      if (f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE || f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED )
      {
      std::string l_textureParallelSlot, l_textureVerticalSlot, l_textureDiagonalSlot;
      if (l_side == 0U)
      {
        l_firstSlot = PARKING_LEFT_SLOT_FIRST;
        l_paraSlotPositionX = g_uiSettings->m_parkingParaSlotPoseX;       //position in x direction
        l_vertSlotPositionX = g_uiSettings->m_parkingVertSlotPoseX;
        if(f_curparkSpace[l_side][l_numberPerside].m_APA_PSId_u16 == 254U && l_numberPerside == 0U)
        {
          m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 254U;
          m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 254U;
          m_parkSpace_info[l_side][l_numberPerside].m_PS_Id = 254U;
        }
        else if(l_numberPerside == 0U)
        {
          if(f_curparkSpace[l_side][l_numberPerside].m_APA_PSId_u16 != m_parkSpace_info[l_side][l_numberPerside].m_PS_Id)
          {
            m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 0U;
            m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 0U;
          }
          if(m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag == 0U)
          {
            m_parkSpace_info[l_side][l_numberPerside].m_runnable_count++;
            if(m_parkSpace_info[l_side][l_numberPerside].m_runnable_count > g_managerSettingsFixed->m_parkingSlotflashDeplay)
            {
              m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 0U;
              m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 1U;
            }
          }
          if(m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag == 1U)
          {
            m_parkSpace_info[l_side][l_numberPerside].m_runnable_count++;
            if(m_parkSpace_info[l_side][l_numberPerside].m_runnable_count > g_managerSettingsFixed->m_parkingSlotflashDeplay)
            {
              m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 0U;
              m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 2U;
            }
          }
          m_parkSpace_info[l_side][l_numberPerside].m_PS_Id = f_curparkSpace[l_side][l_numberPerside].m_APA_PSId_u16;
        }
        if (f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE)
        {
          l_textureParallelSlot = g_uiSettings->m_texturePathHoriUnselectedSlotParaLeft;
          l_textureVerticalSlot = g_uiSettings->m_texturePathHoriUnselectedSlotVertLeft;
          l_textureDiagonalSlot = g_uiSettings->m_texturePathHoriUnselectedSlotDiagLeft;
        }
        else if (f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
        {
          l_textureParallelSlot = g_uiSettings->m_texturePathHoriSelectedSlotParaLeft;
          l_textureVerticalSlot = g_uiSettings->m_texturePathHoriSelectedSlotVertLeft;
          l_textureDiagonalSlot = g_uiSettings->m_texturePathHoriSelectedSlotDiagLeft;
        }

      }
      else if (l_side == 1U)
      {
        l_firstSlot = PARKING_RIGHT_SLOT_FIRST;
        l_paraSlotPositionX = g_uiSettings->m_parkingCalculateLeftSlotPoseX - g_uiSettings->m_parkingParaSlotPoseX;
        l_vertSlotPositionX = g_uiSettings->m_parkingCalculateLeftSlotPoseX - g_uiSettings->m_parkingVertSlotPoseX;
        if(f_curparkSpace[l_side][l_numberPerside].m_APA_PSId_u16 == 254U && l_numberPerside == 0U)
        {
          m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 254U;
          m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 254U;
          m_parkSpace_info[l_side][l_numberPerside].m_PS_Id = 254U;
        }
        else if(l_numberPerside == 0U)
        {
          if(f_curparkSpace[l_side][l_numberPerside].m_APA_PSId_u16 != m_parkSpace_info[l_side][l_numberPerside].m_PS_Id)
          {
            m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 0U;
            m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 0U;
          }
          if(m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag == 0U)
          {
            m_parkSpace_info[l_side][l_numberPerside].m_runnable_count++;
            if(m_parkSpace_info[l_side][l_numberPerside].m_runnable_count > g_managerSettingsFixed->m_parkingSlotflashDeplay)
            {
              m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 0U;
              m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 1U;
            }
          }
          if(m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag == 1U)
          {
            m_parkSpace_info[l_side][l_numberPerside].m_runnable_count++;
            if(m_parkSpace_info[l_side][l_numberPerside].m_runnable_count > g_managerSettingsFixed->m_parkingSlotflashDeplay)
            {
              m_parkSpace_info[l_side][l_numberPerside].m_runnable_count = 0U;
              m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag = 2U;
            }
          }
          m_parkSpace_info[l_side][l_numberPerside].m_PS_Id = f_curparkSpace[l_side][l_numberPerside].m_APA_PSId_u16;
        }
        if (f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE)
        {
          l_textureParallelSlot = g_uiSettings->m_texturePathHoriUnselectedSlotParaRight;
          l_textureVerticalSlot = g_uiSettings->m_texturePathHoriUnselectedSlotVertRight;
          l_textureDiagonalSlot = g_uiSettings->m_texturePathHoriUnselectedSlotDiagRight;
        }
        else if (f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
        {
          l_textureParallelSlot = g_uiSettings->m_texturePathHoriSelectedSlotParaRight;
          l_textureVerticalSlot = g_uiSettings->m_texturePathHoriSelectedSlotVertRight;
          l_textureDiagonalSlot = g_uiSettings->m_texturePathHoriSelectedSlotDiagRight;
        }
      }
      // display the first slot per side individually
      if (l_numberPerside == 0U)
      {
        l_curSlotType = getSlotType( f_curparkSpace[l_side][0U].m_APA_PSType, f_curparkSpace[l_side][0U].m_APA_PrkgSlotSta_f32);
        if (l_curSlotType == 0u && m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag != 1U)
        {
          setSlot(l_firstSlot, l_textureParallelSlot, g_uiSettings->m_settingParallelSlot.m_iconSize, osg::Vec2(static_cast<float>(l_paraSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL, l_rParkSpotUIDataContainer);
        }

        else if (l_curSlotType == 1u && m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag != 1U)
        {
          setSlot(l_firstSlot, l_textureVerticalSlot, g_uiSettings->m_settingVerticalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_CROSS, l_rParkSpotUIDataContainer);
        }

        else if (l_curSlotType == 2u && m_parkSpace_info[l_side][l_numberPerside].m_slot_flash_flag != 1U)
        {
          setSlot(l_firstSlot, l_textureDiagonalSlot, g_uiSettings->m_settingDiagonalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL, l_rParkSpotUIDataContainer);
        }

        else
        {
          //do nothing
        }

      }
      // display the slots per side one by one from the second slot
      else if (f_curparkSpace[l_side][l_numberPerside-1u].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE || f_curparkSpace[l_side][l_numberPerside-1u].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED )
      {
        l_curSlotType = getSlotType( f_curparkSpace[l_side][l_numberPerside].m_APA_PSType, f_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlotSta_f32);
        l_lastSlotType= getSlotType( f_curparkSpace[l_side][l_numberPerside-1u].m_APA_PSType, f_curparkSpace[l_side][l_numberPerside-1u].m_APA_PrkgSlotSta_f32);
        if (l_lastSlotType == 0u && l_curSlotType == 0u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPP;       // g_uiSettings->m_parkingDistanceBetweenPP means distance between parallel slot icons
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureParallelSlot, g_uiSettings->m_settingParallelSlot.m_iconSize, osg::Vec2(static_cast<float>(l_paraSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 0u && l_curSlotType == 1u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPC  ;         // g_uiSettings->m_parkingDistanceBetweenPC   means distance between parallel & vertical slot icons
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureVerticalSlot, g_uiSettings->m_settingVerticalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_CROSS, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 0u && l_curSlotType == 2u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPP;          // g_uiSettings->m_parkingDistanceBetweenPP means distance between parallel & diagonal slot icons
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureDiagonalSlot, g_uiSettings->m_settingDiagonalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 1u && l_curSlotType == 0u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPC  ;
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureParallelSlot, g_uiSettings->m_settingParallelSlot.m_iconSize, osg::Vec2(static_cast<float>(l_paraSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 1u && l_curSlotType == 1u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenCC;           // g_uiSettings->m_parkingDistanceBetweenCC means distance between vertical slot icons
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureVerticalSlot, g_uiSettings->m_settingVerticalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_CROSS, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 1u && l_curSlotType == 2u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPC  ;             // g_uiSettings->m_parkingDistanceBetweenPC   means distance between diagonal & vertical slot icons
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureDiagonalSlot, g_uiSettings->m_settingDiagonalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 2u && l_curSlotType == 0u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPP;
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureParallelSlot, g_uiSettings->m_settingParallelSlot.m_iconSize, osg::Vec2(static_cast<float>(l_paraSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 2u && l_curSlotType == 1u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenPC  ;
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureVerticalSlot, g_uiSettings->m_settingVerticalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_CROSS, l_rParkSpotUIDataContainer);
          }
        }

        if (l_lastSlotType == 2u && l_curSlotType == 2u)
        {
          l_height = l_height+g_uiSettings->m_parkingDistanceBetweenDD;               // parkingDistanceBetweenDD means distance between diagonal slot icons
          if (l_height <= l_maxHeight)
          {
          setSlot(l_firstSlot+l_numberPerside, l_textureDiagonalSlot, g_uiSettings->m_settingDiagonalSlot.m_iconSize, osg::Vec2(static_cast<float>(l_vertSlotPositionX), static_cast<float>(l_height)), cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL, l_rParkSpotUIDataContainer);
          }
        }
      }
      else
      {
        // do nothing
      }

    }
    }
  }
  cc::daddy::CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort.deliver();
}

void ParkingSlotFixedManager::displaySelectedSlot(vfc::uint8_t f_vehicle, vfc::uint8_t f_guideline, vfc::uint8_t f_slot, const osg::Vec2& f_position)
{
  m_settingParkSlot.getIcon(f_vehicle)->setPosition(f_position,pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_vehicle)->setEnabled(true);
  m_settingParkSlot.getIcon(f_slot)->setEnabled(true);
  m_settingParkSlot.getIcon(f_guideline)->setEnabled(true);
}

void ParkingSlotFixedManager::displaySelectedSlot(vfc::uint8_t f_vehicle,  vfc::uint8_t f_slot, const osg::Vec2& f_position)
{
  m_settingParkSlot.getIcon(f_vehicle)->setPosition(f_position,pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_vehicle)->setEnabled(true);
  m_settingParkSlot.getIcon(f_slot)->setEnabled(true);
}

void ParkingSlotFixedManager::manageDisplayLogicInGuidance(SelectedSlotFixed f_selectedParkSpace, cc::target::common::rbp_Type_ParkManeuverType_en f_curparkPSDirection)    // PRQA S 6043
{
  vfc::uint8_t l_curSlotType = getSlotType(f_selectedParkSpace.m_selectedSlotType, f_selectedParkSpace.m_slotAngle);
  if (f_selectedParkSpace.m_selectedSlotSide == 0)
  {
    if (l_curSlotType == 0u)         // parallel slot
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, GUIDELINE_PARASLOT_LEFT, SELECTED_PARASLOT_LEFT, g_uiSettings->m_settingSmallAutoPicPara.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
        displaySelectedSlot(PARKING_VEHICLE_PIC, GUIDELINE_VERTSLOT_REARIN_LEFT, SELECTED_VERTSLOT_REARIN_LEFT, g_uiSettings->m_settingSmallAutoPicVertRearInLeft.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, GUIDELINE_VERTSLOT_FRONTIN_LEFT, SELECTED_VERTSLOT_FRONTIN_LEFT, g_uiSettings->m_settingSmallAutoPicVertFrontInLeft.m_iconCenter);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)          // diagonal slot
    {
        m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setImage(g_uiSettings->m_texturePathHoriCombinationDiagLeftRearIn);
        m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setEnabled(true);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setImage(g_uiSettings->m_texturePathHoriCombinationDiagLeftFrontIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setEnabled(true);
    }
    else
    {
      //do nothing
    }
  }
  else if (f_selectedParkSpace.m_selectedSlotSide == 1)
  {
    if (l_curSlotType == 0u)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, GUIDELINE_PARASLOT_RIGHT, SELECTED_PARASLOT_RIGHT, g_uiSettings->m_settingSmallAutoPicPara.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, GUIDELINE_VERTSLOT_REARIN_RIGHT, SELECTED_VERTSLOT_REARIN_RIGHT, g_uiSettings->m_settingSmallAutoPicVertRearInRight.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, GUIDELINE_VERTSLOT_FRONTIN_RIGHT, SELECTED_VERTSLOT_FRONTIN_RIGHT, g_uiSettings->m_settingSmallAutoPicVertFrontInRight.m_iconCenter);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setImage(g_uiSettings->m_texturePathHoriCombinationDiagRightRearIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setEnabled(true);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setImage(g_uiSettings->m_texturePathHoriCombinationDiagRightFrontIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setEnabled(true);
    }
  }
  else
  {
    //do nothing
  }

}

void ParkingSlotFixedManager::manageDisplayLogicInPause(SelectedSlotFixed f_selectedParkSpace, cc::target::common::rbp_Type_ParkManeuverType_en f_curparkPSDirection)    // PRQA S 6043
{
  vfc::uint8_t l_curSlotType = getSlotType(f_selectedParkSpace.m_selectedSlotType, f_selectedParkSpace.m_slotAngle);
  if (f_selectedParkSpace.m_selectedSlotSide == 0)
  {
    if (l_curSlotType == 0u)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_PARASLOT_LEFT, g_uiSettings->m_settingSmallAutoPicPara.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
        displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_VERTSLOT_REARIN_LEFT, g_uiSettings->m_settingSmallAutoPicVertRearInLeft.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_VERTSLOT_FRONTIN_LEFT, g_uiSettings->m_settingSmallAutoPicVertFrontInLeft.m_iconCenter);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)          // diagonal slot
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setImage(g_uiSettings->m_texturePathHoriPauseCombinationDiagLeftRearIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setEnabled(true);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setImage(g_uiSettings->m_texturePathHoriPauseCombinationDiagLeftFrontIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_LEFT)->setEnabled(true);
    }
  }
  else if (f_selectedParkSpace.m_selectedSlotSide == 1)
  {
    if (l_curSlotType == 0u)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_PARASLOT_RIGHT, g_uiSettings->m_settingSmallAutoPicPara.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_VERTSLOT_REARIN_RIGHT, g_uiSettings->m_settingSmallAutoPicVertRearInRight.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_VERTSLOT_FRONTIN_RIGHT, g_uiSettings->m_settingSmallAutoPicVertFrontInRight.m_iconCenter);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setImage(g_uiSettings->m_texturePathHoriPauseCombinationDiagRightRearIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setEnabled(true);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setImage(g_uiSettings->m_texturePathHoriPauseCombinationDiagRightFrontIn);
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_RIGHT)->setEnabled(true);
    }
  }
  else
  {
    //do nothing
  }

}

void ParkingSlotFixedManager::manageDisplayLogicInComplete(SelectedSlotFixed f_selectedParkSpace, cc::target::common::rbp_Type_ParkManeuverType_en f_curparkPSDirection)    // PRQA S 6043
{
  vfc::uint8_t l_curSlotType = getSlotType(f_selectedParkSpace.m_selectedSlotType, f_selectedParkSpace.m_slotAngle);
  if (f_selectedParkSpace.m_selectedSlotSide == 0)
  {
    if (l_curSlotType == 0u)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_PARASLOT_LEFT, g_uiSettings->m_settingHoriSelectedSlotParaLeft.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC_RIGHT, SELECTED_VERTSLOT_REARIN_LEFT, g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC_LEFT, SELECTED_VERTSLOT_FRONTIN_LEFT, g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn.m_iconCenter);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_REARIN_LEFT)->setEnabled(true);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_FRONTIN_LEFT)->setEnabled(true);
    }
  }
  else if (f_selectedParkSpace.m_selectedSlotSide == 1)
  {
    if (l_curSlotType == 0u)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC, SELECTED_PARASLOT_RIGHT, g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC_LEFT, SELECTED_VERTSLOT_REARIN_RIGHT, g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn.m_iconCenter);
    }
    else if (l_curSlotType == 1u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      displaySelectedSlot(PARKING_VEHICLE_PIC_RIGHT, SELECTED_VERTSLOT_FRONTIN_RIGHT, g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn.m_iconCenter);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_REARIN_RIGHT)->setEnabled(true);
    }
    else if (l_curSlotType == 2u && f_curparkPSDirection == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkFwdIn_enm)
    {
      m_settingParkSlot.getIcon(COMBINATION_DIAGSLOT_FRONTIN_RIGHT)->setEnabled(true);
    }
  }
  else
  {
    //do nothing
  }

}

SelectedSlotFixed ParkingSlotFixedManager::getSelectedSlot(std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace)
{
  SelectedSlotFixed l_selectedSlot;
  l_selectedSlot.m_selectedSlotSide = 255u;
  l_selectedSlot.m_selectedSlotId   = 255u;
  l_selectedSlot.m_selectedSlotType = cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT;
  l_selectedSlot.m_slotAngle        = 999.f;
  for(vfc::uint8_t l_side = 0; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
        for(vfc::uint8_t l_numberPerside = 0; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
        {
          if (f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
          {
            l_selectedSlot.m_selectedSlotSide = l_side;
            l_selectedSlot.m_selectedSlotId   = l_numberPerside;
            l_selectedSlot.m_selectedSlotType = f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PSType;
            l_selectedSlot.m_slotAngle        = f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlotSta_f32;
            return l_selectedSlot;
          }
          else
          {
            //do nothing
          }
        }
    }
  return l_selectedSlot;
}

bool ParkingSlotFixedManager::isASlotSelected(std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace)
{
  for(vfc::uint8_t l_side = 0; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
        for(vfc::uint8_t l_numberPerside = 0; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
        {
          if (f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
          {
            return true;
          }
          else
          {
            //do nothing
          }
        }
    }
    return false;
}

void ParkingSlotFixedManager::CleanButtonDispSts()
{
  m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkingInTypeDispSts        = cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
}

bool ParkingSlotFixedManager::DeliverButtonDispSts(const core::CustomFramework* f_framework)
{
  cc::daddy::ParkDisp2TouchStsDaddy_t& l_ParkDisp2TouchSts =
              cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.reserve() ;

  l_ParkDisp2TouchSts.m_Data.m_ButtonParkSlotsSelectingDispSts = m_ViewButtonParkInSlotsSelectingDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkInTypeDispSts         = m_ViewButtonParkingInTypeDispSts;

  cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.deliver() ;

  return false;
}

} // namespace uielements
} // namespace assets
} // namespace cc