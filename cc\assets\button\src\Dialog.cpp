//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/Dialog.h"
#include "cc/assets/button/inc/Button.h"

// #define GET_PORT_DATA(dataDaddy, port, PortHaveDataFlag)                                                               \ // PRQA S 1054
//     auto(dataDaddy)    = (port).getData();                                                                             \ // PRQA S 1054
//     (PortHaveDataFlag) = (PortHaveDataFlag) && ((dataDaddy) != nullptr);                                               \ // PRQA S 1054
//     if ((dataDaddy) == nullptr)                                                                                        \ // PRQA S 1054
//     {                                                                                                                  \ // PRQA S 1054
//         XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \ // PRQA S 1054
//     }

namespace cc
{
namespace assets
{
namespace button
{

pc::util::coding::Item<DialogSettings> g_DialogSettings("DialogSettings");

DialogMask::DialogMask(cc::core::AssetId f_assetId, osg::Camera* f_referenceView)
: Button{f_assetId, f_referenceView}
{
    setName("DialogMask");
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine // PRQA S 4262 // PRQA S 4264
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
    setIconAtMiddle(true);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(g_DialogSettings->m_dialogMaskPos);
    setTexturePath(g_DialogSettings->m_diagMaskTexturePath);
}

DialogContent::DialogContent(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
: Button{f_assetId, f_referenceView}
, m_framework{f_framework}
{
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine // PRQA S 4262 // PRQA S 4264
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
}

void DialogContent::update()
{
    setSettingModifiedCount(g_DialogSettings->getModifiedCount());
    GET_PORT_DATA(touchStatusContainer,       m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)
    GET_PORT_DATA(hmiDataContainer,           m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)

    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    static TouchStatus s_lastTouch=TouchStatus::TOUCH_DOWN;
    s_lastTouch=touchStatus();

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

    if (touchStatusPortHaveData)
    {
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    const bool touchInsideResponseArea = checkTouchInsideCustomClickArea();
    const TouchStatus touchSts = touchStatus();
    const ButtonState previousState = getState();
    ButtonState newState = AVAILABLE;

    if (newState == AVAILABLE && (touchSts == TOUCH_DOWN || touchSts == TOUCH_MOVE) && touchInsideResponseArea)
    {
        newState = PRESSED;
    }
    if (touchStatusChanged && touchSts == TOUCH_UP && previousState == PRESSED && touchInsideResponseArea)
    {
        newState = RELEASED;
    }
    setState(newState);

    if(!m_newTime&&s_lastTouch!=TOUCH_UP&&touchSts==TOUCH_UP)
    {
        m_touchInside=checkTouchInsideResponseArea();
    }
    if(m_newTime&&s_lastTouch!=TOUCH_UP&&touchSts==TOUCH_UP)
    {
        m_newTime=false;
    }
}

void DialogContent::changeContent(DialogID f_dialogID)
{
    cc::target::common::EThemeTypeDayNight l_themeTypeDayNight = cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY;
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)
    if (dayNightThemePortHaveData)
    {
        l_themeTypeDayNight = dayNightThemeContainer->m_Data;
    }

    switch (f_dialogID)
    {
        case DialogID::DIALOG_MOD:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentModTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentModTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentModPos);
            setCustomClickArea(g_DialogSettings->m_diagContentModClickArea.m_iconCenter, g_DialogSettings->m_diagContentModClickArea.m_responseArea);
            break;
        }
        case DialogID::DIALOG_DGEARACT:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentDGearActTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentDGearActTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentDGearActPos);
            setCustomClickArea(g_DialogSettings->m_diagContentDGearClickArea.m_iconCenter, g_DialogSettings->m_diagContentDGearClickArea.m_responseArea);
            break;
        }
        case DialogID::DIALOG_SONARACT:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentSonarActTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentSonarActTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentSonarActPos);
            setCustomClickArea(g_DialogSettings->m_diagContentSonarClickArea.m_iconCenter, g_DialogSettings->m_diagContentSonarClickArea.m_responseArea);
            break;
        }
        case DialogID::DIALOG_NARROWLANEACT:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentNarrowLaneActTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentNarrowLaneActTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentNarrowLaneActPos);
            setCustomClickArea(g_DialogSettings->m_diagContentNarrowLaneClickArea.m_iconCenter, g_DialogSettings->m_diagContentNarrowLaneClickArea.m_responseArea);
            break;
        }
        case DialogID::DIALOG_VEHTRANS:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentVehTransTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentVehTransTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentVehTransPos);
            setCustomClickArea(g_DialogSettings->m_diagContentVehTransClickArea.m_iconCenter, g_DialogSettings->m_diagContentVehTransClickArea.m_responseArea);
            break;
        }
        case DialogID::DIALOG_STEERACT:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentSteerActTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentSteerActTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentSteerActPos);
            setCustomClickArea(g_DialogSettings->m_diagContentSteerClickArea.m_iconCenter, g_DialogSettings->m_diagContentSteerClickArea.m_responseArea);
            break;
        }
        case DialogID::DIALOG_NIGHTMODE:
        {
            if(l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(g_DialogSettings->m_diagContentNightModeTexture.m_day.m_AvailableTexturePath);
            }
            else
            {
                setTexturePath(g_DialogSettings->m_diagContentNightModeTexture.m_night.m_AvailableTexturePath);
            }
            setPositionHori(g_DialogSettings->m_diagContentNightModePos);
            setCustomClickArea(g_DialogSettings->m_diagContentNightClickArea.m_iconCenter, g_DialogSettings->m_diagContentNightClickArea.m_responseArea);
            break;
        }
        default:
        {
            break;
        }
    }
    resetTouchInside();
}



void cc::assets::button::Dialog::update()
{
    if (getNumChildren() == 0u)
    {
        m_showDialog = false;
        m_enabled    = false;
        return;
    }

    if(!(m_dialogContent->getTouchInside())&& m_showDialog == true)
    {
        m_showDialog = false;
    }

    GET_PORT_DATA(showReqContainer, m_framework->asCustomFramework()->m_showReq_ReceiverPort, showReqPortHaveData)


    if (showReqPortHaveData)
    {
        const bool showReq = showReqContainer->m_Data;
        if (showReq == false && m_showStatus!= false)
        {
            m_showDialog = false;  // hide dialog when avm close
        }
        m_showStatus = showReq;
    }

    if (!m_showDialog)
    {
        m_enabled = false;
        cc::assets::button::ButtonPopController::unRegisterPopButton();
        return;
    }
    m_enabled                = true;
    const vfc::int32_t numChildren = getNumChildren();
    if (m_showDialog)
    {
        for (vfc::int32_t i = 0; i < numChildren; i++)
        {
            const auto button = dynamic_cast<Button*>(getChild(i));  // PRQA S 3400
            if (button != nullptr)
            {
                button->traverseUpdate(false);
            }
        }
    }
    for (vfc::int32_t i = 0; i < numChildren; i++)
    {
        const auto button = dynamic_cast<Button*>(getChild(i));  // PRQA S 3400
        if (button != nullptr)
        {
            if (button->getState() == Button::ButtonState::RELEASED)
            {
                m_showDialog = false;
            }
        }
    }
}

} // namespace button
} // namespace assets
} // namespace cc
