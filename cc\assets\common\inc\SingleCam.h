//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SingleCam.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_COMMON_SINGLECAM_H
#define CC_ASSETS_COMMON_SINGLECAM_H

#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/factory/inc/SV3DNode.h"

namespace cc
{
namespace assets
{
namespace common
{


//======================================================
// SingleCamNode
//------------------------------------------------------
/// Responsible for single cam geometry.
/// Creates geometry for single cam depends on the area.
/// Tipically updated if the sat cam calib or mask
/// changed.
/// <AUTHOR> Florian
//======================================================
class SingleCamNode : public osg::Group
{
public:

  SingleCamNode(pc::factory::SV3DNode* f_sv3dNode, pc::factory::SingleCamArea f_area);

  virtual void traverse(osg::NodeVisitor& f_nv) override;
  
protected:

  virtual ~SingleCamNode();
  //! Copy constructor is not permitted.
  SingleCamNode (const SingleCamNode& other); // = delete
  //! Copy assignment operator is not permitted.
  SingleCamNode& operator=(const SingleCamNode& other); // = delete

private:

  osg::ref_ptr<pc::factory::SV3DNode> m_sv3dNode;
  unsigned int m_lastVertexArrayUpdate;
};

//======================================================
// SingleCam
//------------------------------------------------------
/// Responsible for single cam geometries.
/// Creates and stores single cam nodes for bowl
/// and floor depends on the area.
/// <AUTHOR> Florian
//======================================================
class SingleCam : public pc::core::Asset
{
public:

  SingleCam(cc::core::AssetId f_assetId,
    pc::factory::SV3DNode* f_pBowl,
    pc::factory::SV3DNode* f_pFloor,
    pc::factory::SingleCamArea f_cam);

protected:

  virtual ~SingleCam();
  //! Copy constructor is not permitted.
  SingleCam (const SingleCam& other); // = delete
  //! Copy assignment operator is not permitted.
  SingleCam& operator=(const SingleCam& other); // = delete

};

} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_SINGLECAM_H