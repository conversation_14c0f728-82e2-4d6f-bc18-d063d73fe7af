//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SplineOverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_SPLINEOVERLAY_SPLINEOVERLAY_H
#define CC_ASSETS_SPLINEOVERLAY_SPLINEOVERLAY_H

#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
#include "pc/svs/vehicle/inc/Ultrasonic.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/coding/inc/CodingManager.h"

#include <vector>
#include <osg/ref_ptr>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Depth>
#include <osg/NodeCallback>

#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "cc/util/beziercurve/inc/BezierCurve.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "cc/assets/trajectory/inc/MainLogic.h"


//! forward declaration
namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc


namespace cc
{
namespace assets
{
namespace splineoverlay
{

typedef pc::util::math::LinearInterpolator<osg::Vec4f> ColorInterpolator;

extern ColorInterpolator g_colorInterpolator;
extern ColorInterpolator g_colorInterpolator_OffCourse;
extern ColorInterpolator g_colorInterpolator_OffCourse_Shadow;

class Settings;
extern pc::util::coding::Item<Settings> g_settings;

class SectorData // The distances are measured around the car along the sectors.
{
public:
  SectorData();

  // ~SectorData();

  osg::Vec2f m_refPoint;
  osg::Vec2f m_refPointEnd;
  osg::Vec2f m_dir; // The origin for the direction is the m_referencePoint.
  osg::Vec2f m_leftBorderRefPoint;
  osg::Vec2f m_leftBorderRefPointEnd;
  osg::Vec2f m_leftBorderDir;
  float      m_currentDistance;
  float      m_targetDistance;
  bool       m_onCourse;
  bool       m_obstaclePresent;
  bool       m_leftBorderIsFrontOrRearType; // True if the border is defined as blue in the requirement of Ilias, otherwise false (red).
                                            // See "https://rb-alm-13-p-dwa.de.bosch.com:8443/dwa/rm/urn:rational::1-4147106800294823-O-453-0009c51d?doors.view=00000003".


};


struct ObstacleIsland
{
  size_t  m_startSectorIndex;
  size_t  m_endSectorIndex;
  bool m_obstaclesInAllSectors;
};


enum CurveType
{
  StartCurve = 0,
  MiddleCurve = 1,
  EndCurve = 2,
  MiddleCurve_LoopEnd = 3 // For the case when there are obstacles in all sectors.
};


enum EShadowType : vfc::uint8_t
{
    SHADOWTYPE_Default                   = 0u,  //! default, align with middle line dir
    SHADOWTYPE_LineNormal                = 1u,  //! normal the line
    SHADOWTYPE_SectorDir                 = 2u,  //! align with middle line dir
    SHADOWTYPE_LineNorm_SectorDir        = 3u,  //! start curve and end curve normal to the line, others align with sector dir
    SHADOWTYPE_Center_Point_Intersection = 4u,  //! rotate with sector intersection
    SHADOWTYPE_Center_Point              = 5u   //! rotate with fixed point
};


struct Curve
{
  cc::util::beziercurve::BezierCurve m_bezierCurve;
  CurveType                          m_type;
  size_t                       m_sectorIndex;
  size_t                       m_splineMiddlePointStartIndex;
  size_t                       m_splineMiddlePointEndIndex;
};


//!
//! ColorValues
//!
class ColorValues : public pc::util::coding::ISerializable
{
public:

  ColorValues()
  : m_nearColor(0.953f, 0.0f, 0.039f, 1.0f)
  , m_middleColor(1.0f, 0.494f, 0.0f, 1.0f)
  , m_farColor(0.0f, 0.761f, 0.216f, 1.0f)
  , m_nearColor_OffCourse(0.68f, 0.22f, 0.2f, 1.0f)
  , m_middleColor_OffCourse(0.96f, 0.72f, 0.32f, 1.0f)
  , m_farColor_OffCourse(1.0f, 1.0f, 1.0f, 0.4f)
  , m_nearColor_OffCourse_Shadow(0.8f, 0.0f, 0.039f, 0.2f)
  , m_middleColor_OffCourse_Shadow(0.8f, 0.6f, 0.0f, 0.2f)
  , m_farColor_OffCourse_Shadow(1.0f, 1.0f, 1.0f, 0.2f)
  , m_shadowColor(0.0f, 0.0f, 0.0f, 0.5f)
  {
  }

  SERIALIZABLE(ColorValues)
  {
    ADD_MEMBER(osg::Vec4f, nearColor);
    ADD_MEMBER(osg::Vec4f, middleColor);
    ADD_MEMBER(osg::Vec4f, farColor);
    ADD_MEMBER(osg::Vec4f, nearColor_OffCourse);
    ADD_MEMBER(osg::Vec4f, middleColor_OffCourse);
    ADD_MEMBER(osg::Vec4f, farColor_OffCourse);
    ADD_MEMBER(osg::Vec4f, nearColor_OffCourse_Shadow);
    ADD_MEMBER(osg::Vec4f, middleColor_OffCourse_Shadow);
    ADD_MEMBER(osg::Vec4f, farColor_OffCourse_Shadow);
    ADD_MEMBER(osg::Vec4f, shadowColor);
  }

  osg::Vec4f m_nearColor;
  osg::Vec4f m_middleColor;
  osg::Vec4f m_farColor;
  osg::Vec4f m_nearColor_OffCourse;
  osg::Vec4f m_middleColor_OffCourse;
  osg::Vec4f m_farColor_OffCourse;
  osg::Vec4f m_nearColor_OffCourse_Shadow;
  osg::Vec4f m_middleColor_OffCourse_Shadow;
  osg::Vec4f m_farColor_OffCourse_Shadow;
  osg::Vec4f m_shadowColor;
};

//!
//! Settings
//!
class Settings : public pc::util::coding::ISerializable
{
public:

  Settings()
  : m_zPos(0.01f)
  , m_distanceNear(0.3f)
  , m_distanceMiddle(0.6f)
  , m_distanceFar(0.9f)
  , m_shadowOuterContourDist(4.0f)
  , m_splineWidth(0.04f)
  , m_splineWidthShadow(1.0f)
  , m_maxShowDistance(2.0f)
  , m_endPointOffset(0.7f)
  , m_handlePointScale(0.35f)
  , m_handlePointScaleMid(0.55f)
  , m_farHandlePointScale(0.8f)
  , m_fadingWidth(10u)
  {
  }

  SERIALIZABLE(Settings)
  {
    ADD_FLOAT_MEMBER(zPos);
    ADD_FLOAT_MEMBER(distanceNear);
    ADD_FLOAT_MEMBER(distanceMiddle);
    ADD_FLOAT_MEMBER(distanceFar);
    ADD_FLOAT_MEMBER(shadowOuterContourDist);
    ADD_FLOAT_MEMBER(splineWidth);
    ADD_FLOAT_MEMBER(splineWidthShadow);
    ADD_FLOAT_MEMBER(maxShowDistance);
    ADD_FLOAT_MEMBER(endPointOffset);
    ADD_FLOAT_MEMBER(handlePointScale);
    ADD_FLOAT_MEMBER(handlePointScaleMid);
    ADD_FLOAT_MEMBER(farHandlePointScale);
    ADD_UINT32_MEMBER(fadingWidth);
    ADD_MEMBER(ColorValues, colors);
  }

  float        m_zPos;
  float        m_distanceNear;
  float        m_distanceMiddle;
  float        m_distanceFar;
  float        m_shadowOuterContourDist;
  float        m_splineWidth;
  float        m_splineWidthShadow;
  float        m_maxShowDistance;
  float        m_endPointOffset;
  float        m_handlePointScale;
  float        m_handlePointScaleMid;
  float        m_farHandlePointScale;
  unsigned int m_fadingWidth;

  ColorValues m_colors;
};

extern pc::util::coding::Item<Settings> g_settings;


//======================================================
// SplineOverlay
//------------------------------------------------------
/// On the Top View obstacles are shown by the USS zones.
/// Shows the curve around the car.
/// <AUTHOR>
//======================================================
class SplineOverlay : public osg::Geode
{
  // Init
public:
  SplineOverlay(cc::core::CustomFramework* f_pCustomFramework,
                cc::core::CustomZoneLayout* f_pZoneLayout,
                int f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend,
                cc::assets::trajectory::mainlogic::MainLogic* f_mainLogicRefPtr, bool f_isShadow);

  static constexpr vfc::uint32_t mc_segmentsPerBezierCurve = 16u;
  static constexpr vfc::uint32_t mc_trianglesPerSegment = 4u;
  static constexpr vfc::uint32_t mc_numOfVertexLines = 4u;
private:
      void connectDaddyPorts();
      void defineSectorData();
      void initCurves();
      void create1DTexture();
      void loadTexture();

  // Deinit
public:
  virtual ~SplineOverlay();
private:
  SplineOverlay(const SplineOverlay& other);              // private = avoid copying the instance
  SplineOverlay& operator=(const SplineOverlay& other);   // private = avoid copying the instance
  void disconnectDaddyPorts();

  // Cyclic
public:
  void update();
private:
      void setSectorDistances();
          void mapDistanceInputToSectors();
          bool convergeDistances(float& f_currentDistance, float f_targetDistance);
      size_t getPrevSectorIndex(const size_t & f_index) const;
      size_t getNextSectorIndex(const size_t & f_index) const;
      void findObstacleIslands();
          osg::Vec2f getLeftHandlePointVec(const size_t f_sectorIndex, const osg::Vec2f& f_obstaclePos, const float f_scale) const;
          osg::Vec2f getLeftBorderHandlePointVec(const size_t f_sectorIndex, const osg::Vec2f& f_obstaclePos, const float f_scale) const;
          osg::Vec2f getRightBorderHandlePointVec(const size_t f_sectorIndex, const osg::Vec2f& f_obstaclePos, const float f_scale) const;
      void createBezierPoints();
      void generateSplineMiddlePoints();
      // bool pointIsInsideTheDrivingTube(osg::Vec2f f_point, float f_translationAngle_Rad);
      void generateSplineGeometry();
          osg::Vec2f getLineProjectionVector(const size_t f_curveIndex, const size_t f_middlePointIndex) const;
              size_t getPrevMiddlePointIndex(const size_t f_middlePointIndex) const;
              size_t getNextMiddlePointIndex(const size_t f_middlePointIndex) const;
              size_t getIndexDiffBetweenMiddlePoints(const size_t f_precedingMiddlePoint, const size_t f_subsequentMiddlePoint) const;
          osg::Vec2f getShadowOuterPoint(const size_t f_curveIndex, const size_t f_middlePointIndex, const osg::Vec2f& f_pointToProject) const;
          float calcMiddlePointDistance(const size_t f_curveIndex, const size_t f_middlePointIndex) const;
              bool pointFallsInSector(const osg::Vec2f& f_point, const size_t f_sectorIndex) const;
              size_t getSectorIndex(const size_t f_curveIndex, const size_t f_middlePointIndex, const osg::Vec2f& f_pointToProject) const;
      void makeDirty();
      void createDummyMesh();

  // Members
private:
  osg::ref_ptr<cc::assets::trajectory::mainlogic::MainLogic>      m_mainLogicRefPtr;
  const cc::assets::trajectory::mainlogic::ModelData_st &         m_modelData;
  const cc::assets::trajectory::mainlogic::Inputs_st    &         m_inputData;
  cc::core::CustomFramework*                                      m_pCustomFramework;
  osg::ref_ptr<cc::core::CustomZoneLayout>                        m_zoneLayout;
  cc::assets::trajectory::commontypes::VertexData_st              m_vertexData;
  osg::ref_ptr<osg::Geometry>                                     m_geometry;
  std::vector<SectorData>                                         m_sectors;
  std::vector<ObstacleIsland>                                     m_obstacleIslands;
  std::vector<Curve>                                              m_curves;
  osg::ref_ptr<osg::Vec2Array>                                    mp_splineMiddlePoints;
  ::daddy::TLatestReceiverPort < pc::daddy::UltrasonicDataDaddy > m_ultrasonicDataReceiver;
  bool                                                            m_isShadow;
  EShadowType                                                     m_shadowType;
};


class UpdateCallback : public osg::NodeCallback
{
public:
  UpdateCallback() {};

  ~UpdateCallback() {};

  virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv);

private:
  //! Copy constructor is not permitted.
  UpdateCallback (const UpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  UpdateCallback& operator=(const UpdateCallback& other); // = delete

};

void initColorInterpolator(ColorInterpolator& f_interpolator, const ColorValues& f_colors);
void initColorInterpolator_OffCourse(ColorInterpolator& f_interpolator, const ColorValues& f_colors);
void initColorInterpolator_OffCourse_Shadow(ColorInterpolator& f_interpolator, const ColorValues& f_colors);

} // namespace splineoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_SPLINEOVERLAY_SPLINEOVERLAY_H