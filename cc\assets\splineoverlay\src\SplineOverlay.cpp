//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SplineOverlay.cpp
/// @brief
//=============================================================================

#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/assets/splineoverlay/inc/SplineOverlay.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "cc/assets/trajectory/inc/MainLogic.h"
#include "CustomSystemConf.h"

#include "osg/Geometry"
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osgDB/WriteFile"

namespace cc
{
namespace assets
{
namespace splineoverlay
{

pc::util::coding::Item<Settings> g_settings("SplineOverlay");

ColorInterpolator g_colorInterpolator;
ColorInterpolator g_colorInterpolator_OffCourse;
ColorInterpolator g_colorInterpolator_OffCourse_Shadow;

void initColorInterpolator(ColorInterpolator& f_interpolator, const ColorValues& f_colors)
{
  f_interpolator.clear();
  f_interpolator.addSample(g_settings->m_distanceNear,    f_colors.m_nearColor);
  f_interpolator.addSample(g_settings->m_distanceMiddle,  f_colors.m_middleColor);
  f_interpolator.addSample(g_settings->m_distanceFar,     f_colors.m_farColor);
  f_interpolator.init();
}

void initColorInterpolator_OffCourse(ColorInterpolator& f_interpolator, const ColorValues& f_colors)
{
  f_interpolator.clear();
  f_interpolator.addSample(g_settings->m_distanceNear,    f_colors.m_nearColor_OffCourse);
  f_interpolator.addSample(g_settings->m_distanceMiddle,  f_colors.m_middleColor_OffCourse);
  f_interpolator.addSample(g_settings->m_distanceFar,     f_colors.m_farColor_OffCourse);
  f_interpolator.init();
}

void initColorInterpolator_OffCourse_Shadow(ColorInterpolator& f_interpolator, const ColorValues& f_colors)
{
  f_interpolator.clear();
  f_interpolator.addSample(g_settings->m_distanceNear,    f_colors.m_nearColor_OffCourse_Shadow);
  f_interpolator.addSample(g_settings->m_distanceMiddle,  f_colors.m_middleColor_OffCourse_Shadow);
  f_interpolator.addSample(g_settings->m_distanceFar,     f_colors.m_farColor_OffCourse_Shadow);
  f_interpolator.init();
}

SectorData::SectorData()
  : m_refPoint             {osg::Vec2f(0.0f, 0.0f)}
  , m_refPointEnd          {osg::Vec2f(0.0f, 0.0f)}
  , m_dir                  {osg::Vec2f(0.0f, 0.0f)}
  , m_leftBorderRefPoint   {osg::Vec2f(0.0f, 0.0f)}
  , m_leftBorderRefPointEnd{osg::Vec2f(0.0f, 0.0f)}
  , m_leftBorderDir        {osg::Vec2f(0.0f, 0.0f)}
  , m_currentDistance      {0.0f}
  , m_targetDistance       {0.0f}
  , m_onCourse             {false}
  , m_obstaclePresent      {false}
  , m_leftBorderIsFrontOrRearType {true}
{
}

// SectorData::~SectorData()
// {
// }

// constexpr vfc::uint32_t SplineOverlay::mc_segmentsPerBezierCurve = 16u;
// constexpr vfc::uint32_t SplineOverlay::mc_trianglesPerSegment = 4u;
// constexpr vfc::uint32_t SplineOverlay::mc_numOfVertexLines = 4u;

SplineOverlay::SplineOverlay(cc::core::CustomFramework* f_pCustomFramework,
                             cc::core::CustomZoneLayout* f_pZoneLayout,
                             vfc::int32_t f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend,
                             cc::assets::trajectory::mainlogic::MainLogic* f_mainLogicRefPtr, bool f_isShadow)
  : m_mainLogicRefPtr{f_mainLogicRefPtr}
  , m_modelData{f_mainLogicRefPtr->getModelDataRef()}
  , m_inputData{f_mainLogicRefPtr->getInputDataRef()}
  , m_pCustomFramework{f_pCustomFramework}
  , m_zoneLayout{f_pZoneLayout}
  , m_vertexData{}
  , m_geometry{}
  , m_sectors{}
  , m_obstacleIslands{}
  , m_curves{}
  , mp_splineMiddlePoints{}
  , m_ultrasonicDataReceiver{}
  , m_isShadow{f_isShadow}
  , m_shadowType{SHADOWTYPE_Center_Point}
{
  if ((f_pCustomFramework != nullptr) && (f_pZoneLayout != nullptr) && (f_mainLogicRefPtr != nullptr))
  {
#if 0
  for (unsigned long long int i = 0; i < 5000000000; i++) {}
#endif
    defineSectorData();

    initColorInterpolator(g_colorInterpolator, g_settings->m_colors);
    initColorInterpolator_OffCourse(g_colorInterpolator_OffCourse, g_settings->m_colors);
    initColorInterpolator_OffCourse_Shadow(g_colorInterpolator_OffCourse_Shadow, g_settings->m_colors);

    osg::Depth * const l_depthStateAttrib = new osg::Depth(osg::Depth::LESS);
    l_depthStateAttrib->setWriteMask(f_depthBufferWrite);
    const osg::StateAttribute::GLModeValue l_depthTest = f_depthTest ? osg::StateAttribute::ON : osg::StateAttribute::OFF;
    const osg::StateAttribute::GLModeValue l_blend     = f_blend     ? osg::StateAttribute::ON : osg::StateAttribute::OFF;
    const vfc::uint32_t  arraySize = static_cast<vfc::uint32_t>(m_sectors.size() * 2u * (mc_segmentsPerBezierCurve + 1u) * mc_numOfVertexLines);
    m_vertexData.Vertices  = new osg::Vec3Array(arraySize);
    m_vertexData.Normals   = new osg::Vec3Array(1u);
    m_vertexData.Colors    = new osg::Vec4Array(arraySize);
    m_vertexData.TexCoords = new osg::Vec2Array(arraySize);
    m_vertexData.Indices   = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES),
                                                static_cast<vfc::uint16_t>(m_sectors.size() * 2u * mc_segmentsPerBezierCurve * mc_trianglesPerSegment * 3u));
    m_geometry = new osg::Geometry;
    m_geometry->setUseDisplayList(false);
    m_geometry->setVertexArray(m_vertexData.Vertices);
    m_geometry->setNormalArray(m_vertexData.Normals, osg::Array::BIND_OVERALL);
    m_geometry->setColorArray(m_vertexData.Colors, osg::Array::BIND_PER_VERTEX);
    m_geometry->setTexCoordArray(0u, m_vertexData.TexCoords);
    m_geometry->addPrimitiveSet(m_vertexData.Indices);  // PRQA S 3803
    osg::Geode::addDrawable(m_geometry);  // PRQA S 3803

    osg::StateSet* const l_stateSet = m_geometry->getOrCreateStateSet();
    //pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicColor");
    l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
    l_stateSet->setMode(GL_BLEND, l_blend); // PRQA S 3143
    l_stateSet->setMode(GL_DEPTH_TEST, l_depthTest); // PRQA S 3143
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(f_renderBinOrder, "RenderBin");
    l_stateSet->setAttributeAndModes(l_depthStateAttrib);

    addUpdateCallback(new UpdateCallback);

    connectDaddyPorts();
    initCurves();
    create1DTexture();
    loadTexture();

    update();
  }
}

SplineOverlay::~SplineOverlay()
{
  disconnectDaddyPorts();
}

void SplineOverlay::connectDaddyPorts()
{
  cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.connect(m_ultrasonicDataReceiver);

}

void SplineOverlay::disconnectDaddyPorts()
{
    try
    {
        cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.disconnect(m_ultrasonicDataReceiver);
    }
    catch(...)
    {
    }
}

void SplineOverlay::update()
{
  setSectorDistances();
  findObstacleIslands();
  createBezierPoints();
  generateSplineMiddlePoints();
  generateSplineGeometry();

  if (0u == m_vertexData.Indices->size())
  {
    createDummyMesh();
  }

  makeDirty();
}

void SplineOverlay::setSectorDistances()  // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{
  if (true == m_pCustomFramework->m_ultrasonicDataReceiver.isConnected())
  {
    const pc::daddy::UltrasonicDataDaddy* const l_pDataDaddy = m_pCustomFramework->m_ultrasonicDataReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      pc::vehicle::UltrasonicData l_ultrasonicData = l_pDataDaddy->m_Data;

      for (size_t i = 0u; i < m_sectors.size(); i++)
      {
        if (0u == i || 15u == i)
        {
          if (l_ultrasonicData[i].getDistance() > 1.1f)
          {
            l_ultrasonicData[i].setDistance( 2.5f );
          }
        }
        else if (7u == i || 8u == i)
        {
          if (l_ultrasonicData[i].getDistance() > 1.5f)
          {
            l_ultrasonicData[i].setDistance( 2.5f );
          }
        }
        else
        {
          if (l_ultrasonicData[i].getDistance() > 0.6f)
          {
            l_ultrasonicData[i].setDistance( 2.5f );
          }
        }
      }

      if (m_pCustomFramework->m_pasStatus_ReceiverPort.hasData())
      {
        const cc::daddy::PasStatusDaddy_t* const l_pasStatus = m_pCustomFramework->m_pasStatus_ReceiverPort.getData();
        const cc::target::common::EPasStatus l_pasSt = l_pasStatus->m_Data;
        if (cc::target::common::EPasStatus::PAS_FActiveRFailure == l_pasSt)
        {
          l_ultrasonicData[6u].setDistance( 2.5f );
          l_ultrasonicData[7u].setDistance( 2.5f );
          l_ultrasonicData[8u].setDistance( 2.5f );
          l_ultrasonicData[9u].setDistance( 2.5f );
        }
        else if (cc::target::common::EPasStatus::PAS_RActiveFFailure == l_pasSt)
        {
          l_ultrasonicData[0u].setDistance( 2.5f );
          l_ultrasonicData[1u].setDistance( 2.5f );
          l_ultrasonicData[14u].setDistance( 2.5f );
          l_ultrasonicData[15u].setDistance( 2.5f );
        }
        else if (cc::target::common::EPasStatus::PAS_Standby == l_pasSt)
        {
          l_ultrasonicData[6u].setDistance( 2.5f );
          l_ultrasonicData[7u].setDistance( 2.5f );
          l_ultrasonicData[8u].setDistance( 2.5f );
          l_ultrasonicData[9u].setDistance( 2.5f );
          l_ultrasonicData[0u].setDistance( 2.5f );
          l_ultrasonicData[1u].setDistance( 2.5f );
          l_ultrasonicData[14u].setDistance( 2.5f );
          l_ultrasonicData[15u].setDistance( 2.5f );
        }
        else
        {
          //Do nothing
        }
      }

      if(m_pCustomFramework->m_sdwStatusF_ReceiverPort.hasData())
      {
        const cc::daddy::SdwStatusFDaddy_t* const l_sdwFstatus = m_pCustomFramework->m_sdwStatusF_ReceiverPort.getData();
        const bool l_sdwF_St = l_sdwFstatus->m_Data;
        if (!l_sdwF_St)
        {
          l_ultrasonicData[2u].setDistance( 2.5f );
          l_ultrasonicData[13u].setDistance( 2.5f );
        }
      }

      if(m_pCustomFramework->m_sdwStatusFM_ReceiverPort.hasData())
      {
        const cc::daddy::SdwStatusFMDaddy_t* const l_sdwFMstatus = m_pCustomFramework->m_sdwStatusFM_ReceiverPort.getData();
        const bool l_sdwFM_St = l_sdwFMstatus->m_Data;
        if (!l_sdwFM_St)
        {
          l_ultrasonicData[3u].setDistance( 2.5f );
          l_ultrasonicData[12u].setDistance( 2.5f );
        }
      }

      if(m_pCustomFramework->m_sdwStatusRM_ReceiverPort.hasData())
      {
        const cc::daddy::SdwStatusRMDaddy_t* const l_sdwRMstatus = m_pCustomFramework->m_sdwStatusRM_ReceiverPort.getData();
        const bool l_sdwRM_St = l_sdwRMstatus->m_Data;
        if (!l_sdwRM_St)
        {
          l_ultrasonicData[4u].setDistance( 2.5f );
          l_ultrasonicData[11u].setDistance( 2.5f );
        }
      }

      if(m_pCustomFramework->m_sdwStatusR_ReceiverPort.hasData())
      {
        const cc::daddy::SdwStatusRDaddy_t* const l_sdwRstatus = m_pCustomFramework->m_sdwStatusR_ReceiverPort.getData();
        const bool l_sdwR_St = l_sdwRstatus->m_Data;
        if (!l_sdwR_St)
        {
          l_ultrasonicData[5u].setDistance( 2.5f );
          l_ultrasonicData[10u].setDistance( 2.5f );
        }
      }

      for (size_t i = 0u; i < m_sectors.size(); i++) // PRQA S 4297
      {
        if (isLess(l_ultrasonicData[i].getDistance(), g_settings->m_maxShowDistance))
        {
          m_sectors[i].m_obstaclePresent = true;
          m_sectors[i].m_targetDistance = l_ultrasonicData[i].getDistance();
          //float l_distance = convergeDistances(m_sectors[i].m_currentDistance, m_sectors[i].m_targetDistance);
          m_sectors[i].m_currentDistance = m_sectors[i].m_targetDistance;
          if (true == l_ultrasonicData[i].getOnPath())
          {
            m_sectors[i].m_onCourse = true;
          }
          else
          {
            m_sectors[i].m_onCourse = false;
          }
        }
        else
        {
          m_sectors[i].m_obstaclePresent = false;
          m_sectors[i].m_onCourse = false;
        }
      }
    }
  }

#if 0
  for (unsigned int sectorIndex = 0; sectorIndex < m_sectors.size(); sectorIndex++)
  {
    //if (0 == (sectorIndex % 2))
    if (true)
    {
      m_sectors[sectorIndex].m_obstaclePresent = true;
      m_sectors[sectorIndex].m_currentDistance = m_sectors[0].m_currentDistance;
    }
    else
    {
      m_sectors[sectorIndex].m_obstaclePresent = false;
    }
  }
#endif
}

void SplineOverlay::defineSectorData()
{
  m_sectors = std::vector<SectorData>(16u);
  assert(m_sectors.size() == static_cast<size_t>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES));

  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(m_sectors.size()); i++)
  {
    m_sectors[i].m_refPoint    = m_zoneLayout->getMiddleLine(i).m_innerPoint;
    m_sectors[i].m_refPointEnd = m_zoneLayout->getMiddleLine(i).m_outerPoint;
    m_sectors[i].m_dir         = m_zoneLayout->getMiddleLine(i).m_direction;
    m_sectors[i].m_leftBorderRefPoint    = m_zoneLayout->getLeftBorderLine(i).m_innerPoint;
    m_sectors[i].m_leftBorderRefPointEnd = m_zoneLayout->getLeftBorderLine(i).m_outerPoint;
    m_sectors[i].m_leftBorderDir         = m_zoneLayout->getLeftBorderLine(i).m_direction;
  }

  m_sectors[ 0u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 1u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 2u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 3u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 4u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 5u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 6u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 7u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 8u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[ 9u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[10u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[11u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[12u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[13u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[14u].m_leftBorderIsFrontOrRearType = false;
  m_sectors[15u].m_leftBorderIsFrontOrRearType = false;
}

void SplineOverlay::initCurves()
{
  // Allocate memory
  m_obstacleIslands = std::vector<ObstacleIsland>(m_sectors.size());
  m_curves = std::vector<Curve>(m_sectors.size() * 2u);
  mp_splineMiddlePoints = new osg::Vec2Array(static_cast<vfc::uint32_t>(m_curves.size() * (mc_segmentsPerBezierCurve + 1u)));
}

void SplineOverlay::create1DTexture()
{
  /*const unsigned int  lc_imageWidth  = 256; // Image width in pixels.
  const unsigned int  lc_imageHeight = 1;   // Image height in pixels.
  const unsigned int  lc_imageDepth  = 1;   // Image depth in pixels, in case of a 3D image.
  const unsigned char lc_channelsPerPixel = 4;
  const unsigned char lc_bytesPerChannel  = 1;
  const float         lc_imageGeometryWidth = static_cast<float>(lc_imageWidth - 1);

  // This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
  const float lc_extraWidthForBlurMul = 1.2f; // (1 <= )


  const float lc_halfGradientWidth = std::abs(g_trajCodingParams->m_gradientWidth) * 0.5f;
  const float lc_halfWholeWidth = m_trajParams.WheelTrack_Width_Whole * 0.5f; // Half of the whole width of the wheel track
  const float lc_halfBorderLineWidth = m_trajParams.WheelTrack_Width_BorderLine * 0.5f;
  float l_absDistancesFromCenter[4]; // 0..3: From outermost to innermost
  float l_normalizedPositions[8];    // 0..7: From left to right

  l_absDistancesFromCenter[0] = lc_halfWholeWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[1] = lc_halfWholeWidth - lc_halfGradientWidth;
  l_absDistancesFromCenter[2] = lc_halfWholeWidth - lc_halfBorderLineWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[3] = lc_halfWholeWidth - lc_halfBorderLineWidth - lc_halfGradientWidth;

  const float lc_halfGeometryWidth = l_absDistancesFromCenter[0] * lc_extraWidthForBlurMul;
  m_lineGeometryWidth              = lc_halfGeometryWidth * 2.0f;

  l_normalizedPositions[0] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0]) / m_lineGeometryWidth;
  l_normalizedPositions[1] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1]) / m_lineGeometryWidth;
  l_normalizedPositions[2] = (lc_halfGeometryWidth - l_absDistancesFromCenter[2]) / m_lineGeometryWidth;
  l_normalizedPositions[3] = (lc_halfGeometryWidth - l_absDistancesFromCenter[3]) / m_lineGeometryWidth;
  l_normalizedPositions[4] = 1.0f - l_normalizedPositions[3];
  l_normalizedPositions[5] = 1.0f - l_normalizedPositions[2];
  l_normalizedPositions[6] = 1.0f - l_normalizedPositions[1];
  l_normalizedPositions[7] = 1.0f - l_normalizedPositions[0];

  unsigned char l_pixelArray[lc_imageWidth][lc_imageHeight][lc_imageDepth][lc_channelsPerPixel][lc_bytesPerChannel];
  osg::Vec4ub l_lineColor_Inside;
  l_lineColor_Inside.r() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_Inside.r() * 255.0f + 0.1f);
  l_lineColor_Inside.g() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_Inside.g() * 255.0f + 0.1f);
  l_lineColor_Inside.b() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_Inside.b() * 255.0f + 0.1f);
  l_lineColor_Inside.a() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_Inside.a() * 255.0f + 0.1f);
  osg::Vec4ub l_lineColor_BorderLine;
  l_lineColor_BorderLine.r() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_BorderLine.r() * 255.0f + 0.1f);
  l_lineColor_BorderLine.g() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_BorderLine.g() * 255.0f + 0.1f);
  l_lineColor_BorderLine.b() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_BorderLine.b() * 255.0f + 0.1f);
  l_lineColor_BorderLine.a() = static_cast<unsigned char>(m_trajParams.WheelTrack_Color_BorderLine.a() * 255.0f + 0.1f);
  osg::Vec4ub l_lineColor_Outside = l_lineColor_BorderLine;
  l_lineColor_Outside.a() = 0;

  for (unsigned int x = 0; x < lc_imageWidth; x++)
  {
    float l_x_normalized = static_cast<float>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < l_normalizedPositions[0])
      || (l_x_normalized > l_normalizedPositions[7]) )
    {
      // Outside the wheel track
      l_pixelArray[x][0][0][trajectory::helper::R][0] = l_lineColor_Outside.r();
      l_pixelArray[x][0][0][trajectory::helper::G][0] = l_lineColor_Outside.g();
      l_pixelArray[x][0][0][trajectory::helper::B][0] = l_lineColor_Outside.b();
      l_pixelArray[x][0][0][trajectory::helper::A][0] = l_lineColor_Outside.a();
    }
    else if ( (l_x_normalized > l_normalizedPositions[3])
           && (l_x_normalized < l_normalizedPositions[4]) )
    {
      // Middle of the wheel track
      l_pixelArray[x][0][0][trajectory::helper::R][0] = l_lineColor_Inside.r();
      l_pixelArray[x][0][0][trajectory::helper::G][0] = l_lineColor_Inside.g();
      l_pixelArray[x][0][0][trajectory::helper::B][0] = l_lineColor_Inside.b();
      l_pixelArray[x][0][0][trajectory::helper::A][0] = l_lineColor_Inside.a();
    }
    else if ( (l_x_normalized > l_normalizedPositions[1])
           && (l_x_normalized < l_normalizedPositions[2]) )
    {
      // Middle of the left border line
      l_pixelArray[x][0][0][trajectory::helper::R][0] = l_lineColor_BorderLine.r();
      l_pixelArray[x][0][0][trajectory::helper::G][0] = l_lineColor_BorderLine.g();
      l_pixelArray[x][0][0][trajectory::helper::B][0] = l_lineColor_BorderLine.b();
      l_pixelArray[x][0][0][trajectory::helper::A][0] = l_lineColor_BorderLine.a();
    }
    else if ( (l_x_normalized > l_normalizedPositions[5])
           && (l_x_normalized < l_normalizedPositions[6]) )
    {
      // Middle of the right border line
      l_pixelArray[x][0][0][trajectory::helper::R][0] = l_lineColor_BorderLine.r();
      l_pixelArray[x][0][0][trajectory::helper::G][0] = l_lineColor_BorderLine.g();
      l_pixelArray[x][0][0][trajectory::helper::B][0] = l_lineColor_BorderLine.b();
      l_pixelArray[x][0][0][trajectory::helper::A][0] = l_lineColor_BorderLine.a();
    }
    else
    {
      // Gradient
      osg::Vec4ub l_interpolatedColor;

      if (l_x_normalized <= l_normalizedPositions[1])
      {
        // Left border line, left gradient
        l_interpolatedColor = trajectory::helper::smoothstep_Vec4ub(
          l_lineColor_Outside, l_lineColor_BorderLine, l_normalizedPositions[0], l_normalizedPositions[1], l_x_normalized);
        l_pixelArray[x][0][0][trajectory::helper::R][0] = l_interpolatedColor.r();
        l_pixelArray[x][0][0][trajectory::helper::G][0] = l_interpolatedColor.g();
        l_pixelArray[x][0][0][trajectory::helper::B][0] = l_interpolatedColor.b();
        l_pixelArray[x][0][0][trajectory::helper::A][0] = l_interpolatedColor.a();
      }
      else if (l_x_normalized >= l_normalizedPositions[6])
      {
        // Right border line, right gradient
        l_interpolatedColor = trajectory::helper::smoothstep_Vec4ub(
          l_lineColor_BorderLine, l_lineColor_Outside, l_normalizedPositions[6], l_normalizedPositions[7], l_x_normalized);
        l_pixelArray[x][0][0][trajectory::helper::R][0] = l_interpolatedColor.r();
        l_pixelArray[x][0][0][trajectory::helper::G][0] = l_interpolatedColor.g();
        l_pixelArray[x][0][0][trajectory::helper::B][0] = l_interpolatedColor.b();
        l_pixelArray[x][0][0][trajectory::helper::A][0] = l_interpolatedColor.a();
      }
      else
      {
        // Inner gradient of either border lines
        if (l_x_normalized <= l_normalizedPositions[3])
        {
          // Left border line, right gradient
          l_interpolatedColor = trajectory::helper::smoothstep_Vec4ub(
            l_lineColor_BorderLine, l_lineColor_Inside, l_normalizedPositions[2], l_normalizedPositions[3], l_x_normalized);
          l_pixelArray[x][0][0][trajectory::helper::R][0] = l_interpolatedColor.r();
          l_pixelArray[x][0][0][trajectory::helper::G][0] = l_interpolatedColor.g();
          l_pixelArray[x][0][0][trajectory::helper::B][0] = l_interpolatedColor.b();
          l_pixelArray[x][0][0][trajectory::helper::A][0] = l_interpolatedColor.a();
        }
        else //(l_x_normalized >= l_normalizedPositions[4])
        {
          // Right border line, left gradient
          l_interpolatedColor = trajectory::helper::smoothstep_Vec4ub(
            l_lineColor_Inside, l_lineColor_BorderLine, l_normalizedPositions[4], l_normalizedPositions[5], l_x_normalized);
          l_pixelArray[x][0][0][trajectory::helper::R][0] = l_interpolatedColor.r();
          l_pixelArray[x][0][0][trajectory::helper::G][0] = l_interpolatedColor.g();
          l_pixelArray[x][0][0][trajectory::helper::B][0] = l_interpolatedColor.b();
          l_pixelArray[x][0][0][trajectory::helper::A][0] = l_interpolatedColor.a();
        }
      }
    }
  }

  osg::ref_ptr<osg::Image> l_image = new osg::Image;
  l_image->setOrigin(osg::Image::TOP_LEFT);
  l_image->setImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_RGBA, GL_UNSIGNED_BYTE, &(l_pixelArray[0][0][0][0][0]), osg::Image::NO_DELETE);
  osgDB::writeImageFile(*l_image, mc_texturePath);*/
}

void SplineOverlay::loadTexture()
{
  /*osg::ref_ptr<osg::Image> l_texImage = osgDB::readImageFile(mc_texturePath);
  osg::ref_ptr<osg::Texture2D> l_tex2D = new osg::Texture2D;
  l_tex2D->setDataVariance(osg::Object::DYNAMIC);
  l_tex2D->setImage(l_texImage);
  l_tex2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_tex2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_tex2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_tex2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

  osg::StateSet* l_stateSet = m_geometry->getOrCreateStateSet();
  l_stateSet->setTextureAttribute(0, l_tex2D);*/
}

bool SplineOverlay::convergeDistances(vfc::float32_t& f_currentDistance, vfc::float32_t f_targetDistance)
{
  const vfc::float32_t l_distanceDelta = std::abs(f_targetDistance - f_currentDistance);
  if (l_distanceDelta < 0.01f)
  {
    //! converged
    return true;
  }
  else if(l_distanceDelta > 0.5f)
  {
    f_currentDistance = f_targetDistance;
  }
  else if (f_currentDistance < f_targetDistance)
  {
    f_currentDistance += (l_distanceDelta / 8.0f);
  }
  else
  {
    f_currentDistance -= (l_distanceDelta / 8.0f);
  }
  return false;
}

size_t SplineOverlay::getNextSectorIndex(const size_t & f_index) const
{
  return m_sectors.size() <= f_index + 1u ? 0u : f_index + 1u;
}

size_t SplineOverlay::getPrevSectorIndex(const size_t & f_index) const
{
  return (0u == f_index) ? m_sectors.size() - 1u : f_index - 1u;
}

void SplineOverlay::findObstacleIslands()
{
  m_obstacleIslands.clear();

  if (0u == m_sectors.size())
  {
    return;
  }

  size_t l_searchEndIndex = 0u;
  bool l_obstaclesInAllSectors = true;
  for (size_t i = 0u; i < m_sectors.size(); i++) // PRQA S 4297
  {
    if (false == m_sectors[i].m_obstaclePresent)
    {
      l_searchEndIndex = i;
      l_obstaclesInAllSectors = false;
      break;
    }
  }

  if (l_obstaclesInAllSectors)
  {
    m_obstacleIslands.push_back(ObstacleIsland());
    m_obstacleIslands.back().m_startSectorIndex = 0u;
    m_obstacleIslands.back().m_endSectorIndex = m_sectors.size() - 1u;
    m_obstacleIslands.back().m_obstaclesInAllSectors = true;
    return;
  }

  const size_t l_searchStartIndex = getNextSectorIndex(l_searchEndIndex);
  bool l_prevSectorHasObstacle = false;

  for (size_t i = l_searchStartIndex; ; i = getNextSectorIndex(i))
  {
    if ( (m_sectors[i].m_obstaclePresent) && (false == l_prevSectorHasObstacle) )
    {
      m_obstacleIslands.push_back(ObstacleIsland());
      m_obstacleIslands.back().m_startSectorIndex = i;
      m_obstacleIslands.back().m_obstaclesInAllSectors = false;
    }
    if ( (false == m_sectors[i].m_obstaclePresent) && (l_prevSectorHasObstacle) )
    {
      m_obstacleIslands.back().m_endSectorIndex = getPrevSectorIndex(i);
    }

    if (i == l_searchEndIndex)
    {
      break;
    }

    l_prevSectorHasObstacle = m_sectors[i].m_obstaclePresent;
  }
}

osg::Vec2f SplineOverlay::getLeftHandlePointVec(const size_t f_sectorIndex, const osg::Vec2f& f_obstaclePos, const vfc::float32_t f_scale) const
{
  const osg::Vec2f l_leftPoint =
      m_sectors[f_sectorIndex].m_leftBorderRefPoint
      + (m_sectors[f_sectorIndex].m_leftBorderDir * m_sectors[f_sectorIndex].m_currentDistance);
  const vfc::float32_t l_toLeftPointDist = (l_leftPoint - f_obstaclePos).length();

  // Rotate to the left
  const osg::Vec2f l_leftVec = osg::Vec2f(-m_sectors[f_sectorIndex].m_dir.y(), m_sectors[f_sectorIndex].m_dir.x());

  return l_leftVec * l_toLeftPointDist * f_scale;
}

osg::Vec2f SplineOverlay::getLeftBorderHandlePointVec(const size_t f_sectorIndex, const osg::Vec2f& f_obstaclePos, const vfc::float32_t f_scale) const
{
  const osg::Vec2f l_leftPoint =
      m_sectors[f_sectorIndex].m_leftBorderRefPoint
      + (m_sectors[f_sectorIndex].m_leftBorderDir * m_sectors[f_sectorIndex].m_currentDistance);
  const vfc::float32_t l_toLeftPointDist = (l_leftPoint - f_obstaclePos).length();

  // Rotate to the right
  const osg::Vec2f l_leftVec = osg::Vec2f(m_sectors[f_sectorIndex].m_leftBorderDir.y(), -m_sectors[f_sectorIndex].m_leftBorderDir.x());

  return l_leftVec * l_toLeftPointDist * f_scale;
}

osg::Vec2f SplineOverlay::getRightBorderHandlePointVec(const size_t f_sectorIndex, const osg::Vec2f& f_obstaclePos, const vfc::float32_t f_scale) const
{
  const size_t l_prevSectorIndex = getPrevSectorIndex(f_sectorIndex);

  const osg::Vec2f l_rightPoint =
      m_sectors[l_prevSectorIndex].m_leftBorderRefPoint
      + (m_sectors[l_prevSectorIndex].m_leftBorderDir * m_sectors[f_sectorIndex].m_currentDistance);
  const vfc::float32_t l_toRightPointDist = (l_rightPoint - f_obstaclePos).length();

  // Rotate to the left
  const osg::Vec2f l_rightVec = osg::Vec2f(-m_sectors[l_prevSectorIndex].m_leftBorderDir.y(), m_sectors[l_prevSectorIndex].m_leftBorderDir.x());

  return l_rightVec * l_toRightPointDist * f_scale;
}

void SplineOverlay::createBezierPoints()  // PRQA S 6043
{
  m_curves.clear();

  // No obstacles
  if (0u == m_obstacleIslands.size())
  {
    return;
  }

  // Obstacles everywhere, one big spline ring
  else if ( (1u == m_obstacleIslands.size()) && (m_obstacleIslands[0u].m_obstaclesInAllSectors) )
  {
    for (size_t sectorIndex = m_obstacleIslands[0u].m_startSectorIndex;
         ;
        sectorIndex = getNextSectorIndex(sectorIndex))

    {
      const osg::Vec2f l_pointInThisSector =
          m_sectors[sectorIndex].m_refPoint
          + (m_sectors[sectorIndex].m_dir * m_sectors[sectorIndex].m_currentDistance);

      const size_t l_nextSectorIndex = getNextSectorIndex(sectorIndex);
      const osg::Vec2f l_pointInNextSector =
          m_sectors[l_nextSectorIndex].m_refPoint
          + (m_sectors[l_nextSectorIndex].m_dir * m_sectors[l_nextSectorIndex].m_currentDistance);

      m_curves.push_back(Curve());
      m_curves.back().m_type = MiddleCurve;
      m_curves.back().m_sectorIndex = sectorIndex;
      m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_pointInNextSector);

      const osg::Vec2f l_thisSectorLeftHandlePointVec  =  getLeftHandlePointVec(sectorIndex,       l_pointInThisSector, g_settings->m_handlePointScaleMid);
      const osg::Vec2f l_nextSectorRightHandlePointVec = -getLeftHandlePointVec(l_nextSectorIndex, l_pointInNextSector, g_settings->m_handlePointScaleMid);
      m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
                                                    l_pointInNextSector + l_nextSectorRightHandlePointVec);

      if (sectorIndex == m_obstacleIslands[0u].m_endSectorIndex)
      {
        m_curves.back().m_type = MiddleCurve_LoopEnd;
        break;
      }
    }
  }

  // Spline islands
  else
  {
    for (size_t islandIndex = 0u; islandIndex < m_obstacleIslands.size(); islandIndex++) // PRQA S 4297 // PRQA S 4687
    {
      for (size_t sectorIndex = m_obstacleIslands[islandIndex].m_startSectorIndex;
           ;
           sectorIndex = getNextSectorIndex(sectorIndex))
      {
        const size_t l_nextSectorIndex = getNextSectorIndex(sectorIndex);
        const size_t l_prevSectorIndex = getPrevSectorIndex(sectorIndex);


        if (sectorIndex == m_obstacleIslands[islandIndex].m_startSectorIndex)
        {
          // The current sector is a start sector, and might be an end sector as well (if the island is made of only 1 obstacle).

          const osg::Vec2f l_islandStartPoint_Far =
            m_sectors[l_prevSectorIndex].m_leftBorderRefPoint
            + (m_sectors[l_prevSectorIndex].m_leftBorderDir * (g_settings->m_maxShowDistance + g_settings->m_endPointOffset));

          const osg::Vec2f l_pointInThisSector =
              m_sectors[sectorIndex].m_refPoint
              + (m_sectors[sectorIndex].m_dir * m_sectors[sectorIndex].m_currentDistance);

          // START CURVE BEGIN
          m_curves.push_back(Curve());
          m_curves.back().m_type = StartCurve;
          m_curves.back().m_sectorIndex = sectorIndex;

          const osg::Vec2f l_thisSectorRightHandlePointVec = -getLeftHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);

          // Connect the start point to the far end of the right "blue" border.
          if (m_sectors[l_prevSectorIndex].m_leftBorderIsFrontOrRearType)
          {
            // Control point calculation
            m_curves.back().m_bezierCurve.setControlPoints(l_islandStartPoint_Far, l_pointInThisSector);

            // Handle point calculation
            osg::Vec2f l_lateralVector = m_sectors[sectorIndex].m_dir;
            // Rotate to the left by 90 degrees
            l_lateralVector = osg::Vec2f(-l_lateralVector.y(), l_lateralVector.x());
            const osg::Vec2f l_lateralLineEndPoint_Left  = l_pointInThisSector + l_lateralVector * 10.0f;
            const osg::Vec2f l_lateralLineEndPoint_Right = l_pointInThisSector - l_lateralVector * 10.0f;
            const osg::Vec2f l_rightBorderStart = m_sectors[l_prevSectorIndex].m_leftBorderRefPoint - m_sectors[l_prevSectorIndex].m_leftBorderDir * 1.0f;
            const osg::Vec2f l_rightBorderEnd   = l_rightBorderStart + m_sectors[l_prevSectorIndex].m_leftBorderDir * 10.0f;
            osg::Vec2f l_rightBorderIntersectionPoint;
            pc::util::findIntersection(l_rightBorderStart, l_rightBorderEnd, l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_rightBorderIntersectionPoint);  // PRQA S 3803
            const osg::Vec2f l_islandStartPointHandlePointVec = (l_rightBorderIntersectionPoint - l_islandStartPoint_Far) * g_settings->m_farHandlePointScale;

            // osg::Vec2f hdl1 = l_islandStartPoint_Far + l_islandStartPointHandlePointVec;
            // osg::Vec2f hdl2 = l_pointInThisSector + l_thisSectorRightHandlePointVec;

            m_curves.back().m_bezierCurve.setHandlePoints(l_islandStartPoint_Far + l_islandStartPointHandlePointVec,
                                                          l_pointInThisSector + l_thisSectorRightHandlePointVec);
          }
          // Connect the start point straight to the right "red" border.
          else
          {
            // Control point calculation
            const osg::Vec2f l_islandStartPoint_StraightToRedBorder =
              m_sectors[l_prevSectorIndex].m_leftBorderRefPoint
              + (m_sectors[l_prevSectorIndex].m_leftBorderDir * m_sectors[sectorIndex].m_currentDistance);
            // l_islandStartPoint_StraightToRedBorder.y() = l_pointInThisSector.y();


            // Handle point calculation
            // osg::Vec2f l_RightBorderLongFarPoint = m_sectors[l_prevSectorIndex].m_leftBorderRefPoint + ((m_sectors[l_prevSectorIndex].m_leftBorderRefPointEnd - m_sectors[l_prevSectorIndex].m_leftBorderRefPoint) * 10.0f);
            // osg::Vec2f l_RightBorderLongInnerPoint = m_sectors[l_prevSectorIndex].m_leftBorderRefPoint - ((m_sectors[l_prevSectorIndex].m_leftBorderRefPointEnd - m_sectors[l_prevSectorIndex].m_leftBorderRefPoint) * 0.5f);

            // osg::Vec2f l_islandStartPointHandlePointVec = l_islandStartPoint_StraightToRedBorder + getRightBorderHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);

            // osg::Vec2f l_RighCtrlLengthed = l_islandStartPoint_StraightToRedBorder + ((l_islandStartPoint_StraightToRedBorder - l_islandStartPointHandlePointVec) * 10.0f);
            // osg::Vec2f l_NewRightCtrlPoint;
            // pc::util::findIntersection(l_RightBorderLongInnerPoint, l_RightBorderLongFarPoint, l_islandStartPointHandlePointVec, l_RighCtrlLengthed, l_NewRightCtrlPoint);
            // osg::Vec2f l_RightHdlPointMove = l_NewRightCtrlPoint - l_islandStartPoint_StraightToRedBorder;

            // m_curves.back().m_bezierCurve.setControlPoints(l_NewRightCtrlPoint, l_pointInThisSector);
            // m_curves.back().m_bezierCurve.setHandlePoints(l_islandStartPointHandlePointVec + l_RightHdlPointMove,
            //                                                l_pointInThisSector + l_thisSectorRightHandlePointVec);

            m_curves.back().m_bezierCurve.setControlPoints(l_islandStartPoint_StraightToRedBorder, l_pointInThisSector);

            // Handle point calculation
            const osg::Vec2f l_islandStartPointHandlePointVec = getRightBorderHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);

            m_curves.back().m_bezierCurve.setHandlePoints(l_islandStartPoint_StraightToRedBorder + l_islandStartPointHandlePointVec,
                                                          l_pointInThisSector + l_thisSectorRightHandlePointVec);
          }
          // START CURVE END



          // END CURVE BEGIN
          m_curves.push_back(Curve());
          m_curves.back().m_sectorIndex = sectorIndex;

          const osg::Vec2f l_thisSectorLeftHandlePointVec = -l_thisSectorRightHandlePointVec;
          const osg::Vec2f l_thisSectorLeftHandlePointVecMid = getLeftHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScaleMid);
          if (sectorIndex == m_obstacleIslands[islandIndex].m_endSectorIndex) // This USS zone has obstacle, the neighbours not!!!!
          {
            m_curves.back().m_type = EndCurve;

            // Connect the end point to the far end of the left "blue" border.
            if (m_sectors[sectorIndex].m_leftBorderIsFrontOrRearType)
            {
              // Control point calculation
              const osg::Vec2f l_islandEndPoint_Far =
                  m_sectors[sectorIndex].m_leftBorderRefPoint
                  + (m_sectors[sectorIndex].m_leftBorderDir * (g_settings->m_maxShowDistance + g_settings->m_endPointOffset));

              m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_islandEndPoint_Far);

              // Handle point calculation
              osg::Vec2f l_lateralVector = m_sectors[sectorIndex].m_dir;
              // Rotate to the left by 90 degrees
              l_lateralVector = osg::Vec2f(-l_lateralVector.y(), l_lateralVector.x());
              const osg::Vec2f l_lateralLineEndPoint_Left  = l_pointInThisSector + l_lateralVector * 10.0f;
              const osg::Vec2f l_lateralLineEndPoint_Right = l_pointInThisSector - l_lateralVector * 10.0f;
              const osg::Vec2f l_leftBorderStart  = m_sectors[sectorIndex].m_leftBorderRefPoint - m_sectors[sectorIndex].m_leftBorderDir * 1.0f;
              const osg::Vec2f l_leftBorderEnd    = l_leftBorderStart + m_sectors[sectorIndex].m_leftBorderDir * 10.0f;
              osg::Vec2f l_leftBorderIntersectionPoint;
              pc::util::findIntersection(l_leftBorderStart,  l_leftBorderEnd,  l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_leftBorderIntersectionPoint);  // PRQA S 3803
              const osg::Vec2f l_islandEndPointHandlePointVec = (l_leftBorderIntersectionPoint - l_islandEndPoint_Far) * g_settings->m_farHandlePointScale;

              m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
                                                            l_islandEndPoint_Far + l_islandEndPointHandlePointVec);
            }
            // Connect the end point straight to the left "red" border.
            else
            {
              // Control point calculation
              const osg::Vec2f l_islandEndPoint_StraightToRedBorder =
                m_sectors[sectorIndex].m_leftBorderRefPoint
                + (m_sectors[sectorIndex].m_leftBorderDir * m_sectors[sectorIndex].m_currentDistance);
              // l_islandEndPoint_StraightToRedBorder.y() = l_pointInThisSector.y();

              // osg::Vec2f l_LeftBorderLongFarPoint = m_sectors[sectorIndex].m_leftBorderRefPoint + ((m_sectors[sectorIndex].m_leftBorderRefPointEnd - m_sectors[sectorIndex].m_leftBorderRefPoint) * 10.0f);
              // osg::Vec2f l_LeftBorderLongInnerPoint = m_sectors[sectorIndex].m_leftBorderRefPoint - ((m_sectors[sectorIndex].m_leftBorderRefPointEnd - m_sectors[sectorIndex].m_leftBorderRefPoint) * 0.5f);

              // Handle point calculation
              // osg::Vec2f l_islandEndPointHandlePointVec = l_islandEndPoint_StraightToRedBorder + getLeftBorderHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);
              // osg::Vec2f l_RighCtrlLengthed = l_islandEndPoint_StraightToRedBorder - ((l_islandEndPointHandlePointVec - l_islandEndPoint_StraightToRedBorder) * 10.0f);
              // osg::Vec2f l_NewLeftCtrlPoint;
              // pc::util::findIntersection(l_LeftBorderLongInnerPoint, l_LeftBorderLongFarPoint, l_islandEndPoint_StraightToRedBorder, l_RighCtrlLengthed, l_NewLeftCtrlPoint);
              // osg::Vec2f l_LeftHdlPointMove = l_NewLeftCtrlPoint - l_islandEndPoint_StraightToRedBorder;

              // m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_NewLeftCtrlPoint);
              // m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
              //                                                l_islandEndPointHandlePointVec + l_LeftHdlPointMove);

              m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_islandEndPoint_StraightToRedBorder);

              // Handle point calculation
              const osg::Vec2f l_islandEndPointHandlePointVec = getLeftBorderHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);

              m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
                                                            l_islandEndPoint_StraightToRedBorder + l_islandEndPointHandlePointVec);
            }
          }
          else // (MiddleCurve == l_nextCurveType)
          {
            m_curves.back().m_type = MiddleCurve;
            const osg::Vec2f l_pointInNextSector =
                m_sectors[l_nextSectorIndex].m_refPoint
                + (m_sectors[l_nextSectorIndex].m_dir * m_sectors[l_nextSectorIndex].m_currentDistance);

            m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_pointInNextSector);

            const osg::Vec2f l_nextSectorRightHandlePointVec = -getLeftHandlePointVec(l_nextSectorIndex, l_pointInNextSector, g_settings->m_handlePointScaleMid);

            m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVecMid,
                                                          l_pointInNextSector + l_nextSectorRightHandlePointVec);
          }
          // END CURVE END
        }
        else if (sectorIndex != m_obstacleIslands[islandIndex].m_endSectorIndex)
        {
          // The current sector is a middle sector.

          const osg::Vec2f l_pointInThisSector =
              m_sectors[sectorIndex].m_refPoint
              + (m_sectors[sectorIndex].m_dir * m_sectors[sectorIndex].m_currentDistance);

          const osg::Vec2f l_pointInNextSector =
              m_sectors[l_nextSectorIndex].m_refPoint
              + (m_sectors[l_nextSectorIndex].m_dir * m_sectors[l_nextSectorIndex].m_currentDistance);

          m_curves.push_back(Curve());
          m_curves.back().m_type = MiddleCurve;
          m_curves.back().m_sectorIndex = sectorIndex;
          m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_pointInNextSector);

          const osg::Vec2f l_thisSectorLeftHandlePointVec  =  getLeftHandlePointVec(sectorIndex,       l_pointInThisSector, g_settings->m_handlePointScaleMid);
          const osg::Vec2f l_nextSectorRightHandlePointVec = -getLeftHandlePointVec(l_nextSectorIndex, l_pointInNextSector, g_settings->m_handlePointScaleMid);
          m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
                                                        l_pointInNextSector + l_nextSectorRightHandlePointVec);
        }
        else
        {
          // The current sector is an end sector.

          const osg::Vec2f l_pointInThisSector =
              m_sectors[sectorIndex].m_refPoint
              + (m_sectors[sectorIndex].m_dir * m_sectors[sectorIndex].m_currentDistance);


          // END CURVE BEGIN
          m_curves.push_back(Curve());
          m_curves.back().m_sectorIndex = sectorIndex;

          const osg::Vec2f l_thisSectorLeftHandlePointVec = getLeftHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);
          m_curves.back().m_type = EndCurve;

          // Connect the end point to the far end of the left "blue" border.
          if (m_sectors[sectorIndex].m_leftBorderIsFrontOrRearType)
          {
            // Control point calculation
            const osg::Vec2f l_islandEndPoint_Far =
                m_sectors[sectorIndex].m_leftBorderRefPoint
                + (m_sectors[sectorIndex].m_leftBorderDir * (g_settings->m_maxShowDistance + g_settings->m_endPointOffset));

            m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_islandEndPoint_Far);

            // Handle point calculation
            osg::Vec2f l_lateralVector = m_sectors[sectorIndex].m_dir;
            // Rotate to the left by 90 degrees
            l_lateralVector = osg::Vec2f(-l_lateralVector.y(), l_lateralVector.x());
            const osg::Vec2f l_lateralLineEndPoint_Left  = l_pointInThisSector + l_lateralVector * 10.0f;
            const osg::Vec2f l_lateralLineEndPoint_Right = l_pointInThisSector - l_lateralVector * 10.0f;
            const osg::Vec2f l_leftBorderStart  = m_sectors[sectorIndex].m_leftBorderRefPoint - m_sectors[sectorIndex].m_leftBorderDir * 1.0f;
            const osg::Vec2f l_leftBorderEnd    = l_leftBorderStart + m_sectors[sectorIndex].m_leftBorderDir * 10.0f;
            osg::Vec2f l_leftBorderIntersectionPoint;
            pc::util::findIntersection(l_leftBorderStart,  l_leftBorderEnd,  l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_leftBorderIntersectionPoint);  // PRQA S 3803
            const osg::Vec2f l_islandEndPointHandlePointVec = (l_leftBorderIntersectionPoint - l_islandEndPoint_Far) * g_settings->m_farHandlePointScale;

            m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
                                                          l_islandEndPoint_Far + l_islandEndPointHandlePointVec);
          }
          // Connect the end point straight to the left "red" border.
          else
          {
            // Control point calculation
            const osg::Vec2f l_islandEndPoint_StraightToRedBorder =
              m_sectors[sectorIndex].m_leftBorderRefPoint
              + (m_sectors[sectorIndex].m_leftBorderDir * m_sectors[sectorIndex].m_currentDistance);
            // l_islandEndPoint_StraightToRedBorder.y() = l_pointInThisSector.y();

            // Handle point calculation
            // osg::Vec2f l_islandEndPointHandlePointVec = l_islandEndPoint_StraightToRedBorder + getLeftBorderHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);
            // osg::Vec2f l_RighCtrlLengthed = l_islandEndPoint_StraightToRedBorder - ((l_islandEndPointHandlePointVec - l_islandEndPoint_StraightToRedBorder) * 10.0f);
            // osg::Vec2f l_NewLeftCtrlPoint;
            // pc::util::findIntersection(m_sectors[sectorIndex].m_leftBorderRefPoint, m_sectors[sectorIndex].m_leftBorderRefPointEnd, l_islandEndPoint_StraightToRedBorder, l_RighCtrlLengthed, l_NewLeftCtrlPoint);
            // osg::Vec2f l_LeftHdlPointMove = l_NewLeftCtrlPoint - l_islandEndPoint_StraightToRedBorder;

            // m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_NewLeftCtrlPoint);
            // m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
            //   l_islandEndPointHandlePointVec + l_LeftHdlPointMove);

            m_curves.back().m_bezierCurve.setControlPoints(l_pointInThisSector, l_islandEndPoint_StraightToRedBorder);

            // Handle point calculation
            const osg::Vec2f l_islandEndPointHandlePointVec = getLeftBorderHandlePointVec(sectorIndex, l_pointInThisSector, g_settings->m_handlePointScale);

            m_curves.back().m_bezierCurve.setHandlePoints(l_pointInThisSector + l_thisSectorLeftHandlePointVec,
                                                          l_islandEndPoint_StraightToRedBorder + l_islandEndPointHandlePointVec);

          }
          // END CURVE END
        }

        if (sectorIndex == m_obstacleIslands[islandIndex].m_endSectorIndex)
        {
          break;
        }
      }
    }
  }
}

void SplineOverlay::generateSplineMiddlePoints()
{
  mp_splineMiddlePoints->clear();
  for (size_t curveIndex = 0u; curveIndex < m_curves.size(); curveIndex++) // PRQA S 4297 // PRQA S 4687
  {
    m_curves[curveIndex].m_splineMiddlePointStartIndex = mp_splineMiddlePoints->size();

    if (EndCurve == m_curves[curveIndex].m_type)
    {
      m_curves[curveIndex].m_bezierCurve.generateVertices(mp_splineMiddlePoints, mc_segmentsPerBezierCurve, true); // PRQA S 2759
      m_curves[curveIndex].m_splineMiddlePointEndIndex = mp_splineMiddlePoints->size() - 1u;
    }
    else if (MiddleCurve_LoopEnd == m_curves[curveIndex].m_type)
    {
      m_curves[curveIndex].m_bezierCurve.generateVertices(mp_splineMiddlePoints, mc_segmentsPerBezierCurve, false); // PRQA S 2759
      m_curves[curveIndex].m_splineMiddlePointEndIndex = 0u;
    }
    else
    {
      m_curves[curveIndex].m_bezierCurve.generateVertices(mp_splineMiddlePoints, mc_segmentsPerBezierCurve, false); // PRQA S 2759
      m_curves[curveIndex].m_splineMiddlePointEndIndex = mp_splineMiddlePoints->size();
    }
  }
}

size_t SplineOverlay::getPrevMiddlePointIndex(const size_t f_middlePointIndex) const
{
  size_t l_result = 0u;
  if (0u == f_middlePointIndex)
  {
    // Last point
    l_result = mp_splineMiddlePoints->size() - 1u;
  }
  else
  {
    l_result = f_middlePointIndex - 1u;
  }

  return l_result;
}

size_t SplineOverlay::getNextMiddlePointIndex(const size_t f_middlePointIndex) const
{
  size_t l_result = 0u;
  if ((f_middlePointIndex + 1u) == mp_splineMiddlePoints->size())
  {
    // First point
    l_result = 0u;
  }
  else
  {
    l_result = f_middlePointIndex + 1u;
  }

  return l_result;
}

size_t SplineOverlay::getIndexDiffBetweenMiddlePoints(const size_t f_precedingMiddlePoint, const size_t f_subsequentMiddlePoint) const
{
  size_t l_indexDiff = 0u;
  if (f_precedingMiddlePoint <= f_subsequentMiddlePoint)
  {
    l_indexDiff = f_subsequentMiddlePoint - f_precedingMiddlePoint;
  }
  else
  {
    l_indexDiff = (f_subsequentMiddlePoint + mp_splineMiddlePoints->size()) - f_precedingMiddlePoint;
  }

  return l_indexDiff;
}

// Returns the middle point normal which points towards the car.
osg::Vec2f SplineOverlay::getLineProjectionVector(const size_t f_curveIndex, const size_t f_middlePointIndex) const
{
  osg::Vec2f l_normal;

  if ( (f_middlePointIndex == m_curves[f_curveIndex].m_splineMiddlePointStartIndex)
    && (m_curves[f_curveIndex].m_type == StartCurve) )
  {
    // First spline middle point of the island
    l_normal = (*mp_splineMiddlePoints)[getNextMiddlePointIndex(f_middlePointIndex)] - (*mp_splineMiddlePoints)[f_middlePointIndex];
    l_normal.normalize();  // PRQA S 3803  // PRQA S 3804
    // Rotate to the left by 90 degrees
    l_normal = osg::Vec2f(-l_normal.y(), l_normal.x());
  }
  else if ( (f_middlePointIndex == m_curves[f_curveIndex].m_splineMiddlePointEndIndex)
         && (m_curves[f_curveIndex].m_type == EndCurve) )
  {
    // Last spline middle point of the island
    l_normal = (*mp_splineMiddlePoints)[getPrevMiddlePointIndex(f_middlePointIndex)] - (*mp_splineMiddlePoints)[f_middlePointIndex];
    l_normal.normalize();  // PRQA S 3803  // PRQA S 3804
    // Rotate to the right by 90 degrees
    l_normal = osg::Vec2f(l_normal.y(), -l_normal.x());
  }
  else
  {
    // Intermediate spline middle point of the island
    osg::Vec2f l_normal_1 = (*mp_splineMiddlePoints)[getNextMiddlePointIndex(f_middlePointIndex)] - (*mp_splineMiddlePoints)[f_middlePointIndex];
    l_normal_1.normalize();  // PRQA S 3803  // PRQA S 3804
    // Rotate to the left by 90 degrees
    l_normal_1 = osg::Vec2f(-l_normal_1.y(), l_normal_1.x());

    osg::Vec2f l_normal_2 = (*mp_splineMiddlePoints)[getPrevMiddlePointIndex(f_middlePointIndex)] - (*mp_splineMiddlePoints)[f_middlePointIndex];
    l_normal_2.normalize();  // PRQA S 3803  // PRQA S 3804
    // Rotate to the right by 90 degrees
    l_normal_2 = osg::Vec2f(l_normal_2.y(), -l_normal_2.x());

    l_normal = l_normal_1 + l_normal_2;
    l_normal.normalize();  // PRQA S 3803  // PRQA S 3804
  }

  return l_normal;
}

//! Returns true if the point is located inside the sector.
bool SplineOverlay::pointFallsInSector(const osg::Vec2f& f_point, const size_t f_sectorIndex) const
{
  // Rotate the point around the left border reference point
  vfc::float32_t l_sin = m_sectors[f_sectorIndex].m_leftBorderDir.y();
  vfc::float32_t l_cos = m_sectors[f_sectorIndex].m_leftBorderDir.x();
  osg::Vec2f l_pointToRotate = f_point - m_sectors[f_sectorIndex].m_leftBorderRefPoint;
  osg::Vec2f l_rotatedPoint;
  l_rotatedPoint.x() =  l_pointToRotate.x() * l_cos + l_pointToRotate.y() * l_sin;
  l_rotatedPoint.y() = -l_pointToRotate.x() * l_sin + l_pointToRotate.y() * l_cos;
  if (l_rotatedPoint.y() > 0.0f)
  {
    return false;
  }

  // Rotate the point around the right border reference point
  const size_t l_prevSectorIndex = getPrevSectorIndex(f_sectorIndex);
  l_sin = m_sectors[l_prevSectorIndex].m_leftBorderDir.y();
  l_cos = m_sectors[l_prevSectorIndex].m_leftBorderDir.x();
  l_pointToRotate = f_point - m_sectors[l_prevSectorIndex].m_leftBorderRefPoint;
  l_rotatedPoint.x() =  l_pointToRotate.x() * l_cos + l_pointToRotate.y() * l_sin;
  l_rotatedPoint.y() = -l_pointToRotate.x() * l_sin + l_pointToRotate.y() * l_cos;
  if (l_rotatedPoint.y() < 0.0f)
  {
    return false;
  }

  return true;
}

size_t SplineOverlay::getSectorIndex(const size_t f_curveIndex, const size_t f_middlePointIndex, const osg::Vec2f& f_pointToProject) const
{
  size_t l_sectorIndex = m_curves[f_curveIndex].m_sectorIndex;

  if ( (m_curves[f_curveIndex].m_type == MiddleCurve) ||
       (m_curves[f_curveIndex].m_type == MiddleCurve_LoopEnd) )
  {
    if (f_middlePointIndex == m_curves[f_curveIndex].m_splineMiddlePointStartIndex)
    {
      // l_sectorIndex is already correct.
    }
    else if (f_middlePointIndex == m_curves[f_curveIndex].m_splineMiddlePointEndIndex)
    {
      l_sectorIndex = getNextSectorIndex(l_sectorIndex);
    }
    else
    {
      // For intermediate points, we have to check, into which sector they really fall.
      if ( pointFallsInSector(f_pointToProject, l_sectorIndex) )
      {
        // l_sectorIndex is already correct.
      }
      else
      {
        l_sectorIndex = getNextSectorIndex(l_sectorIndex);
      }
    }
  }

  return l_sectorIndex;
}

vfc::float32_t SplineOverlay::calcMiddlePointDistance(const size_t f_curveIndex, const size_t f_middlePointIndex) const
{
  const size_t l_sectorIndex = getSectorIndex(f_curveIndex, f_middlePointIndex, (*mp_splineMiddlePoints)[f_middlePointIndex]);

  osg::Vec2f l_lateralVector = m_sectors[l_sectorIndex].m_dir;
  // Rotate to the left by 90 degrees
  l_lateralVector = osg::Vec2f(-l_lateralVector.y(), l_lateralVector.x());

  const osg::Vec2f l_lateralLineEndPoint_Left  = (*mp_splineMiddlePoints)[f_middlePointIndex] + l_lateralVector * 10.0f;
  const osg::Vec2f l_lateralLineEndPoint_Right = (*mp_splineMiddlePoints)[f_middlePointIndex] - l_lateralVector * 10.0f;

  const osg::Vec2f l_longitudinalVector_Start = m_sectors[l_sectorIndex].m_refPoint - m_sectors[l_sectorIndex].m_dir * 10.0f;
  const osg::Vec2f l_longitudinalVector_End = m_sectors[l_sectorIndex].m_refPointEnd + m_sectors[l_sectorIndex].m_dir * 10.0f;

  osg::Vec2f l_intersectionPoint;

  pc::util::findIntersection(l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_longitudinalVector_Start, l_longitudinalVector_End, l_intersectionPoint);  // PRQA S 3803

  return (l_intersectionPoint - m_sectors[l_sectorIndex].m_refPoint).length();
}

osg::Vec2f SplineOverlay::getShadowOuterPoint(const size_t f_curveIndex, const size_t f_middlePointIndex, const osg::Vec2f& f_pointToProject) const
{
  const size_t l_sectorIndex = getSectorIndex(f_curveIndex, f_middlePointIndex, f_pointToProject);

  const size_t l_prevSectorIndex = getPrevSectorIndex(l_sectorIndex);

  osg::Vec2f l_lateralVector = m_sectors[l_sectorIndex].m_dir;
  // Rotate to the left by 90 degrees
  l_lateralVector = osg::Vec2f(-l_lateralVector.y(), l_lateralVector.x());

  const osg::Vec2f l_lateralLineEndPoint_Left  = f_pointToProject + l_lateralVector * 10.0f;
  const osg::Vec2f l_lateralLineEndPoint_Right = f_pointToProject - l_lateralVector * 10.0f;

  const osg::Vec2f l_leftBorderStart  = m_sectors[l_sectorIndex].m_leftBorderRefPoint - m_sectors[l_sectorIndex].m_leftBorderDir * 1.0f;
  const osg::Vec2f l_leftBorderEnd    = l_leftBorderStart + m_sectors[l_sectorIndex].m_leftBorderDir * 10.0f;
  const osg::Vec2f l_rightBorderStart = m_sectors[l_prevSectorIndex].m_leftBorderRefPoint - m_sectors[l_prevSectorIndex].m_leftBorderDir * 1.0f;
  const osg::Vec2f l_rightBorderEnd   = l_rightBorderStart + m_sectors[l_prevSectorIndex].m_leftBorderDir * 10.0f;

  osg::Vec2f l_leftBorderIntersectionPoint;
  osg::Vec2f l_rightBorderIntersectionPoint;
  pc::util::findIntersection(l_leftBorderStart,  l_leftBorderEnd,  l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_leftBorderIntersectionPoint);  // PRQA S 3803
  pc::util::findIntersection(l_rightBorderStart, l_rightBorderEnd, l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_rightBorderIntersectionPoint);  // PRQA S 3803

  const osg::Vec2f l_fromLeftBorderToRightBorder = l_rightBorderIntersectionPoint - l_leftBorderIntersectionPoint;
  const osg::Vec2f l_fromLeftBorderToPoint       = f_pointToProject               - l_leftBorderIntersectionPoint;

  const vfc::float32_t l_portionFromRightBorderDirVec = l_fromLeftBorderToPoint.length() / l_fromLeftBorderToRightBorder.length();
  const vfc::float32_t l_portionFromLeftBorderDirVec  = 1.0f - l_portionFromRightBorderDirVec;

  osg::Vec2f l_shadowProjVec = m_sectors[l_sectorIndex].m_leftBorderDir     * l_portionFromLeftBorderDirVec
                             + m_sectors[l_prevSectorIndex].m_leftBorderDir * l_portionFromRightBorderDirVec;
  l_shadowProjVec.normalize();  // PRQA S 3803  // PRQA S 3804

  return f_pointToProject + l_shadowProjVec * 1.3f;
}

// bool SplineOverlay::pointIsInsideTheDrivingTube(osg::Vec2f f_point, float f_translationAngle_Rad)
// {

//   if (cc::assets::trajectory::commontypes::Rotation_enm == m_inputData.Internal.VehicleMovementType)
//   {
//     float l_pointRadius = m_mainLogicRefPtr->getDistanceFromAckermannPoint(f_point);

//     if ( ( (m_modelData.LeftTouchPoint.Radius  >= l_pointRadius) &&
//            (m_modelData.RightTouchPoint.Radius <= l_pointRadius) )
//          ||
//          ( (m_modelData.LeftTouchPoint.Radius  <= l_pointRadius) &&
//            (m_modelData.RightTouchPoint.Radius >= l_pointRadius) ) )
//     {
//       return true;
//     }
//     else
//     {
//       return false;
//     }
//   }
//   else // Translation_enm
//   {
//     osg::Vec2f l_rotatedPoint = f_point;
//     trajectory::helper::Rotate2DPoint(l_rotatedPoint, osg::Vec2f(0.0f, 0.0f), -f_translationAngle_Rad);

//     if ( (m_modelData.LeftTouchPoint.Pos.y()  >= l_rotatedPoint.y()) &&
//          (m_modelData.RightTouchPoint.Pos.y() <= l_rotatedPoint.y()) )
//     {
//       return true;
//     }
//     else
//     {
//       return false;
//     }
//   }
// }

void SplineOverlay::generateSplineGeometry()    // PRQA S 6040  // PRQA S 6041  // PRQA S 6043
{
  // float l_translationAngle_Rad = osg::DegreesToRadians(m_inputData.Internal.TranslationAngle);

  m_vertexData.Indices->clear();
  const size_t l_numOfSplineMiddlePoints = mp_splineMiddlePoints->size();
  vfc::float32_t l_halfLineWidth = g_settings->m_splineWidth * 0.5f;
  if (m_isShadow)
  {
    l_halfLineWidth = g_settings->m_splineWidthShadow * 0.5f;
  }

  for (size_t curveIndex = 0u; curveIndex < m_curves.size(); curveIndex++) // PRQA S 4297
  {
    for (size_t middlePointIndex = m_curves[curveIndex].m_splineMiddlePointStartIndex;
             ;
             middlePointIndex = getNextMiddlePointIndex(middlePointIndex))
    {
      osg::Vec2f l_normal;
      const vfc::uint32_t l_sectorIndex = static_cast<vfc::uint32_t>(m_curves[curveIndex].m_sectorIndex);
      const osg::Vec2f l_centerPointFront = osg::Vec2f(2.7f, 0.0f);  //! the center of front shadow, around front axel center
      const osg::Vec2f l_centerPointRear = osg::Vec2f(0.3f, 0.0f);   //! the center of rear shadow, around rear axel center

      switch (static_cast<vfc::int32_t>(m_shadowType))
      {
      case SHADOWTYPE_LineNormal:
      {
        l_normal = getLineProjectionVector(curveIndex, middlePointIndex);
        l_normal = l_normal * (-1.0f);
        break;
      }

      case SHADOWTYPE_SectorDir:
      {
        l_normal = m_sectors[m_curves[curveIndex].m_sectorIndex].m_dir;
        l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        break;
      }

      case SHADOWTYPE_LineNorm_SectorDir:
      {
        if ((StartCurve == m_curves[curveIndex].m_type) && (middlePointIndex < (m_curves[curveIndex].m_splineMiddlePointStartIndex+6u)))
        {
          l_normal = getLineProjectionVector(curveIndex, middlePointIndex);
          l_normal = l_normal * (-1.0f);
        }
        else if ((EndCurve == m_curves[curveIndex].m_type) && (middlePointIndex > (m_curves[curveIndex].m_splineMiddlePointEndIndex-6u)))
        {
          l_normal = getLineProjectionVector(curveIndex, middlePointIndex);
          l_normal = l_normal * (-1.0f);
        }
        else
        {
          l_normal = m_sectors[m_curves[curveIndex].m_sectorIndex].m_dir;
          l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        }
        break;
      }

      case SHADOWTYPE_Center_Point_Intersection:
      {
        if (0u == l_sectorIndex)
        {
          osg::Vec2f l_sector0BoderIntersectionPoint;
          pc::util::findIntersection(m_sectors[0u].m_leftBorderRefPoint, m_sectors[0u].m_leftBorderRefPointEnd, m_sectors[15u].m_leftBorderRefPoint, m_sectors[15u].m_leftBorderRefPointEnd, l_sector0BoderIntersectionPoint);    // PRQA S 3803
          l_normal = (*mp_splineMiddlePoints)[middlePointIndex] - l_sector0BoderIntersectionPoint;
          l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        }
        else if (1u == l_sectorIndex || 14u == l_sectorIndex || 15u == l_sectorIndex ||
                6u == l_sectorIndex || 7u == l_sectorIndex || 8u == l_sectorIndex || 9u == l_sectorIndex)
        {
          // osg::Vec2f l_leftBorderStart  = m_sectors[l_sectorIndex].m_leftBorderRefPoint - m_sectors[l_sectorIndex].m_leftBorderDir * 1.0f;
          // osg::Vec2f l_leftBorderEnd    = l_leftBorderStart + m_sectors[l_sectorIndex].m_leftBorderDir * 10.0f;
          // osg::Vec2f l_sectorBoderIntersectionPoint;
          // pc::util::findIntersection(l_leftBorderStart, l_leftBorderEnd, l_lateralLineEndPoint_Left, l_lateralLineEndPoint_Right, l_rightBorderIntersectionPoint);
          const SectorData l_sector = m_sectors[l_sectorIndex];
          const SectorData l_sectorPre = m_sectors[l_sectorIndex-1u];
          osg::Vec2f l_sectorBoderIntersectionPoint;
          pc::util::findIntersection(l_sector.m_leftBorderRefPoint, l_sector.m_leftBorderRefPointEnd, l_sectorPre.m_leftBorderRefPoint, l_sectorPre.m_leftBorderRefPointEnd, l_sectorBoderIntersectionPoint);    // PRQA S 3803
          l_normal = (*mp_splineMiddlePoints)[middlePointIndex] - l_sectorBoderIntersectionPoint;
          l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        }
        else
        {
          l_normal = m_sectors[m_curves[curveIndex].m_sectorIndex].m_dir;
          l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        }
        break;
      }

      case SHADOWTYPE_Center_Point:
      {
        if (0u == l_sectorIndex || 1u == l_sectorIndex || 14u == l_sectorIndex || 15u == l_sectorIndex)
        {
          l_normal = (*mp_splineMiddlePoints)[middlePointIndex] - l_centerPointFront;
        }
        else if (6u == l_sectorIndex || 7u == l_sectorIndex || 8u == l_sectorIndex || 9u == l_sectorIndex)
        {
          l_normal = (*mp_splineMiddlePoints)[middlePointIndex] - l_centerPointRear;
        }
        else
        {
          l_normal = m_sectors[m_curves[curveIndex].m_sectorIndex].m_dir;
        }
        l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        break;
      }

      default:
      {
        l_normal = m_sectors[m_curves[curveIndex].m_sectorIndex].m_dir;
        l_normal.normalize();    // PRQA S 3803  // PRQA S 3804
        break;
      }
      }

      osg::Vec2f l_innerPoint = (*mp_splineMiddlePoints)[middlePointIndex];
      osg::Vec2f l_outerPoint = (*mp_splineMiddlePoints)[middlePointIndex] + l_normal * l_halfLineWidth * 2.0f;

      // osg::Vec2f l_normal = getLineProjectionVector(curveIndex, middlePointIndex);
      // osg::Vec2f l_innerPoint = (*mp_splineMiddlePoints)[middlePointIndex] + l_normal * l_halfLineWidth;
      // osg::Vec2f l_outerPoint = (*mp_splineMiddlePoints)[middlePointIndex] - l_normal * l_halfLineWidth;
      osg::Vec2f l_shadowOuterPoint = getShadowOuterPoint(curveIndex, middlePointIndex, l_outerPoint);
      const size_t l_index0 = middlePointIndex + 0u * l_numOfSplineMiddlePoints;
      const size_t l_index1 = middlePointIndex + 1u * l_numOfSplineMiddlePoints;
      const size_t l_index2 = middlePointIndex + 2u * l_numOfSplineMiddlePoints;
      const size_t l_index3 = middlePointIndex + 3u * l_numOfSplineMiddlePoints;
      // Line:
      (*m_vertexData.Vertices)[l_index0].set(osg::Vec3f(l_innerPoint.x(), l_innerPoint.y(), g_settings->m_zPos));
      (*m_vertexData.Vertices)[l_index1].set(osg::Vec3f(l_outerPoint.x(), l_outerPoint.y(), g_settings->m_zPos));

      const vfc::float32_t l_distance = calcMiddlePointDistance(curveIndex, middlePointIndex);
      if (true == m_sectors[getSectorIndex(curveIndex, middlePointIndex, (*mp_splineMiddlePoints)[middlePointIndex])].m_onCourse)
      {
        osg::Vec4f l_color = g_colorInterpolator.getValue(l_distance);
        // if (false == pointIsInsideTheDrivingTube(l_innerPoint, l_translationAngle_Rad))
        // {
          // l_color = g_colorInterpolator_OffCourse.getValue(l_distance);
        // }
        // (*m_vertexData.Colors  )[l_index0].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
        // (*m_vertexData.Colors  )[l_index1].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
        if (m_isShadow)
        {
          l_color = g_colorInterpolator_OffCourse_Shadow.getValue(l_distance);
          (*m_vertexData.Colors  )[l_index0].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
          (*m_vertexData.Colors  )[l_index1].set(l_color.r(), l_color.g(), l_color.b(), 0.0f);
        }
        else
        {
          l_color = g_colorInterpolator_OffCourse.getValue(l_distance);
          (*m_vertexData.Colors  )[l_index0].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
          (*m_vertexData.Colors  )[l_index1].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
        }
      }
      else
      {
        // osg::Vec4f l_color = g_colorInterpolator_OffCourse.getValue(l_distance);
        // (*m_vertexData.Colors  )[l_index0].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
        // (*m_vertexData.Colors  )[l_index1].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
        if (m_isShadow)
        {
          osg::Vec4f l_color = g_colorInterpolator_OffCourse_Shadow.getValue(l_distance);
          (*m_vertexData.Colors  )[l_index0].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
          (*m_vertexData.Colors  )[l_index1].set(l_color.r(), l_color.g(), l_color.b(), 0.0f);
        }
        else
        {
          osg::Vec4f l_color = g_colorInterpolator_OffCourse.getValue(l_distance);
          (*m_vertexData.Colors  )[l_index0].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
          (*m_vertexData.Colors  )[l_index1].set(l_color.r(), l_color.g(), l_color.b(), l_color.a());
        }
      }

      // Shadow:
      (*m_vertexData.Vertices)[l_index2].set(osg::Vec3f(l_outerPoint.x(), l_outerPoint.y(), g_settings->m_zPos));
      (*m_vertexData.Vertices)[l_index3].set(osg::Vec3f(l_shadowOuterPoint.x(), l_shadowOuterPoint.y(), g_settings->m_zPos));
      osg::Vec4f l_shadowColor = g_settings->m_colors.m_shadowColor;
      (*m_vertexData.Colors  )[l_index2].set(l_shadowColor.r(), l_shadowColor.g(), l_shadowColor.b(), l_shadowColor.a());
      (*m_vertexData.Colors  )[l_index3].set(l_shadowColor.r(), l_shadowColor.g(), l_shadowColor.b(), 0.0);

      // osg::Vec4f l_nearshadowColor = g_settings->m_colors.m_nearColor;
      // if (l_distance <g_settings->m_distanceNear)
      // {
      //   (*m_vertexData.Colors  )[l_index2].set(l_nearshadowColor.r(), l_nearshadowColor.g(), l_nearshadowColor.b(), l_nearshadowColor.a());
      //   (*m_vertexData.Colors  )[l_index3].set(l_nearshadowColor.r(), l_nearshadowColor.g(), l_nearshadowColor.b(), 0.0);
      // }

      if (StartCurve == m_curves[curveIndex].m_type)
      {
        // Create a fading-in at the start of the curve
        const vfc::float32_t l_indexDiff = static_cast<vfc::float32_t>(getIndexDiffBetweenMiddlePoints(m_curves[curveIndex].m_splineMiddlePointStartIndex, middlePointIndex));

        vfc::float32_t l_fadingAlphaMul_Smoothstepped = 1.0f;
        if (0u < g_settings->m_fadingWidth)
        {
          const vfc::float32_t l_fadingAlphaMul_Linear = l_indexDiff / static_cast<vfc::float32_t>(g_settings->m_fadingWidth);
          l_fadingAlphaMul_Smoothstepped = trajectory::helper::smoothstep(
            0.0f, 1.0f, 0.0f, 1.0f, l_fadingAlphaMul_Linear);
        }

        // Line
        (*m_vertexData.Colors)[l_index0].a() *= l_fadingAlphaMul_Smoothstepped;
        (*m_vertexData.Colors)[middlePointIndex + 1u * l_numOfSplineMiddlePoints].a() *= l_fadingAlphaMul_Smoothstepped;
        // Shadow
        (*m_vertexData.Colors)[l_index2].a() *= l_fadingAlphaMul_Smoothstepped;
        (*m_vertexData.Colors)[l_index3].a() *= l_fadingAlphaMul_Smoothstepped;
      }
      if (EndCurve == m_curves[curveIndex].m_type)
      {
        // Create a fading-out at the end of the curve
        const vfc::float32_t l_indexDiff = static_cast<vfc::float32_t>(getIndexDiffBetweenMiddlePoints(middlePointIndex, m_curves[curveIndex].m_splineMiddlePointEndIndex));

        vfc::float32_t l_fadingAlphaMul_Smoothstepped = 1.0f;
        if (0u < g_settings->m_fadingWidth)
        {
          const vfc::float32_t l_fadingAlphaMul_Linear = l_indexDiff / static_cast<vfc::float32_t>(g_settings->m_fadingWidth);
          l_fadingAlphaMul_Smoothstepped = trajectory::helper::smoothstep(
            0.0f, 1.0f, 0.0f, 1.0f, l_fadingAlphaMul_Linear);
        }

        // Line
        (*m_vertexData.Colors)[l_index0].a() *= l_fadingAlphaMul_Smoothstepped;
        (*m_vertexData.Colors)[middlePointIndex + 1u * l_numOfSplineMiddlePoints].a() *= l_fadingAlphaMul_Smoothstepped;
        // Shadow
        (*m_vertexData.Colors)[l_index2].a() *= l_fadingAlphaMul_Smoothstepped;
        (*m_vertexData.Colors)[l_index3].a() *= l_fadingAlphaMul_Smoothstepped;
      }

      if (middlePointIndex == m_curves[curveIndex].m_splineMiddlePointEndIndex)
      {
        break;
      }
    }

    const size_t l_splineMiddlePointEndIndexMinusOne = getPrevMiddlePointIndex(m_curves[curveIndex].m_splineMiddlePointEndIndex);
    for (size_t middlePointIndex =  m_curves[curveIndex].m_splineMiddlePointStartIndex;
             ;
             middlePointIndex = getNextMiddlePointIndex(middlePointIndex))
    {
      const size_t l_nextMiddlePointIndex = getNextMiddlePointIndex(middlePointIndex);

      // Line:
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( l_nextMiddlePointIndex + 0u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( middlePointIndex       + 0u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( middlePointIndex       + 1u * l_numOfSplineMiddlePoints));

      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( middlePointIndex       + 1u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( l_nextMiddlePointIndex + 1u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( l_nextMiddlePointIndex + 0u * l_numOfSplineMiddlePoints));

      // if (middlePointIndex % 2 == 0)
      // {
      // Shadow:
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( l_nextMiddlePointIndex + 2u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( middlePointIndex       + 2u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( middlePointIndex       + 3u * l_numOfSplineMiddlePoints));

      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( middlePointIndex       + 3u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( l_nextMiddlePointIndex + 3u * l_numOfSplineMiddlePoints));
      m_vertexData.Indices->push_back( static_cast<vfc::uint16_t>( l_nextMiddlePointIndex + 2u * l_numOfSplineMiddlePoints));
      // }

      if (middlePointIndex == l_splineMiddlePointEndIndexMinusOne)
      {
        break;
      }
    }
  }

  (*m_vertexData.Normals)[0u].set(osg::Vec3f(0.0f, 0.0f, 1.0f));
}

void SplineOverlay::makeDirty() // PRQA S 4211
{
  m_vertexData.Vertices->dirty();
  m_vertexData.Colors->dirty();
  m_vertexData.Indices->dirty();
  m_vertexData.Normals->dirty();
  m_vertexData.TexCoords->dirty();
  m_geometry->dirtyBound();
}

void SplineOverlay::createDummyMesh() // PRQA S 4211
{
  (*m_vertexData.Colors)[0u].set(0.0f, 0.0f, 0.0f, 0.0f);
  m_vertexData.Indices->clear();
  m_vertexData.Indices->push_back(0u);
  m_vertexData.Indices->push_back(0u);
  m_vertexData.Indices->push_back(0u);
}

void UpdateCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  SplineOverlay* const l_splineOverlay = dynamic_cast<SplineOverlay*> (f_node); // PRQA S 3077  // PRQA S 3400
  if (nullptr != l_splineOverlay)
  {
    l_splineOverlay->update();
  }

  traverse(f_node, f_nv);
}


} // namespace splineoverlay
} // namespace assets
} // namespace cc

