//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EIK2LR Karim Eid (CC-DA/EAV1)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  BasePlate.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_BASEPLATE_BASEPLATE_H
#define CC_ASSETS_BASEPLATE_BASEPLATE_H

#include <osg/Geode>
#include "pc/svs/core/inc/Array.h"
#include "pc/svs/util/math/inc/Box2D.h"

namespace cc
{
namespace assets
{
namespace baseplate
{
  
class BasePlateData;
extern pc::util::coding::Item<BasePlateData> g_basePlateData;

//======================================================
// BasePlate
//------------------------------------------------------
/// Polygon below the vehicle.
/// Took over from Gen1.
/// <AUTHOR>
//======================================================
class BasePlate : public osg::Geode
{
public:

  enum
  {
    NUM_BASE_PLATE_POINTS = 6,
    NUM_CORNER_POINTS = 4
  };

  typedef ::pc::core::DirectionalArray<float> BorderWidth;

  BasePlate();
  BasePlate(const BasePlate& f_basePlate, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::baseplate, BasePlate);  // PRQA S 2504

  //! calculate the base plate corners given the adaptive mesh points
  void calculateBasePlateCorners(const osg::Vec3* f_corners);

  void calculateTextureCoordinates(const osg::Vec3& f_lowerLeftCorner, const osg::Vec3& f_upperRightCorner);

  const BorderWidth getBorderWidth() const;

  void setBorderWidth(const BorderWidth& f_borderWidth);

  //! update the base plate in case the adaptive mesh changes (due to calibration change)
  void updateBasePlate();

  const osg::Vec3Array* getStitchingCorners() const;

  const pc::util::Box2D getBounds() const;

protected:

  virtual ~BasePlate();
  //! Copy constructor is not permitted.
  BasePlate (const BasePlate& other); // = delete
  //! Copy assignment operator is not permitted.
  BasePlate& operator=(const BasePlate& other); // = delete

private:

  //! vertex array of the base plate geometry
  osg::ref_ptr<osg::Vec3Array> m_vertexArray;
  //! texture coordinate array of the base plate geomtry
  osg::ref_ptr<osg::Vec2Array> m_textureArray;
  //! the corners of the base plate
  osg::ref_ptr<osg::Vec3Array> m_basePlateCorners;
  osg::ref_ptr<osg::Vec3Array> m_stitchingCorners;
  osg::ref_ptr<osg::Geometry> m_geometry;

  BorderWidth m_borderWidth;

  pc::util::Box2D m_bounds;
};


} // namespace baseplate
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BASEPLATE_BASEPLATE_H
