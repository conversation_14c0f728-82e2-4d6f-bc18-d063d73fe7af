//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS SAAP
//  Target systems: SAAP
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (XC-AS/EPF2-CN)
//  Department: XC-AS/EPF
//=============================================================================
/// @swcomponent SVS SAAP
/// @file  CameraImage.cpp
/// @brief
//=============================================================================

#include "cc/assets/common/inc/CameraImageCallback.h"
#include "pc/generic/util/coding/inc/CodingManager.h"

namespace cc
{
namespace assets
{
namespace common
{

class CameraImageSetting : public pc::util::coding::ISerializable
{
public:

    CameraImageSetting()
        : m_brightFactorNight{1.0f}
    {
    }

    SERIALIZABLE(CameraImageSetting) // PRQA S 2428 // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
        return;
        }
        ADD_MEMBER(vfc::float32_t, brightFactorNight);
    }

    vfc::float32_t m_brightFactorNight;
};
static pc::util::coding::Item<CameraImageSetting> g_settings("CameraImageCallback");

void CameraBrightCallback::update(osg::Uniform* f_uniform, osg::NodeVisitor* /* f_nv */)
{
    if (nullptr != f_uniform)
    {
        f_uniform->set(1.0f); // PRQA S 3803
        if ( m_indexReceiver.isConnected() )
        {
            if(m_indexReceiver.hasData())
            {
                const cc::daddy::HUNightModeStsDaddy_t* const l_pDaddy = m_indexReceiver.getData();
                if (nullptr != l_pDaddy)
                {
                    if (l_pDaddy->m_Data)  // true: night; false: normal
                    {
                        f_uniform->set(g_settings->m_brightFactorNight); // PRQA S 3803
                    }
                }
            }
        }
    }
}

} // namespace common
} // namespace assets
} // namespace cc

