//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  ParkingSpaceMark.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_PARKINGSPACE_PARKINGSPACEMARK_H
#define CC_ASSETS_PARKINGSPACE_PARKINGSPACEMARK_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace parkingspace
{


//!
//! ParkingSpaceMarkManager
//!
class ParkingSpaceMarkManager
{
public:

  ParkingSpaceMarkManager();
  virtual ~ParkingSpaceMarkManager();

  void init(pc::assets::ImageOverlays* f_imageOverlays);
  void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
  void update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);
  void isSlotNotDisplayed(
    std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace,
    float f_distanceX,
    float f_distanceY,
    cc::target::common::EPARKStatusR2L f_curparkStatus,
    std::array<std::array<cc::target::common::StrippedParkhmiPositionSearching, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_parkSpaceMark,
    cc::target::common::StrippedParkhmiTargetPosition f_finalEndPosition);

private:
  //! Copy constructor is not permitted.
  ParkingSpaceMarkManager (const ParkingSpaceMarkManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSpaceMarkManager& operator=(const ParkingSpaceMarkManager& other); // = delete

  unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_parkingIcons;
  bool m_mat_b;

  //     PARKING PLAN VIEW
  //          /width/                              Y positive  <-----
  //   --------------------           /top/                         ^
  //   |                  |                                      X  |
  //   |       ....       |        /front bumper/          positive |
  //   |       ....       |        /front axle  /
  //   |       ....       |        /rear  axle  /
  //   |       ....       |        /rear  bumper/
  //   |                  |
  //   --------------------           /bottom/
  // /left/            /right/

  const float m_planViewWidth_hori;
  const float m_planViewWidth_vert;
  const float m_distanceX_hori;    // abs distance between rear axle and bottom of parking plan view in X direction
  const float m_distanceX_vert;
  const float m_distanceY_hori;    // abs distance between rear axle and left/right of parking plan view in Y direction
  const float m_distanceY_vert;
  bool m_isLeftSlotNotShown;
  bool m_isRightSlotNotShown;

};


//!
//! ParkingSpaceMarkSymbols
//!
class ParkingSpaceMarkSymbols: public cc::assets::uielements::CustomImageOverlays
{
public:
  ParkingSpaceMarkSymbols(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~ParkingSpaceMarkSymbols();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  ParkingSpaceMarkSymbols (const ParkingSpaceMarkSymbols& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSpaceMarkSymbols& operator=(const ParkingSpaceMarkSymbols& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ParkingSpaceMarkManager m_manager;
};


} // namespace parkingspace
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKINGSPACE_PARKINGSPACEMARK_H
