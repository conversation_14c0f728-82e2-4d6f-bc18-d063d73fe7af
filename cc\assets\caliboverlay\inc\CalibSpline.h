//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CalibSpline.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_CALIBOVERLAY_CALIBSPLINE_H
#define CC_ASSETS_CALIBOVERLAY_CALIBSPLINE_H

#include <osg/Geode>
#include <osg/Vec2f>

namespace cc
{
namespace assets
{
namespace caliboverlay
{
osg::Vec4f fromRGBA(int f_r, int f_g, int f_b, int f_a);
osg::Vec4f fromRGBA(const osg::Vec4i& f_rgba);

osg::Vec4f brighten(const osg::Vec4f& f_color, float f_add);

//!
//! CalibSpline
//!
class CalibSpline
{
public:

  //!
  //! UpdateVisitor
  //!
  class UpdateVisitor : public osg::NodeVisitor
  {
  public:

    UpdateVisitor();

    virtual ~UpdateVisitor();

    virtual void apply(osg::Node& f_node) override;

  private:

    //! Copy constructor is not permitted.
    UpdateVisitor (const UpdateVisitor& other); // = delete
    //! Copy assignment operator is not permitted.
    UpdateVisitor& operator=(const UpdateVisitor& other); // = delete


  };


  CalibSpline(const CalibSpline& f_other);

  virtual void setLayout() = 0;


protected:

  CalibSpline();

  virtual ~CalibSpline();

  unsigned int m_numVertices;

private:
  //! Copy constructor is not permitted.
  //CalibSpline (const CalibSpline& other); // = delete // The Copy constructor is already defined in public
  //! Copy assignment operator is not permitted.
  CalibSpline& operator=(const CalibSpline& other); // = delete

};




//!
//! CalibSpline2D
//!
class CalibSpline2D : public osg::Geode, public CalibSpline
{
public:

  enum
  {
    NUM_VERTICES_PER_INNER_SEGMENT = 2,
    NUM_VERTICES_PER_OUTER_SEGMENT = 2,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_INNER_SEGMENT + NUM_VERTICES_PER_OUTER_SEGMENT
  };

  CalibSpline2D();
  CalibSpline2D(const CalibSpline2D& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::caliboverlay, CalibSpline2D);  // PRQA S 2504
  
  virtual void setLayout(
  ) override;
protected:

  virtual ~CalibSpline2D();

private:
  //! Copy constructor is not permitted.
  CalibSpline2D (const CalibSpline2D& other); // = delete
  //! Copy assignment operator is not permitted.
  CalibSpline2D& operator=(const CalibSpline2D& other); // = delete

};



} // namespace caliboverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_CALIBOVERLAY_CALIBSPLINE_H