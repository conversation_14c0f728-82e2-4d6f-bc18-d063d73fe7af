//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  IpcCodingReader.cpp
/// @brief 
//=============================================================================

#include "cc/core/inc/IpcCodingReader.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "cc/virtcam/inc/CameraPositions.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/trajectory/inc/MainLogic.h"
#include "cc/assets/common/inc/FloorPlateSm.h"
#include "cc/assets/common/inc/Floor.h"
#include "cc/assets/common/inc/CustomFloorPlateRenderer.h"
#include "pc/svs/assets/floorplate/inc/FloorPlateRenderer.h"
#include "cc/assets/overlaycallback/inc/OverlayCallback.h"
#include "cc/assets/splineoverlay/inc/UssZoneAdaptation.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/virtcam/inc/HeadUnitHemisphereCameraUpdater.h"
#include "cc/virtcam/inc/HemisphereCameraUpdater.h"
#include "cc/assets/caliboverlay/inc/CalibOverlay.h"
#include "cc/views/panoramaview/inc/PanoramaView.h"
#include "cc/views/driveassistview/inc/DriveAssistView.h"
#include "cc/views/planview/inc/PlanView.h"
#include "cc/assets/customfloorplategenerator/inc/CustomFloorPlateGenerator.h"
#include "pc/generic/core/inc/Threads.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/generic/util/chrono/inc/chrono.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"
#include "pc/svs/assets/vehiclemodel/inc/DoorAnimation.h"
#include "pc/svs/util/math/inc/Conversions.hpp"
#include "pc/svs/views/warpfisheyeview/inc/WarpFisheyeView.h"
#include "cc/views/customwarpfisheyeview/inc/CustomWarpFisheyeView.h"
#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"
#include "cc/assets/uielements/inc/ParkingSlot.h"
//#include "pc/svs/texfloor/odometry/src/OdometryConverter.cpp"

#include "osg/Timer"
#include "osg/io_utils"
#include <iomanip>
#include <ctime>

using pc::util::logging::g_EngineContext;
using pc::util::logging::g_StartupContext;

#define LCF_LOGGING 1


namespace cc
{
namespace core
{

std::ostream& operator << (std::ostream& f_os, const osg::Vec3& f_data)
{
  f_os << std::fixed << std::setprecision(3)
       << " x: " << f_data.x()
       << " y: " << f_data.y()
       << " z: " << f_data.z();  // PRQA S 3803
  return f_os;
}

std::ostream& operator << (std::ostream& f_os, const cc::core::PasZonesDescription& f_pasZoneDescription)
{
    f_os << "PasZonesDescription:\n  left border lines:";  // PRQA S 3803
    for (unsigned int i = 0u; i < static_cast<unsigned int>(cc::core::PasZonesDescription::NUM_PAS_ZONES); ++i)
    {
        const osg::Vec4f& l_zone = f_pasZoneDescription.getLeftBorderLine(i);
        f_os << "\n  zone " << i << " = " << osg::Vec2f(l_zone.x(), l_zone.y()) << ", " << osg::Vec2f(l_zone.z(), l_zone.w());  // PRQA S 3803
    }
    f_os << "\n  middle lines:";  // PRQA S 3803
    for (unsigned int i = 0u; i < static_cast<unsigned int>(cc::core::PasZonesDescription::NUM_PAS_ZONES); ++i)
    {
        const osg::Vec4f& l_zone = f_pasZoneDescription.getMiddleLine(i);
        f_os << "\n  zone " << i << " = " << osg::Vec2f(l_zone.x(), l_zone.y()) << ", " << osg::Vec2f(l_zone.z(), l_zone.w());  // PRQA S 3803
    }
    return f_os;
}

osg::Vec4 lcfStitch2Vec( const CStitchingAngles_st& f_lcfStitch)
{
    //! See definition in pc/svs/virtcam/inc/VirtualCam.h
    //! x=FR, y=RR, z=RL, w=FL
  return osg::Vec4f( static_cast<float>(f_lcfStitch.m_angleFrontRight_deg), 
                    static_cast<float>(f_lcfStitch.m_angleRearRight_deg), 
                    static_cast<float>(f_lcfStitch.m_angleRearLeft_deg), 
                    static_cast<float>(f_lcfStitch.m_angleFrontLeft_deg)); 
}
class IpcCodingReaderSettings : public pc::util::coding::ISerializable
{
public:
  IpcCodingReaderSettings()
    : m_readFromIpc(true)
    , m_printIpcData(false)
  {
  }

  SERIALIZABLE(IpcCodingReaderSettings)
  {
    ADD_BOOL_MEMBER(readFromIpc);
    ADD_BOOL_MEMBER(printIpcData);
  }

  bool m_readFromIpc;
  bool m_printIpcData;
};

pc::util::coding::Item<IpcCodingReaderSettings> g_codingReaderSettings("IpcCodingReader");

IpcCodingReader::IpcCodingReader()
{
}

IpcCodingReader::~IpcCodingReader() = default;

IpcCodingReader& IpcCodingReader::instance()
{
  static IpcCodingReader instance;  // PRQA S 2504
  return instance;
}

void IpcCodingReader::readFromIpc(pc::util::coding::CodingManager* f_codingManager)  // PRQA S 6043
{
  if (g_codingReaderSettings->m_readFromIpc)
  {
    cc::daddy::CustomDaddyPorts::sm_LCF_SenderPort.connect(m_ipcLcfReceiver);
  //   cc::daddy::CustomDaddyPorts::sm_CCF_SenderPort.connect(m_ipcCcfReceiver);

    osg::Timer l_timer;
    double l_timeOut = 1.; // in seconds
    bool l_lcfReceived = false;
  //   bool l_ccfReceived = false;
    bool l_ipcDataRead = false;

    // XLOG_INFO_OS(g_EngineContext) << "Trying to read LCF and CCF from IPC"<< XLOG_ENDL;
    XLOG_INFO_OS(g_EngineContext) << "Trying to read LCF from IPC"<< XLOG_ENDL;
    while (!l_ipcDataRead && l_timer.time_s() < l_timeOut)
    {
  //     // CCF first
  //     if (m_ipcCcfReceiver.isConnected() && !l_ccfReceived)
  //     {
  //       m_ipcCcfReceiver.update();
  //       if( m_ipcCcfReceiver.hasData())
  //       {
  //         const cc::daddy::CcfDaddy* l_ccfData = m_ipcCcfReceiver.getData();
  //         XLOG_INFO_OS(g_StartupContext) << "CCF Found Time [ms]: " << chrono_ms() << XLOG_ENDL;
  //         bool l_allSet = modifyCcfData(f_codingManager, l_ccfData);
  //         if(true == l_allSet)
  //         {
  //           XLOG_DEBUG_OS(g_StartupContext) << "All CCFs have been used" << XLOG_ENDL;
  //         }
  //         else
  //         {
  //           XLOG_DEBUG_OS(g_StartupContext) << "Not all CCFs have been used correctly" << XLOG_ENDL;
  //         }
  //         XLOG_INFO_OS(g_StartupContext) << "CCF Loaded Time [ms]: " << chrono_ms() << XLOG_ENDL;
  //         l_ccfReceived = true;
  //         XLOG_INFO_OS(g_EngineContext) << "CCF from IPC found after " << l_timer.time_m() << " ms."  << XLOG_ENDL;
  //       }
  //       else
  //       {
  //         // XLOG_INFO_OS(g_EngineContext) << "Trying to read CCF from IPC, but no new data "<< XLOG_ENDL;
  //       }
  //     }
  //     else
  //     {
  //       // XLOG_INFO_OS(g_EngineContext) << "Trying to read CCF from IPC, but not connected "<< XLOG_ENDL;
  //     }

      // LCF next
      if (m_ipcLcfReceiver.isConnected() && !l_lcfReceived)
      {
        m_ipcLcfReceiver.update();
        if( m_ipcLcfReceiver.hasData())
        {
          const cc::daddy::LcfDaddy* l_lcfData = m_ipcLcfReceiver.getData();
          XLOG_INFO_OS(g_StartupContext) << "LCF Found Time [ms]: " << chrono_ms() << XLOG_ENDL;
          bool l_allSet = modifyLcfData(f_codingManager, l_lcfData);
          if(true == l_allSet)
          {
            XLOG_DEBUG_OS(g_StartupContext) << "All LCFs have been used" << XLOG_ENDL;
          }
          else
          {
            XLOG_DEBUG_OS(g_StartupContext) << "Not all LCFs have been used correctly" << XLOG_ENDL;
          }
          XLOG_INFO_OS(g_StartupContext) << "LCF Loaded Time [ms]: " << chrono_ms() << XLOG_ENDL;
          l_lcfReceived = true;
          XLOG_INFO_OS(g_EngineContext) << "LCF from IPC found after " << l_timer.time_m() << " ms."  << XLOG_ENDL;
        }
        else
        {
          // XLOG_INFO_OS(g_EngineContext) << "Trying to read LCF from IPC, but no new data "<< XLOG_ENDL;
        }
      }
      else
      {
        // XLOG_INFO_OS(g_EngineContext) << "Trying to read LCF from IPC, but not connected "<< XLOG_ENDL;
      }

      // l_ipcDataRead = l_ccfReceived && l_lcfReceived;
      l_ipcDataRead = l_lcfReceived;

      // allow other threads to get cpu time
      std::this_thread::yield();
    }

  //   if (!l_ccfReceived)
  //   {
  //     XLOG_INFO_OS(g_EngineContext) << "CCF could not be read within "<< l_timeOut <<" second(s)." << XLOG_ENDL;
  //   }
    if (!l_lcfReceived)
    {
      XLOG_INFO_OS(g_EngineContext) << "LCF could not be read within "<< l_timeOut <<" second(s)." << XLOG_ENDL;
    }

    m_ipcLcfReceiver.cleanup();
  //   m_ipcCcfReceiver.cleanup();

    cc::daddy::CustomDaddyPorts::sm_LCF_SenderPort.disconnect(m_ipcLcfReceiver);
  //   cc::daddy::CustomDaddyPorts::sm_CCF_SenderPort.disconnect(m_ipcCcfReceiver);
  }
}

bool IpcCodingReader::setVirtualCamera(pc::util::coding::CodingManager* f_codingManager, const std::string f_key, const CVirtualCamParam_st& f_lcfCam)
{
  const std::string l_Virtcam = "VirtCam";
  const std::string l_eyeKey = "eye";
  const std::string l_centerKey = "center";
  const std::string l_upKey = "up";
  const std::string l_fovyKey = "fovy";

  bool l_ret = true;

#if LCF_LOGGING
  {
    cc::virtcam::CameraPositions* l_cams = dynamic_cast<cc::virtcam::CameraPositions*>(f_codingManager->getItem(l_Virtcam)); // PRQA S 3077 
    if ( nullptr != l_cams )
    {
      pc::virtcam::VirtualCamera l_cam;
      getMemberByName(l_cams, f_key, l_cam);  // PRQA S 3803
      XLOG_INFO_OS(g_EngineContext) << "Camera \"" << f_key << "\""<< " BEFORE LCF:\n" << l_cam.asString() << XLOG_ENDL;
    }
  }
#endif // LCF_LOGGING


  const float l_factor = 0.01f; // TODO: 1.0 / 1000.0;

  // eye
  const osg::Vec3f l_eye(static_cast<float>(f_lcfCam.m_eyePos.m_xCoord_cm), static_cast<float>(f_lcfCam.m_eyePos.m_yCoord_cm), static_cast<float>(f_lcfCam.m_eyePos.m_zCoord_cm)); 
  l_ret = (setCodingValue<pc::virtcam::VirtualCamera, osg::Vec3f>(l_Virtcam, f_key, l_eyeKey, f_codingManager, l_eye * l_factor) && l_ret);

  // center
  const osg::Vec3f l_center(static_cast<float>(f_lcfCam.m_centerPos.m_xCoord_cm), static_cast<float>(f_lcfCam.m_centerPos.m_yCoord_cm), static_cast<float>(f_lcfCam.m_centerPos.m_zCoord_cm)); 
  l_ret = (setCodingValue<pc::virtcam::VirtualCamera, osg::Vec3f>(l_Virtcam, f_key, l_centerKey, f_codingManager, l_center * l_factor) && l_ret);

  // up
  const osg::Vec3f l_up(static_cast<float>(f_lcfCam.m_upPos.m_xCoord_cm), static_cast<float>(f_lcfCam.m_upPos.m_yCoord_cm), static_cast<float>(f_lcfCam.m_upPos.m_zCoord_cm)); 
  l_ret = (setCodingValue<pc::virtcam::VirtualCamera, osg::Vec3f>(l_Virtcam, f_key, l_upKey, f_codingManager, l_up * l_factor) && l_ret);

  // fovy
  const float l_fovy = static_cast<float>(f_lcfCam.m_FOVy_deg); 
  l_ret = (setCodingValue<pc::virtcam::VirtualCamera, float>(l_Virtcam, f_key, l_fovyKey, f_codingManager, l_fovy) && l_ret);

  if ( !l_ret )
  {
    XLOG_INFO_OS(g_EngineContext) << "[IpcCodingReader] Could not set VirtCam: " << f_key << XLOG_ENDL;
  }

#if LCF_LOGGING
  {
    cc::virtcam::CameraPositions* l_cams = dynamic_cast<cc::virtcam::CameraPositions*>(f_codingManager->getItem(l_Virtcam)); // PRQA S 3077 
    if ( nullptr != l_cams )
    {
      pc::virtcam::VirtualCamera l_cam;
      getMemberByName(l_cams, f_key, l_cam);  // PRQA S 3803
      XLOG_INFO_OS(g_EngineContext) << "Camera \"" << f_key << "\""<< " AFTER LCF:\n" << l_cam.asString() << XLOG_ENDL;
    }
  }
#endif // LCF_LOGGING

  return l_ret;
}


bool IpcCodingReader::setStitchingLines(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::virtcam::CameraPositions* l_cams = dynamic_cast<cc::virtcam::CameraPositions*>(f_codingManager->getItem("VirtCam")); // PRQA S 3077 

  if ( nullptr != l_cams )
  {

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Stitching Lines BEFORE LCF: " 
                                  << "\n    FrontThreat " << l_cams->m_frontThreat.m_stitchingLines
                                  << "\n    RearThreat  " << l_cams->m_rearThreat.m_stitchingLines
                                  << "\n    FrontRight  " << l_cams->m_frontRight.m_stitchingLines
                                  << "\n    RearRight   " << l_cams->m_rearRight.m_stitchingLines
                                  << "\n    RearLeft    " << l_cams->m_rearLeft.m_stitchingLines
                                  << "\n    FrontLeft   " << l_cams->m_frontLeft.m_stitchingLines
                                  << XLOG_ENDL;
#endif // LCF_LOGGING

    l_cams->m_frontThreat.m_stitchingLines = lcfStitch2Vec( f_lcfData->m_Data.m_bumperViewStitchingAngle_Front );
    l_cams->m_rearThreat.m_stitchingLines  = lcfStitch2Vec( f_lcfData->m_Data.m_bumperViewStitchingAngle_Rear );
    l_cams->m_persFront.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Front );
    l_cams->m_persRear.m_stitchingLines    = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Rear );
    l_cams->m_persLeft.m_stitchingLines    = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Left );
    l_cams->m_persRight.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Right );
    l_cams->m_frontRight.m_stitchingLines  = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_FR );
    l_cams->m_rearRight.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_RR );
    l_cams->m_rearLeft.m_stitchingLines    = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_RL );
    l_cams->m_frontLeft.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_FL );
    l_cams->m_frontWheelLeft.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_frontWheelLeftViewStitchingAngle );
    l_cams->m_rearWheelRight.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_frontWheelRightViewStitchingAngle );


    // l_cams->m_frontThreat.m_stitchingLines = lcfStitch2Vec( f_lcfData->m_Data.m_bumperViewStitchingAngle_Front_Vert );
    // l_cams->m_rearThreat.m_stitchingLines  = lcfStitch2Vec( f_lcfData->m_Data.m_bumperViewStitchingAngle_Rear_Vert );
    // l_cams->m_persLeft.m_stitchingLines    = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Left_Vert );
    // l_cams->m_persRight.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Right_Vert );
#if ENABLE_VERTICAL_MODE
    l_cams->m_vertPersFront.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Front_Vert );
    l_cams->m_vertPersRear.m_stitchingLines    = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_Rear_Vert );
    l_cams->m_vertFrontRight.m_stitchingLines  = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_FR_Vert );
    l_cams->m_vertRearRight.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_RR_Vert );
    l_cams->m_vertRearLeft.m_stitchingLines    = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_RL_Vert );
    l_cams->m_vertFrontLeft.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_perspectiveViewStitchingAngle_FL_Vert );
    l_cams->m_vertWheelLeftView.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_frontWheelLeftViewStitchingAngle_Vert );
    l_cams->m_vertWheelRightView.m_stitchingLines   = lcfStitch2Vec( f_lcfData->m_Data.m_frontWheelRightViewStitchingAngle_Vert );
#endif
    l_cams->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Stitching Lines AFTER LCF: " 
                                  << "\n    FrontThreat " << l_cams->m_frontThreat.m_stitchingLines
                                  << "\n    RearThreat  " << l_cams->m_rearThreat.m_stitchingLines
                                  << "\n    FrontRight  " << l_cams->m_frontRight.m_stitchingLines
                                  << "\n    RearRight   " << l_cams->m_rearRight.m_stitchingLines
                                  << "\n    RearLeft    " << l_cams->m_rearLeft.m_stitchingLines
                                  << "\n    FrontLeft   " << l_cams->m_frontLeft.m_stitchingLines
                                  << XLOG_ENDL;
#endif // LCF_LOGGING

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "Persp. Views Stitching lines not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyLcfData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_allCamerasSet        = modifyVirtualCameras(f_codingManager, f_lcfData);
  bool l_vehicleContourData   = modifyVehicleContourData(f_codingManager, f_lcfData);
  bool l_vehicleContourSet    = modifyVehicleContourParameters(f_codingManager, f_lcfData);
  bool l_carMechanicalDataSet = modifyCarMechanicalData(f_codingManager, f_lcfData);
  bool l_ussZonesSet          = modifyUssZones(f_codingManager, f_lcfData);
  bool l_gbcSet               = modifyGbcData(f_codingManager, f_lcfData);
  bool l_viewModeSmSet        = modifyViewModeSm(f_codingManager, f_lcfData);
  bool l_bowlSet              = modifyBowlData(f_codingManager, f_lcfData);
  // bool l_modelAnimSet         = modifyVehicleAnimData(f_codingManager, f_lcfData);
  bool l_vehicleLCF           = modifyVehicleLCF(f_codingManager, f_lcfData);
  bool l_wheelTracksSet       = modifyWheelTracksOverlayData(f_codingManager, f_lcfData);
  bool l_zoomscaleSet         = modifyHuHemisphereData(f_codingManager,f_lcfData);
  bool l_calibSwitch          = modifyCalibSwitchData(f_codingManager,f_lcfData);
  bool l_floorPlate           = modifyFloorPlateData(f_codingManager,f_lcfData);
  bool l_floorPlateSm         = modifyFloorPlateSmData(f_codingManager,f_lcfData);
  bool l_fisheyeView          = modifyFisheyeViewData(f_codingManager,f_lcfData);
  bool l_fisheyeCropView      = modifyFisheyeCropViewData(f_codingManager,f_lcfData);

  // keep Plan View for the end -> info will be printed for LSMG
  bool l_planSet              = modfyPlanView(f_codingManager, f_lcfData);

  bool l_parkingSpace         = modifyParkingSpaceData(f_codingManager, f_lcfData);
  bool l_tileOverlay          = modifyTileOverlayData(f_codingManager, f_lcfData);
  bool l_freeParking          = modifyFreeParkingData(f_codingManager, f_lcfData);
  bool l_parkingSlot          = modifyParkingSlotData(f_codingManager, f_lcfData);
  //bool l_odoConverter         = modifyOdometryConverterData(f_codingManager, f_lcfData);

  XLOG_INFO_OS(g_EngineContext) << "LCF has been parsed and is being considered from this point on..." << XLOG_ENDL;

  bool l_ret = (l_allCamerasSet       &&
               l_vehicleContourData   &&
               l_vehicleContourSet    &&
               l_carMechanicalDataSet &&
               l_ussZonesSet          &&
               l_gbcSet               &&
               l_viewModeSmSet        &&
               l_bowlSet              &&
              //  l_modelAnimSet         &&
               l_vehicleLCF           &&
               l_wheelTracksSet       &&
               l_zoomscaleSet         &&
               l_calibSwitch          &&
               l_floorPlate           &&
               l_floorPlateSm         &&
               l_fisheyeView          &&
               l_fisheyeCropView      &&
               l_planSet              &&
               l_parkingSpace         &&
               l_tileOverlay          &&
               l_freeParking          &&
               l_parkingSlot);

  return l_ret;
}


// bool IpcCodingReader::modifyCcfData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::CcfDaddy* f_ccfData)
// {

//   bool l_ret = true;

//   cc::core::VehicleCcf* l_ccf = dynamic_cast<cc::core::VehicleCcf*>(f_codingManager->getItem("CCF"));

//   //! distribute ccf...
//   if(0 != l_ccf)
//   {
//     l_ccf->m_ccf4x4InfoDisplay        = f_ccfData->m_Data.m_ccf4x4InfoDisplay;
//     l_ccf->m_ccfNFSVariant            = f_ccfData->m_Data.m_ccfNFSVariant;
//     l_ccf->m_ccfParkAssist            = f_ccfData->m_Data.m_ccfParkAssist;
//     l_ccf->m_ccfPerspectiveViews      = f_ccfData->m_Data.m_ccfPerspectiveViews;
//     l_ccf->m_ccfSeeThroughBonnet      = f_ccfData->m_Data.m_ccfSeeThroughBonnet;
//     l_ccf->m_ccfSteeringWheelPosn     = f_ccfData->m_Data.m_ccfSteeringWheelPosn;
//     l_ccf->m_ccfTowBar                = f_ccfData->m_Data.m_ccfTowBar;
//     l_ccf->m_ccfUltrasonicSensorFit   = f_ccfData->m_Data.m_ccfUltrasonicSensorFit;
//     l_ccf->m_ccfVehicleType           = f_ccfData->m_Data.m_ccfVehicleType;
//     l_ccf->m_ccfVisionLegislation     = f_ccfData->m_Data.m_ccfVisionLegislation;
//     l_ccf->m_ccfDoors                 = f_ccfData->m_Data.m_ccfDoors;
//     l_ccf->m_ccfCameraSensorFitment   = f_ccfData->m_Data.m_ccfCameraSensorFitment;
//     l_ccf->m_ccfDriveAssist           = f_ccfData->m_Data.m_ccfDriveAssist;
//     l_ccf->dirty();

//     XLOG_INFO_OS(g_EngineContext) << "Received CCF:\n" << l_ccf->asString() << XLOG_ENDL;
//   }
//   else
//   {
//     XLOG_ERROR_OS(g_EngineContext) << "Could not fetch CCF struct from coding manager!"  << XLOG_ENDL;
//     l_ret = false;
//   }

//   pc::vehicle::MechanicalData* l_mechanicalData = dynamic_cast<pc::vehicle::MechanicalData*>(f_codingManager->getItem("VehicleMechanicalData"));
//   if(0 != l_mechanicalData)
//   {
//     if (cc::core::STEERING_WHEEL_POSITION_LEFT_HAND_DRIVE == f_ccfData->m_Data.m_ccfSteeringWheelPosn)
//     {
//       l_mechanicalData->m_leftHandDrive = true;
//     }
//     else
//     {
//       l_mechanicalData->m_leftHandDrive = false;
//     }

//     switch (f_ccfData->m_Data.m_ccfDoors)
//     {
//       case cc::core::CCF_DOORS_2DOOR: l_mechanicalData->m_nmbrDoors = 2; break;
//       case cc::core::CCF_DOORS_3DOOR: l_mechanicalData->m_nmbrDoors = 3; break;
//       case cc::core::CCF_DOORS_4DOOR: l_mechanicalData->m_nmbrDoors = 4; break;
//       case cc::core::CCF_DOORS_5DOOR: l_mechanicalData->m_nmbrDoors = 5; break;
//       default: break; // nmbrDoors has default value: 5
//     }
//     l_mechanicalData->dirty();
//   }
//   else
//   {
//     XLOG_ERROR_OS(g_EngineContext) << "Could not fetch Mechanical Data struct from coding manager!"  << XLOG_ENDL;
//     l_ret = false;
//   }

//   return l_ret;

// }


bool IpcCodingReader::modifyVirtualCameras(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

//! Virtual Camera Positions
  l_ret = (setVirtualCamera( f_codingManager, "plan",                    f_lcfData->m_Data.m_planView                    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "horiParkingPlan",         f_lcfData->m_Data.m_planView_Parking            ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "front",                   f_lcfData->m_Data.m_frontView                   ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rear",                    f_lcfData->m_Data.m_rearView                    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "leftView",                f_lcfData->m_Data.m_leftView                    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rightView",               f_lcfData->m_Data.m_rightView                   ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "frontWheelLeft",          f_lcfData->m_Data.m_frontWheelLeftView          ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "frontWheelRight",         f_lcfData->m_Data.m_frontWheelRightView         ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rearWheelLeft",           f_lcfData->m_Data.m_rearWheelLeftView           ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rearWheelRight",          f_lcfData->m_Data.m_rearWheelRightView          ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "frontThreat",             f_lcfData->m_Data.m_frontBumperView             ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rearThreat",              f_lcfData->m_Data.m_rearBumperView              ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "persFront",               f_lcfData->m_Data.m_perspectiveView_Front       ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "persRear",                f_lcfData->m_Data.m_perspectiveView_Rear        ) && l_ret);
  //l_ret = (setVirtualCamera( f_codingManager, "persLeft",                f_lcfData->m_Data.m_perspectiveView_Left        ) && l_ret);
  //l_ret = (setVirtualCamera( f_codingManager, "persRight",               f_lcfData->m_Data.m_perspectiveView_Right       ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "frontLeft",               f_lcfData->m_Data.m_perspectiveView_FrontLeft   ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rearLeft",                f_lcfData->m_Data.m_perspectiveView_RearLeft    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "frontRight",              f_lcfData->m_Data.m_perspectiveView_FrontRight  ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "rearRight",               f_lcfData->m_Data.m_perspectiveView_RearRight   ) && l_ret);
#if ENABLE_VERTICAL_MODE
  l_ret = (setVirtualCamera( f_codingManager, "vertPlan",                    f_lcfData->m_Data.m_planView_Vert                    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertParkingPlan",             f_lcfData->m_Data.m_planView_Parking_Vert            ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertFront",                   f_lcfData->m_Data.m_frontView_Vert                   ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertRear",                    f_lcfData->m_Data.m_rearView_Vert                    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertLeftView",                f_lcfData->m_Data.m_leftView_Vert                    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertRightView",               f_lcfData->m_Data.m_rightView_Vert                   ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertWheelLeftView",           f_lcfData->m_Data.m_frontWheelLeftView_Vert          ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertWheelRightView",          f_lcfData->m_Data.m_frontWheelRightView_Vert         ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertRearWheelLeft",           f_lcfData->m_Data.m_rearWheelLeftView_Vert           ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertRearWheelRight",          f_lcfData->m_Data.m_rearWheelRightView_Vert          ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertFrontThreat",             f_lcfData->m_Data.m_frontBumperView_Vert             ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertRearThreat",              f_lcfData->m_Data.m_rearBumperView_Vert              ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertPersFront",               f_lcfData->m_Data.m_perspectiveView_Front_Vert       ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertPersRear",                f_lcfData->m_Data.m_perspectiveView_Rear_Vert        ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertPersLeft",                f_lcfData->m_Data.m_perspectiveView_Left_Vert        ) && l_ret);
  // l_ret = (setVirtualCamera( f_codingManager, "vertPersRight",               f_lcfData->m_Data.m_perspectiveView_Right_Vert       ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertFrontLeft",               f_lcfData->m_Data.m_perspectiveView_FrontLeft_Vert   ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertRearLeft",                f_lcfData->m_Data.m_perspectiveView_RearLeft_Vert    ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertFrontRight",              f_lcfData->m_Data.m_perspectiveView_FrontRight_Vert  ) && l_ret);
  l_ret = (setVirtualCamera( f_codingManager, "vertRearRight",               f_lcfData->m_Data.m_perspectiveView_RearRight_Vert   ) && l_ret);
#endif
//! stitching lines
  l_ret = (setStitchingLines(f_codingManager, f_lcfData) && l_ret);

  return l_ret;

}

bool IpcCodingReader::modifyVehicleContourData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::core::VehicleContour* l_vehicleContour = dynamic_cast<cc::core::VehicleContour*> (f_codingManager->getItem("VehicleContour")); // PRQA S 3077 
  if (l_vehicleContour != nullptr) 
  {
    const unsigned int l_numContourPntsFront = static_cast<unsigned int> (f_lcfData->m_Data.m_vehContourParam.m_numContourPntsFront);
    if (cc::core::VehicleContour::NUM_POINTS_FRONT != l_numContourPntsFront)
    {
      XLOG_ERROR_OS(g_EngineContext) << "VehicleContour: unexpected number of front points received (" << l_numContourPntsFront << ")" << XLOG_ENDL;
      l_ret = false;
    }
    const unsigned int l_numContourPntsSide  = static_cast<unsigned int> (f_lcfData->m_Data.m_vehContourParam.m_numContourPntsSide);
    if (cc::core::VehicleContour::NUM_POINTS_SIDE != l_numContourPntsSide)
    {
      XLOG_ERROR_OS(g_EngineContext) << "VehicleContour: unexpected number of side points received (" << l_numContourPntsSide << ")" << XLOG_ENDL;
      l_ret = false;
    }
    const unsigned int l_numContourPntsRear  = static_cast<unsigned int> (f_lcfData->m_Data.m_vehContourParam.m_numContourPntsRear);
    if (cc::core::VehicleContour::NUM_POINTS_REAR != l_numContourPntsRear)
    {
      XLOG_ERROR_OS(g_EngineContext) << "VehicleContour: unexpected number of rear points received (" << l_numContourPntsRear << ")" << XLOG_ENDL;
      l_ret = false;
    }

    //XLOG_INFO_OS(g_EngineContext) << "VehicleContour BEFORE IPC:\n" << *l_vehicleContour << XLOG_ENDL;

    //! front points
    for (unsigned int i = 0u; i < cc::core::VehicleContour::NUM_POINTS_FRONT; ++i)
    {
      float l_x = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_vehContourFrontX[i]);
      float l_y = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_vehContourFrontY[i]);
      l_vehicleContour->setFront(i, osg::Vec2f(pc::util::mm2m(l_x), pc::util::mm2m(l_y)));
    }
    //! side points
    for (unsigned int i = 0u; i < cc::core::VehicleContour::NUM_POINTS_SIDE; ++i)
    {
      float l_x = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_vehContourSideX[i]);
      float l_y = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_vehContourSideY[i]);
      l_vehicleContour->setSide(i, osg::Vec2f(pc::util::mm2m(l_x), pc::util::mm2m(l_y)));
    }
    //! rear points
    for (unsigned int i = 0u; i < cc::core::VehicleContour::NUM_POINTS_REAR; ++i)
    {
      float l_x = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_vehContourRearX[i]);
      float l_y = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_vehContourRearY[i]);
      l_vehicleContour->setRear(i, osg::Vec2f(pc::util::mm2m(l_x), pc::util::mm2m(l_y)));
    }
    //! unfolded mirror points
    for (unsigned int i = 0u; i < cc::core::VehicleContour::NUM_POINTS_MIRROR_UNFOLDED; ++i)
    {
      float l_x = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedOutX[i]);
      float l_y = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedOutY[i]);
      l_vehicleContour->setUnfoldedMirror(i, osg::Vec2f(pc::util::mm2m(l_x), pc::util::mm2m(l_y)));
    }
    //! folded mirror points
    for (unsigned int i = 0u; i < cc::core::VehicleContour::NUM_POINTS_MIRROR_FOLDED; ++i)
    {
      float l_x = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedInX[i]);
      float l_y = static_cast<float> (f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedInY[i]);
      l_vehicleContour->setFoldedMirror(i, osg::Vec2f(pc::util::mm2m(l_x), pc::util::mm2m(l_y)));
    }
    l_vehicleContour->dirty();

    //XLOG_INFO_OS(g_EngineContext) << "VehicleContour AFTER IPC:\n" << *l_vehicleOutline << XLOG_ENDL;
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "VehicleContour not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyVehicleContourParameters(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)  // PRQA S 6041
{
  bool l_ret = true;

  // This is a hard-coded index of the side contour values
  // As long as there is not a better solution to mark the mirror point,
  // it is fixed as #2 from side. Based on data from L663_90
  const unsigned int l_hardCodedMirrorSideIndex = 2u;

  //! Vehicle Contour
  cc::assets::trajectory::VehicleOutline* l_vehicleOutline =  dynamic_cast<cc::assets::trajectory::VehicleOutline*>(f_codingManager->getItem("VehicleOutline")); // PRQA S 3077 

  if ( nullptr != l_vehicleOutline )
  {

    const unsigned int l_numContourPntsFront = static_cast<unsigned int>( f_lcfData->m_Data.m_vehContourParam.m_numContourPntsFront );
    const unsigned int l_numContourPntsSide  = static_cast<unsigned int>( f_lcfData->m_Data.m_vehContourParam.m_numContourPntsSide );
    const unsigned int l_numContourPntsRear  = static_cast<unsigned int>( f_lcfData->m_Data.m_vehContourParam.m_numContourPntsRear );
    l_vehicleOutline->m_pointCount = l_numContourPntsFront + l_numContourPntsSide + l_numContourPntsRear;

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "[IpcCodingReader::modifyVehicleContourParameters] " 
                                  << "\n   NumberContourPntsFront: " << l_numContourPntsFront
                                  << "\n   NumberContourPntsSide: "  << l_numContourPntsSide
                                  << "\n   NumberContourPntsRear: "  << l_numContourPntsRear  << XLOG_ENDL;

    std::ostringstream l_stream;
    for(unsigned int i = 0u; i < l_numContourPntsFront; ++i)
    {
      vfc::int16_t l_vehContourFrontX = f_lcfData->m_Data.m_vehContourParam.m_vehContourFrontX[i];
      vfc::int16_t l_vehContourFrontY = f_lcfData->m_Data.m_vehContourParam.m_vehContourFrontY[i];
      l_stream << "\n    vehContourFront[" << i << "]: " << "X: " << l_vehContourFrontX << ", " << "Y: " << l_vehContourFrontY;  // PRQA S 3803
    }
    l_stream << "\n----------------";  // PRQA S 3803
    for(unsigned int i = 0u; i < l_numContourPntsSide; ++i)
    {
      vfc::int16_t l_vehContourSideX = f_lcfData->m_Data.m_vehContourParam.m_vehContourSideX[i];
      vfc::int16_t l_vehContourSideY = f_lcfData->m_Data.m_vehContourParam.m_vehContourSideY[i];
      l_stream << "\n    vehContourSide[" << i << "]: " << "X: " << l_vehContourSideX << ", " << "Y: " << l_vehContourSideY;  // PRQA S 3803
    }
    l_stream << "\n----------------";  // PRQA S 3803
    for(unsigned int i = 0u; i < l_numContourPntsRear; ++i)
    {
      vfc::int16_t l_vehContourRearX = f_lcfData->m_Data.m_vehContourParam.m_vehContourRearX[i];
      vfc::int16_t l_vehContourRearY = f_lcfData->m_Data.m_vehContourParam.m_vehContourRearY[i];
      l_stream << "\n    vehContourRear[" << i << "]: "  << "X: " << l_vehContourRearX << ", " << "Y: " << l_vehContourRearY;  // PRQA S 3803
    }
    XLOG_INFO_OS(g_EngineContext) << "Raw LCF Vehicle Countour Points: " << l_stream.str() << XLOG_ENDL;

    XLOG_INFO_OS(g_EngineContext) << "VehicleContour BEFORE LCF:\n" << l_vehicleOutline->asString() << XLOG_ENDL;

#endif // LCF_LOGGING

    unsigned int l_pointCounter = 0u;
    //! Connection:
    //! Need to revert order. PMA clockwise. SVS counter clockwise. Front > Side > Rear.

    // 0 - Front points
    for( unsigned int i = (l_numContourPntsFront); i > 0u; --i)
    {
      vfc::int16_t l_vehContourFrontX = f_lcfData->m_Data.m_vehContourParam.m_vehContourFrontX[i-1u];
      vfc::int16_t l_vehContourFrontY = f_lcfData->m_Data.m_vehContourParam.m_vehContourFrontY[i-1u];
      l_vehicleOutline->setPoint(l_pointCounter, mm2m( static_cast<float>(l_vehContourFrontX) ), mm2m( static_cast<float>(l_vehContourFrontY) ));  // PRQA S 3803

      ++l_pointCounter;
    }
    // Front Bumper Indices
    l_vehicleOutline->m_frontBumperStartPointIndex = 0u;
    l_vehicleOutline->m_frontBumperEndPointIndex   = l_pointCounter - 1u;

    // 1 - Side points
    for(unsigned int i = 1u; i < (l_numContourPntsSide); ++i) // Skip first -> it is a replica of front sector's last point
    {
      vfc::int16_t l_vehContourSideX = f_lcfData->m_Data.m_vehContourParam.m_vehContourSideX[i];
      vfc::int16_t l_vehContourSideY = f_lcfData->m_Data.m_vehContourParam.m_vehContourSideY[i];
      l_vehicleOutline->setPoint(l_pointCounter, mm2m(static_cast<float>(l_vehContourSideX)), mm2m(static_cast<float>(l_vehContourSideY)));  // PRQA S 3803

      // Mirror Point - hard coded to l_hardCodedMirrorSideIndex-th index of the side contour
      if(l_hardCodedMirrorSideIndex == i)
      {
        l_vehicleOutline->m_mirrorPointIndex = l_pointCounter;
      }

      ++l_pointCounter;
    }

    // 2 - Rear points
    l_vehicleOutline->m_rearBumperStartPointIndex  = l_pointCounter - 1u; // First from rear = last from side
    for(unsigned int i = 1u; i < (l_numContourPntsRear); ++i) // Skip first -> it is a replica of side sector's last point
    {
      vfc::int16_t l_vehContourRearX = f_lcfData->m_Data.m_vehContourParam.m_vehContourRearX[i];
      vfc::int16_t l_vehContourRearY = f_lcfData->m_Data.m_vehContourParam.m_vehContourRearY[i];
      l_vehicleOutline->setPoint(l_pointCounter, mm2m(static_cast<float>(l_vehContourRearX)), mm2m(static_cast<float>(l_vehContourRearY)));  // PRQA S 3803

      ++l_pointCounter;
    }

    l_vehicleOutline->m_pointCount = l_pointCounter;

    l_vehicleOutline->m_rearBumperEndPointIndex    = l_pointCounter - 1u;
    l_vehicleOutline->m_pointCount                 = l_pointCounter;

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "A total of " << static_cast<int>(l_pointCounter) << " contour points has been set" << XLOG_ENDL;
#endif

    l_vehicleOutline->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "VehicleContour AFTER LCF:\n" << l_vehicleOutline->asString() << XLOG_ENDL;

    // What shall we do with these? - they seem to be all zero!
    {
      const unsigned int l_numContourPntsMirrorOut = 4u;
      const unsigned int l_numContourPntsMirrorIn = 4u;

      for( unsigned int i = 0u; i < l_numContourPntsMirrorOut; ++i)
      {
        vfc::int16_t l_vehContourX = f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedOutX[i];
        vfc::int16_t l_vehContourY = f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedOutY[i];
        l_stream << "\n    ContourPntsMirrorOut[" << i << "]: " << "X: " << l_vehContourX << ", " << "Y: " << l_vehContourY;  // PRQA S 3803
      }
      l_stream << "\n----------------";  // PRQA S 3803
      for( unsigned int i = 0u; i < l_numContourPntsMirrorIn; ++i)
      {
        vfc::int16_t l_vehContourX = f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedInX[i];
        vfc::int16_t l_vehContourY = f_lcfData->m_Data.m_vehContourParam.m_mirrorContourFoldedInY[i];
        l_stream << "\n    ContourPntsMirrorIn[" << i << "]: "  << "X: " << l_vehContourX << ", " << "Y: " << l_vehContourY;  // PRQA S 3803
      }
      XLOG_INFO_OS(g_EngineContext) << "UNUSED Raw LCF Vehicle Countour Points: " << l_stream.str() << XLOG_ENDL;
    }
#endif // LCF_LOGGING

    l_ret = true;
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "VehicleOutline not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyCarMechanicalData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  //! Vehicle Parameters
#if LCF_LOGGING
  XLOG_INFO_OS(g_EngineContext) << "[IpcCodingReader] Raw VehParams: "    << 
                                   "\n    LengthFront_u16:             "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehLengthFront              ) <<
                                   "\n    LengthRear_u16:              "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehLengthRear               ) <<
                                   "\n    HalfWidth_u16:               "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehHalfWidth                ) <<
                                   "\n    CornerRadiusFront_u16:       "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehCornerRadiusFront        ) <<
                                   "\n    CornerRadiusRear_u16:        "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehCornerRadiusRear         ) <<
                                   "\n    Wheelbase_u16:               "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehWheelbase                ) <<
                                   "\n    RWheel_u16:                  "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_vehRWheel                   ) <<
                                   "\n    WheelHalfWidth_u16:          "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_wheelHalfWidth              ) <<
                                   "\n    HitchLength_u16:             "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_hitchLength                 ) <<
                                   "\n    ActiveRearAxleSteeringPMA_b: "  << static_cast<unsigned int>( f_lcfData->m_Data.m_vehParams.m_activeRearAxleSteeringPMA_b ) << XLOG_ENDL;
#endif // LCF_LOGGING

  //! Mechanical Data LCFs
  pc::vehicle::MechanicalData* l_mechData = dynamic_cast<pc::vehicle::MechanicalData*>(f_codingManager->getItem("VehicleMechanicalData")); // PRQA S 3077 
  if ( nullptr != l_mechData )
  {
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "MechanicalData BEFORE LCF:\n" << l_mechData->asString() << XLOG_ENDL;
#endif // LCF_LOGGING

    l_mechData->m_wheelbase                 = mm2m( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehWheelbase) );
    l_mechData->m_wheelRadius               = mm2m( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehRWheel) );
    l_mechData->m_wheelWidthFront           = mm2m( 2.f * static_cast<float>(f_lcfData->m_Data.m_vehParams.m_wheelHalfWidth) );
    l_mechData->m_wheelWidthRear            = mm2m( 2.f * static_cast<float>(f_lcfData->m_Data.m_vehParams.m_wheelHalfWidth) );
    l_mechData->m_axleToBumperDistanceFront = mm2m( ( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehLengthFront) - static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehWheelbase) ) );
    l_mechData->m_axleToBumperDistanceRear  = mm2m( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehLengthRear) );
    l_mechData->m_width                     = mm2m( 2.f * static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehHalfWidth) );
    l_mechData->m_trackFront                = cm2m( static_cast<float>(f_lcfData->m_Data.m_vehWheelTrack_Front_cm) );
    l_mechData->m_trackRear                 = cm2m( static_cast<float>(f_lcfData->m_Data.m_vehWheelTrack_Rear_cm) );


    // set as dirty
    l_mechData->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "MechanicalData AFTER LCF:\n" << l_mechData->asString() << XLOG_ENDL;
#endif // LCF_LOGGING

    l_ret = true;
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "VehicleMechanicalData not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyUssZones(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)  // PRQA S 6040  // PRQA S 6041
{
  bool l_ret = true;

  //! LSMG Sectors (USS Zones);

  cc::core::PasZonesDescription* l_ultrasonic = dynamic_cast<cc::core::PasZonesDescription*>(f_codingManager->getItem("UltrasonicZones"));//! USS Zones // PRQA S 3077 
  cc::assets::trajectory::VehicleOutline* l_vehicleOutline =  dynamic_cast<cc::assets::trajectory::VehicleOutline*>(f_codingManager->getItem("VehicleOutline")); //! Vehicle Contour // PRQA S 3077 
  if ( (l_ultrasonic != nullptr) && (l_vehicleOutline != nullptr))
  {
    cc::assets::splineoverlay::UssZoneAdaptation l_adaptation;

    const unsigned int l_totalNumSectors = 16u;
    const unsigned int l_numFrontSectors = 2u;
    const unsigned int l_numSideSectors  = 4u;
    const unsigned int l_numRearSectors  = 2u;

    unsigned int l_sectorCounter = 0u;

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "[IpcCodingReader] DispFoV (USS Zones): "  << XLOG_ENDL;
    XLOG_INFO_OS(g_EngineContext) << "Before LCF:\n" << *l_ultrasonic << XLOG_ENDL;
#endif // LCF_LOGGING

    // Set the vehicle contour
    for (unsigned int i = 0u; i < l_vehicleOutline->m_pointCount; ++i)
    {
      l_adaptation.carBoundingRect(*l_vehicleOutline->getPointPtr(i));
    }

    // Get the bounding rect of the inner points
    l_sectorCounter = 0u;
    for (int i = static_cast<int>(l_numFrontSectors - 1u); i >= 0; --i)
    {
      l_adaptation.insertToUssBoundingRect(osg::Vec2f(static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA1X_ps16[i]), static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA1Y_ps16[i])) * 0.001f);
      l_sectorCounter++;
    }
    for (int i = static_cast<int>(l_numSideSectors - 1u); i >= 0; --i)
    {
      l_adaptation.insertToUssBoundingRect(osg::Vec2f(static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaLeftA1X_ps16[i]), static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaLeftA1Y_ps16[i])) * 0.001f); 
      l_sectorCounter++;
    }
    for (unsigned int i = 0u; i < l_numRearSectors; ++i)
    {
      l_adaptation.insertToUssBoundingRect(osg::Vec2f(static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaRearA2X_ps16[i]), -static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaRearA2Y_ps16[i])) * 0.001f); 
      l_sectorCounter++;
    }
    l_adaptation.closeUssBoundingRect();

    l_adaptation.setIsScaleNeeded(!l_adaptation.getCarBoundingRect().rectInside(l_adaptation.getUssBoundingRect()));

    l_sectorCounter = 0u;

    for (int i = static_cast<int>(l_numFrontSectors-1u); i >= 0; --i)
    {
      osg::Vec4f l_ussZone(
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA1X_ps16[i]), 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA1Y_ps16[i]), 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA4X_ps16[i]), 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA4Y_ps16[i]) ); 
      osg::Vec4f l_line = l_adaptation.scaleUssZone(l_ussZone * 0.001f);

      l_ultrasonic->setLeftBorderLine(l_sectorCounter, l_line); // [mm] -> [m]
      l_sectorCounter++;
    }

    for (int i = static_cast<int>(l_numSideSectors-1u); i >= 0; --i)
    {
      osg::Vec4f l_ussZone(
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaLeftA1X_ps16[i]), 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaLeftA1Y_ps16[i]), 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaLeftA4X_ps16[i]), 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaLeftA4Y_ps16[i]) ); 
      osg::Vec4f l_line = l_adaptation.scaleUssZone(l_ussZone * 0.001f);

      l_ultrasonic->setLeftBorderLine(l_sectorCounter, l_line); // [mm] -> [m]
      l_sectorCounter++;
    }

    // In this case the order is correct, since PMA is using the rear-right part of the vehicle in a clock-wise manner.
    for (unsigned int i = 0u; i < l_numRearSectors; ++i)
    {
      osg::Vec4f l_ussZone(
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaRearA2X_ps16[i]), 
      -static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaRearA2Y_ps16[i]), // flip ! 
        static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaRearA3X_ps16[i]), 
      -static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaRearA3Y_ps16[i]) ); // flip ! 
      osg::Vec4f l_line = l_adaptation.scaleUssZone(l_ussZone * 0.001f);

      l_ultrasonic->setLeftBorderLine(l_sectorCounter, l_line); // [mm] -> [m]
      l_sectorCounter++;
    }

    // Now mirror the rest...
    while ( l_sectorCounter < (l_totalNumSectors-1u) )
    {
      osg::Vec4f l_line = l_ultrasonic->getLeftBorderLine(l_totalNumSectors - l_sectorCounter - 2u);
      l_line.y() = -l_line.y();
      l_line.w() = -l_line.w();
      l_ultrasonic->setLeftBorderLine(l_sectorCounter, l_line); // already in [mm]
      l_sectorCounter++;
    }

    // ... and close the loop with the first sector !
    osg::Vec4f l_ussZone(
      static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA2X_ps16[l_numFrontSectors - 1u]), 
      static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA2Y_ps16[l_numFrontSectors - 1u]), 
      static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA3X_ps16[l_numFrontSectors - 1u]), 
      static_cast<float>(f_lcfData->m_Data.m_PAS_DispFoV.m_dispAreaFrontA3Y_ps16[l_numFrontSectors - 1u]) ); 
    osg::Vec4f l_line = l_adaptation.scaleUssZone(l_ussZone * 0.001f);

    l_ultrasonic->setLeftBorderLine(l_sectorCounter, l_line); // [mm] -> [m]
    l_sectorCounter++;


    //! Now do the middle lines... In this case we won't go for very advance maths, but rather average the two nearest and two furtherst points
    for (unsigned int i = 0u; i < l_totalNumSectors; ++i)
    {
      unsigned int l_left  = i;
      unsigned int l_right = ((i+l_totalNumSectors)-1u) % l_totalNumSectors;
      osg::Vec4f l_leftLine  = l_ultrasonic->getLeftBorderLine(l_left);
      osg::Vec4f l_rightLine = l_ultrasonic->getLeftBorderLine(l_right);
      osg::Vec4f l_middle = (l_leftLine + l_rightLine) / 2.0f; // element-wise
      l_ultrasonic->setMiddleBorderLine(i, l_middle);
    }

    l_ultrasonic->dirty();

    // Align all zones to the car contour
    for (unsigned int iCarCtr = 0u; iCarCtr < l_vehicleOutline->m_pointCount + 1u; ++iCarCtr)
    {
      osg::Vec4f l_carContourSegmentLeft;
      osg::Vec4f l_carContourSegmentRight;

      // Get a segment of the car contour according to the points of it
      if (iCarCtr == 0u)
      {
        osg::Vec2f firstPoint(*l_vehicleOutline->getPointPtr(0u));
        l_carContourSegmentLeft.z() =  firstPoint.x();
        l_carContourSegmentLeft.w() =  firstPoint.y();
        l_carContourSegmentLeft.x() =  firstPoint.x();
        l_carContourSegmentLeft.y() = -firstPoint.y();
      }
      else if (iCarCtr == l_vehicleOutline->m_pointCount)
      {
        osg::Vec2f lastPoint(*l_vehicleOutline->getPointPtr(iCarCtr - 1u));
        l_carContourSegmentLeft.x() =  lastPoint.x();
        l_carContourSegmentLeft.y() =  lastPoint.y();
        l_carContourSegmentLeft.z() =  lastPoint.x();
        l_carContourSegmentLeft.w() = -lastPoint.y();
      }
      else
      {
        osg::Vec2f pt1(*l_vehicleOutline->getPointPtr(iCarCtr - 1u));
        osg::Vec2f pt2(*l_vehicleOutline->getPointPtr(iCarCtr));
        l_carContourSegmentLeft.x() = pt1.x();
        l_carContourSegmentLeft.y() = pt1.y();
        l_carContourSegmentLeft.z() = pt2.x();
        l_carContourSegmentLeft.w() = pt2.y();
      }

      // Create the mirror of the left to get the right contour segment
      l_carContourSegmentRight = l_carContourSegmentLeft;
      l_carContourSegmentRight.y() *= -1.0f;
      l_carContourSegmentRight.w() *= -1.0f;

      // Align the corrsesponding USS zone to the contour side
      for (unsigned int iUssZone = 0u; iUssZone < static_cast<unsigned int>(cc::target::sysconf::E_ULTRASONIC_NUM_ZONES); ++iUssZone)
      {
        osg::Vec4f l_leftBorderLine   = l_ultrasonic->getLeftBorderLine(iUssZone);
        osg::Vec4f l_middleBorderLine = l_ultrasonic->getMiddleLine(iUssZone);
        if (l_adaptation.shiftUssLineToTheCarContour(l_carContourSegmentLeft, l_leftBorderLine) || l_adaptation.shiftUssLineToTheCarContour(l_carContourSegmentRight, l_leftBorderLine))
        {
          l_ultrasonic->setLeftBorderLine(iUssZone, l_leftBorderLine);
        }
        if (l_adaptation.shiftUssLineToTheCarContour(l_carContourSegmentLeft, l_middleBorderLine) || l_adaptation.shiftUssLineToTheCarContour(l_carContourSegmentRight, l_middleBorderLine))
        {
          l_ultrasonic->setMiddleBorderLine(iUssZone, l_middleBorderLine);
        }
      }
    }

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "After LCF:\n" << *l_ultrasonic << XLOG_ENDL;
#endif // LCF_LOGGING

    l_ret = true;
  } // l_ultrasonic
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "UltrasonicZones not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyGbcData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  // unsigned int l_factor_ds2ms = 100;      // conversion factor [deciseconds] -> [milliseconds]
  float l_factor_s2ms         = 1000.f;   // conversion factor [seconds] -> [milliseconds]

  //! GBC LCFs
  //! Transparent vehicle model
  cc::assets::common::TranspSmData* l_transpSm = dynamic_cast<cc::assets::common::TranspSmData*>( f_codingManager->getItem("TranspSm") ); // PRQA S 3077 
  if ( nullptr != l_transpSm )
  {

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "GBC Transparency Parameters BEFORE LCF:\n" << l_transpSm->asString() << XLOG_ENDL;
#endif

    // Timing parameters
    l_transpSm->m_transpDurnThreshold_ms    = static_cast<uint32_t>(f_lcfData->m_Data.m_gbcData.m_transpDurnThreshold_ms); // [ms] -> [ms]
    l_transpSm->m_opaqueDurnThreshold_ms    = static_cast<uint32_t>(f_lcfData->m_Data.m_gbcData.m_opaqueDurnThreshold_ms); // [ms] -> [ms]
    l_transpSm->m_transition2opaqueDuration = l_factor_s2ms * static_cast<float>( f_lcfData->m_Data.m_gbcData.m_transition2opaqueDuration_sec ); // [sec] -> [ms]
    l_transpSm->m_transition2transpDuration = l_factor_s2ms * static_cast<float>( f_lcfData->m_Data.m_gbcData.m_transition2transpDuration_sec ); // [sec] -> [ms]

    l_transpSm->m_transpDurnThreshold_Parking_ms    = static_cast<uint32_t>(f_lcfData->m_Data.m_gbcData.m_transpDurnThreshold_Parking_ms); // [ms] -> [ms]
    l_transpSm->m_opaqueDurnThreshold_Parking_ms    = static_cast<uint32_t>(f_lcfData->m_Data.m_gbcData.m_opaqueDurnThreshold_Parking_ms); // [ms] -> [ms]
    l_transpSm->m_transition2opaqueDuration_Parking = l_factor_s2ms * static_cast<float>( f_lcfData->m_Data.m_gbcData.m_transition2opaqueDuration_Parking_sec ); // [sec] -> [ms]
    l_transpSm->m_transition2transpDuration_Parking = l_factor_s2ms * static_cast<float>( f_lcfData->m_Data.m_gbcData.m_transition2transpDuration_Parking_sec ); // [sec] -> [ms]


    // Minimum driven distance (x and y)
    const float l_fullVehicleLength_m = mm2m( ( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehLengthFront) + static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehLengthRear) ) );
    l_transpSm->m_minDrivenDistX = 1.1f * l_fullVehicleLength_m; // add a safety 10% to the full vehicle length (the truth lies in the mask, not in the vehicle itself)

    const float l_fullVehicleWidth_m = mm2m( 2.f * static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehHalfWidth) );
    l_transpSm->m_minDrivenDistY = 1.1f * l_fullVehicleWidth_m; // add a safety 10% to the vehicle width (the truth lies in the mask, not in the vehicle itself)


    // vehicle transparency
    const float l_vehicleTransp = pc::util::clamp( static_cast<float>(f_lcfData->m_Data.m_gbcData.m_vehicleModelTransparency) / 100.f, 0.f, 1.f);
    l_transpSm->m_vehicleOpacity = 1.f - l_vehicleTransp; // LCF defines transparency, but SVS expects opacity

    // wheel transparency
    const float l_wheelTransp = pc::util::clamp( static_cast<float>(f_lcfData->m_Data.m_gbcData.m_wheelTransperancy) / 100.f, 0.f, 1.f);
    l_transpSm->m_wheelOpacity = 1.f - l_wheelTransp; // LCF defines transparency, but SVS expects opacity

    l_transpSm->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "GBC Transparency Parameters AFTER LCF:\n" << l_transpSm->asString() << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "TranspSm not found!" << XLOG_ENDL;
    l_ret = false;
  }

  //! Ground plate blurring
  cc::assets::common::BlurSmData* l_blurSm = dynamic_cast<cc::assets::common::BlurSmData*>( f_codingManager->getItem("BlurSm") ); // PRQA S 3077 
  if ( nullptr != l_blurSm )
  {

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "GBC Floor Blurring Parameters BEFORE LCF:\n" << l_blurSm->asString() << XLOG_ENDL;
#endif

    // Timing parameters
    // l_blurSm->m_blurDurnThreshold_ms    = static_cast<unsigned int>( f_lcfData->m_Data.m_gbcData.m_vehStaticGroundBlurThreshold_ms );
    l_blurSm->m_blurDurnThreshold_ms    = static_cast<unsigned int>( f_lcfData->m_Data.m_gbcData.m_opaqueDurnThreshold_ms );   // keep timing the same as vehicle model transpancy
    l_blurSm->m_deblurDurnThreshold_ms  = static_cast<unsigned int>( f_lcfData->m_Data.m_gbcData.m_vehStaticGroundBlurThreshold_ms );
    l_blurSm->m_anim2blurDuration       = l_factor_s2ms * static_cast<float>( f_lcfData->m_Data.m_gbcData.m_blurGroundPlateAnimationDuration_sec ); // [sec] -> [ms]
    l_blurSm->m_anim2normalDuration     = l_factor_s2ms * static_cast<float>( f_lcfData->m_Data.m_gbcData.m_blurGroundPlateAnimationDuration_sec ); // [sec] -> [ms]

    l_blurSm->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "GBC Floor Blurring Parameters AFTER LCF:\n" << l_blurSm->asString() << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "BlurSm not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyViewModeSm(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  unsigned int l_factor_s2ms = 1000u;      // conversion factor [seconds] -> [milliseconds]

  //! ViewMode State Machine
  StateMachineParameters* l_viewModeSm = dynamic_cast<StateMachineParameters*>( f_codingManager->getItem("ViewModeStateMachine") ); // PRQA S 3077 
  if ( nullptr != l_viewModeSm )
  {
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "ViewModeSm Parameters BEFORE LCF:\n" << l_viewModeSm->asString() << XLOG_ENDL;
#endif

    // l_viewModeSm->m_distTrigIn_m                  = cm2m( static_cast<float>( f_lcfData->m_Data.m_smDistTrigIn_cm ) );
    // l_viewModeSm->m_distTrigOut_m                 = cm2m( static_cast<float>( f_lcfData->m_Data.m_smDistTrigOut_cm ) );
    l_viewModeSm->m_speedTrigIn_kph               = static_cast<uint32_t>( f_lcfData->m_Data.m_smSpeedTrigIn_kph );
    l_viewModeSm->m_speedTrigOut_kph              = static_cast<uint32_t>( f_lcfData->m_Data.m_smSpeedTrigOut_kph );
    l_viewModeSm->m_steeringAngleTrigIn           = static_cast<uint32_t>( f_lcfData->m_Data.m_smSteeringAngleTrigIn );
    l_viewModeSm->m_steeringAngleTrigOut          = static_cast<uint32_t>( f_lcfData->m_Data.m_smSteeringAngleTrigOut );
    l_viewModeSm->m_unavlMsgDisplay_Timeout       = l_factor_s2ms * static_cast<uint32_t>( f_lcfData->m_Data.m_smUnavlMsgDisplay_Timeout_sec );
    l_viewModeSm->m_threatDuration                = l_factor_s2ms * static_cast<uint32_t>( f_lcfData->m_Data.m_smThreatDuration_sec );
    // l_viewModeSm->m_parkingScreen_Delay_ms        = static_cast<uint32_t>( f_lcfData->m_Data.m_smParkingScreen_Delay_ms );
    l_viewModeSm->m_sm_CpcShow_Delay_s            = static_cast<uint32_t>( f_lcfData->m_Data.m_smCpcShow_Delay_s );
    l_viewModeSm->m_vehicleModels                 = static_cast<uint32_t>(f_lcfData->m_Data.m_vehicleModel);

    l_viewModeSm->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "ViewModeSm Parameters AFTER LCF:\n" << l_viewModeSm->asString() << XLOG_ENDL;
#endif

    l_ret = true;
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "ViewModeStateMachine not found!" << XLOG_ENDL;
    l_ret = false;
  }


  return l_ret;
}

bool IpcCodingReader::modifyVehicleLCF(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::assets::overlaycallback::OverlayData* l_overlaySettings = dynamic_cast<cc::assets::overlaycallback::OverlayData*>( f_codingManager->getItem("OverlayData") ); // PRQA S 3077 
  if ( nullptr != l_overlaySettings )
  {
    #if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "OverlayData BEFORE LCF:\n" << l_overlaySettings->asString() << XLOG_ENDL;
    #endif // LCF_LOGGING

    l_overlaySettings->m_SATCAM_rear_tailgatePosition = static_cast<uint32_t>(f_lcfData->m_Data.m_satcamRearTailgatePosition);
    // l_overlaySettings->m_ICE_stallTimeout = f_lcfData->m_Data.m_iceStallTimeout_sec;
    l_overlaySettings->dirty();

    #if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "OverlayData AFTER LCF:\n" << l_overlaySettings->asString() << XLOG_ENDL;
    #endif // LCF_LOGGING

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "OverlayData not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyBowlData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  // TODO: height seems to contain length - why?

  bool l_ret = true;

  //! Big Bowl
  {
    pc::worker::bowlshaping::BowlShaperData* l_bowl = dynamic_cast<pc::worker::bowlshaping::BowlShaperData*>( f_codingManager->getItem("BowlShaperDefault") ); // PRQA S 3077 
    if ( nullptr != l_bowl )
    {
#if LCF_LOGGING
      XLOG_INFO_OS(g_EngineContext) << "BowlShaperDefault BEFORE LCF: " 
                                    << "\n    m_semiaxis.x():  " << l_bowl->m_bowlDefault.m_semiaxis.x()
                                    << "\n    m_semiaxis.y():  " << l_bowl->m_bowlDefault.m_semiaxis.y() << XLOG_ENDL;
#endif // LCF_LOGGING

      l_bowl->m_bowlDefault.m_semiaxis.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_bowlSize.m_bowlSizeLarge.m_bowlHeight_cm ) ); // TODO: change naming!!!
      l_bowl->m_bowlDefault.m_semiaxis.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_bowlSize.m_bowlSizeLarge.m_bowlWidth_cm ) );
      l_bowl->dirty();

#if LCF_LOGGING
      XLOG_INFO_OS(g_EngineContext) << "BowlShaperDefault AFTER LCF: " 
                                    << "\n    m_semiaxis.x():  " << l_bowl->m_bowlDefault.m_semiaxis.x()
                                    << "\n    m_semiaxis.y():  " << l_bowl->m_bowlDefault.m_semiaxis.y() << XLOG_ENDL;
#endif // LCF_LOGGING
      l_ret = ( l_ret && true );
    }
    else
    {
      XLOG_ERROR_OS(g_EngineContext) << "BowlShaperDefault not found!" << XLOG_ENDL;
      l_ret = false;
    }
  }

  //! Mid Bowl
  {
    pc::worker::bowlshaping::BowlShaperData* l_bowl = dynamic_cast<pc::worker::bowlshaping::BowlShaperData*>( f_codingManager->getItem("BowlShaperMedium") ); // PRQA S 3077 
    if ( nullptr != l_bowl )
    {
#if LCF_LOGGING
      XLOG_INFO_OS(g_EngineContext) << "BowlShaperMedium BEFORE LCF: " 
                                    << "\n    m_semiaxis.x():  " << l_bowl->m_bowlDefault.m_semiaxis.x()
                                    << "\n    m_semiaxis.y():  " << l_bowl->m_bowlDefault.m_semiaxis.y() << XLOG_ENDL;
#endif // LCF_LOGGING
      l_bowl->m_bowlDefault.m_semiaxis.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_bowlSize.m_bowlSizeMid.m_bowlHeight_cm ) ); // TODO: change naming!!!
      l_bowl->m_bowlDefault.m_semiaxis.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_bowlSize.m_bowlSizeMid.m_bowlWidth_cm ) );
      l_bowl->dirty();

#if LCF_LOGGING
      XLOG_INFO_OS(g_EngineContext) << "BowlShaperMedium AFTER LCF: " 
                                    << "\n    m_semiaxis.x():  " << l_bowl->m_bowlDefault.m_semiaxis.x()
                                    << "\n    m_semiaxis.y():  " << l_bowl->m_bowlDefault.m_semiaxis.y() << XLOG_ENDL;
#endif // LCF_LOGGING
      l_ret = ( l_ret && true );
    }
    else
    {
      XLOG_ERROR_OS(g_EngineContext) << "BowlShaperMedium not found!" << XLOG_ENDL;
      l_ret = false;
    }
  }

  //! Small Bowl
  {
    pc::worker::bowlshaping::BowlShaperData* l_bowl = dynamic_cast<pc::worker::bowlshaping::BowlShaperData*>( f_codingManager->getItem("BowlShaperSmall") ); // PRQA S 3077 
    if ( nullptr != l_bowl )
    {
#if LCF_LOGGING
      XLOG_INFO_OS(g_EngineContext) << "BowlShaperSmall BEFORE LCF: " 
                                    << "\n    m_semiaxis.x():  " << l_bowl->m_bowlDefault.m_semiaxis.x()
                                    << "\n    m_semiaxis.y():  " << l_bowl->m_bowlDefault.m_semiaxis.y() << XLOG_ENDL;
#endif // LCF_LOGGING
      l_bowl->m_bowlDefault.m_semiaxis.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_bowlSize.m_bowlSizeSmall.m_bowlHeight_cm ) ); // TODO: change naming!!!
      l_bowl->m_bowlDefault.m_semiaxis.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_bowlSize.m_bowlSizeSmall.m_bowlWidth_cm ) );
      l_bowl->dirty();

#if LCF_LOGGING
      XLOG_INFO_OS(g_EngineContext) << "BowlShaperSmall AFTER LCF: " 
                                    << "\n    m_semiaxis.x():  " << l_bowl->m_bowlDefault.m_semiaxis.x()
                                    << "\n    m_semiaxis.y():  " << l_bowl->m_bowlDefault.m_semiaxis.y() << XLOG_ENDL;
#endif // LCF_LOGGING
      l_ret = ( l_ret && true );
    }
    else
    {
      XLOG_ERROR_OS(g_EngineContext) << "BowlShaperSmall not found!" << XLOG_ENDL;
      l_ret = false;
    }
  }

  return l_ret;
}

bool IpcCodingReader::modfyPlanView(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::views::planview::PlanViewSettings* l_plan = dynamic_cast<cc::views::planview::PlanViewSettings*>( f_codingManager->getItem("PlanView") ); // PRQA S 3077 
  if ( nullptr != l_plan )
  {
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Plan View Width BEFORE LCF: " << l_plan->m_widthMeters << XLOG_ENDL;
#endif

    const float l_totalWidthMeters = cm2m( static_cast<float>( f_lcfData->m_Data.m_planViewWidth_cm ) );
    l_plan->m_widthMeters = l_totalWidthMeters;
    l_plan->m_widthMetersParkingHori = cm2m( static_cast<float>( f_lcfData->m_Data.m_planViewWidthParkingHori_cm ) );
    l_plan->m_widthMetersParkingVert = cm2m( static_cast<float>( f_lcfData->m_Data.m_planViewWidthParkingVert_cm ) );
    l_plan->m_widthMetersVeh2dDiff   = cm2m( static_cast<float>( f_lcfData->m_Data.m_smDistTrigIn_cm ) );

    l_plan->dirty();

    //! Print information for LSMG
    const cc::core::CustomViews* l_views = dynamic_cast<cc::core::CustomViews*>( f_codingManager->getItem("Views") ); // PRQA S 3077 
    cc::virtcam::CameraPositions* l_cams = dynamic_cast<cc::virtcam::CameraPositions*>(f_codingManager->getItem("VirtCam")); // PRQA S 3077 

    //! Important assumption here: virtual camera positions and viewports
    //! are already handled before this function is called.
    if (nullptr != l_views && nullptr != l_cams)
    {
      XLOG_INFO_OS(g_EngineContext) << "Please use following visibility ranges for LSMG:" << XLOG_ENDL;

      //! Plan View Camera
      osg::Vec3f l_eye = l_cams->m_plan.m_eye;

      //! LSMG Side
      const float l_vehicleHalfWidthMeters = mm2m( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehHalfWidth) );
      const float l_lsmgSide = l_totalWidthMeters / 2.f - l_vehicleHalfWidthMeters;
      XLOG_INFO_OS(g_EngineContext) << "## LSMG Side Visibility: " << l_lsmgSide << " [m]" << XLOG_ENDL;


      const float l_totalLengthMeters = static_cast<float>(l_views->m_planViewport.m_size.y()) * l_totalWidthMeters / static_cast<float>(l_views->m_planViewport.m_size.x()); 
      const float l_halfLengthMeters = 0.5f * l_totalLengthMeters;

      //! LSMG Front
      const float l_vehLengthFront = mm2m( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehLengthFront) );
      const float l_lsmgFront = l_halfLengthMeters - (l_vehLengthFront - l_eye.x());
      XLOG_INFO_OS(g_EngineContext) << "## LSMG Front Visibility: " << l_lsmgFront << " [m]" << XLOG_ENDL;

      //! LSMG Rear
      const float l_vehLengthRear = mm2m( static_cast<float>(f_lcfData->m_Data.m_vehParams.m_vehLengthRear) );
      const float l_lsmgRear = l_halfLengthMeters - (l_vehLengthRear + l_eye.x());
      XLOG_INFO_OS(g_EngineContext) << "## LSMG Rear Visibility: " << l_lsmgRear << " [m]" << XLOG_ENDL;
    }

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Plan View Width AFTER LCF: " << l_plan->m_widthMeters << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "PlanView not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

// bool IpcCodingReader::modifyVehicleAnimData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
// {
//   bool l_ret = true;

//   pc::vehiclemodel::DoorAnimationSettings* l_doorAnim = dynamic_cast<pc::vehiclemodel::DoorAnimationSettings*>( f_codingManager->getItem("DoorAnimation").getData() );

//   if ( 0 != l_doorAnim )
//   {
// #if LCF_LOGGING
//     XLOG_INFO_OS(g_EngineContext) << "Door Animation Time [sec] BEFORE LCF: " << l_doorAnim->m_animDurationSec << XLOG_ENDL;
// #endif

//     float l_factor_ds2s = 0.1f;     // conversion factor [deciseconds] -> [seconds]
//     l_doorAnim->m_animDurationSec = l_factor_ds2s * static_cast<float>( f_lcfData->m_Data.m_doorTransitionTime_sec );
//     l_doorAnim->dirty();

// #if LCF_LOGGING
//     XLOG_INFO_OS(g_EngineContext) << "Door Animation Time [sec] AFTER LCF: " << l_doorAnim->m_animDurationSec << XLOG_ENDL;
// #endif
//     l_ret = ( l_ret && true );
//   }
//   else
//   {
//     XLOG_ERROR_OS(g_EngineContext) << "DoorAnimation not found!" << XLOG_ENDL;
//     l_ret = false;
//   }

//   return l_ret;
// }


bool IpcCodingReader::modifyWheelTracksOverlayData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;
  cc::assets::trajectory::TrajectoryCodingParams* l_trajCodingParams = dynamic_cast<cc::assets::trajectory::TrajectoryCodingParams*>( f_codingManager->getItem("Trajectory") ); // PRQA S 3077 

  if ( nullptr != l_trajCodingParams )
  {

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Trajectory BEFORE LCF: " << l_trajCodingParams->asString() << XLOG_ENDL;
#endif

    l_trajCodingParams->m_DL1_Offset_Front           = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectoryStartOffset_Front_cm ) );
    l_trajCodingParams->m_DL1_Offset_Rear            = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectoryStartOffset_Rear_cm ) );
    // l_trajCodingParams->m_renderOffset_Front         = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectoryRenderOffset_Front_cm ) );
    // l_trajCodingParams->m_renderOffset_Rear          = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectoryRenderOffset_Rear_cm ) );
    l_trajCodingParams->m_outermostLine_OL_WT_minGap = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectory_OL_WT_minGap_cm ) );
    l_trajCodingParams->m_outermostLine_VehContour_Gap     = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectory_OL_VehContour_Gap ) );
    l_trajCodingParams->m_outermostLine_rear_point_offset  = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectory_OL_rear_point_offset_cm ) );
    l_trajCodingParams->m_outermostLine_front_point_offset = cm2m( static_cast<float>( f_lcfData->m_Data.m_trajectory_OL_front_point_offset_cm ) );

    // set as dirty
    l_trajCodingParams->dirty();

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Trajectory AFTER LCF: " << l_trajCodingParams->asString()<< XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "Trajectory not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyHuHemisphereData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;
  cc::virtcam::CarCenter* l_carCenter = dynamic_cast<cc::virtcam::CarCenter*>(f_codingManager->getItem("CarCenter")); // PRQA S 3077 
  cc::virtcam::HeadUnitHemisphereCameraData* l_headUnitHemisphereCameraParams = dynamic_cast<cc::virtcam::HeadUnitHemisphereCameraData*>(f_codingManager->getItem("huhemisdata")); // PRQA S 3077 
  cc::virtcam::HemisphereCameraParameters* l_hemisphereCameraparams = dynamic_cast<cc::virtcam::HemisphereCameraParameters*>(f_codingManager->getItem("HemisphereCamera")); // PRQA S 3077 

  if (nullptr != l_carCenter && nullptr != l_headUnitHemisphereCameraParams && nullptr != l_hemisphereCameraparams)
  {
    l_carCenter->m_carCenterHori.x()                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_carCenter_Hori.m_xCoord_cm ));
    l_carCenter->m_carCenterHori.y()                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_carCenter_Hori.m_yCoord_cm ));
    l_carCenter->m_carCenterHori.z()                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_carCenter_Hori.m_zCoord_cm ));
    l_carCenter->m_carCenterVert.x()                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_carCenter_Vert.m_xCoord_cm ));
    l_carCenter->m_carCenterVert.y()                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_carCenter_Vert.m_yCoord_cm ));
    l_carCenter->m_carCenterVert.z()                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_carCenter_Vert.m_zCoord_cm ));
    //l_headUnitHemisphereCameraParams->m_zoomScale      = static_cast<float>( f_lcfData->m_Data.m_zoomScale );
    l_headUnitHemisphereCameraParams->m_zoomfactorHori = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_zoomfactorHori ));
    l_headUnitHemisphereCameraParams->m_zoomfactorVert = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_zoomfactorVert ));
    l_headUnitHemisphereCameraParams->m_minElevationHu = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_minElevationHu ));
    l_headUnitHemisphereCameraParams->m_maxElevationHu = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_maxElevationHu ));
    l_hemisphereCameraparams->m_minElevation           = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_minElevationHu ));
    l_hemisphereCameraparams->m_maxElevation           = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_maxElevationHu ));

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) //<< "The Hemisphere zoomscale : " << l_headUnitHemisphereCameraParams->m_zoomScale 
                                  << "The Hori CarCenter   x() : " << l_carCenter->m_carCenterHori.x()
                                  << "The                  y() : " << l_carCenter->m_carCenterHori.y()
                                  << "The                  z() : " << l_carCenter->m_carCenterHori.z()
                                  << "The Vert CarCenter   x() : " << l_carCenter->m_carCenterVert.x()
                                  << "The                  y() : " << l_carCenter->m_carCenterVert.y()
                                  << "The                  z() : " << l_carCenter->m_carCenterVert.z()
                                  << "The HeadUnitHemisphereCameraData   zoomfactorHori : " << l_headUnitHemisphereCameraParams->m_zoomfactorHori
                                  << "The                                zoomfactorVert : " << l_headUnitHemisphereCameraParams->m_zoomfactorVert
                                  << "The                                minElevation   : " << l_headUnitHemisphereCameraParams->m_minElevationHu
                                  << "The                                maxElevation   : " << l_headUnitHemisphereCameraParams->m_maxElevationHu
                                  << "The HemisphereCameraUpdater        minElevation   : " << l_hemisphereCameraparams->m_minElevation
                                  << "The                                maxElevation   : " << l_hemisphereCameraparams->m_maxElevation <<XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "HeadUnitHemisphereCameraData & HemisphereCameraUpdater not found" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyCalibSwitchData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;
  cc::assets::caliboverlay::CalibSettings* l_caliboverlayParams = dynamic_cast<cc::assets::caliboverlay::CalibSettings*>(f_codingManager->getItem("CalibOverlay")); // PRQA S 3077 

  if ( nullptr != l_caliboverlayParams )
  {

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "Switch for the calib overlay : " << l_caliboverlayParams->m_calibSwitch << XLOG_ENDL;
#endif
    l_caliboverlayParams->m_calibSwitch = static_cast<unsigned int>( f_lcfData->m_Data.m_calibSwitch ); 

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "calib overlay not found" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyFloorPlateData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::assets::floorplate::CustomFloorPlateData* l_floorPlateParams = dynamic_cast<cc::assets::floorplate::CustomFloorPlateData*>(f_codingManager->getItem("CustomFloorPlate")); // PRQA S 3077 

  if ( nullptr != l_floorPlateParams )
  {
    l_floorPlateParams->m_rearRightCorner.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_floorPlate_RearRightCorner_cm.m_FloorPlateCorner_x ));
    l_floorPlateParams->m_rearRightCorner.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_floorPlate_RearRightCorner_cm.m_FloorPlateCorner_y ));
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The floorPlate RearRightCorner x: " << l_floorPlateParams->m_rearRightCorner.x() 
                                  << "\n                             y: " << l_floorPlateParams->m_rearRightCorner.y() << XLOG_ENDL;
#endif

    l_floorPlateParams->m_frontLeftCorner.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_floorPlate_FrontLeftCorner_cm.m_FloorPlateCorner_x ));
    l_floorPlateParams->m_frontLeftCorner.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_floorPlate_FrontLeftCorner_cm.m_FloorPlateCorner_y ));
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The floorPlate FrontLeftCorner x: " << l_floorPlateParams->m_frontLeftCorner.x() 
                                  << "\n                             y: " << l_floorPlateParams->m_frontLeftCorner.y() << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "FloorPlate not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyFloorPlateSmData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::assets::common::StbSmData* l_floorPlateSm = dynamic_cast<cc::assets::common::StbSmData*>(f_codingManager->getItem("FloorPlateSm")); // PRQA S 3077 

  if ( nullptr != l_floorPlateSm )
  {
    l_floorPlateSm->m_speedTrigVehTrans_lower_kph = static_cast<float>( f_lcfData->m_Data.m_floorPlateSm_SpeedTrigVehTrans_lower_kph );
    l_floorPlateSm->m_speedTrigVehTrans_upper_kph = static_cast<float>( f_lcfData->m_Data.m_floorPlateSm_SpeedTrigVehTrans_upper_kph );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The floorPlateSm speedTrigVehTrans_lower_kph: " << l_floorPlateSm->m_speedTrigVehTrans_lower_kph 
                                  << "\n                                uppper_kph: " << l_floorPlateSm->m_speedTrigVehTrans_upper_kph << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "FloorPlate not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyFisheyeViewData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData) // PRQA S 6041
{
  bool l_ret = true;

  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_front     = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("FrontFisheye")); // PRQA S 3077 
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_rear      = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("RearFisheye")); // PRQA S 3077 
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_left      = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("LeftFisheye")); // PRQA S 3077 
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_right     = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("RightFisheye")); // PRQA S 3077 
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_frontPano = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("FrontPanoFisheye")); // PRQA S 3077 
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_rearPano  = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("RearPanoFisheye")); // PRQA S 3077 
#if ENABLE_VERTICAL_MODE
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_front_vert     = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("VertFrontFisheye"));
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_rear_vert      = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("VertRearFisheye"));
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_left_vert      = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("VertLeftFisheye"));
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_right_vert     = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("VertRightFisheye"));
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_frontPano_vert = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("VertFrontPanoFisheye"));
  pc::views::warpfisheye::FisheyeViewSettings* l_fisheyeViewParams_rearPano_vert  = dynamic_cast<pc::views::warpfisheye::FisheyeViewSettings*>(f_codingManager->getItem("VertRearPanoFisheye"));

  if ( (0 != l_fisheyeViewParams_front)      && (0 != l_fisheyeViewParams_rear)           && (0 != l_fisheyeViewParams_left)
    && (0 != l_fisheyeViewParams_right)      && (0 != l_fisheyeViewParams_frontPano)      && (0 != l_fisheyeViewParams_rearPano)
    && (0 != l_fisheyeViewParams_front_vert) && (0 != l_fisheyeViewParams_rear_vert)      && (0 != l_fisheyeViewParams_left_vert)
    && (0 != l_fisheyeViewParams_right_vert) && (0 != l_fisheyeViewParams_frontPano_vert) && (0 != l_fisheyeViewParams_rearPano_vert))
#else
  if ( (nullptr != l_fisheyeViewParams_front)      && (nullptr != l_fisheyeViewParams_rear)           && (nullptr != l_fisheyeViewParams_left)
    && (nullptr != l_fisheyeViewParams_right)      && (nullptr != l_fisheyeViewParams_frontPano)      && (nullptr != l_fisheyeViewParams_rearPano))
#endif
  {
    l_fisheyeViewParams_front->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_front->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_front->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView Front Yaw:   " << l_fisheyeViewParams_front->m_virtualYaw 
                                  << "The                   Pitch: " << l_fisheyeViewParams_front->m_virtualPitch
                                  << "\n                    Roll:  " << l_fisheyeViewParams_front->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_rear->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_rear->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_rear->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView Rear Yaw:   " << l_fisheyeViewParams_rear->m_virtualYaw 
                                  << "The                  Pitch: " << l_fisheyeViewParams_rear->m_virtualPitch
                                  << "\n                   Roll:  " << l_fisheyeViewParams_rear->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_left->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_left->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_left->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView Left Yaw:   " << l_fisheyeViewParams_left->m_virtualYaw 
                                  << "The                  Pitch: " << l_fisheyeViewParams_left->m_virtualPitch
                                  << "\n                   Roll:  " << l_fisheyeViewParams_left->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_right->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_right->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_right->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView Right Yaw:   " << l_fisheyeViewParams_right->m_virtualYaw 
                                  << "The                   Pitch: " << l_fisheyeViewParams_right->m_virtualPitch
                                  << "\n                    Roll:  " << l_fisheyeViewParams_right->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_frontPano->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_frontPano->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_frontPano->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView FrontPano Yaw:   " << l_fisheyeViewParams_frontPano->m_virtualYaw 
                                  << "The                       Pitch: " << l_fisheyeViewParams_frontPano->m_virtualPitch
                                  << "\n                        Roll:  " << l_fisheyeViewParams_frontPano->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_rearPano->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_rearPano->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_rearPano->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView RearPano Yaw:   " << l_fisheyeViewParams_rearPano->m_virtualYaw 
                                  << "The                      Pitch: " << l_fisheyeViewParams_rearPano->m_virtualPitch
                                  << "\n                       Roll:  " << l_fisheyeViewParams_rearPano->m_virtualRoll << XLOG_ENDL;
#endif

#if ENABLE_VERTICAL_MODE

    l_fisheyeViewParams_front_vert->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front_Vert.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_front_vert->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front_Vert.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_front_vert->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front_Vert.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView FrontVert Yaw:   " << l_fisheyeViewParams_front_vert->m_virtualYaw
                                  << "The                       Pitch: " << l_fisheyeViewParams_front_vert->m_virtualPitch
                                  << "\n                        Roll:  " << l_fisheyeViewParams_front_vert->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_rear_vert->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear_Vert.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_rear_vert->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear_Vert.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_rear_vert->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear_Vert.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView RearVert Yaw:   " << l_fisheyeViewParams_rear_vert->m_virtualYaw
                                  << "The                      Pitch: " << l_fisheyeViewParams_rear_vert->m_virtualPitch
                                  << "\n                       Roll:  " << l_fisheyeViewParams_rear_vert->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_left_vert->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left_Vert.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_left_vert->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left_Vert.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_left_vert->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left_Vert.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView LeftVert Yaw:   " << l_fisheyeViewParams_left_vert->m_virtualYaw
                                  << "The                      Pitch: " << l_fisheyeViewParams_left_vert->m_virtualPitch
                                  << "\n                       Roll:  " << l_fisheyeViewParams_left_vert->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_right_vert->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right_Vert.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_right_vert->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right_Vert.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_right_vert->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right_Vert.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView RightVert Yaw:   " << l_fisheyeViewParams_right_vert->m_virtualYaw
                                  << "The                       Pitch: " << l_fisheyeViewParams_right_vert->m_virtualPitch
                                  << "\n                        Roll:  " << l_fisheyeViewParams_right_vert->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_frontPano_vert->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano_Vert.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_frontPano_vert->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano_Vert.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_frontPano_vert->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano_Vert.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView FrontPanoVert Yaw:   " << l_fisheyeViewParams_frontPano_vert->m_virtualYaw
                                  << "The                           Pitch: " << l_fisheyeViewParams_frontPano_vert->m_virtualPitch
                                  << "\n                            Roll:  " << l_fisheyeViewParams_frontPano_vert->m_virtualRoll << XLOG_ENDL;
#endif

    l_fisheyeViewParams_rearPano_vert->m_virtualYaw   = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano_Vert.m_fisheyeVirtualYaw );
    l_fisheyeViewParams_rearPano_vert->m_virtualPitch = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano_Vert.m_fisheyeVirtualPitch );
    l_fisheyeViewParams_rearPano_vert->m_virtualRoll  = static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano_Vert.m_fisheyeVirtualRoll );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The fisheyeView RearPanoVert Yaw:   " << l_fisheyeViewParams_rearPano_vert->m_virtualYaw
                                  << "The                          Pitch: " << l_fisheyeViewParams_rearPano_vert->m_virtualPitch
                                  << "\n                           Roll:  " << l_fisheyeViewParams_rearPano_vert->m_virtualRoll << XLOG_ENDL;
#endif
#endif  // ENABLE_VERTICAL_MODE

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "FisheyeView not found!" << XLOG_ENDL;
    l_ret = false;
  }

  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_front          = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishFront")); // PRQA S 3077 
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_rear           = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishRear")); // PRQA S 3077 
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_left           = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishLeft")); // PRQA S 3077 
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_right          = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishRight")); // PRQA S 3077 
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_frontPano      = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishFrontPano")); // PRQA S 3077 
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_rearPano       = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishRearPano")); // PRQA S 3077 
#if ENABLE_VERTICAL_MODE
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_front_vert     = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishFrontVert"));
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_rear_vert      = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishRearVert"));
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_left_vert      = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishLeftVert"));
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_right_vert     = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishRightVert"));
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_frontPano_vert = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishFrontPanoVert"));
  pc::views::warpfisheye::PartialUnfishSettings* l_fisheyeViewParamsPartial_rearPano_vert  = dynamic_cast<pc::views::warpfisheye::PartialUnfishSettings*>(f_codingManager->getItem("PartialUnfishRearPanoVert"));

  if ( (0 != l_fisheyeViewParamsPartial_front)      && (0 != l_fisheyeViewParamsPartial_rear)           && (0 != l_fisheyeViewParamsPartial_left)
    && (0 != l_fisheyeViewParamsPartial_right)      && (0 != l_fisheyeViewParamsPartial_frontPano)      && (0 != l_fisheyeViewParamsPartial_rearPano)
    && (0 != l_fisheyeViewParamsPartial_front_vert) && (0 != l_fisheyeViewParamsPartial_rear_vert)      && (0 != l_fisheyeViewParamsPartial_left_vert)
    && (0 != l_fisheyeViewParamsPartial_right_vert) && (0 != l_fisheyeViewParamsPartial_frontPano_vert) && (0 != l_fisheyeViewParamsPartial_rearPano_vert))
#else
  if ( (nullptr != l_fisheyeViewParamsPartial_front)      && (nullptr != l_fisheyeViewParamsPartial_rear)           && (nullptr != l_fisheyeViewParamsPartial_left)
    && (nullptr != l_fisheyeViewParamsPartial_right)      && (nullptr != l_fisheyeViewParamsPartial_frontPano)      && (nullptr != l_fisheyeViewParamsPartial_rearPano))
#endif
  {
    l_fisheyeViewParamsPartial_front->m_modelSettings.m_horizontalHalfFov           =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_front->m_delta                                       = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_rear->m_modelSettings.m_horizontalHalfFov            =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_rear->m_delta                                        = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_left->m_modelSettings.m_horizontalHalfFov            =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_left->m_delta                                        = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_right->m_modelSettings.m_horizontalHalfFov           =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_right->m_delta                                       = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_frontPano->m_modelSettings.m_horizontalHalfFov       =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_frontPano->m_delta                                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_rearPano->m_modelSettings.m_horizontalHalfFov        =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_rearPano->m_delta                                    = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano.m_fisheyePartialUnfishDelta ));
#if ENABLE_VERTICAL_MODE
    l_fisheyeViewParamsPartial_front_vert->m_modelSettings.m_horizontalHalfFov      =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front_Vert.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_front_vert->m_delta                                  = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Front_Vert.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_rear_vert->m_modelSettings.m_horizontalHalfFov       =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear_Vert.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_rear_vert->m_delta                                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Rear_Vert.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_left_vert->m_modelSettings.m_horizontalHalfFov       =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left_Vert.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_left_vert->m_delta                                   = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Left_Vert.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_right_vert->m_modelSettings.m_horizontalHalfFov      =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right_Vert.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_right_vert->m_delta                                  = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_Right_Vert.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_frontPano_vert->m_modelSettings.m_horizontalHalfFov  =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano_Vert.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_frontPano_vert->m_delta                              = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_FrontPano_Vert.m_fisheyePartialUnfishDelta ));
    l_fisheyeViewParamsPartial_rearPano_vert->m_modelSettings.m_horizontalHalfFov   =          static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano_Vert.m_fisheyePartialUnfishFov );
    l_fisheyeViewParamsPartial_rearPano_vert->m_delta                               = 0.01f * (static_cast<float>( f_lcfData->m_Data.m_fisheyeView_RearPano_Vert.m_fisheyePartialUnfishDelta ));
#endif
    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "FisheyeViewPartial not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyFisheyeCropViewData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData) // PRQA S 6041
{
  bool l_ret = true;

  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropFrontView     = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsFrontView")); // PRQA S 3077 
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropRearView      = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsRearView")); // PRQA S 3077 
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropFrontPano     = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsFrontPano")); // PRQA S 3077 
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropRearPano      = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsRearPano")); // PRQA S 3077 
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropLeftView      = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsLeftView")); // PRQA S 3077 
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropRightView     = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingRightView")); // PRQA S 3077 
#if ENABLE_VERTICAL_MODE
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropFrontVert     = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsFrontVert"));
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropRearVert      = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsRearVert"));
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropFrontPanoVert = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsFrontPanoVert"));
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropRearPanoVert  = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsRearPanoVert"));
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropLeftViewVert  = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingsLeftViewVert"));
  cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings* l_fisheyeCropRightViewVert = dynamic_cast<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings*>(f_codingManager->getItem("FisheyeCropSettingRightViewVert"));

  if ( (0 != l_fisheyeCropFrontView)      && (0 != l_fisheyeCropRearView)           && (0 != l_fisheyeCropFrontPano)
    && (0 != l_fisheyeCropRearPano)      && (0 != l_fisheyeCropLeftView)      && (0 != l_fisheyeCropRightView)
    && (0 != l_fisheyeCropFrontVert) && (0 != l_fisheyeCropRearVert)      && (0 != l_fisheyeCropFrontPanoVert)
    && (0 != l_fisheyeCropRearPanoVert) && (0 != l_fisheyeCropLeftViewVert) && (0 != l_fisheyeCropRightViewVert))
#else
  if ( (nullptr != l_fisheyeCropFrontView)      && (nullptr != l_fisheyeCropRearView)           && (nullptr != l_fisheyeCropFrontPano)
    && (nullptr != l_fisheyeCropRearPano)      && (nullptr != l_fisheyeCropLeftView)      && (nullptr != l_fisheyeCropRightView))
#endif
  {
    l_fisheyeCropFrontView->m_cropBounds.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontView.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropFrontView->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontView.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropFrontView->m_cropBounds.z() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontView.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropFrontView->m_cropBounds.w() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontView.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropFrontView Left: " << l_fisheyeCropFrontView->m_cropBounds.x() 
                                  << "The                           Right: " << l_fisheyeCropFrontView->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropFrontView->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropFrontView->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropRearView->m_cropBounds.x() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearView.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropRearView->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearView.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropRearView->m_cropBounds.z() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearView.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropRearView->m_cropBounds.w() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearView.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropRearView  Left: " << l_fisheyeCropRearView->m_cropBounds.x() 
                                  << "The                           Right: " << l_fisheyeCropRearView->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropRearView->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropRearView->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropFrontPano->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPano.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropFrontPano->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPano.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropFrontPano->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPano.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropFrontPano->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPano.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropFrontPano Left: " << l_fisheyeCropFrontPano->m_cropBounds.x() 
                                  << "The                           Right: " << l_fisheyeCropFrontPano->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropFrontPano->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropFrontPano->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropRearPano->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPano.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropRearPano->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPano.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropRearPano->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPano.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropRearPano->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPano.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropRearPano  Left: " << l_fisheyeCropRearPano->m_cropBounds.x() 
                                  << "The                           Right: " << l_fisheyeCropRearPano->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropRearPano->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropRearPano->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropLeftView->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftView.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropLeftView->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftView.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropLeftView->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftView.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropLeftView->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftView.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropLeftView  Left: " << l_fisheyeCropLeftView->m_cropBounds.x() 
                                  << "The                           Right: " << l_fisheyeCropLeftView->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropLeftView->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropLeftView->m_cropBounds.w() << XLOG_ENDL;
#endif
    l_fisheyeCropRightView->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightView.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropRightView->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightView.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropRightView->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightView.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropRightView->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightView.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropRightView Left: " << l_fisheyeCropRightView->m_cropBounds.x() 
                                  << "The                           Right: " << l_fisheyeCropRightView->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropRightView->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropRightView->m_cropBounds.w() << XLOG_ENDL;
#endif
#if ENABLE_VERTICAL_MODE
    l_fisheyeCropFrontVert->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontVert.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropFrontVert->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontVert.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropFrontVert->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontVert.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropFrontVert->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontVert.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropFrontVert Left: " << l_fisheyeCropFrontVert->m_cropBounds.x()
                                  << "The                           Right: " << l_fisheyeCropFrontVert->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropFrontVert->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropFrontVert->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropRearVert->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearVert.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropRearVert->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearVert.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropRearVert->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearVert.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropRearVert->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearVert.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropRearVert  Left: " << l_fisheyeCropRearVert->m_cropBounds.x()
                                  << "The                           Right: " << l_fisheyeCropRearVert->m_cropBounds.y()
                                  << "The                          Bottom: " << l_fisheyeCropRearVert->m_cropBounds.z()
                                  << "\n                              Top: " << l_fisheyeCropRearVert->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropFrontPanoVert->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPanoVert.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropFrontPanoVert->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPanoVert.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropFrontPanoVert->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPanoVert.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropFrontPanoVert->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsFrontPanoVert.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropFrontPanoVert Left: " << l_fisheyeCropFrontPanoVert->m_cropBounds.x()
                                  << "The                               Right: " << l_fisheyeCropFrontPanoVert->m_cropBounds.y()
                                  << "The                              Bottom: " << l_fisheyeCropFrontPanoVert->m_cropBounds.z()
                                  << "\n                                  Top: " << l_fisheyeCropFrontPanoVert->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropRearPanoVert->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPanoVert.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropRearPanoVert->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPanoVert.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropRearPanoVert->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPanoVert.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropRearPanoVert->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsRearPanoVert.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropReatPanoVert  Left: " << l_fisheyeCropRearPanoVert->m_cropBounds.x()
                                  << "The                               Right: " << l_fisheyeCropRearPanoVert->m_cropBounds.y()
                                  << "The                              Bottom: " << l_fisheyeCropRearPanoVert->m_cropBounds.z()
                                  << "\n                                  Top: " << l_fisheyeCropRearPanoVert->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropLeftViewVert->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftViewVert.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropLeftViewVert->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftViewVert.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropLeftViewVert->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftViewVert.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropLeftViewVert->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingsLeftViewVert.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropLeftViewVert  Left: " << l_fisheyeCropLeftViewVert->m_cropBounds.x()
                                  << "The                               Right: " << l_fisheyeCropLeftViewVert->m_cropBounds.y()
                                  << "The                              Bottom: " << l_fisheyeCropLeftViewVert->m_cropBounds.z()
                                  << "\n                                  Top: " << l_fisheyeCropLeftViewVert->m_cropBounds.w() << XLOG_ENDL;
#endif

    l_fisheyeCropRightViewVert->m_cropBounds.x()   = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightViewVert.m_fisheyeCropBoundsLeft ) );
    l_fisheyeCropRightViewVert->m_cropBounds.y() = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightViewVert.m_fisheyeCropBoundsRight ) );
    l_fisheyeCropRightViewVert->m_cropBounds.z()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightViewVert.m_fisheyeCropBoundsBottom ) );
    l_fisheyeCropRightViewVert->m_cropBounds.w()  = cm2m( static_cast<float>( f_lcfData->m_Data.m_customFishEyeCropView.m_FisheyeCropSettingRightViewVert.m_fisheyeCropBoundsTop ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The customFisheyeCropRightViewVert Left: " << l_fisheyeCropRightViewVert->m_cropBounds.x()
                                  << "The                               Right: " << l_fisheyeCropRightViewVert->m_cropBounds.y()
                                  << "The                              Bottom: " << l_fisheyeCropRightViewVert->m_cropBounds.z()
                                  << "\n                                  Top: " << l_fisheyeCropRightViewVert->m_cropBounds.w() << XLOG_ENDL;
#endif
#endif // ENABLE_VERTICAL_MODE

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "Custom FisheyeCropView not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyParkingSpaceData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::assets::parkingspace::ParkingSpaceSettings* l_parkingSpaceSettings = dynamic_cast<cc::assets::parkingspace::ParkingSpaceSettings*>(f_codingManager->getItem("ParkingSpace")); // PRQA S 3077 

  if ( (nullptr != l_parkingSpaceSettings))
  {
    l_parkingSpaceSettings->m_isEnabled           = static_cast<bool>( f_lcfData->m_Data.m_parkingSpace.m_isEnabled ) ;
    l_parkingSpaceSettings->m_enablePSMode        = static_cast<unsigned int>( f_lcfData->m_Data.m_parkingSpace.m_enablePSMode ) ;
    l_parkingSpaceSettings->m_iconSize.x()        = static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_iconSize_x ) ; 
    l_parkingSpaceSettings->m_iconSize.y()        = static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_iconSize_y ) ; 
    l_parkingSpaceSettings->m_iconSizeVert.x()    = static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_iconSizeVert_x ) ; 
    l_parkingSpaceSettings->m_iconSizeVert.y()    = static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_iconSizeVert_y ) ; 
    l_parkingSpaceSettings->m_slotCrossX          = cm2m(static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_slotCrossX_cm )) ;
    l_parkingSpaceSettings->m_slotParallelX       = cm2m(static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_slotParallelX_cm )) ;
    // l_parkingSpaceSettings->m_slotOffset.x()      = cm2m(static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_slotOffset_x_cm ) );
    // l_parkingSpaceSettings->m_slotOffset.y()      = cm2m(static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_slotOffset_y_cm ) );
    l_parkingSpaceSettings->m_manoeuverShowDelay  = static_cast<float>( f_lcfData->m_Data.m_parkingSpace.m_manoeuverShowDelay_s ) / 1000.f  ;
    l_parkingSpaceSettings->m_parkingDiagSlotAngleLowerLimit = 0.001f * static_cast<float>( f_lcfData->m_Data.m_parkingDiagSlotAngleLowerLimit) ;
    l_parkingSpaceSettings->m_parkingDiagSlotAngleUpperLimit = 0.001f * static_cast<float>( f_lcfData->m_Data.m_parkingDiagSlotAngleUpperLimit) ;
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The ParkingSpace Setting     isEnable: " << l_parkingSpaceSettings->m_isEnabled 
                                  << "The                      enablePSMode: " << l_parkingSpaceSettings->m_enablePSMode
                                  << "The                      iconSize.x(): " << l_parkingSpaceSettings->m_iconSize.x()
                                  << "The                      iconSize.y(): " << l_parkingSpaceSettings->m_iconSize.y()
                                  << "The                  iconSizeVert.x(): " << l_parkingSpaceSettings->m_iconSizeVert.x()
                                  << "The                  iconSizeVert.y(): " << l_parkingSpaceSettings->m_iconSizeVert.y()
                                  << "The                        slotCrossX: " << l_parkingSpaceSettings->m_slotCrossX
                                  << "The                     slotParallelX: " << l_parkingSpaceSettings->m_slotParallelX
                                  // << "The                    slotOffset.x(): " << l_parkingSpaceSettings->m_slotOffset.x()
                                  // << "The                    slotOffset.y(): " << l_parkingSpaceSettings->m_slotOffset.y()
                                  << "\n                 manoeuverShowDelay: " << l_parkingSpaceSettings->m_manoeuverShowDelay << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "ParkingSpace not found!" << XLOG_ENDL;
    l_ret = false;
  }
  return l_ret;
}

bool IpcCodingReader::modifyTileOverlayData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::assets::tileoverlay::TileSettings* l_TileSettings = dynamic_cast<cc::assets::tileoverlay::TileSettings*>(f_codingManager->getItem("TileOverlay")); // PRQA S 3077 

  if ( nullptr != l_TileSettings )
  {
    //l_TileSettings->m_SplineTypeForEachSegment = static_cast<bool>( f_lcfData->m_Data.m_SplineTypeForEachSegment ) ;
#if LCF_LOGGING
    //XLOG_INFO_OS(g_EngineContext) << "The SplineTypeForEachSegment : " << l_TileSettings->m_SplineTypeForEachSegment << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "TileSettings not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

bool IpcCodingReader::modifyFreeParkingData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;

  cc::assets::freeparkingoverlay::FreeparkingManagerSettings* l_freeParkingSettings = dynamic_cast<cc::assets::freeparkingoverlay::FreeparkingManagerSettings*>(f_codingManager->getItem("FreeparkingManager")); // PRQA S 3077 

  if ( (nullptr != l_freeParkingSettings) )
  {
    l_freeParkingSettings->m_translation_step                   = mm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_translation_step ) );
    l_freeParkingSettings->m_rotation_step                      = static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_rotation_step ) ;
    l_freeParkingSettings->m_widthOffset                        = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_widthOffset ) );
    l_freeParkingSettings->m_lengthOffset                       = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_lengthOffset ) );
    l_freeParkingSettings->m_fpParkRange_Parallel_X_low_Limit   = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Parallel_X_low_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Parallel_X_upper_Limit = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Parallel_X_upper_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Parallel_Y_low_Limit   = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Parallel_Y_low_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Parallel_Y_upper_Limit = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Parallel_Y_upper_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Cross_X_low_Limit      = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Cross_X_low_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Cross_X_upper_Limit    = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Cross_X_upper_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Cross_Y_low_Limit      = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Cross_Y_low_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Cross_Y_upper_Limit    = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Cross_Y_upper_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Diagonal_X_low_Limit   = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Diagonal_X_low_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Diagonal_X_upper_Limit = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Diagonal_X_upper_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Diagonal_Y_low_Limit   = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Diagonal_Y_low_Limit_cm ) );
    l_freeParkingSettings->m_fpParkRange_Diagonal_Y_upper_Limit = cm2m( static_cast<float>( f_lcfData->m_Data.m_freeParkingManager.m_fpParkRange_Diagonal_Y_upper_Limit_cm ) );
#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The FreeParking Setting translation_step: " << l_freeParkingSettings->m_translation_step 
                                  << "The                        rotation_step: " << l_freeParkingSettings->m_rotation_step
                                  << "The                          widthOffset: " << l_freeParkingSettings->m_widthOffset
                                  << "The     fpParkRange_Parallel_X_low_Limit: " << l_freeParkingSettings->m_fpParkRange_Parallel_X_low_Limit
                                  << "The   fpParkRange_Parallel_X_upper_Limit: " << l_freeParkingSettings->m_fpParkRange_Parallel_X_upper_Limit
                                  << "The     fpParkRange_Parallel_Y_low_Limit: " << l_freeParkingSettings->m_fpParkRange_Parallel_Y_low_Limit
                                  << "The   fpParkRange_Parallel_Y_upper_Limit: " << l_freeParkingSettings->m_fpParkRange_Parallel_Y_upper_Limit
                                  << "The        fpParkRange_Cross_X_low_Limit: " << l_freeParkingSettings->m_fpParkRange_Cross_X_low_Limit
                                  << "The      fpParkRange_Cross_X_upper_Limit: " << l_freeParkingSettings->m_fpParkRange_Cross_X_upper_Limit
                                  << "The        fpParkRange_Cross_Y_low_Limit: " << l_freeParkingSettings->m_fpParkRange_Cross_Y_low_Limit
                                  << "The      fpParkRange_Cross_Y_upper_Limit: " << l_freeParkingSettings->m_fpParkRange_Cross_Y_upper_Limit
                                  << "The     fpParkRange_Diagonal_X_low_Limit: " << l_freeParkingSettings->m_fpParkRange_Diagonal_X_low_Limit
                                  << "The   fpParkRange_Diagonal_X_upper_Limit: " << l_freeParkingSettings->m_fpParkRange_Diagonal_X_upper_Limit
                                  << "The     fpParkRange_Diagonal_Y_low_Limit: " << l_freeParkingSettings->m_fpParkRange_Diagonal_Y_low_Limit
                                  << "The   fpParkRange_Diagonal_Y_upper_Limit: " << l_freeParkingSettings->m_fpParkRange_Diagonal_Y_upper_Limit << XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "FreeparkingManagerSettings not found!" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

// disabled because of linux compilation failure
// bool IpcCodingReader::modifyOdometryConverterData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
// {
//   bool l_ret = true;
//   pc::texfloor::odometry::OdometryConverterSettings* l_odoConverter = dynamic_cast<pc::texfloor::odometry::OdometryConverterSettings*>(f_codingManager->getItem("OdometryConverter"));

//   if ( 0 != l_odoConverter )
//   {
//     l_odoConverter->m_thresholdX = 0.001f * static_cast<float>( f_lcfData->m_Data.m_odometryConverter_thresholdX) ;
//     l_odoConverter->m_thresholdY = 0.001f * static_cast<float>( f_lcfData->m_Data.m_odometryConverter_thresholdY) ;
//     l_odoConverter->m_thresholdYaw = 0.01f * static_cast<float>( f_lcfData->m_Data.m_odometryConverter_thresholdYaw) ;

// #if LCF_LOGGING
//     XLOG_INFO_OS(g_EngineContext) << "The OdometryConverter thresholdX : " << l_odoConverter->m_thresholdX
//                                   << "The                   thresholdY : " << l_odoConverter->m_thresholdY
//                                   << "The                 thresholdYaw : " << l_odoConverter->m_thresholdYaw <<XLOG_ENDL;
// #endif

//     l_ret = ( l_ret && true );
//   }
//   else
//   {
//     XLOG_ERROR_OS(g_EngineContext) << "Odo Converter data not found" << XLOG_ENDL;
//     l_ret = false;
//   }

//   return l_ret;
// }

bool IpcCodingReader::modifyParkingSlotData(pc::util::coding::CodingManager* f_codingManager, const cc::daddy::LcfDaddy* f_lcfData)
{
  bool l_ret = true;
  cc::assets::uielements::ParkingSlotSettings* l_parkingSlot = dynamic_cast<cc::assets::uielements::ParkingSlotSettings*>(f_codingManager->getItem("ParkingSlot")); // PRQA S 3077 

  if ( nullptr != l_parkingSlot )
  {
    l_parkingSlot->m_parkingDiagSlotAngleLowerLimit = 0.001f * static_cast<float>( f_lcfData->m_Data.m_parkingDiagSlotAngleLowerLimit) ;
    l_parkingSlot->m_parkingDiagSlotAngleUpperLimit = 0.001f * static_cast<float>( f_lcfData->m_Data.m_parkingDiagSlotAngleUpperLimit) ;

#if LCF_LOGGING
    XLOG_INFO_OS(g_EngineContext) << "The ParkingSlot diag slot angle lower limit : " << l_parkingSlot->m_parkingDiagSlotAngleLowerLimit 
                                  << "The                             upper limit : " << l_parkingSlot->m_parkingDiagSlotAngleUpperLimit<<XLOG_ENDL;
#endif

    l_ret = ( l_ret && true );
  }
  else
  {
    XLOG_ERROR_OS(g_EngineContext) << "ParkingSlot data not found" << XLOG_ENDL;
    l_ret = false;
  }

  return l_ret;
}

} // namespace core
} // namespace cc 
 
