//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ParkingConfirmInterface.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_PARKINGCONFIRMINTERFACE_H
#define CC_ASSETS_UIELEMENTS_PARKINGCONFIRMINTERFACE_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace parkconfirminterface
{

enum EParkConfirmButton : vfc::uint8_t
{
  PARKCONFIRM_NOTSHOW     = 0u,
  PARKCONFIRM_INACTIVE    = 1u,
  PARKCONFIRM_ACTIVE      = 2u
};


//======================================================
// ParkingConfirmInterfaceSettings
//------------------------------------------------------
/// Setting class for parkconfirminterface
/// <AUTHOR>
//======================================================
class ParkingConfirmInterfaceSettings : public pc::util::coding::ISerializable
{
public:

  ParkingConfirmInterfaceSettings()
    : m_texturePathAPAParkIcon("cc/vehicle_model/ui/21_APA_park_confirmed.png")
    , m_texturePathAPAParkIconOFF("cc/vehicle_model/ui/22_APA_park_not_confirmed.png")
    , m_texturePathRPAParkIcon("cc/vehicle_model/ui/21_RPA_park_confirmed.png")
    , m_texturePathRPAParkIconOFF("cc/vehicle_model/ui/22_RPA_park_not_confirmed.png")
    , m_texturePathParkPopOutBox("cc/vehicle_model/ui/23_park_confirm_bkg.png")
    , m_texturePathTextBoxPressParkingSwitch("cc/vehicle_model/ui/98_searching_press_parking_switch.png")
    , m_texturePathParkRPApressParkSwitch("cc/vehicle_model/ui/24_RPA_press_parking_switch.png")
    , m_texturePathTextBoxBuckleSeatBelt("cc/vehicle_model/ui/98_searching_buckle_seat_belt.png")
    , m_texturePathParkBoxPressBrakePedal("cc/vehicle_model/ui/98_searching_press_brake_pedal.png")
    , m_texturePathTextBoxCloseDoor("cc/vehicle_model/ui/98_searching_door_to_close.png")
    , m_texturePathTextBoxCloseTrunk("cc/vehicle_model/ui/98_searching_close_trunk.png")
    , m_texturePathParkAPASelectedVariantAPA("cc/vehicle_model/ui/19_APA_selected_variant_APA.png")
    , m_texturePathParkAPAUnselected("cc/vehicle_model/ui/19_APA_unselected.png") 
    , m_texturePathParkRPAUnselected("cc/vehicle_model/ui/19_RPA_unselected.png") 
    , m_texturePathParkRPASketch("cc/vehicle_model/ui/24_RPA_sketch.png") 
    , m_texturePathParkRPAChecklist("cc/vehicle_model/ui/24_RPA_1234.png") 
    , m_texturePathParkRPACheck("cc/vehicle_model/ui/24_RPA_check.png") 
    , m_texturePathParkRPAClosetrunk("cc/vehicle_model/ui/24_RPA_close_trunk.png") 
    , m_texturePathParkRPAConnectBT("cc/vehicle_model/ui/24_RPA_connect_BT.png") 
    , m_texturePathParkRPACloseDoor("cc/vehicle_model/ui/24_RPA_door_to_close.png") 
    , m_texturePathParkRPAGeartoP("cc/vehicle_model/ui/24_RPA_gear_to_P.png") 
    , m_texturePathParkRPAEbrakeOn("cc/vehicle_model/ui/24_RPA_open_ebrake.png") 
    , m_texturePathParkRPALeaveCar("cc/vehicle_model/ui/24_RPA_leavecar.png")
    , m_texturePathConfirmParkType("cc/vehicle_model/ui/98_confirming_park_type.png")
    , m_texturePathConfirmExit("cc/vehicle_model/ui/98_confirming_exit.png")
    , m_texturePathConfirmSeparator("cc/vehicle_model/ui/98_confirming_separator.png")
  {
  }

  SERIALIZABLE(ParkingConfirmInterfaceSettings)
  {
    ADD_MEMBER(cc::assets::uielements::UIData, settingParkAPAConfirm);
    ADD_MEMBER(cc::assets::uielements::UIData, settingParkAPAParkOutConfirm);
    ADD_MEMBER(cc::assets::uielements::UIData, settingParkRPAConfirm);
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKPopOutBox);
    ADD_MEMBER(cc::assets::uielements::UIData, settingParkAPAConfirmText);
    ADD_MEMBER(cc::assets::uielements::UIData, settingParkAPAParkOutConfirmText);
    ADD_MEMBER(cc::assets::uielements::UIData, settingParkRPAConfirmText);
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKRPASketch); 
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKRPAChecklist); 
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKConfirmTitle);
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKAPADrvReqInd);
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKRPADrvReqInd); 
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKConfirmExit);
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKConfirmSeparator);
    ADD_MEMBER(cc::assets::uielements::UIData, settingPARKRPAConfirmSeparator);
    ADD_STRING_MEMBER(texturePathAPAParkIcon);
    ADD_STRING_MEMBER(texturePathAPAParkIconOFF);
    ADD_STRING_MEMBER(texturePathRPAParkIcon);
    ADD_STRING_MEMBER(texturePathRPAParkIconOFF);
    ADD_STRING_MEMBER(texturePathParkPopOutBox);
    ADD_STRING_MEMBER(texturePathTextBoxPressParkingSwitch);
    ADD_STRING_MEMBER(texturePathParkRPApressParkSwitch);
    ADD_STRING_MEMBER(texturePathTextBoxBuckleSeatBelt);
    ADD_STRING_MEMBER(texturePathParkBoxPressBrakePedal);
    ADD_STRING_MEMBER(texturePathTextBoxCloseDoor);
    ADD_STRING_MEMBER(texturePathTextBoxCloseTrunk);
    ADD_STRING_MEMBER(texturePathParkAPASelectedVariantAPA);
    ADD_STRING_MEMBER(texturePathParkAPAUnselected);
    ADD_STRING_MEMBER(texturePathParkRPAUnselected);
    ADD_STRING_MEMBER(texturePathParkRPASketch);
    ADD_STRING_MEMBER(texturePathParkRPAChecklist);
    ADD_STRING_MEMBER(texturePathParkRPACheck);
    ADD_STRING_MEMBER(texturePathParkRPAClosetrunk);
    ADD_STRING_MEMBER(texturePathParkRPAConnectBT);
    ADD_STRING_MEMBER(texturePathParkRPACloseDoor);
    ADD_STRING_MEMBER(texturePathParkRPAGeartoP);
    ADD_STRING_MEMBER(texturePathParkRPAEbrakeOn);
    ADD_STRING_MEMBER(texturePathParkRPALeaveCar);
    ADD_STRING_MEMBER(texturePathConfirmParkType);
    ADD_STRING_MEMBER(texturePathConfirmExit);
    ADD_STRING_MEMBER(texturePathConfirmSeparator);

  }

  cc::assets::uielements::UIData  m_settingParkAPAConfirm;
  cc::assets::uielements::UIData  m_settingParkAPAParkOutConfirm;
  cc::assets::uielements::UIData  m_settingParkRPAConfirm;
  cc::assets::uielements::UIData  m_settingPARKPopOutBox;
  cc::assets::uielements::UIData  m_settingParkAPAConfirmText;
  cc::assets::uielements::UIData  m_settingParkAPAParkOutConfirmText;
  cc::assets::uielements::UIData  m_settingParkRPAConfirmText; 
  cc::assets::uielements::UIData  m_settingPARKRPASketch; 
  cc::assets::uielements::UIData  m_settingPARKRPAChecklist;
  cc::assets::uielements::UIData  m_settingPARKConfirmTitle; 
  cc::assets::uielements::UIData  m_settingPARKAPADrvReqInd; 
  cc::assets::uielements::UIData  m_settingPARKRPADrvReqInd;
  cc::assets::uielements::UIData  m_settingPARKConfirmExit;
  cc::assets::uielements::UIData  m_settingPARKConfirmSeparator;
  cc::assets::uielements::UIData  m_settingPARKRPAConfirmSeparator;
  
  // ! parking confirm icon
  std::string                     m_texturePathAPAParkIcon;
  std::string                     m_texturePathAPAParkIconOFF;
  std::string                     m_texturePathRPAParkIcon;
  std::string                     m_texturePathRPAParkIconOFF;
  std::string                     m_texturePathParkPopOutBox;
  std::string                     m_texturePathTextBoxPressParkingSwitch;
  std::string                     m_texturePathParkRPApressParkSwitch;
  std::string                     m_texturePathTextBoxBuckleSeatBelt;
  std::string                     m_texturePathParkBoxPressBrakePedal;
  std::string                     m_texturePathTextBoxCloseDoor;
  std::string                     m_texturePathTextBoxCloseTrunk;
  std::string                     m_texturePathParkAPASelectedVariantAPA;
  std::string                     m_texturePathParkAPAUnselected;
  std::string                     m_texturePathParkRPAUnselected;
  std::string                     m_texturePathParkRPASketch;
  std::string                     m_texturePathParkRPAChecklist;
  std::string                     m_texturePathParkRPACheck; 
  std::string                     m_texturePathParkRPAClosetrunk;
  std::string                     m_texturePathParkRPAConnectBT; 
  std::string                     m_texturePathParkRPACloseDoor; 
  std::string                     m_texturePathParkRPAGeartoP; 
  std::string                     m_texturePathParkRPAEbrakeOn; 
  std::string                     m_texturePathParkRPALeaveCar;
  std::string                     m_texturePathConfirmParkType;
  std::string                     m_texturePathConfirmExit;
  std::string                     m_texturePathConfirmSeparator;
};

extern pc::util::coding::Item<ParkingConfirmInterfaceSettings> g_parkingConfirmInterfaceSettings;

//!
//! ParkingIconManager
//!
class ParkingConfirmInterfaceManager
{
public:

  ParkingConfirmInterfaceManager();

  virtual ~ParkingConfirmInterfaceManager();

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);
  void update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);
  void batchSetInActiveIcons();

private:

  //! Copy constructor is not permitted.
  ParkingConfirmInterfaceManager (const ParkingConfirmInterfaceManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingConfirmInterfaceManager& operator=(const ParkingConfirmInterfaceManager& other); // = delete

  osg::Vec2f transferToBottomLeft(const osg::Vec2f f_iconPos);
  
  unsigned int m_lastConfigUpdate;
  unsigned int m_count;
  pc::assets::IconGroup m_settingParkConfirmInterface;
  EParkConfirmButton  m_confirmBtnSts;
  bool m_mat_b;
};


//!
//! ParkingConfirmInterface
//!
class ParkingConfirmInterface: public cc::assets::uielements::CustomImageOverlays
{
public:

  ParkingConfirmInterface(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId/*, pc::core::View* f_view*/);

  virtual ~ParkingConfirmInterface();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  //! Copy constructor is not permitted.
  ParkingConfirmInterface (const ParkingConfirmInterface& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingConfirmInterface& operator=(const ParkingConfirmInterface& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ParkingConfirmInterfaceManager m_manager;

};


} // namespace parkconfirminterface
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_PARKINGCONFIRMINTERFACE_H
