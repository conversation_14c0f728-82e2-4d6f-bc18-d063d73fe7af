//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_DL1
#define CC_ASSETS_TRAJECTORY_SUBASSETS_DL1

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/assets/trajectory/inc/OutermostLine.h"


namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic


class DL1 : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:

  DL1(
    pc::core::Framework* f_framework,
    float f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    const assets::trajectory::OutermostLine* const f_leftOutermostLine,
    const assets::trajectory::OutermostLine* const f_rightOutermostLine,
    unsigned int f_numLayoutPoints);

  osg::Image* create1DTexture() const;

  float getDistanceLineWidth() const;

  virtual void generateVertexData();

protected:

  virtual ~DL1();

  void generateVertexData_usingTexture();

private:

  //! Copy constructor is not permitted.
  DL1 (const DL1& other); // = delete
  //! Copy assignment operator is not permitted.
  DL1& operator=(const DL1& other); // = delete

  const osg::ref_ptr<const assets::trajectory::OutermostLine> m_leftOutermostLine;
  const osg::ref_ptr<const assets::trajectory::OutermostLine> m_rightOutermostLine;
  unsigned int m_numLayoutPoints;
};

osg::DrawElements* createSurfaceDL(unsigned int f_numLayoutPoints);

} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_DL1
