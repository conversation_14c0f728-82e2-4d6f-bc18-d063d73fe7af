//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_ELLIPTICAL_SLIDING_BAR_H
#define CC_ASSETS_BUTTON_ELLIPTICAL_SLIDING_BAR_H

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/core/inc/Viewport.h"
#include <osg/Geode>
#include <osg/Texture2D>
#include "cc/assets/uielements/inc/RotateIcon.h"
#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace button
{
class ViewChangeOverlay;
class EllipticalSlidingBarSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(EllipticalSlidingBarSettings) // PRQA S 2428
    {
        ADD_MEMBER(vfc::int32_t,a);
        ADD_MEMBER(vfc::int32_t,b);
        ADD_MEMBER(vfc::int32_t,midX);
        ADD_MEMBER(vfc::int32_t,midY);
        ADD_MEMBER(vfc::int32_t,width);
        ADD_MEMBER(vfc::float32_t,slideChangeLimit);
        ADD_MEMBER(vfc::float32_t,moveChangeLimit);
    }

    vfc::int32_t             m_a;
    vfc::int32_t             m_b;
    vfc::int32_t             m_midX;
    vfc::int32_t             m_midY;
    vfc::int32_t             m_width;
    vfc::float32_t  m_slideChangeLimit;
    vfc::float32_t  m_moveChangeLimit;
};

class ViewChangeOverlaySetting : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(ViewChangeOverlaySetting) // PRQA S 2428
    {
        ADD_MEMBER(std::string, overlayPath);
        ADD_INT_MEMBER(displayDelay);
        ADD_MEMBER(vfc::float32_t,FLY);
        ADD_MEMBER(vfc::float32_t,FRY);
        ADD_MEMBER(vfc::float32_t,RLY);
        ADD_MEMBER(vfc::float32_t,RRY);
    }

    std::string m_overlayPath;
    vfc::int32_t  m_displayDelay = 3;
    vfc::float32_t m_FLY;
    vfc::float32_t m_FRY;
    vfc::float32_t m_RLY;
    vfc::float32_t m_RRY;
};

enum ButtonArea: vfc::uint8_t
{
    BUTTON_INVALID=0u,
    BUTTON_LT=1u,
    BUTTON_FL=2u,
    BUTTON_FT=3u,
    BUTTON_FR=4u,
    BUTTON_RT=5u,
    BUTTON_RR=6u,
    BUTTON_R =7u,
    BUTTON_RL=8u
};
class EllipticalSlidingBar :  public cc::assets::button::ButtonGroup
{
public:
    EllipticalSlidingBar( cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView = nullptr);
    osg::Camera* getReferenceView() const
    {
        return m_referenceView;
    }

    void setReferenceView(osg::Camera* f_referenceView)
    {
        m_referenceView = f_referenceView;
    }

    void update();

    enum TouchStatus : vfc::uint8_t
    {
        TOUCH_INVALID = 0u,
        TOUCH_DOWN = 1u,
        TOUCH_UP = 2u,
        TOUCH_MOVE = 3u
    };
protected:
    virtual ~EllipticalSlidingBar() = default;

    osg::Camera* m_referenceView;

    void traverse(osg::NodeVisitor& f_nv) override;

    void getPointOnEllipse(vfc::float32_t ratio, vfc::float32_t& f_x, vfc::float32_t& f_y);

    vfc::float32_t calculateAngleRatio(vfc::float32_t pointX, vfc::float32_t pointY);

    void closestPointOnEllipse(vfc::float32_t pointX, vfc::float32_t pointY, vfc::float32_t& ellipseX, vfc::float32_t& ellipseY);

private:
    void updateRealCamera();
    bool checkTouchInsideResponseArea(const vfc::float32_t f_x,const vfc::float32_t f_y);
    bool checkTouchInsideButtonResponseArea(const vfc::float32_t f_x,const vfc::float32_t f_y,const osg::Vec2f& f_buttonCenter,const osg::Vec2f& f_responseArea);
    bool checkAllButtons(const vfc::float32_t f_x,const vfc::float32_t f_y);
    void updateCamera();
    vfc::float32_t getPreviewidToAxis(const EScreenID f_previewid);
    bool checkViewChangeForButton(const vfc::float32_t f_x,const vfc::float32_t f_y,vfc::uint8_t f_touchEvent,const vfc::float32_t f_angleY);
    ViewChangeOverlay*   m_viewChangeOverlay;
    pc::core::Framework*  m_framework;
    ButtonArea            m_buttonArea;
    vfc::float32_t        m_y;
    vfc::float32_t        m_angleRatio; //the ratio of 360
    const vfc::int32_t    m_maxY=1800;
    vfc::float32_t        m_slideChangeLimit;
    vfc::float32_t        m_moveChangeLimit;
    bool                  m_isAnimating;
    cc::target::common::EThemeTypeDayNight    m_dayNightTheme = cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT;
    const EllipticalSlidingBarSettings* m_settings;
    const vfc::int32_t    m_a;
    const vfc::int32_t    m_b;
    const vfc::int32_t    m_midX;
    const vfc::int32_t    m_midY;
    const vfc::int32_t    m_width;
    const vfc::int32_t    m_displayDelay = 3;
};


class ViewChangeOverlay : public cc::assets::button::Button
{
public:
    friend class EllipticalSlidingBar;
    ViewChangeOverlay(cc::core::AssetId f_assetId, cc::core::CustomFramework* f_framework,osg::Camera* f_referenceView);
    ~ViewChangeOverlay() = default;
    void       setIconCenter(vfc::float32_t f_x,vfc::float32_t f_y);
    void       setY(vfc::float32_t f_y){m_y=f_y;};
    bool       allowShowInGearR(vfc::float32_t f_y){return f_y >= m_settings->m_FRY && f_y <= m_settings->m_FLY;};
    bool       allowShowInGearD(vfc::float32_t f_y){return f_y >= m_settings->m_RLY || f_y <= m_settings->m_RRY;};
    osg::Vec2f getIconSize()
    {
        osg::Vec2f l_size{0,0};
        pc::assets::Icon::UnitType l_type;
        m_icon->getSize(l_size,l_type);
        return l_size;
    };
protected:
    void update()override;
    vfc::float32_t calculateRotationAngle(vfc::float32_t f_x, vfc::float32_t f_y);
    EScreenID getScreenId() const
    {
        return m_screenId;
    }

private:
    using RotateThemeType = cc::target::common::EThemeTypeHU;
    RotateThemeType m_rotateTheme = RotateThemeType::ETHEME_TYPE_HORI;
    osg::ref_ptr<uielements::RotateIcon> m_icon;
    osg::Vec2f m_iconCenter;
    pc::core::Framework*      m_framework;
    ESVSViewMode m_preViewMode;
    EGear        m_preGear;
    EScreenID m_screenId;
    vfc::int32_t  m_delayCounter;
    bool m_gearChangedToP;
    ViewChangeOverlaySetting* m_settings;
    vfc::int32_t m_y=0;
    void init();
    void onInvalid() override
    {
        m_icon->setEnabled(false);
    }

    void onUnavailable() override
    {
        m_icon->setEnabled(false);
    }

    void onAvailable() override
    {
        m_icon->setEnabled(true);
    }

    void onPressed() override
    {
        m_icon->setEnabled(true);
    }

    void onReleased() override
    {
        m_icon->setEnabled(true);
    }
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_ELLIPTICAL_SLIDING_BAR_H
