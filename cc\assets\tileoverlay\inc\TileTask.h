//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileTask.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_TILEOVERLAY_TILETASK_H
#define CC_ASSETS_TILEOVERLAY_TILETASK_H

#include "cc/assets/tileoverlay/inc/TileSpline.h"
#include "cc/assets/tileoverlay/inc/TileOverlay.h"
#include "cc/core/inc/CustomUltrasonic.h"

#include "pc/svs/worker/core/inc/Task.h"
#include "pc/svs/util/math/inc/FilterSpatial.h"

//! forward declarations
namespace pc
{
namespace worker
{
namespace fusion
{
class FusionTask;
} // namespace fusion
} // namespace worker
} // namespace pc


namespace cc
{
namespace assets
{
namespace tileoverlay
{

//======================================================
// ProcessingTask
//------------------------------------------------------
/// Main logi to handle the overlay update
/// <AUTHOR>
//======================================================
class ProcessingTask : public pc::worker::core::Task
{
public:

  typedef pc::util::SpatialFilter<pc::util::FloatList> SpatialFilter;

  ProcessingTask(TileOverlay* f_tileOverlay, bool f_isVehOffset);

  virtual bool onRun(pc::worker::core::TaskManager* f_taskManager);

protected:

  virtual ~ProcessingTask();

private:

  //! Copy constructor is not permitted.
  ProcessingTask (const ProcessingTask& other); // = delete
  //! Copy assignment operator is not permitted.
  ProcessingTask& operator=(const ProcessingTask& other); // = delete
  
  osg::observer_ptr<TileOverlay> m_tileOverlay;
  bool m_flankAvailable;
  bool m_rearAvailable;
  bool m_staticStateReached;
  osg::ref_ptr<SpatialFilter> m_distanceFilter;
  pc::util::FloatList m_distancesCurrent;
  pc::util::FloatList m_distancesTarget;

  std::vector<TileSectorData>                       m_sectors;
  osg::ref_ptr<cc::core::CustomZoneLayout>          m_zoneLayout;
  unsigned int                                      m_ussZoneNum;
  osg::ref_ptr<TileUpdateVisitor>                   m_updateVisitor;
  bool                                              m_isVehOffset;
};

} // namespace tileoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TILEOVERLAY_TILETASK_H