//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: BOK1YH (XC-DA/EPF5) Bokelmann Karin
//  Department: CC-DA/EPF5
//=============================================================================
/// @swcomponent SV3D
/// @file  CustomFloorPlateRenderer.h
/// @brief
//=============================================================================

#ifndef CUSTOMFLOOR_PLATE_RENDER_H
#define CUSTOMFLOOR_PLATE_RENDER_H

#include "pc/svs/assets/floorplate/inc/FloorPlateRenderer.h"
#include "pc/svs/assets/floorplate/inc/BlurredFloorPlate.h"
#include "pc/svs/assets/floorplate/inc/NoCamBorderUpdateVisitor.h"
#include "pc/svs/assets/floorplate/inc/StripedBasePlate.h"
#include "pc/svs/factory/inc/Floor.h"
#include "pc/svs/animation/inc/Animation.h"

namespace cc
{
namespace assets
{
namespace common
{

class CustomFloorPlateRendererSettings : public pc::util::coding::ISerializable
{
public:
  CustomFloorPlateRendererSettings()
  : m_texturedBasePlateFadeoutSpeedMS(500) // in ms
  , m_frameDurationMS(33.0f) // assuming 30fps -> 33ms per frame
  {
  }

  SERIALIZABLE(CustomFloorPlateRendererSettings)
  {
    ADD_FLOAT_MEMBER(texturedBasePlateFadeoutSpeedMS);
    ADD_FLOAT_MEMBER(frameDurationMS);
  }

  float m_texturedBasePlateFadeoutSpeedMS;
  float m_frameDurationMS;
};

enum class FadingState : unsigned int
{
    IDLE,
    FADEIN_ONGOING,
    FADEOUT_ONGOING
};

enum BaseplateType
{
  Blurred = 0,
  History = 1
};

class CustomFloorPlateRenderer : public pc::assets::FloorPlateRenderer
{
public:
    /**
     * @note 'f_enableFOVBasedCulling' is used to enable/disable floor vertices FOV based culling on camera texture
     * 
     * In the case of Curb view the FOV base vertex culling is enabled/disabled by calling or not isInsideFOVInline().
     */
    CustomFloorPlateRenderer(pc::factory::Floor* f_floor, const pc::worker::bowlshaping::PolarBowlLayout& f_polarBowlLayout, pc::texfloor::core::FloorGenerator* f_floorPlateGenerator, cc::core::AssetId f_floorPlateAssetId, pc::core::Framework* f_framework, const bool f_enableFOVBasedCulling);
    CustomFloorPlateRenderer(pc::factory::Floor* f_floor, const pc::worker::bowlshaping::PolarBowlLayout& f_polarBowlLayout, pc::texfloor::core::FloorGenerator* f_floorPlateGenerator, pc::assets::FloorPlateStateHandler* f_floorPlateStateHandler, cc::core::AssetId f_floorPlateAssetId, pc::core::Framework* f_framework, const bool f_enableFOVBasedCulling);

    void animateAlpha() override;
    void updateAlphaAnimationForBaseplate(const BaseplateType f_baseplateType);

    FadingState m_fadingStateBlurredBP;
    FadingState m_fadingStateHistoricBP;

    osg::Timer   m_timer;
    osg::Timer_t m_timeoutBlurredBP;
    osg::Timer_t m_timeoutHistoricBP;
    osg::Timer_t m_timeout;
};

} //namespace common
} //namespace assets
} //namespace cc

#endif // CUSTOMFLOOR_PLATE_RENDER_H
