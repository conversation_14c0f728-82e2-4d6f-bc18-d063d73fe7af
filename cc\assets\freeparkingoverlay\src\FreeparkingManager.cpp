//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EPF2-CN)
//  Department: CC-DA/EPF
//=============================================================================
/// @swcomponent CC DAI
/// @file  FreefreeparkingOverlayManager.cpp
/// @brief
//=============================================================================

// #include "vis-dongfeng/assets/freeparkingoverlay/freeparking_utils.hpp"
#include "vis-dongfeng/assets/freeparkingoverlay/freeparking_manager.hpp"
#include "vis-dongfeng/assets/freeparkingoverlay/freeparking_event_handler.hpp"
#include "vis-dongfeng/assets/freeparkingoverlay/freeparking_overlay.hpp"
#include "vis-dongfeng/core/custom_mechanical_data.hpp"

#include "generic/coding/coding_manager.hpp"
#include "generic/logging/logging.hpp"
#include "generic/logging/logging_contexts.hpp"

#include "vis/animation/animation_manager.hpp"
#include "vis/util/math/common_math.hpp" //updateIfGreater
#include "vis/util/math/math_2d.hpp"
#include "vis/vehicle/mechanical_data.hpp"

// #include "cc/assets/corner/ViewportCorner.hpp"
#include "vis-dongfeng/core/custom_framework.hpp"
#include "vis-dongfeng/core/custom_scene.hpp"
#include "vis-dongfeng/daddy/custom_daddy_ports.hpp"
#include "vis-dongfeng/sm/viewmode/ViewModeStateMachine.h"
// #include "cc/views/planview/PlanView.hpp"

#include "vis-dongfeng/views/imguiview/imgui_manager.hpp"

#ifdef TARGET_STANDALONE
#include "imgui/implot/implot.h"
#endif

using pc::util::logging::g_AppContext;

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = (port).getData(); /* PRQA S 1030 */                                                        \
    (allPortHaveDataFlag) = (allPortHaveDataFlag) && ((dataDaddy) != nullptr);                                         \
    if ((dataDaddy) == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

pc::util::coding::Item<FreeparkingManagerSettings> g_managerSettings("FreeparkingManager");

void clampAngleDegree(vfc::float32_t& f_angle)
{
    while (f_angle < 0.0f)
    {
        f_angle += 360.0f;
    }
    while (f_angle > 360.0f)
    {
        f_angle -= 360.0f;
    }

    if (f_angle > 180.0f)
    {
        f_angle = -(360.0f - f_angle);
    }
}

void clampAngleRad(vfc::float32_t& f_angle)
{
    while (f_angle < 0.0f) // PRQA S 4234
    {
        f_angle += 2.f * static_cast<vfc::float32_t>(osg::PI);
    }
    while (f_angle > 2.f * static_cast<vfc::float32_t>(osg::PI)) // PRQA S 4234
    {
        f_angle -= 2.f * static_cast<vfc::float32_t>(osg::PI);
    }
}

//!
//! FreeparkingManager
//!
FreeparkingManager::FreeparkingManager(pc::core::Framework* f_framework, const pc::core::Viewport* f_viewport)
    : osg::Group()
    , m_framework{f_framework}
    , m_freeparkingOverlayAssets{}
    , m_lastUpdate{}
    , m_SlitheringFlag{}
    , m_MVPmatrix{}
    , m_MVPmatrixValid{}
    , m_spotSize{} // slot size in meter
    , m_centerPosition{g_managerSettings->m_defaultPositionParallel}
    , m_rotateAngle{g_managerSettings->m_defaultAngle}
    , m_horizontalPad{g_managerSettings->m_horizontalPad_default}
    , m_viewport{f_viewport}
    , m_isUserFinishedMoving{}
    , m_firstEntry{}
    , m_isSlotMovedByUser{false}
    , m_canReset{false}
    , m_360Rotate{false}
    , m_previousIsLeft{}
    , m_lastConfigUpdate{~0u}
    , m_SlitherPos{}
    , m_prevSlitherActionType{UNKOWN}
    , m_SlitherActionType{UNKOWN}
    , m_SlitherBeginPos{}
    , m_FrontLeft_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_FrontRight_vertex_sreen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_RearLeft_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_RearRight_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_Center_vertex_screen{osg::Vec3f(0.f, 0.f, 0.f)}
    , m_viewportTopLeft{osg::Vec2f(0.f, 0.f)}
    , m_viewportTopRight{osg::Vec2f(0.f, 0.f)}
    , m_viewportBottomLeft{osg::Vec2f(0.f, 0.f)}
    , m_viewportBottomRight{osg::Vec2f(0.f, 0.f)}
    , m_spotRect{g_managerSettings->m_defaultPositionParallel}
    , m_SlotOrientation{}
    , m_freeparkingActive{false}
    , m_freeparkingType{EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL}
    , m_touchStatus{TouchStatus::Invalid}
    , m_touchPosition{0.0f, 0.0f}
    , m_parkable{}
    , m_parkingSlotStateCtr{}
    , m_outputAngle{}
    , m_outputSlotCenter{}
    , m_outputRearAxleCenter{}
{
    setName("FreeparkingManager");
    setNumChildrenRequiringUpdateTraversal(1u);
    setCullingActive(false);
    // setCullCallback(m_touchScreenCallback.get());

    //! setup parking spots
    m_freeparkingOverlayAssets = new osg::Switch;
    m_freeparkingOverlayAssets->setName("FreeparkingAssets");
    m_SlotOrientation.m_yawAngleRaw = 0.0f;
    m_SlotOrientation.m_CenterPos   = g_managerSettings->m_defaultPositionParallel;
    m_freeparkingOverlayAssets->addChild(new FreeparkingOverlay, false); // PRQA S 3803
    osg::Group::addChild(m_freeparkingOverlayAssets.get());              // PRQA S 3803
    // TODO: meterPerPixel should be obtained from mainViewport class
    // updateFreeparkingSpotSize();

    addEventCallback(new FreeparkingEventHandler{});
}

void FreeparkingManager::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (m_lastUpdate != f_nv.getFrameStamp()->getFrameNumber())
        {
            m_lastUpdate = f_nv.getFrameStamp()->getFrameNumber();
            update();
            logInternal();
        }
    }
    if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
    {
        osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*>(&f_nv);
        m_MVPmatrix                      = (*(l_cv->getModelViewMatrix())) * (*(l_cv->getProjectionMatrix()));
        m_MVPmatrixValid                 = true;
    }
    osg::Group::traverse(f_nv);
}

FreeparkingOverlay* FreeparkingManager::getFreeparkingOverlay(vfc::uint32_t f_index)
{
    return static_cast<FreeparkingOverlay*>(m_freeparkingOverlayAssets->getChild(f_index));
}

void FreeparkingManager::setTouchData(TouchStatus f_touchStatus, const osg::Vec2f& f_touchPoint)
{
    if (m_touchStatus != f_touchStatus)
    {
        if ((m_touchStatus == TouchStatus::Down || m_touchStatus == TouchStatus::Move) &&
            f_touchStatus == TouchStatus::Up)
        {
            m_isUserFinishedMoving = true;
        }
        else
        {
            m_isUserFinishedMoving = false;
        }
    }
    else if (f_touchStatus == TouchStatus::Move)
    {
        m_isUserFinishedMoving = false;
    }
    else if (f_touchStatus == TouchStatus::Up)
    {
        m_isUserFinishedMoving = true;
    }
    m_touchStatus   = f_touchStatus;
    m_touchPosition = f_touchPoint;
}

void FreeparkingManager::reset()
{
    m_SlitherPos.StartPos           = osg::Vec2f(0.0f, 0.0f);
    m_SlitherPos.EndPos             = osg::Vec2f(0.0f, 0.0f);
    m_SlotOrientation.m_yawAngleRaw = 0.0f;
    switch (m_freeparkingType)
    {
    case EFreeParkingSpaceType::IPS_PS_TYPE_CROSS:
    {
        m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionCross;
        break;
    }
    case EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL:
    {
        m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionDiagonal;
        break;
    }
    case EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL:
    default:
    {
        m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionParallel;
        break;
    }
    }
    m_centerPosition = m_SlotOrientation.m_CenterPos;
    m_rotateAngle    = g_managerSettings->m_defaultAngle;
    if (!m_freeparkingActive && !m_firstEntry)
    {
        updateSpotSize();
        updateFreeparkingOverlay();
    }
    m_isSlotMovedByUser = false;
    m_firstEntry        = true;
}

void FreeparkingManager::updateInput()
{
    if (g_managerSettings->getModifiedCount() != m_lastConfigUpdate)
    {
        // updateFreeparkingSpotSize();
        m_lastConfigUpdate = g_managerSettings->getModifiedCount();
    }

    const auto customFramework  = m_framework->asCustomFramework();
    bool       allPortsHaveData = true;

    m_previousIsLeft                   = m_centerPosition.y() > 0.0f;
    m_360Rotate                        = IMGUI_GET_CHECKBOX_BOOL("FreeparkingManager", "360 Rotate");
    m_freeparkingActive                = IMGUI_GET_CHECKBOX_BOOL("FreeparkingManager", "Freeparking Active");
    m_canReset                         = IMGUI_GET_CHECKBOX_BOOL("FreeparkingManager", "Freeparking can Reset");
    m_parkable                         = IMGUI_GET_CHECKBOX_BOOL("FreeparkingManager", "Freeparking Parkable")
                                             ? EFreeParkingSlotState::AVAILABLE
                                             : EFreeParkingSlotState::UNAVAILABLE;
    const bool l_previous360Rotate     = m_360Rotate;
    const auto previousFreeparkingType = m_freeparkingType;
    m_freeparkingType                  = EFreeParkingSpaceType::IPS_PS_TYPE_CROSS;
    if (m_360Rotate)
    {
        m_freeparkingType = EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL;
    }
    bool l_parkingTypeChanged = false;
    if (previousFreeparkingType != m_freeparkingType)
    {
        l_parkingTypeChanged = true;
    }
    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    if (m_parkable == EFreeParkingSlotState::AVAILABLE)
    {
        l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::AVAILABLE);
    }
    else
    {
        l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::UNAVAILABLE);
    }

    m_prevSlitherActionType = m_SlitherActionType;
    if (m_freeparkingActive && (m_360Rotate != l_previous360Rotate))
    {
        m_SlotOrientation.m_yawAngleRaw = 0.0f;
    }

    if (m_freeparkingActive && l_parkingTypeChanged && !m_360Rotate)
    {
        if (!m_isSlotMovedByUser)
        {
            switch (m_freeparkingType)
            {
            case EFreeParkingSpaceType::IPS_PS_TYPE_CROSS:
            {
                m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionCross;
                break;
            }
            case EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL:
            {
                m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionDiagonal;
                break;
            }
            case EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL:
            default:
            {
                m_SlotOrientation.m_CenterPos = g_managerSettings->m_defaultPositionParallel;
            }
            }
            // clang-format on
            l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::DEFAULT);
        }
        const bool l_isLeft = m_SlotOrientation.m_CenterPos.y() > 0.0f;
        if (m_freeparkingType == EFreeParkingSpaceType::IPS_PS_TYPE_CROSS)
        {
            m_SlotOrientation.m_yawAngleRaw = osg::DegreesToRadians((!l_isLeft) ? 90.0f : 90.0f);
        }
        else if (m_freeparkingType == EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL)
        {
            m_SlotOrientation.m_yawAngleRaw = osg::DegreesToRadians((!l_isLeft) ? 45.0f : 45.0f);
        }
        else
        {
            m_SlotOrientation.m_yawAngleRaw = 0.0f;
        }
    }

    if (!m_freeparkingActive && m_canReset)
    {
        reset();
    }
}

void FreeparkingManager::update()
{
    // using namespace cc::target::common;
    updateInput();

    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    l_freeparkingOverlay->setParkable(m_parkable);
    if (m_freeparkingActive)
    {
        getSlitherStartEndPoint();
        getSpotCornerCoorandSlitherType();
        UpdateSlotOrientation(); // update the orientation and rotate center
        updateSpotSize();
        updateFreeparkingOverlay();
        m_freeparkingOverlayAssets->setValue(0u, true);
        l_freeparkingOverlay->setVisibility(true);
        l_freeparkingOverlay->setRotateButtonVisibility(true);
    }
    else
    {
        m_freeparkingOverlayAssets->setValue(0u, false);
        l_freeparkingOverlay->setVisibility(false);
        l_freeparkingOverlay->setRotateButtonVisibility(false);
    }

    if (m_freeparkingActive &&
        (m_SlitherActionType == ROTATION || m_SlitherActionType == TRANSLATION || m_SlitherActionType == TELEPORTATION))
    {
        m_isSlotMovedByUser  = true;
        m_isSlotMovingByUser = true;
    }
    else
    {
        m_isSlotMovingByUser = false;
    }

    prepareOutput();
    if ((m_isUserFinishedMoving) || (m_freeparkingActive && m_firstEntry))
    {
        sendOutput();
        if (m_freeparkingActive && m_firstEntry)
        {
            m_firstEntry = false;
        }
    }

    if (m_isSlotMovingByUser)
    {
        l_freeparkingOverlay->setParkingPlanState(ParkingPlaneState::DEFAULT);
    }

#ifdef TARGET_STANDALONE
    // {
    //     auto&      l_fpSlot = cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.reserve();
    //     osg::Vec2f l_frontLeft;
    //     osg::Vec2f l_rearLeft;
    //     osg::Vec2f l_rearRight;
    //     osg::Vec2f l_frontRight;
    //     m_spotRect.updateRectPoints(l_frontLeft, l_rearLeft, l_rearRight, l_frontRight);
    //     if (m_outputSlotCenter.y() > 0.0f)
    //     {
    //         l_fpSlot.m_Data.m_frontLeft  = l_frontRight;
    //         l_fpSlot.m_Data.m_rearLeft   = l_frontLeft;
    //         l_fpSlot.m_Data.m_rearRight  = l_rearLeft;
    //         l_fpSlot.m_Data.m_frontRight = l_rearRight;
    //     }
    //     else
    //     {
    //         l_fpSlot.m_Data.m_frontLeft  = l_rearLeft;
    //         l_fpSlot.m_Data.m_rearLeft   = l_rearRight;
    //         l_fpSlot.m_Data.m_rearRight  = l_frontRight;
    //         l_fpSlot.m_Data.m_frontRight = l_frontLeft;
    //     }
    //     cc::daddy::CustomDaddyPorts::sm_FreeParkingSlotInternal_SenderPort.deliver();
    // }
#endif
}

void FreeparkingManager::updateSpotSize()
{
    m_spotSize = osg::Vec2f(
        pc::vehicle::g_mechanicalData->getLength() + g_managerSettings->m_lengthOffset,
        pc::vehicle::g_mechanicalData->getWidthWithMirrors() + g_managerSettings->m_widthOffset);
}

void FreeparkingManager::updateFreeparkingOverlay()
{
    FreeparkingOverlay* const l_freeparkingOverlay = getFreeparkingOverlay(0u);
    const osg::Vec2f          l_spotOverlaySize    = osg::Vec2f(0.f, 0.f);
    m_centerPosition                               = m_SlotOrientation.m_CenterPos;
    m_rotateAngle                                  = osg::RadiansToDegrees(m_SlotOrientation.m_yawAngleRaw);
    clampAngleDegree(m_rotateAngle);

    const bool l_isLeft = m_centerPosition.y() > 0.0f;

    osg::Vec2f l_frontLeft;
    osg::Vec2f l_rearLeft;
    osg::Vec2f l_rearRight;
    osg::Vec2f l_frontRight;
    m_spotRect.setLength(m_spotSize.x());
    m_spotRect.setWidth(m_spotSize.y());
    m_spotRect.setAngle(m_rotateAngle);
    m_spotRect.setCenterPoint(m_centerPosition);
    m_spotRect.updateRectPoints(l_frontLeft, l_rearLeft, l_rearRight, l_frontRight);

    constexpr vfc::uint8_t l_parkSlotTypeRemap[4] = {
        4u, 1u, 0u, 2u}; // parkspot input define is different from park spot plane type define
    l_freeparkingOverlay->setType(l_parkSlotTypeRemap[static_cast<vfc::int32_t>(m_freeparkingType)]);
    l_freeparkingOverlay->setSize(m_spotSize);
    l_freeparkingOverlay->setPosition(m_centerPosition);
    l_freeparkingOverlay->setAngle(m_rotateAngle);
    l_freeparkingOverlay->setIsLeftSide(l_isLeft);
    l_freeparkingOverlay->setSelectionState(
        cc::assets::parkingspots::ParkingSpot::SELECTABLE, cc::assets::parkingspots::ParkingSpot::SELECTABLE);
    l_freeparkingOverlay->dirty();
}

void FreeparkingManager::prepareOutput()
{
    // const bool           l_isLeft               = m_centerPosition.y() > 0.0f;
    // const vfc::float32_t l_slotAngle            = m_spotRect.getAngle();
    // const osg::Vec2f     l_slotCenter           = m_spotRect.getCenterPoint();
    // const osg::Vec2f     l_rearAxleCenterOffset = pc::vehicle::g_mechanicalData->getCenter();
    // m_outputAngle                         = l_slotAngle;
    // m_outputSlotCenter                    = m_spotRect.getCenterPoint();
    // m_outputRearAxleCenter                = l_slotCenter - pc::util::rotate(l_rearAxleCenterOffset, l_slotAngle);
}

void FreeparkingManager::sendOutput()
{
    IMGUI_LOG("FreeParking", "SendNewPort", "TRUE");
    osg::Vec2f l_frontLeft;
    osg::Vec2f l_rearLeft;
    osg::Vec2f l_rearRight;
    osg::Vec2f l_frontRight;
    m_spotRect.updateRectPoints(l_frontLeft, l_rearLeft, l_rearRight, l_frontRight);

    IMGUI_LOG("FreeParking", "l_frontLeft", std::to_string(l_frontLeft.x()) + " - " + std::to_string(l_frontLeft.y()));
    IMGUI_LOG("FreeParking", "l_rearLeft", std::to_string(l_rearLeft.x()) + " - " + std::to_string(l_rearLeft.y()));
    IMGUI_LOG("FreeParking", "l_rearRight", std::to_string(l_rearRight.x()) + " - " + std::to_string(l_rearRight.y()));
    IMGUI_LOG("FreeParking", "l_frontRight", std::to_string(l_frontRight.x()) + " - " + std::to_string(l_frontRight.y()));

    // auto& l_fpSlot        = cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.reserve();
    // l_fpSlot.m_Data.m_x   = m_centerPosition.x() * 100;
    // l_fpSlot.m_Data.m_y   = m_centerPosition.y() * 100;
    // l_fpSlot.m_Data.m_yaw = - osg::RadiansToDegrees(m_outputAngle);
    // cc::daddy::CustomDaddyPorts::sm_FreeParkingSlot_SenderPort.deliver();
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
