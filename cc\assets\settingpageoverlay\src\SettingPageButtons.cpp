//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//------------------------------------------------------------------------------
#include "cc/assets/settingpageoverlay/inc/SettingPageButtons.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060
#include "cc/util/pdmwriter/inc/PdmWriter.h"

namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

pc::util::coding::Item<SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>>
    static g_settingPageSwitchButtonsSettings("SettingPageSwitchButton");

SettingPageSwitchButton::SettingPageSwitchButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : cc::assets::button::Button{f_assetId, f_referenceView}
    , m_buttonSwitch{false}
    , m_framework{f_framework}
{
    setState(AVAILABLE);
    setName("SettingPage Swich Button");
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine // PRQA S 4262 // PRQA S 4264
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(f_buttonPos);
}

void SettingPageSwitchButton::onInvalid()
{
    setIconEnable(false);
}

void SettingPageSwitchButton::onUnavailable()
{
    setIconEnable(true);
}

void SettingPageSwitchButton::onAvailable()
{
    setIconEnable(true);
}

void SettingPageSwitchButton::onReleased()
{
    setIconEnable(true);
    // m_touchFocusView->setTouchFocused(cc::views::touchfocus::TouchFocusView::TEST_DIALOG);
}

void SettingPageSwitchButton::update()
{
    settingPageButtonUpdate();

    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)



    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)


    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

    if (g_settingPageSwitchButtonsSettings->getModifiedCount() != getSettingModifiedCount())
    {
        setSettingModifiedCount(g_settingPageSwitchButtonsSettings->getModifiedCount());
    }

    bool touchInsideResponseArea = false;
    if(m_hasCustomClickArea)
    {
        touchInsideResponseArea = checkTouchInsideCustomClickArea();
    }
    else
    {
        touchInsideResponseArea = checkTouchInsideResponseArea();
    }
    ButtonState currentState = getState();

    switch (currentState)
    {
    case AVAILABLE:
    {
        if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
        {
            currentState = PRESSED;
        }
        break;
    }
    case PRESSED:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = RELEASED;
        }
        break;
    }
    case RELEASED:
    {
        currentState = AVAILABLE;
    }
    break;
    case INVALID:
    case UNAVAILABLE:
    default:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = AVAILABLE;
        }
        break;
    }
    }
    setState(currentState);
}

SettingPageMODStatusButton::SettingPageMODStatusButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
{
    SettingPageSwitchButton::update(); // update setting from PDM when initd // PRQA S 4273
}

void SettingPageMODStatusButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (cc::daddy::CustomDaddyPorts::sm_PdmMODStatusDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmMODStatusDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(!getSwitchStatus());
            cc::daddy::CustomDaddyPorts::sm_PdmMODStatusDaddy_SenderPort.deliver();
        }
    }
}

void SettingPageMODStatusButton::settingPageButtonUpdate()
{
    cc::util::pdmwriter::PdmSettings* const l_pdmSetting =
        dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));  // PRQA S 3400
    const vfc::int32_t pdmMODStatus = l_pdmSetting->getPdmMODStatus();
    updateSwitch(static_cast<bool>(pdmMODStatus));
    if (cc::daddy::CustomDaddyPorts::sm_HU_MODStsDaddy_SenderPort.isConnected())
    {
        cc::daddy::HUMODStsDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_HU_MODStsDaddy_SenderPort.reserve();
        l_container.m_Data                      = getSwitchStatus();
        cc::daddy::CustomDaddyPorts::sm_HU_MODStsDaddy_SenderPort.deliver();
    }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}

SettingPageDGearActStatusButton::SettingPageDGearActStatusButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
{
    SettingPageSwitchButton::update(); // update setting from PDM when initd // PRQA S 4273
}

void SettingPageDGearActStatusButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (cc::daddy::CustomDaddyPorts::sm_DGearActStatusDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_DGearActStatusDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(!getSwitchStatus());
            cc::daddy::CustomDaddyPorts::sm_DGearActStatusDaddy_SenderPort.deliver();
        }
    }
}

void SettingPageDGearActStatusButton::settingPageButtonUpdate()
{
    cc::util::pdmwriter::PdmSettings* const l_pdmSetting =
        dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));  // PRQA S 3400
    const vfc::int32_t pdmDGearActStatus = l_pdmSetting->getPdmDGearActStatus();
    updateSwitch(static_cast<bool>(pdmDGearActStatus));
    if (cc::daddy::CustomDaddyPorts::sm_HU_DGearActStsDaddy_SenderPort.isConnected())
    {
        cc::daddy::HUDGearACTStsDaddy_t& l_container =
            cc::daddy::CustomDaddyPorts::sm_HU_DGearActStsDaddy_SenderPort.reserve();
        l_container.m_Data = getSwitchStatus();
        cc::daddy::CustomDaddyPorts::sm_HU_DGearActStsDaddy_SenderPort.deliver();
    }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}

SettingPageSteerActStatusButton::SettingPageSteerActStatusButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
{
    SettingPageSwitchButton::update(); // update setting from PDM when initd // PRQA S 4273
}

void SettingPageSteerActStatusButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (cc::daddy::CustomDaddyPorts::sm_PdmSteerActStatusDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmSteerActStatusDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(!getSwitchStatus());
            cc::daddy::CustomDaddyPorts::sm_PdmSteerActStatusDaddy_SenderPort.deliver();
        }
    }
}

void SettingPageSteerActStatusButton::settingPageButtonUpdate()
{
    cc::util::pdmwriter::PdmSettings* const l_pdmSetting =
        dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));  // PRQA S 3400
    const vfc::int32_t pdmSteeringActStatus = l_pdmSetting->getPdmSteerActStatus();
    updateSwitch(static_cast<bool>(pdmSteeringActStatus));
    if (cc::daddy::CustomDaddyPorts::sm_HU_SteerActStsDaddy_SenderPort.isConnected())
    {
        cc::daddy::HUSteerACTStsDaddy_t& l_container =
            cc::daddy::CustomDaddyPorts::sm_HU_SteerActStsDaddy_SenderPort.reserve();
        l_container.m_Data = getSwitchStatus();
        cc::daddy::CustomDaddyPorts::sm_HU_SteerActStsDaddy_SenderPort.deliver();
    }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}

SettingPagePasActStatusButton::SettingPagePasActStatusButton(
    cc::core::AssetId                     f_assetId,
    osg::Vec2f                            f_buttonPos,
    pc::core::Framework*                  f_framework,
    SettingPageSonarTrigLevelButtonGroup* f_settingPageSonarTrigLevelButtonGroup,
    osg::Camera*                          f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
    , m_isInactived{false}
    , m_settingPageSonarTrigLevelButtonGroup{f_settingPageSonarTrigLevelButtonGroup}
{
    SettingPageSwitchButton::update(); // update setting from PDM when initd // PRQA S 4273
}

void SettingPagePasActStatusButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (cc::daddy::CustomDaddyPorts::sm_PdmPasActStatusDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmPasActStatusDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(!getSwitchStatus());
            cc::daddy::CustomDaddyPorts::sm_PdmPasActStatusDaddy_SenderPort.deliver();
        }
    }
}

void SettingPagePasActStatusButton::settingPageButtonUpdate()
{
    cc::util::pdmwriter::PdmSettings* const l_pdmSetting =
        dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));  // PRQA S 3400
    const vfc::int32_t pdmPasActStatus = l_pdmSetting->getPdmPasActStatus();
    bool isPasActStatusButtonDeactived = false;
    if (m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.isConnected())
    {
        const cc::daddy::SonarAPPDataDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            isPasActStatusButtonDeactived = (l_pData->m_Data.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_DEACTIVE);
        }
    }
    if( isPasActStatusButtonDeactived )
    {
        updateSwitch(false);  //Deactive PasAcStatusButton but keep pdm
        setState(UNAVAILABLE);
    }
    else
    {
        updateSwitch(static_cast<bool>(pdmPasActStatus));
        if(m_isInactived !=  isPasActStatusButtonDeactived)
        {
            setState(AVAILABLE);
        }
    }

    m_isInactived = isPasActStatusButtonDeactived;
    if (cc::daddy::CustomDaddyPorts::sm_HU_PASActStsDaddy_SenderPort.isConnected())
    {
        cc::daddy::HUPASACTStsDaddy_t& l_container =
            cc::daddy::CustomDaddyPorts::sm_HU_PASActStsDaddy_SenderPort.reserve();
        l_container.m_Data = getSwitchStatus();
        cc::daddy::CustomDaddyPorts::sm_HU_PASActStsDaddy_SenderPort.deliver();
    }
    if(m_settingPageSonarTrigLevelButtonGroup != nullptr)
    {
        m_settingPageSonarTrigLevelButtonGroup->setSonarTrigLevelEnabled( getSwitchStatus());
    }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)

    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}


SettingPageNarrowLaneActButton::SettingPageNarrowLaneActButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
    , m_isInactived{false}
{
}

void SettingPageNarrowLaneActButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (cc::daddy::CustomDaddyPorts::sm_PdmNarrowLaneActStatusDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmNarrowLaneActStatusDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(!getSwitchStatus());
            cc::daddy::CustomDaddyPorts::sm_PdmNarrowLaneActStatusDaddy_SenderPort.deliver();
        }
    }
}

void SettingPageNarrowLaneActButton::settingPageButtonUpdate()
{
    cc::util::pdmwriter::PdmSettings* const l_pdmSetting =
        dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));  // PRQA S 3400
    const vfc::int32_t pdmNarrowLaneActStatus = l_pdmSetting->getPdmNarrowLaneACTStatus();
     bool isPasActStatusButtonDeactived = false;
    if (m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.isConnected())
    {
        const cc::daddy::SonarAPPDataDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            isPasActStatusButtonDeactived = (l_pData->m_Data.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_DEACTIVE);
        }
    }
    if( isPasActStatusButtonDeactived )
    {
        updateSwitch(false);  //Deactive PasAcStatusButton but keep pdm
        setState(UNAVAILABLE);
    }
    else
    {
        updateSwitch(pdmNarrowLaneActStatus);
        if(m_isInactived !=  isPasActStatusButtonDeactived)
        {
            setState(AVAILABLE);
        }
    }
    m_isInactived = isPasActStatusButtonDeactived;
    if (cc::daddy::CustomDaddyPorts::sm_HU_NarrowLaneActDaddy_SenderPort.isConnected())
    {
        auto& l_container =
            cc::daddy::CustomDaddyPorts::sm_HU_NarrowLaneActDaddy_SenderPort.reserve();
        l_container.m_Data = getSwitchStatus();
        cc::daddy::CustomDaddyPorts::sm_HU_NarrowLaneActDaddy_SenderPort.deliver();
    }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData); // PRQA S 4500  // PRQA S 1109

    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}

SettingPageVehTransStatusButton::SettingPageVehTransStatusButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
{
    SettingPageSwitchButton::update(); // update setting from PDM when initd // PRQA S 4273
}

void SettingPageVehTransStatusButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(!getSwitchStatus());
            cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.deliver();
        }
    }
}

void SettingPageVehTransStatusButton::settingPageButtonUpdate()
{
    GET_PORT_DATA(vehicleTransIconContainer, m_framework->asCustomFramework()->m_VehTransparenceStsInternalReceiver, vehicleTransIconPortHaveData)



    if (vehicleTransIconPortHaveData)
    {
        // XLOG_ERROR(g_AppContext, "Vehicle trans statsu recevied" << static_cast<int>(vehicleTransIconContainer->m_Data));
        updateSwitch(vehicleTransIconContainer->m_Data);
    }



    // if (cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.isConnected())
    // {
    //     cc::daddy::SVSVehTransStsDaddy_t& l_container =
    //         cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.reserve();
    //     l_container.m_Data = getSwitchStatus() ? 1u : 0u;
    //     cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.deliver();
    // }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}

cc::daddy::SonarDistTrigLevel SettingPageSonarTrigLevelButton::s_curSonarLevel =
    cc::daddy::SonarDistTrigLevel::ESonarDistTrigLevel_CLOSE;

SettingPageSonarTrigLevelButton::SettingPageSonarTrigLevelButton( // PRQA S 4207
    cc::core::AssetId                                                    f_assetId,
    osg::Vec2f                                                           f_buttonPos,
    const SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>& f_settingPageButtonSetting,
    cc::daddy::SonarDistTrigLevel                                        f_sonarDistTrigLevel,
    pc::core::Framework*                                                 f_framework,
    osg::Camera*                                                         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
    , m_framework{f_framework}
    , m_buttonSonarLevel{f_sonarDistTrigLevel}
    , m_sonarTrigLevelButtonTexturePath{f_settingPageButtonSetting}
{
    setState(AVAILABLE);
    setName("SettingPage SonarTrigLevel Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(f_buttonPos);
    SettingPageSwitchButton::update(); // PRQA S 4273
}

void SettingPageSonarTrigLevelButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (getButtonSonarLevel() != getCurrentSonarLevel())
        {
            modifyCurrentSonarLevel(getButtonSonarLevel());
        }
    }
}

void SettingPageSonarTrigLevelButton::settingPageButtonUpdate()
{
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }
    const bool l_enabled = getButtonEnabled();

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    if ((getButtonSonarLevel() == getCurrentSonarLevel()))
    {
        if (l_enabled)
        {
            setState(AVAILABLE);
            if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_day.m_EnabledTexturePath);
            }
            else
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_night.m_EnabledTexturePath);
            }
        }
        else
        {
            setState(UNAVAILABLE);
            if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_day.m_EnabledUnavailableTexturePath);
            }
            else
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_night.m_EnabledUnavailableTexturePath);
            }
        }
    }
    else
    {
        if (l_enabled)
        {
            setState(AVAILABLE);
            if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_day.m_DisabledTexturePath);
            }
            else
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_night.m_DisabledTexturePath);
            }
        }
        else
        {
            setState(UNAVAILABLE);
            if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_day.m_DisabledUnavailableTexturePath);
            }
            else
            {
                setTexturePath(m_sonarTrigLevelButtonTexturePath.m_night.m_DisabledUnavailableTexturePath);
            }
        }
    }
}

SettingPageSonarTrigLevelBackground::SettingPageSonarTrigLevelBackground( // PRQA S 4206
    cc::core::AssetId                                                    f_assetId,
    osg::Vec2f                                                           f_buttonPos,
    const SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>& f_settingPageButtonSetting,
    pc::core::Framework*                                                 f_framework,
    osg::Camera*                                                         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
    , m_framework{f_framework}
    , m_sonarTrigLevelBackgroundTexturePath{f_settingPageButtonSetting}
{
}

void SettingPageSonarTrigLevelBackground::settingPageButtonUpdate()
{
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }
    const bool l_backgroundEnabled = getBackgroundEnabled();

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (l_backgroundEnabled)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(m_sonarTrigLevelBackgroundTexturePath.m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(m_sonarTrigLevelBackgroundTexturePath.m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(m_sonarTrigLevelBackgroundTexturePath.m_day.m_EnabledUnavailableTexturePath);
        }
        else
        {
            setTexturePath(m_sonarTrigLevelBackgroundTexturePath.m_night.m_EnabledUnavailableTexturePath);
        }
    }
}

SettingPageSonarTrigLevelButtonGroup::SettingPageSonarTrigLevelButtonGroup(
    cc::core::AssetId                    f_assetId,
    const SettingPageSonarLevelSettings& f_settingPageSonarLevelButtonSetting,
    pc::core::Framework*                 f_framework,
    osg::Camera*                         f_referenceView)
    : ButtonGroup{f_assetId}
    , m_storedButtonSonarLevel{cc::daddy::SonarDistTrigLevel::ESonarDistTrigLevel_CLOSE}
    , m_sonarTrigLevelEnabled{false}
    , m_settingPageSonarLevelButtonSetting{f_settingPageSonarLevelButtonSetting}
{
    setName("SettingPageSonarTrigLevelButtonGroup");

    addButton(new SettingPageSonarTrigLevelBackground(
        cc::core::AssetId::EASSETS_SETTINGPAGE_SONAR_TRIG_LEVEL_BACKGROUND,
        m_settingPageSonarLevelButtonSetting.m_SonarTrigLevelBackgroundPos,
        m_settingPageSonarLevelButtonSetting.m_SonarTrigLevelBackgroundTexturePath,
        f_framework,
        f_referenceView));
    addButton(new SettingPageSonarTrigLevelButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_CLOSE_SONAR_TRIG_LEVEL_SUBBUTTON,
        m_settingPageSonarLevelButtonSetting.m_CloseSonarTrigLevelButtonPos,
        m_settingPageSonarLevelButtonSetting.m_CloseSonarTrigLevelButtonTexturePath,
        cc::daddy::SonarDistTrigLevel::ESonarDistTrigLevel_CLOSE,
        f_framework,
        f_referenceView));
    addButton(new SettingPageSonarTrigLevelButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_MIDDLE_SONAR_TRIG_LEVEL_SUBBUTTON,
        m_settingPageSonarLevelButtonSetting.m_MiddleSonarTrigLevelButtonPos,
        m_settingPageSonarLevelButtonSetting.m_MiddleSonarTrigLevelButtonTexturePath,
        cc::daddy::SonarDistTrigLevel::ESonarDistTrigLevel_MIDDLE,
        f_framework,
        f_referenceView));
    addButton(new SettingPageSonarTrigLevelButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_FAR_SONAR_TRIG_LEVEL_SUBBUTTON,
        m_settingPageSonarLevelButtonSetting.m_FarSonarTrigLevelButtonPos,
        m_settingPageSonarLevelButtonSetting.m_FarSonarTrigLevelButtonTexturePath,
        cc::daddy::SonarDistTrigLevel::ESonarDistTrigLevel_FAR,
        f_framework,
        f_referenceView));

    SettingPageSonarTrigLevelButton::modifyCurrentSonarLevel(
        static_cast<cc::daddy::SonarDistTrigLevel>(cc::util::pdmwriter::g_pdmSetting->getPdmSonarTrigLevel()));

    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    else
    {
        XLOG_ERROR(g_AppContext, "SettingPageSonarTrigLevelButtonGroup setReferenceView Failed");
    }
    SettingPageSonarTrigLevelButtonGroup:update();
}

void SettingPageSonarTrigLevelButtonGroup::update()
{
//    constexpr vfc::int32_t l_pdmSonarLevel = 0u;
    // update level from pdm to the button group
    m_storedButtonSonarLevel =
        static_cast<cc::daddy::SonarDistTrigLevel>(cc::util::pdmwriter::g_pdmSetting->getPdmSonarTrigLevel());

    // check if the level of button group is the same as pdm , or update into pdm
    if (m_storedButtonSonarLevel != SettingPageSonarTrigLevelButton::getCurrentSonarLevel())
    {
        m_storedButtonSonarLevel = SettingPageSonarTrigLevelButton::getCurrentSonarLevel();

        if (cc::daddy::CustomDaddyPorts::sm_PdmSonarLevelStatusDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmSonarLevelStatusDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::daddy::ESettingSonarLevelPdm>(m_storedButtonSonarLevel); // PRQA S 4899
            cc::daddy::CustomDaddyPorts::sm_PdmSonarLevelStatusDaddy_SenderPort.deliver();
        }
    }

    if (cc::daddy::CustomDaddyPorts::sm_SonarDistTrigLevel_SenderPort.isConnected())
    {
        cc::daddy::SonarDistTrigLevelDaddy_t& l_container =
            cc::daddy::CustomDaddyPorts::sm_SonarDistTrigLevel_SenderPort.reserve();
        l_container.m_Data = m_storedButtonSonarLevel;
        cc::daddy::CustomDaddyPorts::sm_SonarDistTrigLevel_SenderPort.deliver();
    }
    m_enabled = true;
}

SettingPageNightModeStatusButton::SettingPageNightModeStatusButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
{
}

void SettingPageNightModeStatusButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        updateSwitch(!getSwitchStatus());
    }
}

void SettingPageNightModeStatusButton::settingPageButtonUpdate()
{
    if (cc::daddy::CustomDaddyPorts::sm_HU_NightModeDaddy_SenderPort.isConnected())
    {
        cc::daddy::HUNightModeStsDaddy_t& l_container =
            cc::daddy::CustomDaddyPorts::sm_HU_NightModeDaddy_SenderPort.reserve();
        l_container.m_Data = getSwitchStatus();
        cc::daddy::CustomDaddyPorts::sm_HU_NightModeDaddy_SenderPort.deliver();
    }
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (m_buttonSwitch)
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_EnabledTexturePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_day.m_DisabledTexturePath);
        }
        else
        {
            setTexturePath(g_settingPageSwitchButtonsSettings->m_night.m_DisabledTexturePath);
        }
    }
}


cc::daddy::EColorCode SettingPageVehColorButton::s_curVehColor = cc::daddy::EColorCode::NISSAN_WHITE;
SettingPageVehColorButton::SettingPageVehColorButton(
    cc::core::AssetId                                                    f_assetId,
    osg::Vec2f                                                           f_buttonPos,
    const SettingPageButtonSettings<SettingPageSwitchButtonTexturePath>& f_settingPageButtonSetting,
    cc::daddy::EColorCode                                                f_color,
    pc::core::Framework*                                                 f_framework,
    osg::Camera*                                                         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
    , m_framework{f_framework}
    , m_buttonVehColor{f_color}
    , m_vehColorButtonTexturePath{f_settingPageButtonSetting}
{
    setState(AVAILABLE);
    setName("SettingPage VehicleColor Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(f_buttonPos);
    SettingPageSwitchButton::update(); // PRQA S 4273
}

void SettingPageVehColorButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        if (getButtonVehColor() != getCurrentVehColor())
        {
            modifyCurrentVehColor(getButtonVehColor());
        }
    }
}

void SettingPageVehColorButton::settingPageButtonUpdate()
{
    if ((getButtonVehColor() == getCurrentVehColor()))
    {
        setTexturePath(m_vehColorButtonTexturePath.m_day.m_EnabledTexturePath);
    }
    else
    {
        setTexturePath(m_vehColorButtonTexturePath.m_day.m_DisabledTexturePath);
    }
}

SettingPageVehColorButtonGroup::SettingPageVehColorButtonGroup(
        cc::core::AssetId                       f_assetId,
        const SettingPageVehColorButtonSetting& f_settingPageVehColorButtonSetting,
        pc::core::Framework*                    f_framework,
        osg::Camera*                            f_referenceView)
    : ButtonGroup{f_assetId}
    , m_storedButtonVehColor{cc::daddy::EColorCode::NISSAN_WHITE}
    , m_settingPageVehColorButtonSetting{f_settingPageVehColorButtonSetting}
{
    setName("SettingPageVehColorButtonGroup");
    addButton(new SettingPageVehColorButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
        m_settingPageVehColorButtonSetting.m_VehicleColorWhiteButtonPos,
        m_settingPageVehColorButtonSetting.m_VehicleColorWhiteButtonTexturePath,
        cc::daddy::EColorCode::NISSAN_WHITE,
        f_framework,
        f_referenceView));
    addButton(new SettingPageVehColorButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
        m_settingPageVehColorButtonSetting.m_VehicleColorGreyButtonPos,
        m_settingPageVehColorButtonSetting.m_VehicleColorGreyButtonTexturePath,
        cc::daddy::EColorCode::NISSAN_SLIVER,
        f_framework,
        f_referenceView));
    addButton(new SettingPageVehColorButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
        m_settingPageVehColorButtonSetting.m_VehicleColorBlackButtonPos,
        m_settingPageVehColorButtonSetting.m_VehicleColorBlackButtonTexturePath,
        cc::daddy::EColorCode::NISSAN_BLACK,
        f_framework,
        f_referenceView));
    addButton(new SettingPageVehColorButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
        m_settingPageVehColorButtonSetting.m_VehicleColorPinkButtonPos,
        m_settingPageVehColorButtonSetting.m_VehicleColorPinkButtonTexturePath,
        cc::daddy::EColorCode::NISSAN_PINK,
        f_framework,
        f_referenceView));
    addButton(new SettingPageVehColorButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
        m_settingPageVehColorButtonSetting.m_VehicleColorBlueButtonPos,
        m_settingPageVehColorButtonSetting.m_VehicleColorBlueButtonTexturePath,
        cc::daddy::EColorCode::NISSAN_BLUE,
        f_framework,
        f_referenceView));
    addButton(new SettingPageVehColorButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
        m_settingPageVehColorButtonSetting.m_VehicleColorPurpleButtonPos,
        m_settingPageVehColorButtonSetting.m_VehicleColorPurpleButtonTexturePath,
        cc::daddy::EColorCode::NISSAN_PURPLE,
        f_framework,
        f_referenceView));

    SettingPageVehColorButton::modifyCurrentVehColor(
        static_cast<cc::daddy::EColorCode>(cc::util::pdmwriter::g_pdmSetting->getPdmColorIndex()));

    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    else
    {
        XLOG_ERROR(g_AppContext, "SettingPageSonarTrigLevelButtonGroup setReferenceView Failed");
    }
    SettingPageVehColorButtonGroup::update();
}

void SettingPageVehColorButtonGroup::update()
{
//    constexpr vfc::int32_t l_pdmSonarLevel = 0u;
    // update level from pdm to the button group
    m_storedButtonVehColor =
        static_cast<cc::daddy::EColorCode>(cc::util::pdmwriter::g_pdmSetting->getPdmColorIndex());

    // check if the level of button group is the same as pdm , or update into pdm
    if (m_storedButtonVehColor != SettingPageVehColorButton::getCurrentVehColor())
    {
        m_storedButtonVehColor = SettingPageVehColorButton::getCurrentVehColor();

        if (cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehColorStsDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehColorStsDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<vfc::uint8_t>(m_storedButtonVehColor);
            cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehColorStsDaddy_SenderPort.deliver();
        }
    }

    if (cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.isConnected())
    {
        cc::daddy::SVSVehColorAckDaddy_t& l_container =
            cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.reserve();
        l_container.m_Data = m_storedButtonVehColor;
        cc::daddy::CustomDaddyPorts::sm_SVSVehColorAckDaddy_SenderPort.deliver();
    }
    m_enabled = true;
}

static pc::util::coding::Item<SettingPageButtonSettings<SettingPageInfoButtonTexturePath>> g_settingPageInfoButtonsSettings("SettingPageInfoButton");
SettingPageInfoButton::SettingPageInfoButton(
    cc::core::AssetId                   f_assetId,
    pc::core::Framework*                f_framework,
    osg::Vec2f                          f_buttonPos,
    cc::assets::button::DialogID        f_dialogID,
    cc::assets::button::IDialogTrigger* f_dialogTrigger,
    osg::Camera*                        f_referenceView)
    : CustomButton{f_assetId, f_framework, f_referenceView}
    , m_dialogID{f_dialogID}
    , m_diaLogTrigger{f_dialogTrigger}
{
    setState(AVAILABLE);
    setName("SettingPage Info Button");
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine // PRQA S 4262 // PRQA S 4264
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(f_buttonPos);
}

void SettingPageInfoButton::onPressed()
{
    m_diaLogTrigger->tirgeDialog(m_dialogID);
}

void SettingPageInfoButton::update()
{
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
    {
        setTexturePath(g_settingPageInfoButtonsSettings->m_day.m_InfoButtonTexturePath);
    }
    else
    {
        setTexturePath(g_settingPageInfoButtonsSettings->m_night.m_InfoButtonTexturePath);
    }

    handleTouch();
}

pc::util::coding::Item<SettingPageButtonSettings<SettingPageFlipButtonTexturePath>>
    static g_settingPageFlipButtonSettings("SettingPageFlipButton");
SettingPageFlippingButton::SettingPageFlippingButton(
    cc::core::AssetId    f_assetId,
    osg::Vec2f           f_buttonPos,
    SettingPageFlipArea  f_lastPageFlipArea,
    SettingPageFlipArea  f_nextPageFlipArea,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : SettingPageSwitchButton{f_assetId, f_buttonPos, f_framework, f_referenceView}
    , m_lastPageFlipArea{f_lastPageFlipArea}
    , m_nextPageFlipArea{f_nextPageFlipArea}
{
    setState(AVAILABLE);
    setName("SettingPage Flipping Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(f_buttonPos);
    setCustomClickArea(osg::Vec2f((f_lastPageFlipArea.m_FlipAreaCenter + f_nextPageFlipArea.m_FlipAreaCenter) * 0.5f), osg::Vec2f(f_lastPageFlipArea.m_FlipResponseArea + f_nextPageFlipArea.m_FlipResponseArea)); // Merge top click area(forward flipping) and button click area
}

void SettingPageFlippingButton::onPressed()
{
    // use swtich button to flip. If current button status is close , target page is the second page.
    if(checkTouchInsideTargetClickArea(m_lastPageFlipArea.m_FlipAreaCenter, m_lastPageFlipArea.m_FlipResponseArea) && getSwitchStatus())
    {
        SettingPageGroupHandler::turnOver(SettingPageGroupID::PAGE_ONE);
        updateSwitch(!m_buttonSwitch);
    }
    else if (checkTouchInsideTargetClickArea(m_nextPageFlipArea.m_FlipAreaCenter, m_nextPageFlipArea.m_FlipResponseArea) && !getSwitchStatus())
    {
        SettingPageGroupHandler::turnOver(SettingPageGroupID::PAGE_TWO);
        updateSwitch(!m_buttonSwitch);
    }
    else
    {
        //Do nothing
    }

}

void SettingPageFlippingButton::settingPageButtonUpdate()
{
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();

    if (!getSwitchStatus())
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageFlipButtonSettings->m_day.m_FlipButtonGroupOneTexutrePath);
        }
        else
        {
            setTexturePath(g_settingPageFlipButtonSettings->m_night.m_FlipButtonGroupOneTexutrePath);
        }
    }
    else
    {
        if (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setTexturePath(g_settingPageFlipButtonSettings->m_day.m_FlipButtonGroupTwoTexutrePath);
        }
        else
        {
            setTexturePath(g_settingPageFlipButtonSettings->m_night.m_FlipButtonGroupTwoTexutrePath);
        }
    }
}

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc // PRQA S 1041
