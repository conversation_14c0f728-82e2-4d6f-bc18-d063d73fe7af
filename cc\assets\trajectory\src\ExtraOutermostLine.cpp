//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/util/osgx/inc/Quantization.h"
#include "cc/assets/trajectory/inc/ExtraOutermostLine.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "vfc/core/vfc_types.hpp"
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "osgDB/WriteFile"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

// This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
constexpr vfc::float32_t g_extraWidthForBlurMul = 1.2f; // (1 <= )


ExtraOutermostLine::ExtraOutermostLine(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  vfc::uint32_t f_numOfVerts)
  : GeneralTrajectoryLine{
      f_framework,
      cc::assets::trajectory::commontypes::Middle_enm /* DUMMY */,
      2u,
      f_height,
      f_trajParams,
      true}
  , mc_numOfVerts{f_numOfVerts}
{
  m_geometry->addPrimitiveSet(new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES)));  // PRQA S 3804  // PRQA S 3803  

  osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
  l_texCoords->setNormalize(true);
  l_texCoords->reserve(2u * mc_numOfVerts);
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; ++l_vertexIndex)
  {
    l_texCoords->push_back(osg::Vec2ub(0u, 127u));
  }
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; ++l_vertexIndex)
  {
    l_texCoords->push_back(osg::Vec2ub(255u, 127u));
  }
  m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray()); // PRQA S 3076
  l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

  const vfc::float32_t lc_halfWidth           = std::abs(m_trajParams.OutermostLine_Width) * 0.5f;
  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_gradientOuterEndPos = lc_halfWidth + lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_halfGeometryWidth   = lc_gradientOuterEndPos * g_extraWidthForBlurMul;
  m_lineGeometryWidth                = lc_halfGeometryWidth * 2.0f;
}


ExtraOutermostLine::~ExtraOutermostLine() = default;


osg::Image* ExtraOutermostLine::create1DTexture() const
{
  constexpr vfc::uint32_t  lc_imageWidth  = 64u; // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;  // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;  // Image depth in pixels, in case of a 3D image.
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1);

  const vfc::float32_t lc_halfWidth           = std::abs(m_trajParams.OutermostLine_Width) * 0.5f;
  const vfc::float32_t lc_halfGradientWidth   = std::abs(m_trajParams.GradientWidth) * 0.5f;
  const vfc::float32_t lc_gradientInnerEndPos = lc_halfWidth - lc_halfGradientWidth; // Measured from the middle line (right side of the image)
  const vfc::float32_t lc_gradientOuterEndPos = lc_halfWidth + lc_halfGradientWidth; // Measured from the middle line (right side of the image)

  const vfc::float32_t lc_gradientInnerEndPos_Normalized_Right = lc_gradientInnerEndPos / m_lineGeometryWidth + 0.5f; // Measured from the right side of the image.
  const vfc::float32_t lc_gradientOuterEndPos_Normalized_Right = lc_gradientOuterEndPos / m_lineGeometryWidth + 0.5f; // Measured from the right side of the image.
  const vfc::float32_t lc_gradientInnerEndPos_Normalized_Left = 1.0f - lc_gradientInnerEndPos_Normalized_Right;   // Measured from the left side of the image.
  const vfc::float32_t lc_gradientOuterEndPos_Normalized_Left = 1.0f - lc_gradientOuterEndPos_Normalized_Right;   // Measured from the left side of the image.

  const osg::Vec4ub l_lineColor_Inside = osg::Vec4ub(
    255u,
    255u,
    255u,
    pc::util::osgx::toUByte(m_trajParams.OutermostLine_Color_Manual.a()));
  osg::Vec4ub l_lineColor_Outside = l_lineColor_Inside;
  l_lineColor_Outside.a() = 0u;

  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(lc_imageWidth, lc_imageHeight, lc_imageDepth, GL_RGBA, GL_UNSIGNED_BYTE); //PRQA S 3143
  osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data());
  for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if ( (l_x_normalized < lc_gradientOuterEndPos_Normalized_Left)
      || (l_x_normalized > lc_gradientOuterEndPos_Normalized_Right) )
    {
      (*l_data) = l_lineColor_Outside;
    }
    else if ( (l_x_normalized > lc_gradientInnerEndPos_Normalized_Left)
           && (l_x_normalized < lc_gradientInnerEndPos_Normalized_Right) )
    {
      (*l_data) = l_lineColor_Inside;
    }
    else
    {
      if (l_x_normalized < lc_gradientInnerEndPos_Normalized_Left)
      {
        (*l_data) = helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Outside, l_lineColor_Inside, lc_gradientOuterEndPos_Normalized_Left, lc_gradientInnerEndPos_Normalized_Left, l_x_normalized);
      }
      else
      {
        (*l_data) = helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, lc_gradientInnerEndPos_Normalized_Right, lc_gradientOuterEndPos_Normalized_Right, l_x_normalized);
      }
    }
    ++l_data;
  }

  return l_image;
}





void ExtraOutermostLine::generateVertexData()
{
  generateVertexData_usingTexture();
}


void ExtraOutermostLine::generateVertexData_usingTexture()
{
  setCull(true);

  // *** 1. Create frame ***
  m_frame.removeAllPoints();

  vfc::float32_t l_touchPointAngle = 0.0f;
  //float l_touchPointLongitudinalPos = 0.0f;
  vfc::float32_t l_wheelCenterAngle = 0.0f;
  vfc::float32_t l_wheelCenterRadius = 0.0f;
  const vfc::float32_t lc_halfGeometryWidth = m_lineGeometryWidth * 0.5f;
  const vfc::float32_t lc_halfWidth = m_trajParams.OutermostLine_Width * 0.5f;
  const vfc::float32_t lc_touchPointToGeometryOuterEdge = lc_halfGeometryWidth - lc_halfWidth;
  const vfc::float32_t lc_touchPointToGeometryInnerEdge = m_lineGeometryWidth - lc_touchPointToGeometryOuterEdge;

  if (cc::assets::trajectory::commontypes::Rotation_enm == sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
  {
    if (0.0f < sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Pos.x())
    {
      if (cc::assets::trajectory::mainlogic::Forward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
      {
        // The extra touch point is on the front bumper and we are going forward.
        return;
      }
    }
    else
    {
      if (cc::assets::trajectory::mainlogic::Backward_enm == sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
      {
        // The extra touch point is on the rear bumper and we are going backward.
        return;
      }
    }


    // When it's rotation
    if (0.0f < sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Pos.y())
    {
      // Left outermost line:
      m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Radius
                             + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
      m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Radius
                             + lc_touchPointToGeometryInnerEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

      m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
      m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
      l_touchPointAngle = sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Angle;
      l_wheelCenterAngle = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Angle;
      l_wheelCenterRadius = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Radius;
    }
    else
    {
      // Right outermost line:
      m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Radius
                             + lc_touchPointToGeometryOuterEdge * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
      m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Radius
                             + lc_touchPointToGeometryInnerEdge * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

      m_frame.setVertexLineRadius(0u, m_frameRadiuses[1u]);
      m_frame.setVertexLineRadius(1u, m_frameRadiuses[0u]);
      l_touchPointAngle = sm_mainLogicRefPtr->getModelDataRef().ExtraTouchPoint.Angle;
      l_wheelCenterAngle = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Angle;
      l_wheelCenterRadius = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Radius;
    }
  }
  else
  {
    // When it's translation
    return;
  }
  m_frame.setBumperLineAngle(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
  m_frame.setBumperLinePos  (0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());

  osg::Vec4f l_lineColor;
  if (sm_mainLogicRefPtr->getInputDataRef().External.Parking.AutomaticParking)
  {
    l_lineColor = m_trajParams.OutermostLine_Color_Auto;
  }
  else
  {
    l_lineColor = m_trajParams.OutermostLine_Color_Manual;
  }
  l_lineColor.a() = 1.0f;

  cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint;
  const vfc::float32_t l_startAngle = l_touchPointAngle;
  const vfc::float32_t l_endAngle =
      l_wheelCenterAngle
        + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
          * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
          * ((sm_mainLogicRefPtr->getInputDataRef().External.Car.Wheelbase - 0.3f) / l_wheelCenterRadius);
  constexpr vfc::float32_t l_startPos = 0.0f; //l_touchPointLongitudinalPos;
  constexpr vfc::float32_t l_endPos = 0.0f; //l_startPos + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
//        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
//        * sm_mainLogicRefPtr->getInputDataRef().External.Car.Wheelbase;
  l_controlPoint.Angle = l_startAngle;
  l_controlPoint.LongitudinalPos = l_startPos;
  l_controlPoint.Color = l_lineColor;
  l_controlPoint.Index = 0u; // Dummy value. Will be calculated later.
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);

  vfc::float32_t l_tempAngle =
      l_controlPoint.Angle
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * (1.3f / m_frame.getVertexLineRadius(0u));
  vfc::float32_t l_tempPos =
      l_controlPoint.LongitudinalPos
      + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
        * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * 1.3f;
  m_frame.setFadeInStartAngle(0u, l_tempAngle);
  m_frame.setFadeInStartAngle(1u, l_tempAngle);

  m_frame.setFadeInStartPos(0u, l_tempPos);
  m_frame.setFadeInStartPos(1u, l_tempPos);

  m_frame.setFadeInEndAngle(0u, l_controlPoint.Angle);
  m_frame.setFadeInEndAngle(1u, l_controlPoint.Angle);

  m_frame.setFadeInEndPos(0u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeInEndPos(1u, l_controlPoint.LongitudinalPos);


  l_controlPoint.Angle = l_endAngle;
  l_controlPoint.LongitudinalPos = l_endPos;
  l_controlPoint.Color = l_lineColor;
  m_frame.addControlPoint(0u, l_controlPoint);
  m_frame.addControlPoint(1u, l_controlPoint);

  l_tempAngle = l_endAngle
                - sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul
                  * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
                  * (1.0f / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);
  l_tempPos = 0.0f; // Dummy, since the translation is disabled for this asset.
  m_frame.setFadeOutStartAngle(0u, l_tempAngle);
  m_frame.setFadeOutStartAngle(1u, l_tempAngle);

  m_frame.setFadeOutStartPos(0u, l_tempPos);
  m_frame.setFadeOutStartPos(1u, l_tempPos);

  m_frame.setFadeOutEndAngle(0u, l_controlPoint.Angle);
  m_frame.setFadeOutEndAngle(1u, l_controlPoint.Angle);

  m_frame.setFadeOutEndPos(0u, l_controlPoint.LongitudinalPos);
  m_frame.setFadeOutEndPos(1u, l_controlPoint.LongitudinalPos);

  // *** 2. Create vertices (and colors) ***
  // Generate line vertices
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray()); // PRQA S 3076
  l_vertices->clear();

  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray()); // PRQA S 3076
  l_colors->clear();

  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
  m_frame.generateVertices(  // PRQA S 3803
      0u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);

  m_frame.generateVertices(  // PRQA S 3803
      1u, 0u, 1u, sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint, m_height,
      l_vertices, l_colors, cc::assets::trajectory::frame::Manual_enm,
      mc_numOfVerts, sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
      l_translationAngle_Rad);


  // *** 3-A. Create indices ***
  osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*> (m_geometry->getPrimitiveSet(0u)); // PRQA S 3076
  l_indices->clear();
  m_frame.generateIndices(0u, 0u, 1u, 0u, mc_numOfVerts, l_indices);

  l_vertices->dirty();
  l_colors->dirty();
  l_indices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


} // namespace trajectory
} // namespace assets
} // namespace cc
