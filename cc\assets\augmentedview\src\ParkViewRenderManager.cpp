//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/augmentedview/inc/ParkViewRenderManager.h"
#include "osg/CullFace"

namespace cc
{
namespace assets
{

//!
//! ParkViewRenderManager
//!
ParkViewRenderManager::ParkViewRenderManager(
    pc::factory::RenderManagerRegistry* f_registry,
    cc::virtcam::VirtualCamEnum f_virtCam)
  : pc::factory::RenderManager{f_registry, f_virtCam}
  , m_hideEnvironment{false}
{
  osg::CullFace* const l_cullFace = new osg::CullFace(osg::CullFace::BACK);
  m_stateGraphWall.m_common->setAttributeAndModes(l_cullFace, osg::StateAttribute::ON);    // PRQA S 3143
}


ParkViewRenderManager::~ParkViewRenderManager() = default;


void ParkViewRenderManager::showEnvironment()
{
  m_hideEnvironment = false;
}


void ParkViewRenderManager::hideEnvironment()
{
  m_hideEnvironment = true;
}


void ParkViewRenderManager::updateInputData(const osg::FrameStamp* f_frameStamp)
{
  pc::factory::RenderManager::updateInputData(f_frameStamp);
  if (m_hideEnvironment)
  {
    m_degradationMaskTarget = 0xFu;
  }
}

} // namespace assets
} // namespace cc

