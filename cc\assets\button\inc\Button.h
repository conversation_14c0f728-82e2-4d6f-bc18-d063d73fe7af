//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_H
#define CC_ASSETS_BUTTON_H

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomIcon.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

#define GET_PORT_DATA(dataDaddy, port, allPortHaveDataFlag)                                                            \
    const auto dataDaddy  = port.getData();                                                                          \
    const bool allPortHaveDataFlag = (dataDaddy != nullptr);                                         \
    if (dataDaddy == nullptr)                                                                                        \
    {                                                                                                                  \
        XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \
    }

#define SEND_PORT(port, data) \
    {\
        auto& container = port.reserve(); \
        container.m_Data = (data); \
        port.deliver(); \
    }

#define SEND_PORT_MEMBER(port, member, data) \
    {\
        auto& container = port.reserve(); \
        container.m_Data.member = (data); \
        port.deliver(); \
    }

namespace pc
{
namespace core
{
class View;
} // namespace core

} // namespace pc

namespace cc
{
// namespace views
// {
// // namespace touchfocus
// // {
// // class TouchFocusView;
// // } // namespace touchfocus
// } // namespace views


namespace assets
{
namespace button
{

class ButtonTexturePath : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(ButtonTexturePath)
    {
        ADD_STRING_MEMBER(PressedTexturePath);
        ADD_STRING_MEMBER(AvailableTexturePath);
        ADD_STRING_MEMBER(UnavailableTexturePath);
        ADD_STRING_MEMBER(ReleasedTexturePath);
        ADD_STRING_MEMBER(SelectedTexturePath);
    }

    std::string m_PressedTexturePath;
    std::string m_AvailableTexturePath;
    std::string m_UnavailableTexturePath;
    std::string m_ReleasedTexturePath;
    std::string m_SelectedTexturePath;
};


class ButtonTextureSettings : public pc::util::coding::ISerializable
{
public:

    SERIALIZABLE(ButtonTextureSettings)
    {
        ADD_MEMBER(ButtonTexturePath, day);
        ADD_MEMBER(ButtonTexturePath, night);
    }

    ButtonTexturePath m_day;
    ButtonTexturePath m_night;
};

class IButtonUpdater
{
public:
	virtual void doUpdate(osg::NodeVisitor& f_nv, bool f_popListNotEmpty) = 0;
};

class ButtonPopController : public IButtonUpdater
{
public:
    static void registerPopButton(IButtonUpdater* f_popButtonGroup)
    {
        popList.push_back(f_popButtonGroup);
    }

    static void unRegisterPopButton()
    {
        if(!popList.empty())
        {
            popList.pop_back();
        }
    }

	void invokeUpdate(osg::NodeVisitor& f_nv)
	{
        this->doUpdate(f_nv, !popList.empty());
	}

private:
	static std::list<IButtonUpdater*> popList;
};

class Button : public pc::assets::ImageOverlays, public ButtonPopController
{
public:

    enum ButtonState : vfc::uint8_t
    {
        INVALID = 0u,
        UNAVAILABLE = 1u,
        AVAILABLE = 2u,
        PRESSED = 3u,
        RELEASED = 4u,
        SELECTED = 5u
    };

    enum TouchStatus : vfc::uint8_t
    {
        TOUCH_INVALID = 0u,
        TOUCH_DOWN = 1u,
        TOUCH_UP = 2u,
        TOUCH_MOVE = 3u
    };
public:
    // static void setTouchFocusView(cc::views::touchfocus::TouchFocusView* f_touchFocusView)
    // {
    //     m_touchFocusView = f_touchFocusView;
    // }

public:
    Button(cc::core::AssetId f_assetId, osg::Camera* f_referenceView=nullptr);

    void traverse(osg::NodeVisitor& f_nv) override;

    bool checkTouchInsideResponseArea(osg::Vec2f f_touch);

    osg::Vec2f getIconPositionScreen();

    osg::Vec2f getIconPositionHeadUnit();

    osg::Vec2f getResponseArea()
    {
        return m_responseArea;
    }

    cc::target::common::EThemeTypeHU getRotateTheme() const
    {
        return m_rotateTheme;
    }

    cc::target::common::EThemeTypeDayNight getDayNightTheme() const
    {
        return m_dayNightTheme;
    }

    void setHoriReferenceView(pc::core::View* f_view)
    {
        m_horiReferenceView = f_view;
    }

    void setVertReferenceView(pc::core::View* f_view)
    {
        m_vertReferenceView = f_view;
    }

    void setRotateTheme(cc::target::common::EThemeTypeHU f_theme)
    {
        if (f_theme != m_rotateTheme || m_referenceView == nullptr)
        {
            m_rotateTheme = f_theme;
            setReferenceView(m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI ? m_horiReferenceView : m_vertReferenceView);
            m_dirty = true;
        }
    }

    void setDayNightTheme(cc::target::common::EThemeTypeDayNight f_theme)
    {
        if (f_theme != m_dayNightTheme)
        {
            m_dayNightTheme = f_theme;
            m_dirty = true;
            m_dirtyState = true; // force update texture
        }
    }

    void setIconAtMiddle(bool f_enable)
    {
        m_iconAtMiddle = f_enable;
    }

    void setPositionHori(osg::Vec2f f_position)
    {
        m_positionHori = f_position;
    }

    void setPositionVert(osg::Vec2f f_position)
    {
        m_positionVert = f_position;
    }

    void setTexturePath(const std::string& f_texturePath)
    {
        if (m_texturePath != f_texturePath)
        {
            m_texturePath = f_texturePath;
            m_dirty = true;
        }
    }

    void setIconEnable(bool f_enable)
    {
        m_iconEnable = f_enable;
    }

    ButtonState getState() const
    {
        return m_state;
    }

    void setCustomClickArea( const osg::Vec2f& f_iconCenter, const osg::Vec2f& f_responseArea )
    {
        m_hasCustomClickArea = true;
        m_customClickArea.m_iconCenter = f_iconCenter;
        m_customClickArea.m_responseArea = f_responseArea;
    }

    void setCustomButtonSize(const osg::Vec2f& f_buttonSize)
    {
        m_customIconSize = f_buttonSize;
    }

protected:
    friend class Dialog;
    void init();
    virtual void update() = 0;
    // virtual bool isTouchFocused();

    void traverseUpdate(bool blockTouch);

    void doUpdate(osg::NodeVisitor& f_nv, bool f_blockTouch) override
    {
        traverseUpdate(f_blockTouch);
    }

    bool checkTouchInsideResponseArea();

    bool checkTouchInsideCustomClickArea();

    bool checkTouchInsideTargetClickArea(const osg::Vec2f& f_iconCenter, const osg::Vec2f& f_responseArea);

    bool checkTouchInsideViewport();

    void setState(ButtonState f_state)
    {
        if (m_state != f_state)
        {
            m_state = f_state;
            m_dirtyState = true;
        }
    }

    vfc::uint32_t getSettingModifiedCount() const
    {
        return m_settingModifiedCount;
    }

    void setSettingModifiedCount(vfc::uint32_t f_modifiedCount)
    {
        if (m_settingModifiedCount != f_modifiedCount)
        {
            m_settingModifiedCount = f_modifiedCount;
            init();
        }
    }

    TouchStatus touchStatus() const { return m_touchStatus; }
    void setTouchStatus(const TouchStatus &touchStatus) { m_touchStatus = touchStatus; }
    TouchStatus getTouchStatus() const { return m_touchStatus;};
    vfc::uint16_t huX() const { return m_huX; }
    void setHuX(const vfc::uint16_t &huX) { m_huX = huX; }

    vfc::uint16_t huY() const { return m_huY; }
    void setHuY(const vfc::uint16_t &huY) { m_huY = huY; }

    virtual void onInvalid() = 0;
    virtual void onUnavailable() = 0;
    virtual void onAvailable() = 0;
    virtual void onPressed() = 0;
    virtual void onReleased() = 0;
    virtual void onSelected() {};

    void updateIconCenter();

protected:
    bool m_dirty = true;
    bool m_dirtyState = true;
    ButtonState m_state = INVALID;
    osg::ref_ptr<uielements::CustomIcon> m_icon;
    bool m_iconEnable = false;
    bool m_isFocusing = false;
    bool m_hasCustomClickArea;
    // static cc::views::touchfocus::TouchFocusView* m_touchFocusView;

private:
    osg::Vec2f m_iconCenter;
    osg::Vec2f m_customIconSize;
    osg::Vec2f m_responseArea;
    bool m_iconAtMiddle = false;
    osg::Vec2f m_positionHori;
    osg::Vec2f m_positionVert;
    TouchStatus m_touchStatus = TOUCH_INVALID;
    vfc::uint16_t m_huX = 0u;
    vfc::uint16_t m_huY = 0u;
    pc::core::View* m_horiReferenceView;
    pc::core::View* m_vertReferenceView;
    std::string m_texturePath;
    cc::target::common::EThemeTypeHU m_rotateTheme = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
    cc::target::common::EThemeTypeDayNight m_dayNightTheme = cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT;
    vfc::uint32_t m_settingModifiedCount;
    cc::assets::uielements::UIData m_customClickArea;
};

class ButtonBackground : public Button
{
public:
    ButtonBackground(cc::core::AssetId f_assetId, osg::Camera* f_referenceView=nullptr);

protected:
    void update() override
    {
        setState(ButtonState::AVAILABLE);
        m_dirty = true;
    }

private:
    void onInvalid() override
    {
        setIconEnable(false);
    }
    void onUnavailable() override
    {
        setIconEnable(false);
    }
    void onAvailable() override
    {
        setIconEnable(true);
    }
    void onPressed() override
    {
        setIconEnable(true);
    }
    void onReleased() override {
        setIconEnable(true);
    }

private:
    // pc::core::Framework* m_framework;
    vfc::uint32_t m_modifiedCount = ~0u;
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_H
