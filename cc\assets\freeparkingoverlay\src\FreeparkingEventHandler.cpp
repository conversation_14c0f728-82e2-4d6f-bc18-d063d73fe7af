//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/freeparkingoverlay/inc/FreeparkingEventHandler.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

FreeparkingEventHandler::FreeparkingEventHandler()
    : rbp::vis_dongfeng::util::TouchEventHandler(true)
{
}

bool FreeparkingEventHandler::handle(
    const osgGA::GUIEventAdapter& f_ea,
    osgGA::GUIActionAdapter&      f_aa,
    osg::Object*                  f_obj,
    osg::NodeVisitor*             f_nv)
{
    bool l_hasEvents = rbp::vis_dongfeng::util::TouchEventHandler::handle(f_ea, f_aa, f_obj, f_nv);
    if (l_hasEvents)
    {
        auto l_freeparkingManager = dynamic_cast<FreeparkingManager*>(f_obj);
        if (nullptr != l_freeparkingManager)
        {
            return handleEvents(f_ea, l_freeparkingManager);
        }
    }
    return false;
}

bool FreeparkingEventHandler::handleEvents(const osgGA::GUIEventAdapter& f_ea, FreeparkingManager* f_freeparkingManager)
{
    if (1U != getNumTouchPoints())
    {
        return false;
    }
    const auto& l_touchPoint        = getTouchPoint(0U);
    bool        l_touchPointHandled = false;
    l_touchPointHandled             = true;
    switch (l_touchPoint.phase)
    {
    case osgGA::GUIEventAdapter::TOUCH_BEGAN:
    {
        f_freeparkingManager->setTouchData(
            FreeparkingManager::TouchStatus::Down, osg::Vec2f(l_touchPoint.x, l_touchPoint.y));
        break;
    }
    case osgGA::GUIEventAdapter::TOUCH_MOVED:
    {
        f_freeparkingManager->setTouchData(
            FreeparkingManager::TouchStatus::Move, osg::Vec2f(l_touchPoint.x, l_touchPoint.y));
        break;
    }
    case osgGA::GUIEventAdapter::TOUCH_ENDED:
    {
        f_freeparkingManager->setTouchData(
            FreeparkingManager::TouchStatus::Up, osg::Vec2f(l_touchPoint.x, l_touchPoint.y));
        break;
    }
    default:
        break;
    }
    return l_touchPointHandled;
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
