//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Vehicle2DOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "CustomSystemConf.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

#define CHECK_PORT_DATA(containerName, port, flag)                                                                     \
    auto const containerName = port.getData();                                                                            \
    if (containerName == nullptr)                                                                                    \
    {                                                                                                                  \
        flag = false;                                                                                                \
    }
namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons

pc::util::coding::Item<Vehicle2DSettings> g_Vehicle2DSettings("Vehicle2DSettings");

//!
//! @brief Construct a new Vehicle2DOverlay Manager:: Vehicle2DOverlay Manager object
//!
//! @param f_config
//!
Vehicle2DOverlayManager::Vehicle2DOverlayManager()
    : m_lastConfigUpdate{~0u}
    , m_Vehicle2DOverlays{}
    , m_mat_b{false}
    , m_colorIndex{0u}
    , m_parkActive{false}
{
}

Vehicle2DOverlayManager::~Vehicle2DOverlayManager() = default;

bool checkParkingActive(cc::target::common::EPARKStatusR2L f_parkStatus)
{
    bool l_parkActive = false;
    switch (f_parkStatus)
    {
    case cc::target::common::EPARKStatusR2L::PARK_Searching:
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend:
    case cc::target::common::EPARKStatusR2L::PARK_Terminated:
    case cc::target::common::EPARKStatusR2L::PARK_Completed:
    case cc::target::common::EPARKStatusR2L::PARK_Failure:
    case cc::target::common::EPARKStatusR2L::PARK_AssistStandby:
    {
        l_parkActive = true;
        break;
    }
    case cc::target::common::EPARKStatusR2L::PARK_Off:
    case cc::target::common::EPARKStatusR2L::PARK_Standby:
    default:
    {
        l_parkActive = false;
        break;
    }
    }
    return l_parkActive;
}

inline void setFilterLinear(osg::Texture2D* f_texture)
{
    if (f_texture)
    {
        f_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
        f_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    }
}

static vfc::float64_t calculateIconScaler(osg::Camera* f_camera)
{
    if (f_camera == nullptr)
    {
        return 0.0;
    }
    using pc::vehicle::g_mechanicalData;
    vfc::float64_t left = 0.0;
    vfc::float64_t right = 0.0;
    vfc::float64_t bottom = 0.0;
    vfc::float64_t top = 0.0;
    vfc::float64_t zFar = 0.0;
    vfc::float64_t zNear = 0.0;
    vfc::float64_t pixelPerMeter = 0.0;
    if (!f_camera->getProjectionMatrixAsOrtho(left, right, bottom, top, zNear, zFar))
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DOverlay: Cannot retrieve projection matrix!");
        return 0.f;
    }
    const auto           viewport            = f_camera->getViewport();
    const vfc::float64_t viewportWidth_Pixel = viewport->width();
    const vfc::float64_t viewportWidth_Meter = right - left;
    pixelPerMeter                      = viewportWidth_Pixel / viewportWidth_Meter;
    const vfc::float64_t iconLength          = static_cast<vfc::float32_t>(pixelPerMeter) * g_mechanicalData->getLength();
    return iconLength / g_Vehicle2DSettings->m_proportion.x();
}

void Vehicle2DOverlayManager::init(
    pc::assets::ImageOverlays* f_imageOverlays,
    core::CustomFramework*     f_framework,
    const vfc::uint8_t         f_colorIndex)
{
    // ! init Vehicle2DOverlay icons
    m_Vehicle2DOverlays.clear(f_imageOverlays);

    const auto l_color = getColorFromIndex(f_colorIndex);

    // DEFAULT
    // VEHICLE2D_BODY_OPEN
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_vehicleBodyOpen, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_BODY_OPEN)->getTexture()));
    // VEHICLE2D_FRONT_LEFT_DOOR_OPEN
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_frontLeftDoorOpen, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)->getTexture()));
    // VEHICLE2D_FRONT_RIGHT_DOOR_OPEN
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_frontRightDoorOpen, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)->getTexture()));
    // VEHICLE2D_REAR_LEFT_DOOR_OPEN
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_rearLeftDoorOpen, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)->getTexture()));
    // VEHICLE2D_REAR_RIGHT_DOOR_OPEN
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_rearRightDoorOpen, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)->getTexture()));
    // VEHICLE2D_TRUNK_OPEN
    m_Vehicle2DOverlays.addIcon(f_imageOverlays, createIconTopLeft(l_color.m_trunkOpen, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_OPEN)->getTexture()));
    // VEHICLE2D_FRONT_LEFT_DOOR_CLOSE
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_frontLeftDoorClose, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)->getTexture()));
    // VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_frontRightDoorClose, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)->getTexture()));
    // VEHICLE2D_REAR_LEFT_DOOR_CLOSE
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_rearLeftDoorClose, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)->getTexture()));
    // VEHICLE2D_REAR_RIGHT_DOOR_CLOSE
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays, createIconTopLeft(l_color.m_rearRightDoorClose, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)->getTexture()));
    // VEHICLE2D_TRUNK_CLOSE
    m_Vehicle2DOverlays.addIcon(f_imageOverlays, createIconTopLeft(l_color.m_trunkClose, {0.0f, 0.0f}, {0.0f, 0.0f}));
    setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_TRUNK_CLOSE)->getTexture()));
    //! Indicator
    // VEHICLE2D_LEFT_FRONT_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_leftIndicator.m_frontIndicatorOnTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_LEFT_FRONT_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_LEFT_REAR_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_leftIndicator.m_rearIndicatorOnTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_LEFT_REAR_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_LEFT_DOOR_OPEN_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_leftIndicator.m_doorOpenIndicatorTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_LEFT_DOOR_OPEN_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_LEFT_DOOR_CLOSE_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_leftIndicator.m_doorCloseIndicatorTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_LEFT_DOOR_CLOSE_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_RIGHT_FRONT_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_rightIndicator.m_frontIndicatorOnTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_RIGHT_FRONT_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_RIGHT_REAR_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_rightIndicator.m_rearIndicatorOnTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_RIGHT_REAR_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_RIGHT_DOOR_OPEN_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_rightIndicator.m_doorOpenIndicatorTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_RIGHT_DOOR_OPEN_INDICATOR_ON)->getTexture()));
    // VEHICLE2D_RIGHT_DOOR_CLOSE_INDICATOR_ON
    m_Vehicle2DOverlays.addIcon(
        f_imageOverlays,
        createIconTopLeft(
            g_Vehicle2DSettings->m_rightIndicator.m_doorCloseIndicatorTexturePath, {0.0f, 0.0f}, {0.0f, 0.0f}));
            setFilterLinear(const_cast<osg::Texture2D*>(m_Vehicle2DOverlays.getIcon(VEHICLE2D_RIGHT_DOOR_CLOSE_INDICATOR_ON)->getTexture()));
    vfc::uint32_t l_viewId = std::numeric_limits<vfc::uint32_t>::max();
    if(!updateReferenceView(f_imageOverlays, f_framework, l_viewId))
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DOverlay: f_imageOverlays not ready " );
    }
    else
    {
        updateIconsSizePosition(l_viewId, f_imageOverlays, f_framework);
    }
}

const Vehicle2DOverlaySettings Vehicle2DOverlayManager::getColorFromIndex(vfc::uint8_t f_colorIndex)
{
    switch (static_cast<vfc::int32_t>(f_colorIndex))
    {
    case cc::daddy::NISSAN_WHITE:
        {return g_Vehicle2DSettings->m_white;}
    case cc::daddy::NISSAN_SLIVER:
        {return g_Vehicle2DSettings->m_sliver;}
    case cc::daddy::NISSAN_BLACK:
        {return g_Vehicle2DSettings->m_black;}
    case cc::daddy::NISSAN_PINK:
        {return g_Vehicle2DSettings->m_pink;}
    case cc::daddy::NISSAN_BLUE:
        {return g_Vehicle2DSettings->m_blue;}
    case cc::daddy::NISSAN_PURPLE:
        {return g_Vehicle2DSettings->m_purple;}
    case cc::daddy::NISSAN_TRANSPARENT:
        {return g_Vehicle2DSettings->m_transparent;}
    default:
        {return g_Vehicle2DSettings->m_sliver;}
    }
}

void Vehicle2DOverlayManager::update(
    pc::assets::ImageOverlays* f_imageOverlays,
    core::CustomFramework*     f_framework) // PRQA S 6040 // PRQA S 6043 // PRQA S 6041
{
    if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
    {
        return;
    }
    pc::assets::IconGroup* const l_Vehicle2DOverlays = &m_Vehicle2DOverlays;
    m_Vehicle2DOverlays.setAllEnabled(false);
    bool allPortsHaveData = true;
    CHECK_PORT_DATA(themeContainer, f_framework->m_SVSRotateStatusDaddy_Receiver, allPortsHaveData);
    CHECK_PORT_DATA(colorContainer, f_framework->m_vehColorSts_ReceiverPort, allPortsHaveData);
    CHECK_PORT_DATA(
        vehTransContainer, f_framework->m_VehTransparenceStsFromSM_Receiver, allPortsHaveData);
    CHECK_PORT_DATA(doorContainer, f_framework->m_doorStateReceiver, allPortsHaveData);
    CHECK_PORT_DATA(mirrorContainer, f_framework->m_mirrorStateReceiver, allPortsHaveData);
    if (!allPortsHaveData)
    {
        // XLOG_WARN(g_AppContext, "Vehicle2DOverlayManager::update() some ports don't have data!");
        return;
    }
    bool vehicleLightsPortHaveData = true;
    CHECK_PORT_DATA(vehicleLightsContainer, f_framework->m_VehicleLightsReceiver, vehicleLightsPortHaveData);

    vfc::uint32_t l_curViewId      = std::numeric_limits<vfc::uint32_t>::max();
    auto          l_colorIndex     = colorContainer->m_Data;
    const auto          l_vehTransStatus = vehTransContainer->m_Data;
    const auto l_frontRightDoorOpen = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT];
    const auto l_frontLeftDoorOpen  = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_LEFT];
//    const auto l_hoodOpen           = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_HOOD];
    const auto l_trunkOpen          = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_TRUNK];
    const auto l_rearRightDoorOpen  = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_REAR_RIGHT];
    const auto l_rearLeftDoorOpen   = pc::daddy::CARDOORSTATE_CLOSED != doorContainer->m_Data[pc::daddy::CARDOOR_REAR_LEFT];
//    const auto l_allDoorsAndMirrorsClosed =
        // (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) &&
        // (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) &&
        // (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_TRUNK]) &&
        // (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_HOOD]) &&
        // (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) &&
        // (pc::daddy::CARDOORSTATE_CLOSED == doorContainer->m_Data[pc::daddy::CARDOOR_REAR_LEFT]) &&
        // (pc::daddy::MIRRORSTATE_NOT_FLAPPED ==
        //  mirrorContainer->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_LEFT)]) &&
        // (pc::daddy::MIRRORSTATE_NOT_FLAPPED ==
        //  mirrorContainer->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_RIGHT)]);
    bool l_leftIndicatorOpen  = false;
    bool l_rightIndicatorOpen = false;
    if (vehicleLightsPortHaveData)
    {
        l_leftIndicatorOpen  = (vehicleLightsContainer->m_Data.m_leftIndicatorBlinkState == 1u);
        l_rightIndicatorOpen = (vehicleLightsContainer->m_Data.m_rightIndicatorBlinkState == 2u);
    }
    EScreenID       l_curviewid    = EScreenID_NO_CHANGE;

    if (l_vehTransStatus!= 0)
    {
        l_colorIndex = cc::daddy::NISSAN_TRANSPARENT;
    }

    // ! check if config has changed
    if ((g_Vehicle2DSettings->getModifiedCount() != m_lastConfigUpdate))
    {
        init(f_imageOverlays, f_framework, l_colorIndex);
        m_lastConfigUpdate = g_Vehicle2DSettings->getModifiedCount();
    }
    else
    {
        if (l_colorIndex != m_colorIndex)
        {
            init(f_imageOverlays, f_framework, l_colorIndex);
        }

        const bool l_isNewRefView = updateReferenceView(f_imageOverlays, f_framework, l_curViewId);

        if (l_isNewRefView || (l_colorIndex != m_colorIndex))
        {
            updateIconsSizePosition(l_curViewId, f_imageOverlays, f_framework);
        }
    }
    bool l_sonarShow=true;
    auto const l_scene = f_framework->getScene();
    if (l_scene->getViewId(static_cast<pc::core::View*>(f_imageOverlays->getReferenceView()))==cc::core::CustomViews::MINI_USS_VIEW&&
        f_framework->m_SonarAPPData_ReceiverPort.hasData())
    {
        auto& l_SonarAPPData = f_framework->m_SonarAPPData_ReceiverPort.getData()->m_Data;
        const bool  l_frontSonarError =
            (l_SonarAPPData.m_sonarStatusDisplayRequest ==
                cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_FRONT_ERROR) ||
            (l_SonarAPPData.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_ERROR);
        const bool l_rearSonarError =
            (l_SonarAPPData.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_ERROR);
            if (l_SonarAPPData.m_sonarDetectionDisplayReq || l_frontSonarError || l_rearSonarError)
            {
                l_sonarShow=true;
            }
            else
            {
                l_sonarShow=false;
            }
    }
    const auto currentView = f_framework->getScene()->getView(l_curViewId);
    if (currentView != nullptr && (currentView->getNodeMask() == 0u)||!l_sonarShow)
    {
        l_Vehicle2DOverlays->setAllEnabled(false);
    }
    else
    {
        // if (!l_showTransparent /*|| !l_allDoorsAndMirrorsClosed*/)
        // {
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN)->setEnabled(l_frontLeftDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_FRONT_LEFT_DOOR_CLOSE)->setEnabled(!l_frontLeftDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN)->setEnabled(l_frontRightDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE)->setEnabled(!l_frontRightDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_REAR_LEFT_DOOR_OPEN)->setEnabled(l_rearLeftDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_REAR_LEFT_DOOR_CLOSE)->setEnabled(!l_rearLeftDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_REAR_RIGHT_DOOR_OPEN)->setEnabled(l_rearRightDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_REAR_RIGHT_DOOR_CLOSE)->setEnabled(!l_rearRightDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_TRUNK_OPEN)->setEnabled(l_trunkOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_TRUNK_CLOSE)->setEnabled(!l_trunkOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_BODY_OPEN)->setEnabled(true); //! VEHICLE BODY WITHOUT DOORS
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_LEFT_FRONT_INDICATOR_ON)->setEnabled(l_leftIndicatorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_LEFT_REAR_INDICATOR_ON)->setEnabled(l_leftIndicatorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_LEFT_DOOR_OPEN_INDICATOR_ON)
            ->setEnabled(l_leftIndicatorOpen && l_frontLeftDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_LEFT_DOOR_CLOSE_INDICATOR_ON)
            ->setEnabled(l_leftIndicatorOpen && !l_frontLeftDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_RIGHT_FRONT_INDICATOR_ON)->setEnabled(l_rightIndicatorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_RIGHT_REAR_INDICATOR_ON)->setEnabled(l_rightIndicatorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_RIGHT_DOOR_OPEN_INDICATOR_ON)
            ->setEnabled(l_rightIndicatorOpen && l_frontRightDoorOpen);
        l_Vehicle2DOverlays->getIcon(VEHICLE2D_RIGHT_DOOR_CLOSE_INDICATOR_ON)
            ->setEnabled(l_rightIndicatorOpen && !l_frontRightDoorOpen);
        // }
        // if (l_showTransparent /* && l_allDoorsAndMirrorsClosed */)
        // {
        //   m_Vehicle2DOverlays.setAllEnabled(false);
        //   l_Vehicle2DOverlays->getIcon(VEHICLE2D_TRANSPARENT)->setEnabled(true);
        // }
    }
    //   l_Vehicle2DOverlays->getIcon(VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK)->setEnabled(l_frontLeftDoorOpen);
    //   l_Vehicle2DOverlays->getIcon(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK)->setEnabled(l_frontRightDoorOpen);

    m_colorIndex = l_colorIndex;
    m_colorIndex = l_colorIndex;
    m_viewId     = l_curViewId;
}

void Vehicle2DOverlayManager::setVehicleIconPosition(core::CustomFramework* f_framework) // PRQA S 4211
{
    if (f_framework == nullptr)
    {
        return;
    }
    if (f_framework->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayid = f_framework->m_displayedView_ReceiverPort.getData();
        if (nullptr != l_displayid)
        {
            IconList l_iconlist = m_Vehicle2DOverlays.getIconList();
            if ((EScreenID_HORI_PARKING == l_displayid->m_Data))
            {
                for (IconList::const_iterator l_itr = l_iconlist.begin(); l_itr != l_iconlist.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
                {
                    pc::assets::Icon* const l_icon = l_itr->get();
                    l_icon->setPosition(g_Vehicle2DSettings->m_parkingPlanViewPos, pc::assets::Icon::UnitType::Pixel);
                }
            }
            else
            {
                for (IconList::const_iterator l_itr = l_iconlist.begin(); l_itr != l_iconlist.end(); ++l_itr) // PRQA S 4297 // PRQA S 4687
                {
                    pc::assets::Icon* const l_icon = l_itr->get();
                    l_icon->setPosition(g_Vehicle2DSettings->m_planViewPos, pc::assets::Icon::UnitType::Pixel);
                }
            }
        }
    }
}

bool Vehicle2DOverlayManager::updateReferenceView( // PRQA S 4678
    pc::assets::ImageOverlays* f_imageOverlays,
    core::CustomFramework*     f_framework,
    vfc::uint32_t&             f_viewId) // PRQA S 4287
{
//     using namespace cc::core;
    if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
    {
        return false;
    }
    auto const l_scene = f_framework->getScene();
    assert(l_scene);

    pc::core::View* l_newRefView   = nullptr;
//    constexpr vfc::uint32_t   l_newRefViewID = std::numeric_limits<vfc::uint32_t>::max(); // PRQA S 3803
    EScreenID       l_curviewid    = EScreenID_NO_CHANGE;

    //! update current displayed viewid
    if (f_framework->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayid = f_framework->m_displayedView_ReceiverPort.getData();
        l_curviewid                                           = l_displayid->m_Data;
    }

    const bool isFloatView =
        (l_curviewid == EScreenID_FLOAT_SINGLE_FRONT || l_curviewid == EScreenID_FLOAT_SINGLE_REAR ||
         l_curviewid == EScreenID_FLOAT_WHEEL_FRONT_DUAL || l_curviewid == EScreenID_FLOAT_WHEEL_REAR_DUAL ||
         l_curviewid == EScreenID_FLOAT_FRONT_PLAN_VIEW || l_curviewid == EScreenID_FLOAT_REAR_PLAN_VIEW ||
         l_curviewid == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW || l_curviewid == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW);

    const bool isJunctionView =
        (l_curviewid == EScreenID_SINGLE_REAR_JUNCTION || l_curviewid == EScreenID_SINGLE_FRONT_JUNCTION);

    l_newRefView = (isFloatView) ? l_scene->getView(cc::core::CustomViews::FLOAT_PLAN_VIEW_VEHICLE2D)
                                 : l_scene->getView(cc::core::CustomViews::PLAN_VIEW_VEHICLE2D);

    l_newRefView = (isJunctionView)?l_scene->getView(cc::core::CustomViews::MINI_USS_VIEW):l_newRefView;

    f_viewId = l_scene->getViewId(l_newRefView);
    if (l_newRefView->getNodeMask() != 0u &&
        (l_scene->getViewId(static_cast<pc::core::View*>(f_imageOverlays->getReferenceView())) != f_viewId)) // PRQA S 3076
    {
        f_imageOverlays->setReferenceView(l_newRefView);
        return true;
    }

    return false;
}

void Vehicle2DOverlayManager::updateIconsSizePosition(
    const vfc::uint32_t        f_viewId,
    pc::assets::ImageOverlays* f_imageOverlays,
    core::CustomFramework*     f_framework) // PRQA S 2755
{
//     using namespace cc::daddy;
//     using namespace cc::core;

    if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
    {
        return;
    }
    const auto           refView  = f_imageOverlays->getReferenceView();
    const auto           viewport = refView->getViewport();
    osg::Vec2f     l_position;
    const osg::Vec2f     l_maskPositionLeft;
    const osg::Vec2f     l_maskPositionRight;
    vfc::float64_t l_iconScaler = 0.0;
    osg::Vec2f     l_maskIconSize;

    l_maskIconSize =
        osg::Vec2f{static_cast<vfc::float32_t>(viewport->width()), static_cast<vfc::float32_t>(viewport->height())};

    l_iconScaler = calculateIconScaler(f_framework->getScene()->getView(f_viewId));
    l_position   = osg::Vec2f{
        static_cast<vfc::float32_t>(viewport->width() * 0.5f + viewport->width()* g_Vehicle2DSettings->m_planViewBodyPosOffset.x()), static_cast<vfc::float32_t>(viewport->height() * 0.5f + viewport->height()* g_Vehicle2DSettings->m_planViewBodyPosOffset.y())};

    updateIconSizePosition(VEHICLE2D_BODY_OPEN, l_iconScaler, l_position, osg::Vec2f{0.0f, 0.0f});
    updateIconSizePosition(
        VEHICLE2D_TRUNK_OPEN, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewTrunkOpenPosOffset);
    updateIconSizePosition(
        VEHICLE2D_FRONT_LEFT_DOOR_OPEN, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewFLDoorOpenPosOffset);
    updateIconSizePosition(
        VEHICLE2D_FRONT_LEFT_DOOR_CLOSE, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewFLDoorClosePosOffset);
    updateIconSizePosition(
        VEHICLE2D_FRONT_RIGHT_DOOR_OPEN, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewFRDoorOpenPosOffset);
    updateIconSizePosition(
        VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_planViewFRDoorClosePosOffset);
    updateIconSizePosition(
        VEHICLE2D_REAR_LEFT_DOOR_OPEN, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewRLDoorOpenPosOffset);
    updateIconSizePosition(
        VEHICLE2D_REAR_LEFT_DOOR_CLOSE, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewRLDoorClosePosOffset);
    updateIconSizePosition(
        VEHICLE2D_REAR_RIGHT_DOOR_OPEN, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewRRDoorOpenPosOffset);
    updateIconSizePosition(
        VEHICLE2D_REAR_RIGHT_DOOR_CLOSE, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewRRDoorClosePosOffset);
    updateIconSizePosition(
        VEHICLE2D_TRUNK_CLOSE, l_iconScaler, l_position, g_Vehicle2DSettings->m_planViewTrunkClosePosOffset);
    // !Indicator
    updateIconSizePosition(
        VEHICLE2D_LEFT_FRONT_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_leftIndicator.m_frontIndicatorOnPosOffset);
    updateIconSizePosition(
        VEHICLE2D_LEFT_REAR_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_leftIndicator.m_rearIndicatorOnPosOffset);
    updateIconSizePosition(
        VEHICLE2D_LEFT_DOOR_OPEN_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_leftIndicator.m_doorOpenIndicatorPosOffset);
    updateIconSizePosition(
        VEHICLE2D_LEFT_DOOR_CLOSE_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_leftIndicator.m_doorCloseIndicatorPosOffset);
    updateIconSizePosition(
        VEHICLE2D_RIGHT_FRONT_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_rightIndicator.m_frontIndicatorOnPosOffset);
    updateIconSizePosition(
        VEHICLE2D_RIGHT_REAR_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_rightIndicator.m_rearIndicatorOnPosOffset);
    updateIconSizePosition(
        VEHICLE2D_RIGHT_DOOR_OPEN_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_rightIndicator.m_doorOpenIndicatorPosOffset);
    updateIconSizePosition(
        VEHICLE2D_RIGHT_DOOR_CLOSE_INDICATOR_ON,
        l_iconScaler,
        l_position,
        g_Vehicle2DSettings->m_rightIndicator.m_doorCloseIndicatorPosOffset);
    //   updateIconSizePosition(VEHICLE2D_TRANSPARENT, l_iconSize, l_position,
    //   g_Vehicle2DSettings->m_planViewRRPosOffset, g_Vehicle2DSettings->m_planViewDoorScale);
    //   updateIconSizePosition(VEHICLE2D_FRONT_LEFT_DOOR_OPEN_MASK,  l_maskIconSize, l_maskPositionLeft,
    //   osg::Vec2f{0.0f, 0.0f}, osg::Vec2f{1.0f, 1.0f}); updateIconSizePosition(VEHICLE2D_FRONT_RIGHT_DOOR_OPEN_MASK,
    //   l_maskIconSize, l_maskPositionRight, osg::Vec2f{0.0f, 0.0f}, osg::Vec2f{1.0f, 1.0f});
}

void Vehicle2DOverlayManager::updateIconSizePosition(
    const cc::assets::uielements::Vehicle2DOverlayType f_index,
    const vfc::float64_t                              f_iconScaler,
    const osg::Vec2f&                                  f_iconCenter,
    const osg::Vec2f&                                  f_iconPosOffset)
{
    const auto l_icon = static_cast<cc::assets::uielements::CustomIcon*>(m_Vehicle2DOverlays.getIcon(f_index)); // PRQA S 3076
    if (l_icon == nullptr)
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DOverlay: Cannot get vehicle 2dIcon: " << f_index);
        return;
    }
    osg::Vec2f l_iconOriginSize = l_icon->getIconSize();
    const osg::Vec2f l_iconSize       = osg::Vec2f{l_iconOriginSize.x() * static_cast<vfc::float32_t>(f_iconScaler), l_iconOriginSize.y() * static_cast<vfc::float32_t>(f_iconScaler)};

    const osg::Vec2f l_iconOffset = osg::Vec2f{
        g_Vehicle2DSettings->m_proportion.x() * static_cast<vfc::float32_t>(f_iconScaler) * f_iconPosOffset.x(),
        g_Vehicle2DSettings->m_proportion.x() * static_cast<vfc::float32_t>(f_iconScaler) * f_iconPosOffset.y()};
    const osg::Vec2f l_iconPos = f_iconCenter + l_iconOffset;

    l_icon->setPosition(l_iconPos, pc::assets::Icon::UnitType::Pixel);
    l_icon->setSize(l_iconSize, pc::assets::Icon::UnitType::Pixel);
}

//!
//! @brief Construct a new Vehicle2DOverlay:: Vehicle2DOverlay object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
Vehicle2DOverlay::Vehicle2DOverlay(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
    : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
    , m_customFramework{f_customFramework}
    , m_manager{}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

    //! render order
    //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
    constexpr vfc::uint32_t l_renderOrder = 10u;
    cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder);
}

Vehicle2DOverlay::~Vehicle2DOverlay() = default;

void Vehicle2DOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        m_manager.update(this, m_customFramework);
    }
    pc::assets::ImageOverlays::traverse(f_nv);
}

} // namespace uielements
} // namespace assets
} // namespace cc
