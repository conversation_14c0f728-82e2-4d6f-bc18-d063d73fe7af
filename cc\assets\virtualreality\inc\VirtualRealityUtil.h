//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityUtil.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYUTIL_H
#define CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYUTIL_H

#include "cc/assets/virtualreality/inc/VirtualRealityManager.h"
#include "cc/assets/parkingspots/inc/ParkingSpotUtil.h"


namespace cc
{
namespace assets
{
namespace virtualreality
{

void setColor(osg::Node* pNode,osg::Vec4 vecColor);

vfc::uint8_t isPointOnLineWhere(osg::Vec2f& f_linePoint1, osg::Vec2f& f_linePoint2, osg::Vec2f& f_point);
bool isPointInQuadrilateral(vfc::TFixedVector<osg::Vec2f, 4>& f_points2D, osg::Vec2f& f_pointClick);
vfc::TFixedVector<osg::Vec4f, 4> getParkingSpotFourCorners(const cc::target::common::ParkSlot_st& f_parkSlot);
bool parkingSpotIsSelected(const cc::target::common::ParkSlot_st& f_parkSlot, const osg::Matrixf& f_MVPmatrix, vfc::uint16_t f_click_x, vfc::uint16_t f_click_y);
bool isShow(const cc::core::CustomFramework* f_framework);

} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYUTIL_H
