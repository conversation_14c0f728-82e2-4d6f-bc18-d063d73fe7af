//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ParkingSpotUtil.h
/// @brief This file declared useful functions for ParkingSpotManager.cpp
//=============================================================================
#ifndef CC_ASSETS_PARKINGSPOTS_PARKINGSPOTUTIL_H
#define CC_ASSETS_PARKINGSPOTS_PARKINGSPOTUTIL_H

#include "pc/svs/util/math/inc/Box2D.h"

#include "cc/assets/parkingspots/inc/ParkingSpotManager.h"

namespace cc
{
namespace assets
{
namespace parkingspots
{

inline float cm2m(float value_cm)  { return (value_cm * 0.01f); }

unsigned int getParkingSpotCoverage(const ParkingSpot* f_parkingSpot, const osg::Matrixf& f_MVPmatrix);

void updateParkingSpot( ParkingSpot* f_parkingSpot, cc::target::common::StrippedEAPAParkSpace f_data, const osg::Vec2f& f_spotSize, vfc::uint8_t f_side);

void avoidOverlappedParkingSpots(ParkingSpot* f_parkingSpotCurr, const ParkingSpot* f_parkingSpotPrev);

unsigned int getParkingSpotCoverage(ParkingSpot* f_parkingSpot, const osg::Matrixf& f_MVPmatrix);

UISpotData getParkingSpotVertexandSize(const ParkingSpot* f_parkingSpot, const osg::Matrixf& f_MVPmatrix, vfc::uint8_t f_index);

osg::Vec4f adaptOsgCoordToViewCoord(osg::Vec4f f_point, const osg::Matrixf& f_MVPmatrix);

// manact::EParkSlotSide getParkingSpotSide(const cc::daddy::ParkingSpot& f_data);

} // namespace parkingspots
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKINGSPOTS_PARKINGSPOTUTIL_H