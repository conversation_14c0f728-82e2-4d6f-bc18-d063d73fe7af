//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/distanceoverlay/inc/DistanceOverlay.h"
#include "cc/assets/uielements/inc/CustomIcon.h"
#include "cc/assets/uielements/inc/RotateIcon.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "vfc/core/vfc_types.hpp"
#include <chrono>
using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace distanceoverlay
{

class IconSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(IconSettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
            return;
        }
        ADD_STRING_MEMBER(filepathGreen);
        ADD_STRING_MEMBER(filepathGreenFar);
        ADD_STRING_MEMBER(filepathYellow);
        ADD_STRING_MEMBER(filepathRed);
        ADD_MEMBER(osg::Vec2f, position);
    }

    std::string m_filepathGreen;
    std::string m_filepathGreenFar;
    std::string m_filepathYellow;
    std::string m_filepathRed;
    osg::Vec2f  m_position;
};

class DigitalIconsSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(DigitalIconsSettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
            return;
        }
        ADD_STRING_MEMBER(texturePath_stop);
        ADD_STRING_MEMBER(texturePath_30cm);
        ADD_STRING_MEMBER(texturePath_40cm);
        ADD_STRING_MEMBER(texturePath_50cm);
        ADD_STRING_MEMBER(texturePath_60cm);
        ADD_STRING_MEMBER(texturePath_70cm);
        ADD_STRING_MEMBER(texturePath_80cm);
        ADD_STRING_MEMBER(texturePath_90cm);
        ADD_STRING_MEMBER(texturePath_100cm);
        ADD_STRING_MEMBER(texturePath_110cm);
        ADD_STRING_MEMBER(texturePath_120cm);
        ADD_STRING_MEMBER(texturePath_130cm);
        ADD_STRING_MEMBER(texturePath_140cm);
        ADD_STRING_MEMBER(texturePath_150cm);
        ADD_STRING_MEMBER(texturePath_160cm);
        ADD_STRING_MEMBER(texturePath_170cm);
        ADD_STRING_MEMBER(texturePath_180cm);
        ADD_STRING_MEMBER(texturePath_190cm);
        ADD_STRING_MEMBER(texturePath_200cm);
    }

    std::string m_texturePath_stop;
    std::string m_texturePath_30cm;
    std::string m_texturePath_40cm;
    std::string m_texturePath_50cm;
    std::string m_texturePath_60cm;
    std::string m_texturePath_70cm;
    std::string m_texturePath_80cm;
    std::string m_texturePath_90cm;
    std::string m_texturePath_100cm;
    std::string m_texturePath_110cm;
    std::string m_texturePath_120cm;
    std::string m_texturePath_130cm;
    std::string m_texturePath_140cm;
    std::string m_texturePath_150cm;
    std::string m_texturePath_160cm;
    std::string m_texturePath_170cm;
    std::string m_texturePath_180cm;
    std::string m_texturePath_190cm;
    std::string m_texturePath_200cm;
};

class DegrationIconSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(DegrationIconSettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
            return;
        }
        ADD_MEMBER(std::string, texturePath);
        ADD_MEMBER(osg::Vec2f, posOffset);
        ADD_MEMBER(osg::Vec2f, sizeScaler);
    }

    std::string m_texturePath;
    osg::Vec2f  m_posOffset;
    osg::Vec2f  m_sizeScaler;
};
class DigitalPositionSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(DigitalPositionSettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
            return;
        }
        ADD_MEMBER(osg::Vec2f, posLevel1);
        ADD_MEMBER(osg::Vec2f, posLevel2);
        ADD_MEMBER(osg::Vec2f, posLevel3);
        ADD_MEMBER(osg::Vec2f, posLevel4);
    }

    osg::Vec2f m_posLevel1;
    osg::Vec2f m_posLevel2;
    osg::Vec2f m_posLevel3;
    osg::Vec2f m_posLevel4;
};

class DistanceOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(DistanceOverlaySettings) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
        return;
        }
        //! FRONT SECTORS
        ADD_MEMBER(IconSettings, frontMiddleDistanceIcon);
        ADD_MEMBER(IconSettings, frontLeftDistanceIcon);
        ADD_MEMBER(IconSettings, frontLeftSideDistanceIcon);
        ADD_MEMBER(IconSettings, frontRightDistanceIcon);
        ADD_MEMBER(IconSettings, frontRightSideDistanceIcon);
        //! REAR SECTORS
        ADD_MEMBER(IconSettings, rearMiddleDistanceIcon);
        ADD_MEMBER(IconSettings, rearLeftDistanceIcon);
        ADD_MEMBER(IconSettings, rearLeftSideDistanceIcon);
        ADD_MEMBER(IconSettings, rearRightDistanceIcon);
        ADD_MEMBER(IconSettings, rearRightSideDistanceIcon);
        //! DEGRATION
        ADD_MEMBER(DegrationIconSettings, frontSonarDegrationIcon);
        ADD_MEMBER(DegrationIconSettings, rearSonarDegrationIcon);
        ADD_MEMBER(DegrationIconSettings, degrationWarnIcon);
        ADD_MEMBER(DegrationIconSettings, degrationWarnWideViewIcon);

        //! COMMON
        ADD_MEMBER(std::string, vehiclePath);
        ADD_MEMBER(osg::Vec2f, originRefenceViewport);
        ADD_MEMBER(osg::Vec2f, originIconSize);
        ADD_MEMBER(osg::Vec2f, SonarOverlayCenterDiff);
        //! DISTANCE DISPLAY
        ADD_MEMBER(DigitalIconsSettings, digitalIcons);
        ADD_MEMBER(DigitalPositionSettings, rearRightSide);
        ADD_MEMBER(DigitalPositionSettings, rearRight);
        ADD_MEMBER(DigitalPositionSettings, rearMiddle);
        ADD_MEMBER(DigitalPositionSettings, rearLeft);
        ADD_MEMBER(DigitalPositionSettings, rearLeftSide);
        ADD_MEMBER(DigitalPositionSettings, frontLeftSide);
        ADD_MEMBER(DigitalPositionSettings, frontLeft);
        ADD_MEMBER(DigitalPositionSettings, frontMiddle);
        ADD_MEMBER(DigitalPositionSettings, frontRight);
        ADD_MEMBER(DigitalPositionSettings, frontRightSide);

        ADD_FLOAT_MEMBER(rearRightSideRotAngle);
        ADD_FLOAT_MEMBER(rearRightRotAngle);
        ADD_FLOAT_MEMBER(rearMiddleRotAngle);
        ADD_FLOAT_MEMBER(rearLeftRotAngle);
        ADD_FLOAT_MEMBER(rearLeftSideRotAngle);
        ADD_FLOAT_MEMBER(frontLeftSideRotAngle);
        ADD_FLOAT_MEMBER(frontLeftRotAngle);
        ADD_FLOAT_MEMBER(frontMiddleRotAngle);
        ADD_FLOAT_MEMBER(frontRightRotAngle);
        ADD_FLOAT_MEMBER(frontRightSideRotAngle);
    }

    //! FRONT SECTORS
    IconSettings m_frontMiddleDistanceIcon;
    IconSettings m_frontLeftDistanceIcon;
    IconSettings m_frontLeftSideDistanceIcon;
    IconSettings m_frontRightDistanceIcon;
    IconSettings m_frontRightSideDistanceIcon;

    //! REAR SECTOR
    IconSettings m_rearMiddleDistanceIcon;
    IconSettings m_rearLeftDistanceIcon;
    IconSettings m_rearLeftSideDistanceIcon;
    IconSettings m_rearRightDistanceIcon;
    IconSettings m_rearRightSideDistanceIcon;

    //! Degration
    DegrationIconSettings m_frontSonarDegrationIcon;
    DegrationIconSettings m_rearSonarDegrationIcon;
    DegrationIconSettings m_degrationWarnIcon;
    DegrationIconSettings m_degrationWarnWideViewIcon;

    //! COMMON
    std::string m_vehiclePath{"cc/resources/uss/vehicle2d.png"};
    osg::Vec2f  m_originRefenceViewport;
    osg::Vec2f  m_originIconSize;
    osg::Vec2f  m_SonarOverlayCenterDiff;
    //! DISTANCE DISPLAY
    DigitalIconsSettings    m_digitalIcons;
    DigitalPositionSettings m_rearRightSide;
    DigitalPositionSettings m_rearRight;
    DigitalPositionSettings m_rearMiddle;
    DigitalPositionSettings m_rearLeft;
    DigitalPositionSettings m_rearLeftSide;
    DigitalPositionSettings m_frontLeftSide;
    DigitalPositionSettings m_frontLeft;
    DigitalPositionSettings m_frontMiddle;
    DigitalPositionSettings m_frontRight;
    DigitalPositionSettings m_frontRightSide;

    vfc::float32_t m_rearRightSideRotAngle;
    vfc::float32_t m_rearRightRotAngle;
    vfc::float32_t m_rearMiddleRotAngle;
    vfc::float32_t m_rearLeftRotAngle;
    vfc::float32_t m_rearLeftSideRotAngle;
    vfc::float32_t m_frontLeftSideRotAngle;
    vfc::float32_t m_frontLeftRotAngle;
    vfc::float32_t m_frontMiddleRotAngle;
    vfc::float32_t m_frontRightRotAngle;
    vfc::float32_t m_frontRightSideRotAngle;
};

static pc::util::coding::Item<DistanceOverlaySettings> g_settings("DistanceOverlay");

// osg::Vec2f getDistancePositionByValue(DistanceOverlayAsset::DistanceSectorId f_distanceValue)
// {
//     osg::Vec2f position;
//     switch (f_distanceValue)
//     {
//     default:
//     case DistanceOverlayAsset::FRONT_LEFT_SIDE:
//         position = g_settings->m_frontLeftSide;
//         break;
//     case DistanceOverlayAsset::FRONT_LEFT:
//         position = g_settings->m_frontLeft;
//         break;
//     case DistanceOverlayAsset::FRONT_MIDDLE:
//         position = g_settings->m_frontMiddle;
//         break;
//     case DistanceOverlayAsset::FRONT_RIGHT:
//         position = g_settings->m_frontRight;
//         break;
//     case DistanceOverlayAsset::FRONT_RIGHT_SIDE:
//         position = g_settings->m_frontRightSide;
//         break;
//     case DistanceOverlayAsset::REAR_LEFT_SIDE:
//         position = g_settings->m_rearLeftSide;
//         break;
//     case DistanceOverlayAsset::REAR_LEFT:
//         position = g_settings->m_rearLeft;
//         break;
//     case DistanceOverlayAsset::REAR_MIDDLE:
//         position = g_settings->m_rearMiddle;
//         break;
//     case DistanceOverlayAsset::REAR_RIGHT:
//         position = g_settings->m_rearRight;
//         break;
//     case DistanceOverlayAsset::REAR_RIGHT_SIDE:
//         position = g_settings->m_rearRightSide;
//         break;
//     }
//     return position;
// }

static vfc::float32_t getDistanceRotationByValue(DistanceOverlayAsset::DistanceSectorId f_distanceValue)
{
    vfc::float32_t rotationAngle = 0.0f;
    switch (f_distanceValue)
    {
    case DistanceOverlayAsset::FRONT_LEFT_SIDE:
    {
        rotationAngle = g_settings->m_frontLeftSideRotAngle;
        break;
    }
    case DistanceOverlayAsset::FRONT_LEFT:
    {
        rotationAngle = g_settings->m_frontLeftRotAngle;
        break;
    }
    case DistanceOverlayAsset::FRONT_MIDDLE:
    {
        rotationAngle = g_settings->m_frontMiddleRotAngle;
        break;
    }
    case DistanceOverlayAsset::FRONT_RIGHT:
    {
        rotationAngle = g_settings->m_frontRightRotAngle;
        break;
    }
    case DistanceOverlayAsset::FRONT_RIGHT_SIDE:
    {
        rotationAngle = g_settings->m_frontRightSideRotAngle;
        break;
    }
    case DistanceOverlayAsset::REAR_LEFT_SIDE:
    {
        rotationAngle = g_settings->m_rearLeftSideRotAngle;
        break;
    }
    case DistanceOverlayAsset::REAR_LEFT:
    {
        rotationAngle = g_settings->m_rearLeftRotAngle;
        break;
    }
    case DistanceOverlayAsset::REAR_MIDDLE:
    {
        rotationAngle = g_settings->m_rearMiddleRotAngle;
        break;
    }
    case DistanceOverlayAsset::REAR_RIGHT:
    {
        rotationAngle = g_settings->m_rearRightRotAngle;
        break;
    }
    case DistanceOverlayAsset::REAR_RIGHT_SIDE:
    {
        rotationAngle = g_settings->m_rearRightSideRotAngle;
        break;
    }
    default:
    {
        rotationAngle = g_settings->m_frontLeftSideRotAngle;
        break;
    }
    }
    return rotationAngle;
}

static const std::string& getDistanceTextureByValue(DistanceOverlayAsset::DistanceDigitalValue f_distanceValue)
{
    const std::string* textureFile = nullptr;
    switch (f_distanceValue)
    {
    case DistanceOverlayAsset::DISTANCE_DIGITAL_STOP:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_stop;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_30:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_30cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_40:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_40cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_50:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_50cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_60:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_60cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_70:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_70cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_80:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_80cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_90:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_90cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_100:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_100cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_110:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_110cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_120:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_120cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_130:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_130cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_140:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_140cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_150:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_150cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_160:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_160cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_170:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_170cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_180:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_180cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_190:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_190cm;
        break;
    }
    case DistanceOverlayAsset::DISTANCE_DIGITAL_CM_200:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_200cm;
        break;
    }
    case DistanceOverlayAsset::NO_WARNING:
    default:
    {
        textureFile = &g_settings->m_digitalIcons.m_texturePath_stop;
        break;
    }
    }

    return *textureFile;
}

std::string cc::assets::distanceoverlay::DistanceOverlayAsset::getSectorNameFromIndex(DistanceSectorId f_id)
{
    switch (f_id)
    {
    case FRONT_MIDDLE:
    {
        return "FRONT_MIDDLE";
    }
    case FRONT_LEFT:
    {
        return "FRONT_LEFT";
    }
    case FRONT_LEFT_SIDE:
    {
        return "FRONT_LEFT_SIDE";
    }
    case FRONT_RIGHT:
    {
        return "FRONT_RIGHT";
    }
    case FRONT_RIGHT_SIDE:
    {
        return "FRONT_RIGHT_SIDE";
    }
    case REAR_MIDDLE:
    {
        return "REAR_MIDDLE";
    }
    case REAR_LEFT:
    {
        return "REAR_LEFT";
    }
    case REAR_LEFT_SIDE:
    {   
        return "REAR_LEFT_SIDE";
    }
    case REAR_RIGHT:
    {
        return "REAR_RIGHT";
    }
    case REAR_RIGHT_SIDE:
    {
        return "REAR_RIGHT_SIDE";
    }
    case NUM_SECTOR:
    {
        return "NUM_SECTOR";
    }
    default:
    {
        return "INVALID";
    }
    }
}

static void
adaptDistanceWarningLevel(cc::target::common::ESonarDistRange f_range, DistanceOverlayAsset::DistanceWarningLevel& f_warningLevel) // PRQA S 4287
{
    switch (f_range)
    {
    case cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE:
    {
        f_warningLevel = DistanceOverlayAsset::NO_WARNING;
        break;
    }
    case cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_LEVEL4:
    {
        f_warningLevel = DistanceOverlayAsset::FAR_GREEN;
        break;
    }
    case cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_LEVEL3:
    {
        f_warningLevel = DistanceOverlayAsset::GREEN;
        break;
    }
    case cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_LEVEL2:
    {
        f_warningLevel = DistanceOverlayAsset::YELLOW;
        break;
    }
    case cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_LEVEL1:
    {
        f_warningLevel = DistanceOverlayAsset::RED;
        break;
    }
    case cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NOT_USED:
    {
        f_warningLevel = DistanceOverlayAsset::NO_WARNING;
        break;
    }
    default:
    {
        f_warningLevel = DistanceOverlayAsset::NO_WARNING;
        break;
    }
    }
}

DistanceOverlayAsset::DistanceOverlayAsset(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : pc::assets::ImageOverlays{f_assetId, f_referenceView}
    , m_degrationOverlays{}
    , m_framework{f_framework}
{
    m_pSettings[FRONT_MIDDLE]     = &g_settings->m_frontMiddleDistanceIcon;
    m_pSettings[FRONT_LEFT]       = &g_settings->m_frontLeftDistanceIcon;
    m_pSettings[FRONT_LEFT_SIDE]  = &g_settings->m_frontLeftSideDistanceIcon;
    m_pSettings[FRONT_RIGHT]      = &g_settings->m_frontRightDistanceIcon;
    m_pSettings[FRONT_RIGHT_SIDE] = &g_settings->m_frontRightSideDistanceIcon;

    m_pSettings[REAR_MIDDLE]     = &g_settings->m_rearMiddleDistanceIcon;
    m_pSettings[REAR_LEFT]       = &g_settings->m_rearLeftDistanceIcon;
    m_pSettings[REAR_LEFT_SIDE]  = &g_settings->m_rearLeftSideDistanceIcon;
    m_pSettings[REAR_RIGHT]      = &g_settings->m_rearRightDistanceIcon;
    m_pSettings[REAR_RIGHT_SIDE] = &g_settings->m_rearRightSideDistanceIcon;

    m_warningLevels.fill(NO_WARNING);
}

void DistanceOverlayAsset::traverse(osg::NodeVisitor& nv)
{
    if (nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        if ((m_modifiedCount != g_settings->getModifiedCount()) || (!m_initialized))
        {
            if (m_enableMiniVehicle2d)
            {
                initMiniVehicle2d();
            }
            init();
            m_modifiedCount = g_settings->getModifiedCount();
            m_initialized   = true;
        }
        bool l_showReq = false;
        if (m_framework->asCustomFramework()->m_showReq_ReceiverPort.hasData())
        {
            l_showReq = m_framework->asCustomFramework()->m_showReq_ReceiverPort.getData()->m_Data;
        }

        if (m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.hasData())
        {
            auto& l_SonarAPPData = m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.getData()->m_Data;
            const bool  l_frontSonarError =
                (l_SonarAPPData.m_sonarStatusDisplayRequest ==
                 cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_FRONT_ERROR) ||
                (l_SonarAPPData.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_ERROR);
            const bool l_rearSonarError =
                (l_SonarAPPData.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_ERROR);
            if (m_enableMiniVehicle2d)
            {
                if (l_SonarAPPData.m_sonarDetectionDisplayReq || l_frontSonarError || l_rearSonarError)
                {
                    m_vehicle2d.getIcon(0u)->setEnabled(true);
                }
                else
                {
                    m_vehicle2d.getIcon(0u)->setEnabled(false);
                }
            }

            updateWarningLevels(l_SonarAPPData);
            updateDistanceDisplay(l_SonarAPPData);
            sonarDegrationHandling(l_frontSonarError, l_rearSonarError, l_showReq);
            updateDegrationWarning();
        }
    }

    ImageOverlays::traverse(nv);
}

void DistanceOverlayAsset::initMiniVehicle2d()
{
    m_vehicle2d.clear(this);
    pc::assets::Icon* const l_vehicle2dIcon = new pc::assets::Icon{g_settings->m_vehiclePath};
    l_vehicle2dIcon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    l_vehicle2dIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_vehicle2dIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_vehicle2dIcon->setEnabled(false);
    const auto viewport = m_referenceView->getViewport();
    if (viewport != nullptr)
    {
        const osg::Vec2f iconPos = osg::Vec2f{
            static_cast<vfc::float32_t>(
                viewport->width() * 0.5f +
                viewport->width() * cc::assets::uielements::g_Vehicle2DSettings->m_planViewBodyPosOffset.x()),
            static_cast<vfc::float32_t>(
                viewport->height() * 0.5f +
                viewport->height() * cc::assets::uielements::g_Vehicle2DSettings->m_planViewBodyPosOffset.y())};
        l_vehicle2dIcon->setPosition(iconPos, pc::assets::Icon::UnitType::Pixel);
    }
    // setIconSize(l_vehicle2dIcon);
    m_vehicle2d.addIcon(this, l_vehicle2dIcon);
}

void DistanceOverlayAsset::init()
{
    // Sectors uss
    for (size_t i = 0u; i < NUM_SECTOR; i++)
    {
        auto& sectorIcons = m_sectors[i];
        sectorIcons.clear(this);
        for (size_t j = 0u; j < NUM_WARNING; j++)
        {
            sectorIcons.addIcon(
                this,
                createDistanceLevelIcon(
                    m_pSettings[i],
                    static_cast<DistanceWarningLevel>(j),
                    isMiddleSector(static_cast<DistanceSectorId>(i))));
        }
    }

    // Degration
    vfc::float32_t l_referenceWidth = 0.0f;
    vfc::float32_t l_referenceHight = 0.0f;
    if (m_referenceView != nullptr)
    {
        const auto viewport = m_referenceView->getViewport();
        if (viewport != nullptr)
        {
            l_referenceWidth = static_cast<vfc::float32_t>(viewport->width());
            l_referenceHight = static_cast<vfc::float32_t>(viewport->height());
        }
    }
    m_degrationOverlays.clear(this);
    m_degrationOverlays.addIcon(
        this,
        createIconTopLeft(
            g_settings->m_frontSonarDegrationIcon.m_texturePath,
            osg::Vec2f(
                g_settings->m_frontSonarDegrationIcon.m_posOffset.x() * l_referenceWidth + l_referenceWidth / 2.0f,
                g_settings->m_frontSonarDegrationIcon.m_posOffset.y() * l_referenceHight + l_referenceHight / 2.0f),
            osg::Vec2f(
                g_settings->m_frontSonarDegrationIcon.m_sizeScaler.x() * l_referenceWidth,
                g_settings->m_frontSonarDegrationIcon.m_sizeScaler.y() * l_referenceHight)));
    m_degrationOverlays.addIcon(
        this,
        createIconTopLeft(
            g_settings->m_rearSonarDegrationIcon.m_texturePath,
            osg::Vec2f(
                g_settings->m_rearSonarDegrationIcon.m_posOffset.x() * l_referenceWidth + l_referenceWidth / 2.0f,
                g_settings->m_rearSonarDegrationIcon.m_posOffset.y() * l_referenceHight + l_referenceHight / 2.0f),
            osg::Vec2f(
                g_settings->m_rearSonarDegrationIcon.m_sizeScaler.x() * l_referenceWidth,
                g_settings->m_rearSonarDegrationIcon.m_sizeScaler.y() * l_referenceHight)));

    if (m_referenceView != nullptr && m_referenceView->getName() == "Mini Uss View")
    {
        m_degrationOverlays.addIcon(
            this,
            createIconTopLeft(
                g_settings->m_degrationWarnWideViewIcon.m_texturePath,
                osg::Vec2f(
                    g_settings->m_degrationWarnWideViewIcon.m_posOffset.x() * l_referenceWidth,
                    g_settings->m_degrationWarnWideViewIcon.m_posOffset.y() * l_referenceHight),
                osg::Vec2f(
                    g_settings->m_degrationWarnWideViewIcon.m_sizeScaler.x() * l_referenceWidth,
                    g_settings->m_degrationWarnWideViewIcon.m_sizeScaler.y() * l_referenceHight)));
    }
    else
    {
        m_degrationOverlays.addIcon(
            this,
            createIconTopLeft(
                g_settings->m_degrationWarnIcon.m_texturePath,
                osg::Vec2f(
                    g_settings->m_degrationWarnIcon.m_posOffset.x() * l_referenceWidth,
                    g_settings->m_degrationWarnIcon.m_posOffset.y() * l_referenceHight),
                osg::Vec2f(
                    g_settings->m_degrationWarnIcon.m_sizeScaler.x() * l_referenceWidth,
                    g_settings->m_degrationWarnIcon.m_sizeScaler.y() * l_referenceHight)));
    }

    // Distance display
    m_distanceDisplay.clear(this);
    for (size_t i = 0u; i < NUM_SECTOR; i++)
    {
        m_distanceDisplay.addIcon(
            this, createDistanceDigitalIcon(&g_settings->m_digitalIcons, static_cast<DistanceSectorId>(i)));
    }
}

void DistanceOverlayAsset::setIconSize(pc::assets::Icon* f_icon) // PRQA S 4211
{
    if (f_icon == nullptr)
    {
        return;
    }
    // Set Icon Size based on viewport
    if (m_referenceView != nullptr)
    {
        const auto viewport = m_referenceView->getViewport();
        if (viewport == nullptr)
        {
            XLOG_ERROR(g_AppContext, "DistanceOverlayAsset: viewport is null");
            return;
        }

        const osg::Texture2D* const l_texture = f_icon->getTexture();
        // assert(l_texture);
        if (l_texture == nullptr)
        {
            XLOG_WARN(g_AppContext, "DistanceOverlayAsset: texture not found! - setIconSize");
            return;
        }
        osg::Vec2i l_textureSize(l_texture->getTextureWidth(), l_texture->getTextureHeight());
        if ((0 == l_textureSize.x()) || (0 == l_textureSize.y()))
        {
            const osg::Image* const l_image = l_texture->getImage();
            if (l_image != nullptr)
            {
                l_textureSize.x() = l_image->s();
                l_textureSize.y() = l_image->t();
            }
        }
        const vfc::float32_t scaleFactor =
            static_cast<vfc::float32_t>(viewport->width()) / g_settings->m_originRefenceViewport.x() * 100.0f;
        f_icon->setSize({scaleFactor, scaleFactor}, pc::assets::Icon::UnitType::Percentage);
    }
    else
    {
        f_icon->setSize({100.0f, 100.0f}, pc::assets::Icon::UnitType::Percentage);
    }
}

pc::assets::Icon* DistanceOverlayAsset::createDistanceLevelIcon(
    const IconSettings*  f_settings,
    DistanceWarningLevel f_level,
    bool                 f_isMiddleSector)
{
    if (f_settings == nullptr)
    {
        return nullptr;
    }
    const std::string* textureFile = nullptr;
    switch (f_level)
    {
    case FAR_GREEN:
    {
        textureFile = (f_isMiddleSector) ? &f_settings->m_filepathGreenFar : &f_settings->m_filepathGreen;
        break;
    }
    case GREEN:
    {
        textureFile = &f_settings->m_filepathGreen;
        break;
    }
    case YELLOW:
    {
        textureFile = &f_settings->m_filepathYellow;
        break;
    }
    case RED:
    {
        textureFile = &f_settings->m_filepathRed;
        break;
    }
    case NO_WARNING:
    default:
    {
        textureFile = &f_settings->m_filepathGreen;
        break;
    }
    }
    pc::assets::Icon* const l_icon = new pc::assets::Icon{*textureFile};
    if (l_icon == nullptr)
    {
        XLOG_ERROR(g_AppContext, "DistanceOverlayAsset: icon is null - createDistanceLevelIcon");
        return l_icon;
    }

    l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    const auto l_viewport = m_referenceView->getViewport();
    if (l_viewport == nullptr)
    {
        XLOG_ERROR(g_AppContext, "DistanceOverlayAsset: viewport is null");
        return l_icon;
    }
    const osg::Vec2f iconPos = {
        static_cast<vfc::float32_t>(l_viewport->width()) / 2.0f +
            static_cast<vfc::float32_t>(l_viewport->width()) * g_settings->m_SonarOverlayCenterDiff.x(),
        static_cast<vfc::float32_t>(l_viewport->height()) / 2.0f +
            static_cast<vfc::float32_t>(l_viewport->height()) * g_settings->m_SonarOverlayCenterDiff.y()};

    l_icon->setPosition(iconPos, pc::assets::Icon::UnitType::Pixel);
    l_icon->setEnabled(false);
    const auto l_texture = const_cast<osg::Texture2D*>(l_icon->getTexture()); // PRQA S 3066
    if (l_texture == nullptr)
    {
        XLOG_WARN(g_AppContext, "DistanceOverlayAsset: texture not found! - createDistanceLevelIcon");
        return l_icon;
    }
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    setIconSize(l_icon);
    return l_icon;
}

pc::assets::Icon*
DistanceOverlayAsset::createDistanceDigitalIcon(const DigitalIconsSettings* /*f_settings*/, DistanceSectorId f_value)
{
    const std::string l_initdigitalIcon = g_settings->m_digitalIcons.m_texturePath_stop;
    if (l_initdigitalIcon.empty())
    {
        XLOG_ERROR(g_AppContext, "DistanceDigitalIcon: icon is null - createDistanceDigitalIcon");
        return new pc::assets::Icon(false);
    }
    cc::assets::uielements::RotateIcon* const l_icon =
        new cc::assets::uielements::RotateIcon(g_settings->m_digitalIcons.m_texturePath_stop);
    l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_icon->setEnabled(false);
    // l_icon->setPosition(getDistancePositionByValue(f_value), pc::assets::Icon::UnitType::Percentage);
    l_icon->setRotateAngle(getDistanceRotationByValue(f_value));
    const auto l_texture = const_cast<osg::Texture2D*>(l_icon->getTexture()); // PRQA S 3066
    if (l_texture == nullptr)
    {
        XLOG_WARN(g_AppContext, "DistanceOverlayAsset: texture not found! - createDistanceDigitalIcon");
        return l_icon;
    }
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    setIconSize(l_icon);
    return l_icon;
}

pc::assets::Icon* DistanceOverlayAsset::createIconTopLeft(
    const std::string& f_iconPath,
    const osg::Vec2f&  f_iconPos,
    const osg::Vec2f&  f_iconSize)
{
    // pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
    cc::assets::uielements::CustomIcon* const l_icon =
        new cc::assets::uielements::CustomIcon(f_iconPath, false, false, true, true);
    l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
    l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
    l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_icon->setEnabled(false);
    return l_icon;
}

void cc::assets::distanceoverlay::DistanceOverlayAsset::updateWarningLevels(const cc::target::common::SonarAPPData_st& f_SonarAPPData)
{
    auto& rearDistances       = f_SonarAPPData.m_sonarDistRear;
    auto& rearDistancesAvail  = f_SonarAPPData.m_sonarDistRearAvail;
    auto& frontDistances      = f_SonarAPPData.m_sonarDistFront;
    auto& frontDistancesAvail = f_SonarAPPData.m_sonarDistFrontAvail;
    //! FRONT SECTOR
    for (size_t i = 0u; i < static_cast<vfc::uint8_t>(NUM_SECTOR) / 2u; i++)
    {
        const size_t frontIndex = i;
        if (frontDistancesAvail[frontIndex])
        {
            adaptDistanceWarningLevel(frontDistances[i], m_warningLevels[frontIndex]);
        }
        else
        {
            m_warningLevels[i] = NO_WARNING;
        }
    }
    //! REAR SECTOR
    for (size_t i = 0u; i < static_cast<vfc::uint8_t>(NUM_SECTOR) / 2u; i++)
    {
        const size_t rearIndex = i + ( static_cast<vfc::uint8_t>(NUM_SECTOR) / 2u);
        if (rearDistancesAvail[i])
        {
            adaptDistanceWarningLevel(rearDistances[i], m_warningLevels[rearIndex]);
        }
        else
        {
            m_warningLevels[rearIndex] = NO_WARNING;
        }
    }
    for (size_t i = 0u; i < NUM_SECTOR; i++)
    {
        updateWarningLevel(static_cast<DistanceSectorId>(i), m_warningLevels[i]);
    }
}

void cc::assets::distanceoverlay::DistanceOverlayAsset::updateWarningLevel(
    DistanceSectorId     f_sector,
    DistanceWarningLevel f_warningLevel)
{
    auto& sectorIcons        = m_sectors[static_cast<vfc::uint32_t>(f_sector)];
    const bool  isMiddleSectorFlag = isMiddleSector(f_sector);
    for (size_t i = 0u; i < NUM_WARNING; i++)
    {
        const auto icon      = sectorIcons.getIcon(i);
        if (icon == nullptr)
        {
            XLOG_ERROR(g_AppContext, "DistanceOverlayAsset: icon is null, invalid sector id: " << f_sector);
            return;
        }
        const auto l_texture = const_cast<osg::Texture2D*>(icon->getTexture()); // PRQA S 3066
        if (l_texture == nullptr)
        {
            XLOG_WARN(g_AppContext, "DistanceOverlayAsset: texture not found!");
            return;
        }
        l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
        l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

        if (f_warningLevel == NO_WARNING)
        {
            icon->setEnabled(false);
        }
        else if (f_warningLevel == FAR_GREEN)
        {
            icon->setEnabled(isMiddleSectorFlag && (i == FAR_GREEN));
        }
        else
        {
            icon->setEnabled(i == static_cast<size_t>(f_warningLevel) && (i > FAR_GREEN));
        }
    }
    IMGUI_LOG(
        "DistanceOverlay",
        getSectorNameFromIndex(f_sector) + " WarningLevel: ",
        f_warningLevel == FAR_GREEN ? "FAR_GREEN"
        : f_warningLevel == GREEN   ? "GREEN"
        : f_warningLevel == YELLOW  ? "YELLOW"
        : f_warningLevel == RED     ? "RED"
                                    : "NO_WARNING");
}

osg::Vec2f cc::assets::distanceoverlay::DistanceOverlayAsset::getDistanceDisplayPositon(DistanceSectorId f_sectorID)
{
    DigitalPositionSettings l_digitalPositionSettings = g_settings->m_frontMiddle;
    switch (f_sectorID)
    {
    case FRONT_LEFT_SIDE:
    {
        l_digitalPositionSettings = g_settings->m_frontLeftSide;
        break;
    }
    case FRONT_LEFT:
    {
        l_digitalPositionSettings = g_settings->m_frontLeft;
        break;
    }
    case FRONT_MIDDLE:
    {
        l_digitalPositionSettings = g_settings->m_frontMiddle;
        break;
    }
    case FRONT_RIGHT:
    {
        l_digitalPositionSettings = g_settings->m_frontRight;
        break;
    }
    case FRONT_RIGHT_SIDE:
    {
        l_digitalPositionSettings = g_settings->m_frontRightSide;
        break;
    }
    case REAR_LEFT_SIDE:
    {
        l_digitalPositionSettings = g_settings->m_rearLeftSide;
        break;
    }
    case REAR_LEFT:
    {
        l_digitalPositionSettings = g_settings->m_rearLeft;
        break;
    }
    case REAR_MIDDLE:
    {
        l_digitalPositionSettings = g_settings->m_rearMiddle;
        break;
    }
    case REAR_RIGHT:
    {
        l_digitalPositionSettings = g_settings->m_rearRight;
        break;
    }
    case REAR_RIGHT_SIDE:
    {
        l_digitalPositionSettings = g_settings->m_rearRightSide;
        break;
    }
    default:
    {
        break;
    }
    }

    switch (m_warningLevels[f_sectorID])
    {
    case FAR_GREEN:
    {
        return l_digitalPositionSettings.m_posLevel4;
        }
    case GREEN:
    {
        return l_digitalPositionSettings.m_posLevel3;
        }
    case YELLOW:
    {
        return l_digitalPositionSettings.m_posLevel2;
        }
    case RED:
    default:
    {
        return l_digitalPositionSettings.m_posLevel1;
        }
    }
    return l_digitalPositionSettings.m_posLevel1; // PRQA S 2880
    ;
}

void cc::assets::distanceoverlay::DistanceOverlayAsset::updateDistanceDisplay(
    const cc::target::common::SonarAPPData_st& f_SonarAPPData)
{
    const cc::target::common::ESonarDetectDistanceStatus& l_distShortestRear      = f_SonarAPPData.m_distShortestRear;
    const cc::target::common::ESonarDetectDistanceStatus& l_distShortestFront     = f_SonarAPPData.m_distShortestFront;
    const cc::target::common::ESoundObstacleZone&         l_distShortestRearZone  = f_SonarAPPData.m_distShortestRearZone;
    const cc::target::common::ESoundObstacleZone&         l_distShortestFrontZone = f_SonarAPPData.m_distShortestFrontZone;
    m_distanceDisplay.setAllEnabled(false);

    //! FRONT
    if (l_distShortestFront != cc::target::common::ESonarDetectDistanceStatus::NO_DETECTION)
    {
        const std::string l_frontDistanceDigitalTexturePath =
            getDistanceTextureByValue(static_cast<DistanceDigitalValue>(static_cast<vfc::uint8_t>(l_distShortestFront) - static_cast<vfc::uint8_t>(1)));
        switch (l_distShortestFrontZone)
        {
        case cc::target::common::ESoundObstacleZone::LEFT:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_LEFT))->setImage(l_frontDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_LEFT))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::FRONT_LEFT), pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_LEFT))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::CENTER:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_MIDDLE))->setImage(l_frontDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_MIDDLE))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::FRONT_MIDDLE), pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_MIDDLE))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::RIGHT:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_RIGHT))->setImage(l_frontDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_RIGHT))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::FRONT_RIGHT), pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_RIGHT))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::LEFT_SIDE:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_LEFT_SIDE))->setImage(l_frontDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_LEFT_SIDE))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::FRONT_LEFT_SIDE),
                    pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_LEFT_SIDE))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::RIGHT_SIDE:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_RIGHT_SIDE))->setImage(l_frontDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_RIGHT_SIDE))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::FRONT_RIGHT_SIDE),
                    pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(FRONT_RIGHT_SIDE))->setEnabled(true);
            break;
        }
        default:
        {
            break;
        }
        }
    }
    //! REAR

    if (l_distShortestRear != cc::target::common::ESonarDetectDistanceStatus::NO_DETECTION)
    {
        const std::string l_rearDistanceDigitalTexturePath =
            getDistanceTextureByValue(static_cast<DistanceDigitalValue>(static_cast<vfc::uint8_t>(l_distShortestRear) - static_cast<vfc::uint8_t>(1)));
        switch (l_distShortestRearZone)
        {
        case cc::target::common::ESoundObstacleZone::LEFT:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_LEFT))->setImage(l_rearDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_LEFT))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::REAR_LEFT), pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_LEFT))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::CENTER:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_MIDDLE))->setImage(l_rearDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_MIDDLE))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::REAR_MIDDLE), pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_MIDDLE))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::RIGHT:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_RIGHT))->setImage(l_rearDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_RIGHT))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::REAR_RIGHT), pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_RIGHT))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::LEFT_SIDE:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_LEFT_SIDE))->setImage(l_rearDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_LEFT_SIDE))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::REAR_LEFT_SIDE),
                    pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_LEFT_SIDE))->setEnabled(true);
            break;
        }
        case cc::target::common::ESoundObstacleZone::RIGHT_SIDE:
        {
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_RIGHT_SIDE))->setImage(l_rearDistanceDigitalTexturePath);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_RIGHT_SIDE))
                ->setPosition(
                    getDistanceDisplayPositon(DistanceSectorId::REAR_RIGHT_SIDE),
                    pc::assets::Icon::UnitType::Percentage);
            m_distanceDisplay.getIcon(static_cast<vfc::int32_t>(REAR_RIGHT_SIDE))->setEnabled(true);
            break;
        }
        default:
        {
            break;
        }
        }
    }

    for (vfc::int32_t i = 0; i < NUM_SECTOR; i++)
    {
        const auto l_icon = m_distanceDisplay.getIcon(i); // PRQA S 3000
        if (l_icon == nullptr)
        {
            XLOG_ERROR(g_AppContext, "DistanceOverlayAsset: icon is null - m_distanceDisplay");
            return;
        }
        setIconSize(l_icon);
        const auto l_texture = const_cast<osg::Texture2D*>(l_icon->getTexture()); // PRQA S 3066
        if (l_texture == nullptr)
        {
            XLOG_WARN(g_AppContext, "DistanceOverlayAsset: texture not found! - m_distanceDisplay");
            return;
        }
        l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
        l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    }
}
bool       DistanceOverlayAsset::s_showIcons  = false;
vfc::float64_t     DistanceOverlayAsset::s_startTime  = 0.0;
bool       DistanceOverlayAsset::s_isFlashing = false;
osg::Timer DistanceOverlayAsset::s_timer;
bool       DistanceOverlayAsset::s_rearSonarsError  = false;
bool       DistanceOverlayAsset::s_frontSonarsError = false;
bool       DistanceOverlayAsset::s_showReq          = false;

void DistanceOverlayAsset::sonarDegrationHandling(bool f_isFrontSonarsError, bool f_isRearSonarsError, bool f_showReq)
{
    if (f_isFrontSonarsError || f_isRearSonarsError)
    {
        m_degrationOverlays.getIcon(DEGRATION_WARN)->setEnabled(true);
    }
    else
    {
        m_degrationOverlays.getIcon(DEGRATION_WARN)->setEnabled(false);
    }

    if ((!s_frontSonarsError && f_isFrontSonarsError) || (!s_rearSonarsError && f_isRearSonarsError))
    {
        s_isFlashing = true;
        s_startTime  = s_timer.time_s(); // PRQA S 3803
    }

    if ((!s_showReq && f_showReq))
    {
        s_isFlashing = true;
        s_startTime  = s_timer.time_s();
        s_showReq    = true; // only once
    }

    s_frontSonarsError = f_isFrontSonarsError;
    s_rearSonarsError  = f_isRearSonarsError;
}

void DistanceOverlayAsset::updateDegrationWarning()
{
    const vfc::float64_t elapsedTime = s_timer.time_s() - s_startTime;

    if (elapsedTime > 3.0)
    {
        s_isFlashing = false;
        setIconsEnabled(false);
        return;
    }

    constexpr vfc::float64_t interval   = 0.5;
    const vfc::float64_t tmp = elapsedTime / interval;
    const bool   shouldShow = static_cast<vfc::int32_t>(tmp) % 2 == 0; // PRQA S 3016

    if (s_showIcons != shouldShow)
    {
        s_showIcons = shouldShow;
        setIconsEnabled(s_showIcons);
    }
}

void DistanceOverlayAsset::setIconsEnabled(bool enabled)
{
    m_degrationOverlays.getIcon(DEGRATION_FRONT)->setEnabled(enabled && s_frontSonarsError);
    m_degrationOverlays.getIcon(DEGRATION_REAR)->setEnabled(enabled && s_rearSonarsError);
}

} // namespace distanceoverlay
} // namespace assets
} // namespace cc
