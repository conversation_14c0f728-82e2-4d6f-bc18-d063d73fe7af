//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/core/inc/CustomFramework.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "vfc/core/vfc_types.hpp" // PRQA S 0034

#include "osg/Group"

// #include "cc/target/common/inc/commonInterface.h"

namespace cc
{
namespace assets
{
namespace rimline
{
//!
//! RimLineSettings
//!

class RimLineSettings : public pc::util::coding::ISerializable
{
public:
    RimLineSettings()
        : m_groundLevel(0.001f)
        , m_lineWidth(0.02) 
        , m_distanceToVehicleBumper(0.3)
        , m_distanceToVehicleSide(0.25)
        , m_prallelLineLengthUpward(0.36) 
        , m_prallelLineLengthDownward(1.12) 
        , m_verticalLineLengthLeftward(0.12)     
        , m_verticalLineLengthRightward(0.3) 
    {
    }
    SERIALIZABLE(RimLineSettings)
    {
        ADD_FLOAT_MEMBER(groundLevel);
        ADD_FLOAT_MEMBER(lineWidth);
        ADD_FLOAT_MEMBER(distanceToVehicleBumper);
        ADD_FLOAT_MEMBER(distanceToVehicleSide);
        ADD_FLOAT_MEMBER(prallelLineLengthUpward);
        ADD_FLOAT_MEMBER(prallelLineLengthDownward);
        ADD_FLOAT_MEMBER(verticalLineLengthLeftward);
        ADD_FLOAT_MEMBER(verticalLineLengthRightward);
    }

    vfc::float32_t m_groundLevel;
    vfc::float32_t m_lineWidth;
    vfc::float32_t m_distanceToVehicleBumper;
    vfc::float32_t m_distanceToVehicleSide;
    vfc::float32_t m_prallelLineLengthUpward;  // refer to the front left wheel
    vfc::float32_t m_prallelLineLengthDownward;  // refer to the front left wheel
    vfc::float32_t m_verticalLineLengthLeftward;  // refer to the front left wheel
    vfc::float32_t m_verticalLineLengthRightward;  // refer to the front left wheel
};

extern pc::util::coding::Item<RimLineSettings> g_rimLineSettings;

enum ERimPlaneView : vfc::uint8_t
{
    RIMPLANEVIEW_NONE  = 0,
    RIMPLANEVIEW_FRONT = 1,
    RIMPLANEVIEW_REAR  = 2
};

//!
//! RimProtectionLine
//!
class RimProtectionLine : public osg::Group
{
public:
    static const int c_pointSum =  12u;
    static const int c_triangleSum =  30u;

    RimProtectionLine(ERimPlaneView f_view);
    virtual ~RimProtectionLine() = default;
    virtual void traverse(osg::NodeVisitor& f_nv) override;

    void update(ERimPlaneView f_view = RIMPLANEVIEW_NONE);
    void initCrossPoint();
    void updateLineVertices(osg::Vec3Array* f_vertices);
private:
    //! Copy constructor is not permitted.
    RimProtectionLine(const RimProtectionLine& other) = delete;
    //! Copy assignment operator is not permitted.
    RimProtectionLine& operator=(const RimProtectionLine& other) = delete;
    vfc::uint32_t m_lastConfigUpdate;
    osg::Vec3f m_crossPoint;
    ERimPlaneView m_rimPlaneView;
};


} // namespace rimline
} // namespace assets
} // namespace cc
