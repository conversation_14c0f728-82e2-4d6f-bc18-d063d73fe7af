//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ParkingSearching.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_PARKINGSEARCHING_H
#define CC_ASSETS_UIELEMENTS_PARKINGSEARCHING_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"
#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

//!
//! ParkingIconManager
//!
class ParkingSearchingManager
{
public:
  ParkingSearchingManager();
  virtual ~ParkingSearchingManager();

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);
  void update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);

private:
  //! Copy constructor is not permitted.
  ParkingSearchingManager (const ParkingSearchingManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSearchingManager& operator=(const ParkingSearchingManager& other); // = delete
  
  unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_settingParkSearching;
  bool m_mat_b;
};


//!
//! ParkingSearching
//!
class ParkingSearching: public cc::assets::uielements::CustomImageOverlays
{
public:
  ParkingSearching(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~ParkingSearching();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  ParkingSearching (const ParkingSearching& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkingSearching& operator=(const ParkingSearching& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ParkingSearchingManager m_manager;
};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_PARKINGMODE_H
