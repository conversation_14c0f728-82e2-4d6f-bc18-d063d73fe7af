//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#include "cc/assets/button/inc/CustomButtons.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui_manager.h"
namespace cc
{
namespace assets
{
namespace button
{

static pc::util::coding::Item<CustomButtonsSetting> g_customButtonsSetting("CustomButtonsSetting");

CustomButton::CustomButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
{
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine // PRQA S 4262 // PRQA S 4264
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
}

void CustomButton::onInvalid()
{
    setIconEnable(false);
}

void CustomButton::onUnavailable()
{
    setIconEnable(true);
}

void CustomButton::onAvailable()
{
    setIconEnable(true);
}

void CustomButton::onPressed()
{
    setIconEnable(true);
}

void CustomButton::onReleased()
{
    setIconEnable(true);
}

bool CustomButton::isPressed(ButtonState f_buttonState)
{
    bool l_pressed = false;
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)



    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)


    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }
    const bool        touchInsideResponseArea = checkTouchInsideResponseArea();
//    const TouchStatus touchSts                = touchStatus(); // PRQA S 3803

    if (AVAILABLE == f_buttonState || PRESSED == f_buttonState)
    {
        if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
        {
            l_pressed = true;
        }
    }

    switch (getState())
    {
    case INVALID:
        {IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;}
    case UNAVAILABLE:
        {IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
        break;}
    case AVAILABLE:
        {IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
        break;}
    case PRESSED:
        {IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
        break;}
    default:
        {break;}
    }

    return l_pressed;
}

void CustomButton::handleTouch()
{
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)



    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)


    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }
    const bool        touchInsideResponseArea = checkTouchInsideResponseArea();
//    const TouchStatus touchSts                = touchStatus(); // PRQA S 3803
    ButtonState currentState            = getState();

    switch (currentState)
    {
        case AVAILABLE:
        {
            if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
            {
                currentState = PRESSED;
            }
            break;
        }
        case PRESSED:
        {
            if (touchStatusChanged && touchStatus() == TOUCH_UP || currentState == INVALID || currentState == UNAVAILABLE)
            {
                currentState = RELEASED;
            }
            else if (touchInsideResponseArea && touchStatus() == PRESSED)
            {
                currentState = PRESSED;
            }
            else{}
            break;
        }
        case RELEASED:
        {
            currentState = AVAILABLE;
            break;
        }
        case INVALID:
        case UNAVAILABLE:
        default:
        {
            break;
        }
    }

    setState(currentState);

    switch (getState())
    {

    case INVALID:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    case UNAVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
        break;
    }
    case AVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
        break;
    }
    case PRESSED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
        break;
    }
    case RELEASED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "RELEASED");
        break;
    }
    default:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    }
}

//! Close Button: not show, show
CloseButton::CloseButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : CustomButton{f_assetId, f_framework, f_referenceView}
    , m_preGear{pc::daddy::EGear::GEAR_INIT}
    , m_delayCounter{0}
    , m_gearChangedToP{false}
{
    setState(AVAILABLE);
    setName("Close Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }

    if (f_referenceView->getName() == "Float Button View")
    {
        // setCustomButtonSize(g_customButtonsSetting->m_floatCloseButtonSetting.m_buttonSize); //use custom size, not
        // texture size
        setPositionHori(g_customButtonsSetting->m_floatCloseButtonSetting.m_horiPos);
        setTexturePath(
            g_customButtonsSetting->m_floatCloseButtonSetting.m_buttonTexture.m_night.m_AvailableTexturePath);
    }
    else
    {
        setPositionHori(g_customButtonsSetting->m_closeButtonSetting.m_horiPos);
        setTexturePath(g_customButtonsSetting->m_closeButtonSetting.m_buttonTexture.m_night.m_AvailableTexturePath);
    }
}

void CloseButton::deliverPressed(bool f_isPressed)
{
    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_CloseButtonPressedDaddy_SenderPort, f_isPressed);

    static bool l_isPressed = false;
    if (f_isPressed == true && l_isPressed == false)
    {
        XLOG_INFO(g_AppContext, "Close Button Pressed ");
        IMGUI_LOG("Buttons", getName() + "Output", "PRESSED");
    }
    else if (f_isPressed == false && l_isPressed == true)
    {
        XLOG_INFO(g_AppContext, "Close Button Released ");
        IMGUI_LOG("Buttons", getName() + "Output", "Released");
    }
    else
    {
        // do nothing
    }
    l_isPressed = f_isPressed;
}

void CloseButton::update()
{
    if (m_referenceView->getName() == "Float Button View")
    {
        EScreenID       l_displayedView = EScreenID::EScreenID_SINGLE_FRONT_NORMAL;
        if (m_framework->asCustomFramework()->m_displayedView_ReceiverPort.hasData())
        {
            const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayedViewDaddy =
                m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
            l_displayedView = l_displayedViewDaddy->m_Data;
        }
        const bool l_isFRView =
            (l_displayedView == EScreenID_FLOAT_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_REAR_VIEW ||
            l_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW);

        if(l_isFRView)
        {
            const osg::Vec2f s_planViewPos=osg::Vec2f(78.0f, 982.0f);
            setPositionHori(s_planViewPos);
            m_dirty=true;
        }
        else
        {
            setPositionHori(g_customButtonsSetting->m_floatCloseButtonSetting.m_horiPos);
            m_dirty=true;
        }
    }

    EGear l_curGear = EGear_P;
    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = static_cast<vfc::uint8_t>(l_pData->m_Data);
        }
    }

    if (m_preGear != EGear_P && l_curGear == EGear_P)
    {
        m_gearChangedToP = true; // Gear Change to P
    }
    else if (m_preGear == EGear_P && l_curGear != EGear_P)
    {
        m_gearChangedToP = false; // Gear Change from P
    }
    else{}
    m_preGear = l_curGear;

    bool l_isParking = false;
    if (m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.isConnected())
    {
        const cc::daddy::APAFunctionStatusDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            if ((l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PARKINGINOUT || (l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PAUSED)
            {
                l_isParking = true;
            }
        }
    }

    ButtonState l_currentState = INVALID;
    if (l_isParking == false && l_curGear != EGear_R)
    {
        if (m_gearChangedToP)
        {
            m_delayCounter++;
            if (m_delayCounter > g_customButtonsSetting->m_displayDelay)
            {
                l_currentState   = AVAILABLE;
                m_gearChangedToP = false;
                m_delayCounter   = 0;
            }
            else
            {
                // Do nothing
            }
        }
        else
        {
            l_currentState = AVAILABLE;
        }
    }

    setState(l_currentState);
    deliverPressed(isPressed(l_currentState));
}

//! Enlarge Button
EnlargeButton::EnlargeButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : CustomButton{f_assetId, f_framework, f_referenceView}
    , m_preGear{pc::daddy::EGear::GEAR_INIT}
    , m_delayCounter{0}
    , m_gearChangedToP{false}
{
    setState(AVAILABLE);
    setName("Enlarge Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }

    setPositionHori(g_customButtonsSetting->m_EnlargePlanButtonSetting.m_horiPos);
    setTexturePath(
        g_customButtonsSetting->m_EnlargePlanButtonSetting.m_buttonTexture.m_night.m_AvailableTexturePath);
}

void EnlargeButton::deliverPressed(bool f_isPressed)
{
    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_EnlargeButtonPressedDaddy_SenderPort, f_isPressed); // PRQA S 4500
}

void EnlargeButton::update()
{
    EScreenID       l_displayedView = EScreenID::EScreenID_SINGLE_FRONT_NORMAL;
    if (m_framework->asCustomFramework()->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayedViewDaddy =
            m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
        l_displayedView = l_displayedViewDaddy->m_Data;
    }
    const bool l_isPlanView =
        (l_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW || l_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW ||
         l_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW || l_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW);
    if(l_isPlanView)
    {
        setPositionHori(g_customButtonsSetting->m_EnlargePlanButtonSetting.m_horiPos);
        m_dirty=true;
    }
    else
    {
        setPositionHori(g_customButtonsSetting->m_EnlargeFRButtonSetting.m_horiPos);
        m_dirty=true;
    }

    bool l_isParking = false;
    if (m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.isConnected())
    {
        const cc::daddy::APAFunctionStatusDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            if ((l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PARKINGINOUT || (l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PAUSED)
            {
                l_isParking = true;
            }
        }
    }

    ButtonState l_currentState = INVALID;
    if (l_isParking == false)
    {
        l_currentState = AVAILABLE;
    }

    setState(l_currentState);
    deliverPressed(isPressed(l_currentState));
}

//! Park Button: show & not clickable, show & clickable, show & clicked
ParkButton::ParkButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : CustomButton{f_assetId, f_framework, f_referenceView}
    , m_showStatus{false}
{
    setName("Park Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(g_customButtonsSetting->m_parkButtonSetting.m_horiPos);
    setTexturePath(g_customButtonsSetting->m_parkButtonSetting.m_buttonTexture.m_night.m_UnavailableTexturePath);
}

void ParkButton::deliverPressed(bool f_isPressed)
{
    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_parkButtonPress_SenderPort, f_isPressed ? (cc::target::common::EParkButtonPress::PARK_BUTTON_PRESSED) : (cc::target::common::EParkButtonPress::PARK_BUTTON_NOT_PRESS));



    static bool l_isPressed = false;
    if (f_isPressed == true && l_isPressed == false)
    {
        XLOG_INFO(g_AppContext, "Park Button Pressed ");
        IMGUI_LOG("Buttons", getName() + "Output", "PRESSED");
    }
    else if (f_isPressed == false && l_isPressed == true)
    {
        XLOG_INFO(g_AppContext, "Park Button Released ");
        IMGUI_LOG("Buttons", getName() + "Output", "Released");
    }
    else
    {
        // do nothing
    }
    l_isPressed = f_isPressed;
}
void ParkButton::onUnavailable()
{
    setTexturePath(g_customButtonsSetting->m_parkButtonSetting.m_buttonTexture.m_night.m_UnavailableTexturePath);
    setIconEnable(true);
}

void ParkButton::onAvailable()
{
    setTexturePath(g_customButtonsSetting->m_parkButtonSetting.m_buttonTexture.m_night.m_AvailableTexturePath);
    setIconEnable(true);
}

void ParkButton::onPressed()
{
    setTexturePath(g_customButtonsSetting->m_parkButtonSetting.m_buttonTexture.m_night.m_PressedTexturePath);
    setIconEnable(true);
}

void ParkButton::update()
{
    bool l_curAPASwitchStatus = true;
//    constexpr bool l_curShowReqStatus   = false;
    // if (m_framework->asCustomFramework()->m_APAEnterSwitchStatus_ReceiverPort.isConnected())
    // {
    //     const cc::daddy::APAEnterSwitchStatusDaddy_t* l_pData =
    //         m_framework->asCustomFramework()->m_APAEnterSwitchStatus_ReceiverPort.getData();
    //     if (nullptr != l_pData)
    //     {
    //         l_curAPASwitchStatus &=
    //             ((l_pData->m_Data == cc::target::common::EAPAEnterSwitchStatus::ENTERSTS_AVAILABLE) ||
    //              (l_pData->m_Data == cc::target::common::EAPAEnterSwitchStatus::ENTERSTS_HIGHLIGHT));
    //     }
    // }

    bool l_UnityIsReady = false;
    bool l_apaIsReady = false;
    if (m_framework->asCustomFramework()->m_UnityIsReady_ReceiverPort.isConnected())
    {
        const cc::daddy::UnityIsReadyDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_UnityIsReady_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            if (l_pData->m_Data == 1u)
            {
                l_UnityIsReady = true;
            }
        }
    }

    if (m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.isConnected())
    {
        const cc::daddy::APAFunctionStatusDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            if ((l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_STANDBY || (l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_SEARCHINGINOUT)
            {
                l_apaIsReady = true;
            }
        }
    }

    l_curAPASwitchStatus = (l_UnityIsReady && l_apaIsReady);

    ButtonState l_currentState = UNAVAILABLE;
    if (l_curAPASwitchStatus)
    {
        l_currentState = AVAILABLE;
    }

    const bool l_isPressed = isPressed(l_currentState);
    if (l_isPressed)
    {
        l_currentState = PRESSED;
    }
    setState(l_currentState);
    deliverPressed(l_isPressed);

    // release button  manually when avm closed
    // if (m_framework->asCustomFramework()->m_showReq_ReceiverPort.isConnected())
    // {
    //     const cc::daddy::SVSShowReqDaddy_t* l_pData =
    //         m_framework->asCustomFramework()->m_showReq_ReceiverPort.getData();
    //     if (nullptr != l_pData)
    //     {
    //         l_curShowReqStatus = l_pData->m_Data;
    //     }

    //     if (l_curShowReqStatus == false && m_showStatus!= false)
    //     {
    //         deliverPressed(false);
    //     }
    //     m_showStatus = l_curShowReqStatus;
    // }
}

//! SonarLoudSpeakerButton: no show, show & mute, show & unmute
SonarLoudSpeakerButton::SonarLoudSpeakerButton(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : CustomButton{f_assetId, f_framework, f_referenceView}
{
    setState(AVAILABLE);
    setName("SonarLoudSpeakerButton");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }

    if (f_referenceView->getName() == "Float Button View")
    {
        setCustomButtonSize(
            g_customButtonsSetting->m_floatCloseButtonSetting.m_buttonSize); // use custom size, not texture size
        setPositionHori(g_customButtonsSetting->m_floatSonarLoudSpeakerButtonMutedSetting.m_horiPos);
        setTexturePath(g_customButtonsSetting->m_floatSonarLoudSpeakerButtonMutedSetting.m_buttonTexture.m_night
                           .m_AvailableTexturePath);
    }
    else
    {
        setPositionHori(g_customButtonsSetting->m_SonarLoudSpeakerButtonMutedSetting.m_horiPos);
        setTexturePath(g_customButtonsSetting->m_SonarLoudSpeakerButtonMutedSetting.m_buttonTexture.m_night
                           .m_AvailableTexturePath);
    }
}

void SonarLoudSpeakerButton::deliverPressed(bool f_isPressed)
{
    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_PPWarningSwitch_SenderPort, f_isPressed);

    static bool l_isPressed = false;
    if (f_isPressed == true && l_isPressed == false)
    {
        XLOG_INFO(g_AppContext, "PPWarningSwitch Pressed ");
        IMGUI_LOG("Buttons", getName() + "Output", "PRESSED");
    }
    else if (f_isPressed == false && l_isPressed == true)
    {
        XLOG_INFO(g_AppContext, "PPWarningSwitch Released ");
        IMGUI_LOG("Buttons", getName() + "Output", "Released");
    }
    else
    {
        // do nothing
    }
    l_isPressed = f_isPressed;
}

void SonarLoudSpeakerButton::update()
{
    if (m_referenceView->getName() == "Float Button View")
    {
        EScreenID       l_displayedView = EScreenID::EScreenID_SINGLE_FRONT_NORMAL;
        if (m_framework->asCustomFramework()->m_displayedView_ReceiverPort.hasData())
        {
            const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayedViewDaddy =
                m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
            l_displayedView = l_displayedViewDaddy->m_Data;
        }
        const bool l_isFRView =
            (l_displayedView == EScreenID_FLOAT_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_REAR_VIEW ||
            l_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW);

        if(l_isFRView)
        {
            const osg::Vec2f s_planViewPos=osg::Vec2f(78.0f, 76.0f);
            setPositionHori(s_planViewPos);
            m_dirty=true;
        }
        else
        {
            setPositionHori(g_customButtonsSetting->m_floatSonarLoudSpeakerButtonMutedSetting.m_horiPos);
            m_dirty=true;
        }
    }
    bool l_isSonarLoudSpeakerMuted           = false;
    bool l_isSonarLoudSpeakerButtonAvailable = true;
    if (m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.isConnected())
    {
        const cc::daddy::SonarAPPDataDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            l_isSonarLoudSpeakerMuted =
                (l_pData->m_Data.m_soundLevelRequest == cc::target::common::ESonarSoundLevelRequest::SONARSOUND_MUTE);
            l_isSonarLoudSpeakerButtonAvailable =
                l_isSonarLoudSpeakerButtonAvailable && (l_pData->m_Data.m_sonarStatusDisplayRequest != cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_DEACTIVE);
        }
    }
    else
    {
        l_isSonarLoudSpeakerButtonAvailable = false;
    }

    if (m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.isConnected())
    {
        const cc::daddy::APAFunctionStatusDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_APAFuncStatus_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            if ((l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PARKINGINOUT || (l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PAUSED)
            {
                l_isSonarLoudSpeakerButtonAvailable = false;
            }
        }
    }

    // if (m_framework->asCustomFramework()->m_displayedView_ReceiverPort.isConnected())
    // {
    //     const cc::daddy::SVSDisplayedViewDaddy_t* l_pData =
    //         m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
    //     if (nullptr != l_pData)
    //     {
    //         l_isSonarLoudSpeakerButtonAvailable &=
    //             ((l_pData->m_Data != EScreenID::EScreenID_SINGLE_REAR_JUNCTION) &&
    //              (l_pData->m_Data != EScreenID::EScreenID_SINGLE_FRONT_JUNCTION));
    //     }
    // }

    if (l_isSonarLoudSpeakerMuted)
    {
        setTexturePath(g_customButtonsSetting->m_SonarLoudSpeakerButtonMutedSetting.m_buttonTexture.m_night
                           .m_AvailableTexturePath);
    }
    else
    {
        setTexturePath(g_customButtonsSetting->m_SonarLoudSpeakerButtonUnMutedSetting.m_buttonTexture.m_night
                           .m_AvailableTexturePath);
    }

    ButtonState l_currentState = INVALID;
    if (l_isSonarLoudSpeakerButtonAvailable)
    {
        l_currentState = AVAILABLE;
    }
    setState(l_currentState);
    deliverPressed(isPressed(l_currentState));
}

//FloatViewChangeButton
FloatViewChangeButton::FloatViewChangeButton(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : CustomButton{f_assetId, f_framework, f_referenceView},
    m_framework(f_framework),
    m_FloatViewTypeButtonTexturePath{}
{
    setState(AVAILABLE);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    if(f_assetId== cc::core::AssetId::EASSETS_FLOAT_PLAN_VIEW_CHANGE_BUTTON){
        setName("FloatPlanViewChangeButton");
        m_buttonFloatViewType=cc::daddy::FLOAT_PLANVIEW;
        setPositionHori(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewPlanButtonPos);
        setTexturePath(
            g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewButtonTexturePath.m_EnabledTexturePath);
    }
    else
    {
        setName("FloatFRViewChangeButton");
        m_buttonFloatViewType=cc::daddy::FLOAT_FRVIEW;
        setPositionHori(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewFRButtonPos);
        setTexturePath(
            g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatFrontViewButtonTexturePath.m_EnabledTexturePath);
    }
}
void  FloatViewChangeButton::updateFloatPlanViewButton()
{
    EScreenID       l_displayedView = EScreenID::EScreenID_SINGLE_FRONT_NORMAL;
    if (m_framework->asCustomFramework()->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayedViewDaddy =
            m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
        l_displayedView = l_displayedViewDaddy->m_Data;
    }
    const bool l_isFRView =
        (l_displayedView == EScreenID_FLOAT_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_REAR_VIEW ||
         l_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW);

    if(l_isFRView)
    {
        setPositionHori(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatFRViewPlanButtonPos);
        setTexturePath(
            g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewButtonTexturePath.m_DisabledTexturePath);
    }
    else
    {
        setPositionHori(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewPlanButtonPos);
        setTexturePath(
            g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewButtonTexturePath.m_EnabledTexturePath);
    }

    ButtonState l_currentState = INVALID;

    l_currentState = AVAILABLE;

    setState(l_currentState);
    deliverPressed(isPressed(l_currentState));

}
void  FloatViewChangeButton::updateFloatFRViewButton()
{

    EScreenID       l_displayedView = EScreenID::EScreenID_SINGLE_FRONT_NORMAL;
    if (m_framework->asCustomFramework()->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayedViewDaddy =
            m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
        l_displayedView = l_displayedViewDaddy->m_Data;
    }
    const bool l_isFRView =
        (l_displayedView == EScreenID_FLOAT_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_REAR_VIEW ||
         l_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW || l_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW);
    static bool s_showFront=true;
    EGear l_curGear = EGear_P;
    if(m_framework->asCustomFramework()->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = static_cast<vfc::uint8_t>(l_pData->m_Data);
        }
    }
    static vfc::int32_t l_delayTime=0;
    static EGear s_lastGear = EGear_Init;
    if(s_lastGear!=EGear_P&&l_curGear==EGear_P)
    {
        l_delayTime=3;
    }
    s_lastGear=l_curGear;
    if(l_curGear==EGear_P)
    {
        if(l_delayTime<=0)
        {
           s_showFront=true;
        }
        else
        {
            l_delayTime--;
        }
    }

    if(l_curGear==EGear_D)
    {
        s_showFront=true;
    }
    if(l_curGear==EGear_R)
    {
        s_showFront=false;
    }
    if(s_showFront)
    {
        if(l_isFRView)
        {
            setTexturePath(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatFrontViewButtonTexturePath.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatFrontViewButtonTexturePath.m_DisabledTexturePath);
        }
    }
    else
    {
        if(l_isFRView)
        {
            setTexturePath(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatRearViewButtonTexturePath.m_EnabledTexturePath);
        }
        else
        {
            setTexturePath(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatRearViewButtonTexturePath.m_DisabledTexturePath);
        }

    }
    if(l_isFRView)
    {
        setPositionHori(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatFRViewFRButtonPos);
    }
    else
    {
        setPositionHori(g_customButtonsSetting->m_FloatViewChangeButtonSettings.m_FloatPlanViewFRButtonPos);
    }
    ButtonState l_currentState = INVALID;

    l_currentState = AVAILABLE;

    setState(l_currentState);
    deliverPressed(isPressed(l_currentState));
}
void  FloatViewChangeButton::update()
{
    switch(m_identifier)
    {
        case cc::core::AssetId::EASSETS_FLOAT_PLAN_VIEW_CHANGE_BUTTON:
        {
            updateFloatPlanViewButton();
            break;
        }
        case cc::core::AssetId::EASSETS_FLOAT_FR_VIEW_CHANGE_BUTTON:
        {
            updateFloatFRViewButton();
            break;
        }
        default:
        {
            break;
        }
    }
}
void FloatViewChangeButton::deliverPressed(bool f_isPressed)
{
    if(f_isPressed)
    {
        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort, m_buttonFloatViewType); // PRQA S 4500
    }
}
FloatViewChangeButtonGroup::FloatViewChangeButtonGroup(
        cc::core::AssetId                       f_assetId,
        pc::core::Framework*                    f_framework,
        osg::Camera*                            f_referenceView):
    cc::assets::button::ButtonGroup{f_assetId}
    , m_FloatViewChangeButtonSetting{}
    , m_framework{f_framework}
{
    setName("FloatViewChangeButtonGroup");

    addButton(new FloatViewChangeButton(
        cc::core::AssetId::EASSETS_FLOAT_PLAN_VIEW_CHANGE_BUTTON,
        f_framework,
        f_referenceView));
    addButton(new FloatViewChangeButton(
        cc::core::AssetId::EASSETS_FLOAT_FR_VIEW_CHANGE_BUTTON,
        f_framework,
        f_referenceView));

    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    else
    {
        XLOG_ERROR(g_AppContext, "FloatViewChangeButtonGroup setReferenceView Failed");
    }
    FloatViewChangeButtonGroup::update();
}
void FloatViewChangeButtonGroup::update()
{

}
} // namespace button
} // namespace assets
} // namespace cc // PRQA S 1041
