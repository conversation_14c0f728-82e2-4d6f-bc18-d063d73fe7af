//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomImageOverlays.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_CUSTOMIMAGEOVERLAYS_H
#define CC_ASSETS_UIELEMENTS_CUSTOMIMAGEOVERLAYS_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/uielements/inc/CustomIcon.h"
#include <osgDB/ReadFile>

namespace cc
{

namespace assets
{

namespace uielements
{

//!
//! CustomImageOverlays
//!
class CustomImageOverlays : public pc::assets::ImageOverlays
{
public:

  CustomImageOverlays(cc::core::AssetId f_assetId, osg::Camera* f_referenceView = nullptr);

  CustomImageOverlays(cc::core::AssetId f_assetId, osg::Camera* f_referenceView, cc::assets::uielements::CustomIcon::AnimationStyle f_animationStyle, vfc::uint32_t f_numberOfDelayTraversalCycle = 50u);

  void CustomSetRenderOrder(unsigned int f_customRenderOrder);

  void CustomSetRenderOrder(unsigned int f_customRenderOrder, bool f_isPreRender);

protected:

  virtual ~CustomImageOverlays();

private:
  //! Copy constructor is not permitted.
  CustomImageOverlays (const CustomImageOverlays& other); // = delete
  //! Copy assignment operator is not permitted.
  CustomImageOverlays& operator=(const CustomImageOverlays& other); // = delete

};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_CUSTOMIMAGEOVERLAYS_H
