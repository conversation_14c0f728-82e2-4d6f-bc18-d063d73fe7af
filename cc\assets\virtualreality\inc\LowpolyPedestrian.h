//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  LowpolyPedestrian.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_PESDESTRIAN_H
#define CC_ASSETS_VIRTUALREALITY_PESDESTRIAN_H

#include "pc/generic/util/coding/inc/CodingManager.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/virtualreality/inc/VirtualRealityObject.h"

#include <osg/Group>
#include <osg/MatrixTransform>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace virtualreality
{

class LowpolyPedestrianSettings : public pc::util::coding::ISerializable
{
public:
  LowpolyPedestrianSettings()
    : m_pedestrianModelFilename("cc/resources/virualReality/People.osg")
    , m_colorNormal(osg::Vec4f(0.28f, 0.41f, 0.89f, 1.0f))
    , m_colorCritical(osg::Vec4f(0.89f, 0.34f, 0.29f, 1.0f))
    , m_heightOverGround(1.0f)
  {
  }

  SERIALIZABLE(LowpolyPedestrianSettings)
  {
    ADD_STRING_MEMBER(pedestrianModelFilename);
    ADD_MEMBER(osg::Vec4f, colorNormal);
    ADD_MEMBER(osg::Vec4f, colorCritical);
    ADD_MEMBER(float, heightOverGround);
  }

  std::string m_pedestrianModelFilename;
  osg::Vec4f m_colorNormal;
  osg::Vec4f m_colorCritical;
  float m_heightOverGround;
};

extern pc::util::coding::Item<LowpolyPedestrianSettings> g_lowpolyPestrianSetting;

//!
//! LowpolyPedestrian
//!
class LowpolyPedestrian : public VirtualRealityObject
{
public:
  LowpolyPedestrian();

  LowpolyPedestrian(const LowpolyPedestrian& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::virtualreality, LowpolyPedestrian);

  void addObjectNode() override;

  void updateObjectNode() override;

  void setIsCritical(bool f_isCritical)
  {
    m_isCritical = f_isCritical;
    m_isColorUpdated = false;
  }

  const bool virtual getIsCritical() const
  {
    return m_isCritical;
  }

protected:
  ~LowpolyPedestrian();

private:
  bool m_isCritical;
  bool m_isColorUpdated;
  osg::ref_ptr<osg::Node> m_asset;

};



} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_PESDESTRIAN_H
