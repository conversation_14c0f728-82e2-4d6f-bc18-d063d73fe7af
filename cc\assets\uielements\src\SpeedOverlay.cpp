//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  SpeedOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/SpeedOverlay.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h" // PRQA S 1060
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace uielements
{

pc::util::coding::Item<SpeedOverlaySettings> g_displaySettings("SpeedOverlay");

//!
//! SpeedOverlay
//!
SpeedOverlay::SpeedOverlay(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_SpeedDisGeode{}
{
  setName("SpeedOverlay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


SpeedOverlay::~SpeedOverlay() = default;


void SpeedOverlay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (g_displaySettings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      addUpdateCallback(new SpeedOverlayUpdateCallback(m_SpeedDisGeode, m_framework));
      m_settingsModifiedCount = g_displaySettings->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}


void SpeedOverlay::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803

  m_SpeedDisGeode = new osg::Geode;
  addChild(m_SpeedDisGeode);    // PRQA S 3803
  {

  }


  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
}

SpeedOverlayUpdateCallback::SpeedOverlayUpdateCallback(
                                                          osg::ref_ptr<osg::Geode> f_SpeedDisGeode,
                                                          pc::core::Framework* f_pFramework
                                                          )
  : m_SpeedDisGeode{f_SpeedDisGeode}
  , m_pFramework{f_pFramework}
{

}

SpeedOverlayUpdateCallback::~SpeedOverlayUpdateCallback() = default;

void SpeedOverlayUpdateCallback::updateSpeed(osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{

  const osg::ref_ptr<osgText::Text> l_Speed = new osgText::Text;

  const osg::Vec3f l_offset = g_displaySettings->m_offset_Speed;
  const osg::Vec3f l_swPOS = -l_offset;

  l_Speed->setPosition(l_swPOS);
  l_Speed->setFont(g_displaySettings->m_fontType);
  l_Speed->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
  l_Speed->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
  l_Speed->setCharacterSize(25.0f);
  l_Speed->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
  l_Speed->setAxisAlignment(osgText::Text::XY_PLANE);
  l_Speed->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
  l_Speed->setAlignment(osgText::Text::CENTER_TOP);

  std::ostringstream l_SpeedString;

  vfc::uint8_t l_curSpeed = 0u;
  if (true == m_pFramework->asCustomFramework()->m_speedReceiver.hasData())
  {
    const pc::daddy::SpeedDaddy* const l_speedDaddy = m_pFramework->asCustomFramework()->m_speedReceiver.getData();
    if (nullptr != l_speedDaddy)
    {
        const vfc::float32_t l_temp = l_speedDaddy->m_Data+0.5f;
        l_curSpeed = static_cast<vfc::uint8_t>(l_temp); // PRQA S 3016
        const std::string str = std::to_string(static_cast<vfc::int32_t>(l_curSpeed));
        l_SpeedString << str;  // PRQA S 3803
    }
  }

  l_SpeedString << " km/h";    // PRQA S 3803

  cc::target::common::EPARKStatusR2L                l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld               l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EAPAPARKMODE                  l_curParkAPARPAParkMode   = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::EPARKDriverIndR2L             l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndSearchR2L       l_curParkDriverIndSearch  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                          l_curFreeParkingActive    = false;

  if(m_pFramework->asCustomFramework()->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = m_pFramework->asCustomFramework()->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (m_pFramework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = m_pFramework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }


  if (m_pFramework->asCustomFramework()->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = m_pFramework->asCustomFramework()->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curParkAPARPAParkMode = l_parkAPAPARKMODE->m_Data;
  }


  if (m_pFramework->asCustomFramework()->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = m_pFramework->asCustomFramework()->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }


  if (m_pFramework->asCustomFramework()->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = m_pFramework->asCustomFramework()->m_ParkDriverIndSearchReceiver.getData();
    l_curParkDriverIndSearch = l_APADriverReq_Search->m_Data;
  }

  if (m_pFramework->asCustomFramework()->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = m_pFramework->asCustomFramework()->m_freeparkingActiveReceiver.getData();
    l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if ( l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN && l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA && cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparkStatus &&  false == l_curFreeParkingActive)
  {
    if(l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_SearchingProcess || l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop || l_curParkDriverIndSearch == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleSlowdown)
    {
      f_Geode->setNodeMask(~0u);
      l_Speed->setText(l_SpeedString.str());
      if(l_curSpeed >=25u)
      {
        l_Speed->setColor(osg::Vec4f(1.0f, 0.0f, 0.0f, 1.0f));
      }
      if(l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop || l_curParkDriverIndSearch == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleSlowdown)
      {
        const osg::Vec3f l_speedoverlayPos = l_swPOS + osg::Vec3f(0.5f,0.0f,0.0f);
        l_Speed->setPosition(l_speedoverlayPos);
      }
      f_Geode->removeDrawables(0u, 1u);  // PRQA S 3803
      f_Geode->addDrawable(l_Speed);    // PRQA S 3803
    }
    else
    {
      f_Geode->setNodeMask(0u);
    }
  }
  else
  {
    f_Geode->setNodeMask(0u);
  }

}



void SpeedOverlayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
if ((f_node == nullptr) || (f_nv == nullptr))
{
    return;
}
updateSpeed(*f_nv, m_SpeedDisGeode);
traverse(f_node, f_nv);
}

} // namespace uielements
} // namespace assets
} // namespace cc
