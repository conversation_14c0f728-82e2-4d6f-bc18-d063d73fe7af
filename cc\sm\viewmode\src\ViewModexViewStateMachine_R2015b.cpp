//
// File: ViewModexViewStateMachine_R2015b.cpp
//
// Code generated for Simulink model 'ViewModexViewStateMachine_R2015b'.
//
// Model version                  : 11.419
// Simulink Coder version         : 9.6 (R2021b) 14-May-2021
// C/C++ source code generated on : Fri Aug  1 18:05:47 2025
//
// Target selection: ert.tlc
// Embedded hardware selection: ARM Compatible->ARM Cortex
// Code generation objectives:
//    1. Execution efficiency
//    2. RAM efficiency
//    3. ROM efficiency
//    4. MISRA C:2012 guidelines
//    5. Debugging
//    6. Safety precaution
// Validation result: Passed (29), Warnings (4), Error (0)
//
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_private.h"

// Named constants for Chart: '<S1>/CpcHuSwitch'
const uint8_T IN_NO_ACTIVE_CHILD = 0U;
const uint8_T IN_add = 1U;
const uint8_T IN_counterFrontView = 1U;
const uint8_T IN_counterTopView = 1U;
const uint8_T IN_false = 1U;
const uint8_T IN_reset = 2U;
const uint8_T IN_reset1 = 2U;
const uint8_T IN_temp = 2U;
const uint8_T IN_true = 2U;

// Named constants for Chart: '<S1>/CpcViewHandling'
const uint8_T IN_delay = 1U;
const uint8_T IN_entry = 2U;

// Named constants for Chart: '<S1>/DegradationHandling'
const uint8_T IN_av = 1U;
const uint8_T IN_unav = 2U;

// Named constants for Chart: '<S1>/FreeModeHandling'
const uint8_T IN_DoNothing = 1U;
const uint8_T IN_Horizontal_mode = 2U;

// Named constants for Chart: '<S1>/MainSM_Lev2'
const uint8_T IN_AVM_Error = 1U;
const uint8_T IN_AVM_Error_Activate_Triggered = 1U;
const uint8_T IN_AVM_Error_Activate_Voice = 2U;
const uint8_T IN_AVM_Error_No_Activate_Trigge = 3U;
const uint8_T IN_Acivate_Through_Compete = 1U;
const uint8_T IN_Available = 1U;
const uint8_T IN_Compete_Passed = 1U;
const uint8_T IN_Compete_Refused = 2U;
const uint8_T IN_Compete_Req_Finished = 1U;
const uint8_T IN_ECAL_CPC = 1U;
const uint8_T IN_Enlarge_delay = 1U;
const uint8_T IN_Exit_Excuted = 1U;
const uint8_T IN_Exit_Handling = 2U;
const uint8_T IN_FloatingFrontViews_NotPark = 2U;
const uint8_T IN_FloatingRearViews_NotPark = 3U;
const uint8_T IN_FloatingView = 1U;
const uint8_T IN_FloatingViews_Park = 4U;
const uint8_T IN_FreeViewID = 2U;
const uint8_T IN_Free_Parking = 3U;
const uint8_T IN_FrontPlan_FloatView = 1U;
const uint8_T IN_FrontView_From_FloatFrontVie = 1U;
const uint8_T IN_Front_FloatView = 2U;
const uint8_T IN_FullScreenViews_From_FloatSc = 4U;
const uint8_T IN_FullScreen_R = 5U;
const uint8_T IN_GearD_FrontView = 1U;
const uint8_T IN_GearD_FrontWheelView = 2U;
const uint8_T IN_GearD_FrontWideView = 3U;
const uint8_T IN_GearD_NoViewChange = 4U;
const uint8_T IN_GearD_PersRearView = 5U;
const uint8_T IN_GearNotR_Activated_Compete_F = 1U;
const uint8_T IN_GearNotR_Activated_Compete_R = 1U;
const uint8_T IN_GearNotR_Activated_Compete_k = 2U;
const uint8_T IN_GearNotR_Activated_Compete_m = 2U;
const uint8_T IN_GearR_Activated_Compete_Floa = 1U;
const uint8_T IN_GearR_Activated_Compete_Full = 2U;
const uint8_T IN_GearR_Activated_Compete_Re_d = 4U;
const uint8_T IN_GearR_Activated_Compete_Requ = 3U;
const uint8_T IN_GearR_NoViewChange = 1U;
const uint8_T IN_GearR_PersFrontView = 2U;
const uint8_T IN_GearR_RearView = 3U;
const uint8_T IN_GearR_RearWheelView = 4U;
const uint8_T IN_GearR_RearWideView = 5U;
const uint8_T IN_Gear_Activate_SVS = 1U;
const uint8_T IN_HasSonarWarning = 1U;
const uint8_T IN_HasSteeringWarning = 1U;
const uint8_T IN_Has_Compete_Feedback = 2U;
const uint8_T IN_HoldOnwithSonarWarning = 1U;
const uint8_T IN_Immediate_Exit = 2U;
const uint8_T IN_Inactive = 3U;
const uint8_T IN_LastView = 6U;
const uint8_T IN_LastViewModeGroup = 1U;
const uint8_T IN_Mannually_Active_First = 7U;
const uint8_T IN_ManualChange = 8U;
const uint8_T IN_Manual_FrontView = 1U;
const uint8_T IN_Manual_FrontWheelView = 1U;
const uint8_T IN_Manual_FrontWideView = 1U;
const uint8_T IN_Manual_RearView = 2U;
const uint8_T IN_Manual_RearWheelView = 2U;
const uint8_T IN_Manual_RearWideView = 2U;
const uint8_T IN_Manually_Activate_SVS = 2U;
const uint8_T IN_Manually_Activated_By_Voic_b = 6U;
const uint8_T IN_Manually_Activated_By_Voice_ = 5U;
const uint8_T IN_Manually_Activated_Compete_R = 7U;
const uint8_T IN_Manually_Activated_Compete_h = 9U;
const uint8_T IN_Manually_Activated_Compete_i = 8U;
const uint8_T IN_NarrowLane_Activated = 3U;
const uint8_T IN_NarrowLane_Activated_Compe_p = 11U;
const uint8_T IN_NarrowLane_Activated_Compete = 10U;
const uint8_T IN_NoChange_1min = 1U;
const uint8_T IN_NoChange_3min = 2U;
const uint8_T IN_NoSonarWarning = 1U;
const uint8_T IN_NoSonarWarning_5s = 2U;
const uint8_T IN_NoSteeringWarning = 2U;
const uint8_T IN_NoViewChangeByGearChanged = 6U;
const uint8_T IN_NoViewChangeByVoice = 1U;
const uint8_T IN_No_Compete_Req = 3U;
const uint8_T IN_Normal_activate = 4U;
const uint8_T IN_NotRMode = 9U;
const uint8_T IN_ParkActiveHandling = 4U;
const uint8_T IN_ParkToNoPark = 1U;
const uint8_T IN_Park_Activated = 2U;
const uint8_T IN_Park_Activated_Compete_Req_p = 13U;
const uint8_T IN_Park_Activated_Compete_Reque = 12U;
const uint8_T IN_Park_FrontView = 1U;
const uint8_T IN_Park_RearView = 2U;
const uint8_T IN_Park_View = 1U;
const uint8_T IN_ParkingFrontPlan_FloatView = 1U;
const uint8_T IN_ParkingPlan_FloatView = 2U;
const uint8_T IN_ParkingRearPlan_FloatView = 2U;
const uint8_T IN_Power_Save_Mode = 2U;
const uint8_T IN_Power_Save_Mode_Activate_T_g = 2U;
const uint8_T IN_Power_Save_Mode_Activate_Tri = 1U;
const uint8_T IN_Power_Save_Mode_No_Activate_ = 3U;
const uint8_T IN_RearPlan_FloatView = 1U;
const uint8_T IN_RearView_From_FloatRearView = 2U;
const uint8_T IN_Rear_FloatView = 2U;
const uint8_T IN_Request_Compete_TimeOut = 4U;
const uint8_T IN_SPD_Activate_Triggered = 1U;
const uint8_T IN_SPD_Activate_Voice = 2U;
const uint8_T IN_SPD_No_Activate_Triggered = 3U;
const uint8_T IN_Sonar_Activated = 5U;
const uint8_T IN_Sonar_Activated_Compete_Re_k = 15U;
const uint8_T IN_Sonar_Activated_Compete_Requ = 14U;
const uint8_T IN_Sonar_exitHandling = 2U;
const uint8_T IN_Start_Compete_Req = 5U;
const uint8_T IN_Steering_Activated = 6U;
const uint8_T IN_Steering_Activated_Compete_R = 16U;
const uint8_T IN_Steering_Activated_Compete_k = 17U;
const uint8_T IN_SuperStateL2 = 1U;
const uint8_T IN_Unavailable = 2U;
const uint8_T IN_VIewGroup_PersViews = 2U;
const uint8_T IN_ViewGroup_STBView = 3U;
const uint8_T IN_ViewGroup_SignleView = 4U;
const uint8_T IN_ViewGroup_WheelView = 5U;
const uint8_T IN_ViewGroup_WideView = 6U;
const uint8_T IN_ViewModeChanged = 7U;
const uint8_T IN_ViewMode_FrontGroup = 2U;
const uint8_T IN_ViewMode_PreFrontGroup = 1U;
const uint8_T IN_ViewMode_RearGroup = 3U;
const uint8_T IN_ViewMode_RearGroup_m = 2U;
const uint8_T IN_Voice_3DView_GearR = 2U;
const uint8_T IN_Voice_3DView_NotGearR = 3U;
const uint8_T IN_Voice_FloatFrontView = 4U;
const uint8_T IN_Voice_FloatRearView = 5U;
const uint8_T IN_Voice_FrontView = 6U;
const uint8_T IN_Voice_FrontWheelView = 7U;
const uint8_T IN_Voice_FrontWideView = 8U;
const uint8_T IN_Voice_RearView = 9U;
const uint8_T IN_Voice_RearWheelView = 10U;
const uint8_T IN_Voice_RearWideView = 11U;
const uint8_T IN_Voice_SKELETON = 12U;
const uint8_T IN_Voice_ViewChange_Failed_In_S = 1U;
const uint8_T IN_Voice_ViewChange_Failed_Out_ = 2U;
const uint8_T IN_Voice_View_Succeed = 3U;
const uint8_T IN_Voice_Views = 10U;
const uint8_T IN_hasChangeDuringExit = 3U;
const uint8_T IN_hasChangeDuringExit_i = 2U;
const uint8_T IN_reset_f = 3U;
const uint8_T IN_spdtoohigh = 3U;
const uint8_T IN_start = 2U;
const uint8_T IN_temp_FL = 11U;
const uint8_T IN_temp_FR = 12U;
const uint8_T IN_temp_PFR = 13U;
const uint8_T IN_temp_PLE = 14U;
const uint8_T IN_temp_PRE = 15U;
const uint8_T IN_temp_PRI = 16U;
const uint8_T IN_temp_RL = 17U;
const uint8_T IN_temp_RR = 18U;
const uint8_T IN_vidouterror = 4U;

// Named constants for Chart: '<S1>/ObstacleAndSteeringrHandling'
const uint8_T IN_NarrowLaneActivate_Handling = 1U;
const uint8_T IN_NarrowLaneActivate_SettingOf = 2U;
const uint8_T IN_NarrowLaneActivated = 1U;
const uint8_T IN_NarrowLaneNotActivated = 2U;
const uint8_T IN_ObstacleActivate_SettingOff = 1U;
const uint8_T IN_SonarActivate_Handling = 2U;
const uint8_T IN_SonarActivated = 1U;
const uint8_T IN_SonarNotActivated = 2U;
const uint8_T IN_SteeringActivated = 1U;
const uint8_T IN_SteeringNotActivated = 2U;

// Named constants for Chart: '<S1>/PasWarnToneHandling'
const uint8_T IN_NotActivate = 1U;
const uint8_T IN_ToActivate = 2U;

// Named constants for Chart: '<S1>/screenTypeHandling'
const uint8_T IN_SVSScreenType_None = 1U;
const uint8_T IN_SVSScreenType_Shown = 2U;

// Named constants for Chart: '<S1>/voiceDockFeedbackHandling'
const uint8_T IN_Has_VoiceDockRequest = 1U;
const uint8_T IN_No_VoiceDockRequest = 2U;
const uint8_T IN_temp_f = 3U;

// user code (top of source file)
// caf8si sage: Begin custom C code
//note: to read the stateflow generated doc, start at the file below:
//MATLAB/xViewStateMachine_ert_rtw/html/xViewStateMachine_codegen_rpt.html

// caf8si sage: End custom C code

// Function for Chart: '<S1>/FreeModeHandling'
void ViewModeStateFlowStateMachineModelClass::enter_internal_ViewID(const
  ESVSViewMode *UnitDelay1)
{
  // Entry Internal 'ViewID': '<S5>:56'
  // Transition: '<S5>:115'
  // '<S5>:114:1' sf_internal_predicateOutput = (EScreenID.PERSPECTIVE_RL   == in_displayedVewL ||... 
  // '<S5>:114:2' EScreenID.PERSPECTIVE_FL     == in_displayedVewL ||...
  // '<S5>:114:3' EScreenID.PERSPECTIVE_PFR  == in_displayedVewL ||...
  // '<S5>:114:4' EScreenID.PERSPECTIVE_FR  	 == in_displayedVewL ||...
  // '<S5>:114:5' EScreenID.PERSPECTIVE_RR   == in_displayedVewL ||...
  // '<S5>:114:6' EScreenID.PERSPECTIVE_PLE  	 == in_displayedVewL ||...
  // '<S5>:114:7' EScreenID.PERSPECTIVE_PRI   == in_displayedVewL ||...
  // '<S5>:114:8' EScreenID.PERSPECTIVE_PRE    == in_displayedVewL) && ...
  // '<S5>:114:9' (ESVSViewMode.VM_Perspective == in_vewModeL);
  if (((EScreenID_PERSPECTIVE_RL == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_FL == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PFR == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_FR == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_RR == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PLE == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PRI == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PRE == rtDWork.UnitDelay5)) &&
      (ESVSViewMode_VM_Perspective == *UnitDelay1))
  {
    // Transition: '<S5>:114'
    rtDWork.is_ViewID = IN_Horizontal_mode;

    // Entry Internal 'Horizontal_mode': '<S5>:103'
    // Transition: '<S5>:61'
    // Entry 'default': '<S5>:151'
    // '<S5>:151:3' out_freemodeviewID = in_displayedVewL;
    rtDWork.out_freemodeviewID = rtDWork.UnitDelay5;
  }
  else
  {
    // Transition: '<S5>:195'
    rtDWork.is_ViewID = IN_DoNothing;
  }
}

// Function for Chart: '<S1>/FreeModeHandling'
void ViewModeStateFlowStateMachineModelClass::ViewID(const ESVSViewMode
  *UnitDelay1, const uint32_T *in_campositionY_prev, const EScreenID
  *in_displayedVewL_prev)
{
  // During 'ViewID': '<S5>:56'
  // '<S5>:100:1' sf_internal_predicateOutput = hasChanged(in_campositionY)||... 
  // '<S5>:100:2' hasChanged(in_displayedVewL);
  if ((*in_campositionY_prev != rtDWork.in_campositionY_start) ||
      (*in_displayedVewL_prev != rtDWork.in_displayedVewL_start))
  {
    // Transition: '<S5>:100'
    // Exit Internal 'ViewID': '<S5>:56'
    // Exit Internal 'Horizontal_mode': '<S5>:103'
    enter_internal_ViewID(UnitDelay1);
  }
  else if (static_cast<uint32_T>(rtDWork.is_ViewID) != IN_DoNothing)
  {
    // Inport: '<Root>/In_campositionY'
    // During 'Horizontal_mode': '<S5>:103'
    // '<S5>:62:1' sf_internal_predicateOutput = in_campositionY<=90 ||...
    // '<S5>:62:2'  in_campositionY>1720;
    if ((rtU.In_campositionY <= 90U) || (rtU.In_campositionY > 1720U))
    {
      // Transition: '<S5>:62'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'Rear': '<S5>:57'
      // '<S5>:57:3' out_freemodeviewID=EScreenID.PERSPECTIVE_PRE;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_PRE;

      // '<S5>:67:1' sf_internal_predicateOutput = in_campositionY<=162;
    }
    else if (rtU.In_campositionY <= 162U)
    {
      // Transition: '<S5>:67'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'RearLeft': '<S5>:104'
      // '<S5>:104:3' out_freemodeviewID=EScreenID.PERSPECTIVE_RL;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_RL;

      // '<S5>:226:1' sf_internal_predicateOutput = in_campositionY<=738;
    }
    else if (rtU.In_campositionY <= 738U)
    {
      // Transition: '<S5>:226'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'Left': '<S5>:225'
      // '<S5>:225:3' out_freemodeviewID=EScreenID.PERSPECTIVE_PLE;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_PLE;

      // '<S5>:69:1' sf_internal_predicateOutput = in_campositionY<=810;
    }
    else if (rtU.In_campositionY <= 810U)
    {
      // Transition: '<S5>:69'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'FrontLeft': '<S5>:106'
      // '<S5>:106:3' out_freemodeviewID=EScreenID.PERSPECTIVE_FL;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_FL;

      // '<S5>:160:1' sf_internal_predicateOutput = in_campositionY<=990;
    }
    else if (rtU.In_campositionY <= 990U)
    {
      // Transition: '<S5>:160'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'Front': '<S5>:159'
      // '<S5>:159:3' out_freemodeviewID=EScreenID.PERSPECTIVE_PFR;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_PFR;

      // '<S5>:183:1' sf_internal_predicateOutput = in_campositionY<=1062;
    }
    else if (rtU.In_campositionY <= 1062U)
    {
      // Transition: '<S5>:183'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'FrontRight': '<S5>:182'
      // '<S5>:182:3' out_freemodeviewID=EScreenID.PERSPECTIVE_FR;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_FR;

      // '<S5>:230:1' sf_internal_predicateOutput = in_campositionY<=1638;
    }
    else if (rtU.In_campositionY <= 1638U)
    {
      // Transition: '<S5>:230'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'Right': '<S5>:227'
      // '<S5>:227:3' out_freemodeviewID=EScreenID.PERSPECTIVE_PRI;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_PRI;

      // '<S5>:189:1' sf_internal_predicateOutput = in_campositionY<=1720;
    }
    else if (rtU.In_campositionY <= 1720U)
    {
      // Transition: '<S5>:189'
      // Exit Internal 'Horizontal_mode': '<S5>:103'
      // Entry 'RearRight': '<S5>:188'
      // '<S5>:188:3' out_freemodeviewID=EScreenID.PERSPECTIVE_RR;
      rtDWork.out_freemodeviewID = EScreenID_PERSPECTIVE_RR;
    }
    else
    {
      // no actions
    }

    // End of Inport: '<Root>/In_campositionY'
  }
  else
  {
    // During 'DoNothing': '<S5>:194'
  }
}

real_T rt_roundd(real_T u)
{
  real_T y;
  if (std::abs(u) < 4.503599627370496E+15)
  {
    if (u >= 0.5)
    {
      y = std::floor(u + 0.5);
    }
    else if (u > -0.5)
    {
      y = 0.0;
    }
    else
    {
      y = std::ceil(u - 0.5);
    }
  }
  else
  {
    y = u;
  }

  return y;
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::durationNoChange(const
  ESVSViewMode *in_HUSVSMode_prev, const EScreenID *in_HUViewReq_prev)
{
  boolean_T l_changed;

  // During 'durationNoChange': '<S7>:598'
  // '<S7>:598:3' l_changed = (hasChanged(in_gear) || hasChanged(in_voiceDockRequest) || hasChanged(in_HUViewReq) || hasChanged(in_HUSVSMode) || ... 
  // '<S7>:598:4'     (in_touchCoorChange == true));
  l_changed = ((rtDWork.in_gear_prev != rtDWork.in_gear_start) ||
               (rtDWork.in_voiceDockRequest_prev !=
                rtDWork.in_voiceDockRequest_start) || (*in_HUViewReq_prev !=
    rtDWork.in_HUViewReq_start) || (*in_HUSVSMode_prev !=
    rtDWork.in_HUSVSMode_start) || rtDWork.out_touchCoorChange);

  // '<S7>:598:5' l_exitDelayDiffer = (in_exitDelay.ActiveExitDelay - in_exitDelay.PassiveExitDelay); 
  switch (rtDWork.is_durationNoChange)
  {
   case IN_NoChange_1min:
    {
      // During 'NoChange_1min': '<S7>:613'
      // '<S7>:1741:1' sf_internal_predicateOutput = l_changed;
      if (l_changed)
      {
        // Transition: '<S7>:1741'
        rtDWork.is_durationNoChange = IN_reset_f;
        rtDWork.temporalCounter_i5 = 0U;

        // Entry 'reset': '<S7>:599'
      }
      else
      {
        real_T tmp;
        uint32_T qY;

        // '<S7>:1307:1' sf_internal_predicateOutput = ~l_changed && ...
        // '<S7>:1307:2' after(l_exitDelayDiffer * in_timeStepScaleFactor, sec); 
        qY = rtDWork.BusConversion_InsertedFor_Mai_d.ActiveExitDelay -
          /*MW:OvSatOk*/
          rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay;
        if (qY > rtDWork.BusConversion_InsertedFor_Mai_d.ActiveExitDelay)
        {
          qY = 0U;
        }

        // Inport: '<Root>/In_timeStepScaleFactor'
        tmp = rt_roundd(static_cast<real_T>(qY) * rtU.In_timeStepScaleFactor);
        if (tmp < 4.294967296E+9)
        {
          if (tmp >= 0.0)
          {
            qY = static_cast<uint32_T>(tmp);
          }
          else
          {
            qY = 0U;
          }
        }
        else
        {
          qY = MAX_uint32_T;
        }

        if (rtDWork.temporalCounter_i5 >= static_cast<uint32_T>
            (static_cast<int32_T>(static_cast<int32_T>(qY) * 100)))
        {
          // Transition: '<S7>:1307'
          rtDWork.is_durationNoChange = IN_NoChange_3min;
        }
      }
    }
    break;

   case IN_NoChange_3min:
    // During 'NoChange_3min': '<S7>:1306'
    // '<S7>:1308:1' sf_internal_predicateOutput = l_changed;
    if (l_changed)
    {
      // Transition: '<S7>:1308'
      rtDWork.is_durationNoChange = IN_reset_f;
      rtDWork.temporalCounter_i5 = 0U;

      // Entry 'reset': '<S7>:599'
    }
    break;

   default:
    {
      // During 'reset': '<S7>:599'
      // '<S7>:621:1' sf_internal_predicateOutput = l_changed;
      if (l_changed)
      {
        // Transition: '<S7>:621'
        rtDWork.is_durationNoChange = IN_reset_f;
        rtDWork.temporalCounter_i5 = 0U;

        // Entry 'reset': '<S7>:599'
      }
      else
      {
        real_T tmp;
        uint32_T qY;

        // Inport: '<Root>/In_timeStepScaleFactor'
        // '<S7>:2056:1' sf_internal_predicateOutput = ~l_changed && ...
        // '<S7>:2056:2' after(in_exitDelay.PassiveExitDelay * in_timeStepScaleFactor, sec); 
        tmp = rt_roundd(static_cast<real_T>
                        (rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay)
                        * rtU.In_timeStepScaleFactor);
        if (tmp < 4.294967296E+9)
        {
          if (tmp >= 0.0)
          {
            qY = static_cast<uint32_T>(tmp);
          }
          else
          {
            qY = 0U;
          }
        }
        else
        {
          qY = MAX_uint32_T;
        }

        if (rtDWork.temporalCounter_i5 >= static_cast<uint32_T>
            (static_cast<int32_T>(static_cast<int32_T>(qY) * 100)))
        {
          // Transition: '<S7>:2056'
          rtDWork.is_durationNoChange = IN_NoChange_1min;
          rtDWork.temporalCounter_i5 = 0U;
        }
      }
    }
    break;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_Unavailable(void)
{
  // Inport: '<Root>/In_FID_SVSEcuInternalStatus'
  // Entry Internal 'Unavailable': '<S7>:28'
  // Transition: '<S7>:1242'
  // '<S7>:2323:1' sf_internal_predicateOutput = ~in_FID_SVSEcuInternalStatus;
  if (!rtU.In_FID_SVSEcuInternalStatus)
  {
    // Transition: '<S7>:2323'
    rtDWork.is_Unavailable = IN_vidouterror;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    // Entry 'vidouterror': '<S7>:2325'
    // '<S7>:2325:3' out_SVSUnavlMsgs = ENotActiveReason.QNX_AVM_ERROR;
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

    // Inport: '<Root>/In_AVMError'
    // '<S7>:2329:1' sf_internal_predicateOutput = in_AVMError;
  }
  else if (rtU.In_AVMError)
  {
    // Transition: '<S7>:2329'
    rtDWork.is_Unavailable = IN_AVM_Error;

    // Entry 'AVM_Error': '<S7>:2306'
    // Entry Internal 'AVM_Error': '<S7>:2306'
    // Transition: '<S7>:2361'
    rtDWork.is_AVM_Error = IN_AVM_Error_No_Activate_Trigge;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    // Entry 'AVM_Error_No_Activate_Triggered': '<S7>:2315'
    // '<S7>:2315:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

    // '<S7>:2315:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
    rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;

    // Inport: '<Root>/In_PowerSaveMode'
    // '<S7>:2348:1' sf_internal_predicateOutput = in_PowerSaveMode;
  }
  else if (rtU.In_PowerSaveMode)
  {
    // Transition: '<S7>:2348'
    rtDWork.is_Unavailable = IN_Power_Save_Mode;

    // Entry 'Power_Save_Mode': '<S7>:2341'
    // Entry Internal 'Power_Save_Mode': '<S7>:2341'
    // Transition: '<S7>:2363'
    rtDWork.is_Power_Save_Mode = IN_Power_Save_Mode_No_Activate_;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    // Entry 'Power_Save_Mode_No_Activate_Triggered': '<S7>:2345'
    // '<S7>:2345:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

    // '<S7>:2345:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
    rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
  }
  else
  {
    // Transition: '<S7>:1246'
    rtDWork.is_Unavailable = IN_spdtoohigh;

    // Entry 'spdtoohigh': '<S7>:1244'
    // Entry Internal 'spdtoohigh': '<S7>:1244'
    // Transition: '<S7>:2352'
    rtDWork.is_spdtoohigh = IN_SPD_No_Activate_Triggered;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    // Entry 'SPD_No_Activate_Triggered': '<S7>:2300'
    // '<S7>:2300:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

    // '<S7>:2300:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
    rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;

    // End of Inport: '<Root>/In_PowerSaveMode'
    // End of Inport: '<Root>/In_AVMError'
  }

  // End of Inport: '<Root>/In_FID_SVSEcuInternalStatus'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::exit_internal_Unavailable(void)
{
  // Exit Internal 'Unavailable': '<S7>:28'
  // Exit Internal 'AVM_Error': '<S7>:2306'
  rtDWork.is_AVM_Error = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'Power_Save_Mode': '<S7>:2341'
  rtDWork.is_Power_Save_Mode = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'spdtoohigh': '<S7>:1244'
  rtDWork.is_spdtoohigh = IN_NO_ACTIVE_CHILD;
  rtDWork.is_Unavailable = IN_NO_ACTIVE_CHILD;
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_RR(void)
{
  // During 'temp_RR': '<S7>:1378'
  // '<S7>:1379:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1379'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersRearRightView': '<S7>:68'
    // '<S7>:68:3' out_displayedView = EScreenID.PERSPECTIVE_RR;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_RR;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:68:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_RR;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_RR;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_Park_View(void)
{
  // Inport: '<Root>/In_gear'
  // Entry Internal 'Park_View': '<S7>:2637'
  // Transition: '<S7>:2641'
  // '<S7>:2211:1' sf_internal_predicateOutput = in_gear==EGear.R;
  if (static_cast<uint32_T>(rtU.In_gear) == EGear_R)
  {
    // Transition: '<S7>:2211'
    rtDWork.is_Park_View = IN_Park_RearView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Park_RearView': '<S7>:1358'
    // '<S7>:1358:3' out_displayedView = EScreenID.FLOAT_PARKING_REAR_VIEW;
    rtY.Out_displayedView = EScreenID_FLOAT_PARKING_REAR_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1358:4' out_SVSCurrentView = EScreenID.FLOAT_PARKING_REAR_VIEW;
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_PARKING_REAR_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:1358:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:1358:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
    rtY.Out_ViewModeGroup = VIEWMODE_REAR;
  }
  else
  {
    // Transition: '<S7>:2215'
    // Transition: '<S7>:2220'
    rtDWork.is_Park_View = IN_Park_FrontView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Park_FrontView': '<S7>:1357'
    // '<S7>:1357:3' out_displayedView = EScreenID.FLOAT_PARKING_FRONT_VIEW;
    rtY.Out_displayedView = EScreenID_FLOAT_PARKING_FRONT_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1357:4' out_SVSCurrentView = EScreenID.FLOAT_PARKING_FRONT_VIEW;
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_PARKING_FRONT_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:1357:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:1357:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
    rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
  }

  // End of Inport: '<Root>/In_gear'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_ParkingPlan_Floa
  (void)
{
  // Inport: '<Root>/In_gear'
  // Entry Internal 'ParkingPlan_FloatView': '<S7>:2558'
  // Transition: '<S7>:2616'
  // '<S7>:2629:1' sf_internal_predicateOutput = in_gear==EGear.R;
  if (static_cast<uint32_T>(rtU.In_gear) == EGear_R)
  {
    // Transition: '<S7>:2629'
    rtDWork.is_ParkingPlan_FloatView = IN_ParkingRearPlan_FloatView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'ParkingRearPlan_FloatView': '<S7>:2619'
    // '<S7>:2619:3' out_displayedView = EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW; 
    rtY.Out_displayedView = EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:2619:4' out_SVSCurrentView = EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW; 
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:2619:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:2619:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
    rtY.Out_ViewModeGroup = VIEWMODE_REAR;
  }
  else
  {
    // Transition: '<S7>:2628'
    rtDWork.is_ParkingPlan_FloatView = IN_ParkingFrontPlan_FloatView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'ParkingFrontPlan_FloatView': '<S7>:2615'
    // '<S7>:2615:3' out_displayedView = EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW; 
    rtY.Out_displayedView = EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:2615:4' out_SVSCurrentView = EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW; 
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:2615:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:2615:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
    rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
  }

  // End of Inport: '<Root>/In_gear'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_FloatingRearView
  (void)
{
  // Inport: '<Root>/In_FloatViewChange'
  // Entry Internal 'FloatingRearViews_NotPark': '<S7>:2581'
  // Transition: '<S7>:2583'
  // '<S7>:2631:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.FRView; 
  if (static_cast<uint32_T>(rtU.In_FloatViewChange) == EFloatViewType_FRView)
  {
    // Transition: '<S7>:2631'
    rtDWork.is_FloatingRearViews_NotPark = IN_Rear_FloatView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Rear_FloatView': '<S7>:2585'
    // '<S7>:2585:3' out_displayedView = EScreenID.FLOAT_REAR_VIEW;
    rtY.Out_displayedView = EScreenID_FLOAT_REAR_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:2585:4' out_SVSCurrentView = EScreenID.FLOAT_REAR_VIEW;
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_REAR_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:2585:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:2585:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
    rtY.Out_ViewModeGroup = VIEWMODE_REAR;
  }
  else
  {
    // Transition: '<S7>:2632'
    rtDWork.is_FloatingRearViews_NotPark = IN_RearPlan_FloatView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'RearPlan_FloatView': '<S7>:2584'
    // '<S7>:2584:3' out_displayedView = EScreenID.FLOAT_REAR_PLAN_VIEW;
    rtY.Out_displayedView = EScreenID_FLOAT_REAR_PLAN_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:2584:4' out_SVSCurrentView = EScreenID.FLOAT_REAR_PLAN_VIEW;
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_REAR_PLAN_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:2584:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:2584:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
    rtY.Out_ViewModeGroup = VIEWMODE_REAR;
  }

  // End of Inport: '<Root>/In_FloatViewChange'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_FloatingFrontVie
  (void)
{
  // Inport: '<Root>/In_FloatViewChange'
  // Entry Internal 'FloatingFrontViews_NotPark': '<S7>:1067'
  // Transition: '<S7>:2633'
  // '<S7>:2636:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.FRView&&in_SVSShowReq == true; 
  if ((static_cast<uint32_T>(rtU.In_FloatViewChange) == EFloatViewType_FRView) &&
      rtDWork.UnitDelay8)
  {
    // Transition: '<S7>:2636'
    rtDWork.is_FloatingFrontViews_NotPark = IN_Front_FloatView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Front_FloatView': '<S7>:2553'
    // '<S7>:2553:3' out_displayedView = EScreenID.FLOAT_FRONT_VIEW;
    rtY.Out_displayedView = EScreenID_FLOAT_FRONT_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:2553:4' out_SVSCurrentView = EScreenID.FLOAT_FRONT_VIEW;
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_FRONT_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:2553:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:2553:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
    rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
  }
  else
  {
    // Transition: '<S7>:2634'
    rtDWork.is_FloatingFrontViews_NotPark = IN_FrontPlan_FloatView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'FrontPlan_FloatView': '<S7>:1068'
    // '<S7>:1068:3' out_displayedView = EScreenID.FLOAT_FRONT_PLAN_VIEW;
    rtY.Out_displayedView = EScreenID_FLOAT_FRONT_PLAN_VIEW;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1068:4' out_SVSCurrentView = EScreenID.FLOAT_FRONT_PLAN_VIEW;
    rtY.Out_SVSCurrentView = EScreenID_FLOAT_FRONT_PLAN_VIEW;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:1068:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:1068:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
    rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
  }

  // End of Inport: '<Root>/In_FloatViewChange'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::FloatingView(void)
{
  // During 'FloatingView': '<S7>:1206'
  switch (rtDWork.is_FloatingView)
  {
   case IN_Enlarge_delay:
    // During 'Enlarge_delay': '<S7>:2653'
    // '<S7>:2656:1' sf_internal_predicateOutput = after(2,tick);
    if (static_cast<int32_T>(rtDWork.temporalCounter_i1_b) >= 2)
    {
      // Transition: '<S7>:2656'
      // '<S7>:2657:1' sf_internal_predicateOutput = (in_displayedVewL == EScreenID.FLOAT_REAR_PLAN_VIEW||... 
      // '<S7>:2657:2' in_displayedVewL == EScreenID.FLOAT_REAR_VIEW);
      if ((rtDWork.UnitDelay5 == EScreenID_FLOAT_REAR_PLAN_VIEW) ||
          (rtDWork.UnitDelay5 == EScreenID_FLOAT_REAR_VIEW))
      {
        // Transition: '<S7>:2657'
        rtDWork.is_FloatingView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Available = IN_FullScreenViews_From_FloatSc;
        rtDWork.is_FullScreenViews_From_FloatSc = IN_RearView_From_FloatRearView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'RearView_From_FloatRearView': '<S7>:2407'
        // '<S7>:2407:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:2407:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:2407:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:2407:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;

        // '<S7>:2658:1' sf_internal_predicateOutput = (in_displayedVewL == EScreenID.FLOAT_FRONT_PLAN_VIEW||... 
        // '<S7>:2658:2' in_displayedVewL == EScreenID.FLOAT_FRONT_VIEW);
      }
      else if ((rtDWork.UnitDelay5 == EScreenID_FLOAT_FRONT_PLAN_VIEW) ||
               (rtDWork.UnitDelay5 == EScreenID_FLOAT_FRONT_VIEW))
      {
        // Transition: '<S7>:2658'
        rtDWork.is_FloatingView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Available = IN_FullScreenViews_From_FloatSc;
        rtDWork.is_FullScreenViews_From_FloatSc =
          IN_FrontView_From_FloatFrontVie;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'FrontView_From_FloatFrontView': '<S7>:2408'
        // '<S7>:2408:4' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:2408:5' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:2408:6' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:2408:7' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
      }
      else
      {
        // no actions
      }
    }
    break;

   case IN_FloatingFrontViews_NotPark:
    {
      EGear tmp;
      EGear tmp_1;

      // During 'FloatingFrontViews_NotPark': '<S7>:1067'
      // '<S7>:2607:1' sf_internal_predicateOutput = hasChangedTo(in_gear,EGear.R)&&~in_ParkingSearch; 
      tmp_1 = rtDWork.in_gear_start;
      tmp = rtDWork.in_gear_prev;

      // Inport: '<Root>/In_ParkingSearch'
      if ((tmp != tmp_1) && (static_cast<uint32_T>(tmp_1) == EGear_R) &&
          (!rtU.In_ParkingSearch))
      {
        // Transition: '<S7>:2607'
        // Exit Internal 'FloatingFrontViews_NotPark': '<S7>:1067'
        rtDWork.is_FloatingFrontViews_NotPark = IN_NO_ACTIVE_CHILD;
        rtDWork.is_FloatingView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Available = IN_FullScreen_R;

        // Entry 'FullScreen_R': '<S7>:1220'
        rtDWork.is_FullScreen_R = IN_GearR_RearView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'GearR_RearView': '<S7>:2399'
        // '<S7>:2399:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:2399:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:2399:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:2399:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;

        // '<S7>:2670:1' sf_internal_predicateOutput = hasChangedTo(in_gear,EGear.R)&&in_ParkingSearch; 
      }
      else if ((tmp != tmp_1) && (static_cast<uint32_T>(tmp_1) == EGear_R) &&
               rtU.In_ParkingSearch)
      {
        // Transition: '<S7>:2670'
        // Exit Internal 'FloatingFrontViews_NotPark': '<S7>:1067'
        rtDWork.is_FloatingFrontViews_NotPark = IN_NO_ACTIVE_CHILD;
        rtDWork.is_FloatingView = IN_FloatingRearViews_NotPark;
        rtDWork.temporalCounter_i2 = 0U;

        // Entry 'FloatingRearViews_NotPark': '<S7>:2581'
        enter_internal_FloatingRearView();
      }
      else if (static_cast<uint32_T>(rtDWork.is_FloatingFrontViews_NotPark) ==
               IN_FrontPlan_FloatView)
      {
        // Update for Outport: '<Root>/Out_ViewModeGroup'
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

        // Inport: '<Root>/In_FloatViewChange'
        // During 'FrontPlan_FloatView': '<S7>:1068'
        // '<S7>:2555:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.FRView; 
        if (static_cast<uint32_T>(rtU.In_FloatViewChange) ==
            EFloatViewType_FRView)
        {
          // Transition: '<S7>:2555'
          rtDWork.is_FloatingFrontViews_NotPark = IN_Front_FloatView;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Front_FloatView': '<S7>:2553'
          // '<S7>:2553:3' out_displayedView = EScreenID.FLOAT_FRONT_VIEW;
          rtY.Out_displayedView = EScreenID_FLOAT_FRONT_VIEW;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:2553:4' out_SVSCurrentView = EScreenID.FLOAT_FRONT_VIEW;
          rtY.Out_SVSCurrentView = EScreenID_FLOAT_FRONT_VIEW;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:2553:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:2553:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        }
      }
      else
      {
        // Update for Outport: '<Root>/Out_ViewModeGroup'
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

        // Inport: '<Root>/In_FloatViewChange'
        // During 'Front_FloatView': '<S7>:2553'
        // '<S7>:2557:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.PlanView; 
        if (static_cast<uint32_T>(rtU.In_FloatViewChange) ==
            EFloatViewType_PlanView)
        {
          // Transition: '<S7>:2557'
          rtDWork.is_FloatingFrontViews_NotPark = IN_FrontPlan_FloatView;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'FrontPlan_FloatView': '<S7>:1068'
          // '<S7>:1068:3' out_displayedView = EScreenID.FLOAT_FRONT_PLAN_VIEW;
          rtY.Out_displayedView = EScreenID_FLOAT_FRONT_PLAN_VIEW;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:1068:4' out_SVSCurrentView = EScreenID.FLOAT_FRONT_PLAN_VIEW; 
          rtY.Out_SVSCurrentView = EScreenID_FLOAT_FRONT_PLAN_VIEW;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:1068:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:1068:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        }
      }
    }
    break;

   case IN_FloatingRearViews_NotPark:
    {
      // Inport: '<Root>/In_ParkingSearch' incorporates:
      //   Inport: '<Root>/In_gear'
      //   Inport: '<Root>/In_timeStepScaleFactor'

      // During 'FloatingRearViews_NotPark': '<S7>:2581'
      // '<S7>:2669:1' sf_internal_predicateOutput = ~in_ParkingSearch&&...
      // '<S7>:2669:2' in_gear==EGear.R&&...
      // '<S7>:2669:3' after(2*in_timeStepScaleFactor, sec);
      if ((!rtU.In_ParkingSearch) && (static_cast<uint32_T>(rtU.In_gear) ==
           EGear_R) && (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::
            ceil(2.0 * rtU.In_timeStepScaleFactor * 100.0))))
      {
        // Transition: '<S7>:2669'
        // Exit Internal 'FloatingRearViews_NotPark': '<S7>:2581'
        rtDWork.is_FloatingRearViews_NotPark = IN_NO_ACTIVE_CHILD;
        rtDWork.is_FloatingView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Available = IN_FullScreen_R;

        // Entry 'FullScreen_R': '<S7>:1220'
        rtDWork.is_FullScreen_R = IN_GearR_RearView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'GearR_RearView': '<S7>:2399'
        // '<S7>:2399:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:2399:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:2399:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:2399:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
      }
      else
      {
        EGear tmp_1;

        // '<S7>:2606:1' sf_internal_predicateOutput = hasChangedTo(in_gear,EGear.D); 
        tmp_1 = rtDWork.in_gear_start;
        if ((rtDWork.in_gear_prev != tmp_1) && (static_cast<uint32_T>(tmp_1) ==
             EGear_D))
        {
          // Transition: '<S7>:2606'
          // Exit Internal 'FloatingRearViews_NotPark': '<S7>:2581'
          rtDWork.is_FloatingRearViews_NotPark = IN_NO_ACTIVE_CHILD;
          rtDWork.is_FloatingView = IN_FloatingFrontViews_NotPark;

          // Entry 'FloatingFrontViews_NotPark': '<S7>:1067'
          enter_internal_FloatingFrontVie();
        }
        else if (static_cast<uint32_T>(rtDWork.is_FloatingRearViews_NotPark) ==
                 IN_RearPlan_FloatView)
        {
          // Update for Outport: '<Root>/Out_ViewModeGroup'
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;

          // Inport: '<Root>/In_FloatViewChange'
          // During 'RearPlan_FloatView': '<S7>:2584'
          // '<S7>:2586:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.FRView; 
          if (static_cast<uint32_T>(rtU.In_FloatViewChange) ==
              EFloatViewType_FRView)
          {
            // Transition: '<S7>:2586'
            rtDWork.is_FloatingRearViews_NotPark = IN_Rear_FloatView;

            // Outport: '<Root>/Out_displayedView'
            // Entry 'Rear_FloatView': '<S7>:2585'
            // '<S7>:2585:3' out_displayedView = EScreenID.FLOAT_REAR_VIEW;
            rtY.Out_displayedView = EScreenID_FLOAT_REAR_VIEW;

            // Update for Outport: '<Root>/Out_SVSCurrentView'
            // '<S7>:2585:4' out_SVSCurrentView = EScreenID.FLOAT_REAR_VIEW;
            rtY.Out_SVSCurrentView = EScreenID_FLOAT_REAR_VIEW;

            // Outport: '<Root>/Out_SVSViewModeSts'
            // '<S7>:2585:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
            rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

            // Update for Outport: '<Root>/Out_ViewModeGroup'
            // '<S7>:2585:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
            rtY.Out_ViewModeGroup = VIEWMODE_REAR;
          }
        }
        else
        {
          // Update for Outport: '<Root>/Out_ViewModeGroup'
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;

          // Inport: '<Root>/In_FloatViewChange'
          // During 'Rear_FloatView': '<S7>:2585'
          // '<S7>:2587:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.PlanView; 
          if (static_cast<uint32_T>(rtU.In_FloatViewChange) ==
              EFloatViewType_PlanView)
          {
            // Transition: '<S7>:2587'
            rtDWork.is_FloatingRearViews_NotPark = IN_RearPlan_FloatView;

            // Outport: '<Root>/Out_displayedView'
            // Entry 'RearPlan_FloatView': '<S7>:2584'
            // '<S7>:2584:3' out_displayedView = EScreenID.FLOAT_REAR_PLAN_VIEW; 
            rtY.Out_displayedView = EScreenID_FLOAT_REAR_PLAN_VIEW;

            // Update for Outport: '<Root>/Out_SVSCurrentView'
            // '<S7>:2584:4' out_SVSCurrentView = EScreenID.FLOAT_REAR_PLAN_VIEW; 
            rtY.Out_SVSCurrentView = EScreenID_FLOAT_REAR_PLAN_VIEW;

            // Outport: '<Root>/Out_SVSViewModeSts'
            // '<S7>:2584:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
            rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

            // Update for Outport: '<Root>/Out_ViewModeGroup'
            // '<S7>:2584:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
            rtY.Out_ViewModeGroup = VIEWMODE_REAR;
          }
        }
      }
    }
    break;

   default:
    {
      EGear tmp_1;

      // Inport: '<Root>/In_gear'
      // During 'FloatingViews_Park': '<S7>:1346'
      // '<S7>:2662:1' sf_internal_predicateOutput = hasChangedTo(in_parkingsts, false)&&in_gear == EGear.R; 
      tmp_1 = rtU.In_gear;
      if ((rtDWork.in_parkingsts_prev != rtDWork.in_parkingsts_start) &&
          (!rtDWork.in_parkingsts_start) && (static_cast<uint32_T>(tmp_1) ==
           EGear_R))
      {
        // Transition: '<S7>:2662'
        // Exit Internal 'FloatingViews_Park': '<S7>:1346'
        // Exit Internal 'Park_View': '<S7>:2637'
        rtDWork.is_Park_View = IN_NO_ACTIVE_CHILD;

        // Exit Internal 'ParkingPlan_FloatView': '<S7>:2558'
        rtDWork.is_ParkingPlan_FloatView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_FloatingViews_Park = IN_NO_ACTIVE_CHILD;
        rtDWork.is_FloatingView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Available = IN_FullScreen_R;

        // Entry 'FullScreen_R': '<S7>:1220'
        rtDWork.is_FullScreen_R = IN_GearR_RearView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'GearR_RearView': '<S7>:2399'
        // '<S7>:2399:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:2399:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:2399:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:2399:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
      }
      else if (static_cast<uint32_T>(rtDWork.is_FloatingViews_Park) ==
               IN_Park_View)
      {
        EFloatViewType tmp_0;

        // During 'Park_View': '<S7>:2637'
        // '<S7>:2566:1' sf_internal_predicateOutput = [hasChangedTo(in_FloatViewChange,EFloatViewType.PlanView)]; 
        tmp_0 = rtDWork.in_FloatViewChange_start;
        if ((rtDWork.in_FloatViewChange_prev != tmp_0) && (static_cast<uint32_T>
             (tmp_0) == EFloatViewType_PlanView))
        {
          // Transition: '<S7>:2566'
          // Exit Internal 'Park_View': '<S7>:2637'
          rtDWork.is_Park_View = IN_NO_ACTIVE_CHILD;
          rtDWork.is_FloatingViews_Park = IN_ParkingPlan_FloatView;

          // Entry 'ParkingPlan_FloatView': '<S7>:2558'
          enter_internal_ParkingPlan_Floa();
        }
        else if (static_cast<uint32_T>(rtDWork.is_Park_View) ==
                 IN_Park_FrontView)
        {
          // Update for Outport: '<Root>/Out_ViewModeGroup'
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

          // During 'Park_FrontView': '<S7>:1357'
          // '<S7>:2613:1' sf_internal_predicateOutput = in_gear==EGear.R;
          if (static_cast<uint32_T>(tmp_1) == EGear_R)
          {
            // Transition: '<S7>:2613'
            rtDWork.is_Park_View = IN_Park_RearView;

            // Outport: '<Root>/Out_displayedView'
            // Entry 'Park_RearView': '<S7>:1358'
            // '<S7>:1358:3' out_displayedView = EScreenID.FLOAT_PARKING_REAR_VIEW; 
            rtY.Out_displayedView = EScreenID_FLOAT_PARKING_REAR_VIEW;

            // Update for Outport: '<Root>/Out_SVSCurrentView'
            // '<S7>:1358:4' out_SVSCurrentView = EScreenID.FLOAT_PARKING_REAR_VIEW; 
            rtY.Out_SVSCurrentView = EScreenID_FLOAT_PARKING_REAR_VIEW;

            // Outport: '<Root>/Out_SVSViewModeSts'
            // '<S7>:1358:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
            rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

            // Update for Outport: '<Root>/Out_ViewModeGroup'
            // '<S7>:1358:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
            rtY.Out_ViewModeGroup = VIEWMODE_REAR;
          }
        }
        else
        {
          // Update for Outport: '<Root>/Out_ViewModeGroup'
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;

          // During 'Park_RearView': '<S7>:1358'
          // '<S7>:2614:1' sf_internal_predicateOutput = in_gear==EGear.D;
          if (static_cast<uint32_T>(tmp_1) == EGear_D)
          {
            // Transition: '<S7>:2614'
            rtDWork.is_Park_View = IN_Park_FrontView;

            // Outport: '<Root>/Out_displayedView'
            // Entry 'Park_FrontView': '<S7>:1357'
            // '<S7>:1357:3' out_displayedView = EScreenID.FLOAT_PARKING_FRONT_VIEW; 
            rtY.Out_displayedView = EScreenID_FLOAT_PARKING_FRONT_VIEW;

            // Update for Outport: '<Root>/Out_SVSCurrentView'
            // '<S7>:1357:4' out_SVSCurrentView = EScreenID.FLOAT_PARKING_FRONT_VIEW; 
            rtY.Out_SVSCurrentView = EScreenID_FLOAT_PARKING_FRONT_VIEW;

            // Outport: '<Root>/Out_SVSViewModeSts'
            // '<S7>:1357:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
            rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

            // Update for Outport: '<Root>/Out_ViewModeGroup'
            // '<S7>:1357:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
            rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
          }
        }
      }
      else
      {
        EFloatViewType tmp_0;

        // During 'ParkingPlan_FloatView': '<S7>:2558'
        // '<S7>:2559:1' sf_internal_predicateOutput = hasChangedTo(in_FloatViewChange,EFloatViewType.FRView); 
        tmp_0 = rtDWork.in_FloatViewChange_start;
        if ((rtDWork.in_FloatViewChange_prev != tmp_0) && (static_cast<uint32_T>
             (tmp_0) == EFloatViewType_FRView))
        {
          // Transition: '<S7>:2559'
          // Exit Internal 'ParkingPlan_FloatView': '<S7>:2558'
          rtDWork.is_ParkingPlan_FloatView = IN_NO_ACTIVE_CHILD;
          rtDWork.is_FloatingViews_Park = IN_Park_View;

          // Entry 'Park_View': '<S7>:2637'
          enter_internal_Park_View();
        }
        else if (static_cast<uint32_T>(rtDWork.is_ParkingPlan_FloatView) ==
                 IN_ParkingFrontPlan_FloatView)
        {
          // Update for Outport: '<Root>/Out_ViewModeGroup'
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

          // During 'ParkingFrontPlan_FloatView': '<S7>:2615'
          // '<S7>:2623:1' sf_internal_predicateOutput = in_gear==EGear.R;
          if (static_cast<uint32_T>(tmp_1) == EGear_R)
          {
            // Transition: '<S7>:2623'
            rtDWork.is_ParkingPlan_FloatView = IN_ParkingRearPlan_FloatView;

            // Outport: '<Root>/Out_displayedView'
            // Entry 'ParkingRearPlan_FloatView': '<S7>:2619'
            // '<S7>:2619:3' out_displayedView = EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW; 
            rtY.Out_displayedView = EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW;

            // Update for Outport: '<Root>/Out_SVSCurrentView'
            // '<S7>:2619:4' out_SVSCurrentView = EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW; 
            rtY.Out_SVSCurrentView = EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW;

            // Outport: '<Root>/Out_SVSViewModeSts'
            // '<S7>:2619:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
            rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

            // Update for Outport: '<Root>/Out_ViewModeGroup'
            // '<S7>:2619:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
            rtY.Out_ViewModeGroup = VIEWMODE_REAR;
          }
        }
        else
        {
          // Update for Outport: '<Root>/Out_ViewModeGroup'
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;

          // During 'ParkingRearPlan_FloatView': '<S7>:2619'
          // '<S7>:2624:1' sf_internal_predicateOutput = in_gear==EGear.D;
          if (static_cast<uint32_T>(tmp_1) == EGear_D)
          {
            // Transition: '<S7>:2624'
            rtDWork.is_ParkingPlan_FloatView = IN_ParkingFrontPlan_FloatView;

            // Outport: '<Root>/Out_displayedView'
            // Entry 'ParkingFrontPlan_FloatView': '<S7>:2615'
            // '<S7>:2615:3' out_displayedView = EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW; 
            rtY.Out_displayedView = EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW;

            // Update for Outport: '<Root>/Out_SVSCurrentView'
            // '<S7>:2615:4' out_SVSCurrentView = EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW; 
            rtY.Out_SVSCurrentView = EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW;

            // Outport: '<Root>/Out_SVSViewModeSts'
            // '<S7>:2615:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
            rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

            // Update for Outport: '<Root>/Out_ViewModeGroup'
            // '<S7>:2615:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
            rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
          }
        }
      }
    }
    break;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::ManualChange(void)
{
  // During 'ManualChange': '<S7>:38'
  switch (rtDWork.is_ManualChange)
  {
   case IN_ECAL_CPC:
    // During 'ECAL_CPC': '<S7>:1073'
    break;

   case IN_VIewGroup_PersViews:
    // During 'VIewGroup_PersViews': '<S7>:62'
    break;

   case IN_ViewGroup_STBView:
    // During 'ViewGroup_STBView': '<S7>:1252'
    // During 'Manual_BonnetView': '<S7>:1150'
    break;

   case IN_ViewGroup_SignleView:
    // During 'ViewGroup_SignleView': '<S7>:39'
    if (static_cast<uint32_T>(rtDWork.is_ViewGroup_SignleView) ==
        IN_Manual_FrontView)
    {
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Manual_FrontView': '<S7>:681'
    }
    else
    {
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Manual_RearView': '<S7>:670'
    }
    break;

   case IN_ViewGroup_WheelView:
    // During 'ViewGroup_WheelView': '<S7>:1250'
    if (static_cast<uint32_T>(rtDWork.is_ViewGroup_WheelView) ==
        IN_Manual_FrontWheelView)
    {
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Manual_FrontWheelView': '<S7>:53'
    }
    else
    {
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Manual_RearWheelView': '<S7>:51'
    }
    break;

   case IN_ViewGroup_WideView:
    // During 'ViewGroup_WideView': '<S7>:1251'
    if (static_cast<uint32_T>(rtDWork.is_ViewGroup_WideView) ==
        IN_Manual_FrontWideView)
    {
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Manual_FrontWideView': '<S7>:1213'
    }
    else
    {
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Manual_RearWideView': '<S7>:1214'
    }
    break;

   default:
    // During 'ViewModeChanged': '<S7>:1258'
    switch (rtDWork.is_ViewModeChanged)
    {
     case IN_LastViewModeGroup:
      // During 'LastViewModeGroup': '<S7>:1992'
      if (static_cast<uint32_T>(rtDWork.is_LastViewModeGroup) ==
          IN_ViewMode_PreFrontGroup)
      {
        // During 'ViewMode_PreFrontGroup': '<S7>:1998'
        // Transition: '<S7>:2005'
        rtDWork.is_LastViewModeGroup = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ViewModeChanged = IN_ViewMode_FrontGroup;

        // Entry 'ViewMode_FrontGroup': '<S7>:1263'
      }
      else
      {
        // During 'ViewMode_RearGroup': '<S7>:1999'
        // Transition: '<S7>:2006'
        rtDWork.is_LastViewModeGroup = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ViewModeChanged = IN_ViewMode_RearGroup;

        // Entry 'ViewMode_RearGroup': '<S7>:1264'
      }
      break;

     case IN_ViewMode_FrontGroup:
      // Inport: '<Root>/In_HUSVSMode'
      // During 'ViewMode_FrontGroup': '<S7>:1263'
      // Transition: '<S7>:1536'
      // '<S7>:1539:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Wheel; 
      switch (rtU.In_HUSVSMode)
      {
       case ESVSViewMode_VM_Wheel:
        // Transition: '<S7>:1539'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_WheelView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_WheelView': '<S7>:1250'
        // '<S7>:1250:3' out_SVSViewModeSts = ESVSViewMode.VM_Wheel;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wheel;
        rtDWork.is_ViewGroup_WheelView = IN_Manual_FrontWheelView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_FrontWheelView': '<S7>:53'
        // '<S7>:53:3' out_displayedView = EScreenID.WHEEL_FRONT_DUAL;
        rtY.Out_displayedView = EScreenID_WHEEL_FRONT_DUAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:53:4' out_SVSCurrentView = EScreenID.WHEEL_FRONT_DUAL;
        rtY.Out_SVSCurrentView = EScreenID_WHEEL_FRONT_DUAL;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:53:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        break;

       case ESVSViewMode_VM_Standard:
        // Transition: '<S7>:1538'
        // '<S7>:1537:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Standard; 
        // Transition: '<S7>:1537'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_SignleView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_SignleView': '<S7>:39'
        // '<S7>:39:3' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;
        rtDWork.is_ViewGroup_SignleView = IN_Manual_FrontView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_FrontView': '<S7>:681'
        // '<S7>:681:3' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:681:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:681:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        break;

       case ESVSViewMode_VM_Wide:
        // Transition: '<S7>:1540'
        // '<S7>:1541:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Wide; 
        // Transition: '<S7>:1541'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_WideView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_WideView': '<S7>:1251'
        // '<S7>:1251:3' out_SVSViewModeSts = ESVSViewMode.VM_Wide;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wide;
        rtDWork.is_ViewGroup_WideView = IN_Manual_FrontWideView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_FrontWideView': '<S7>:1213'
        // '<S7>:1213:3' out_displayedView = EScreenID.SINGLE_FRONT_JUNCTION;
        rtY.Out_displayedView = EScreenID_SINGLE_FRONT_JUNCTION;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1213:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_JUNCTION;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_JUNCTION;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1213:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        break;

       case ESVSViewMode_VM_STB:
        // Transition: '<S7>:1542'
        // '<S7>:1543:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_STB; 
        // Transition: '<S7>:1543'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_STBView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_STBView': '<S7>:1252'
        // '<S7>:1252:3' out_SVSViewModeSts = ESVSViewMode.VM_STB;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_STB;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_BonnetView': '<S7>:1150'
        // '<S7>:1150:3' out_displayedView = EScreenID.SINGLE_STB;
        rtY.Out_displayedView = EScreenID_SINGLE_STB;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1150:4' out_SVSCurrentView = EScreenID.SINGLE_STB;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_STB;
        break;

       case ESVSViewMode_VM_Perspective:
        // '<S7>:1544:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Perspective; 
        // Transition: '<S7>:1544'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'VIewGroup_PersViews': '<S7>:62'
        // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_PersRearView': '<S7>:1011'
        // '<S7>:1011:3' out_displayedView = EScreenID.PERSPECTIVE_PRE;
        rtY.Out_displayedView = EScreenID_PERSPECTIVE_PRE;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1011:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PRE;
        rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PRE;
        break;

       default:
        // no actions
        break;
      }
      break;

     default:
      // Inport: '<Root>/In_HUSVSMode'
      // During 'ViewMode_RearGroup': '<S7>:1264'
      // Transition: '<S7>:1545'
      // '<S7>:1548:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Wheel; 
      switch (rtU.In_HUSVSMode)
      {
       case ESVSViewMode_VM_Wheel:
        // Transition: '<S7>:1548'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_WheelView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_WheelView': '<S7>:1250'
        // '<S7>:1250:3' out_SVSViewModeSts = ESVSViewMode.VM_Wheel;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wheel;
        rtDWork.is_ViewGroup_WheelView = IN_Manual_RearWheelView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_RearWheelView': '<S7>:51'
        // '<S7>:51:3' out_displayedView = EScreenID.WHEEL_REAR_DUAL;
        rtY.Out_displayedView = EScreenID_WHEEL_REAR_DUAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:51:4' out_SVSCurrentView = EScreenID.WHEEL_REAR_DUAL;
        rtY.Out_SVSCurrentView = EScreenID_WHEEL_REAR_DUAL;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:51:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        break;

       case ESVSViewMode_VM_Standard:
        // Transition: '<S7>:2442'
        // '<S7>:1546:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Standard; 
        // Transition: '<S7>:1546'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_SignleView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_SignleView': '<S7>:39'
        // '<S7>:39:3' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;
        rtDWork.is_ViewGroup_SignleView = IN_Manual_RearView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_RearView': '<S7>:670'
        // '<S7>:670:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:670:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:670:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        break;

       case ESVSViewMode_VM_Wide:
        // Transition: '<S7>:1549'
        // '<S7>:1550:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Wide; 
        // Transition: '<S7>:1550'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_WideView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_WideView': '<S7>:1251'
        // '<S7>:1251:3' out_SVSViewModeSts = ESVSViewMode.VM_Wide;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wide;
        rtDWork.is_ViewGroup_WideView = IN_Manual_RearWideView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_RearWideView': '<S7>:1214'
        // '<S7>:1214:3' out_displayedView = EScreenID.SINGLE_REAR_JUNCTION;
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_JUNCTION;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1214:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_JUNCTION;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_JUNCTION;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1214:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        break;

       case ESVSViewMode_VM_STB:
        // Transition: '<S7>:1553'
        // '<S7>:1554:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_STB; 
        // Transition: '<S7>:1554'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_ViewGroup_STBView;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'ViewGroup_STBView': '<S7>:1252'
        // '<S7>:1252:3' out_SVSViewModeSts = ESVSViewMode.VM_STB;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_STB;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_BonnetView': '<S7>:1150'
        // '<S7>:1150:3' out_displayedView = EScreenID.SINGLE_STB;
        rtY.Out_displayedView = EScreenID_SINGLE_STB;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1150:4' out_SVSCurrentView = EScreenID.SINGLE_STB;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_STB;
        break;

       case ESVSViewMode_VM_Perspective:
        // '<S7>:1991:1' sf_internal_predicateOutput = in_HUSVSMode == ESVSViewMode.VM_Perspective; 
        // Transition: '<S7>:1991'
        rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // Entry 'VIewGroup_PersViews': '<S7>:62'
        // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Manual_PersFrontView': '<S7>:1006'
        // '<S7>:1006:3' out_displayedView = EScreenID.PERSPECTIVE_PFR;
        rtY.Out_displayedView = EScreenID_PERSPECTIVE_PFR;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1006:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PFR;
        rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PFR;
        break;

       default:
        // no actions
        break;
      }
      break;
    }
    break;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Voice_Views(void)
{
  // During 'Voice_Views': '<S7>:1395'
  switch (rtDWork.is_Voice_Views)
  {
   case IN_Voice_ViewChange_Failed_In_S:
    // During 'Voice_ViewChange_Failed_In_SR': '<S7>:2434'
    break;

   case IN_Voice_ViewChange_Failed_Out_:
    // During 'Voice_ViewChange_Failed_Out_SR': '<S7>:2437'
    break;

   default:
    // During 'Voice_View_Succeed': '<S7>:2432'
    switch (rtDWork.is_Voice_View_Succeed)
    {
     case IN_NoViewChangeByVoice:
      // During 'NoViewChangeByVoice': '<S7>:2244'
      break;

     case IN_Voice_3DView_GearR:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Voice_3DView_GearR': '<S7>:1410'
      break;

     case IN_Voice_3DView_NotGearR:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Voice_3DView_NotGearR': '<S7>:2516'
      break;

     case IN_Voice_FloatFrontView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Voice_FloatFrontView': '<S7>:2485'
      break;

     case IN_Voice_FloatRearView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Voice_FloatRearView': '<S7>:2492'
      break;

     case IN_Voice_FrontView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Voice_FrontView': '<S7>:1403'
      break;

     case IN_Voice_FrontWheelView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Voice_FrontWheelView': '<S7>:1407'
      break;

     case IN_Voice_FrontWideView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Voice_FrontWideView': '<S7>:1408'
      break;

     case IN_Voice_RearView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Voice_RearView': '<S7>:1411'
      break;

     case IN_Voice_RearWheelView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Voice_RearWheelView': '<S7>:1419'
      break;

     case IN_Voice_RearWideView:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // During 'Voice_RearWideView': '<S7>:1412'
      break;

     default:
      // Update for Outport: '<Root>/Out_ViewModeGroup'
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

      // During 'Voice_SKELETON': '<S7>:1409'
      break;
    }
    break;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_FL(void)
{
  // During 'temp_FL': '<S7>:1391'
  // '<S7>:1392:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1392'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersFrontLeftView': '<S7>:65'
    // '<S7>:65:3' out_displayedView = EScreenID.PERSPECTIVE_FL;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_FL;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:65:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_FL;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_FL;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_FR(void)
{
  // During 'temp_FR': '<S7>:1393'
  // '<S7>:1394:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1394'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersFrontRightView': '<S7>:70'
    // '<S7>:70:3' out_displayedView = EScreenID.PERSPECTIVE_FR;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_FR;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:70:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_FR;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_FR;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_PFR(void)
{
  // During 'temp_PFR': '<S7>:1386'
  // '<S7>:1387:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1387'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersFrontView': '<S7>:1006'
    // '<S7>:1006:3' out_displayedView = EScreenID.PERSPECTIVE_PFR;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_PFR;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1006:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PFR;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PFR;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_PLE(void)
{
  // During 'temp_PLE': '<S7>:1388'
  // '<S7>:1389:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1389'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersLeftView': '<S7>:1253'
    // '<S7>:1253:3' out_displayedView = EScreenID.PERSPECTIVE_PLE;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_PLE;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1253:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PLE;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PLE;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_PRE(void)
{
  // During 'temp_PRE': '<S7>:1384'
  // '<S7>:1385:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1385'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersRearView': '<S7>:1011'
    // '<S7>:1011:3' out_displayedView = EScreenID.PERSPECTIVE_PRE;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_PRE;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1011:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PRE;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PRE;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_PRI(void)
{
  // During 'temp_PRI': '<S7>:1382'
  // '<S7>:1383:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1383'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersRightView': '<S7>:1254'
    // '<S7>:1254:3' out_displayedView = EScreenID.PERSPECTIVE_PRI;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_PRI;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1254:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PRI;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PRI;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::temp_RL(void)
{
  // During 'temp_RL': '<S7>:1380'
  // '<S7>:1381:1' sf_internal_predicateOutput = EAnimationState.ANIM_ONGOING ~= in_animationState.state; 
  if (EAnimationState_ANIM_ONGOING !=
      rtDWork.BusConversion_InsertedFor_Mai_m.state)
  {
    // Transition: '<S7>:1381'
    rtDWork.is_Available = IN_ManualChange;

    // Entry 'ManualChange': '<S7>:38'
    rtDWork.is_ManualChange = IN_VIewGroup_PersViews;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // Entry 'VIewGroup_PersViews': '<S7>:62'
    // '<S7>:62:3' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'Manual_PersRearLeftView': '<S7>:67'
    // '<S7>:67:3' out_displayedView = EScreenID.PERSPECTIVE_RL;
    rtY.Out_displayedView = EScreenID_PERSPECTIVE_RL;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:67:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_RL;
    rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_RL;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::exit_internal_Available(void)
{
  // Exit Internal 'Available': '<S7>:30'
  // Exit Internal 'FloatingView': '<S7>:1206'
  // Exit Internal 'FloatingFrontViews_NotPark': '<S7>:1067'
  rtDWork.is_FloatingFrontViews_NotPark = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'FloatingRearViews_NotPark': '<S7>:2581'
  rtDWork.is_FloatingRearViews_NotPark = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'FloatingViews_Park': '<S7>:1346'
  // Exit Internal 'Park_View': '<S7>:2637'
  rtDWork.is_Park_View = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'ParkingPlan_FloatView': '<S7>:2558'
  rtDWork.is_ParkingPlan_FloatView = IN_NO_ACTIVE_CHILD;
  rtDWork.is_FloatingViews_Park = IN_NO_ACTIVE_CHILD;
  rtDWork.is_FloatingView = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'FullScreenViews_From_FloatScreenViews': '<S7>:2406'
  rtDWork.is_FullScreenViews_From_FloatSc = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'FullScreen_R': '<S7>:1220'
  rtDWork.is_FullScreen_R = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'ManualChange': '<S7>:38'
  // Exit Internal 'VIewGroup_PersViews': '<S7>:62'
  // Exit Internal 'ViewGroup_STBView': '<S7>:1252'
  // Exit Internal 'ViewGroup_SignleView': '<S7>:39'
  rtDWork.is_ViewGroup_SignleView = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'ViewGroup_WheelView': '<S7>:1250'
  rtDWork.is_ViewGroup_WheelView = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'ViewGroup_WideView': '<S7>:1251'
  rtDWork.is_ViewGroup_WideView = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'ViewModeChanged': '<S7>:1258'
  // Exit Internal 'LastViewModeGroup': '<S7>:1992'
  rtDWork.is_LastViewModeGroup = IN_NO_ACTIVE_CHILD;
  rtDWork.is_ViewModeChanged = IN_NO_ACTIVE_CHILD;
  rtDWork.is_ManualChange = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'NotRMode': '<S7>:1234'
  rtDWork.is_NotRMode = IN_NO_ACTIVE_CHILD;

  // Exit Internal 'Voice_Views': '<S7>:1395'
  // Exit Internal 'Voice_View_Succeed': '<S7>:2432'
  rtDWork.is_Voice_View_Succeed = IN_NO_ACTIVE_CHILD;
  rtDWork.is_Voice_Views = IN_NO_ACTIVE_CHILD;
  rtDWork.is_Available = IN_NO_ACTIVE_CHILD;
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_ViewModeChanged
  (void)
{
  EGear tmp;

  // Inport: '<Root>/In_gear'
  // Entry Internal 'ViewModeChanged': '<S7>:1258'
  // Transition: '<S7>:1271'
  // '<S7>:1995:1' sf_internal_predicateOutput = in_SVSViewPre == EScreenID.SINGLE_FRONT_NORMAL || ... 
  // '<S7>:1995:2'  in_SVSViewPre == EScreenID.WHEEL_FRONT_DUAL || ...
  // '<S7>:1995:3'  in_SVSViewPre == EScreenID.SINGLE_FRONT_JUNCTION || ...
  // '<S7>:1995:4'  in_gear == EGear.D;
  tmp = rtU.In_gear;
  if ((rtDWork.UnitDelay == EScreenID_SINGLE_FRONT_NORMAL) || (rtDWork.UnitDelay
       == EScreenID_WHEEL_FRONT_DUAL) || (rtDWork.UnitDelay ==
       EScreenID_SINGLE_FRONT_JUNCTION) || (static_cast<uint32_T>(tmp) ==
       EGear_D))
  {
    // Transition: '<S7>:1995'
    // Transition: '<S7>:1266'
    rtDWork.is_ViewModeChanged = IN_ViewMode_FrontGroup;

    // Entry 'ViewMode_FrontGroup': '<S7>:1263'

    // '<S7>:1996:1' sf_internal_predicateOutput = in_SVSViewPre == EScreenID.SINGLE_REAR_NORMAL_ON_ROAD || ... 
    // '<S7>:1996:2' in_SVSViewPre == EScreenID.WHEEL_REAR_DUAL || ...
    // '<S7>:1996:3' in_SVSViewPre == EScreenID.SINGLE_REAR_JUNCTION || ...
    // '<S7>:1996:4' in_gear == EGear.R;
  }
  else if ((rtDWork.UnitDelay == EScreenID_SINGLE_REAR_NORMAL_ON_ROAD) ||
           (rtDWork.UnitDelay == EScreenID_WHEEL_REAR_DUAL) ||
           (rtDWork.UnitDelay == EScreenID_SINGLE_REAR_JUNCTION) || (
            static_cast<uint32_T>(tmp) == EGear_R))
  {
    // Transition: '<S7>:1996'
    // Transition: '<S7>:1267'
    rtDWork.is_ViewModeChanged = IN_ViewMode_RearGroup;

    // Entry 'ViewMode_RearGroup': '<S7>:1264'
  }
  else
  {
    // Transition: '<S7>:1997'
    rtDWork.is_ViewModeChanged = IN_LastViewModeGroup;

    // Entry 'LastViewModeGroup': '<S7>:1992'
    // Entry Internal 'LastViewModeGroup': '<S7>:1992'
    // Transition: '<S7>:2000'
    // '<S7>:2003:1' sf_internal_predicateOutput = in_preViewModeGroup == EViewModeGroup.VIEWMODE_FRONT; 
    if (rtDWork.UnitDelay7 == VIEWMODE_FRONT)
    {
      // Transition: '<S7>:2003'
      rtDWork.is_LastViewModeGroup = IN_ViewMode_PreFrontGroup;

      // Entry 'ViewMode_PreFrontGroup': '<S7>:1998'
    }
    else
    {
      // Transition: '<S7>:2004'
      rtDWork.is_LastViewModeGroup = IN_ViewMode_RearGroup_m;

      // Entry 'ViewMode_RearGroup': '<S7>:1999'
    }
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_NotRMode(void)
{
  // Inport: '<Root>/In_gear'
  // Entry Internal 'NotRMode': '<S7>:1234'
  // Transition: '<S7>:1515'
  // '<S7>:1557:1' sf_internal_predicateOutput = in_gear == EGear.D;
  if (static_cast<uint32_T>(rtU.In_gear) == EGear_D)
  {
    // Transition: '<S7>:1557'
    // '<S7>:1510:1' sf_internal_predicateOutput = in_displayedVewL == EScreenID.WHEEL_REAR_DUAL; 
    switch (rtDWork.UnitDelay5)
    {
     case EScreenID_WHEEL_REAR_DUAL:
      // Transition: '<S7>:1510'
      rtDWork.is_NotRMode = IN_GearD_FrontWheelView;

      // Outport: '<Root>/Out_displayedView'
      // Entry 'GearD_FrontWheelView': '<S7>:1518'
      // '<S7>:1518:3' out_displayedView = EScreenID.WHEEL_FRONT_DUAL;
      rtY.Out_displayedView = EScreenID_WHEEL_FRONT_DUAL;

      // Update for Outport: '<Root>/Out_SVSCurrentView'
      // '<S7>:1518:4' out_SVSCurrentView = EScreenID.WHEEL_FRONT_DUAL;
      rtY.Out_SVSCurrentView = EScreenID_WHEEL_FRONT_DUAL;

      // Outport: '<Root>/Out_SVSViewModeSts'
      // '<S7>:1518:5' out_SVSViewModeSts = ESVSViewMode.VM_Wheel;
      rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wheel;

      // Update for Outport: '<Root>/Out_ViewModeGroup'
      // '<S7>:1518:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
      break;

     case EScreenID_SINGLE_REAR_JUNCTION:
      // Transition: '<S7>:1512'
      // '<S7>:1519:1' sf_internal_predicateOutput = in_displayedVewL == EScreenID.SINGLE_REAR_JUNCTION; 
      // Transition: '<S7>:1519'
      rtDWork.is_NotRMode = IN_GearD_FrontWideView;

      // Outport: '<Root>/Out_displayedView'
      // Entry 'GearD_FrontWideView': '<S7>:1524'
      // '<S7>:1524:3' out_displayedView = EScreenID.SINGLE_FRONT_JUNCTION;
      rtY.Out_displayedView = EScreenID_SINGLE_FRONT_JUNCTION;

      // Update for Outport: '<Root>/Out_SVSCurrentView'
      // '<S7>:1524:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_JUNCTION;
      rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_JUNCTION;

      // Outport: '<Root>/Out_SVSViewModeSts'
      // '<S7>:1524:5' out_SVSViewModeSts = ESVSViewMode.VM_Wide;
      rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wide;

      // Update for Outport: '<Root>/Out_ViewModeGroup'
      // '<S7>:1524:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
      rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
      break;

     default:
      // Transition: '<S7>:1513'
      // '<S7>:1520:1' sf_internal_predicateOutput = (in_displayedVewL == EScreenID.PERSPECTIVE_PLE ) ||...  
      // '<S7>:1520:2' (in_displayedVewL == EScreenID.PERSPECTIVE_PRE ) ||...
      // '<S7>:1520:3' (in_displayedVewL == EScreenID.PERSPECTIVE_PRI ) ||...
      // '<S7>:1520:4' (in_displayedVewL == EScreenID.PERSPECTIVE_RR ) ||...
      // '<S7>:1520:5' (in_displayedVewL == EScreenID.PERSPECTIVE_RL )||...
      // '<S7>:1520:6' (in_displayedVewL == EScreenID.PERSPECTIVE_PFR ) ||...
      // '<S7>:1520:7' (in_displayedVewL == EScreenID.PERSPECTIVE_FR ) ||...
      // '<S7>:1520:8' (in_displayedVewL == EScreenID.PERSPECTIVE_FL );
      if ((rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_PLE) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_PRE) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_PRI) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_RR) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_RL) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_PFR) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_FR) ||
          (rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_FL))
      {
        // Transition: '<S7>:1520'
        rtDWork.is_NotRMode = IN_GearD_PersRearView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'GearD_PersRearView': '<S7>:1517'
        // '<S7>:1517:3' out_displayedView = EScreenID.PERSPECTIVE_PRE;
        rtY.Out_displayedView = EScreenID_PERSPECTIVE_PRE;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1517:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PRE;
        rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PRE;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1517:5' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1517:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

        // Transition: '<S7>:2051'
        // '<S7>:2402:1' sf_internal_predicateOutput = in_displayedVewL == EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
      }
      else if (rtDWork.UnitDelay5 == EScreenID_SINGLE_REAR_NORMAL_ON_ROAD)
      {
        // Transition: '<S7>:2402'
        // Transition: '<S7>:1528'
        rtDWork.is_NotRMode = IN_GearD_FrontView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'GearD_FrontView': '<S7>:1525'
        // '<S7>:1525:3' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1525:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1525:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1525:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
      }
      else
      {
        // Transition: '<S7>:2404'
        rtDWork.is_NotRMode = IN_GearD_NoViewChange;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'GearD_NoViewChange': '<S7>:2405'
        // '<S7>:2405:3' out_displayedView = in_displayedVewL;
        rtY.Out_displayedView = rtDWork.UnitDelay5;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:2405:4' out_SVSCurrentView = in_displayedVewL;
        rtY.Out_SVSCurrentView = rtDWork.UnitDelay5;
      }
      break;
    }
  }
  else
  {
    // Transition: '<S7>:1559'
    rtDWork.is_NotRMode = IN_NoViewChangeByGearChanged;
  }

  // End of Inport: '<Root>/In_gear'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_FullScreen_R(void)
{
  // Entry Internal 'FullScreen_R': '<S7>:1220'
  // Transition: '<S7>:1489'
  // '<S7>:1495:1' sf_internal_predicateOutput = in_displayedVewL == EScreenID.WHEEL_FRONT_DUAL; 
  switch (rtDWork.UnitDelay5)
  {
   case EScreenID_WHEEL_FRONT_DUAL:
    // Transition: '<S7>:1495'
    rtDWork.is_FullScreen_R = IN_GearR_RearWheelView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'GearR_RearWheelView': '<S7>:1494'
    // '<S7>:1494:3' out_displayedView = EScreenID.WHEEL_REAR_DUAL;
    rtY.Out_displayedView = EScreenID_WHEEL_REAR_DUAL;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1494:4' out_SVSCurrentView = EScreenID.WHEEL_REAR_DUAL;
    rtY.Out_SVSCurrentView = EScreenID_WHEEL_REAR_DUAL;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:1494:5' out_SVSViewModeSts = ESVSViewMode.VM_Wheel;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wheel;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:1494:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
    rtY.Out_ViewModeGroup = VIEWMODE_REAR;
    break;

   case EScreenID_SINGLE_FRONT_JUNCTION:
    // Transition: '<S7>:1498'
    // '<S7>:1500:1' sf_internal_predicateOutput = in_displayedVewL == EScreenID.SINGLE_FRONT_JUNCTION; 
    // Transition: '<S7>:1500'
    rtDWork.is_FullScreen_R = IN_GearR_RearWideView;

    // Outport: '<Root>/Out_displayedView'
    // Entry 'GearR_RearWideView': '<S7>:1493'
    // '<S7>:1493:3' out_displayedView = EScreenID.SINGLE_REAR_JUNCTION;
    rtY.Out_displayedView = EScreenID_SINGLE_REAR_JUNCTION;

    // Update for Outport: '<Root>/Out_SVSCurrentView'
    // '<S7>:1493:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_JUNCTION;
    rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_JUNCTION;

    // Outport: '<Root>/Out_SVSViewModeSts'
    // '<S7>:1493:5' out_SVSViewModeSts = ESVSViewMode.VM_Wide;
    rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wide;

    // Update for Outport: '<Root>/Out_ViewModeGroup'
    // '<S7>:1493:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
    rtY.Out_ViewModeGroup = VIEWMODE_REAR;
    break;

   default:
    // Transition: '<S7>:1499'
    // '<S7>:1501:1' sf_internal_predicateOutput = (in_displayedVewL == EScreenID.PERSPECTIVE_PLE ) ||...  
    // '<S7>:1501:2' (in_displayedVewL == EScreenID.PERSPECTIVE_PRE ) ||...
    // '<S7>:1501:3' (in_displayedVewL == EScreenID.PERSPECTIVE_PRI ) ||...
    // '<S7>:1501:4' (in_displayedVewL == EScreenID.PERSPECTIVE_RR ) ||...
    // '<S7>:1501:5' (in_displayedVewL == EScreenID.PERSPECTIVE_RL )||...
    // '<S7>:1501:6' (in_displayedVewL == EScreenID.PERSPECTIVE_PFR ) ||...
    // '<S7>:1501:7' (in_displayedVewL == EScreenID.PERSPECTIVE_FR ) ||...
    // '<S7>:1501:8' (in_displayedVewL == EScreenID.PERSPECTIVE_FL );
    if ((rtDWork.UnitDelay5 == EScreenID_PERSPECTIVE_PLE) || (rtDWork.UnitDelay5
         == EScreenID_PERSPECTIVE_PRE) || (rtDWork.UnitDelay5 ==
         EScreenID_PERSPECTIVE_PRI) || (rtDWork.UnitDelay5 ==
         EScreenID_PERSPECTIVE_RR) || (rtDWork.UnitDelay5 ==
         EScreenID_PERSPECTIVE_RL) || (rtDWork.UnitDelay5 ==
         EScreenID_PERSPECTIVE_PFR) || (rtDWork.UnitDelay5 ==
         EScreenID_PERSPECTIVE_FR) || (rtDWork.UnitDelay5 ==
         EScreenID_PERSPECTIVE_FL))
    {
      // Transition: '<S7>:1501'
      rtDWork.is_FullScreen_R = IN_GearR_PersFrontView;

      // Outport: '<Root>/Out_displayedView'
      // Entry 'GearR_PersFrontView': '<S7>:1492'
      // '<S7>:1492:3' out_displayedView = EScreenID.PERSPECTIVE_PFR;
      rtY.Out_displayedView = EScreenID_PERSPECTIVE_PFR;

      // Update for Outport: '<Root>/Out_SVSCurrentView'
      // '<S7>:1492:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PFR;
      rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PFR;

      // Outport: '<Root>/Out_SVSViewModeSts'
      // '<S7>:1492:5' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
      rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

      // Update for Outport: '<Root>/Out_ViewModeGroup'
      // '<S7>:1492:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;

      // Transition: '<S7>:1503'
      // '<S7>:2400:1' sf_internal_predicateOutput = (in_displayedVewL == EScreenID.SINGLE_FRONT_NORMAL ) ||... 
      // '<S7>:2400:2' in_displayedVewL == EScreenID.SINGLE_STB ||...
      // '<S7>:2400:3' in_displayedVewL == EScreenID.FLOAT_SINGLE_FRONT ||...
      // '<S7>:2400:4' in_displayedVewL == EScreenID.FLOAT_WHEEL_FRONT_DUAL ||... 
      // '<S7>:2400:5' in_displayedVewL == EScreenID.FLOAT_SINGLE_REAR;
    }
    else if ((rtDWork.UnitDelay5 == EScreenID_SINGLE_FRONT_NORMAL) ||
             (rtDWork.UnitDelay5 == EScreenID_SINGLE_STB) || (rtDWork.UnitDelay5
              == EScreenID_FLOAT_SINGLE_FRONT) || (rtDWork.UnitDelay5 ==
              EScreenID_FLOAT_WHEEL_FRONT_DUAL) || (rtDWork.UnitDelay5 ==
              EScreenID_FLOAT_SINGLE_REAR))
    {
      // Transition: '<S7>:2400'
      rtDWork.is_FullScreen_R = IN_GearR_RearView;

      // Outport: '<Root>/Out_displayedView'
      // Entry 'GearR_RearView': '<S7>:2399'
      // '<S7>:2399:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
      rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

      // Update for Outport: '<Root>/Out_SVSCurrentView'
      // '<S7>:2399:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
      rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

      // Outport: '<Root>/Out_SVSViewModeSts'
      // '<S7>:2399:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
      rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

      // Update for Outport: '<Root>/Out_ViewModeGroup'
      // '<S7>:2399:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
      rtY.Out_ViewModeGroup = VIEWMODE_REAR;
    }
    else
    {
      // Transition: '<S7>:2398'
      // Transition: '<S7>:1504'
      rtDWork.is_FullScreen_R = IN_GearR_NoViewChange;

      // Outport: '<Root>/Out_displayedView'
      // Entry 'GearR_NoViewChange': '<S7>:1224'
      // '<S7>:1224:3' out_displayedView = in_displayedVewL;
      rtY.Out_displayedView = rtDWork.UnitDelay5;

      // Update for Outport: '<Root>/Out_SVSCurrentView'
      // '<S7>:1224:4' out_SVSCurrentView = in_displayedVewL;
      rtY.Out_SVSCurrentView = rtDWork.UnitDelay5;
    }
    break;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_Voice_Views(void)
{
  EVoiceDockReq tmp;

  // Inport: '<Root>/In_voiceDockRequest'
  // Entry Internal 'Voice_Views': '<S7>:1395'
  // Transition: '<S7>:1404'
  // '<S7>:2436:1' sf_internal_predicateOutput = (in_SRIsTopActivity)...
  // '<S7>:2436:2' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONTWIDE...
  // '<S7>:2436:3' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_SKELETON ...
  // '<S7>:2436:4' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_3D...
  // '<S7>:2436:5' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWIDE ...
  // '<S7>:2436:6' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONTWHEEL ...
  // '<S7>:2436:7' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL);
  tmp = rtU.In_voiceDockRequest;

  // Inport: '<Root>/In_SRIsTopActivity'
  if (rtU.In_SRIsTopActivity && ((static_cast<uint32_T>(tmp) ==
        EVoiceDockReq_OPEN_FRONTWIDE) || (static_cast<uint32_T>(tmp) ==
        EVoiceDockReq_OPEN_SKELETON) || (static_cast<uint32_T>(tmp) ==
        EVoiceDockReq_OPEN_3D) || (static_cast<uint32_T>(tmp) ==
        EVoiceDockReq_OPEN_REARWIDE) || (static_cast<uint32_T>(tmp) ==
        EVoiceDockReq_OPEN_FRONTWHEEL) || (static_cast<uint32_T>(tmp) ==
        EVoiceDockReq_OPEN_REARWHEEL)))
  {
    // Transition: '<S7>:2436'
    rtDWork.is_Voice_Views = IN_Voice_ViewChange_Failed_In_S;

    // Entry 'Voice_ViewChange_Failed_In_SR': '<S7>:2434'
    // '<S7>:2434:3' out_voiceDockFeedback = EVoiceDockFb.OPEN_FAIL_UNAVAILABLE_IN_SR; 
    rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_OPEN_FAIL_UNAVAILABLE_IN_SR;
  }
  else
  {
    EGear tmp_0;

    // Inport: '<Root>/In_gear'
    // '<S7>:2438:1' sf_internal_predicateOutput = ((in_gear==EGear.D)&&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR... 
    // '<S7>:2438:2' ||in_voiceDockRequest==EVoiceDockReq.OPEN_REARWIDE...
    // '<S7>:2438:3' ||in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL));
    tmp_0 = rtU.In_gear;
    if ((static_cast<uint32_T>(tmp_0) == EGear_D) && ((static_cast<uint32_T>(tmp)
          == EVoiceDockReq_OPEN_REAR) || (static_cast<uint32_T>(tmp) ==
          EVoiceDockReq_OPEN_REARWIDE) || (static_cast<uint32_T>(tmp) ==
          EVoiceDockReq_OPEN_REARWHEEL)))
    {
      // Transition: '<S7>:2438'
      rtDWork.is_Voice_Views = IN_Voice_ViewChange_Failed_Out_;

      // Entry 'Voice_ViewChange_Failed_Out_SR': '<S7>:2437'
      // '<S7>:2437:3' out_voiceDockFeedback = EVoiceDockFb.OPEN_FAIL_NOTPN;
      rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_OPEN_FAIL_NOTPN;
    }
    else
    {
      // Transition: '<S7>:2439'
      rtDWork.is_Voice_Views = IN_Voice_View_Succeed;

      // Entry 'Voice_View_Succeed': '<S7>:2432'
      // '<S7>:2432:3' out_voiceDockFeedback = EVoiceDockFb.OPEN_SUCCESS;
      rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_OPEN_SUCCESS;

      // Inport: '<Root>/In_voiceDockRequest'
      // Entry Internal 'Voice_View_Succeed': '<S7>:2432'
      // Transition: '<S7>:2433'
      // '<S7>:1406:1' sf_internal_predicateOutput = (in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONT); 
      switch (rtU.In_voiceDockRequest)
      {
       case EVoiceDockReq_OPEN_FRONT:
        // Transition: '<S7>:1406'
        // '<S7>:2486:1' sf_internal_predicateOutput = in_SRIsTopActivity == true; 
        if (rtU.In_SRIsTopActivity)
        {
          // Transition: '<S7>:2486'
          rtDWork.is_Voice_View_Succeed = IN_Voice_FloatFrontView;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Voice_FloatFrontView': '<S7>:2485'
          // '<S7>:2485:3' out_displayedView = EScreenID.FLOAT_SINGLE_FRONT;
          rtY.Out_displayedView = EScreenID_FLOAT_SINGLE_FRONT;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:2485:4' out_SVSCurrentView = EScreenID.FLOAT_SINGLE_FRONT;
          rtY.Out_SVSCurrentView = EScreenID_FLOAT_SINGLE_FRONT;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:2485:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:2485:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        }
        else
        {
          // Transition: '<S7>:2487'
          rtDWork.is_Voice_View_Succeed = IN_Voice_FrontView;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Voice_FrontView': '<S7>:1403'
          // '<S7>:1403:3' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL;
          rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:1403:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL;
          rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:1403:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:1403:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        }
        break;

       case EVoiceDockReq_OPEN_FRONTWHEEL:
        // Transition: '<S7>:1432'
        // '<S7>:1413:1' sf_internal_predicateOutput = (in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONTWHEEL); 
        // Transition: '<S7>:1413'
        // Transition: '<S7>:2489'
        // Transition: '<S7>:2521'
        rtDWork.is_Voice_View_Succeed = IN_Voice_FrontWheelView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Voice_FrontWheelView': '<S7>:1407'
        // '<S7>:1407:3' out_displayedView = EScreenID.WHEEL_FRONT_DUAL;
        rtY.Out_displayedView = EScreenID_WHEEL_FRONT_DUAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1407:4' out_SVSCurrentView = EScreenID.WHEEL_FRONT_DUAL;
        rtY.Out_SVSCurrentView = EScreenID_WHEEL_FRONT_DUAL;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1407:5' out_SVSViewModeSts = ESVSViewMode.VM_Wheel;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wheel;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1407:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        break;

       case EVoiceDockReq_OPEN_FRONTWIDE:
        // Transition: '<S7>:1433'
        // '<S7>:1414:1' sf_internal_predicateOutput = in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONTWIDE; 
        // Transition: '<S7>:1414'
        rtDWork.is_Voice_View_Succeed = IN_Voice_FrontWideView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Voice_FrontWideView': '<S7>:1408'
        // '<S7>:1408:3' out_displayedView = EScreenID.SINGLE_FRONT_JUNCTION;
        rtY.Out_displayedView = EScreenID_SINGLE_FRONT_JUNCTION;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1408:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_JUNCTION;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_JUNCTION;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1408:5' out_SVSViewModeSts = ESVSViewMode.VM_Wide;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wide;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1408:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        break;

       case EVoiceDockReq_OPEN_SKELETON:
        // Transition: '<S7>:1434'
        // '<S7>:1415:1' sf_internal_predicateOutput = in_voiceDockRequest ==  EVoiceDockReq.OPEN_SKELETON; 
        // Transition: '<S7>:1415'
        rtDWork.is_Voice_View_Succeed = IN_Voice_SKELETON;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Voice_SKELETON': '<S7>:1409'
        // '<S7>:1409:3' out_displayedView = EScreenID.SINGLE_STB;
        rtY.Out_displayedView = EScreenID_SINGLE_STB;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1409:4' out_SVSCurrentView = EScreenID.SINGLE_STB;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_STB;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1409:5' out_SVSViewModeSts = ESVSViewMode.VM_STB;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_STB;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1409:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        break;

       case EVoiceDockReq_OPEN_3D:
        // Transition: '<S7>:1435'
        // '<S7>:1416:1' sf_internal_predicateOutput = in_voiceDockRequest ==  EVoiceDockReq.OPEN_3D; 
        // Transition: '<S7>:1416'
        // '<S7>:2518:1' sf_internal_predicateOutput = in_gear==EGear.R;
        if (static_cast<uint32_T>(tmp_0) == EGear_R)
        {
          // Transition: '<S7>:2518'
          rtDWork.is_Voice_View_Succeed = IN_Voice_3DView_GearR;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Voice_3DView_GearR': '<S7>:1410'
          // '<S7>:1410:3' out_displayedView = EScreenID.PERSPECTIVE_PFR;
          rtY.Out_displayedView = EScreenID_PERSPECTIVE_PFR;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:1410:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PFR;
          rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PFR;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:1410:5' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:1410:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        }
        else
        {
          // Transition: '<S7>:2519'
          rtDWork.is_Voice_View_Succeed = IN_Voice_3DView_NotGearR;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Voice_3DView_NotGearR': '<S7>:2516'
          // '<S7>:2516:3' out_displayedView = EScreenID.PERSPECTIVE_PRE;
          rtY.Out_displayedView = EScreenID_PERSPECTIVE_PRE;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:2516:4' out_SVSCurrentView = EScreenID.PERSPECTIVE_PRE;
          rtY.Out_SVSCurrentView = EScreenID_PERSPECTIVE_PRE;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:2516:5' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:2516:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        }
        break;

       case EVoiceDockReq_OPEN_REAR:
        // Transition: '<S7>:1436'
        // '<S7>:1417:1' sf_internal_predicateOutput = (in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR); 
        // Transition: '<S7>:1417'
        // '<S7>:2494:1' sf_internal_predicateOutput = in_SRIsTopActivity == true; 
        if (rtU.In_SRIsTopActivity)
        {
          // Transition: '<S7>:2494'
          rtDWork.is_Voice_View_Succeed = IN_Voice_FloatRearView;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Voice_FloatRearView': '<S7>:2492'
          // '<S7>:2492:3' out_displayedView = EScreenID.FLOAT_SINGLE_REAR;
          rtY.Out_displayedView = EScreenID_FLOAT_SINGLE_REAR;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:2492:4' out_SVSCurrentView = EScreenID.FLOAT_SINGLE_REAR;
          rtY.Out_SVSCurrentView = EScreenID_FLOAT_SINGLE_REAR;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:2492:5' out_SVSViewModeSts = ESVSViewMode.VM_Floating;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Floating;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:2492:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        }
        else
        {
          // Transition: '<S7>:2495'
          rtDWork.is_Voice_View_Succeed = IN_Voice_RearView;

          // Outport: '<Root>/Out_displayedView'
          // Entry 'Voice_RearView': '<S7>:1411'
          // '<S7>:1411:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
          rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

          // Update for Outport: '<Root>/Out_SVSCurrentView'
          // '<S7>:1411:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
          rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

          // Outport: '<Root>/Out_SVSViewModeSts'
          // '<S7>:1411:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

          // Update for Outport: '<Root>/Out_ViewModeGroup'
          // '<S7>:1411:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        }
        break;

       case EVoiceDockReq_OPEN_REARWIDE:
        // Transition: '<S7>:1437'
        // '<S7>:1418:1' sf_internal_predicateOutput = in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWIDE; 
        // Transition: '<S7>:1418'
        rtDWork.is_Voice_View_Succeed = IN_Voice_RearWideView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Voice_RearWideView': '<S7>:1412'
        // '<S7>:1412:3' out_displayedView = EScreenID.SINGLE_REAR_JUNCTION;
        rtY.Out_displayedView = EScreenID_SINGLE_REAR_JUNCTION;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1412:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_JUNCTION;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_JUNCTION;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1412:5' out_SVSViewModeSts = ESVSViewMode.VM_Wide;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wide;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1412:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        break;

       case EVoiceDockReq_OPEN_REARWHEEL:
        // Transition: '<S7>:1438'
        // '<S7>:1420:1' sf_internal_predicateOutput = in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL; 
        // Transition: '<S7>:1420'
        rtDWork.is_Voice_View_Succeed = IN_Voice_RearWheelView;

        // Outport: '<Root>/Out_displayedView'
        // Entry 'Voice_RearWheelView': '<S7>:1419'
        // '<S7>:1419:3' out_displayedView = EScreenID.WHEEL_REAR_DUAL;
        rtY.Out_displayedView = EScreenID_WHEEL_REAR_DUAL;

        // Update for Outport: '<Root>/Out_SVSCurrentView'
        // '<S7>:1419:4' out_SVSCurrentView = EScreenID.WHEEL_REAR_DUAL;
        rtY.Out_SVSCurrentView = EScreenID_WHEEL_REAR_DUAL;

        // Outport: '<Root>/Out_SVSViewModeSts'
        // '<S7>:1419:5' out_SVSViewModeSts = ESVSViewMode.VM_Wheel;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Wheel;

        // Update for Outport: '<Root>/Out_ViewModeGroup'
        // '<S7>:1419:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
        rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        break;

       default:
        // Transition: '<S7>:2246'
        // Transition: '<S7>:2247'
        rtDWork.is_Voice_View_Succeed = IN_NoViewChangeByVoice;
        break;
      }
    }
  }

  // End of Inport: '<Root>/In_SRIsTopActivity'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Available(const boolean_T
  *out_manualChangeAvail, const boolean_T *out_isViewChangeAvl, const boolean_T *
  out_freemodeviewAct, const boolean_T *out_isCpcShow, const ESVSViewMode
  *in_HUSVSMode_prev, const EScreenID *in_HUViewReq_prev)
{
  // Outport: '<Root>/Out_systemAvailable'
  rtY.Out_systemAvailable = ESystem_Available;

  // Outport: '<Root>/Out_SVSUnavlMsgs'
  rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

  // Inport: '<Root>/In_FID_SVSEcuInternalStatus'
  // During 'Available': '<S7>:30'
  // '<S7>:757:1' sf_internal_predicateOutput = ~in_FID_SVSEcuInternalStatus;
  if (!rtU.In_FID_SVSEcuInternalStatus)
  {
    // Transition: '<S7>:757'
    exit_internal_Available();
    rtDWork.is_SuperStateL2 = IN_Unavailable;

    // Outport: '<Root>/Out_systemAvailable'
    // Entry 'Unavailable': '<S7>:28'
    // '<S7>:28:3' out_systemAvailable = ESystem.Unavailable;
    rtY.Out_systemAvailable = ESystem_Unavailable;

    // Outport: '<Root>/Out_systemActive'
    // '<S7>:28:4' out_systemActive = false;
    rtY.Out_systemActive = false;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // out_displayedView = EScreenID.NO_CHANGE;
    // out_SVSCurrentView = EScreenID.NO_CHANGE;
    // '<S7>:28:7' out_SVSShowReq = false;
    rtY.Out_SVSShowReq = false;

    // out_SVSUnavlMsgs = uint8(2);
    rtDWork.is_Unavailable = IN_vidouterror;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    // Entry 'vidouterror': '<S7>:2325'
    // '<S7>:2325:3' out_SVSUnavlMsgs = ENotActiveReason.QNX_AVM_ERROR;
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

    // Inport: '<Root>/In_AVMError'
    // '<S7>:2331:1' sf_internal_predicateOutput = in_AVMError;
  }
  else if (rtU.In_AVMError)
  {
    // Transition: '<S7>:2331'
    exit_internal_Available();
    rtDWork.is_SuperStateL2 = IN_Unavailable;

    // Outport: '<Root>/Out_systemAvailable'
    // Entry 'Unavailable': '<S7>:28'
    // '<S7>:28:3' out_systemAvailable = ESystem.Unavailable;
    rtY.Out_systemAvailable = ESystem_Unavailable;

    // Outport: '<Root>/Out_systemActive'
    // '<S7>:28:4' out_systemActive = false;
    rtY.Out_systemActive = false;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // out_displayedView = EScreenID.NO_CHANGE;
    // out_SVSCurrentView = EScreenID.NO_CHANGE;
    // '<S7>:28:7' out_SVSShowReq = false;
    rtY.Out_SVSShowReq = false;

    // out_SVSUnavlMsgs = uint8(2);
    rtDWork.is_Unavailable = IN_AVM_Error;

    // Entry 'AVM_Error': '<S7>:2306'
    // Entry Internal 'AVM_Error': '<S7>:2306'
    // Transition: '<S7>:2361'
    rtDWork.is_AVM_Error = IN_AVM_Error_No_Activate_Trigge;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    // Entry 'AVM_Error_No_Activate_Triggered': '<S7>:2315'
    // '<S7>:2315:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

    // '<S7>:2315:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
    rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
  }
  else
  {
    EGear tmp_2;
    boolean_T tmp_0;

    // Inport: '<Root>/In_parkstatus'
    // '<S7>:1247:1' sf_internal_predicateOutput = in_vehSpeed > in_speedTrigOut &&... 
    // '<S7>:1247:2'  ~in_parkingsts&&in_gear~=EGear.R;
    tmp_0 = !rtU.In_parkstatus;

    // Inport: '<Root>/In_gear'
    tmp_2 = rtU.In_gear;

    // Inport: '<Root>/In_vehSpeed' incorporates:
    //   Inport: '<Root>/In_speedTrigOut'

    if ((rtU.In_vehSpeed > rtU.In_speedTrigOut) && tmp_0 &&
        (static_cast<uint32_T>(tmp_2) != EGear_R))
    {
      // Transition: '<S7>:1247'
      exit_internal_Available();
      rtDWork.is_SuperStateL2 = IN_Unavailable;

      // Outport: '<Root>/Out_systemAvailable'
      // Entry 'Unavailable': '<S7>:28'
      // '<S7>:28:3' out_systemAvailable = ESystem.Unavailable;
      rtY.Out_systemAvailable = ESystem_Unavailable;

      // Outport: '<Root>/Out_systemActive'
      // '<S7>:28:4' out_systemActive = false;
      rtY.Out_systemActive = false;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // out_displayedView = EScreenID.NO_CHANGE;
      // out_SVSCurrentView = EScreenID.NO_CHANGE;
      // '<S7>:28:7' out_SVSShowReq = false;
      rtY.Out_SVSShowReq = false;

      // out_SVSUnavlMsgs = uint8(2);
      rtDWork.is_Unavailable = IN_spdtoohigh;

      // Entry 'spdtoohigh': '<S7>:1244'
      // Entry Internal 'spdtoohigh': '<S7>:1244'
      // Transition: '<S7>:2352'
      rtDWork.is_spdtoohigh = IN_SPD_No_Activate_Triggered;

      // Outport: '<Root>/Out_SVSUnavlMsgs'
      // Entry 'SPD_No_Activate_Triggered': '<S7>:2300'
      // '<S7>:2300:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
      rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

      // '<S7>:2300:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
      rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;

      // Inport: '<Root>/In_PowerSaveMode'
      // '<S7>:2350:1' sf_internal_predicateOutput = in_PowerSaveMode;
    }
    else if (rtU.In_PowerSaveMode)
    {
      // Transition: '<S7>:2350'
      exit_internal_Available();
      rtDWork.is_SuperStateL2 = IN_Unavailable;

      // Outport: '<Root>/Out_systemAvailable'
      // Entry 'Unavailable': '<S7>:28'
      // '<S7>:28:3' out_systemAvailable = ESystem.Unavailable;
      rtY.Out_systemAvailable = ESystem_Unavailable;

      // Outport: '<Root>/Out_systemActive'
      // '<S7>:28:4' out_systemActive = false;
      rtY.Out_systemActive = false;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // out_displayedView = EScreenID.NO_CHANGE;
      // out_SVSCurrentView = EScreenID.NO_CHANGE;
      // '<S7>:28:7' out_SVSShowReq = false;
      rtY.Out_SVSShowReq = false;

      // out_SVSUnavlMsgs = uint8(2);
      rtDWork.is_Unavailable = IN_Power_Save_Mode;

      // Entry 'Power_Save_Mode': '<S7>:2341'
      // Entry Internal 'Power_Save_Mode': '<S7>:2341'
      // Transition: '<S7>:2363'
      rtDWork.is_Power_Save_Mode = IN_Power_Save_Mode_No_Activate_;

      // Outport: '<Root>/Out_SVSUnavlMsgs'
      // Entry 'Power_Save_Mode_No_Activate_Triggered': '<S7>:2345'
      // '<S7>:2345:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
      rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

      // '<S7>:2345:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
      rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;

      // '<S7>:30:7' l_3DviewInputIsChanged = (hasChanged(in_HUSVSMode)|...
      // '<S7>:30:8'     hasChanged(in_HUDislayModeSwitch)|hasChanged(in_HUDislayModeExpand)|hasChanged(in_HUDislayModeExpandNew)|... 
      // '<S7>:30:9'     hasChanged(in_parkingsts)|hasChanged(in_isCpcShow) )
      // '<S7>:2208:1' sf_internal_predicateOutput = hasChangedTo(in_parkingsts, true); 
    }
    else if ((rtDWork.in_parkingsts_prev != rtDWork.in_parkingsts_start) &&
             rtDWork.in_parkingsts_start)
    {
      // Transition: '<S7>:2208'
      exit_internal_Available();
      rtDWork.is_Available = IN_FloatingView;
      rtDWork.is_FloatingView = IN_FloatingViews_Park;

      // Inport: '<Root>/In_FloatViewChange'
      // Entry 'FloatingViews_Park': '<S7>:1346'
      // Entry Internal 'FloatingViews_Park': '<S7>:1346'
      // Transition: '<S7>:2210'
      // '<S7>:2642:1' sf_internal_predicateOutput = in_FloatViewChange==EFloatViewType.FRView; 
      if (static_cast<uint32_T>(rtU.In_FloatViewChange) == EFloatViewType_FRView)
      {
        // Transition: '<S7>:2642'
        rtDWork.is_FloatingViews_Park = IN_Park_View;

        // Entry 'Park_View': '<S7>:2637'
        enter_internal_Park_View();
      }
      else
      {
        // Transition: '<S7>:2643'
        rtDWork.is_FloatingViews_Park = IN_ParkingPlan_FloatView;

        // Entry 'ParkingPlan_FloatView': '<S7>:2558'
        enter_internal_ParkingPlan_Floa();
      }

      // End of Inport: '<Root>/In_FloatViewChange'
    }
    else
    {
      EDockAvmButtonPress tmp;
      EDockAvmButtonPress tmp_1;

      // '<S7>:2411:1' sf_internal_predicateOutput = (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
      // '<S7>:2411:2' ||hasChangedTo(in_EnlargeButtonPressed,true)||...
      // '<S7>:2411:3' hasChangedTo(in_androidIconActive,true)||...
      // '<S7>:2411:4' hasChangedTo(in_steeringWheelButtonPressed,true))&&...
      // '<S7>:2411:5' ~in_parkingsts && ...
      // '<S7>:2411:6' (out_SVSShowReq == true&&in_SVSShowReq == true)&&...
      // '<S7>:2411:7' (in_displayedVewL == EScreenID.FLOAT_REAR_PLAN_VIEW||...
      // '<S7>:2411:8' in_displayedVewL == EScreenID.FLOAT_REAR_VIEW||...
      // '<S7>:2411:9' in_displayedVewL == EScreenID.FLOAT_FRONT_PLAN_VIEW||...
      // '<S7>:2411:10' in_displayedVewL == EScreenID.FLOAT_FRONT_VIEW);
      tmp = rtDWork.in_dockAvmButtonPress_start;
      tmp_1 = rtDWork.in_dockAvmButtonPress_prev;

      // Update for Outport: '<Root>/Out_SVSShowReq' incorporates:
      //   Inport: '<Root>/In_HUSVSMode'
      //   Inport: '<Root>/In_HaveEverActivated'
      //   Inport: '<Root>/In_ParkingSearch'
      //   Inport: '<Root>/In_SRIsTopActivity'
      //   Inport: '<Root>/In_isCpcFuncOn'
      //   Inport: '<Root>/In_voiceDockRequest'
      //   Outport: '<Root>/Out_SVSCurrentView'
      //   Outport: '<Root>/Out_SVSViewModeSts'
      //   Outport: '<Root>/Out_ViewModeGroup'
      //   Outport: '<Root>/Out_displayedView'

      if ((((tmp_1 != tmp) && (static_cast<uint32_T>(tmp) ==
             EDockAvmButtonPress_AVM_PRESS)) ||
           ((rtDWork.in_EnlargeButtonPressed_prev !=
             rtDWork.in_EnlargeButtonPressed_start) &&
            rtDWork.in_EnlargeButtonPressed_start) ||
           ((rtDWork.in_androidIconActive_prev !=
             rtDWork.in_androidIconActive_start) &&
            rtDWork.in_androidIconActive_start) ||
           ((rtDWork.in_steeringWheelButtonPressed_p !=
             rtDWork.in_steeringWheelButtonPressed_s) &&
            rtDWork.in_steeringWheelButtonPressed_s)) && tmp_0 &&
          rtY.Out_SVSShowReq && rtDWork.UnitDelay8 && ((rtDWork.UnitDelay5 ==
            EScreenID_FLOAT_REAR_PLAN_VIEW) || (rtDWork.UnitDelay5 ==
            EScreenID_FLOAT_REAR_VIEW) || (rtDWork.UnitDelay5 ==
            EScreenID_FLOAT_FRONT_PLAN_VIEW) || (rtDWork.UnitDelay5 ==
            EScreenID_FLOAT_FRONT_VIEW)))
      {
        // Transition: '<S7>:2411'
        exit_internal_Available();
        rtDWork.is_Available = IN_FloatingView;
        rtDWork.is_FloatingView = IN_Enlarge_delay;
        rtDWork.temporalCounter_i1_b = 0U;

        // '<S7>:2646:1' sf_internal_predicateOutput = ~in_parkingsts&&(in_displayedVewL == EScreenID.NO_CHANGE) &&... 
        // '<S7>:2646:2' (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)||... 
        // '<S7>:2646:3' hasChangedTo(in_androidIconActive,true)||...
        // '<S7>:2646:4' hasChangedTo(in_steeringWheelButtonPressed,true))&&...
        // '<S7>:2646:5' (in_gear==EGear.P || in_gear==EGear.N);
      }
      else if (tmp_0 && (rtDWork.UnitDelay5 == EScreenID_NO_CHANGE) && (((tmp_1
                  != tmp) && (static_cast<uint32_T>(tmp) ==
                              EDockAvmButtonPress_AVM_PRESS)) ||
                ((rtDWork.in_androidIconActive_prev !=
                  rtDWork.in_androidIconActive_start) &&
                 rtDWork.in_androidIconActive_start) ||
                ((rtDWork.in_steeringWheelButtonPressed_p !=
                  rtDWork.in_steeringWheelButtonPressed_s) &&
                 rtDWork.in_steeringWheelButtonPressed_s)) &&
               ((static_cast<uint32_T>(tmp_2) == EGear_P) ||
                (static_cast<uint32_T>(tmp_2) == EGear_N)))
      {
        // Transition: '<S7>:2646'
        exit_internal_Available();
        rtDWork.is_Available = IN_Mannually_Active_First;

        // Entry 'Mannually_Active_First': '<S7>:2645'
        // '<S7>:2645:3' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

        // '<S7>:2645:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL;
        rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

        // '<S7>:2645:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
        rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

        // '<S7>:2645:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
        rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
      }
      else
      {
        boolean_T guard1 = false;
        boolean_T guard2 = false;
        boolean_T guard3 = false;
        boolean_T tmp_4;
        boolean_T tmp_5;
        boolean_T tmp_6;

        // '<S7>:2652:1' sf_internal_predicateOutput = (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)||... 
        // '<S7>:2652:2' hasChangedTo(in_androidIconActive,true)||...
        // '<S7>:2652:3' hasChangedTo(in_steeringWheelButtonPressed,true))&&...
        // '<S7>:2652:4' ~in_parkingsts&&...
        // '<S7>:2652:5'  in_gear == EGear.R&&...
        // '<S7>:2652:6' (out_SVSShowReq == false||in_SVSShowReq == false);
        tmp_5 = !rtY.Out_SVSShowReq;
        tmp_6 = !rtDWork.UnitDelay8;
        tmp_4 = (tmp_5 || tmp_6);
        guard1 = false;
        guard2 = false;
        guard3 = false;
        if ((((tmp_1 != tmp) && (static_cast<uint32_T>(tmp) ==
               EDockAvmButtonPress_AVM_PRESS)) ||
             ((rtDWork.in_androidIconActive_prev !=
               rtDWork.in_androidIconActive_start) &&
              rtDWork.in_androidIconActive_start) ||
             ((rtDWork.in_steeringWheelButtonPressed_p !=
               rtDWork.in_steeringWheelButtonPressed_s) &&
              rtDWork.in_steeringWheelButtonPressed_s)) && tmp_0 && (
             static_cast<uint32_T>(tmp_2) == EGear_R) && tmp_4)
        {
          // Transition: '<S7>:2652'
          guard1 = true;
        }
        else
        {
          EGear tmp_3;
          EGear tmp_7;
          boolean_T tmp_8;
          boolean_T tmp_a;
          boolean_T tmp_b;

          // '<S7>:2668:1' sf_internal_predicateOutput = ((hasChangedTo(in_gear,EGear.R) && ... 
          // '<S7>:2668:2' ~in_parkingsts &&...
          // '<S7>:2668:3' (out_SVSShowReq == false||in_SVSShowReq == false)) || ... 
          // '<S7>:2668:4' (in_gear == EGear.R && ...
          // '<S7>:2668:5' hasChangedTo(in_parkingsts, false)&&...
          // '<S7>:2668:6' (out_SVSShowReq == false||in_SVSShowReq == false))|| ... 
          // '<S7>:2668:7' (in_gear == EGear.R && ...
          // '<S7>:2668:8' in_isFirstRGear&&...
          // '<S7>:2668:9' ~in_haveEverActived))&&...
          // '<S7>:2668:10' ~in_ParkingSearch;
          tmp_3 = rtDWork.in_gear_start;
          tmp_7 = rtDWork.in_gear_prev;
          tmp_8 = !rtDWork.in_parkingsts_start;
          tmp_a = !rtU.In_HaveEverActivated;
          tmp_b = !rtU.In_ParkingSearch;
          if ((((tmp_7 != tmp_3) && (static_cast<uint32_T>(tmp_3) == EGear_R) &&
                tmp_0 && tmp_4) || ((static_cast<uint32_T>(tmp_2) == EGear_R) &&
                ((rtDWork.in_parkingsts_prev != rtDWork.in_parkingsts_start) &&
                 tmp_8) && tmp_4) || ((static_cast<uint32_T>(tmp_2) == EGear_R) &&
                rtU.In_isFirstRGear && tmp_a)) && tmp_b)
          {
            // Transition: '<S7>:2668'
            guard1 = true;

            // '<S7>:2650:1' sf_internal_predicateOutput = (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
            // '<S7>:2650:2' ||hasChangedTo(in_EnlargeButtonPressed,true)||...
            // '<S7>:2650:3' hasChangedTo(in_androidIconActive,true)||...
            // '<S7>:2650:4' hasChangedTo(in_steeringWheelButtonPressed,true))&&... 
            // '<S7>:2650:5' ~in_parkingsts &&...
            // '<S7>:2650:6' in_gear ~= EGear.R&&...
            // '<S7>:2650:7' (out_SVSShowReq == false&&in_SVSShowReq == false);
          }
          else if ((((tmp_1 != tmp) && (static_cast<uint32_T>(tmp) ==
                      EDockAvmButtonPress_AVM_PRESS)) ||
                    ((rtDWork.in_EnlargeButtonPressed_prev !=
                      rtDWork.in_EnlargeButtonPressed_start) &&
                     rtDWork.in_EnlargeButtonPressed_start) ||
                    ((rtDWork.in_androidIconActive_prev !=
                      rtDWork.in_androidIconActive_start) &&
                     rtDWork.in_androidIconActive_start) ||
                    ((rtDWork.in_steeringWheelButtonPressed_p !=
                      rtDWork.in_steeringWheelButtonPressed_s) &&
                     rtDWork.in_steeringWheelButtonPressed_s)) && tmp_0 && (
                    static_cast<uint32_T>(tmp_2) != EGear_R) && tmp_5 && tmp_6)
          {
            // Transition: '<S7>:2650'
            guard2 = true;

            // '<S7>:2582:1' sf_internal_predicateOutput = ((hasChangedTo(in_gear,EGear.R) && ... 
            // '<S7>:2582:2' ~in_parkingsts &&...
            // '<S7>:2582:3' (out_SVSShowReq == false||in_SVSShowReq == false)) || ... 
            // '<S7>:2582:4' (in_gear == EGear.R && ...
            // '<S7>:2582:5' hasChangedTo(in_parkingsts, false)&&...
            // '<S7>:2582:6' (out_SVSShowReq == false||in_SVSShowReq == false))|| ... 
            // '<S7>:2582:7' (in_gear == EGear.R && ...
            // '<S7>:2582:8' in_isFirstRGear))&&in_ParkingSearch;
          }
          else if ((((tmp_7 != tmp_3) && (static_cast<uint32_T>(tmp_3) ==
                      EGear_R) && tmp_0 && tmp_4) || ((static_cast<uint32_T>
                      (tmp_2) == EGear_R) && ((rtDWork.in_parkingsts_prev !=
                       rtDWork.in_parkingsts_start) && tmp_8) && tmp_4) || ((
                      static_cast<uint32_T>(tmp_2) == EGear_R) &&
                     rtU.In_isFirstRGear)) && rtU.In_ParkingSearch)
          {
            // Transition: '<S7>:2582'
            guard3 = true;
          }
          else
          {
            ESRActiveSts tmp_9;

            // '<S7>:2660:1' sf_internal_predicateOutput = hasChangedTo(in_SRIsActive, ESRActiveSts.ACTIVE)&&... 
            // '<S7>:2660:2' ~in_parkingsts&&in_gear == EGear.R;
            tmp_9 = rtDWork.in_SRIsActive_start;
            if ((rtDWork.in_SRIsActive_prev != tmp_9) && (static_cast<uint32_T>
                 (tmp_9) == ESRActiveSts_ACTIVE) && tmp_0 &&
                (static_cast<uint32_T>(tmp_2) == EGear_R))
            {
              // Transition: '<S7>:2660'
              guard3 = true;

              // '<S7>:2667:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&... 
              // '<S7>:2667:2' in_haveEverActived == false&&...
              // '<S7>:2667:3' in_isFirstDGear&&...
              // '<S7>:2667:4' (out_SVSShowReq == false||in_SVSShowReq == false))||... 
              // '<S7>:2667:5' (hasChangedTo(in_gear, EGear.D) && ...
              // '<S7>:2667:6' in_isFirstDGear&&...
              // '<S7>:2667:7' (out_SVSShowReq == false||in_SVSShowReq == false))) &&... 
              // '<S7>:2667:8' in_settingDGearActSts==ESettingSts.Set_ON&&...
              // '<S7>:2667:9' ~in_ParkingSearch;
            }
            else if ((((static_cast<uint32_T>(tmp_2) == EGear_D) && tmp_a &&
                       rtU.In_isFirstDGear && tmp_4) || ((tmp_7 != tmp_3) && (
                        static_cast<uint32_T>(tmp_3) == EGear_D) &&
                       rtU.In_isFirstDGear && tmp_4)) && (rtU.In_huDGearAct ==
                      ESettingSts_Set_ON) && tmp_b)
            {
              // Transition: '<S7>:2667'
              guard2 = true;

              // '<S7>:2604:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&... 
              // '<S7>:2604:2' in_haveEverActived == false&&...
              // '<S7>:2604:3' in_isFirstDGear&&...
              // '<S7>:2604:4' (out_SVSShowReq == false||in_SVSShowReq == false))||... 
              // '<S7>:2604:5' (hasChangedTo(in_gear, EGear.D) && ...
              // '<S7>:2604:6' in_isFirstDGear&&...
              // '<S7>:2604:7' (out_SVSShowReq == false||in_SVSShowReq == false))) &&... 
              // '<S7>:2604:8' in_settingDGearActSts==ESettingSts.Set_ON&&...
              // '<S7>:2604:9' in_ParkingSearch;
            }
            else if (((((static_cast<uint32_T>(tmp_2) == EGear_D) && tmp_a &&
                        rtU.In_isFirstDGear && tmp_4) || ((tmp_7 != tmp_3) && (
                         static_cast<uint32_T>(tmp_3) == EGear_D) &&
                        rtU.In_isFirstDGear && tmp_4)) && (rtU.In_huDGearAct ==
                       ESettingSts_Set_ON) && rtU.In_ParkingSearch) || ((((
                         static_cast<uint32_T>(tmp_2) == EGear_D) &&
                        (((rtDWork.in_obstacleAct_prev !=
                           rtDWork.in_obstacleAct_start) &&
                          rtDWork.in_obstacleAct_start) ||
                         ((rtDWork.in_narrowLaneAct_prev !=
                           rtDWork.in_narrowLaneAct_start) &&
                          rtDWork.in_narrowLaneAct_start)) &&
                        ((rtU.In_huDGearAct == ESettingSts_Set_OFF) ||
                         (!rtU.In_isFirstDGear) || rtU.In_HaveEverActivated)) ||
                       ((tmp_7 != tmp_3) && (static_cast<uint32_T>(tmp_3) ==
                         EGear_D) && (rtDWork.out_obstacleAct ||
                         rtDWork.out_narrowLaneAct))) && tmp_4 && tmp_0) || ((((
                         static_cast<uint32_T>(tmp_2) == EGear_D) &&
                        ((rtDWork.in_steeringAct_prev !=
                          rtDWork.in_steeringAct_start) &&
                         rtDWork.in_steeringAct_start)) || ((tmp_7 != tmp_3) &&
                        (static_cast<uint32_T>(tmp_3) == EGear_D) &&
                        rtDWork.out_steeringAct)) && tmp_4 && tmp_0 && tmp_b))
            {
              // Transition: '<S7>:2604'
              // Transition: '<S7>:1069'
              // Transition: '<S7>:1345'
              exit_internal_Available();
              rtDWork.is_Available = IN_FloatingView;
              rtDWork.is_FloatingView = IN_FloatingFrontViews_NotPark;

              // Entry 'FloatingFrontViews_NotPark': '<S7>:1067'
              enter_internal_FloatingFrontVie();

              // '<S7>:1069:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&... 
              // '<S7>:1069:2'  (hasChangedTo(in_obstacleAct, true)||hasChangedTo(in_narrowLaneAct, true) )&&... 
              // '<S7>:1069:3' (in_settingDGearActSts==ESettingSts.Set_OFF || ... 
              // '<S7>:1069:4' ~in_isFirstDGear ||...
              // '<S7>:1069:5' in_haveEverActived)) || ...
              // '<S7>:1069:6' (hasChangedTo(in_gear, EGear.D) && (in_obstacleAct == true||in_narrowLaneAct==true)))&&... 
              // '<S7>:1069:7' (out_SVSShowReq == false||in_SVSShowReq == false) && ... 
              // '<S7>:1069:8' ~in_parkingsts;
              // '<S7>:1345:1' sf_internal_predicateOutput = ((in_gear==EGear.D && hasChangedTo(in_steeringAct, true)) ||... 
              // '<S7>:1345:2' (hasChangedTo(in_gear, EGear.D) && in_steeringAct == true)) && ... 
              // '<S7>:1345:3' (out_SVSShowReq == false||in_SVSShowReq == false) && ... 
              // '<S7>:1345:4' ~in_parkingsts&&...
              // '<S7>:1345:5' ~in_ParkingSearch;
              // '<S7>:1396:1' sf_internal_predicateOutput = in_isViewChangeAvl ... 
              // '<S7>:1396:2' && in_voiceDockRequest ~= EVoiceDockReq.NONE ...
              // '<S7>:1396:3' && hasChanged(in_voiceDockRequest) ...
              // '<S7>:1396:4' && ~in_parkingsts...
              // '<S7>:1396:5' &&in_gear~=EGear.R;
            }
            else if ((*out_isViewChangeAvl) && (static_cast<uint32_T>
                      (rtU.In_voiceDockRequest) != EVoiceDockReq_NONE) &&
                     (rtDWork.in_voiceDockRequest_prev !=
                      rtDWork.in_voiceDockRequest_start) && tmp_0 && (
                      static_cast<uint32_T>(tmp_2) != EGear_R))
            {
              // Transition: '<S7>:1396'
              exit_internal_Available();
              rtDWork.is_Available = IN_Voice_Views;

              // Entry 'Voice_Views': '<S7>:1395'
              enter_internal_Voice_Views();

              // '<S7>:1221:1' sf_internal_predicateOutput = (in_isViewChangeAvl && ... 
              // '<S7>:1221:2' hasChangedTo(in_gear,EGear.R) && ...
              // '<S7>:1221:3' ~in_parkingsts&&...
              // '<S7>:1221:4' ~in(SuperState.SuperStateL2.Available.FloatingView) ) || ... 
              // '<S7>:1221:5' (in_isViewChangeAvl && ...
              // '<S7>:1221:6' in_gear == EGear.R && ...
              // '<S7>:1221:7' hasChangedTo(in_parkingsts, false) &&...
              // '<S7>:1221:8' ~in(SuperState.SuperStateL2.Available.FloatingView) ); 
            }
            else if (((*out_isViewChangeAvl) && ((tmp_7 != tmp_3) && (
                        static_cast<uint32_T>(tmp_3) == EGear_R)) && tmp_0 && (
                       static_cast<int32_T>(rtDWork.is_Available) != 1)) ||
                     ((*out_isViewChangeAvl) && (static_cast<uint32_T>(tmp_2) ==
                       EGear_R) && ((rtDWork.in_parkingsts_prev !=
                        rtDWork.in_parkingsts_start) && tmp_8) &&
                      (static_cast<int32_T>(rtDWork.is_Available) != 1)))
            {
              // Transition: '<S7>:1221'
              exit_internal_Available();
              rtDWork.is_Available = IN_FullScreen_R;

              // Entry 'FullScreen_R': '<S7>:1220'
              enter_internal_FullScreen_R();

              // '<S7>:1488:1' sf_internal_predicateOutput = in_isViewChangeAvl && ... 
              // '<S7>:1488:2' hasChanged(in_gear) && ...
              // '<S7>:1488:3' in_gear ~= EGear.R && ...
              // '<S7>:1488:4' ~in_parkingsts&&...
              // '<S7>:1488:5' ~in_SRIsTopActivity && ...
              // '<S7>:1488:6' (out_SVSShowReq == true&&in_SVSShowReq == true)&&... 
              // '<S7>:1488:7' ~in(SuperState.SuperStateL2.Available.FloatingView)&&... 
              // '<S7>:1488:8' ~(in_gear==EGear.N&&...
              // '<S7>:1488:9' out_SVSViewModeSts==ESVSViewMode.VM_Perspective); 
            }
            else if ((*out_isViewChangeAvl) && (tmp_7 != tmp_3) &&
                     (static_cast<uint32_T>(tmp_2) != EGear_R) && tmp_0 &&
                     (!rtU.In_SRIsTopActivity) && rtY.Out_SVSShowReq &&
                     rtDWork.UnitDelay8 && (static_cast<int32_T>
                      (rtDWork.is_Available) != 1) && ((static_cast<uint32_T>
                       (tmp_2) != EGear_N) || (!(rtY.Out_SVSViewModeSts ==
                        ESVSViewMode_VM_Perspective))))
            {
              // Transition: '<S7>:1488'
              exit_internal_Available();
              rtDWork.is_Available = IN_NotRMode;

              // Entry 'NotRMode': '<S7>:1234'
              enter_internal_NotRMode();

              // '<S7>:2010:1' sf_internal_predicateOutput = hasChangedTo(in_HUViewReq, EScreenID.SINGLE_REAR_NORMAL_ON_ROAD); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start ==
                      EScreenID_SINGLE_REAR_NORMAL_ON_ROAD))
            {
              // Transition: '<S7>:2010'
              exit_internal_Available();
              rtDWork.is_Available = IN_ManualChange;

              // Entry 'ManualChange': '<S7>:38'
              rtDWork.is_ManualChange = IN_ViewGroup_SignleView;

              // Entry 'ViewGroup_SignleView': '<S7>:39'
              // '<S7>:39:3' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
              rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;
              rtDWork.is_ViewGroup_SignleView = IN_Manual_RearView;

              // Entry 'Manual_RearView': '<S7>:670'
              // '<S7>:670:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
              rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

              // '<S7>:670:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
              rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

              // '<S7>:670:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
              rtY.Out_ViewModeGroup = VIEWMODE_REAR;

              // '<S7>:1259:1' sf_internal_predicateOutput = hasChanged(in_HUSVSMode) &&... 
              // '<S7>:1259:2' in_HUSVSMode ~= ESVSViewMode.VM_Default &&...
              // '<S7>:1259:3' ~in_freemodeviewAct;
            }
            else if ((*in_HUSVSMode_prev != rtDWork.in_HUSVSMode_start) &&
                     (rtU.In_HUSVSMode != ESVSViewMode_VM_Default) &&
                     (!*out_freemodeviewAct))
            {
              // Transition: '<S7>:1259'
              exit_internal_Available();
              rtDWork.is_Available = IN_ManualChange;

              // Entry 'ManualChange': '<S7>:38'
              rtDWork.is_ManualChange = IN_ViewModeChanged;

              // Entry 'ViewModeChanged': '<S7>:1258'
              enter_internal_ViewModeChanged();

              // '<S7>:2048:1' sf_internal_predicateOutput = hasChangedTo(in_HUViewReq,EScreenID.SINGLE_FRONT_NORMAL); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start ==
                      EScreenID_SINGLE_FRONT_NORMAL))
            {
              // Transition: '<S7>:2048'
              exit_internal_Available();
              rtDWork.is_Available = IN_ManualChange;

              // Entry 'ManualChange': '<S7>:38'
              rtDWork.is_ManualChange = IN_ViewGroup_SignleView;

              // Entry 'ViewGroup_SignleView': '<S7>:39'
              // '<S7>:39:3' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
              rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;
              rtDWork.is_ViewGroup_SignleView = IN_Manual_FrontView;

              // Entry 'Manual_FrontView': '<S7>:681'
              // '<S7>:681:3' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL; 
              rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

              // '<S7>:681:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL; 
              rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

              // '<S7>:681:5' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
              rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

              // '<S7>:79:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_FR); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_FR))
            {
              // Transition: '<S7>:79'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_FR;

              // '<S7>:74:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_FL); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_FL))
            {
              // Transition: '<S7>:74'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_FL;

              // '<S7>:1256:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_PRI); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_PRI))
            {
              // Transition: '<S7>:1256'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_PRI;

              // '<S7>:1008:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_PFR); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_PFR))
            {
              // Transition: '<S7>:1008'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_PFR;

              // '<S7>:1012:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_PRE); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_PRE))
            {
              // Transition: '<S7>:1012'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_PRE;

              // '<S7>:1255:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_PLE); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_PLE))
            {
              // Transition: '<S7>:1255'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_PLE;

              // '<S7>:76:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_RL); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_RL))
            {
              // Transition: '<S7>:76'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_RL;

              // '<S7>:1074:1' sf_internal_predicateOutput = ((in_isViewChangeAvl && ... 
              // '<S7>:1074:2' in_manualChangeAvail && ...
              // '<S7>:1074:3' hasChangedTo(in_HUViewReq,EScreenID.QUAD_RAW)) ||... 
              // '<S7>:1074:4'   in_isCpcShow) && ...
              // '<S7>:1074:5' in_isCpcFuncOn &&...
              // '<S7>:1074:6' ~in_parkingsts;
            }
            else if ((((*out_isViewChangeAvl) && (*out_manualChangeAvail) &&
                       ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                        (rtDWork.in_HUViewReq_start == EScreenID_QUAD_RAW))) ||
                      (*out_isCpcShow)) && rtU.In_isCpcFuncOn && tmp_0)
            {
              // Transition: '<S7>:1074'
              exit_internal_Available();
              rtDWork.is_Available = IN_ManualChange;

              // Entry 'ManualChange': '<S7>:38'
              rtDWork.is_ManualChange = IN_ECAL_CPC;

              // Entry 'ECAL_CPC': '<S7>:1073'
              // '<S7>:1073:2' out_displayedView = EScreenID.QUAD_RAW;
              rtY.Out_displayedView = EScreenID_QUAD_RAW;

              // '<S7>:1073:3' out_SVSCurrentView = EScreenID.QUAD_RAW;
              rtY.Out_SVSCurrentView = EScreenID_QUAD_RAW;

              // '<S7>:1073:4' local_bool_initialized = false;

              // '<S7>:77:1' sf_internal_predicateOutput =  hasChangedTo(in_HUViewReq,EScreenID.PERSPECTIVE_RR); 
            }
            else if ((*in_HUViewReq_prev != rtDWork.in_HUViewReq_start) &&
                     (rtDWork.in_HUViewReq_start == EScreenID_PERSPECTIVE_RR))
            {
              // Transition: '<S7>:77'
              exit_internal_Available();
              rtDWork.is_Available = IN_temp_RR;

              // '<S7>:697:1' sf_internal_predicateOutput = in_freemodeviewAct;
            }
            else if (*out_freemodeviewAct)
            {
              // Transition: '<S7>:697'
              //  in free view mode
              exit_internal_Available();
              rtDWork.is_Available = IN_FreeViewID;

              // Entry 'FreeViewID': '<S7>:696'
              // '<S7>:696:3' out_SVSCurrentView = in_freemodeviewID;
              rtY.Out_SVSCurrentView = rtDWork.out_freemodeviewID;

              // '<S7>:696:4' out_displayedView =  in_freemodeviewID;
              rtY.Out_displayedView = rtDWork.out_freemodeviewID;

              // '<S7>:696:5' out_SVSViewModeSts = ESVSViewMode.VM_Perspective;
              rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;

              // '<S7>:2694:1' sf_internal_predicateOutput = hasChangedTo(in_isFreeParking,true); 
            }
            else if ((rtDWork.in_isFreeParking_prev !=
                      rtDWork.in_isFreeParking_start) &&
                     rtDWork.in_isFreeParking_start)
            {
              // Transition: '<S7>:2694'
              exit_internal_Available();
              rtDWork.is_Available = IN_Free_Parking;

              // Entry 'Free_Parking': '<S7>:2693'
              // '<S7>:2693:3' out_displayedView = EScreenID.FLOAT_FREE_PARKING_PLAN_VIEW; 
              rtY.Out_displayedView = EScreenID_FLOAT_FREE_PARKING_PLAN_VIEW;

              // '<S7>:2693:4' out_SVSCurrentView = EScreenID.FLOAT_FREE_PARKING_PLAN_VIEW; 
              rtY.Out_SVSCurrentView = EScreenID_FLOAT_FREE_PARKING_PLAN_VIEW;

              // '<S7>:2693:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
              rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;
            }
            else
            {
              switch (rtDWork.is_Available)
              {
               case IN_FloatingView:
                FloatingView();
                break;

               case IN_FreeViewID:
                // During 'FreeViewID': '<S7>:696'
                // '<S7>:696:3' out_SVSCurrentView = in_freemodeviewID;
                rtY.Out_SVSCurrentView = rtDWork.out_freemodeviewID;

                // '<S7>:696:4' out_displayedView =  in_freemodeviewID;
                rtY.Out_displayedView = rtDWork.out_freemodeviewID;

                // '<S7>:696:5' out_SVSViewModeSts = ESVSViewMode.VM_Perspective; 
                rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Perspective;
                break;

               case IN_Free_Parking:
                // During 'Free_Parking': '<S7>:2693'
                break;

               case IN_FullScreenViews_From_FloatSc:
                // During 'FullScreenViews_From_FloatScreenViews': '<S7>:2406'
                if (static_cast<uint32_T>
                    (rtDWork.is_FullScreenViews_From_FloatSc) ==
                    IN_FrontView_From_FloatFrontVie)
                {
                  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

                  // During 'FrontView_From_FloatFrontView': '<S7>:2408'
                }
                else
                {
                  rtY.Out_ViewModeGroup = VIEWMODE_REAR;

                  // During 'RearView_From_FloatRearView': '<S7>:2407'
                }
                break;

               case IN_FullScreen_R:
                // During 'FullScreen_R': '<S7>:1220'
                switch (rtDWork.is_FullScreen_R)
                {
                 case IN_GearR_NoViewChange:
                  // During 'GearR_NoViewChange': '<S7>:1224'
                  break;

                 case IN_GearR_PersFrontView:
                  rtY.Out_ViewModeGroup = VIEWMODE_REAR;

                  // During 'GearR_PersFrontView': '<S7>:1492'
                  break;

                 case IN_GearR_RearView:
                  rtY.Out_ViewModeGroup = VIEWMODE_REAR;

                  // During 'GearR_RearView': '<S7>:2399'
                  break;

                 case IN_GearR_RearWheelView:
                  rtY.Out_ViewModeGroup = VIEWMODE_REAR;

                  // During 'GearR_RearWheelView': '<S7>:1494'
                  break;

                 default:
                  rtY.Out_ViewModeGroup = VIEWMODE_REAR;

                  // During 'GearR_RearWideView': '<S7>:1493'
                  break;
                }
                break;

               case IN_LastView:
                // During 'LastView': '<S7>:324'
                break;

               case IN_Mannually_Active_First:
                rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

                // During 'Mannually_Active_First': '<S7>:2645'
                break;

               case IN_ManualChange:
                ManualChange();
                break;

               case IN_NotRMode:
                // During 'NotRMode': '<S7>:1234'
                switch (rtDWork.is_NotRMode)
                {
                 case IN_GearD_FrontView:
                  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

                  // During 'GearD_FrontView': '<S7>:1525'
                  break;

                 case IN_GearD_FrontWheelView:
                  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

                  // During 'GearD_FrontWheelView': '<S7>:1518'
                  break;

                 case IN_GearD_FrontWideView:
                  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

                  // During 'GearD_FrontWideView': '<S7>:1524'
                  break;

                 case IN_GearD_NoViewChange:
                  // During 'GearD_NoViewChange': '<S7>:2405'
                  break;

                 case IN_GearD_PersRearView:
                  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

                  // During 'GearD_PersRearView': '<S7>:1517'
                  break;

                 default:
                  // During 'NoViewChangeByGearChanged': '<S7>:1558'
                  break;
                }
                break;

               case IN_Voice_Views:
                Voice_Views();
                break;

               case IN_temp_FL:
                temp_FL();
                break;

               case IN_temp_FR:
                temp_FR();
                break;

               case IN_temp_PFR:
                temp_PFR();
                break;

               case IN_temp_PLE:
                temp_PLE();
                break;

               case IN_temp_PRE:
                temp_PRE();
                break;

               case IN_temp_PRI:
                temp_PRI();
                break;

               case IN_temp_RL:
                temp_RL();
                break;

               default:
                temp_RR();
                break;
              }
            }
          }
        }

        if (guard3)
        {
          exit_internal_Available();
          rtDWork.is_Available = IN_FloatingView;
          rtDWork.is_FloatingView = IN_FloatingRearViews_NotPark;
          rtDWork.temporalCounter_i2 = 0U;

          // Entry 'FloatingRearViews_NotPark': '<S7>:2581'
          enter_internal_FloatingRearView();
        }

        if (guard2)
        {
          exit_internal_Available();
          rtDWork.is_Available = IN_NotRMode;

          // Entry 'NotRMode': '<S7>:1234'
          rtDWork.is_NotRMode = IN_GearD_FrontView;

          // Entry 'GearD_FrontView': '<S7>:1525'
          // '<S7>:1525:3' out_displayedView = EScreenID.SINGLE_FRONT_NORMAL;
          rtY.Out_displayedView = EScreenID_SINGLE_FRONT_NORMAL;

          // '<S7>:1525:4' out_SVSCurrentView = EScreenID.SINGLE_FRONT_NORMAL;
          rtY.Out_SVSCurrentView = EScreenID_SINGLE_FRONT_NORMAL;

          // '<S7>:1525:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

          // '<S7>:1525:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_FRONT;
          rtY.Out_ViewModeGroup = VIEWMODE_FRONT;
        }

        if (guard1)
        {
          exit_internal_Available();
          rtDWork.is_Available = IN_FullScreen_R;

          // Entry 'FullScreen_R': '<S7>:1220'
          rtDWork.is_FullScreen_R = IN_GearR_RearView;

          // Entry 'GearR_RearView': '<S7>:2399'
          // '<S7>:2399:3' out_displayedView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
          rtY.Out_displayedView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

          // '<S7>:2399:4' out_SVSCurrentView = EScreenID.SINGLE_REAR_NORMAL_ON_ROAD; 
          rtY.Out_SVSCurrentView = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;

          // '<S7>:2399:5' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
          rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

          // '<S7>:2399:6' out_ViewModeGroup=EViewModeGroup.VIEWMODE_REAR;
          rtY.Out_ViewModeGroup = VIEWMODE_REAR;
        }
      }

      // End of Inport: '<Root>/In_PowerSaveMode'
    }

    // End of Inport: '<Root>/In_vehSpeed'

    // End of Inport: '<Root>/In_AVMError'
  }

  // End of Inport: '<Root>/In_FID_SVSEcuInternalStatus'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::SuperState(const boolean_T
  *out_manualChangeAvail, const boolean_T *out_isViewChangeAvl, const boolean_T *
  out_freemodeviewAct, const boolean_T *out_isCpcShow, const ESVSViewMode
  *in_HUSVSMode_prev, const EScreenID *in_HUViewReq_prev)
{
  // During 'SuperState': '<S7>:792'
  if (static_cast<uint32_T>(rtDWork.is_SuperState) == IN_SuperStateL2)
  {
    // During 'SuperStateL2': '<S7>:27'
    if (static_cast<uint32_T>(rtDWork.is_SuperStateL2) == IN_Available)
    {
      Available(out_manualChangeAvail, out_isViewChangeAvl, out_freemodeviewAct,
                out_isCpcShow, in_HUSVSMode_prev, in_HUViewReq_prev);
    }
    else
    {
      // Outport: '<Root>/Out_systemAvailable'
      rtY.Out_systemAvailable = ESystem_Unavailable;

      // Inport: '<Root>/In_vehSpeed' incorporates:
      //   Inport: '<Root>/In_AVMError'
      //   Inport: '<Root>/In_FID_SVSEcuInternalStatus'
      //   Inport: '<Root>/In_PowerSaveMode'
      //   Inport: '<Root>/In_gear'
      //   Inport: '<Root>/In_parkstatus'
      //   Inport: '<Root>/In_speedTrigIn'

      // During 'Unavailable': '<S7>:28'
      // '<S7>:31:1' sf_internal_predicateOutput = (in_vehSpeed <= in_speedTrigIn ||in_parkingsts||in_gear==EGear.R)&&... 
      // '<S7>:31:2' (~in_AVMError)&& ...
      // '<S7>:31:3' in_FID_SVSEcuInternalStatus&&...
      // '<S7>:31:4' ~in_PowerSaveMode;
      if (((rtU.In_vehSpeed <= rtU.In_speedTrigIn) || rtU.In_parkstatus || (
            static_cast<uint32_T>(rtU.In_gear) == EGear_R)) && (!rtU.In_AVMError)
          && rtU.In_FID_SVSEcuInternalStatus && (!rtU.In_PowerSaveMode))
      {
        // Transition: '<S7>:31'
        exit_internal_Unavailable();
        rtDWork.is_SuperStateL2 = IN_Available;

        // Outport: '<Root>/Out_systemAvailable'
        // Entry 'Available': '<S7>:30'
        // '<S7>:30:3' out_systemAvailable = ESystem.Available;
        rtY.Out_systemAvailable = ESystem_Available;

        // Outport: '<Root>/Out_SVSUnavlMsgs'
        // '<S7>:30:4' out_SVSUnavlMsgs = ENotActiveReason.NONE;
        rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

        // '<S7>:30:5' out_voiceDockFeedback = EVoiceDockFb.NONE;
        rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
        rtDWork.is_Available = IN_LastView;

        // Entry 'LastView': '<S7>:324'
        // out_displayedView = in_SVSViewLast;
        // out_SVSCurrentView = in_SVSViewLast;
        // out_SVSViewModeSts = in_SVSViewModelLast;

        // '<S7>:2365:1' sf_internal_predicateOutput = hasChanged(in_PowerSaveMode) || ... 
        // '<S7>:2365:2' hasChanged(in_AVMError);
      }
      else if ((rtDWork.in_PowerSaveMode_prev != rtDWork.in_PowerSaveMode_start)
               || (rtDWork.in_AVMError_prev != rtDWork.in_AVMError_start))
      {
        // Transition: '<S7>:2365'
        exit_internal_Unavailable();
        rtDWork.is_SuperStateL2 = IN_Unavailable;

        // Outport: '<Root>/Out_systemAvailable'
        // Entry 'Unavailable': '<S7>:28'
        // '<S7>:28:3' out_systemAvailable = ESystem.Unavailable;
        rtY.Out_systemAvailable = ESystem_Unavailable;

        // Outport: '<Root>/Out_systemActive'
        // '<S7>:28:4' out_systemActive = false;
        rtY.Out_systemActive = false;

        // Update for Outport: '<Root>/Out_SVSShowReq'
        // out_displayedView = EScreenID.NO_CHANGE;
        // out_SVSCurrentView = EScreenID.NO_CHANGE;
        // '<S7>:28:7' out_SVSShowReq = false;
        rtY.Out_SVSShowReq = false;

        // out_SVSUnavlMsgs = uint8(2);
        enter_internal_Unavailable();
      }
      else
      {
        switch (rtDWork.is_Unavailable)
        {
         case IN_AVM_Error:
          {
            // During 'AVM_Error': '<S7>:2306'
            switch (rtDWork.is_AVM_Error)
            {
             case IN_AVM_Error_Activate_Triggered:
              // Outport: '<Root>/Out_SVSUnavlMsgs'
              rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

              // Inport: '<Root>/In_timeStepScaleFactor'
              // During 'AVM_Error_Activate_Triggered': '<S7>:2314'
              // '<S7>:2311:1' sf_internal_predicateOutput = after(1 * in_timeStepScaleFactor, sec); 
              if (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::ceil
                   (rtU.In_timeStepScaleFactor * 100.0)))
              {
                // Transition: '<S7>:2311'
                rtDWork.is_AVM_Error = IN_AVM_Error_No_Activate_Trigge;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                // Entry 'AVM_Error_No_Activate_Triggered': '<S7>:2315'
                // '<S7>:2315:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // '<S7>:2315:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
                rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
              }
              break;

             case IN_AVM_Error_Activate_Voice:
              // Outport: '<Root>/Out_SVSUnavlMsgs'
              rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

              // Inport: '<Root>/In_timeStepScaleFactor'
              // During 'AVM_Error_Activate_Voice': '<S7>:2382'
              // '<S7>:2386:1' sf_internal_predicateOutput = after(1 * in_timeStepScaleFactor, sec); 
              if (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::ceil
                   (rtU.In_timeStepScaleFactor * 100.0)))
              {
                // Transition: '<S7>:2386'
                rtDWork.is_AVM_Error = IN_AVM_Error_No_Activate_Trigge;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                // Entry 'AVM_Error_No_Activate_Triggered': '<S7>:2315'
                // '<S7>:2315:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // '<S7>:2315:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
                rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
              }
              break;

             default:
              {
                EDockAvmButtonPress tmp_0;
                EGear tmp;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // During 'AVM_Error_No_Activate_Triggered': '<S7>:2315'
                // '<S7>:2312:1' sf_internal_predicateOutput = ((hasChangedTo(in_androidIconActive,true))... 
                // '<S7>:2312:2' || (hasChangedTo(in_gear,EGear.R))...
                // '<S7>:2312:3' || (hasChangedTo(in_steeringWheelButtonPressed,true))... 
                // '<S7>:2312:4' || (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS))) && ... 
                // '<S7>:2312:5' in_SystemStr==ESystemStr.SYSTEM_STR_NONE;
                tmp = rtDWork.in_gear_start;
                tmp_0 = rtDWork.in_dockAvmButtonPress_start;

                // Inport: '<Root>/In_SystemStr'
                if ((((rtDWork.in_androidIconActive_prev !=
                       rtDWork.in_androidIconActive_start) &&
                      rtDWork.in_androidIconActive_start) ||
                     ((rtDWork.in_gear_prev != tmp) && (static_cast<uint32_T>
                       (tmp) == EGear_R)) ||
                     ((rtDWork.in_steeringWheelButtonPressed_p !=
                       rtDWork.in_steeringWheelButtonPressed_s) &&
                      rtDWork.in_steeringWheelButtonPressed_s) ||
                     ((rtDWork.in_dockAvmButtonPress_prev != tmp_0) && (
                       static_cast<uint32_T>(tmp_0) ==
                       EDockAvmButtonPress_AVM_PRESS))) && (rtU.In_SystemStr ==
                     ESystemStr_SYSTEM_STR_NONE))
                {
                  // Transition: '<S7>:2312'
                  rtDWork.is_AVM_Error = IN_AVM_Error_Activate_Triggered;
                  rtDWork.temporalCounter_i2 = 0U;

                  // Outport: '<Root>/Out_SVSUnavlMsgs'
                  // Entry 'AVM_Error_Activate_Triggered': '<S7>:2314'
                  // '<S7>:2314:3' out_SVSUnavlMsgs = ENotActiveReason.QNX_AVM_ERROR; 
                  rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

                  // Inport: '<Root>/In_voiceDockRequest'
                  // '<S7>:2383:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE&& hasChanged(in_voiceDockRequest))&& ... 
                  // '<S7>:2383:2' in_SystemStr==ESystemStr.SYSTEM_STR_NONE;
                }
                else if ((static_cast<uint32_T>(rtU.In_voiceDockRequest) !=
                          EVoiceDockReq_NONE) &&
                         (rtDWork.in_voiceDockRequest_prev !=
                          rtDWork.in_voiceDockRequest_start) &&
                         (rtU.In_SystemStr == ESystemStr_SYSTEM_STR_NONE))
                {
                  // Transition: '<S7>:2383'
                  rtDWork.is_AVM_Error = IN_AVM_Error_Activate_Voice;
                  rtDWork.temporalCounter_i2 = 0U;

                  // Outport: '<Root>/Out_SVSUnavlMsgs'
                  // Entry 'AVM_Error_Activate_Voice': '<S7>:2382'
                  // '<S7>:2382:3' out_SVSUnavlMsgs = ENotActiveReason.QNX_AVM_ERROR; 
                  rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

                  // '<S7>:2382:4' out_voiceDockFeedback = EVoiceDockFb.OPEN_FAIL_AVM_ERROR; 
                  rtDWork.out_voiceDockFeedback_b =
                    EVoiceDockFb_OPEN_FAIL_AVM_ERROR;
                }
                else
                {
                  // no actions
                }

                // End of Inport: '<Root>/In_SystemStr'
              }
              break;
            }
          }
          break;

         case IN_Power_Save_Mode:
          {
            // During 'Power_Save_Mode': '<S7>:2341'
            switch (rtDWork.is_Power_Save_Mode)
            {
             case IN_Power_Save_Mode_Activate_Tri:
              // Outport: '<Root>/Out_SVSUnavlMsgs'
              rtY.Out_SVSUnavlMsgs = ENotActiveReason_ADCU_IN_SLEEP;

              // Inport: '<Root>/In_timeStepScaleFactor'
              // During 'Power_Save_Mode_Activate_Triggered': '<S7>:2344'
              // '<S7>:2343:1' sf_internal_predicateOutput = after(1 * in_timeStepScaleFactor, sec); 
              if (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::ceil
                   (rtU.In_timeStepScaleFactor * 100.0)))
              {
                // Transition: '<S7>:2343'
                rtDWork.is_Power_Save_Mode = IN_Power_Save_Mode_No_Activate_;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                // Entry 'Power_Save_Mode_No_Activate_Triggered': '<S7>:2345'
                // '<S7>:2345:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // '<S7>:2345:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
                rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
              }
              break;

             case IN_Power_Save_Mode_Activate_T_g:
              // Outport: '<Root>/Out_SVSUnavlMsgs'
              rtY.Out_SVSUnavlMsgs = ENotActiveReason_ADCU_IN_SLEEP;

              // Inport: '<Root>/In_timeStepScaleFactor'
              // During 'Power_Save_Mode_Activate_Triggered1': '<S7>:2388'
              // '<S7>:2390:1' sf_internal_predicateOutput = after(1 * in_timeStepScaleFactor, sec); 
              if (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::ceil
                   (rtU.In_timeStepScaleFactor * 100.0)))
              {
                // Transition: '<S7>:2390'
                rtDWork.is_Power_Save_Mode = IN_Power_Save_Mode_No_Activate_;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                // Entry 'Power_Save_Mode_No_Activate_Triggered': '<S7>:2345'
                // '<S7>:2345:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // '<S7>:2345:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
                rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
              }
              break;

             default:
              {
                EDockAvmButtonPress tmp_0;
                EGear tmp;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // During 'Power_Save_Mode_No_Activate_Triggered': '<S7>:2345'
                // '<S7>:2346:1' sf_internal_predicateOutput = (hasChangedTo(in_androidIconActive,true))... 
                // '<S7>:2346:2' || (hasChangedTo(in_gear,EGear.R))...
                // '<S7>:2346:3' || (hasChangedTo(in_steeringWheelButtonPressed,true))... 
                // '<S7>:2346:4' || (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)); 
                tmp = rtDWork.in_gear_start;
                tmp_0 = rtDWork.in_dockAvmButtonPress_start;
                if (((rtDWork.in_androidIconActive_prev !=
                      rtDWork.in_androidIconActive_start) &&
                     rtDWork.in_androidIconActive_start) ||
                    ((rtDWork.in_gear_prev != tmp) && (static_cast<uint32_T>(tmp)
                      == EGear_R)) || ((rtDWork.in_steeringWheelButtonPressed_p
                                        !=
                                        rtDWork.in_steeringWheelButtonPressed_s)
                     && rtDWork.in_steeringWheelButtonPressed_s) ||
                    ((rtDWork.in_dockAvmButtonPress_prev != tmp_0) && (
                      static_cast<uint32_T>(tmp_0) ==
                      EDockAvmButtonPress_AVM_PRESS)))
                {
                  // Transition: '<S7>:2346'
                  rtDWork.is_Power_Save_Mode = IN_Power_Save_Mode_Activate_Tri;
                  rtDWork.temporalCounter_i2 = 0U;

                  // Outport: '<Root>/Out_SVSUnavlMsgs'
                  // Entry 'Power_Save_Mode_Activate_Triggered': '<S7>:2344'
                  // '<S7>:2344:3' out_SVSUnavlMsgs = ENotActiveReason.ADCU_IN_SLEEP; 
                  rtY.Out_SVSUnavlMsgs = ENotActiveReason_ADCU_IN_SLEEP;

                  // Inport: '<Root>/In_voiceDockRequest'
                  // '<S7>:2389:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE&& hasChanged(in_voiceDockRequest)); 
                }
                else if ((static_cast<uint32_T>(rtU.In_voiceDockRequest) !=
                          EVoiceDockReq_NONE) &&
                         (rtDWork.in_voiceDockRequest_prev !=
                          rtDWork.in_voiceDockRequest_start))
                {
                  // Transition: '<S7>:2389'
                  rtDWork.is_Power_Save_Mode = IN_Power_Save_Mode_Activate_T_g;
                  rtDWork.temporalCounter_i2 = 0U;

                  // Outport: '<Root>/Out_SVSUnavlMsgs'
                  // Entry 'Power_Save_Mode_Activate_Triggered1': '<S7>:2388'
                  // '<S7>:2388:3' out_SVSUnavlMsgs = ENotActiveReason.ADCU_IN_SLEEP; 
                  rtY.Out_SVSUnavlMsgs = ENotActiveReason_ADCU_IN_SLEEP;

                  // '<S7>:2388:4' out_voiceDockFeedback = EVoiceDockFb.OPEN_FAIL_POWER_SAVEMODE; 
                  rtDWork.out_voiceDockFeedback_b =
                    EVoiceDockFb_OPEN_FAIL_POWER_SAVEMODE;
                }
                else
                {
                  // no actions
                }
              }
              break;
            }
          }
          break;

         case IN_spdtoohigh:
          {
            // During 'spdtoohigh': '<S7>:1244'
            switch (rtDWork.is_spdtoohigh)
            {
             case IN_SPD_Activate_Triggered:
              // Outport: '<Root>/Out_SVSUnavlMsgs'
              rtY.Out_SVSUnavlMsgs = ENotActiveReason_SPEED_TOO_HIGH;

              // Inport: '<Root>/In_timeStepScaleFactor'
              // During 'SPD_Activate_Triggered': '<S7>:2298'
              // '<S7>:2301:1' sf_internal_predicateOutput = after(1 * in_timeStepScaleFactor, sec); 
              if (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::ceil
                   (rtU.In_timeStepScaleFactor * 100.0)))
              {
                // Transition: '<S7>:2301'
                rtDWork.is_spdtoohigh = IN_SPD_No_Activate_Triggered;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                // Entry 'SPD_No_Activate_Triggered': '<S7>:2300'
                // '<S7>:2300:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // '<S7>:2300:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
                rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
              }
              break;

             case IN_SPD_Activate_Voice:
              // Outport: '<Root>/Out_SVSUnavlMsgs'
              rtY.Out_SVSUnavlMsgs = ENotActiveReason_SPEED_TOO_HIGH;

              // Inport: '<Root>/In_timeStepScaleFactor'
              // During 'SPD_Activate_Voice': '<S7>:2378'
              // '<S7>:2380:1' sf_internal_predicateOutput = after(1 * in_timeStepScaleFactor, sec); 
              if (rtDWork.temporalCounter_i2 >= static_cast<uint32_T>(std::ceil
                   (rtU.In_timeStepScaleFactor * 100.0)))
              {
                // Transition: '<S7>:2380'
                rtDWork.is_spdtoohigh = IN_SPD_No_Activate_Triggered;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                // Entry 'SPD_No_Activate_Triggered': '<S7>:2300'
                // '<S7>:2300:3' out_SVSUnavlMsgs = ENotActiveReason.NONE;
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // '<S7>:2300:4' out_voiceDockFeedback = EVoiceDockFb.NONE;
                rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
              }
              break;

             default:
              {
                EDockAvmButtonPress tmp_0;
                EGear tmp;

                // Outport: '<Root>/Out_SVSUnavlMsgs'
                rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

                // During 'SPD_No_Activate_Triggered': '<S7>:2300'
                // '<S7>:2303:1' sf_internal_predicateOutput = (hasChangedTo(in_androidIconActive,true))... 
                // '<S7>:2303:2' || (hasChangedTo(in_gear,EGear.R))...
                // '<S7>:2303:3' || (hasChangedTo(in_steeringWheelButtonPressed,true))... 
                // '<S7>:2303:4' || (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)); 
                tmp = rtDWork.in_gear_start;
                tmp_0 = rtDWork.in_dockAvmButtonPress_start;
                if (((rtDWork.in_androidIconActive_prev !=
                      rtDWork.in_androidIconActive_start) &&
                     rtDWork.in_androidIconActive_start) ||
                    ((rtDWork.in_gear_prev != tmp) && (static_cast<uint32_T>(tmp)
                      == EGear_R)) || ((rtDWork.in_steeringWheelButtonPressed_p
                                        !=
                                        rtDWork.in_steeringWheelButtonPressed_s)
                     && rtDWork.in_steeringWheelButtonPressed_s) ||
                    ((rtDWork.in_dockAvmButtonPress_prev != tmp_0) && (
                      static_cast<uint32_T>(tmp_0) ==
                      EDockAvmButtonPress_AVM_PRESS)))
                {
                  // Transition: '<S7>:2303'
                  rtDWork.is_spdtoohigh = IN_SPD_Activate_Triggered;
                  rtDWork.temporalCounter_i2 = 0U;

                  // Outport: '<Root>/Out_SVSUnavlMsgs'
                  // Entry 'SPD_Activate_Triggered': '<S7>:2298'
                  // '<S7>:2298:3' out_SVSUnavlMsgs = ENotActiveReason.SPEED_TOO_HIGH; 
                  rtY.Out_SVSUnavlMsgs = ENotActiveReason_SPEED_TOO_HIGH;

                  // Inport: '<Root>/In_voiceDockRequest'
                  // '<S7>:2379:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE&& hasChanged(in_voiceDockRequest)); 
                }
                else if ((static_cast<uint32_T>(rtU.In_voiceDockRequest) !=
                          EVoiceDockReq_NONE) &&
                         (rtDWork.in_voiceDockRequest_prev !=
                          rtDWork.in_voiceDockRequest_start))
                {
                  // Transition: '<S7>:2379'
                  rtDWork.is_spdtoohigh = IN_SPD_Activate_Voice;
                  rtDWork.temporalCounter_i2 = 0U;

                  // Outport: '<Root>/Out_SVSUnavlMsgs'
                  // Entry 'SPD_Activate_Voice': '<S7>:2378'
                  // '<S7>:2378:3' out_SVSUnavlMsgs = ENotActiveReason.SPEED_TOO_HIGH; 
                  rtY.Out_SVSUnavlMsgs = ENotActiveReason_SPEED_TOO_HIGH;

                  // '<S7>:2378:4' out_voiceDockFeedback = EVoiceDockFb.OPEN_FAIL_SPDHIGH; 
                  rtDWork.out_voiceDockFeedback_b =
                    EVoiceDockFb_OPEN_FAIL_SPDHIGH;
                }
                else
                {
                  // no actions
                }
              }
              break;
            }
          }
          break;

         default:
          // Outport: '<Root>/Out_SVSUnavlMsgs'
          rtY.Out_SVSUnavlMsgs = ENotActiveReason_QNX_AVM_ERROR;

          // During 'vidouterror': '<S7>:2325'
          break;
        }
      }
    }
  }
  else
  {
    // Outport: '<Root>/Out_systemAvailable'
    rtY.Out_systemAvailable = ESystem_Unavailable;

    // Outport: '<Root>/Out_SVSUnavlMsgs'
    rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

    // Inport: '<Root>/In_smDelay'
    // During 'start': '<S7>:793'
    // '<S7>:794:1' sf_internal_predicateOutput = after(in_smDelay, msec);
    if (static_cast<uint32_T>(static_cast<int32_T>(static_cast<int32_T>
          (rtDWork.temporalCounter_i2) * 10)) >= rtU.In_smDelay)
    {
      // Transition: '<S7>:794'
      rtDWork.is_SuperState = IN_SuperStateL2;

      // Inport: '<Root>/In_vehSpeed' incorporates:
      //   Inport: '<Root>/In_FID_SVSEcuInternalStatus'
      //   Inport: '<Root>/In_parkstatus'
      //   Inport: '<Root>/In_speedTrigIn'

      // Entry Internal 'SuperStateL2': '<S7>:27'
      // Transition: '<S7>:925'
      // '<S7>:926:1' sf_internal_predicateOutput = (in_vehSpeed <= in_speedTrigIn ||... 
      // '<S7>:926:2'  in_parkingsts) && ...
      // '<S7>:926:3' in_FID_SVSEcuInternalStatus;
      if (((rtU.In_vehSpeed <= rtU.In_speedTrigIn) || rtU.In_parkstatus) &&
          rtU.In_FID_SVSEcuInternalStatus)
      {
        // Transition: '<S7>:926'
        rtDWork.is_SuperStateL2 = IN_Available;

        // Outport: '<Root>/Out_systemAvailable'
        // Entry 'Available': '<S7>:30'
        // '<S7>:30:3' out_systemAvailable = ESystem.Available;
        rtY.Out_systemAvailable = ESystem_Available;

        // Outport: '<Root>/Out_SVSUnavlMsgs'
        // '<S7>:30:4' out_SVSUnavlMsgs = ENotActiveReason.NONE;
        rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

        // '<S7>:30:5' out_voiceDockFeedback = EVoiceDockFb.NONE;
        rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_NONE;
        rtDWork.is_Available = IN_LastView;

        // Entry 'LastView': '<S7>:324'
        // out_displayedView = in_SVSViewLast;
        // out_SVSCurrentView = in_SVSViewLast;
        // out_SVSViewModeSts = in_SVSViewModelLast;
      }
      else
      {
        // Transition: '<S7>:29'
        rtDWork.is_SuperStateL2 = IN_Unavailable;

        // Outport: '<Root>/Out_systemAvailable'
        // Entry 'Unavailable': '<S7>:28'
        // '<S7>:28:3' out_systemAvailable = ESystem.Unavailable;
        rtY.Out_systemAvailable = ESystem_Unavailable;

        // Outport: '<Root>/Out_systemActive'
        // '<S7>:28:4' out_systemActive = false;
        rtY.Out_systemActive = false;

        // Update for Outport: '<Root>/Out_SVSShowReq'
        // out_displayedView = EScreenID.NO_CHANGE;
        // out_SVSCurrentView = EScreenID.NO_CHANGE;
        // '<S7>:28:7' out_SVSShowReq = false;
        rtY.Out_SVSShowReq = false;

        // out_SVSUnavlMsgs = uint8(2);
        enter_internal_Unavailable();
      }
    }

    // End of Inport: '<Root>/In_smDelay'
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Steering_Activated(void)
{
  // Update for Outport: '<Root>/Out_ShowReqMod'
  rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

  // Inport: '<Root>/In_parkstatus'
  // During 'Steering_Activated': '<S7>:1743'
  // '<S7>:2103:1' sf_internal_predicateOutput = in_parkingsts == true;
  if (rtU.In_parkstatus)
  {
    // Transition: '<S7>:2103'
    // Exit Internal 'Steering_Activated': '<S7>:1743'
    // Exit Internal 'Steering_exitHandling': '<S7>:2105'
    rtDWork.is_Steering_exitHandling = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Normal_activate = IN_ParkActiveHandling;
    rtDWork.is_ParkActiveHandling = IN_Park_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Park_Activated': '<S7>:828'
    // '<S7>:828:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:828:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK;
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;
  }
  else
  {
    EVoiceDockReq tmp_1;
    boolean_T out;

    // Inport: '<Root>/In_voiceDockRequest'
    // '<S7>:2593:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
    // '<S7>:2593:2' && hasChanged(in_voiceDockRequest))...
    // '<S7>:2593:3' &&~((in_gear==EGear.D)...
    // '<S7>:2593:4' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR...
    // '<S7>:2593:5' ||in_voiceDockRequest==EVoiceDockReq.OPEN_REARWIDE...
    // '<S7>:2593:6' ||in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL)) ... 
    // '<S7>:2593:7' ||  (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
    // '<S7>:2593:8' && in(SuperState.SuperStateL2.Available))...
    // '<S7>:2593:9' || (hasChangedTo(in_androidIconActive,true)...
    // '<S7>:2593:10' && in(SuperState.SuperStateL2.Available))...
    // '<S7>:2593:11' || (hasChangedTo(in_steeringWheelButtonPressed,true)...
    // '<S7>:2593:12' && in(SuperState.SuperStateL2.Available))||hasChangedTo(in_EnlargeButtonPressed,true); 
    tmp_1 = rtU.In_voiceDockRequest;
    if ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_NONE) &&
        (rtDWork.in_voiceDockRequest_prev != rtDWork.in_voiceDockRequest_start) &&
        ((static_cast<uint32_T>(rtU.In_gear) != EGear_D) ||
         ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REAR) && (
           static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REARWIDE) && (
           static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REARWHEEL))))
    {
      out = true;
    }
    else
    {
      EDockAvmButtonPress out_tmp;
      out_tmp = rtDWork.in_dockAvmButtonPress_start;
      out = (((rtDWork.in_dockAvmButtonPress_prev != out_tmp) &&
              (static_cast<uint32_T>(out_tmp) == EDockAvmButtonPress_AVM_PRESS) &&
              (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_androidIconActive_prev !=
               rtDWork.in_androidIconActive_start) &&
              rtDWork.in_androidIconActive_start && (static_cast<int32_T>
               (rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_steeringWheelButtonPressed_p !=
               rtDWork.in_steeringWheelButtonPressed_s) &&
              rtDWork.in_steeringWheelButtonPressed_s && (static_cast<int32_T>
               (rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_EnlargeButtonPressed_prev !=
               rtDWork.in_EnlargeButtonPressed_start) &&
              rtDWork.in_EnlargeButtonPressed_start));
    }

    if (out)
    {
      // Transition: '<S7>:2593'
      // Transition: '<S7>:2594'
      // Transition: '<S7>:2600'
      // Transition: '<S7>:2601'
      // Transition: '<S7>:2598'
      // Exit Internal 'Steering_Activated': '<S7>:1743'
      // Exit Internal 'Steering_exitHandling': '<S7>:2105'
      rtDWork.is_Steering_exitHandling = IN_NO_ACTIVE_CHILD;
      rtDWork.is_Normal_activate = IN_Manually_Activate_SVS;

      // Outport: '<Root>/Out_systemActive'
      // Entry 'Manually_Activate_SVS': '<S7>:1564'
      //  VoiceDock, AndroidIcon, SteerButton
      // '<S7>:1564:4' out_systemActive = true;
      rtY.Out_systemActive = true;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // '<S7>:1564:5' out_SVSShowReq = true;
      rtY.Out_SVSShowReq = true;

      // Update for Outport: '<Root>/Out_ShowReqMod'
      // '<S7>:1564:6' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
      rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

      // Entry Internal 'Manually_Activate_SVS': '<S7>:1564'
      // Transition: '<S7>:2181'
      // Entry 'Manually__Activate_ExitHandling': '<S7>:2179'
      // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
      // Entry Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
      // Transition: '<S7>:2184'
      rtDWork.temporalCounter_i6 = 0U;
    }
    else
    {
      // During 'Steering_exitHandling': '<S7>:2105'
      // '<S7>:2105:3' l_changedDuringSteeringExit = (hasChanged(in_gear) || in_touchCoorChange); 
      out = ((rtDWork.in_gear_prev != rtDWork.in_gear_start) ||
             rtDWork.out_touchCoorChange);
      switch (rtDWork.is_Steering_exitHandling)
      {
       case IN_HasSteeringWarning:
        {
          // During 'HasSteeringWarning': '<S7>:2111'
          // '<S7>:2149:1' sf_internal_predicateOutput = ~in_steeringAct;
          if (!rtDWork.out_steeringAct)
          {
            // Transition: '<S7>:2149'
            rtDWork.is_Steering_exitHandling = IN_NoSteeringWarning;
            rtDWork.temporalCounter_i6 = 0U;

            // '<S7>:2120:1' sf_internal_predicateOutput = l_changedDuringSteeringExit == true; 
          }
          else if (out)
          {
            // Transition: '<S7>:2120'
            rtDWork.is_Steering_exitHandling = IN_hasChangeDuringExit;
            rtDWork.temporalCounter_i6 = 0U;
          }
          else
          {
            real_T tmp;
            uint32_T tmp_0;

            // Inport: '<Root>/In_timeStepScaleFactor'
            // '<S7>:2118:1' sf_internal_predicateOutput = after(in_exitDelay.PassiveExitDelay * in_timeStepScaleFactor, sec) && ... 
            // '<S7>:2118:2' in_gear ~= EGear.R && ...
            // '<S7>:2118:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
            // '<S7>:2118:4' (~in_ModWarning) &&...
            // '<S7>:2118:5'  ~in_parkingsts;
            tmp = rt_roundd(static_cast<real_T>
                            (rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay)
                            * rtU.In_timeStepScaleFactor);
            if (tmp < 4.294967296E+9)
            {
              if (tmp >= 0.0)
              {
                tmp_0 = static_cast<uint32_T>(tmp);
              }
              else
              {
                tmp_0 = 0U;
              }
            }
            else
            {
              tmp_0 = MAX_uint32_T;
            }

            // Inport: '<Root>/In_gear' incorporates:
            //   Inport: '<Root>/In_ModWarning'

            if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(
                  static_cast<int32_T>(static_cast<int32_T>(tmp_0) * 100))) && (
                 static_cast<uint32_T>(rtU.In_gear) != EGear_R) &&
                (static_cast<int32_T>(rtDWork.is_durationNoSonarWarning) == 2) &&
                (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
            {
              // Transition: '<S7>:2118'
              rtDWork.is_Steering_exitHandling = IN_NO_ACTIVE_CHILD;
              rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
              rtDWork.is_ActiveState = IN_Exit_Handling;

              // Entry 'Exit_Handling': '<S7>:1692'
              rtDWork.is_Exit_Handling = IN_Exit_Excuted;
            }
          }
        }
        break;

       case IN_NoSteeringWarning:
        {
          // During 'NoSteeringWarning': '<S7>:2109'
          // '<S7>:2117:1' sf_internal_predicateOutput = l_changedDuringSteeringExit == true; 
          if (out)
          {
            // Transition: '<S7>:2117'
            rtDWork.is_Steering_exitHandling = IN_hasChangeDuringExit;
            rtDWork.temporalCounter_i6 = 0U;
          }
          else
          {
            real_T tmp;
            uint32_T tmp_0;

            // Inport: '<Root>/In_timeStepScaleFactor'
            // '<S7>:2119:1' sf_internal_predicateOutput = after(in_exitDelay.WarningExitDelay * in_timeStepScaleFactor, sec) && ... 
            // '<S7>:2119:2' in_gear ~= EGear.R && ...
            // '<S7>:2119:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
            // '<S7>:2119:4' (~in_ModWarning) &&...
            // '<S7>:2119:5'  ~in_parkingsts;
            tmp = rt_roundd(static_cast<real_T>
                            (rtDWork.BusConversion_InsertedFor_Mai_d.WarningExitDelay)
                            * rtU.In_timeStepScaleFactor);
            if (tmp < 4.294967296E+9)
            {
              if (tmp >= 0.0)
              {
                tmp_0 = static_cast<uint32_T>(tmp);
              }
              else
              {
                tmp_0 = 0U;
              }
            }
            else
            {
              tmp_0 = MAX_uint32_T;
            }

            // Inport: '<Root>/In_gear' incorporates:
            //   Inport: '<Root>/In_ModWarning'

            if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(
                  static_cast<int32_T>(static_cast<int32_T>(tmp_0) * 100))) && (
                 static_cast<uint32_T>(rtU.In_gear) != EGear_R) &&
                (static_cast<int32_T>(rtDWork.is_durationNoSonarWarning) == 2) &&
                (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
            {
              // Transition: '<S7>:2119'
              rtDWork.is_Steering_exitHandling = IN_NO_ACTIVE_CHILD;
              rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
              rtDWork.is_ActiveState = IN_Exit_Handling;

              // Entry 'Exit_Handling': '<S7>:1692'
              rtDWork.is_Exit_Handling = IN_Exit_Excuted;

              // '<S7>:2150:1' sf_internal_predicateOutput = in_steeringAct;
            }
            else if (rtDWork.out_steeringAct)
            {
              // Transition: '<S7>:2150'
              rtDWork.is_Steering_exitHandling = IN_HasSteeringWarning;
              rtDWork.temporalCounter_i6 = 0U;
            }
            else
            {
              // no actions
            }
          }
        }
        break;

       default:
        {
          real_T tmp;
          uint32_T tmp_0;

          // Inport: '<Root>/In_timeStepScaleFactor'
          // During 'hasChangeDuringExit': '<S7>:2107'
          // '<S7>:2114:1' sf_internal_predicateOutput = after(in_exitDelay.PassiveExitDelay * in_timeStepScaleFactor, sec) && ... 
          // '<S7>:2114:2' in_gear ~= EGear.R && ...
          // '<S7>:2114:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
          // '<S7>:2114:4' (~in_ModWarning) &&...
          // '<S7>:2114:5'  ~in_parkingsts;
          tmp = rt_roundd(static_cast<real_T>
                          (rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay)
                          * rtU.In_timeStepScaleFactor);
          if (tmp < 4.294967296E+9)
          {
            if (tmp >= 0.0)
            {
              tmp_0 = static_cast<uint32_T>(tmp);
            }
            else
            {
              tmp_0 = 0U;
            }
          }
          else
          {
            tmp_0 = MAX_uint32_T;
          }

          // Inport: '<Root>/In_gear' incorporates:
          //   Inport: '<Root>/In_ModWarning'

          if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(static_cast<
                int32_T>(static_cast<int32_T>(tmp_0) * 100))) &&
              (static_cast<uint32_T>(rtU.In_gear) != EGear_R) &&
              (static_cast<int32_T>(rtDWork.is_durationNoSonarWarning) == 2) &&
              (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
          {
            // Transition: '<S7>:2114'
            rtDWork.is_Steering_exitHandling = IN_NO_ACTIVE_CHILD;
            rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
            rtDWork.is_ActiveState = IN_Exit_Handling;

            // Entry 'Exit_Handling': '<S7>:1692'
            rtDWork.is_Exit_Handling = IN_Exit_Excuted;

            // '<S7>:2116:1' sf_internal_predicateOutput = l_changedDuringSteeringExit == true; 
          }
          else if (out)
          {
            // Transition: '<S7>:2116'
            rtDWork.is_Steering_exitHandling = IN_hasChangeDuringExit;
            rtDWork.temporalCounter_i6 = 0U;
          }
          else
          {
            // no actions
          }
        }
        break;
      }
    }
  }

  // End of Inport: '<Root>/In_parkstatus'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Gear_Activate_SVS(void)
{
  EVoiceDockReq tmp_1;
  boolean_T out;

  // Update for Outport: '<Root>/Out_ShowReqMod'
  rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

  // Inport: '<Root>/In_voiceDockRequest'
  // During 'Gear_Activate_SVS': '<S7>:1565'
  // '<S7>:2602:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
  // '<S7>:2602:2' && hasChanged(in_voiceDockRequest))...
  // '<S7>:2602:3' &&~((in_gear==EGear.D)...
  // '<S7>:2602:4' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR...
  // '<S7>:2602:5' ||in_voiceDockRequest==EVoiceDockReq.OPEN_REARWIDE...
  // '<S7>:2602:6' ||in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL)) ...
  // '<S7>:2602:7' ||  (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
  // '<S7>:2602:8' && in(SuperState.SuperStateL2.Available))...
  // '<S7>:2602:9' || (hasChangedTo(in_androidIconActive,true)...
  // '<S7>:2602:10' && in(SuperState.SuperStateL2.Available))...
  // '<S7>:2602:11' || (hasChangedTo(in_steeringWheelButtonPressed,true)...
  // '<S7>:2602:12' && in(SuperState.SuperStateL2.Available))||hasChangedTo(in_EnlargeButtonPressed,true); 
  tmp_1 = rtU.In_voiceDockRequest;
  if ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_NONE) &&
      (rtDWork.in_voiceDockRequest_prev != rtDWork.in_voiceDockRequest_start) &&
      ((static_cast<uint32_T>(rtU.In_gear) != EGear_D) || ((static_cast<uint32_T>
         (tmp_1) != EVoiceDockReq_OPEN_REAR) && (static_cast<uint32_T>(tmp_1) !=
         EVoiceDockReq_OPEN_REARWIDE) && (static_cast<uint32_T>(tmp_1) !=
         EVoiceDockReq_OPEN_REARWHEEL))))
  {
    out = true;
  }
  else
  {
    EDockAvmButtonPress out_tmp;
    out_tmp = rtDWork.in_dockAvmButtonPress_start;
    out = (((rtDWork.in_dockAvmButtonPress_prev != out_tmp) &&
            (static_cast<uint32_T>(out_tmp) == EDockAvmButtonPress_AVM_PRESS) &&
            (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1)) ||
           ((rtDWork.in_androidIconActive_prev !=
             rtDWork.in_androidIconActive_start) &&
            rtDWork.in_androidIconActive_start && (static_cast<int32_T>
             (rtDWork.is_SuperStateL2) == 1)) ||
           ((rtDWork.in_steeringWheelButtonPressed_p !=
             rtDWork.in_steeringWheelButtonPressed_s) &&
            rtDWork.in_steeringWheelButtonPressed_s && (static_cast<int32_T>
             (rtDWork.is_SuperStateL2) == 1)) ||
           ((rtDWork.in_EnlargeButtonPressed_prev !=
             rtDWork.in_EnlargeButtonPressed_start) &&
            rtDWork.in_EnlargeButtonPressed_start));
  }

  if (out)
  {
    // Transition: '<S7>:2602'
    // Transition: '<S7>:2601'
    // Transition: '<S7>:2598'
    // Exit Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Exit Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    rtDWork.is_Normal_activate = IN_Manually_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Manually_Activate_SVS': '<S7>:1564'
    //  VoiceDock, AndroidIcon, SteerButton
    // '<S7>:1564:4' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1564:5' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1564:6' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Manually_Activate_SVS': '<S7>:1564'
    // Transition: '<S7>:2181'
    // Entry 'Manually__Activate_ExitHandling': '<S7>:2179'
    // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
    // Transition: '<S7>:2184'
    rtDWork.temporalCounter_i6 = 0U;

    // Inport: '<Root>/In_parkstatus'
    // '<S7>:2663:1' sf_internal_predicateOutput = [in_parkingsts == true];
  }
  else if (rtU.In_parkstatus)
  {
    // Transition: '<S7>:2663'
    // Exit Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Exit Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    rtDWork.is_Normal_activate = IN_ParkActiveHandling;
    rtDWork.is_ParkActiveHandling = IN_Park_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Park_Activated': '<S7>:828'
    // '<S7>:828:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:828:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK;
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;

    // During 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // '<S7>:2180:3' l_changedDuringGearExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // During 'hasChangeDuringExit': '<S7>:2196'
    // '<S7>:2199:1' sf_internal_predicateOutput = l_changedDuringGearExit == true; 
  }
  else if ((rtDWork.in_gear_prev != rtDWork.in_gear_start) ||
           rtDWork.out_touchCoorChange)
  {
    // Transition: '<S7>:2199'
    rtDWork.temporalCounter_i6 = 0U;
  }
  else
  {
    real_T tmp;
    uint32_T tmp_0;

    // Inport: '<Root>/In_timeStepScaleFactor'
    // '<S7>:2200:1' sf_internal_predicateOutput = after(in_exitDelay.PassiveExitDelay * in_timeStepScaleFactor, sec) && ... 
    // '<S7>:2200:2' in_gear ~= EGear.R && ...
    // '<S7>:2200:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
    // '<S7>:2200:4' (~in_ModWarning) &&...
    // '<S7>:2200:5'  ~in_parkingsts;
    tmp = rt_roundd(static_cast<real_T>
                    (rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay) *
                    rtU.In_timeStepScaleFactor);
    if (tmp < 4.294967296E+9)
    {
      if (tmp >= 0.0)
      {
        tmp_0 = static_cast<uint32_T>(tmp);
      }
      else
      {
        tmp_0 = 0U;
      }
    }
    else
    {
      tmp_0 = MAX_uint32_T;
    }

    // Inport: '<Root>/In_gear' incorporates:
    //   Inport: '<Root>/In_ModWarning'

    if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(static_cast<int32_T>
          (static_cast<int32_T>(tmp_0) * 100))) && (static_cast<uint32_T>
         (rtU.In_gear) != EGear_R) && (static_cast<int32_T>
         (rtDWork.is_durationNoSonarWarning) == 2) && (!rtU.In_ModWarning) &&
        (!rtU.In_parkstatus))
    {
      // Transition: '<S7>:2200'
      rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
      rtDWork.is_ActiveState = IN_Exit_Handling;

      // Entry 'Exit_Handling': '<S7>:1692'
      rtDWork.is_Exit_Handling = IN_Exit_Excuted;
    }

    // End of Inport: '<Root>/In_gear'

    // End of Inport: '<Root>/In_parkstatus'
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::NarrowLane_Activated(void)
{
  // Update for Outport: '<Root>/Out_ShowReqMod'
  rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

  // Inport: '<Root>/In_parkstatus'
  // During 'NarrowLane_Activated': '<S7>:2532'
  // '<S7>:2527:1' sf_internal_predicateOutput = in_parkingsts == true;
  if (rtU.In_parkstatus)
  {
    // Transition: '<S7>:2527'
    // Exit Internal 'NarrowLane_Activated': '<S7>:2532'
    // Exit Internal 'Sonar_exitHandling': '<S7>:2535'
    rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
    rtDWork.is_NarrowLane_Activated = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Normal_activate = IN_ParkActiveHandling;
    rtDWork.is_ParkActiveHandling = IN_Park_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Park_Activated': '<S7>:828'
    // '<S7>:828:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:828:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK;
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;
  }
  else
  {
    EVoiceDockReq tmp_1;
    boolean_T out;

    // Inport: '<Root>/In_voiceDockRequest'
    // '<S7>:2591:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
    // '<S7>:2591:2' && hasChanged(in_voiceDockRequest))...
    // '<S7>:2591:3' &&~((in_gear==EGear.D)...
    // '<S7>:2591:4' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR...
    // '<S7>:2591:5' ||in_voiceDockRequest==EVoiceDockReq.OPEN_REARWIDE...
    // '<S7>:2591:6' ||in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL)) ... 
    // '<S7>:2591:7' ||  (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
    // '<S7>:2591:8' && in(SuperState.SuperStateL2.Available))...
    // '<S7>:2591:9' || (hasChangedTo(in_androidIconActive,true)...
    // '<S7>:2591:10' && in(SuperState.SuperStateL2.Available))...
    // '<S7>:2591:11' || (hasChangedTo(in_steeringWheelButtonPressed,true)...
    // '<S7>:2591:12' && in(SuperState.SuperStateL2.Available))||hasChangedTo(in_EnlargeButtonPressed,true); 
    tmp_1 = rtU.In_voiceDockRequest;
    if ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_NONE) &&
        (rtDWork.in_voiceDockRequest_prev != rtDWork.in_voiceDockRequest_start) &&
        ((static_cast<uint32_T>(rtU.In_gear) != EGear_D) ||
         ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REAR) && (
           static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REARWIDE) && (
           static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REARWHEEL))))
    {
      out = true;
    }
    else
    {
      EDockAvmButtonPress out_tmp;
      out_tmp = rtDWork.in_dockAvmButtonPress_start;
      out = (((rtDWork.in_dockAvmButtonPress_prev != out_tmp) &&
              (static_cast<uint32_T>(out_tmp) == EDockAvmButtonPress_AVM_PRESS) &&
              (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_androidIconActive_prev !=
               rtDWork.in_androidIconActive_start) &&
              rtDWork.in_androidIconActive_start && (static_cast<int32_T>
               (rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_steeringWheelButtonPressed_p !=
               rtDWork.in_steeringWheelButtonPressed_s) &&
              rtDWork.in_steeringWheelButtonPressed_s && (static_cast<int32_T>
               (rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_EnlargeButtonPressed_prev !=
               rtDWork.in_EnlargeButtonPressed_start) &&
              rtDWork.in_EnlargeButtonPressed_start));
    }

    if (out)
    {
      // Transition: '<S7>:2591'
      // Transition: '<S7>:2592'
      // Transition: '<S7>:2594'
      // Transition: '<S7>:2600'
      // Transition: '<S7>:2601'
      // Transition: '<S7>:2598'
      // Exit Internal 'NarrowLane_Activated': '<S7>:2532'
      // Exit Internal 'Sonar_exitHandling': '<S7>:2535'
      rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
      rtDWork.is_NarrowLane_Activated = IN_NO_ACTIVE_CHILD;
      rtDWork.is_Normal_activate = IN_Manually_Activate_SVS;

      // Outport: '<Root>/Out_systemActive'
      // Entry 'Manually_Activate_SVS': '<S7>:1564'
      //  VoiceDock, AndroidIcon, SteerButton
      // '<S7>:1564:4' out_systemActive = true;
      rtY.Out_systemActive = true;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // '<S7>:1564:5' out_SVSShowReq = true;
      rtY.Out_SVSShowReq = true;

      // Update for Outport: '<Root>/Out_ShowReqMod'
      // '<S7>:1564:6' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
      rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

      // Entry Internal 'Manually_Activate_SVS': '<S7>:1564'
      // Transition: '<S7>:2181'
      // Entry 'Manually__Activate_ExitHandling': '<S7>:2179'
      // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
      // Entry Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
      // Transition: '<S7>:2184'
      rtDWork.temporalCounter_i6 = 0U;
    }
    else if (static_cast<uint32_T>(rtDWork.is_NarrowLane_Activated) ==
             IN_HoldOnwithSonarWarning)
    {
      // During 'HoldOnwithSonarWarning': '<S7>:2526'
      // '<S7>:2537:1' sf_internal_predicateOutput = in_narrowLaneAct == false;
      if (!rtDWork.out_narrowLaneAct)
      {
        // Transition: '<S7>:2537'
        rtDWork.is_NarrowLane_Activated = IN_Sonar_exitHandling;

        // Entry 'Sonar_exitHandling': '<S7>:2535'
        // '<S7>:2535:3' l_changedDuringSonarExit = (hasChanged(in_gear) || in_touchCoorChange); 
        rtDWork.is_Sonar_exitHandling_i = IN_NoSonarWarning;
        rtDWork.temporalCounter_i6 = 0U;
      }
    }
    else
    {
      // During 'Sonar_exitHandling': '<S7>:2535'
      // '<S7>:2535:3' l_changedDuringSonarExit = (hasChanged(in_gear) || in_touchCoorChange); 
      out = ((rtDWork.in_gear_prev != rtDWork.in_gear_start) ||
             rtDWork.out_touchCoorChange);
      if (static_cast<uint32_T>(rtDWork.is_Sonar_exitHandling_i) ==
          IN_NoSonarWarning)
      {
        // During 'NoSonarWarning': '<S7>:2536'
        // '<S7>:2529:1' sf_internal_predicateOutput = in_narrowLaneAct == true; 
        if (rtDWork.out_narrowLaneAct)
        {
          // Transition: '<S7>:2529'
          rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
          rtDWork.is_NarrowLane_Activated = IN_HoldOnwithSonarWarning;

          // '<S7>:2531:1' sf_internal_predicateOutput = l_changedDuringSonarExit == true; 
        }
        else if (out)
        {
          // Transition: '<S7>:2531'
          rtDWork.is_Sonar_exitHandling_i = IN_hasChangeDuringExit_i;
          rtDWork.temporalCounter_i6 = 0U;
        }
        else
        {
          real_T tmp;
          uint32_T tmp_0;

          // Inport: '<Root>/In_timeStepScaleFactor'
          // '<S7>:2539:1' sf_internal_predicateOutput = after(in_exitDelay.WarningExitDelay * in_timeStepScaleFactor, sec) && ... 
          // '<S7>:2539:2' in_gear ~= EGear.R && ...
          // '<S7>:2539:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
          // '<S7>:2539:4' (~in_ModWarning) &&...
          // '<S7>:2539:5'  ~in_parkingsts;
          tmp = rt_roundd(static_cast<real_T>
                          (rtDWork.BusConversion_InsertedFor_Mai_d.WarningExitDelay)
                          * rtU.In_timeStepScaleFactor);
          if (tmp < 4.294967296E+9)
          {
            if (tmp >= 0.0)
            {
              tmp_0 = static_cast<uint32_T>(tmp);
            }
            else
            {
              tmp_0 = 0U;
            }
          }
          else
          {
            tmp_0 = MAX_uint32_T;
          }

          // Inport: '<Root>/In_gear' incorporates:
          //   Inport: '<Root>/In_ModWarning'

          if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>
               (static_cast<int32_T>(static_cast<int32_T>(tmp_0) * 100))) && (
               static_cast<uint32_T>(rtU.In_gear) != EGear_R) &&
              (static_cast<int32_T>(rtDWork.is_durationNoSonarWarning) == 2) &&
              (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
          {
            // Transition: '<S7>:2539'
            rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
            rtDWork.is_NarrowLane_Activated = IN_NO_ACTIVE_CHILD;
            rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
            rtDWork.is_ActiveState = IN_Exit_Handling;

            // Entry 'Exit_Handling': '<S7>:1692'
            rtDWork.is_Exit_Handling = IN_Exit_Excuted;
          }
        }

        // During 'hasChangeDuringExit': '<S7>:2533'
        // '<S7>:2530:1' sf_internal_predicateOutput = in_narrowLaneAct == true; 
      }
      else if (rtDWork.out_narrowLaneAct)
      {
        // Transition: '<S7>:2530'
        rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
        rtDWork.is_NarrowLane_Activated = IN_HoldOnwithSonarWarning;

        // '<S7>:2525:1' sf_internal_predicateOutput = l_changedDuringSonarExit == true; 
      }
      else if (out)
      {
        // Transition: '<S7>:2525'
        rtDWork.is_Sonar_exitHandling_i = IN_hasChangeDuringExit_i;
        rtDWork.temporalCounter_i6 = 0U;

        // Inport: '<Root>/In_gear' incorporates:
        //   Inport: '<Root>/In_ModWarning'

        // '<S7>:2540:1' sf_internal_predicateOutput = after(in_exitDelay.PassiveExitDelay, sec) && ... 
        // '<S7>:2540:2' in_gear ~= EGear.R && ...
        // '<S7>:2540:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
        // '<S7>:2540:4' (~in_ModWarning) &&...
        // '<S7>:2540:5'  ~in_parkingsts;
      }
      else if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(static_cast<
                 int32_T>(static_cast<int32_T>
                          (rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay)
                          * 100))) && (static_cast<uint32_T>(rtU.In_gear) !=
                EGear_R) && (static_cast<int32_T>
                             (rtDWork.is_durationNoSonarWarning) == 2) &&
               (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
      {
        // Transition: '<S7>:2540'
        rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
        rtDWork.is_NarrowLane_Activated = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ActiveState = IN_Exit_Handling;

        // Entry 'Exit_Handling': '<S7>:1692'
        rtDWork.is_Exit_Handling = IN_Exit_Excuted;
      }
      else
      {
        // no actions
      }
    }
  }

  // End of Inport: '<Root>/In_parkstatus'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Sonar_Activated(void)
{
  // Update for Outport: '<Root>/Out_ShowReqMod'
  rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

  // Inport: '<Root>/In_parkstatus'
  // During 'Sonar_Activated': '<S7>:833'
  // '<S7>:2104:1' sf_internal_predicateOutput = in_parkingsts == true;
  if (rtU.In_parkstatus)
  {
    // Transition: '<S7>:2104'
    // Exit Internal 'Sonar_Activated': '<S7>:833'
    // Exit Internal 'Sonar_exitHandling': '<S7>:2135'
    rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Sonar_Activated = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Normal_activate = IN_ParkActiveHandling;
    rtDWork.is_ParkActiveHandling = IN_Park_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Park_Activated': '<S7>:828'
    // '<S7>:828:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:828:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK;
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;
  }
  else
  {
    EVoiceDockReq tmp_1;
    boolean_T out;

    // Inport: '<Root>/In_voiceDockRequest'
    // '<S7>:2595:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
    // '<S7>:2595:2' && hasChanged(in_voiceDockRequest))...
    // '<S7>:2595:3' &&~((in_gear==EGear.D)...
    // '<S7>:2595:4' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR...
    // '<S7>:2595:5' ||in_voiceDockRequest==EVoiceDockReq.OPEN_REARWIDE...
    // '<S7>:2595:6' ||in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL)) ... 
    // '<S7>:2595:7' ||  (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
    // '<S7>:2595:8' && in(SuperState.SuperStateL2.Available))...
    // '<S7>:2595:9' || (hasChangedTo(in_androidIconActive,true)...
    // '<S7>:2595:10' && in(SuperState.SuperStateL2.Available))...
    // '<S7>:2595:11' || (hasChangedTo(in_steeringWheelButtonPressed,true)...
    // '<S7>:2595:12' && in(SuperState.SuperStateL2.Available))||hasChangedTo(in_EnlargeButtonPressed,true); 
    tmp_1 = rtU.In_voiceDockRequest;
    if ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_NONE) &&
        (rtDWork.in_voiceDockRequest_prev != rtDWork.in_voiceDockRequest_start) &&
        ((static_cast<uint32_T>(rtU.In_gear) != EGear_D) ||
         ((static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REAR) && (
           static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REARWIDE) && (
           static_cast<uint32_T>(tmp_1) != EVoiceDockReq_OPEN_REARWHEEL))))
    {
      out = true;
    }
    else
    {
      EDockAvmButtonPress out_tmp;
      out_tmp = rtDWork.in_dockAvmButtonPress_start;
      out = (((rtDWork.in_dockAvmButtonPress_prev != out_tmp) &&
              (static_cast<uint32_T>(out_tmp) == EDockAvmButtonPress_AVM_PRESS) &&
              (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_androidIconActive_prev !=
               rtDWork.in_androidIconActive_start) &&
              rtDWork.in_androidIconActive_start && (static_cast<int32_T>
               (rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_steeringWheelButtonPressed_p !=
               rtDWork.in_steeringWheelButtonPressed_s) &&
              rtDWork.in_steeringWheelButtonPressed_s && (static_cast<int32_T>
               (rtDWork.is_SuperStateL2) == 1)) ||
             ((rtDWork.in_EnlargeButtonPressed_prev !=
               rtDWork.in_EnlargeButtonPressed_start) &&
              rtDWork.in_EnlargeButtonPressed_start));
    }

    if (out)
    {
      // Transition: '<S7>:2595'
      // Transition: '<S7>:2600'
      // Transition: '<S7>:2601'
      // Transition: '<S7>:2598'
      // Exit Internal 'Sonar_Activated': '<S7>:833'
      // Exit Internal 'Sonar_exitHandling': '<S7>:2135'
      rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
      rtDWork.is_Sonar_Activated = IN_NO_ACTIVE_CHILD;
      rtDWork.is_Normal_activate = IN_Manually_Activate_SVS;

      // Outport: '<Root>/Out_systemActive'
      // Entry 'Manually_Activate_SVS': '<S7>:1564'
      //  VoiceDock, AndroidIcon, SteerButton
      // '<S7>:1564:4' out_systemActive = true;
      rtY.Out_systemActive = true;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // '<S7>:1564:5' out_SVSShowReq = true;
      rtY.Out_SVSShowReq = true;

      // Update for Outport: '<Root>/Out_ShowReqMod'
      // '<S7>:1564:6' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
      rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

      // Entry Internal 'Manually_Activate_SVS': '<S7>:1564'
      // Transition: '<S7>:2181'
      // Entry 'Manually__Activate_ExitHandling': '<S7>:2179'
      // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
      // Entry Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
      // Transition: '<S7>:2184'
      rtDWork.temporalCounter_i6 = 0U;
    }
    else if (static_cast<uint32_T>(rtDWork.is_Sonar_Activated) ==
             IN_HoldOnwithSonarWarning)
    {
      // During 'HoldOnwithSonarWarning': '<S7>:2163'
      // '<S7>:2155:1' sf_internal_predicateOutput = in_obstacleAct == false;
      if (!rtDWork.out_obstacleAct)
      {
        // Transition: '<S7>:2155'
        rtDWork.is_Sonar_Activated = IN_Sonar_exitHandling;

        // Entry 'Sonar_exitHandling': '<S7>:2135'
        // '<S7>:2135:3' l_changedDuringSonarExit = (hasChanged(in_gear) || in_touchCoorChange); 
        rtDWork.is_Sonar_exitHandling = IN_NoSonarWarning;
        rtDWork.temporalCounter_i6 = 0U;
      }
    }
    else
    {
      // During 'Sonar_exitHandling': '<S7>:2135'
      // '<S7>:2135:3' l_changedDuringSonarExit = (hasChanged(in_gear) || in_touchCoorChange); 
      out = ((rtDWork.in_gear_prev != rtDWork.in_gear_start) ||
             rtDWork.out_touchCoorChange);
      if (static_cast<uint32_T>(rtDWork.is_Sonar_exitHandling) ==
          IN_NoSonarWarning)
      {
        // During 'NoSonarWarning': '<S7>:2126'
        // '<S7>:2165:1' sf_internal_predicateOutput = in_obstacleAct == true;
        if (rtDWork.out_obstacleAct)
        {
          // Transition: '<S7>:2165'
          rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
          rtDWork.is_Sonar_Activated = IN_HoldOnwithSonarWarning;

          // '<S7>:2127:1' sf_internal_predicateOutput = l_changedDuringSonarExit == true; 
        }
        else if (out)
        {
          // Transition: '<S7>:2127'
          rtDWork.is_Sonar_exitHandling = IN_hasChangeDuringExit_i;
          rtDWork.temporalCounter_i6 = 0U;
        }
        else
        {
          real_T tmp;
          uint32_T tmp_0;

          // Inport: '<Root>/In_timeStepScaleFactor'
          // '<S7>:2129:1' sf_internal_predicateOutput = after(in_exitDelay.WarningExitDelay * in_timeStepScaleFactor, sec) && ... 
          // '<S7>:2129:2' in_gear ~= EGear.R && ...
          // '<S7>:2129:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
          // '<S7>:2129:4' (~in_ModWarning) &&...
          // '<S7>:2129:5'  ~in_parkingsts;
          tmp = rt_roundd(static_cast<real_T>
                          (rtDWork.BusConversion_InsertedFor_Mai_d.WarningExitDelay)
                          * rtU.In_timeStepScaleFactor);
          if (tmp < 4.294967296E+9)
          {
            if (tmp >= 0.0)
            {
              tmp_0 = static_cast<uint32_T>(tmp);
            }
            else
            {
              tmp_0 = 0U;
            }
          }
          else
          {
            tmp_0 = MAX_uint32_T;
          }

          // Inport: '<Root>/In_gear' incorporates:
          //   Inport: '<Root>/In_ModWarning'

          if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(static_cast<
                int32_T>(static_cast<int32_T>(tmp_0) * 100))) &&
              (static_cast<uint32_T>(rtU.In_gear) != EGear_R) &&
              (static_cast<int32_T>(rtDWork.is_durationNoSonarWarning) == 2) &&
              (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
          {
            // Transition: '<S7>:2129'
            rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
            rtDWork.is_Sonar_Activated = IN_NO_ACTIVE_CHILD;
            rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
            rtDWork.is_ActiveState = IN_Exit_Handling;

            // Entry 'Exit_Handling': '<S7>:1692'
            rtDWork.is_Exit_Handling = IN_Exit_Excuted;
          }
        }

        // During 'hasChangeDuringExit': '<S7>:2136'
        // '<S7>:2166:1' sf_internal_predicateOutput = in_obstacleAct == true;
      }
      else if (rtDWork.out_obstacleAct)
      {
        // Transition: '<S7>:2166'
        rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Sonar_Activated = IN_HoldOnwithSonarWarning;

        // '<S7>:2123:1' sf_internal_predicateOutput = l_changedDuringSonarExit == true; 
      }
      else if (out)
      {
        // Transition: '<S7>:2123'
        rtDWork.is_Sonar_exitHandling = IN_hasChangeDuringExit_i;
        rtDWork.temporalCounter_i6 = 0U;

        // Inport: '<Root>/In_gear' incorporates:
        //   Inport: '<Root>/In_ModWarning'

        // '<S7>:2132:1' sf_internal_predicateOutput = after(in_exitDelay.PassiveExitDelay, sec) && ... 
        // '<S7>:2132:2' in_gear ~= EGear.R && ...
        // '<S7>:2132:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&...
        // '<S7>:2132:4' (~in_ModWarning) &&...
        // '<S7>:2132:5'  ~in_parkingsts;
      }
      else if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(static_cast<
                 int32_T>(static_cast<int32_T>
                          (rtDWork.BusConversion_InsertedFor_Mai_d.PassiveExitDelay)
                          * 100))) && (static_cast<uint32_T>(rtU.In_gear) !=
                EGear_R) && (static_cast<int32_T>
                             (rtDWork.is_durationNoSonarWarning) == 2) &&
               (!rtU.In_ModWarning) && (!rtU.In_parkstatus))
      {
        // Transition: '<S7>:2132'
        rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Sonar_Activated = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
        rtDWork.is_ActiveState = IN_Exit_Handling;

        // Entry 'Exit_Handling': '<S7>:1692'
        rtDWork.is_Exit_Handling = IN_Exit_Excuted;
      }
      else
      {
        // no actions
      }
    }
  }

  // End of Inport: '<Root>/In_parkstatus'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Normal_activate(const
  ESVSScreenType *UnitDelay9, const ESystemStr *in_SystemStr_prev, const
  ECalibStatus *in_calibStatus_prev, const uint8_T *in_calibActive_prev, const
  boolean_T *in_closeButtonPressed_prev, const boolean_T *in_competeQuit_prev)
{
  boolean_T guard1 = false;

  // Update for Outport: '<Root>/Out_competeReqStatus'
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

  // During 'Normal_activate': '<S7>:816'
  // '<S7>:838:1' sf_internal_predicateOutput = in(SuperState.SuperStateL2.Unavailable); 
  guard1 = false;
  if (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 2)
  {
    // Transition: '<S7>:838'
    guard1 = true;
  }
  else
  {
    EGear tmp_1;
    EGear tmp_6;
    boolean_T tmp_2;

    // '<S7>:1295:1' sf_internal_predicateOutput = hasChangedTo(in_gear,EGear.P)&&... 
    // '<S7>:1295:2' ~in_parkingsts;
    tmp_1 = rtDWork.in_gear_start;

    // Inport: '<Root>/In_parkstatus'
    tmp_2 = !rtU.In_parkstatus;
    tmp_6 = rtDWork.in_gear_prev;
    if (((tmp_6 != tmp_1) && (static_cast<uint32_T>(tmp_1) == EGear_P) && tmp_2)
        || (((*in_calibStatus_prev != rtDWork.in_calibStatus_start) &&
             (rtDWork.in_calibStatus_start ==
              ECalibStatus_CALIBSTATUS_IN_CALIBRATION)) ||
            ((*in_calibActive_prev != rtDWork.in_calibActive_start) && (
           static_cast<int32_T>(rtDWork.in_calibActive_start) == 1))))
    {
      // Transition: '<S7>:1295'
      // Transition: '<S7>:1312'
      guard1 = true;
    }
    else
    {
      EGear tmp_3;

      // '<S7>:1312:1' sf_internal_predicateOutput = hasChangedTo(in_calibStatus, ECalibStatus.CALIBSTATUS_IN_CALIBRATION) || ... 
      // '<S7>:1312:2' hasChangedTo(in_calibActive, uint8(1));
      // Inport: '<Root>/In_gear'
      // '<S7>:1317:1' sf_internal_predicateOutput = hasChangedTo(in_closeButtonPressed,true) &&... 
      // '<S7>:1317:2' ~in_parkingsts && ...
      // '<S7>:1317:3' in_gear~=EGear.R;
      tmp_3 = rtU.In_gear;
      if (((*in_closeButtonPressed_prev != rtDWork.in_closeButtonPressed_start) &&
           rtDWork.in_closeButtonPressed_start && tmp_2 && (static_cast<uint32_T>
            (tmp_3) != EGear_R)) || ((*in_competeQuit_prev !=
            rtDWork.in_competeQuit_start) && rtDWork.in_competeQuit_start) ||
          ((rtDWork.in_steeringWheelButtonPressed_p !=
            rtDWork.in_steeringWheelButtonPressed_s) &&
           rtDWork.in_steeringWheelButtonPressed_s && tmp_2 &&
           (static_cast<uint32_T>(tmp_3) != EGear_R) && (static_cast<int32_T>
            (rtDWork.is_Available) != 1)) || (rtU.In_backkeyEvent &&
           (*UnitDelay9 == ESVSScreenType_SCREEN_FLOAT_NOTPARK)))
      {
        // Transition: '<S7>:1317'
        // Transition: '<S7>:1767'
        // Transition: '<S7>:2227'
        // Transition: '<S7>:2228'
        guard1 = true;
      }
      else
      {
        EDockAvmButtonPress tmp_4;

        // '<S7>:1767:1' sf_internal_predicateOutput = hasChangedTo(in_competeQuit, true); 
        // '<S7>:2227:1' sf_internal_predicateOutput = hasChangedTo(in_steeringWheelButtonPressed,true) &&... 
        // '<S7>:2227:2' ~in_parkingsts && ...
        // '<S7>:2227:3' in_gear~=EGear.R&& ...
        // '<S7>:2227:4' ~in(SuperState.SuperStateL2.Available.FloatingView);
        // '<S7>:2228:1' sf_internal_predicateOutput = in_backkeyEvent &&...
        // '<S7>:2228:2' in_SVSScreenType == ESVSScreenType.SCREEN_FLOAT_NOTPARK; 
        // '<S7>:2242:1' sf_internal_predicateOutput = hasChangedTo(in_dockAvmButtonPress,EDockAvmButtonPress.CLOSE) && ... 
        // '<S7>:2242:2' ~in_parkingsts && ...
        // '<S7>:2242:3' in_gear~=EGear.R;
        tmp_4 = rtDWork.in_dockAvmButtonPress_start;
        if (((rtDWork.in_dockAvmButtonPress_prev != tmp_4) &&
             (static_cast<uint32_T>(tmp_4) == EDockAvmButtonPress_CLOSE) &&
             tmp_2 && (static_cast<uint32_T>(tmp_3) != EGear_R)) ||
            ((*in_SystemStr_prev != rtDWork.in_SystemStr_start) &&
             (rtDWork.in_SystemStr_start == ESystemStr_SYSTEM_STR_ENTER)))
        {
          // Transition: '<S7>:2242'
          // Transition: '<S7>:2279'
          guard1 = true;
        }
        else
        {
          ESRActiveSts tmp_5;

          // '<S7>:2279:1' sf_internal_predicateOutput = hasChangedTo(in_SystemStr,ESystemStr.SYSTEM_STR_ENTER); 
          // '<S7>:2659:1' sf_internal_predicateOutput = hasChangedTo(in_SRIsActive, ESRActiveSts.ACTIVE)&&... 
          // '<S7>:2659:2' ~in_parkingsts&&in_gear ~= EGear.R;
          tmp_5 = rtDWork.in_SRIsActive_start;
          if ((rtDWork.in_SRIsActive_prev != tmp_5) && (static_cast<uint32_T>
               (tmp_5) == ESRActiveSts_ACTIVE) && tmp_2 && (static_cast<uint32_T>
               (tmp_3) != EGear_R))
          {
            // Transition: '<S7>:2659'
            guard1 = true;
          }
          else
          {
            switch (rtDWork.is_Normal_activate)
            {
             case IN_Gear_Activate_SVS:
              Gear_Activate_SVS();
              break;

             case IN_Manually_Activate_SVS:
              {
                // Update for Outport: '<Root>/Out_ShowReqMod'
                rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

                // Inport: '<Root>/In_parkstatus'
                // During 'Manually_Activate_SVS': '<S7>:1564'
                // '<S7>:2570:1' sf_internal_predicateOutput = in_parkingsts == true; 
                if (rtU.In_parkstatus)
                {
                  // Transition: '<S7>:2570'
                  // Exit Internal 'Manually_Activate_SVS': '<S7>:1564'
                  // Exit Internal 'Manually__Activate_ExitHandling': '<S7>:2179' 
                  rtDWork.is_Normal_activate = IN_ParkActiveHandling;
                  rtDWork.is_ParkActiveHandling = IN_Park_Activated;

                  // Outport: '<Root>/Out_systemActive'
                  // Entry 'Park_Activated': '<S7>:828'
                  // '<S7>:828:3' out_systemActive = true;
                  rtY.Out_systemActive = true;

                  // Update for Outport: '<Root>/Out_SVSShowReq'
                  // '<S7>:828:4' out_SVSShowReq = true;
                  rtY.Out_SVSShowReq = true;

                  // Update for Outport: '<Root>/Out_ShowReqMod'
                  // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK; 
                  rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;

                  //  VoiceDock, AndroidIcon, SteerButton
                  // During 'Manually__Activate_ExitHandling': '<S7>:2179'
                  // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
                  // During 'hasChangeDuringExit': '<S7>:2182'
                  // '<S7>:2187:1' sf_internal_predicateOutput = l_changedDuringMannuallExit == true; 
                }
                else if ((tmp_6 != tmp_1) || rtDWork.out_touchCoorChange)
                {
                  // Transition: '<S7>:2187'
                  rtDWork.temporalCounter_i6 = 0U;
                }
                else
                {
                  real_T tmp;
                  uint32_T tmp_0;

                  // Inport: '<Root>/In_timeStepScaleFactor'
                  // '<S7>:2186:1' sf_internal_predicateOutput = after(in_exitDelay.ActiveExitDelay * in_timeStepScaleFactor, sec) && ... 
                  // '<S7>:2186:2' in_gear ~= EGear.R && ...
                  // '<S7>:2186:3' in(durationNoSonarWarning.NoSonarWarning_5s)&&... 
                  // '<S7>:2186:4' (~in_ModWarning) &&...
                  // '<S7>:2186:5'  ~in_parkingsts;
                  tmp = rt_roundd(static_cast<real_T>
                                  (rtDWork.BusConversion_InsertedFor_Mai_d.ActiveExitDelay)
                                  * rtU.In_timeStepScaleFactor);
                  if (tmp < 4.294967296E+9)
                  {
                    if (tmp >= 0.0)
                    {
                      tmp_0 = static_cast<uint32_T>(tmp);
                    }
                    else
                    {
                      tmp_0 = 0U;
                    }
                  }
                  else
                  {
                    tmp_0 = MAX_uint32_T;
                  }

                  // Inport: '<Root>/In_ModWarning'
                  if ((rtDWork.temporalCounter_i6 >= static_cast<uint32_T>(
                        static_cast<int32_T>(static_cast<int32_T>(tmp_0) * 100)))
                      && (static_cast<uint32_T>(tmp_3) != EGear_R) && (
                       static_cast<int32_T>(rtDWork.is_durationNoSonarWarning) ==
                       2) && (!rtU.In_ModWarning) && tmp_2)
                  {
                    // Transition: '<S7>:2186'
                    rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
                    rtDWork.is_ActiveState = IN_Exit_Handling;

                    // Entry 'Exit_Handling': '<S7>:1692'
                    rtDWork.is_Exit_Handling = IN_Exit_Excuted;
                  }
                }
              }
              break;

             case IN_NarrowLane_Activated:
              NarrowLane_Activated();
              break;

             case IN_ParkActiveHandling:
              {
                boolean_T tmp_7;

                // Inport: '<Root>/In_ModWarning'
                // During 'ParkActiveHandling': '<S7>:2235'
                // '<S7>:2289:1' sf_internal_predicateOutput = [~in_parkingsts&&... 
                // '<S7>:2289:2' (in(durationNoSonarWarning.NoSonarWarning_5s))&&... 
                // '<S7>:2289:3' (~in_ModWarning)&&...
                // '<S7>:2289:4' (in_gear==EGear.R)];
                tmp_7 = !rtU.In_ModWarning;
                if (tmp_2 && (static_cast<int32_T>
                              (rtDWork.is_durationNoSonarWarning) == 2) && tmp_7
                    && (static_cast<uint32_T>(tmp_3) == EGear_R))
                {
                  // Transition: '<S7>:2289'
                  // Transition: '<S7>:2291'
                  // Transition: '<S7>:2292'
                  // Exit Internal 'ParkActiveHandling': '<S7>:2235'
                  rtDWork.is_ParkActiveHandling = IN_NO_ACTIVE_CHILD;
                  rtDWork.is_Normal_activate = IN_Gear_Activate_SVS;

                  // Outport: '<Root>/Out_systemActive'
                  // Entry 'Gear_Activate_SVS': '<S7>:1565'
                  // '<S7>:1565:3' out_systemActive = true;
                  rtY.Out_systemActive = true;

                  // Update for Outport: '<Root>/Out_SVSShowReq'
                  // '<S7>:1565:4' out_SVSShowReq = true;
                  rtY.Out_SVSShowReq = true;

                  // Update for Outport: '<Root>/Out_ShowReqMod'
                  // '<S7>:1565:5' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN; 
                  rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

                  // Entry Internal 'Gear_Activate_SVS': '<S7>:1565'
                  // Transition: '<S7>:2188'
                  // Entry 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
                  // '<S7>:2180:3' l_changedDuringGearExit = (hasChanged(in_gear) || in_touchCoorChange); 
                  // Entry Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180' 
                  // Transition: '<S7>:2198'
                  rtDWork.temporalCounter_i6 = 0U;

                  // '<S7>:1772:1' sf_internal_predicateOutput = ~in_parkingsts&&... 
                  // '<S7>:1772:2' (in(durationNoSonarWarning.NoSonarWarning_5s))&&... 
                  // '<S7>:1772:3' (~in_ModWarning)&&...
                  // '<S7>:1772:4' (in_gear~=EGear.R);
                }
                else if (tmp_2 && (static_cast<int32_T>
                                   (rtDWork.is_durationNoSonarWarning) == 2) &&
                         tmp_7 && (static_cast<uint32_T>(tmp_3) != EGear_R))
                {
                  // Transition: '<S7>:1772'
                  // Exit Internal 'ParkActiveHandling': '<S7>:2235'
                  rtDWork.is_ParkActiveHandling = IN_NO_ACTIVE_CHILD;
                  rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
                  rtDWork.is_ActiveState = IN_Exit_Handling;

                  // Entry 'Exit_Handling': '<S7>:1692'
                  rtDWork.is_Exit_Handling = IN_Exit_Excuted;
                }
                else if (static_cast<uint32_T>(rtDWork.is_ParkActiveHandling) ==
                         IN_ParkToNoPark)
                {
                  // Update for Outport: '<Root>/Out_ShowReqMod'
                  rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

                  // During 'ParkToNoPark': '<S7>:2234'
                }
                else
                {
                  // Update for Outport: '<Root>/Out_ShowReqMod'
                  rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;

                  // During 'Park_Activated': '<S7>:828'
                  // '<S7>:2236:1' sf_internal_predicateOutput = ~in_parkingsts && in_gear~=EGear.R; 
                  if (tmp_2 && (static_cast<uint32_T>(tmp_3) != EGear_R))
                  {
                    // Transition: '<S7>:2236'
                    rtDWork.is_ParkActiveHandling = IN_ParkToNoPark;

                    // Update for Outport: '<Root>/Out_ShowReqMod'
                    // Entry 'ParkToNoPark': '<S7>:2234'
                    // '<S7>:2234:3' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
                    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;
                  }
                }
              }
              break;

             case IN_Sonar_Activated:
              Sonar_Activated();
              break;

             default:
              Steering_Activated();
              break;
            }
          }
        }
      }
    }
  }

  if (guard1)
  {
    // Exit Internal 'Normal_activate': '<S7>:816'
    // Exit Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Exit Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // Exit Internal 'Manually_Activate_SVS': '<S7>:1564'
    // Exit Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
    // Exit Internal 'NarrowLane_Activated': '<S7>:2532'
    // Exit Internal 'Sonar_exitHandling': '<S7>:2535'
    rtDWork.is_Sonar_exitHandling_i = IN_NO_ACTIVE_CHILD;
    rtDWork.is_NarrowLane_Activated = IN_NO_ACTIVE_CHILD;

    // Exit Internal 'ParkActiveHandling': '<S7>:2235'
    rtDWork.is_ParkActiveHandling = IN_NO_ACTIVE_CHILD;

    // Exit Internal 'Sonar_Activated': '<S7>:833'
    // Exit Internal 'Sonar_exitHandling': '<S7>:2135'
    rtDWork.is_Sonar_exitHandling = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Sonar_Activated = IN_NO_ACTIVE_CHILD;

    // Exit Internal 'Steering_Activated': '<S7>:1743'
    // Exit Internal 'Steering_exitHandling': '<S7>:2105'
    rtDWork.is_Steering_exitHandling = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Normal_activate = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Exit_Handling;

    // Entry 'Exit_Handling': '<S7>:1692'
    rtDWork.is_Exit_Handling = IN_Immediate_Exit;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::GearNotR_Activated_Compete_Requ
  (void)
{
  // Update for Outport: '<Root>/Out_competeReqStatus'
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

  // Inport: '<Root>/In_ignoreCompete'
  // During 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673'
  // '<S7>:1886:1' sf_internal_predicateOutput = in_ignoreCompete == true;
  if (rtU.In_ignoreCompete)
  {
    // Transition: '<S7>:1886'
    // Exit Internal 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673'
    rtDWork.is_GearNotR_Activated_Compete_R = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Gear_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Gear_Activate_SVS': '<S7>:1565'
    // '<S7>:1565:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1565:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1565:5' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Transition: '<S7>:2188'
    // Entry 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // '<S7>:2180:3' l_changedDuringGearExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // Transition: '<S7>:2198'
    rtDWork.temporalCounter_i6 = 0U;

    // '<S7>:1679:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
  {
    // Transition: '<S7>:1679'
    // Exit Internal 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673'
    rtDWork.is_GearNotR_Activated_Compete_R = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_GearNotR_Activated_Compete_m;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'GearNotR_Activated_Compete_RequestSucceed': '<S7>:1785'
    // '<S7>:1785:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

    // '<S7>:1802:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
  {
    // Transition: '<S7>:1802'
    // Transition: '<S7>:2082'
    // Transition: '<S7>:2080'
    // Exit Internal 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673'
    rtDWork.is_GearNotR_Activated_Compete_R = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
    // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

    // '<S7>:1801:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
  {
    // Transition: '<S7>:1801'
    // Transition: '<S7>:2083'
    // Transition: '<S7>:2081'
    // Exit Internal 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673'
    rtDWork.is_GearNotR_Activated_Compete_R = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
    // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
  }
  else if (static_cast<uint32_T>(rtDWork.is_GearNotR_Activated_Compete_R) ==
           IN_GearNotR_Activated_Compete_F)
  {
    // Outport: '<Root>/Out_competeScreenTypeReq'
    // During 'GearNotR_Activated_Compete_Float': '<S7>:2682'
    // '<S7>:2682:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_NOTR; 
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR;
  }
  else
  {
    // Outport: '<Root>/Out_competeScreenTypeReq'
    // During 'GearNotR_Activated_Compete_Full': '<S7>:2683'
    // '<S7>:2683:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FULL_NOTR; 
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_NOTR;
  }

  // End of Inport: '<Root>/In_ignoreCompete'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::GearR_Activated_Compete_Request
  (void)
{
  // Update for Outport: '<Root>/Out_competeReqStatus'
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

  // Inport: '<Root>/In_ignoreCompete'
  // During 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
  // '<S7>:1883:1' sf_internal_predicateOutput = in_ignoreCompete == true;
  if (rtU.In_ignoreCompete)
  {
    // Transition: '<S7>:1883'
    // Exit Internal 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
    rtDWork.is_GearR_Activated_Compete_Requ = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Gear_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Gear_Activate_SVS': '<S7>:1565'
    // '<S7>:1565:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1565:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1565:5' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Transition: '<S7>:2188'
    // Entry 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // '<S7>:2180:3' l_changedDuringGearExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // Transition: '<S7>:2198'
    rtDWork.temporalCounter_i6 = 0U;

    // '<S7>:1687:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
  {
    // Transition: '<S7>:1687'
    // Exit Internal 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
    rtDWork.is_GearR_Activated_Compete_Requ = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_GearR_Activated_Compete_Re_d;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'GearR_Activated_Compete_RequestSucceed': '<S7>:1778'
    // '<S7>:1778:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

    // '<S7>:1792:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
  {
    // Transition: '<S7>:1792'
    // Transition: '<S7>:2080'
    // Exit Internal 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
    rtDWork.is_GearR_Activated_Compete_Requ = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
    // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

    // '<S7>:1790:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
  {
    // Transition: '<S7>:1790'
    // Transition: '<S7>:2081'
    // Exit Internal 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
    rtDWork.is_GearR_Activated_Compete_Requ = IN_NO_ACTIVE_CHILD;
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
    // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
  }
  else if (static_cast<uint32_T>(rtDWork.is_GearR_Activated_Compete_Requ) ==
           IN_GearR_Activated_Compete_Floa)
  {
    // Outport: '<Root>/Out_competeScreenTypeReq'
    // During 'GearR_Activated_Compete_Float': '<S7>:2677'
    // '<S7>:2677:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_R; 
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_R;
  }
  else
  {
    // Outport: '<Root>/Out_competeScreenTypeReq'
    // During 'GearR_Activated_Compete_Full': '<S7>:2678'
    // '<S7>:2678:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FULL_R;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_R;
  }

  // End of Inport: '<Root>/In_ignoreCompete'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Manually_Activated_Compete_Requ
  (void)
{
  // Update for Outport: '<Root>/Out_competeReqStatus'
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

  // Inport: '<Root>/In_ignoreCompete'
  // During 'Manually_Activated_Compete_RequestStarted': '<S7>:1566'
  // '<S7>:1879:1' sf_internal_predicateOutput = in_ignoreCompete == true;
  if (rtU.In_ignoreCompete)
  {
    // Transition: '<S7>:1879'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Manually_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Manually_Activate_SVS': '<S7>:1564'
    //  VoiceDock, AndroidIcon, SteerButton
    // '<S7>:1564:4' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1564:5' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1564:6' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Manually_Activate_SVS': '<S7>:1564'
    // Transition: '<S7>:2181'
    // Entry 'Manually__Activate_ExitHandling': '<S7>:2179'
    // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
    // Transition: '<S7>:2184'
    rtDWork.temporalCounter_i6 = 0U;

    // '<S7>:1571:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
  {
    // Transition: '<S7>:1571'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_h;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestSucceed': '<S7>:1776'
    // '<S7>:1776:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

    // '<S7>:1697:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
  {
    // Transition: '<S7>:1697'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
    // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

    // '<S7>:1788:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
  {
    // Transition: '<S7>:1788'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
    // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
  }
  else
  {
    // no actions
  }

  // End of Inport: '<Root>/In_ignoreCompete'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::NarrowLane_Activated_Compete_Re
  (void)
{
  // Update for Outport: '<Root>/Out_competeReqStatus'
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

  // Inport: '<Root>/In_ignoreCompete'
  // During 'NarrowLane_Activated_Compete_RequestStarted': '<S7>:2541'
  // '<S7>:2546:1' sf_internal_predicateOutput = in_ignoreCompete == true;
  if (rtU.In_ignoreCompete)
  {
    // Transition: '<S7>:2546'
    // Transition: '<S7>:2528'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_NarrowLane_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'NarrowLane_Activated': '<S7>:2532'
    // '<S7>:2532:2' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:2532:3' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:2532:4' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

    // Entry Internal 'NarrowLane_Activated': '<S7>:2532'
    // Transition: '<S7>:2534'
    rtDWork.is_NarrowLane_Activated = IN_HoldOnwithSonarWarning;

    // '<S7>:2528:1' sf_internal_predicateOutput = in_ignoreCompete == true;
    // '<S7>:2542:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
  {
    // Transition: '<S7>:2542'
    rtDWork.is_Acivate_Through_Compete = IN_NarrowLane_Activated_Compe_p;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'NarrowLane_Activated_Compete_RequestSucceed': '<S7>:2543'
    // '<S7>:2543:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

    // '<S7>:2545:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
  {
    // Transition: '<S7>:2545'
    // Transition: '<S7>:2088'
    // Transition: '<S7>:2086'
    // Transition: '<S7>:2084'
    // Transition: '<S7>:2082'
    // Transition: '<S7>:2080'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
    // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

    // '<S7>:2544:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
  {
    // Transition: '<S7>:2544'
    // Transition: '<S7>:2089'
    // Transition: '<S7>:2087'
    // Transition: '<S7>:2085'
    // Transition: '<S7>:2083'
    // Transition: '<S7>:2081'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
    // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
  }
  else
  {
    // no actions
  }

  // End of Inport: '<Root>/In_ignoreCompete'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Steering_Activated_Compete_Requ
  (void)
{
  // Update for Outport: '<Root>/Out_competeReqStatus'
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

  // Inport: '<Root>/In_ignoreCompete'
  // During 'Steering_Activated_Compete_RequestStarted': '<S7>:1742'
  // '<S7>:1892:1' sf_internal_predicateOutput = in_ignoreCompete == true;
  if (rtU.In_ignoreCompete)
  {
    // Transition: '<S7>:1892'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Steering_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Steering_Activated': '<S7>:1743'
    // '<S7>:1743:2' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1743:3' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1743:4' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

    // Entry Internal 'Steering_Activated': '<S7>:1743'
    // Transition: '<S7>:2154'
    // Entry 'Steering_exitHandling': '<S7>:2105'
    // '<S7>:2105:3' l_changedDuringSteeringExit = (hasChanged(in_gear) || in_touchCoorChange); 
    rtDWork.is_Steering_exitHandling = IN_HasSteeringWarning;
    rtDWork.temporalCounter_i6 = 0U;

    // '<S7>:1744:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
  {
    // Transition: '<S7>:1744'
    rtDWork.is_Acivate_Through_Compete = IN_Steering_Activated_Compete_k;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Steering_Activated_Compete_RequestSucceed': '<S7>:1827'
    // '<S7>:1827:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

    // '<S7>:1813:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
  {
    // Transition: '<S7>:1813'
    // Transition: '<S7>:2086'
    // Transition: '<S7>:2084'
    // Transition: '<S7>:2082'
    // Transition: '<S7>:2080'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
    // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

    // '<S7>:1812:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
  }
  else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
  {
    // Transition: '<S7>:1812'
    // Transition: '<S7>:2087'
    // Transition: '<S7>:2085'
    // Transition: '<S7>:2083'
    // Transition: '<S7>:2081'
    rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
    // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
  }
  else
  {
    // no actions
  }

  // End of Inport: '<Root>/In_ignoreCompete'
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::Acivate_Through_Compete(void)
{
  // During 'Acivate_Through_Compete': '<S7>:1695'
  switch (rtDWork.is_Acivate_Through_Compete)
  {
   case IN_GearNotR_Activated_Compete_R:
    GearNotR_Activated_Compete_Requ();
    break;

   case IN_GearNotR_Activated_Compete_m:
    // During 'GearNotR_Activated_Compete_RequestSucceed': '<S7>:1785'
    // Transition: '<S7>:1796'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Gear_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Gear_Activate_SVS': '<S7>:1565'
    // '<S7>:1565:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1565:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1565:5' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Transition: '<S7>:2188'
    // Entry 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // '<S7>:2180:3' l_changedDuringGearExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // Transition: '<S7>:2198'
    rtDWork.temporalCounter_i6 = 0U;
    break;

   case IN_GearR_Activated_Compete_Requ:
    GearR_Activated_Compete_Request();
    break;

   case IN_GearR_Activated_Compete_Re_d:
    // During 'GearR_Activated_Compete_RequestSucceed': '<S7>:1778'
    // Transition: '<S7>:1779'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Gear_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Gear_Activate_SVS': '<S7>:1565'
    // '<S7>:1565:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1565:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1565:5' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Gear_Activate_SVS': '<S7>:1565'
    // Transition: '<S7>:2188'
    // Entry 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // '<S7>:2180:3' l_changedDuringGearExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Gear_Activate_SVS_ExitHandling': '<S7>:2180'
    // Transition: '<S7>:2198'
    rtDWork.temporalCounter_i6 = 0U;
    break;

   case IN_Manually_Activated_By_Voice_:
    // During 'Manually_Activated_By_Voice_But_Gear_D': '<S7>:2255'
    // Transition: '<S7>:2269'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Inactive;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Inactive': '<S7>:827'
    // '<S7>:827:3' out_systemActive = false;
    rtY.Out_systemActive = false;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:827:4' out_SVSShowReq = false;
    rtY.Out_SVSShowReq = false;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:827:5' out_showReqMode = EShowReqMode.SHOWREQ_NONE;
    rtY.Out_ShowReqMod = SHOWREQ_NONE;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // '<S7>:827:6' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:827:7' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    break;

   case IN_Manually_Activated_By_Voic_b:
    // During 'Manually_Activated_By_Voice_But_SR_Unsupported': '<S7>:2440'
    // Transition: '<S7>:2497'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Inactive;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Inactive': '<S7>:827'
    // '<S7>:827:3' out_systemActive = false;
    rtY.Out_systemActive = false;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:827:4' out_SVSShowReq = false;
    rtY.Out_SVSShowReq = false;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:827:5' out_showReqMode = EShowReqMode.SHOWREQ_NONE;
    rtY.Out_ShowReqMod = SHOWREQ_NONE;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // '<S7>:827:6' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:827:7' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    break;

   case IN_Manually_Activated_Compete_R:
    // During 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    // Transition: '<S7>:1781'
    // Exit Internal 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Inactive;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Inactive': '<S7>:827'
    // '<S7>:827:3' out_systemActive = false;
    rtY.Out_systemActive = false;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:827:4' out_SVSShowReq = false;
    rtY.Out_SVSShowReq = false;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:827:5' out_showReqMode = EShowReqMode.SHOWREQ_NONE;
    rtY.Out_ShowReqMod = SHOWREQ_NONE;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // '<S7>:827:6' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:827:7' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    break;

   case IN_Manually_Activated_Compete_i:
    Manually_Activated_Compete_Requ();
    break;

   case IN_Manually_Activated_Compete_h:
    // During 'Manually_Activated_Compete_RequestSucceed': '<S7>:1776'
    // Transition: '<S7>:1777'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Manually_Activate_SVS;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Manually_Activate_SVS': '<S7>:1564'
    //  VoiceDock, AndroidIcon, SteerButton
    // '<S7>:1564:4' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1564:5' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1564:6' out_showReqMode = EShowReqMode.SHOWREQ_FULL_SCREEN;
    rtY.Out_ShowReqMod = SHOWREQ_FULL_SCREEN;

    // Entry Internal 'Manually_Activate_SVS': '<S7>:1564'
    // Transition: '<S7>:2181'
    // Entry 'Manually__Activate_ExitHandling': '<S7>:2179'
    // '<S7>:2179:3' l_changedDuringMannuallExit = (hasChanged(in_gear) || in_touchCoorChange); 
    // Entry Internal 'Manually__Activate_ExitHandling': '<S7>:2179'
    // Transition: '<S7>:2184'
    rtDWork.temporalCounter_i6 = 0U;
    break;

   case IN_NarrowLane_Activated_Compete:
    NarrowLane_Activated_Compete_Re();
    break;

   case IN_NarrowLane_Activated_Compe_p:
    // During 'NarrowLane_Activated_Compete_RequestSucceed': '<S7>:2543'
    // Transition: '<S7>:2538'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_NarrowLane_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'NarrowLane_Activated': '<S7>:2532'
    // '<S7>:2532:2' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:2532:3' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:2532:4' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

    // Entry Internal 'NarrowLane_Activated': '<S7>:2532'
    // Transition: '<S7>:2534'
    rtDWork.is_NarrowLane_Activated = IN_HoldOnwithSonarWarning;
    break;

   case IN_Park_Activated_Compete_Reque:
    // Update for Outport: '<Root>/Out_competeReqStatus'
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

    // Inport: '<Root>/In_ignoreCompete'
    // During 'Park_Activated_Compete_RequestStarted': '<S7>:1689'
    // '<S7>:1894:1' sf_internal_predicateOutput = in_ignoreCompete == true;
    if (rtU.In_ignoreCompete)
    {
      // Transition: '<S7>:1894'
      rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
      rtDWork.is_ActiveState = IN_Normal_activate;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Normal_activate': '<S7>:816'
      // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

      // Outport: '<Root>/Out_competeScreenTypeReq'
      // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
      rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
      rtDWork.is_Normal_activate = IN_ParkActiveHandling;
      rtDWork.is_ParkActiveHandling = IN_Park_Activated;

      // Outport: '<Root>/Out_systemActive'
      // Entry 'Park_Activated': '<S7>:828'
      // '<S7>:828:3' out_systemActive = true;
      rtY.Out_systemActive = true;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // '<S7>:828:4' out_SVSShowReq = true;
      rtY.Out_SVSShowReq = true;

      // Update for Outport: '<Root>/Out_ShowReqMod'
      // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK;
      rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;

      // '<S7>:1690:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
    }
    else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
    {
      // Transition: '<S7>:1690'
      rtDWork.is_Acivate_Through_Compete = IN_Park_Activated_Compete_Req_p;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Park_Activated_Compete_RequestSucceed': '<S7>:1829'
      // '<S7>:1829:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

      // '<S7>:1821:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
    }
    else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
    {
      // Transition: '<S7>:1821'
      // Transition: '<S7>:2551'
      // Transition: '<S7>:2088'
      // Transition: '<S7>:2086'
      // Transition: '<S7>:2084'
      // Transition: '<S7>:2082'
      // Transition: '<S7>:2080'
      rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
      // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
      // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

      // '<S7>:1820:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
    }
    else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
    {
      // Transition: '<S7>:1820'
      // Transition: '<S7>:2552'
      // Transition: '<S7>:2089'
      // Transition: '<S7>:2087'
      // Transition: '<S7>:2085'
      // Transition: '<S7>:2083'
      // Transition: '<S7>:2081'
      rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
      // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
      // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
    }
    else
    {
      // no actions
    }
    break;

   case IN_Park_Activated_Compete_Req_p:
    // During 'Park_Activated_Compete_RequestSucceed': '<S7>:1829'
    // Transition: '<S7>:1830'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_ParkActiveHandling;
    rtDWork.is_ParkActiveHandling = IN_Park_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Park_Activated': '<S7>:828'
    // '<S7>:828:3' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:828:4' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:828:5' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_PARK;
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_PARK;
    break;

   case IN_Sonar_Activated_Compete_Requ:
    // Update for Outport: '<Root>/Out_competeReqStatus'
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

    // Inport: '<Root>/In_ignoreCompete'
    // During 'Sonar_Activated_Compete_RequestStarted': '<S7>:1680'
    // '<S7>:1889:1' sf_internal_predicateOutput = in_ignoreCompete == true;
    if (rtU.In_ignoreCompete)
    {
      // Transition: '<S7>:1889'
      rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
      rtDWork.is_ActiveState = IN_Normal_activate;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Normal_activate': '<S7>:816'
      // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

      // Outport: '<Root>/Out_competeScreenTypeReq'
      // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
      rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
      rtDWork.is_Normal_activate = IN_Sonar_Activated;

      // Outport: '<Root>/Out_systemActive'
      // Entry 'Sonar_Activated': '<S7>:833'
      // '<S7>:833:2' out_systemActive = true;
      rtY.Out_systemActive = true;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // '<S7>:833:3' out_SVSShowReq = true;
      rtY.Out_SVSShowReq = true;

      // Update for Outport: '<Root>/Out_ShowReqMod'
      // '<S7>:833:4' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
      rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

      // Entry Internal 'Sonar_Activated': '<S7>:833'
      // Transition: '<S7>:2164'
      rtDWork.is_Sonar_Activated = IN_HoldOnwithSonarWarning;

      // '<S7>:1682:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Passed); 
    }
    else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 1)
    {
      // Transition: '<S7>:1682'
      rtDWork.is_Acivate_Through_Compete = IN_Sonar_Activated_Compete_Re_k;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Sonar_Activated_Compete_RequestSucceed': '<S7>:1822'
      // '<S7>:1822:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_SUCCEED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_SUCCEED;

      // '<S7>:1807:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Request_Compete_TimeOut); 
    }
    else if (static_cast<int32_T>(rtDWork.is_Screen_Compete_Request) == 4)
    {
      // Transition: '<S7>:1807'
      // Transition: '<S7>:2084'
      // Transition: '<S7>:2082'
      // Transition: '<S7>:2080'
      rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
      // Entry 'Manually_Activated_Compete_TimeOut': '<S7>:1786'
      // '<S7>:1786:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_TIMEOUT; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_TIMEOUT;

      // '<S7>:1808:1' sf_internal_predicateOutput = in(Screen_Compete_Request.Has_Compete_Feedback.Compete_Refused); 
    }
    else if (static_cast<int32_T>(rtDWork.is_Has_Compete_Feedback) == 2)
    {
      // Transition: '<S7>:1808'
      // Transition: '<S7>:2085'
      // Transition: '<S7>:2083'
      // Transition: '<S7>:2081'
      rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_R;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'Manually_Activated_Compete_RequestFailed': '<S7>:1775'
      // Entry 'Manually_Activated_Compete_Refused': '<S7>:1787'
      // '<S7>:1787:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REFUSED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REFUSED;
    }
    else
    {
      // no actions
    }
    break;

   case IN_Sonar_Activated_Compete_Re_k:
    // During 'Sonar_Activated_Compete_RequestSucceed': '<S7>:1822'
    // Transition: '<S7>:1823'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Sonar_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Sonar_Activated': '<S7>:833'
    // '<S7>:833:2' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:833:3' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:833:4' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

    // Entry Internal 'Sonar_Activated': '<S7>:833'
    // Transition: '<S7>:2164'
    rtDWork.is_Sonar_Activated = IN_HoldOnwithSonarWarning;
    break;

   case IN_Steering_Activated_Compete_R:
    Steering_Activated_Compete_Requ();
    break;

   default:
    // During 'Steering_Activated_Compete_RequestSucceed': '<S7>:1827'
    // Transition: '<S7>:1828'
    rtDWork.is_Acivate_Through_Compete = IN_NO_ACTIVE_CHILD;
    rtDWork.is_ActiveState = IN_Normal_activate;

    // Update for Outport: '<Root>/Out_competeReqStatus'
    // Entry 'Normal_activate': '<S7>:816'
    // '<S7>:816:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
    rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

    // Outport: '<Root>/Out_competeScreenTypeReq'
    // '<S7>:816:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
    rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    rtDWork.is_Normal_activate = IN_Steering_Activated;

    // Outport: '<Root>/Out_systemActive'
    // Entry 'Steering_Activated': '<S7>:1743'
    // '<S7>:1743:2' out_systemActive = true;
    rtY.Out_systemActive = true;

    // Update for Outport: '<Root>/Out_SVSShowReq'
    // '<S7>:1743:3' out_SVSShowReq = true;
    rtY.Out_SVSShowReq = true;

    // Update for Outport: '<Root>/Out_ShowReqMod'
    // '<S7>:1743:4' out_showReqMode = EShowReqMode.SHOWREQ_FLOAT_SCREEN_NOTPARK; 
    rtY.Out_ShowReqMod = SHOWREQ_FLOAT_SCREEN_NOTPARK;

    // Entry Internal 'Steering_Activated': '<S7>:1743'
    // Transition: '<S7>:2154'
    // Entry 'Steering_exitHandling': '<S7>:2105'
    // '<S7>:2105:3' l_changedDuringSteeringExit = (hasChanged(in_gear) || in_touchCoorChange); 
    rtDWork.is_Steering_exitHandling = IN_HasSteeringWarning;
    rtDWork.temporalCounter_i6 = 0U;
    break;
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::ActiveState(const ESVSScreenType
  *UnitDelay9, const ESystemStr *in_SystemStr_prev, const ECalibStatus
  *in_calibStatus_prev, const uint8_T *in_calibActive_prev, const boolean_T
  *in_closeButtonPressed_prev, const boolean_T *in_competeQuit_prev)
{
  EGear tmp_0;
  boolean_T guard1 = false;

  // During 'ActiveState': '<S7>:835'
  guard1 = false;
  switch (rtDWork.is_ActiveState)
  {
   case IN_Acivate_Through_Compete:
    Acivate_Through_Compete();
    break;

   case IN_Exit_Handling:
    // During 'Exit_Handling': '<S7>:1692'
    if (static_cast<uint32_T>(rtDWork.is_Exit_Handling) == IN_Exit_Excuted)
    {
      // During 'Exit_Excuted': '<S7>:1714'
      // Transition: '<S7>:1735'
      rtDWork.is_Exit_Handling = IN_NO_ACTIVE_CHILD;
      rtDWork.is_ActiveState = IN_Inactive;

      // Outport: '<Root>/Out_systemActive'
      // Entry 'Inactive': '<S7>:827'
      // '<S7>:827:3' out_systemActive = false;
      rtY.Out_systemActive = false;

      // Update for Outport: '<Root>/Out_SVSShowReq'
      // '<S7>:827:4' out_SVSShowReq = false;
      rtY.Out_SVSShowReq = false;

      // Update for Outport: '<Root>/Out_ShowReqMod'
      // '<S7>:827:5' out_showReqMode = EShowReqMode.SHOWREQ_NONE;
      rtY.Out_ShowReqMod = SHOWREQ_NONE;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // '<S7>:827:6' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

      // Outport: '<Root>/Out_competeScreenTypeReq'
      // '<S7>:827:7' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
      rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
    }
    else
    {
      // During 'Immediate_Exit': '<S7>:1693'
      // Transition: '<S7>:1734'
      rtDWork.is_Exit_Handling = IN_Exit_Excuted;
    }
    break;

   case IN_Inactive:
    {
      // Update for Outport: '<Root>/Out_ShowReqMod'
      rtY.Out_ShowReqMod = SHOWREQ_NONE;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

      // During 'Inactive': '<S7>:827'
      // '<S7>:834:1' sf_internal_predicateOutput = hasChangedTo(in_parkingsts, true) &&... 
      // '<S7>:834:2' in(SuperState.SuperStateL2.Available);
      if ((rtDWork.in_parkingsts_prev != rtDWork.in_parkingsts_start) &&
          rtDWork.in_parkingsts_start && (static_cast<int32_T>
           (rtDWork.is_SuperStateL2) == 1))
      {
        // Transition: '<S7>:834'
        rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
        rtDWork.is_Acivate_Through_Compete = IN_Park_Activated_Compete_Reque;

        // Outport: '<Root>/Out_competeScreenTypeReq'
        // Entry 'Park_Activated_Compete_RequestStarted': '<S7>:1689'
        // '<S7>:1689:3' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_PARK; 
        rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_PARK;

        // Update for Outport: '<Root>/Out_competeReqStatus'
        // '<S7>:1689:4' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
        rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;
      }
      else
      {
        EDockAvmButtonPress tmp;
        EDockAvmButtonPress tmp_6;
        EVoiceDockReq tmp_2;
        EVoiceDockReq tmp_3;
        EVoiceDockReq tmp_4;

        // '<S7>:1400:1' sf_internal_predicateOutput = (in_gear~=EGear.R&&in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
        // '<S7>:1400:2' && hasChanged(in_voiceDockRequest)&&...
        // '<S7>:1400:3'  ~in_parkingsts) ...
        // '<S7>:1400:4' ||  (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
        // '<S7>:1400:5' && in(SuperState.SuperStateL2.Available))...
        // '<S7>:1400:6' || (hasChangedTo(in_androidIconActive,true)...
        // '<S7>:1400:7' && in(SuperState.SuperStateL2.Available))...
        // '<S7>:1400:8' || (hasChangedTo(in_steeringWheelButtonPressed,true)... 
        // '<S7>:1400:9' && in(SuperState.SuperStateL2.Available));
        tmp = rtDWork.in_dockAvmButtonPress_start;

        // Inport: '<Root>/In_gear'
        tmp_0 = rtU.In_gear;

        // Inport: '<Root>/In_voiceDockRequest'
        tmp_2 = rtU.In_voiceDockRequest;
        tmp_3 = rtDWork.in_voiceDockRequest_prev;
        tmp_4 = rtDWork.in_voiceDockRequest_start;
        tmp_6 = rtDWork.in_dockAvmButtonPress_prev;
        if (((static_cast<uint32_T>(tmp_0) != EGear_R) && (static_cast<uint32_T>
              (tmp_2) != EVoiceDockReq_NONE) && (tmp_3 != tmp_4) &&
             (!rtU.In_parkstatus)) || ((tmp_6 != tmp) && (static_cast<uint32_T>
              (tmp) == EDockAvmButtonPress_AVM_PRESS) && (static_cast<int32_T>
              (rtDWork.is_SuperStateL2) == 1)) ||
            ((rtDWork.in_androidIconActive_prev !=
              rtDWork.in_androidIconActive_start) &&
             rtDWork.in_androidIconActive_start && (static_cast<int32_T>
              (rtDWork.is_SuperStateL2) == 1)) ||
            ((rtDWork.in_steeringWheelButtonPressed_p !=
              rtDWork.in_steeringWheelButtonPressed_s) &&
             rtDWork.in_steeringWheelButtonPressed_s && (static_cast<int32_T>
              (rtDWork.is_SuperStateL2) == 1)))
        {
          // Transition: '<S7>:1400'
          // '<S7>:2267:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
          // '<S7>:2267:2' && hasChanged(in_voiceDockRequest))...
          // '<S7>:2267:3' &&(in_gear==EGear.D)...
          // '<S7>:2267:4' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_REAR... 
          // '<S7>:2267:5' ||in_voiceDockRequest==EVoiceDockReq.OPEN_REARWIDE... 
          // '<S7>:2267:6' ||in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL); 
          if ((static_cast<uint32_T>(tmp_2) != EVoiceDockReq_NONE) && (tmp_3 !=
               tmp_4) && (static_cast<uint32_T>(tmp_0) == EGear_D) && ((
                static_cast<uint32_T>(tmp_2) == EVoiceDockReq_OPEN_REAR) || (
                static_cast<uint32_T>(tmp_2) == EVoiceDockReq_OPEN_REARWIDE) ||
               (static_cast<uint32_T>(tmp_2) == EVoiceDockReq_OPEN_REARWHEEL)))
          {
            // Transition: '<S7>:2267'
            rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
            rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_By_Voice_;

            // Entry 'Manually_Activated_By_Voice_But_Gear_D': '<S7>:2255'
            // '<S7>:2255:3' out_voiceDockFeedback=EVoiceDockFb.OPEN_FAIL_NOTPN
            rtDWork.out_voiceDockFeedback_b = EVoiceDockFb_OPEN_FAIL_NOTPN;

            // '<S7>:2441:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
            // '<S7>:2441:2' && hasChanged(in_voiceDockRequest))...
            // '<S7>:2441:3' &&(in_SRIsTopActivity)...
            // '<S7>:2441:4' &&(in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONTWIDE... 
            // '<S7>:2441:5' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_SKELETON ... 
            // '<S7>:2441:6' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_3D... 
            // '<S7>:2441:7' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWIDE ... 
            // '<S7>:2441:8' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_FRONTWHEEL ... 
            // '<S7>:2441:9' || in_voiceDockRequest ==  EVoiceDockReq.OPEN_REARWHEEL); 
          }
          else if ((static_cast<uint32_T>(tmp_2) != EVoiceDockReq_NONE) &&
                   (tmp_3 != tmp_4) && rtU.In_SRIsTopActivity &&
                   ((static_cast<uint32_T>(tmp_2) ==
                     EVoiceDockReq_OPEN_FRONTWIDE) || (static_cast<uint32_T>
                     (tmp_2) == EVoiceDockReq_OPEN_SKELETON) ||
                    (static_cast<uint32_T>(tmp_2) == EVoiceDockReq_OPEN_3D) || (
                     static_cast<uint32_T>(tmp_2) == EVoiceDockReq_OPEN_REARWIDE)
                    || (static_cast<uint32_T>(tmp_2) ==
                        EVoiceDockReq_OPEN_FRONTWHEEL) || (static_cast<uint32_T>
                     (tmp_2) == EVoiceDockReq_OPEN_REARWHEEL)))
          {
            // Transition: '<S7>:2441'
            rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
            rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_By_Voic_b;

            // Entry 'Manually_Activated_By_Voice_But_SR_Unsupported': '<S7>:2440' 
            // '<S7>:2440:3' out_voiceDockFeedback=EVoiceDockFb.OPEN_FAIL_UNAVAILABLE_IN_SR 
            rtDWork.out_voiceDockFeedback_b =
              EVoiceDockFb_OPEN_FAIL_UNAVAILABLE_IN_SR;

            // '<S7>:2418:1' sf_internal_predicateOutput = (in_voiceDockRequest ~= EVoiceDockReq.NONE ... 
            // '<S7>:2418:2' && hasChanged(in_voiceDockRequest)...
            // '<S7>:2418:3' && in(SuperState.SuperStateL2.Available.Voice_Views)) ... 
            // '<S7>:2418:4' ||  (hasChangedTo(in_dockAvmButtonPress, EDockAvmButtonPress.AVM_PRESS)... 
            // '<S7>:2418:5' && in(SuperState.SuperStateL2.Available))...
            // '<S7>:2418:6' || (hasChangedTo(in_androidIconActive,true)...
            // '<S7>:2418:7' && in(SuperState.SuperStateL2.Available))...
            // '<S7>:2418:8' || (hasChangedTo(in_steeringWheelButtonPressed,true)... 
            // '<S7>:2418:9' && in(SuperState.SuperStateL2.Available));
          }
          else if (((static_cast<uint32_T>(tmp_2) != EVoiceDockReq_NONE) &&
                    (tmp_3 != tmp_4) && (static_cast<int32_T>
                     (rtDWork.is_Available) == 10)) || ((tmp_6 != tmp) && (
                     static_cast<uint32_T>(tmp) == EDockAvmButtonPress_AVM_PRESS)
                    && (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1)) ||
                   ((rtDWork.in_androidIconActive_prev !=
                     rtDWork.in_androidIconActive_start) &&
                    rtDWork.in_androidIconActive_start && (static_cast<int32_T>
                     (rtDWork.is_SuperStateL2) == 1)) ||
                   ((rtDWork.in_steeringWheelButtonPressed_p !=
                     rtDWork.in_steeringWheelButtonPressed_s) &&
                    rtDWork.in_steeringWheelButtonPressed_s &&
                    (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1)))
          {
            // Transition: '<S7>:2418'
            rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
            rtDWork.is_Acivate_Through_Compete = IN_Manually_Activated_Compete_i;

            // Update for Outport: '<Root>/Out_competeReqStatus'
            // Entry 'Manually_Activated_Compete_RequestStarted': '<S7>:1566'
            // '<S7>:1566:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
            rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

            // Outport: '<Root>/Out_competeScreenTypeReq'
            // '<S7>:1566:4' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FULL_NOTR; 
            rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_NOTR;
          }
          else
          {
            guard1 = true;
          }
        }
        else
        {
          guard1 = true;
        }
      }
    }
    break;

   default:
    Normal_activate(UnitDelay9, in_SystemStr_prev, in_calibStatus_prev,
                    in_calibActive_prev, in_closeButtonPressed_prev,
                    in_competeQuit_prev);
    break;
  }

  if (guard1)
  {
    EGear tmp_1;
    EGear tmp_5;

    // '<S7>:836:1' sf_internal_predicateOutput = ((in_isFirstRGear && in_gear   == EGear.R)||... 
    // '<S7>:836:2' (hasChangedTo(in_gear,EGear.R) && in_SystemStr==ESystemStr.SYSTEM_STR_NONE)||... 
    // '<S7>:836:3' ((in_gear==EGear.R)&&hasChangedTo(in_SystemStr,ESystemStr.SYSTEM_STR_NONE)))&&... 
    // '<S7>:836:4' in(SuperState.SuperStateL2.Available);
    tmp_1 = rtDWork.in_gear_start;
    tmp_5 = rtDWork.in_gear_prev;

    // Inport: '<Root>/In_isFirstRGear' incorporates:
    //   Inport: '<Root>/In_SystemStr'

    if (((rtU.In_isFirstRGear && (static_cast<uint32_T>(tmp_0) == EGear_R)) ||
         ((tmp_5 != tmp_1) && (static_cast<uint32_T>(tmp_1) == EGear_R) &&
          (rtU.In_SystemStr == ESystemStr_SYSTEM_STR_NONE)) ||
         ((static_cast<uint32_T>(tmp_0) == EGear_R) && ((*in_SystemStr_prev !=
            rtDWork.in_SystemStr_start) && (rtDWork.in_SystemStr_start ==
            ESystemStr_SYSTEM_STR_NONE)))) && (static_cast<int32_T>
         (rtDWork.is_SuperStateL2) == 1))
    {
      // Transition: '<S7>:836'
      rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
      rtDWork.is_Acivate_Through_Compete = IN_GearR_Activated_Compete_Requ;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
      // '<S7>:1686:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

      // Inport: '<Root>/In_ParkingSearch'
      // Entry Internal 'GearR_Activated_Compete_RequestStarted': '<S7>:1686'
      // Transition: '<S7>:2687'
      // '<S7>:2679:1' sf_internal_predicateOutput = in_ParkingSearch==true;
      if (rtU.In_ParkingSearch)
      {
        // Transition: '<S7>:2679'
        rtDWork.is_GearR_Activated_Compete_Requ =
          IN_GearR_Activated_Compete_Floa;

        // Outport: '<Root>/Out_competeScreenTypeReq'
        // Entry 'GearR_Activated_Compete_Float': '<S7>:2677'
        // '<S7>:2677:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_R; 
        rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_R;
      }
      else
      {
        // Transition: '<S7>:2680'
        rtDWork.is_GearR_Activated_Compete_Requ =
          IN_GearR_Activated_Compete_Full;

        // Outport: '<Root>/Out_competeScreenTypeReq'
        // Entry 'GearR_Activated_Compete_Full': '<S7>:2678'
        // '<S7>:2678:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FULL_R; 
        rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_R;
      }

      // '<S7>:2547:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&...
      // '<S7>:2547:2' hasChangedTo(in_narrowLaneAct, true)) ||...
      // '<S7>:2547:3' ( hasChangedTo(in_gear, EGear.D) &&...
      // '<S7>:2547:4' in_narrowLaneAct == true))&&...
      // '<S7>:2547:5' in(SuperState.SuperStateL2.Available);
    }
    else if ((((static_cast<uint32_T>(tmp_0) == EGear_D) &&
               ((rtDWork.in_narrowLaneAct_prev != rtDWork.in_narrowLaneAct_start)
                && rtDWork.in_narrowLaneAct_start)) || ((tmp_5 != tmp_1) && (
                static_cast<uint32_T>(tmp_1) == EGear_D) &&
               rtDWork.out_narrowLaneAct)) && (static_cast<int32_T>
              (rtDWork.is_SuperStateL2) == 1))
    {
      // Transition: '<S7>:2547'
      rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
      rtDWork.is_Acivate_Through_Compete = IN_NarrowLane_Activated_Compete;

      // Outport: '<Root>/Out_competeScreenTypeReq'
      // Entry 'NarrowLane_Activated_Compete_RequestStarted': '<S7>:2541'
      // '<S7>:2541:3' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_NOTR; 
      rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // '<S7>:2541:4' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

      // Inport: '<Root>/In_HaveEverActivated' incorporates:
      //   Inport: '<Root>/In_huDGearAct'
      //   Inport: '<Root>/In_isFirstDGear'

      // '<S7>:1677:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&...
      // '<S7>:1677:2' in_haveEverActived == false && ...
      // '<S7>:1677:3' in(SuperState.SuperStateL2.Available)&&...
      // '<S7>:1677:4' in_isFirstDGear)||...
      // '<S7>:1677:5' (hasChangedTo(in_gear, EGear.D) && ...
      // '<S7>:1677:6' in(SuperState.SuperStateL2.Available)&&...
      // '<S7>:1677:7' in_isFirstDGear)) &&...
      // '<S7>:1677:8' in_settingDGearActSts==ESettingSts.Set_ON;
    }
    else if ((((static_cast<uint32_T>(tmp_0) == EGear_D) &&
               (!rtU.In_HaveEverActivated) && (static_cast<int32_T>
                (rtDWork.is_SuperStateL2) == 1) && rtU.In_isFirstDGear) ||
              ((tmp_5 != tmp_1) && (static_cast<uint32_T>(tmp_1) == EGear_D) &&
               (static_cast<int32_T>(rtDWork.is_SuperStateL2) == 1) &&
               rtU.In_isFirstDGear)) && (rtU.In_huDGearAct == ESettingSts_Set_ON))
    {
      // Transition: '<S7>:1677'
      rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
      rtDWork.is_Acivate_Through_Compete = IN_GearNotR_Activated_Compete_R;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // Entry 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673'
      // '<S7>:1673:3' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

      // Inport: '<Root>/In_ParkingSearch'
      // Entry Internal 'GearNotR_Activated_Compete_RequestStarted': '<S7>:1673' 
      // Transition: '<S7>:2688'
      // '<S7>:2684:1' sf_internal_predicateOutput = in_ParkingSearch==true;
      if (rtU.In_ParkingSearch)
      {
        // Transition: '<S7>:2684'
        rtDWork.is_GearNotR_Activated_Compete_R =
          IN_GearNotR_Activated_Compete_F;

        // Outport: '<Root>/Out_competeScreenTypeReq'
        // Entry 'GearNotR_Activated_Compete_Float': '<S7>:2682'
        // '<S7>:2682:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_NOTR; 
        rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR;
      }
      else
      {
        // Transition: '<S7>:2685'
        rtDWork.is_GearNotR_Activated_Compete_R =
          IN_GearNotR_Activated_Compete_k;

        // Outport: '<Root>/Out_competeScreenTypeReq'
        // Entry 'GearNotR_Activated_Compete_Full': '<S7>:2683'
        // '<S7>:2683:2' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FULL_NOTR; 
        rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_NOTR;
      }

      // '<S7>:830:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&...
      // '<S7>:830:2' hasChangedTo(in_obstacleAct, true)) ||...
      // '<S7>:830:3' ( hasChangedTo(in_gear, EGear.D) &&...
      // '<S7>:830:4' in_obstacleAct == true))&&...
      // '<S7>:830:5' in(SuperState.SuperStateL2.Available);
    }
    else if ((((static_cast<uint32_T>(tmp_0) == EGear_D) &&
               ((rtDWork.in_obstacleAct_prev != rtDWork.in_obstacleAct_start) &&
                rtDWork.in_obstacleAct_start)) || ((tmp_5 != tmp_1) && (
                static_cast<uint32_T>(tmp_1) == EGear_D) &&
               rtDWork.out_obstacleAct)) && (static_cast<int32_T>
              (rtDWork.is_SuperStateL2) == 1))
    {
      // Transition: '<S7>:830'
      rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
      rtDWork.is_Acivate_Through_Compete = IN_Sonar_Activated_Compete_Requ;

      // Outport: '<Root>/Out_competeScreenTypeReq'
      // Entry 'Sonar_Activated_Compete_RequestStarted': '<S7>:1680'
      // '<S7>:1680:3' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_NOTR; 
      rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // '<S7>:1680:4' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

      // Inport: '<Root>/In_ParkingSearch'
      // '<S7>:821:1' sf_internal_predicateOutput = ((in_gear==EGear.D &&...
      // '<S7>:821:2' hasChangedTo(in_steeringAct, true)) ||...
      // '<S7>:821:3' ( hasChangedTo(in_gear, EGear.D) &&...
      // '<S7>:821:4' in_steeringAct == true))&&...
      // '<S7>:821:5' in(SuperState.SuperStateL2.Available)&&...
      // '<S7>:821:6' ~in_ParkingSearch;
    }
    else if ((((static_cast<uint32_T>(tmp_0) == EGear_D) &&
               ((rtDWork.in_steeringAct_prev != rtDWork.in_steeringAct_start) &&
                rtDWork.in_steeringAct_start)) || ((tmp_5 != tmp_1) && (
                static_cast<uint32_T>(tmp_1) == EGear_D) &&
               rtDWork.out_steeringAct)) && (static_cast<int32_T>
              (rtDWork.is_SuperStateL2) == 1) && (!rtU.In_ParkingSearch))
    {
      // Transition: '<S7>:821'
      rtDWork.is_ActiveState = IN_Acivate_Through_Compete;
      rtDWork.is_Acivate_Through_Compete = IN_Steering_Activated_Compete_R;

      // Outport: '<Root>/Out_competeScreenTypeReq'
      // Entry 'Steering_Activated_Compete_RequestStarted': '<S7>:1742'
      // '<S7>:1742:3' out_competeScreenTypeReq = ESVSScreenType.SCREEN_FLOAT_PLAN_NOTR; 
      rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR;

      // Update for Outport: '<Root>/Out_competeReqStatus'
      // '<S7>:1742:4' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_STARTED; 
      rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_STARTED;

      // End of Inport: '<Root>/In_HaveEverActivated'
    }
    else
    {
      // no actions
    }

    // End of Inport: '<Root>/In_isFirstRGear'
  }
}

// Function for Chart: '<S1>/MainSM_Lev2'
void ViewModeStateFlowStateMachineModelClass::enter_internal_c1_ViewModexView
  (void)
{
  // Entry Internal: MainSM_Lev1/MainSM_Lev2
  // Entry 'durationNoChange': '<S7>:598'
  // '<S7>:598:3' l_changed = (hasChanged(in_gear) || hasChanged(in_voiceDockRequest) || hasChanged(in_HUViewReq) || hasChanged(in_HUSVSMode) || ... 
  // '<S7>:598:4'     (in_touchCoorChange == true));
  // '<S7>:598:5' l_exitDelayDiffer = (in_exitDelay.ActiveExitDelay - in_exitDelay.PassiveExitDelay); 
  // Entry Internal 'durationNoChange': '<S7>:598'
  // Transition: '<S7>:600'
  rtDWork.is_durationNoChange = IN_reset_f;
  rtDWork.temporalCounter_i5 = 0U;

  // Entry 'reset': '<S7>:599'
  // Entry 'durationNoSonarWarning': '<S7>:2058'
  // '<S7>:2058:3' l_hasSonarWarning = ((in_sonarDistLevel.DistFrontLeftSide ~= 0) || (in_sonarDistLevel.DistFrontLeft ~= 0) || (in_sonarDistLevel.DistFrontCenter ~= 0) || ... 
  // '<S7>:2058:4'     (in_sonarDistLevel.DistFrontRight ~= 0) || (in_sonarDistLevel.DistFrontRightSide ~= 0)); 
  // Entry Internal 'durationNoSonarWarning': '<S7>:2058'
  // Transition: '<S7>:2064'
  rtDWork.is_durationNoSonarWarning = IN_HasSonarWarning;
  rtDWork.temporalCounter_i4 = 0U;

  // Entry 'HasSonarWarning': '<S7>:2062'
  // Entry 'Screen_Compete_Request': '<S7>:1646'
  // Entry Internal 'Screen_Compete_Request': '<S7>:1646'
  // Transition: '<S7>:1660'
  rtDWork.is_Screen_Compete_Request = IN_No_Compete_Req;

  // Entry 'No_Compete_Req': '<S7>:1831'
  // Entry Internal 'freeMode': '<S7>:699'
  // Entry Internal 'freeModeLogic': '<S7>:709'
  // Transition: '<S7>:707'
  rtDWork.is_freeModeLogic = IN_false;

  // Outport: '<Root>/Out_isFreeMode'
  // Entry 'false': '<S7>:700'
  // '<S7>:700:3' out_isFreeMode = false;
  rtY.Out_isFreeMode = false;

  // Entry Internal 'freeModeAct': '<S7>:710'
  // Transition: '<S7>:714'
  rtDWork.is_freeModeAct = IN_false;

  // Outport: '<Root>/Out_isFreeModeAct'
  // Entry 'false': '<S7>:713'
  // '<S7>:713:3' out_isFreeModeAct = false;
  rtY.Out_isFreeModeAct = false;

  // Entry Internal 'SuperState': '<S7>:792'
  // Transition: '<S7>:795'
  rtDWork.is_SuperState = IN_start;
  rtDWork.temporalCounter_i2 = 0U;

  // Outport: '<Root>/Out_systemAvailable'
  // Entry 'start': '<S7>:793'
  // '<S7>:793:3' out_systemAvailable = ESystem.Unavailable;
  rtY.Out_systemAvailable = ESystem_Unavailable;

  // Outport: '<Root>/Out_displayedView'
  // '<S7>:793:4' out_systemActive = false;
  // '<S7>:793:5' out_displayedView = EScreenID.NO_CHANGE;
  rtY.Out_displayedView = EScreenID_NO_CHANGE;

  // Update for Outport: '<Root>/Out_SVSCurrentView'
  // '<S7>:793:6' out_SVSCurrentView = EScreenID.NO_CHANGE;
  rtY.Out_SVSCurrentView = EScreenID_NO_CHANGE;

  // Outport: '<Root>/Out_SVSUnavlMsgs'
  // '<S7>:793:7' out_SVSUnavlMsgs = ENotActiveReason.NONE;
  rtY.Out_SVSUnavlMsgs = ENotActiveReason_NONE;

  // Outport: '<Root>/Out_SVSViewModeSts'
  // '<S7>:793:8' out_SVSViewModeSts = ESVSViewMode.VM_Standard;
  rtY.Out_SVSViewModeSts = ESVSViewMode_VM_Standard;

  // '<S7>:793:9' local_bool_initialized = false;
  // Entry 'ActiveState': '<S7>:835'
  // Entry Internal 'ActiveState': '<S7>:835'
  // Transition: '<S7>:831'
  rtDWork.is_ActiveState = IN_Inactive;

  // Outport: '<Root>/Out_systemActive'
  // Entry 'Inactive': '<S7>:827'
  // '<S7>:827:3' out_systemActive = false;
  rtY.Out_systemActive = false;

  // Update for Outport: '<Root>/Out_SVSShowReq'
  // '<S7>:827:4' out_SVSShowReq = false;
  rtY.Out_SVSShowReq = false;

  // Update for Outport: '<Root>/Out_ShowReqMod'
  // '<S7>:827:5' out_showReqMode = EShowReqMode.SHOWREQ_NONE;
  rtY.Out_ShowReqMod = SHOWREQ_NONE;

  // Update for Outport: '<Root>/Out_competeReqStatus'
  // '<S7>:827:6' out_competeReqStatus = ECompeteReqStatus.COMPETE_REQ_NONE;
  rtY.Out_competeReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;

  // Outport: '<Root>/Out_competeScreenTypeReq'
  // '<S7>:827:7' out_competeScreenTypeReq = ESVSScreenType.SCREEN_NONE;
  rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_NONE;
}

// Function for Chart: '<S1>/screenTypeHandling'
void ViewModeStateFlowStateMachineModelClass::enter_internal_SVSScreenType_no
  (void)
{
  EGear tmp;

  // Inport: '<Root>/In_gear'
  // Entry Internal 'SVSScreenType_notParkScreen': '<S11>:201'
  // Transition: '<S11>:203'
  // '<S11>:218:1' sf_internal_predicateOutput = (in_displayedView == EScreenID.FLOAT_REAR_PLAN_VIEW||... 
  // '<S11>:218:2' in_displayedView == EScreenID.FLOAT_FRONT_PLAN_VIEW)&&...
  // '<S11>:218:3' in_gear~=EGear.R;
  tmp = rtU.In_gear;

  // Outport: '<Root>/Out_displayedView' incorporates:
  //   Outport: '<Root>/Out_SVSScreenType'

  if (((rtY.Out_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW) ||
       (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW)) && (
       static_cast<uint32_T>(tmp) != EGear_R))
  {
    // Transition: '<S11>:218'
    // Entry 'SVSScreenType_SCREEN_FLOAT_PLAN_NOTR': '<S11>:215'
    // '<S11>:215:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_PLAN_NOTR;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR;

    // '<S11>:205:1' sf_internal_predicateOutput = (in_displayedView == EScreenID.FLOAT_REAR_VIEW || ... 
    // '<S11>:205:2' in_displayedView == EScreenID.FLOAT_FRONT_VIEW)&&...
    // '<S11>:205:3' in_gear==EGear.R;
  }
  else if (((rtY.Out_displayedView == EScreenID_FLOAT_REAR_VIEW) ||
            (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_VIEW)) && (
            static_cast<uint32_T>(tmp) == EGear_R))
  {
    // Transition: '<S11>:205'
    // Entry 'SVSScreenType_SCREEN_FLOAT_FR_R': '<S11>:216'
    // '<S11>:216:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_FR_R;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_FR_R;

    // '<S11>:240:1' sf_internal_predicateOutput = (in_displayedView == EScreenID.FLOAT_REAR_PLAN_VIEW||... 
    // '<S11>:240:2' in_displayedView == EScreenID.FLOAT_FRONT_PLAN_VIEW)&&...
    // '<S11>:240:3' in_gear==EGear.R;
  }
  else if (((rtY.Out_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW) ||
            (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW)) && (
            static_cast<uint32_T>(tmp) == EGear_R))
  {
    // Transition: '<S11>:240'
    // Entry 'SVSScreenType_SCREEN_FLOAT_PLAN_R': '<S11>:206'
    // '<S11>:206:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_PLAN_R;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_PLAN_R;

    // '<S11>:241:1' sf_internal_predicateOutput = (in_displayedView == EScreenID.FLOAT_REAR_VIEW || ... 
    // '<S11>:241:2' in_displayedView == EScreenID.FLOAT_FRONT_VIEW)&&...
    // '<S11>:241:3' in_gear~=EGear.R;
  }
  else if (((rtY.Out_displayedView == EScreenID_FLOAT_REAR_VIEW) ||
            (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_VIEW)) && (
            static_cast<uint32_T>(tmp) != EGear_R))
  {
    // Transition: '<S11>:241'
    // Entry 'SVSScreenType_SCREEN_FLOAT_FR_NOTR': '<S11>:207'
    // '<S11>:207:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_FR_NOTR;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_FR_NOTR;
  }
  else
  {
    // no actions
  }

  // End of Outport: '<Root>/Out_displayedView'
}

// Function for Chart: '<S1>/screenTypeHandling'
void ViewModeStateFlowStateMachineModelClass::enter_internal_SVSScreenType_Sh
  (void)
{
  // Outport: '<Root>/Out_displayedView' incorporates:
  //   Inport: '<Root>/In_gear'
  //   Outport: '<Root>/Out_SVSScreenType'

  // Entry Internal 'SVSScreenType_Shown': '<S11>:150'
  // Transition: '<S11>:196'
  // '<S11>:174:1' sf_internal_predicateOutput = in_displayedView == EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW||... 
  // '<S11>:174:2' in_displayedView == EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW||... 
  // '<S11>:174:3' in_displayedView == EScreenID.FLOAT_PARKING_FRONT_VIEW||...
  // '<S11>:174:4' in_displayedView == EScreenID.FLOAT_PARKING_REAR_VIEW;
  if ((rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW) ||
      (rtY.Out_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW) ||
      (rtY.Out_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW) ||
      (rtY.Out_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW))
  {
    // Transition: '<S11>:174'
    // Entry 'SVSScreenType_ParkScreen': '<S11>:182'
    // Entry Internal 'SVSScreenType_ParkScreen': '<S11>:182'
    // Transition: '<S11>:228'
    // '<S11>:226:1' sf_internal_predicateOutput = [in_displayedView == EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW||... 
    // '<S11>:226:2' in_displayedView == EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW]; 
    if ((rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW) ||
        (rtY.Out_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW))
    {
      // Transition: '<S11>:226'
      // Entry 'SVSScreenType_SCREEN_FLOAT_PLAN_PARK1': '<S11>:227'
      // '<S11>:227:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_PLAN_PARK; 
      rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_PLAN_PARK;
    }
    else
    {
      // Transition: '<S11>:224'
      // Entry 'SVSScreenType_SCREEN_FLOAT_FR_PARK': '<S11>:210'
      // '<S11>:210:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_FR_PARK;
      rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_FR_PARK;
    }

    // '<S11>:200:1' sf_internal_predicateOutput = in_displayedView == EScreenID.FLOAT_REAR_VIEW || ... 
    // '<S11>:200:2' in_displayedView == EScreenID.FLOAT_FRONT_VIEW || ...
    // '<S11>:200:3' in_displayedView == EScreenID.FLOAT_FRONT_PLAN_VIEW || ...
    // '<S11>:200:4' in_displayedView == EScreenID.FLOAT_REAR_PLAN_VIEW;
  }
  else if ((rtY.Out_displayedView == EScreenID_FLOAT_REAR_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW))
  {
    // Transition: '<S11>:200'
    // Entry 'SVSScreenType_notParkScreen': '<S11>:201'
    enter_internal_SVSScreenType_no();

    // Transition: '<S11>:194'
    // Entry 'SVSScreenType_FullScreen': '<S11>:190'
    // Entry Internal 'SVSScreenType_FullScreen': '<S11>:190'
    // Transition: '<S11>:180'
    // '<S11>:193:1' sf_internal_predicateOutput = in_gear == EGear.R;
  }
  else if (static_cast<uint32_T>(rtU.In_gear) == EGear_R)
  {
    // Transition: '<S11>:193'
    // Entry 'SVSScreenType_FullScreenR': '<S11>:169'
    // '<S11>:169:3' out_SVSScreenType = ESVSScreenType.SCREEN_FULL_R;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FULL_R;
  }
  else
  {
    // Transition: '<S11>:192'
    // Entry 'SVSScreenType_FullScreenNotR': '<S11>:166'
    // '<S11>:166:3' out_SVSScreenType = ESVSScreenType.SCREEN_FULL_NOTR;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FULL_NOTR;
  }

  // End of Outport: '<Root>/Out_displayedView'
}

//
// Function for Chart: '<S1>/CpcHuSwitch'
// function b_retInArea  = checkCoorInResponseArea(f_CoorX, f_CoorY, f_HmiLayout, f_TouchSt)
//
boolean_T ViewModeStateFlowStateMachineModelClass::checkCoorInResponseArea
  (uint32_T f_CoorX, uint32_T f_CoorY, uint16_T f_HmiLayout_iconCenter_x,
   uint16_T f_HmiLayout_iconCenter_y, uint16_T f_HmiLayout_responseArea_x,
   uint16_T f_HmiLayout_responseArea_y, ETouchStatus f_TouchSt)
{
  uint32_T qY;
  uint32_T qY_0;
  uint32_T qY_tmp;
  uint32_T tmp;
  boolean_T b_retInArea;

  // MATLAB Function 'checkCoorInResponseArea': '<S2>:105'
  // Graphical Function 'checkCoorInResponseArea': '<S2>:105'
  // Transition: '<S2>:124'
  // '<S2>:119:1' sf_internal_predicateOutput = (f_CoorX > (f_HmiLayout.iconCenter.x - (0.5 * f_HmiLayout.responseArea.x))) && (f_CoorX < (f_HmiLayout.iconCenter.x + (0.5 * f_HmiLayout.responseArea.x))) &&... 
  // '<S2>:119:2' (f_CoorY > (f_HmiLayout.iconCenter.y - (0.5 * f_HmiLayout.responseArea.y))) && (f_CoorY < (f_HmiLayout.iconCenter.y + (0.5 * f_HmiLayout.responseArea.y))) &&... 
  // '<S2>:119:3'  ((f_TouchSt == ETouchStatus.Press) || (f_TouchSt == ETouchStatus.Slither)); 
  qY_tmp = static_cast<uint32_T>(rt_roundd(0.5 * static_cast<real_T>
    (f_HmiLayout_responseArea_x)));
  qY = static_cast<uint32_T>(f_HmiLayout_iconCenter_x) - /*MW:OvSatOk*/ qY_tmp;
  if (qY > static_cast<uint32_T>(f_HmiLayout_iconCenter_x))
  {
    qY = 0U;
  }

  tmp = qY_tmp + static_cast<uint32_T>(f_HmiLayout_iconCenter_x);
  if (tmp > 65535U)
  {
    tmp = 65535U;
  }

  qY_tmp = static_cast<uint32_T>(rt_roundd(0.5 * static_cast<real_T>
    (f_HmiLayout_responseArea_y)));
  qY_0 = static_cast<uint32_T>(f_HmiLayout_iconCenter_y) - /*MW:OvSatOk*/ qY_tmp;
  if (qY_0 > static_cast<uint32_T>(f_HmiLayout_iconCenter_y))
  {
    qY_0 = 0U;
  }

  qY_tmp += static_cast<uint32_T>(f_HmiLayout_iconCenter_y);
  if (qY_tmp > 65535U)
  {
    qY_tmp = 65535U;
  }

  if ((f_CoorX > qY) && (f_CoorX < tmp) && (f_CoorY > qY_0) && (f_CoorY < qY_tmp)
      && ((f_TouchSt == ETouchStatus_Press) || (f_TouchSt ==
        ETouchStatus_Slither)))
  {
    // Transition: '<S2>:119'
    // Transition: '<S2>:123'
    // '<S2>:123:1' b_retInArea = true
    b_retInArea = true;
  }
  else
  {
    // Transition: '<S2>:120'
    // '<S2>:120:1' b_retInArea = false
    b_retInArea = false;

    // Transition: '<S2>:122'
  }

  return b_retInArea;
}

// Model step function
void ViewModeStateFlowStateMachineModelClass::step()
{
  uint32_T in_CoorX_prev;
  uint32_T in_CoorY_prev;
  uint32_T in_campositionX_prev;
  uint32_T in_campositionY_prev;
  uint8_T in_calibActive_prev;
  boolean_T in_closeButtonPressed_prev;
  boolean_T in_competeQuit_prev;
  boolean_T l_isViewChangeAvl_Hori;
  boolean_T out_freemodeviewAct;
  boolean_T out_isCpcShow;
  boolean_T out_isViewChangeAvl;
  boolean_T out_manualChangeAvail;
  ECalibStatus in_calibStatus_prev;
  ESVSScreenType UnitDelay9;
  ESVSViewMode UnitDelay1;
  EScreenID in_displayedVewL_prev;
  ESystemStr in_SystemStr_prev;

  // Chart: '<S1>/ManualChangeAvailHandling' incorporates:
  //   Inport: '<Root>/In_parkstatus'

  // Gateway: MainSM_Lev1/ManualChangeAvailHandling
  // During: MainSM_Lev1/ManualChangeAvailHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c4_ViewModexViewState) == 0U)
  {
    // Entry: MainSM_Lev1/ManualChangeAvailHandling
    rtDWork.is_active_c4_ViewModexViewState = 1U;

    // Entry Internal: MainSM_Lev1/ManualChangeAvailHandling
    // Transition: '<S8>:7'
    rtDWork.is_c4_ViewModexViewStateMachine = IN_true;

    // Entry 'true': '<S8>:5'
    // '<S8>:5:3' out_manualChangeAvail = true;
    out_manualChangeAvail = true;
  }
  else if (static_cast<uint32_T>(rtDWork.is_c4_ViewModexViewStateMachine) ==
           IN_false)
  {
    out_manualChangeAvail = false;

    // During 'false': '<S8>:6'
    // '<S8>:9:1' sf_internal_predicateOutput =  ~in_parkingsts;
    if (!rtU.In_parkstatus)
    {
      // Transition: '<S8>:9'
      rtDWork.is_c4_ViewModexViewStateMachine = IN_true;

      // Entry 'true': '<S8>:5'
      // '<S8>:5:3' out_manualChangeAvail = true;
      out_manualChangeAvail = true;
    }
  }
  else
  {
    out_manualChangeAvail = true;

    // During 'true': '<S8>:5'
    // '<S8>:10:1' sf_internal_predicateOutput =  in_parkingsts;
    if (rtU.In_parkstatus)
    {
      // Transition: '<S8>:10'
      rtDWork.is_c4_ViewModexViewStateMachine = IN_false;

      // Entry 'false': '<S8>:6'
      // '<S8>:6:3' out_manualChangeAvail = false;
      out_manualChangeAvail = false;
    }
  }

  // End of Chart: '<S1>/ManualChangeAvailHandling'

  // UnitDelay: '<S1>/Unit Delay'
  rtDWork.UnitDelay = rtDWork.UnitDelay_DSTATE;

  // UnitDelay: '<S1>/Unit Delay1'
  UnitDelay1 = rtDWork.UnitDelay1_DSTATE;

  // UnitDelay: '<S1>/Unit Delay5'
  rtDWork.UnitDelay5 = rtDWork.UnitDelay5_DSTATE;

  // Chart: '<S1>/FreeModeHandling' incorporates:
  //   Inport: '<Root>/In_HUTouchCoorX'
  //   Inport: '<Root>/In_HUTouchCoorY'
  //   Inport: '<Root>/In_TouchSt'
  //   Inport: '<Root>/In_campositionX'
  //   Inport: '<Root>/In_campositionY'
  //   UnitDelay: '<S1>/Unit Delay1'

  if (static_cast<uint32_T>(rtDWork.temporalCounter_i1_p) < 127U)
  {
    rtDWork.temporalCounter_i1_p = static_cast<uint8_T>(static_cast<uint32_T>
      (rtDWork.temporalCounter_i1_p) + 1U);
  }

  // Gateway: MainSM_Lev1/FreeModeHandling
  in_campositionX_prev = rtDWork.in_campositionX_start;
  rtDWork.in_campositionX_start = rtU.In_campositionX;
  in_campositionY_prev = rtDWork.in_campositionY_start;
  rtDWork.in_campositionY_start = rtU.In_campositionY;
  in_CoorX_prev = rtDWork.in_CoorX_start;
  rtDWork.in_CoorX_start = rtU.In_HUTouchCoorX;
  in_CoorY_prev = rtDWork.in_CoorY_start;
  rtDWork.in_CoorY_start = rtU.In_HUTouchCoorY;
  in_displayedVewL_prev = rtDWork.in_displayedVewL_start;
  rtDWork.in_displayedVewL_start = rtDWork.UnitDelay5;

  // During: MainSM_Lev1/FreeModeHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c6_ViewModexViewState) == 0U)
  {
    // Entry: MainSM_Lev1/FreeModeHandling
    rtDWork.is_active_c6_ViewModexViewState = 1U;

    // Entry Internal: MainSM_Lev1/FreeModeHandling
    // Entry Internal 'huTouch': '<S5>:43'
    // Transition: '<S5>:5'
    rtDWork.temporalCounter_i1_p = 0U;

    // Entry Internal 'touchCoorChange': '<S5>:4'
    // Transition: '<S5>:29'
    // '<S5>:32:1' sf_internal_predicateOutput = hasChanged(in_CoorX) ||...
    // '<S5>:32:2' hasChanged(in_CoorY);
    if ((rtU.In_HUTouchCoorX != rtDWork.in_CoorX_start) || (rtU.In_HUTouchCoorY
         != rtDWork.in_CoorY_start))
    {
      // Transition: '<S5>:32'
      // Transition: '<S5>:34'
      // '<S5>:34:1' out_touchCoorChange = true;
      rtDWork.out_touchCoorChange = true;

      // Transition: '<S5>:39'
    }
    else
    {
      // Transition: '<S5>:36'
      // Transition: '<S5>:38'
      // '<S5>:38:1' out_touchCoorChange = false;
      rtDWork.out_touchCoorChange = false;
    }

    // Entry 'ViewID': '<S5>:56'
    enter_internal_ViewID(&UnitDelay1);

    // Entry Internal 'FreeModeVariantHandling': '<S5>:210'
    // Transition: '<S5>:211'
    // Transition: '<S5>:219'
    // '<S5>:219:1' l_lowerLimitX = 846;
    rtDWork.l_lowerLimitX = 846.0;

    // '<S5>:219:2' l_upperLimitX=2560;
    rtDWork.l_upperLimitX = 2560.0;

    // '<S5>:219:3' l_upperLimitY=1320;
    rtDWork.l_upperLimitY = 1320.0;

    // Entry 'freeModeStatus': '<S5>:77'
    // '<S5>:77:3' l_isViewChangeAvl_Hori = ( ETouchStatus.Slither == in_TouchSt && ... 
    // '<S5>:77:4'     (in_CoorX > l_lowerLimitX && in_CoorX < l_upperLimitX &&... 
    // '<S5>:77:5'     in_CoorY < l_upperLimitY) &&...
    // '<S5>:77:6'     (ESVSViewMode.VM_Perspective == in_vewModeL) && ...
    // '<S5>:77:7'     (EScreenID.PERSPECTIVE_RL   == in_displayedVewL ||...
    // '<S5>:77:8'     EScreenID.PERSPECTIVE_FL     == in_displayedVewL ||...
    // '<S5>:77:9'     EScreenID.PERSPECTIVE_PFR  == in_displayedVewL ||...
    // '<S5>:77:10'     EScreenID.PERSPECTIVE_FR  	 == in_displayedVewL ||...
    // '<S5>:77:11'     EScreenID.PERSPECTIVE_RR   == in_displayedVewL ||...
    // '<S5>:77:12'     EScreenID.PERSPECTIVE_PLE  	 == in_displayedVewL ||...
    // '<S5>:77:13'     EScreenID.PERSPECTIVE_PRI   == in_displayedVewL ||...
    // '<S5>:77:14'     EScreenID.PERSPECTIVE_PRE    == in_displayedVewL));
    // Entry Internal 'freeModeStatus': '<S5>:77'
    // Entry 'isViewChangeAvl': '<S5>:44'
    // Entry Internal 'isViewChangeAvl': '<S5>:44'
    // Transition: '<S5>:46'
    rtDWork.is_isViewChangeAvl = IN_true;

    // Entry 'true': '<S5>:45'
    // '<S5>:45:3' out_isViewChangeAvl = true;
    out_isViewChangeAvl = true;

    // Entry Internal 'freemodeActive': '<S5>:50'
    // Transition: '<S5>:53'
    rtDWork.is_freemodeActive = IN_false;

    // Entry 'false': '<S5>:51'
    // '<S5>:51:3' out_freemodeviewAct = false;
    out_freemodeviewAct = false;
  }
  else
  {
    // During 'huTouch': '<S5>:43'
    // During 'touchCoorChange': '<S5>:4'
    // '<S5>:40:1' sf_internal_predicateOutput = after(1, sec);
    if (static_cast<uint32_T>(rtDWork.temporalCounter_i1_p) >= 100U)
    {
      // Transition: '<S5>:40'
      // Entry Internal 'touchCoorChange': '<S5>:4'
      // Transition: '<S5>:29'
      // '<S5>:32:1' sf_internal_predicateOutput = hasChanged(in_CoorX) ||...
      // '<S5>:32:2' hasChanged(in_CoorY);
      if ((in_CoorX_prev != rtDWork.in_CoorX_start) || (in_CoorY_prev !=
           rtDWork.in_CoorY_start))
      {
        // Transition: '<S5>:32'
        // Transition: '<S5>:34'
        // '<S5>:34:1' out_touchCoorChange = true;
        rtDWork.out_touchCoorChange = true;

        // Transition: '<S5>:39'
      }
      else
      {
        // Transition: '<S5>:36'
        // Transition: '<S5>:38'
        // '<S5>:38:1' out_touchCoorChange = false;
        rtDWork.out_touchCoorChange = false;
      }
    }

    ViewID(&UnitDelay1, &in_campositionY_prev, &in_displayedVewL_prev);

    // During 'FreeModeVariantHandling': '<S5>:210'
    // During 'freeModeStatus': '<S5>:77'
    // '<S5>:77:3' l_isViewChangeAvl_Hori = ( ETouchStatus.Slither == in_TouchSt && ... 
    // '<S5>:77:4'     (in_CoorX > l_lowerLimitX && in_CoorX < l_upperLimitX &&... 
    // '<S5>:77:5'     in_CoorY < l_upperLimitY) &&...
    // '<S5>:77:6'     (ESVSViewMode.VM_Perspective == in_vewModeL) && ...
    // '<S5>:77:7'     (EScreenID.PERSPECTIVE_RL   == in_displayedVewL ||...
    // '<S5>:77:8'     EScreenID.PERSPECTIVE_FL     == in_displayedVewL ||...
    // '<S5>:77:9'     EScreenID.PERSPECTIVE_PFR  == in_displayedVewL ||...
    // '<S5>:77:10'     EScreenID.PERSPECTIVE_FR  	 == in_displayedVewL ||...
    // '<S5>:77:11'     EScreenID.PERSPECTIVE_RR   == in_displayedVewL ||...
    // '<S5>:77:12'     EScreenID.PERSPECTIVE_PLE  	 == in_displayedVewL ||...
    // '<S5>:77:13'     EScreenID.PERSPECTIVE_PRI   == in_displayedVewL ||...
    // '<S5>:77:14'     EScreenID.PERSPECTIVE_PRE    == in_displayedVewL));
    l_isViewChangeAvl_Hori = ((ETouchStatus_Slither == rtU.In_TouchSt) && (
      static_cast<real_T>(rtU.In_HUTouchCoorX) > rtDWork.l_lowerLimitX) && (
      static_cast<real_T>(rtU.In_HUTouchCoorX) < rtDWork.l_upperLimitX) && (
      static_cast<real_T>(rtU.In_HUTouchCoorY) < rtDWork.l_upperLimitY) &&
      (ESVSViewMode_VM_Perspective == rtDWork.UnitDelay1_DSTATE) &&
      ((EScreenID_PERSPECTIVE_RL == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_FL == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PFR == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_FR == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_RR == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PLE == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PRI == rtDWork.UnitDelay5) ||
       (EScreenID_PERSPECTIVE_PRE == rtDWork.UnitDelay5)));

    // During 'isViewChangeAvl': '<S5>:44'
    if (static_cast<uint32_T>(rtDWork.is_isViewChangeAvl) == IN_false)
    {
      out_isViewChangeAvl = false;

      // During 'false': '<S5>:47'
      // '<S5>:49:1' sf_internal_predicateOutput = ~l_isViewChangeAvl_Hori;
      if (!l_isViewChangeAvl_Hori)
      {
        // Transition: '<S5>:49'
        rtDWork.is_isViewChangeAvl = IN_true;

        // Entry 'true': '<S5>:45'
        // '<S5>:45:3' out_isViewChangeAvl = true;
        out_isViewChangeAvl = true;
      }
    }
    else
    {
      out_isViewChangeAvl = true;

      // During 'true': '<S5>:45'
      // '<S5>:48:1' sf_internal_predicateOutput = l_isViewChangeAvl_Hori;
      if (l_isViewChangeAvl_Hori)
      {
        // Transition: '<S5>:48'
        rtDWork.is_isViewChangeAvl = IN_false;

        // Entry 'false': '<S5>:47'
        // '<S5>:47:3' out_isViewChangeAvl = false;
        out_isViewChangeAvl = false;
      }
    }

    // During 'freemodeActive': '<S5>:50'
    if (static_cast<uint32_T>(rtDWork.is_freemodeActive) == IN_false)
    {
      out_freemodeviewAct = false;

      // During 'false': '<S5>:51'
      // '<S5>:54:1' sf_internal_predicateOutput = (hasChanged(in_campositionX) || ... 
      // '<S5>:54:2' hasChanged(in_campositionY)) && ...
      // '<S5>:54:3' (l_isViewChangeAvl_Hori);
      if (((in_campositionX_prev != rtDWork.in_campositionX_start) ||
           (in_campositionY_prev != rtDWork.in_campositionY_start)) &&
          l_isViewChangeAvl_Hori)
      {
        // Transition: '<S5>:54'
        rtDWork.is_freemodeActive = IN_true;

        // Entry 'true': '<S5>:52'
        // '<S5>:52:3' out_freemodeviewAct = true;
        out_freemodeviewAct = true;
      }
    }
    else
    {
      out_freemodeviewAct = true;

      // During 'true': '<S5>:52'
      // '<S5>:55:1' sf_internal_predicateOutput = ~l_isViewChangeAvl_Hori;
      if (!l_isViewChangeAvl_Hori)
      {
        // Transition: '<S5>:55'
        rtDWork.is_freemodeActive = IN_false;

        // Entry 'false': '<S5>:51'
        // '<S5>:51:3' out_freemodeviewAct = false;
        out_freemodeviewAct = false;
      }
    }
  }

  // End of Chart: '<S1>/FreeModeHandling'

  // BusCreator generated from: '<S1>/MainSM_Lev2' incorporates:
  //   Inport: '<Root>/In_animationState'

  rtDWork.BusConversion_InsertedFor_Mai_m = rtU.In_animationState;

  // Chart: '<S1>/DegradationHandling' incorporates:
  //   Inport: '<Root>/In_IMB_FrontSt'
  //   Inport: '<Root>/In_IMB_LeftSt'
  //   Inport: '<Root>/In_IMB_RearSt'
  //   Inport: '<Root>/In_IMB_RightSt'

  // Gateway: MainSM_Lev1/DegradationHandling
  // During: MainSM_Lev1/DegradationHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c11_ViewModexViewStat) == 0U)
  {
    // Entry: MainSM_Lev1/DegradationHandling
    rtDWork.is_active_c11_ViewModexViewStat = 1U;

    // Entry Internal: MainSM_Lev1/DegradationHandling
    // Entry Internal 'FrontCamDeg': '<S4>:8'
    // Transition: '<S4>:7'
    rtDWork.is_FrontCamDeg = IN_av;

    // Outport: '<Root>/Out_SVSFrViewSts'
    // Entry 'av': '<S4>:6'
    // '<S4>:6:4' out_SVSFrViewSts = uint8(0);
    rtY.Out_SVSFrViewSts = 0U;

    // Entry Internal 'RightCamDeg': '<S4>:17'
    // Transition: '<S4>:21'
    rtDWork.is_RightCamDeg = IN_av;

    // Outport: '<Root>/Out_SVSRiViewSts'
    // Entry 'av': '<S4>:16'
    // '<S4>:16:4' out_SVSRiViewSts = uint8(0);
    rtY.Out_SVSRiViewSts = 0U;

    // Entry Internal 'LeftCamDeg': '<S4>:23'
    // Transition: '<S4>:27'
    rtDWork.is_LeftCamDeg = IN_av;

    // Outport: '<Root>/Out_SVSLeViewSts'
    // Entry 'av': '<S4>:22'
    // '<S4>:22:4' out_SVSLeViewSts = uint8(0);
    rtY.Out_SVSLeViewSts = 0U;

    // Entry Internal 'RearCamDeg': '<S4>:32'
    // Transition: '<S4>:33'
    rtDWork.is_RearCamDeg = IN_av;

    // Outport: '<Root>/Out_SVSReViewSts'
    // Entry 'av': '<S4>:28'
    // '<S4>:28:4' out_SVSReViewSts = uint8(0);
    rtY.Out_SVSReViewSts = 0U;

    // Entry Internal 'SVSDegSts': '<S4>:53'
    // Transition: '<S4>:55'
    rtDWork.is_SVSDegSts = IN_true;

    // Entry 'true': '<S4>:54'
    // '<S4>:54:3' out_SVSDegSts = true;
  }
  else
  {
    // During 'FrontCamDeg': '<S4>:8'
    if (static_cast<uint32_T>(rtDWork.is_FrontCamDeg) == IN_av)
    {
      // Outport: '<Root>/Out_SVSFrViewSts'
      rtY.Out_SVSFrViewSts = 0U;

      // During 'av': '<S4>:6'
      // '<S4>:14:3' sf_internal_predicateOutput = in_IMB_FrontSt;
      if (rtU.In_IMB_FrontSt)
      {
        // Transition: '<S4>:14'
        // [~in_FID_SVSCamFrontSt || ...
        // ~in_FID_SVSEcuInternalStatus || ...
        rtDWork.is_FrontCamDeg = IN_unav;

        // Outport: '<Root>/Out_SVSFrViewSts'
        // Entry 'unav': '<S4>:13'
        // '<S4>:13:4' out_SVSFrViewSts = uint8(1);
        rtY.Out_SVSFrViewSts = 1U;
      }
    }
    else
    {
      // Outport: '<Root>/Out_SVSFrViewSts'
      rtY.Out_SVSFrViewSts = 1U;

      // During 'unav': '<S4>:13'
      // '<S4>:15:3' sf_internal_predicateOutput = ~in_IMB_FrontSt;
      if (!rtU.In_IMB_FrontSt)
      {
        // Transition: '<S4>:15'
        // [in_FID_SVSCamFrontSt && ...
        // in_FID_SVSEcuInternalStatus && ...
        rtDWork.is_FrontCamDeg = IN_av;

        // Outport: '<Root>/Out_SVSFrViewSts'
        // Entry 'av': '<S4>:6'
        // '<S4>:6:4' out_SVSFrViewSts = uint8(0);
        rtY.Out_SVSFrViewSts = 0U;
      }
    }

    // During 'RightCamDeg': '<S4>:17'
    if (static_cast<uint32_T>(rtDWork.is_RightCamDeg) == IN_av)
    {
      // Outport: '<Root>/Out_SVSRiViewSts'
      rtY.Out_SVSRiViewSts = 0U;

      // During 'av': '<S4>:16'
      // '<S4>:18:3' sf_internal_predicateOutput = in_IMB_RightSt;
      if (rtU.In_IMB_RightSt)
      {
        // Transition: '<S4>:18'
        // [~in_FID_SVSCamRightSt || ...
        // ~in_FID_SVSEcuInternalStatus || ...
        rtDWork.is_RightCamDeg = IN_unav;

        // Outport: '<Root>/Out_SVSRiViewSts'
        // Entry 'unav': '<S4>:20'
        // '<S4>:20:4' out_SVSRiViewSts = uint8(1);
        rtY.Out_SVSRiViewSts = 1U;
      }
    }
    else
    {
      // Outport: '<Root>/Out_SVSRiViewSts'
      rtY.Out_SVSRiViewSts = 1U;

      // During 'unav': '<S4>:20'
      // '<S4>:19:3' sf_internal_predicateOutput = ~in_IMB_RightSt;
      if (!rtU.In_IMB_RightSt)
      {
        // Transition: '<S4>:19'
        // [in_FID_SVSCamRightSt && ...
        // in_FID_SVSEcuInternalStatus && ...
        rtDWork.is_RightCamDeg = IN_av;

        // Outport: '<Root>/Out_SVSRiViewSts'
        // Entry 'av': '<S4>:16'
        // '<S4>:16:4' out_SVSRiViewSts = uint8(0);
        rtY.Out_SVSRiViewSts = 0U;
      }
    }

    // During 'LeftCamDeg': '<S4>:23'
    if (static_cast<uint32_T>(rtDWork.is_LeftCamDeg) == IN_av)
    {
      // Outport: '<Root>/Out_SVSLeViewSts'
      rtY.Out_SVSLeViewSts = 0U;

      // During 'av': '<S4>:22'
      // '<S4>:24:3' sf_internal_predicateOutput = in_IMB_LeftSt;
      if (rtU.In_IMB_LeftSt)
      {
        // Transition: '<S4>:24'
        // [~in_FID_SVSCamLeftSt || ...
        // ~in_FID_SVSEcuInternalStatus ||...
        rtDWork.is_LeftCamDeg = IN_unav;

        // Outport: '<Root>/Out_SVSLeViewSts'
        // Entry 'unav': '<S4>:26'
        // '<S4>:26:4' out_SVSLeViewSts = uint8(1);
        rtY.Out_SVSLeViewSts = 1U;
      }
    }
    else
    {
      // Outport: '<Root>/Out_SVSLeViewSts'
      rtY.Out_SVSLeViewSts = 1U;

      // During 'unav': '<S4>:26'
      // '<S4>:25:3' sf_internal_predicateOutput = ~in_IMB_LeftSt;
      if (!rtU.In_IMB_LeftSt)
      {
        // Transition: '<S4>:25'
        // [in_FID_SVSCamLeftSt && ...
        // in_FID_SVSEcuInternalStatus && ...
        rtDWork.is_LeftCamDeg = IN_av;

        // Outport: '<Root>/Out_SVSLeViewSts'
        // Entry 'av': '<S4>:22'
        // '<S4>:22:4' out_SVSLeViewSts = uint8(0);
        rtY.Out_SVSLeViewSts = 0U;
      }
    }

    // During 'RearCamDeg': '<S4>:32'
    if (static_cast<uint32_T>(rtDWork.is_RearCamDeg) == IN_av)
    {
      // Outport: '<Root>/Out_SVSReViewSts'
      rtY.Out_SVSReViewSts = 0U;

      // During 'av': '<S4>:28'
      // '<S4>:29:3' sf_internal_predicateOutput = in_IMB_RearSt;
      if (rtU.In_IMB_RearSt)
      {
        // Transition: '<S4>:29'
        // [~in_FID_SVSCamRearSt || ...
        // ~in_FID_SVSEcuInternalStatus || ...
        rtDWork.is_RearCamDeg = IN_unav;

        // Outport: '<Root>/Out_SVSReViewSts'
        // Entry 'unav': '<S4>:30'
        // '<S4>:30:4' out_SVSReViewSts = uint8(1);
        rtY.Out_SVSReViewSts = 1U;
      }
    }
    else
    {
      // Outport: '<Root>/Out_SVSReViewSts'
      rtY.Out_SVSReViewSts = 1U;

      // During 'unav': '<S4>:30'
      // '<S4>:31:3' sf_internal_predicateOutput = ~in_IMB_RearSt;
      if (!rtU.In_IMB_RearSt)
      {
        // Transition: '<S4>:31'
        // [in_FID_SVSCamRearSt && ...
        // in_FID_SVSEcuInternalStatus && ...
        rtDWork.is_RearCamDeg = IN_av;

        // Outport: '<Root>/Out_SVSReViewSts'
        // Entry 'av': '<S4>:28'
        // '<S4>:28:4' out_SVSReViewSts = uint8(0);
        rtY.Out_SVSReViewSts = 0U;
      }
    }

    // During 'SVSDegSts': '<S4>:53'
    if (static_cast<uint32_T>(rtDWork.is_SVSDegSts) == IN_false)
    {
      // During 'false': '<S4>:56'
      // '<S4>:58:1' sf_internal_predicateOutput = in(FrontCamDeg.av) || in(RightCamDeg.av) || ... 
      // '<S4>:58:2' in(LeftCamDeg.av) || in(RearCamDeg.av);
      if ((static_cast<int32_T>(rtDWork.is_FrontCamDeg) == 1) || (static_cast<
           int32_T>(rtDWork.is_RightCamDeg) == 1) || (static_cast<int32_T>
           (rtDWork.is_LeftCamDeg) == 1) || (static_cast<int32_T>
           (rtDWork.is_RearCamDeg) == 1))
      {
        // Transition: '<S4>:58'
        rtDWork.is_SVSDegSts = IN_true;

        // Entry 'true': '<S4>:54'
        // '<S4>:54:3' out_SVSDegSts = true;
      }

      // During 'true': '<S4>:54'
      // '<S4>:57:1' sf_internal_predicateOutput = in(FrontCamDeg.unav) && in(RightCamDeg.unav) && ... 
      // '<S4>:57:2' in(LeftCamDeg.unav) && in(RearCamDeg.unav);
    }
    else if ((static_cast<int32_T>(rtDWork.is_FrontCamDeg) == 2) && (
              static_cast<int32_T>(rtDWork.is_RightCamDeg) == 2) && (
              static_cast<int32_T>(rtDWork.is_LeftCamDeg) == 2) && (static_cast<
              int32_T>(rtDWork.is_RearCamDeg) == 2))
    {
      // Transition: '<S4>:57'
      rtDWork.is_SVSDegSts = IN_false;

      // Entry 'false': '<S4>:56'
      // '<S4>:56:3' out_SVSDegSts = false;
    }
    else
    {
      // no actions
    }
  }

  // End of Chart: '<S1>/DegradationHandling'

  // Chart: '<S1>/PasWarnToneHandling' incorporates:
  //   Inport: '<Root>/In_pasWarnTone'

  // Gateway: MainSM_Lev1/PasWarnToneHandling
  // During: MainSM_Lev1/PasWarnToneHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c18_ViewModexViewStat) == 0U)
  {
    // Entry: MainSM_Lev1/PasWarnToneHandling
    rtDWork.is_active_c18_ViewModexViewStat = 1U;

    // Entry Internal: MainSM_Lev1/PasWarnToneHandling
    // Entry Internal 'RadarActivHandling': '<S10>:53'
    // Transition: '<S10>:55'
    rtDWork.is_RadarActivHandling = IN_NotActivate;

    // Entry 'NotActivate': '<S10>:54'
    // '<S10>:54:3' out_WarnToneActivate = false;

    // During 'RadarActivHandling': '<S10>:53'
  }
  else if (static_cast<uint32_T>(rtDWork.is_RadarActivHandling) ==
           IN_NotActivate)
  {
    // During 'NotActivate': '<S10>:54'
    // '<S10>:57:1' sf_internal_predicateOutput = (in_PasWarnTone<5)&&...
    // '<S10>:57:2' (in_PasWarnTone > 0);
    if ((static_cast<int32_T>(rtU.In_pasWarnTone) < 5) && (static_cast<int32_T>
         (rtU.In_pasWarnTone) > 0))
    {
      // Transition: '<S10>:57'
      rtDWork.is_RadarActivHandling = IN_ToActivate;

      // Entry 'ToActivate': '<S10>:56'
      // '<S10>:56:3' out_WarnToneActivate = true;
    }

    // During 'ToActivate': '<S10>:56'
    // '<S10>:58:1' sf_internal_predicateOutput = (in_PasWarnTone==5) || ...
    // '<S10>:58:2' (in_PasWarnTone==0);
  }
  else if ((static_cast<int32_T>(rtU.In_pasWarnTone) == 5) ||
           (static_cast<int32_T>(rtU.In_pasWarnTone) == 0))
  {
    // Transition: '<S10>:58'
    rtDWork.is_RadarActivHandling = IN_NotActivate;

    // Entry 'NotActivate': '<S10>:54'
    // '<S10>:54:3' out_WarnToneActivate = false;
  }
  else
  {
    // no actions
  }

  // End of Chart: '<S1>/PasWarnToneHandling'

  // Chart: '<S1>/CpcViewHandling' incorporates:
  //   Inport: '<Root>/In_HUDislayModeSwitch'
  //   Inport: '<Root>/In_cpcDelay'
  //   Inport: '<Root>/In_isCpcActive'

  if (rtDWork.temporalCounter_i1 < MAX_uint32_T)
  {
    rtDWork.temporalCounter_i1++;
  }

  // Gateway: MainSM_Lev1/CpcViewHandling
  // During: MainSM_Lev1/CpcViewHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c16_ViewModexViewStat) == 0U)
  {
    // Entry: MainSM_Lev1/CpcViewHandling
    rtDWork.is_active_c16_ViewModexViewStat = 1U;

    // Entry Internal: MainSM_Lev1/CpcViewHandling
    // Transition: '<S3>:6'
    rtDWork.is_c16_ViewModexViewStateMachin = IN_false;

    // Entry 'false': '<S3>:5'
    // '<S3>:5:3' out_isCpcShow = false;
    out_isCpcShow = false;
  }
  else if (static_cast<uint32_T>(rtDWork.is_c16_ViewModexViewStateMachin) ==
           IN_false)
  {
    out_isCpcShow = false;

    // During 'false': '<S3>:5'
    // '<S3>:8:1' sf_internal_predicateOutput = in_HUDislayModeSwitch == EHuDisplayModeSwitch.DISPLAY_MODE_STARTCAL; 
    if (rtU.In_HUDislayModeSwitch == DISPLAY_MODE_STARTCAL)
    {
      // Transition: '<S3>:8'
      rtDWork.is_c16_ViewModexViewStateMachin = IN_true;

      // Entry 'true': '<S3>:7'
      // '<S3>:7:3' out_isCpcShow = true;
      out_isCpcShow = true;
      rtDWork.is_true = IN_entry;
    }
  }
  else
  {
    out_isCpcShow = true;

    // During 'true': '<S3>:7'
    if (static_cast<uint32_T>(rtDWork.is_true) == IN_delay)
    {
      // During 'delay': '<S3>:11'
      // '<S3>:13:1' sf_internal_predicateOutput = in_HUDislayModeSwitch == EHuDisplayModeSwitch.DISPLAY_MODE_STARTCAL || ... 
      // '<S3>:13:2' in_isCpcActive;
      if ((rtU.In_HUDislayModeSwitch == DISPLAY_MODE_STARTCAL) ||
          rtU.In_isCpcActive)
      {
        // Transition: '<S3>:13'
        rtDWork.is_true = IN_entry;

        // '<S3>:9:1' sf_internal_predicateOutput = after(in_cpcDelay, sec);
      }
      else if (rtDWork.temporalCounter_i1 >= static_cast<uint32_T>
               (static_cast<int32_T>(static_cast<int32_T>(rtU.In_cpcDelay) * 100)))
      {
        // Transition: '<S3>:9'
        rtDWork.is_true = IN_NO_ACTIVE_CHILD;
        rtDWork.is_c16_ViewModexViewStateMachin = IN_false;

        // Entry 'false': '<S3>:5'
        // '<S3>:5:3' out_isCpcShow = false;
        out_isCpcShow = false;
      }
      else
      {
        // no actions
      }

      // During 'entry': '<S3>:10'
      // '<S3>:12:1' sf_internal_predicateOutput = in_HUDislayModeSwitch ~= EHuDisplayModeSwitch.DISPLAY_MODE_STARTCAL && ... 
      // '<S3>:12:2' ~in_isCpcActive;
    }
    else if ((rtU.In_HUDislayModeSwitch != DISPLAY_MODE_STARTCAL) &&
             (!rtU.In_isCpcActive))
    {
      // Transition: '<S3>:12'
      rtDWork.is_true = IN_delay;
      rtDWork.temporalCounter_i1 = 0U;
    }
    else
    {
      // no actions
    }
  }

  // End of Chart: '<S1>/CpcViewHandling'

  // Chart: '<S1>/ObstacleAndSteeringrHandling' incorporates:
  //   BusCreator generated from: '<S1>/ObstacleAndSteeringrHandling'
  //   Inport: '<Root>/In_huPasAct'
  //   Inport: '<Root>/In_huSteeringAct'
  //   Inport: '<Root>/In_huSteeringAngleFront'
  //   Inport: '<Root>/In_huSteeringAngleTrigIn'
  //   Inport: '<Root>/In_huSteeringAngleTrigOut'
  //   Inport: '<Root>/In_settingNarrowLaneActivate'
  //   Inport: '<Root>/In_sonarDistLevel'

  // Gateway: MainSM_Lev1/ObstacleAndSteeringrHandling
  // During: MainSM_Lev1/ObstacleAndSteeringrHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c5_ViewModexViewState) == 0U)
  {
    // Entry: MainSM_Lev1/ObstacleAndSteeringrHandling
    rtDWork.is_active_c5_ViewModexViewState = 1U;

    // Entry Internal: MainSM_Lev1/ObstacleAndSteeringrHandling
    // Entry 'ObstacleActivateHandling': '<S9>:11'
    //  0&#xFF1A; SONARDISTRANGE_NONE
    //  1&#xFF1A; SONARDISTRANGE_LEVEL4
    //  2&#xFF1A; SONARDISTRANGE_LEVEL3
    //  3:  SONARDISTRANGE_LEVEL2
    //  4:  SONARDISTRANGE_LEVEL1
    //  5:  SONARDISTRANGE_NOT_USED
    // Entry Internal 'ObstacleActivateHandling': '<S9>:11'
    // Transition: '<S9>:321'
    rtDWork.is_ObstacleActivateHandling = IN_ObstacleActivate_SettingOff;

    // Entry 'ObstacleActivate_SettingOff': '<S9>:291'
    // '<S9>:291:3' out_obstacleAct = false;
    rtDWork.out_obstacleAct = false;

    // '<S9>:291:4' out_obstacleActView = EScreenID.NO_CHANGE;
    // Entry Internal 'SteeringActivateHandling': '<S9>:285'
    // Transition: '<S9>:290'
    rtDWork.is_SteeringActivateHandling = IN_SteeringNotActivated;

    // Entry 'SteeringNotActivated': '<S9>:296'
    // '<S9>:296:3' out_steeringAct = false;
    rtDWork.out_steeringAct = false;

    // Entry 'NarrowLaneActivateHandling1': '<S9>:349'
    //  0&#xFF1A; SONARDISTRANGE_NONE
    //  1&#xFF1A; SONARDISTRANGE_LEVEL4
    //  2&#xFF1A; SONARDISTRANGE_LEVEL3
    //  3:  SONARDISTRANGE_LEVEL2
    //  4:  SONARDISTRANGE_LEVEL1
    //  5:  SONARDISTRANGE_NOT_USED
    // Entry Internal 'NarrowLaneActivateHandling1': '<S9>:349'
    // Transition: '<S9>:345'
    rtDWork.is_NarrowLaneActivateHandling1 = IN_NarrowLaneActivate_SettingOf;

    // Entry 'NarrowLaneActivate_SettingOff': '<S9>:351'
    // '<S9>:351:3' out_narrowLaneAct = false;
    rtDWork.out_narrowLaneAct = false;

    // '<S9>:351:4' out_narrowLaneActView = EScreenID.NO_CHANGE;
  }
  else
  {
    // During 'ObstacleActivateHandling': '<S9>:11'
    if (static_cast<uint32_T>(rtDWork.is_ObstacleActivateHandling) ==
        IN_ObstacleActivate_SettingOff)
    {
      rtDWork.out_obstacleAct = false;

      // During 'ObstacleActivate_SettingOff': '<S9>:291'
      // '<S9>:284:1' sf_internal_predicateOutput = ESettingSts.Set_ON == in_settingObstacleActivate; 
      if (ESettingSts_Set_ON == rtU.In_huPasAct)
      {
        // Transition: '<S9>:284'
        rtDWork.is_ObstacleActivateHandling = IN_SonarActivate_Handling;

        // Entry Internal 'SonarActivate_Handling': '<S9>:313'
        // Transition: '<S9>:317'
        rtDWork.is_SonarActivate_Handling = IN_SonarNotActivated;

        // Entry 'SonarNotActivated': '<S9>:233'
        // '<S9>:233:3' out_obstacleAct = false;
        rtDWork.out_obstacleAct = false;

        // '<S9>:233:4' out_obstacleActView = EScreenID.NO_CHANGE;
      }

      // During 'SonarActivate_Handling': '<S9>:313'
      // '<S9>:316:1' sf_internal_predicateOutput = ESettingSts.Set_OFF == in_settingObstacleActivate; 
    }
    else if (ESettingSts_Set_OFF == rtU.In_huPasAct)
    {
      // Transition: '<S9>:316'
      // Exit Internal 'SonarActivate_Handling': '<S9>:313'
      rtDWork.is_SonarActivate_Handling = IN_NO_ACTIVE_CHILD;
      rtDWork.is_ObstacleActivateHandling = IN_ObstacleActivate_SettingOff;

      // Entry 'ObstacleActivate_SettingOff': '<S9>:291'
      // '<S9>:291:3' out_obstacleAct = false;
      rtDWork.out_obstacleAct = false;

      // '<S9>:291:4' out_obstacleActView = EScreenID.NO_CHANGE;
    }
    else if (static_cast<uint32_T>(rtDWork.is_SonarActivate_Handling) ==
             IN_SonarActivated)
    {
      rtDWork.out_obstacleAct = true;

      // During 'SonarActivated': '<S9>:235'
      // '<S9>:231:1' sf_internal_predicateOutput =  in_sonarDistLevel.DistFrontLeft < 3 ... 
      // '<S9>:231:2' && in_sonarDistLevel.DistFrontCenter < 3 ...
      // '<S9>:231:3' && in_sonarDistLevel.DistFrontRight < 3;
      if ((static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontLeft) < 3) && (
           static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontCenter) < 3) && (
           static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontRight) < 3))
      {
        // Transition: '<S9>:231'
        rtDWork.is_SonarActivate_Handling = IN_SonarNotActivated;

        // Entry 'SonarNotActivated': '<S9>:233'
        // '<S9>:233:3' out_obstacleAct = false;
        rtDWork.out_obstacleAct = false;

        // '<S9>:233:4' out_obstacleActView = EScreenID.NO_CHANGE;
      }
    }
    else
    {
      rtDWork.out_obstacleAct = false;

      // During 'SonarNotActivated': '<S9>:233'
      // '<S9>:232:1' sf_internal_predicateOutput =  in_sonarDistLevel.DistFrontLeft >= 3 ... 
      // '<S9>:232:2' || in_sonarDistLevel.DistFrontCenter >= 3 ...
      // '<S9>:232:3' || in_sonarDistLevel.DistFrontRight >= 3;
      if ((static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontLeft) >= 3) || (
           static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontCenter) >= 3) ||
          (static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontRight) >= 3))
      {
        // Transition: '<S9>:232'
        rtDWork.is_SonarActivate_Handling = IN_SonarActivated;

        // Entry 'SonarActivated': '<S9>:235'
        // '<S9>:235:3' out_obstacleAct = true;
        rtDWork.out_obstacleAct = true;

        // '<S9>:235:4' out_obstacleActView = EScreenID.FLOAT_FRONT_PLAN_VIEW;
      }
    }

    // During 'SteeringActivateHandling': '<S9>:285'
    if (static_cast<uint32_T>(rtDWork.is_SteeringActivateHandling) ==
        IN_SteeringActivated)
    {
      rtDWork.out_steeringAct = true;

      // During 'SteeringActivated': '<S9>:303'
      // '<S9>:305:1' sf_internal_predicateOutput = abs(in_steeringAngleFront) <= in_steeringAngleTrigOut||... 
      // '<S9>:305:2' ESettingSts.Set_OFF==in_settingSteeringActivate;
      if ((std::abs(rtU.In_huSteeringAngleFront) <= static_cast<real_T>
           (rtU.In_huSteeringAngleTrigOut)) || (ESettingSts_Set_OFF ==
           rtU.In_huSteeringAct))
      {
        // Transition: '<S9>:305'
        rtDWork.is_SteeringActivateHandling = IN_SteeringNotActivated;

        // Entry 'SteeringNotActivated': '<S9>:296'
        // '<S9>:296:3' out_steeringAct = false;
        rtDWork.out_steeringAct = false;
      }
    }
    else
    {
      rtDWork.out_steeringAct = false;

      // During 'SteeringNotActivated': '<S9>:296'
      // '<S9>:304:1' sf_internal_predicateOutput = abs(in_steeringAngleFront) > in_steeringAngleTrigIn ... 
      // '<S9>:304:2' && ESettingSts.Set_ON==in_settingSteeringActivate;
      if ((std::abs(rtU.In_huSteeringAngleFront) > static_cast<real_T>
           (rtU.In_huSteeringAngleTrigIn)) && (ESettingSts_Set_ON ==
           rtU.In_huSteeringAct))
      {
        // Transition: '<S9>:304'
        rtDWork.is_SteeringActivateHandling = IN_SteeringActivated;

        // Entry 'SteeringActivated': '<S9>:303'
        // '<S9>:303:3' out_steeringAct = true;
        rtDWork.out_steeringAct = true;
      }
    }

    // During 'NarrowLaneActivateHandling1': '<S9>:349'
    if (static_cast<uint32_T>(rtDWork.is_NarrowLaneActivateHandling1) ==
        IN_NarrowLaneActivate_Handling)
    {
      // During 'NarrowLaneActivate_Handling': '<S9>:350'
      // '<S9>:353:1' sf_internal_predicateOutput = ESettingSts.Set_OFF == in_settingNarrowLaneActivate; 
      if (static_cast<int32_T>(ESettingSts_Set_OFF) ==
          (rtU.In_settingNarrowLaneActivate ? static_cast<int32_T>(1) :
           static_cast<int32_T>(0)))
      {
        // Transition: '<S9>:353'
        // Exit Internal 'NarrowLaneActivate_Handling': '<S9>:350'
        rtDWork.is_NarrowLaneActivate_Handling = IN_NO_ACTIVE_CHILD;
        rtDWork.is_NarrowLaneActivateHandling1 = IN_NarrowLaneActivate_SettingOf;

        // Entry 'NarrowLaneActivate_SettingOff': '<S9>:351'
        // '<S9>:351:3' out_narrowLaneAct = false;
        rtDWork.out_narrowLaneAct = false;

        // '<S9>:351:4' out_narrowLaneActView = EScreenID.NO_CHANGE;
      }
      else if (static_cast<uint32_T>(rtDWork.is_NarrowLaneActivate_Handling) ==
               IN_NarrowLaneActivated)
      {
        rtDWork.out_narrowLaneAct = true;

        // During 'NarrowLaneActivated': '<S9>:344'
        // '<S9>:347:1' sf_internal_predicateOutput =  in_sonarDistLevel. DistFrontLeftSide < 4 ... 
        // '<S9>:347:2' && in_sonarDistLevel.DistFrontRightSide < 4;
        if ((static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontLeftSide) < 4) &&
            (static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontRightSide) < 4))
        {
          // Transition: '<S9>:347'
          rtDWork.is_NarrowLaneActivate_Handling = IN_NarrowLaneNotActivated;

          // Entry 'NarrowLaneNotActivated': '<S9>:346'
          // '<S9>:346:3' out_narrowLaneAct = false;
          rtDWork.out_narrowLaneAct = false;

          // '<S9>:346:4' out_narrowLaneActView = EScreenID.NO_CHANGE;
        }
      }
      else
      {
        rtDWork.out_narrowLaneAct = false;

        // During 'NarrowLaneNotActivated': '<S9>:346'
        // '<S9>:352:1' sf_internal_predicateOutput =  in_sonarDistLevel. DistFrontLeftSide >= 4 ... 
        // '<S9>:352:2' || in_sonarDistLevel.DistFrontRightSide >= 4;
        if ((static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontLeftSide) >= 4)
            || (static_cast<int32_T>(rtU.In_sonarDistLevel.DistFrontRightSide) >=
                4))
        {
          // Transition: '<S9>:352'
          rtDWork.is_NarrowLaneActivate_Handling = IN_NarrowLaneActivated;

          // Entry 'NarrowLaneActivated': '<S9>:344'
          // '<S9>:344:3' out_narrowLaneAct = true;
          rtDWork.out_narrowLaneAct = true;

          // '<S9>:344:4' out_narrowLaneActView = EScreenID.FLOAT_FRONT_PLAN_VIEW; 
        }
      }
    }
    else
    {
      rtDWork.out_narrowLaneAct = false;

      // During 'NarrowLaneActivate_SettingOff': '<S9>:351'
      // '<S9>:354:1' sf_internal_predicateOutput = ESettingSts.Set_ON == in_settingNarrowLaneActivate; 
      if (static_cast<int32_T>(ESettingSts_Set_ON) ==
          (rtU.In_settingNarrowLaneActivate ? static_cast<int32_T>(1) :
           static_cast<int32_T>(0)))
      {
        // Transition: '<S9>:354'
        rtDWork.is_NarrowLaneActivateHandling1 = IN_NarrowLaneActivate_Handling;

        // Entry Internal 'NarrowLaneActivate_Handling': '<S9>:350'
        // Transition: '<S9>:348'
        rtDWork.is_NarrowLaneActivate_Handling = IN_NarrowLaneNotActivated;

        // Entry 'NarrowLaneNotActivated': '<S9>:346'
        // '<S9>:346:3' out_narrowLaneAct = false;
        rtDWork.out_narrowLaneAct = false;

        // '<S9>:346:4' out_narrowLaneActView = EScreenID.NO_CHANGE;
      }
    }
  }

  // End of Chart: '<S1>/ObstacleAndSteeringrHandling'

  // UnitDelay: '<S1>/Unit Delay7'
  rtDWork.UnitDelay7 = rtDWork.UnitDelay7_DSTATE;

  // UnitDelay: '<S1>/Unit Delay8'
  rtDWork.UnitDelay8 = rtDWork.UnitDelay8_DSTATE;

  // UnitDelay: '<S1>/Unit Delay9'
  UnitDelay9 = rtDWork.UnitDelay9_DSTATE;

  // BusCreator generated from: '<S1>/MainSM_Lev2' incorporates:
  //   Inport: '<Root>/In_exitDelay'

  rtDWork.BusConversion_InsertedFor_Mai_d = rtU.In_exitDelay;

  // Chart: '<S1>/MainSM_Lev2' incorporates:
  //   BusCreator generated from: '<S1>/MainSM_Lev2'
  //   Inport: '<Root>/In_AVMError'
  //   Inport: '<Root>/In_EnlargeButtonPressed'
  //   Inport: '<Root>/In_FloatViewChange'
  //   Inport: '<Root>/In_HUSVSMode'
  //   Inport: '<Root>/In_HUViewReq'
  //   Inport: '<Root>/In_PowerSaveMode'
  //   Inport: '<Root>/In_SRIsActive'
  //   Inport: '<Root>/In_SystemStr'
  //   Inport: '<Root>/In_androidIconActive'
  //   Inport: '<Root>/In_calibActive'
  //   Inport: '<Root>/In_calibStatus'
  //   Inport: '<Root>/In_closeButtonPressed'
  //   Inport: '<Root>/In_competeActiveAllow'
  //   Inport: '<Root>/In_competeQuit'
  //   Inport: '<Root>/In_dockAvmButtonPress'
  //   Inport: '<Root>/In_gear'
  //   Inport: '<Root>/In_parkstatus'
  //   Inport: '<Root>/In_sonarDistLevel'
  //   Inport: '<Root>/In_steeringWheelButtonPressed'
  //   Inport: '<Root>/In_timeStepScaleFactor'
  //   Inport: '<Root>/In_voiceDockRequest'
  //   Inport: '<Root>/in_isFreeParking'
  //   UnitDelay: '<S1>/Unit Delay6'

  if (rtDWork.temporalCounter_i2 < MAX_uint32_T)
  {
    rtDWork.temporalCounter_i2++;
  }

  if (rtDWork.temporalCounter_i3 < MAX_uint32_T)
  {
    rtDWork.temporalCounter_i3++;
  }

  if (rtDWork.temporalCounter_i4 < MAX_uint32_T)
  {
    rtDWork.temporalCounter_i4++;
  }

  if (rtDWork.temporalCounter_i5 < MAX_uint32_T)
  {
    rtDWork.temporalCounter_i5++;
  }

  if (rtDWork.temporalCounter_i6 < MAX_uint32_T)
  {
    rtDWork.temporalCounter_i6++;
  }

  // Gateway: MainSM_Lev1/MainSM_Lev2
  if (static_cast<uint32_T>(rtDWork.temporalCounter_i1_b) < 3U)
  {
    rtDWork.temporalCounter_i1_b = static_cast<uint8_T>(static_cast<uint32_T>
      (rtDWork.temporalCounter_i1_b) + 1U);
  }

  UnitDelay1 = rtDWork.in_HUSVSMode_start;
  rtDWork.in_HUSVSMode_start = rtU.In_HUSVSMode;
  rtDWork.in_parkingsts_prev = rtDWork.in_parkingsts_start;
  rtDWork.in_parkingsts_start = rtU.In_parkstatus;
  rtDWork.in_gear_prev = rtDWork.in_gear_start;
  rtDWork.in_gear_start = rtU.In_gear;
  rtDWork.in_voiceDockRequest_prev = rtDWork.in_voiceDockRequest_start;
  rtDWork.in_voiceDockRequest_start = rtU.In_voiceDockRequest;
  in_displayedVewL_prev = rtDWork.in_HUViewReq_start;
  rtDWork.in_HUViewReq_start = rtU.In_HUViewReq;
  rtDWork.in_androidIconActive_prev = rtDWork.in_androidIconActive_start;
  rtDWork.in_androidIconActive_start = rtU.In_androidIconActive;
  rtDWork.in_steeringWheelButtonPressed_p =
    rtDWork.in_steeringWheelButtonPressed_s;
  rtDWork.in_steeringWheelButtonPressed_s = rtU.In_steeringWheelButtonPressed;
  rtDWork.in_dockAvmButtonPress_prev = rtDWork.in_dockAvmButtonPress_start;
  rtDWork.in_dockAvmButtonPress_start = rtU.In_dockAvmButtonPress;
  rtDWork.in_FloatViewChange_prev = rtDWork.in_FloatViewChange_start;
  rtDWork.in_FloatViewChange_start = rtU.In_FloatViewChange;
  rtDWork.in_obstacleAct_prev = rtDWork.in_obstacleAct_start;
  rtDWork.in_obstacleAct_start = rtDWork.out_obstacleAct;
  rtDWork.in_narrowLaneAct_prev = rtDWork.in_narrowLaneAct_start;
  rtDWork.in_narrowLaneAct_start = rtDWork.out_narrowLaneAct;
  rtDWork.in_steeringAct_prev = rtDWork.in_steeringAct_start;
  rtDWork.in_steeringAct_start = rtDWork.out_steeringAct;
  rtDWork.in_SRIsActive_prev = rtDWork.in_SRIsActive_start;
  rtDWork.in_SRIsActive_start = rtU.In_SRIsActive;
  rtDWork.in_EnlargeButtonPressed_prev = rtDWork.in_EnlargeButtonPressed_start;
  rtDWork.in_EnlargeButtonPressed_start = rtU.In_EnlargeButtonPressed;
  rtDWork.in_PowerSaveMode_prev = rtDWork.in_PowerSaveMode_start;
  rtDWork.in_PowerSaveMode_start = rtU.In_PowerSaveMode;
  rtDWork.in_AVMError_prev = rtDWork.in_AVMError_start;
  rtDWork.in_AVMError_start = rtU.In_AVMError;
  in_SystemStr_prev = rtDWork.in_SystemStr_start;
  rtDWork.in_SystemStr_start = rtU.In_SystemStr;
  in_calibStatus_prev = rtDWork.in_calibStatus_start;
  rtDWork.in_calibStatus_start = rtU.In_calibStatus;
  in_calibActive_prev = rtDWork.in_calibActive_start;
  rtDWork.in_calibActive_start = rtU.In_calibActive;
  in_closeButtonPressed_prev = rtDWork.in_closeButtonPressed_start;
  rtDWork.in_closeButtonPressed_start = rtU.In_closeButtonPressed;
  in_competeQuit_prev = rtDWork.in_competeQuit_start;
  rtDWork.in_competeQuit_start = rtU.In_competeQuit;
  rtDWork.in_isFreeParking_prev = rtDWork.in_isFreeParking_start;
  rtDWork.in_isFreeParking_start = rtU.in_isFreeParking;

  // During: MainSM_Lev1/MainSM_Lev2
  if (static_cast<uint32_T>(rtDWork.is_active_c1_ViewModexViewState) == 0U)
  {
    rtDWork.in_parkingsts_prev = rtU.In_parkstatus;
    rtDWork.in_gear_prev = rtU.In_gear;
    rtDWork.in_voiceDockRequest_prev = rtU.In_voiceDockRequest;
    rtDWork.in_androidIconActive_prev = rtU.In_androidIconActive;
    rtDWork.in_steeringWheelButtonPressed_p = rtU.In_steeringWheelButtonPressed;
    rtDWork.in_dockAvmButtonPress_prev = rtU.In_dockAvmButtonPress;
    rtDWork.in_FloatViewChange_prev = rtU.In_FloatViewChange;
    rtDWork.in_obstacleAct_prev = rtDWork.out_obstacleAct;
    rtDWork.in_narrowLaneAct_prev = rtDWork.out_narrowLaneAct;
    rtDWork.in_steeringAct_prev = rtDWork.out_steeringAct;
    rtDWork.in_SRIsActive_prev = rtU.In_SRIsActive;
    rtDWork.in_EnlargeButtonPressed_prev = rtU.In_EnlargeButtonPressed;
    rtDWork.in_PowerSaveMode_prev = rtU.In_PowerSaveMode;
    rtDWork.in_AVMError_prev = rtU.In_AVMError;
    rtDWork.in_isFreeParking_prev = rtU.in_isFreeParking;

    // Entry: MainSM_Lev1/MainSM_Lev2
    rtDWork.is_active_c1_ViewModexViewState = 1U;
    enter_internal_c1_ViewModexView();
  }
  else
  {
    durationNoChange(&UnitDelay1, &in_displayedVewL_prev);

    // During 'durationNoSonarWarning': '<S7>:2058'
    // '<S7>:2058:3' l_hasSonarWarning = ((in_sonarDistLevel.DistFrontLeftSide ~= 0) || (in_sonarDistLevel.DistFrontLeft ~= 0) || (in_sonarDistLevel.DistFrontCenter ~= 0) || ... 
    // '<S7>:2058:4'     (in_sonarDistLevel.DistFrontRight ~= 0) || (in_sonarDistLevel.DistFrontRightSide ~= 0)); 
    l_isViewChangeAvl_Hori = ((static_cast<int32_T>
      (rtU.In_sonarDistLevel.DistFrontLeftSide) != 0) || (static_cast<int32_T>
      (rtU.In_sonarDistLevel.DistFrontLeft) != 0) || (static_cast<int32_T>
      (rtU.In_sonarDistLevel.DistFrontCenter) != 0) || (static_cast<int32_T>
      (rtU.In_sonarDistLevel.DistFrontRight) != 0) || (static_cast<int32_T>
      (rtU.In_sonarDistLevel.DistFrontRightSide) != 0));
    if (static_cast<uint32_T>(rtDWork.is_durationNoSonarWarning) ==
        IN_HasSonarWarning)
    {
      real_T tmp_0;

      // During 'HasSonarWarning': '<S7>:2062'
      // '<S7>:2057:1' sf_internal_predicateOutput = ~l_hasSonarWarning && after(in_exitDelay.WarningExitDelay * in_timeStepScaleFactor, sec); 
      tmp_0 = rt_roundd(static_cast<real_T>
                        (rtDWork.BusConversion_InsertedFor_Mai_d.WarningExitDelay)
                        * rtU.In_timeStepScaleFactor);
      if (tmp_0 < 4.294967296E+9)
      {
        if (tmp_0 >= 0.0)
        {
          in_campositionX_prev = static_cast<uint32_T>(tmp_0);
        }
        else
        {
          in_campositionX_prev = 0U;
        }
      }
      else
      {
        in_campositionX_prev = MAX_uint32_T;
      }

      if ((!l_isViewChangeAvl_Hori) && (rtDWork.temporalCounter_i4 >=
           static_cast<uint32_T>(static_cast<int32_T>(static_cast<int32_T>
             (in_campositionX_prev) * 100))))
      {
        // Transition: '<S7>:2057'
        rtDWork.is_durationNoSonarWarning = IN_NoSonarWarning_5s;

        // '<S7>:2063:1' sf_internal_predicateOutput = l_hasSonarWarning;
      }
      else if (l_isViewChangeAvl_Hori)
      {
        // Transition: '<S7>:2063'
        rtDWork.is_durationNoSonarWarning = IN_HasSonarWarning;
        rtDWork.temporalCounter_i4 = 0U;

        // Entry 'HasSonarWarning': '<S7>:2062'
      }
      else
      {
        // no actions
      }

      // During 'NoSonarWarning_5s': '<S7>:2059'
      // '<S7>:2060:1' sf_internal_predicateOutput = l_hasSonarWarning;
    }
    else if (l_isViewChangeAvl_Hori)
    {
      // Transition: '<S7>:2060'
      rtDWork.is_durationNoSonarWarning = IN_HasSonarWarning;
      rtDWork.temporalCounter_i4 = 0U;

      // Entry 'HasSonarWarning': '<S7>:2062'
    }
    else
    {
      // no actions
    }

    // During 'Screen_Compete_Request': '<S7>:1646'
    switch (rtDWork.is_Screen_Compete_Request)
    {
     case IN_Compete_Req_Finished:
      // During 'Compete_Req_Finished': '<S7>:1674'
      // Transition: '<S7>:1676'
      rtDWork.is_Screen_Compete_Request = IN_No_Compete_Req;

      // Entry 'No_Compete_Req': '<S7>:1831'
      break;

     case IN_Has_Compete_Feedback:
      // During 'Has_Compete_Feedback': '<S7>:1657'
      // '<S7>:1669:1' sf_internal_predicateOutput = in_competeReqStatus ~= COMPETE_REQ_STARTED; 
      if (static_cast<uint32_T>(rtDWork.UnitDelay6_DSTATE) !=
          ECompeteReqStatus_COMPETE_REQ_STARTED)
      {
        // Transition: '<S7>:1669'
        // Exit Internal 'Has_Compete_Feedback': '<S7>:1657'
        rtDWork.is_Has_Compete_Feedback = IN_NO_ACTIVE_CHILD;
        rtDWork.is_Screen_Compete_Request = IN_Compete_Req_Finished;

        // Entry 'Compete_Req_Finished': '<S7>:1674'
      }
      break;

     case IN_No_Compete_Req:
      // During 'No_Compete_Req': '<S7>:1831'
      // '<S7>:1832:1' sf_internal_predicateOutput = in_competeReqStatus == COMPETE_REQ_STARTED; 
      if (static_cast<uint32_T>(rtDWork.UnitDelay6_DSTATE) ==
          ECompeteReqStatus_COMPETE_REQ_STARTED)
      {
        // Transition: '<S7>:1832'
        rtDWork.is_Screen_Compete_Request = IN_Start_Compete_Req;
        rtDWork.temporalCounter_i3 = 0U;

        // Entry 'Start_Compete_Req': '<S7>:1649'
      }
      break;

     case IN_Request_Compete_TimeOut:
      // During 'Request_Compete_TimeOut': '<S7>:1655'
      // '<S7>:1668:1' sf_internal_predicateOutput = in_competeReqStatus ~= COMPETE_REQ_STARTED; 
      if (static_cast<uint32_T>(rtDWork.UnitDelay6_DSTATE) !=
          ECompeteReqStatus_COMPETE_REQ_STARTED)
      {
        // Transition: '<S7>:1668'
        rtDWork.is_Screen_Compete_Request = IN_Compete_Req_Finished;

        // Entry 'Compete_Req_Finished': '<S7>:1674'
      }
      break;

     default:
      {
        ECompeteActiveAllow tmp_1;

        // During 'Start_Compete_Req': '<S7>:1649'
        // '<S7>:1658:1' sf_internal_predicateOutput = in_competeActiveAllow == ECompeteActiveAllow. ACTIVE_ALLOW || ... 
        // '<S7>:1658:2' in_competeActiveAllow == ECompeteActiveAllow. ACTIVE_NOT_ALLOW; 
        tmp_1 = rtU.In_competeActiveAllow;
        if ((static_cast<uint32_T>(tmp_1) == ECompeteActiveAllow_ACTIVE_ALLOW) ||
            (static_cast<uint32_T>(tmp_1) ==
             ECompeteActiveAllow_ACTIVE_NOT_ALLOW))
        {
          // Transition: '<S7>:1658'
          rtDWork.is_Screen_Compete_Request = IN_Has_Compete_Feedback;

          // Entry 'Has_Compete_Feedback': '<S7>:1657'
          // Entry Internal 'Has_Compete_Feedback': '<S7>:1657'
          // Transition: '<S7>:1661'
          // '<S7>:1663:1' sf_internal_predicateOutput = in_competeActiveAllow == ECompeteActiveAllow.ACTIVE_ALLOW; 
          if (static_cast<uint32_T>(tmp_1) == ECompeteActiveAllow_ACTIVE_ALLOW)
          {
            // Transition: '<S7>:1663'
            rtDWork.is_Has_Compete_Feedback = IN_Compete_Passed;

            // Entry 'Compete_Passed': '<S7>:1659'
          }
          else
          {
            // Transition: '<S7>:1665'
            // Transition: '<S7>:1666'
            rtDWork.is_Has_Compete_Feedback = IN_Compete_Refused;

            // Entry 'Compete_Refused': '<S7>:1651'
          }

          // '<S7>:1656:1' sf_internal_predicateOutput = after(5 * in_timeStepScaleFactor, sec); 
        }
        else if (rtDWork.temporalCounter_i3 >= static_cast<uint32_T>(std::ceil
                  (5.0 * rtU.In_timeStepScaleFactor * 100.0)))
        {
          // Transition: '<S7>:1656'
          rtDWork.is_Screen_Compete_Request = IN_Request_Compete_TimeOut;

          // Entry 'Request_Compete_TimeOut': '<S7>:1655'
        }
        else
        {
          // no actions
        }
      }
      break;
    }

    // During 'freeMode': '<S7>:699'
    // During 'freeModeLogic': '<S7>:709'
    if (static_cast<uint32_T>(rtDWork.is_freeModeLogic) == IN_false)
    {
      // Outport: '<Root>/Out_isFreeMode'
      rtY.Out_isFreeMode = false;

      // During 'false': '<S7>:700'
      // '<S7>:703:2' sf_internal_predicateOutput = ~in_isViewChangeAvl;
      if (!out_isViewChangeAvl)
      {
        // Transition: '<S7>:703'
        // [in(SuperState.SuperStateL2.Available.FreeViewID)]
        rtDWork.is_freeModeLogic = IN_true;

        // Outport: '<Root>/Out_isFreeMode'
        // Entry 'true': '<S7>:701'
        // '<S7>:701:3' out_isFreeMode = true;
        rtY.Out_isFreeMode = true;
      }
    }
    else
    {
      // Outport: '<Root>/Out_isFreeMode'
      rtY.Out_isFreeMode = true;

      // During 'true': '<S7>:701'
      // '<S7>:704:1' sf_internal_predicateOutput = ~in(SuperState.SuperStateL2.Available.FreeViewID); 
      if (static_cast<int32_T>(rtDWork.is_Available) != 2)
      {
        // Transition: '<S7>:704'
        // [in_isViewChangeAvl]
        rtDWork.is_freeModeLogic = IN_false;

        // Outport: '<Root>/Out_isFreeMode'
        // Entry 'false': '<S7>:700'
        // '<S7>:700:3' out_isFreeMode = false;
        rtY.Out_isFreeMode = false;
      }
    }

    // During 'freeModeAct': '<S7>:710'
    if (static_cast<uint32_T>(rtDWork.is_freeModeAct) == IN_false)
    {
      // Outport: '<Root>/Out_isFreeModeAct'
      rtY.Out_isFreeModeAct = false;

      // During 'false': '<S7>:713'
      // '<S7>:712:1' sf_internal_predicateOutput = in(SuperState.SuperStateL2.Available.FreeViewID); 
      if (static_cast<int32_T>(rtDWork.is_Available) == 2)
      {
        // Transition: '<S7>:712'
        rtDWork.is_freeModeAct = IN_true;

        // Outport: '<Root>/Out_isFreeModeAct'
        // Entry 'true': '<S7>:715'
        // '<S7>:715:3' out_isFreeModeAct = true;
        rtY.Out_isFreeModeAct = true;
      }
    }
    else
    {
      // Outport: '<Root>/Out_isFreeModeAct'
      rtY.Out_isFreeModeAct = true;

      // During 'true': '<S7>:715'
      // '<S7>:711:1' sf_internal_predicateOutput = ~in(SuperState.SuperStateL2.Available.FreeViewID); 
      if (static_cast<int32_T>(rtDWork.is_Available) != 2)
      {
        // Transition: '<S7>:711'
        rtDWork.is_freeModeAct = IN_false;

        // Outport: '<Root>/Out_isFreeModeAct'
        // Entry 'false': '<S7>:713'
        // '<S7>:713:3' out_isFreeModeAct = false;
        rtY.Out_isFreeModeAct = false;
      }
    }

    SuperState(&out_manualChangeAvail, &out_isViewChangeAvl,
               &out_freemodeviewAct, &out_isCpcShow, &UnitDelay1,
               &in_displayedVewL_prev);
    ActiveState(&UnitDelay9, &in_SystemStr_prev, &in_calibStatus_prev,
                &in_calibActive_prev, &in_closeButtonPressed_prev,
                &in_competeQuit_prev);
  }

  // End of Chart: '<S1>/MainSM_Lev2'

  // Chart: '<S1>/screenTypeHandling' incorporates:
  //   Inport: '<Root>/In_gear'
  //   Outport: '<Root>/Out_SVSShowReq'
  //   Outport: '<Root>/Out_displayedView'

  // Gateway: MainSM_Lev1/screenTypeHandling
  // During: MainSM_Lev1/screenTypeHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c9_ViewModexViewState) == 0U)
  {
    // Entry: MainSM_Lev1/screenTypeHandling
    rtDWork.is_active_c9_ViewModexViewState = 1U;

    // Entry Internal: MainSM_Lev1/screenTypeHandling
    // Transition: '<S11>:4'
    // '<S11>:158:1' sf_internal_predicateOutput = in_SVSShowReq == true ...
    // '<S11>:158:2' && in_displayedView ~= EScreenID.NO_CHANGE;
    if (rtY.Out_SVSShowReq && (rtY.Out_displayedView != EScreenID_NO_CHANGE))
    {
      // Transition: '<S11>:158'
      rtDWork.is_c9_ViewModexViewStateMachine = IN_SVSScreenType_Shown;

      // Entry 'SVSScreenType_Shown': '<S11>:150'
      enter_internal_SVSScreenType_Sh();
    }
    else
    {
      // Transition: '<S11>:159'
      rtDWork.is_c9_ViewModexViewStateMachine = IN_SVSScreenType_None;

      // Outport: '<Root>/Out_SVSScreenType'
      // Entry 'SVSScreenType_None': '<S11>:152'
      // '<S11>:152:3' out_SVSScreenType = ESVSScreenType.SCREEN_NONE;
      rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_NONE;
    }
  }
  else if (static_cast<uint32_T>(rtDWork.is_c9_ViewModexViewStateMachine) ==
           IN_SVSScreenType_None)
  {
    // Outport: '<Root>/Out_SVSScreenType'
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_NONE;

    // During 'SVSScreenType_None': '<S11>:152'
    // '<S11>:161:1' sf_internal_predicateOutput = in_SVSShowReq == true;
    if (rtY.Out_SVSShowReq)
    {
      // Transition: '<S11>:161'
      rtDWork.is_c9_ViewModexViewStateMachine = IN_SVSScreenType_Shown;

      // Entry 'SVSScreenType_Shown': '<S11>:150'
      enter_internal_SVSScreenType_Sh();
    }

    // During 'SVSScreenType_Shown': '<S11>:150'
    // '<S11>:160:1' sf_internal_predicateOutput = in_SVSShowReq == false;
  }
  else if (!rtY.Out_SVSShowReq)
  {
    // Transition: '<S11>:160'
    rtDWork.is_c9_ViewModexViewStateMachine = IN_SVSScreenType_None;

    // Outport: '<Root>/Out_SVSScreenType'
    // Entry 'SVSScreenType_None': '<S11>:152'
    // '<S11>:152:3' out_SVSScreenType = ESVSScreenType.SCREEN_NONE;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_NONE;

    // Transition: '<S11>:198'
    // '<S11>:174:1' sf_internal_predicateOutput = in_displayedView == EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW||... 
    // '<S11>:174:2' in_displayedView == EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW||... 
    // '<S11>:174:3' in_displayedView == EScreenID.FLOAT_PARKING_FRONT_VIEW||... 
    // '<S11>:174:4' in_displayedView == EScreenID.FLOAT_PARKING_REAR_VIEW;
  }
  else if ((rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW))
  {
    // Transition: '<S11>:174'
    // Entry 'SVSScreenType_ParkScreen': '<S11>:182'
    // Entry Internal 'SVSScreenType_ParkScreen': '<S11>:182'
    // Transition: '<S11>:228'
    // '<S11>:226:1' sf_internal_predicateOutput = [in_displayedView == EScreenID.FLOAT_FRONT_PARKING_PLAN_VIEW||... 
    // '<S11>:226:2' in_displayedView == EScreenID.FLOAT_REAR_PARKING_PLAN_VIEW]; 
    if ((rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW) ||
        (rtY.Out_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW))
    {
      // Outport: '<Root>/Out_SVSScreenType'
      // Transition: '<S11>:226'
      // Entry 'SVSScreenType_SCREEN_FLOAT_PLAN_PARK1': '<S11>:227'
      // '<S11>:227:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_PLAN_PARK; 
      rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_PLAN_PARK;
    }
    else
    {
      // Outport: '<Root>/Out_SVSScreenType'
      // Transition: '<S11>:224'
      // Entry 'SVSScreenType_SCREEN_FLOAT_FR_PARK': '<S11>:210'
      // '<S11>:210:3' out_SVSScreenType = ESVSScreenType.SCREEN_FLOAT_FR_PARK;
      rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FLOAT_FR_PARK;
    }

    // '<S11>:200:1' sf_internal_predicateOutput = in_displayedView == EScreenID.FLOAT_REAR_VIEW || ... 
    // '<S11>:200:2' in_displayedView == EScreenID.FLOAT_FRONT_VIEW || ...
    // '<S11>:200:3' in_displayedView == EScreenID.FLOAT_FRONT_PLAN_VIEW || ...
    // '<S11>:200:4' in_displayedView == EScreenID.FLOAT_REAR_PLAN_VIEW;
  }
  else if ((rtY.Out_displayedView == EScreenID_FLOAT_REAR_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW) ||
           (rtY.Out_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW))
  {
    // Transition: '<S11>:200'
    // Entry 'SVSScreenType_notParkScreen': '<S11>:201'
    enter_internal_SVSScreenType_no();

    // Transition: '<S11>:194'
    // Entry 'SVSScreenType_FullScreen': '<S11>:190'
    // Entry Internal 'SVSScreenType_FullScreen': '<S11>:190'
    // Transition: '<S11>:180'
    // '<S11>:193:1' sf_internal_predicateOutput = in_gear == EGear.R;
  }
  else if (static_cast<uint32_T>(rtU.In_gear) == EGear_R)
  {
    // Outport: '<Root>/Out_SVSScreenType'
    // Transition: '<S11>:193'
    // Entry 'SVSScreenType_FullScreenR': '<S11>:169'
    // '<S11>:169:3' out_SVSScreenType = ESVSScreenType.SCREEN_FULL_R;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FULL_R;
  }
  else
  {
    // Outport: '<Root>/Out_SVSScreenType'
    // Transition: '<S11>:192'
    // Entry 'SVSScreenType_FullScreenNotR': '<S11>:166'
    // '<S11>:166:3' out_SVSScreenType = ESVSScreenType.SCREEN_FULL_NOTR;
    rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FULL_NOTR;
  }

  // End of Chart: '<S1>/screenTypeHandling'

  // Chart: '<S1>/voiceDockFeedbackHandling' incorporates:
  //   Inport: '<Root>/In_voiceDockRequest'

  if (static_cast<uint32_T>(rtDWork.temporalCounter_i1_e) < 127U)
  {
    rtDWork.temporalCounter_i1_e = static_cast<uint8_T>(static_cast<uint32_T>
      (rtDWork.temporalCounter_i1_e) + 1U);
  }

  // Gateway: MainSM_Lev1/voiceDockFeedbackHandling
  // During: MainSM_Lev1/voiceDockFeedbackHandling
  if (static_cast<uint32_T>(rtDWork.is_active_c7_ViewModexViewState) == 0U)
  {
    // Entry: MainSM_Lev1/voiceDockFeedbackHandling
    rtDWork.is_active_c7_ViewModexViewState = 1U;

    // Entry Internal: MainSM_Lev1/voiceDockFeedbackHandling
    // Transition: '<S12>:4'
    rtDWork.is_c7_ViewModexViewStateMachine = IN_No_VoiceDockRequest;

    // Outport: '<Root>/Out_voiceDockFeedback'
    // Entry 'No_VoiceDockRequest': '<S12>:150'
    // '<S12>:150:3' out_voiceDockFeedback = EVoiceDockFb.NONE;
    rtY.Out_voiceDockFeedback = EVoiceDockFb_NONE;
  }
  else
  {
    switch (rtDWork.is_c7_ViewModexViewStateMachine)
    {
     case IN_Has_VoiceDockRequest:
      // During 'Has_VoiceDockRequest': '<S12>:152'
      // '<S12>:155:1' sf_internal_predicateOutput = in_voiceDockRequest == EVoiceDockReq.NONE; 
      if (static_cast<uint32_T>(rtU.In_voiceDockRequest) == EVoiceDockReq_NONE)
      {
        // Transition: '<S12>:155'
        rtDWork.is_c7_ViewModexViewStateMachine = IN_temp_f;
        rtDWork.temporalCounter_i1_e = 0U;
      }
      else
      {
        // Outport: '<Root>/Out_voiceDockFeedback'
        // '<S12>:152:3' out_voiceDockFeedback = in_voiceDockFeedback;
        rtY.Out_voiceDockFeedback = rtDWork.out_voiceDockFeedback_b;
      }
      break;

     case IN_No_VoiceDockRequest:
      // During 'No_VoiceDockRequest': '<S12>:150'
      // '<S12>:151:1' sf_internal_predicateOutput = in_voiceDockRequest ~= EVoiceDockReq.NONE; 
      if (static_cast<uint32_T>(rtU.In_voiceDockRequest) != EVoiceDockReq_NONE)
      {
        // Transition: '<S12>:151'
        rtDWork.is_c7_ViewModexViewStateMachine = IN_Has_VoiceDockRequest;

        // Outport: '<Root>/Out_voiceDockFeedback'
        // Entry 'Has_VoiceDockRequest': '<S12>:152'
        // '<S12>:152:3' out_voiceDockFeedback = in_voiceDockFeedback;
        rtY.Out_voiceDockFeedback = rtDWork.out_voiceDockFeedback_b;
      }
      break;

     default:
      // During 'temp': '<S12>:154'
      // '<S12>:153:1' sf_internal_predicateOutput = after(1, sec);
      if (static_cast<uint32_T>(rtDWork.temporalCounter_i1_e) >= 100U)
      {
        // Transition: '<S12>:153'
        rtDWork.is_c7_ViewModexViewStateMachine = IN_No_VoiceDockRequest;

        // Outport: '<Root>/Out_voiceDockFeedback'
        // Entry 'No_VoiceDockRequest': '<S12>:150'
        // '<S12>:150:3' out_voiceDockFeedback = EVoiceDockFb.NONE;
        rtY.Out_voiceDockFeedback = EVoiceDockFb_NONE;
      }
      break;
    }
  }

  // End of Chart: '<S1>/voiceDockFeedbackHandling'

  // Chart: '<S1>/CpcHuSwitch' incorporates:
  //   BusCreator generated from: '<S1>/CpcHuSwitch'
  //   Inport: '<Root>/In_CPCSwitch_Layouts'
  //   Inport: '<Root>/In_HUTouchCoorX'
  //   Inport: '<Root>/In_HUTouchCoorY'
  //   Inport: '<Root>/In_TouchSt'

  if (static_cast<uint32_T>(rtDWork.temporalCounter_i1_bn) < 127U)
  {
    rtDWork.temporalCounter_i1_bn = static_cast<uint8_T>(static_cast<uint32_T>
      (rtDWork.temporalCounter_i1_bn) + 1U);
  }

  if (static_cast<uint32_T>(rtDWork.temporalCounter_i2_i) < 1023U)
  {
    rtDWork.temporalCounter_i2_i = static_cast<uint16_T>(static_cast<uint32_T>
      (rtDWork.temporalCounter_i2_i) + 1U);
  }

  if (static_cast<uint32_T>(rtDWork.temporalCounter_i3_g) < 1023U)
  {
    rtDWork.temporalCounter_i3_g = static_cast<uint16_T>(static_cast<uint32_T>
      (rtDWork.temporalCounter_i3_g) + 1U);
  }

  // Gateway: MainSM_Lev1/CpcHuSwitch
  in_displayedVewL_prev = rtDWork.in_SVSCurrentView_start;
  rtDWork.in_SVSCurrentView_start = rtDWork.UnitDelay;

  // During: MainSM_Lev1/CpcHuSwitch
  if (static_cast<uint32_T>(rtDWork.is_active_c2_ViewModexViewState) == 0U)
  {
    // Entry: MainSM_Lev1/CpcHuSwitch
    rtDWork.is_active_c2_ViewModexViewState = 1U;

    // Entry Internal: MainSM_Lev1/CpcHuSwitch
    // Entry Internal 'status': '<S2>:53'
    // Transition: '<S2>:79'
    rtDWork.is_status = IN_false;

    // Entry 'false': '<S2>:84'
    // '<S2>:84:3' out_CpcHUSwitch = false;
    out_manualChangeAvail = false;

    // Entry 'timesTop': '<S2>:95'
    //  5 times at top front coner of top view && 5 times at top front corner of front view in 10s. 
    // Entry Internal 'timesTop': '<S2>:95'
    // Transition: '<S2>:69'
    rtDWork.is_timesTop = IN_reset;

    // Entry 'reset': '<S2>:63'
    // '<S2>:63:3' l_counterTopView = uint8(0);
    rtDWork.l_counterTopView = 0U;

    // Entry 'timesFront': '<S2>:131'
    //  5 times at top front coner of top view && 5 times at top front corner of front view in 10s. 
    // Entry Internal 'timesFront': '<S2>:131'
    // Transition: '<S2>:132'
    rtDWork.is_timesFront = IN_reset1;

    // Entry 'reset1': '<S2>:130'
    // '<S2>:130:3' l_counterFrontView = uint8(0);
    rtDWork.l_counterFrontView = 0U;
  }
  else
  {
    int32_T tmp;

    // During 'status': '<S2>:53'
    if (static_cast<uint32_T>(rtDWork.is_status) == IN_false)
    {
      out_manualChangeAvail = false;

      // During 'false': '<S2>:84'
      // '<S2>:88:1' sf_internal_predicateOutput =  EScreenID.SINGLE_FRONT_NORMAL == in_SVSCurrentView && ... 
      // '<S2>:88:2' l_counterTopView > uint8(4) && ...
      // '<S2>:88:3' l_counterFrontView > uint8(4);
      if ((EScreenID_SINGLE_FRONT_NORMAL == rtDWork.UnitDelay) &&
          (static_cast<int32_T>(rtDWork.l_counterTopView) > 4) &&
          (static_cast<int32_T>(rtDWork.l_counterFrontView) > 4))
      {
        // Transition: '<S2>:88'
        rtDWork.is_status = IN_true;
        rtDWork.temporalCounter_i1_bn = 0U;

        // Entry 'true': '<S2>:86'
        // '<S2>:86:3' out_CpcHUSwitch = true;
        out_manualChangeAvail = true;
      }
    }
    else
    {
      out_manualChangeAvail = true;

      // During 'true': '<S2>:86'
      // '<S2>:92:1' sf_internal_predicateOutput = after(1, sec) || ...
      // '<S2>:92:2' hasChanged(in_SVSCurrentView) || ...
      // '<S2>:92:3' EScreenID.SINGLE_FRONT_NORMAL ~= in_SVSCurrentView;
      if ((static_cast<uint32_T>(rtDWork.temporalCounter_i1_bn) >= 100U) ||
          (in_displayedVewL_prev != rtDWork.in_SVSCurrentView_start) ||
          (EScreenID_SINGLE_FRONT_NORMAL != rtDWork.UnitDelay))
      {
        // Transition: '<S2>:92'
        rtDWork.is_status = IN_false;

        // Entry 'false': '<S2>:84'
        // '<S2>:84:3' out_CpcHUSwitch = false;
        out_manualChangeAvail = false;
      }
    }

    // During 'timesTop': '<S2>:95'
    //  5 times at top front coner of top view && 5 times at top front corner of front view in 10s. 
    if (static_cast<uint32_T>(rtDWork.is_timesTop) == IN_counterTopView)
    {
      // During 'counterTopView': '<S2>:68'
      // '<S2>:73:1' sf_internal_predicateOutput = after(10,sec) || ...
      // '<S2>:73:2' hasChanged(in_SVSCurrentView) || ...
      // '<S2>:73:3' EScreenID.SINGLE_FRONT_NORMAL ~= in_SVSCurrentView || ...
      // '<S2>:73:4' out_CpcHUSwitch == true;
      if ((static_cast<uint32_T>(rtDWork.temporalCounter_i2_i) >= 1000U) ||
          (in_displayedVewL_prev != rtDWork.in_SVSCurrentView_start) ||
          (EScreenID_SINGLE_FRONT_NORMAL != rtDWork.UnitDelay) ||
          out_manualChangeAvail)
      {
        // Transition: '<S2>:73'
        // Exit Internal 'counterTopView': '<S2>:68'
        rtDWork.is_counterTopView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_timesTop = IN_reset;

        // Entry 'reset': '<S2>:63'
        // '<S2>:63:3' l_counterTopView = uint8(0);
        rtDWork.l_counterTopView = 0U;
      }
      else if (static_cast<uint32_T>(rtDWork.is_counterTopView) == IN_add)
      {
        // During 'add': '<S2>:71'
        // '<S2>:76:1' sf_internal_predicateOutput = ~checkCoorInResponseArea(in_CoorX, in_CoorY, in_CPCSwitch_Layouts.CPCSwitchTopView, in_TouchSt); 
        if (!checkCoorInResponseArea(rtU.In_HUTouchCoorX, rtU.In_HUTouchCoorY,
             rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.iconCenter.x,
             rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.iconCenter.y,
             rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.responseArea.x,
             rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.responseArea.y,
             rtU.In_TouchSt))
        {
          // Transition: '<S2>:76'
          rtDWork.is_counterTopView = IN_temp;

          // Entry 'temp': '<S2>:74'
          //  do nothing
        }

        // During 'temp': '<S2>:74'
        // '<S2>:77:1' sf_internal_predicateOutput = checkCoorInResponseArea(in_CoorX, in_CoorY, in_CPCSwitch_Layouts.CPCSwitchTopView, in_TouchSt); 
      }
      else if (checkCoorInResponseArea(rtU.In_HUTouchCoorX, rtU.In_HUTouchCoorY,
                rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.iconCenter.x,
                rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.iconCenter.y,
                rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.responseArea.x,
                rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.responseArea.y,
                rtU.In_TouchSt))
      {
        // Transition: '<S2>:77'
        rtDWork.is_counterTopView = IN_add;

        // Entry 'add': '<S2>:71'
        // '<S2>:71:3' l_counterTopView = l_counterTopView +uint8(1);
        tmp = static_cast<int32_T>(static_cast<uint32_T>(static_cast<uint32_T>
          (rtDWork.l_counterTopView) + 1U));
        if (static_cast<uint32_T>(rtDWork.l_counterTopView) + 1U > 255U)
        {
          tmp = 255;
        }

        rtDWork.l_counterTopView = static_cast<uint8_T>(tmp);
      }
      else
      {
        //  do nothing
      }

      // During 'reset': '<S2>:63'
      // '<S2>:72:1' sf_internal_predicateOutput = checkCoorInResponseArea(in_CoorX, in_CoorY, in_CPCSwitch_Layouts.CPCSwitchTopView, in_TouchSt) ... 
      // '<S2>:72:2' && EScreenID.SINGLE_FRONT_NORMAL == in_SVSCurrentView;
    }
    else if (checkCoorInResponseArea(rtU.In_HUTouchCoorX, rtU.In_HUTouchCoorY,
              rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.iconCenter.x,
              rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.iconCenter.y,
              rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.responseArea.x,
              rtU.In_CPCSwitch_Layouts.CPCSwitchTopView.responseArea.y,
              rtU.In_TouchSt) && (EScreenID_SINGLE_FRONT_NORMAL ==
              rtDWork.UnitDelay))
    {
      // Transition: '<S2>:72'
      rtDWork.is_timesTop = IN_counterTopView;
      rtDWork.temporalCounter_i2_i = 0U;

      // Entry 'counterTopView': '<S2>:68'
      // Entry Internal 'counterTopView': '<S2>:68'
      // Transition: '<S2>:75'
      rtDWork.is_counterTopView = IN_add;

      // Entry 'add': '<S2>:71'
      // '<S2>:71:3' l_counterTopView = l_counterTopView +uint8(1);
      tmp = static_cast<int32_T>(static_cast<uint32_T>(static_cast<uint32_T>
        (rtDWork.l_counterTopView) + 1U));
      if (static_cast<uint32_T>(rtDWork.l_counterTopView) + 1U > 255U)
      {
        tmp = 255;
      }

      rtDWork.l_counterTopView = static_cast<uint8_T>(tmp);
    }
    else
    {
      // no actions
    }

    // During 'timesFront': '<S2>:131'
    //  5 times at top front coner of top view && 5 times at top front corner of front view in 10s. 
    if (static_cast<uint32_T>(rtDWork.is_timesFront) == IN_counterFrontView)
    {
      // During 'counterFrontView': '<S2>:97'
      // '<S2>:104:1' sf_internal_predicateOutput = after(10,sec) || ...
      // '<S2>:104:2' hasChanged(in_SVSCurrentView) || ...
      // '<S2>:104:3' EScreenID.SINGLE_FRONT_NORMAL ~= in_SVSCurrentView || ...
      // '<S2>:104:4' out_CpcHUSwitch == true;
      if ((static_cast<uint32_T>(rtDWork.temporalCounter_i3_g) >= 1000U) ||
          (in_displayedVewL_prev != rtDWork.in_SVSCurrentView_start) ||
          (EScreenID_SINGLE_FRONT_NORMAL != rtDWork.UnitDelay) ||
          out_manualChangeAvail)
      {
        // Transition: '<S2>:104'
        // Exit Internal 'counterFrontView': '<S2>:97'
        rtDWork.is_counterFrontView = IN_NO_ACTIVE_CHILD;
        rtDWork.is_timesFront = IN_reset1;

        // Entry 'reset1': '<S2>:130'
        // '<S2>:130:3' l_counterFrontView = uint8(0);
        rtDWork.l_counterFrontView = 0U;
      }
      else if (static_cast<uint32_T>(rtDWork.is_counterFrontView) == IN_add)
      {
        // During 'add': '<S2>:99'
        // '<S2>:101:1' sf_internal_predicateOutput = ~checkCoorInResponseArea(in_CoorX, in_CoorY, in_CPCSwitch_Layouts.CPCSwitchFrontView, in_TouchSt); 
        if (!checkCoorInResponseArea(rtU.In_HUTouchCoorX, rtU.In_HUTouchCoorY,
             rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.iconCenter.x,
             rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.iconCenter.y,
             rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.responseArea.x,
             rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.responseArea.y,
             rtU.In_TouchSt))
        {
          // Transition: '<S2>:101'
          rtDWork.is_counterFrontView = IN_temp;

          // Entry 'temp': '<S2>:100'
          //  do nothing
        }

        // During 'temp': '<S2>:100'
        // '<S2>:102:1' sf_internal_predicateOutput = checkCoorInResponseArea(in_CoorX, in_CoorY, in_CPCSwitch_Layouts.CPCSwitchFrontView, in_TouchSt); 
      }
      else if (checkCoorInResponseArea(rtU.In_HUTouchCoorX, rtU.In_HUTouchCoorY,
                rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.iconCenter.x,
                rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.iconCenter.y,
                rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.responseArea.x,
                rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.responseArea.y,
                rtU.In_TouchSt))
      {
        // Transition: '<S2>:102'
        rtDWork.is_counterFrontView = IN_add;

        // Entry 'add': '<S2>:99'
        // '<S2>:99:3' l_counterFrontView = l_counterFrontView +uint8(1);
        tmp = static_cast<int32_T>(static_cast<uint32_T>(static_cast<uint32_T>
          (rtDWork.l_counterFrontView) + 1U));
        if (static_cast<uint32_T>(rtDWork.l_counterFrontView) + 1U > 255U)
        {
          tmp = 255;
        }

        rtDWork.l_counterFrontView = static_cast<uint8_T>(tmp);
      }
      else
      {
        //  do nothing
      }

      // During 'reset1': '<S2>:130'
      // '<S2>:98:1' sf_internal_predicateOutput = checkCoorInResponseArea(in_CoorX, in_CoorY, in_CPCSwitch_Layouts.CPCSwitchFrontView, in_TouchSt) ... 
      // '<S2>:98:2' && EScreenID.SINGLE_FRONT_NORMAL == in_SVSCurrentView;
    }
    else if (checkCoorInResponseArea(rtU.In_HUTouchCoorX, rtU.In_HUTouchCoorY,
              rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.iconCenter.x,
              rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.iconCenter.y,
              rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.responseArea.x,
              rtU.In_CPCSwitch_Layouts.CPCSwitchFrontView.responseArea.y,
              rtU.In_TouchSt) && (EScreenID_SINGLE_FRONT_NORMAL ==
              rtDWork.UnitDelay))
    {
      // Transition: '<S2>:98'
      rtDWork.is_timesFront = IN_counterFrontView;
      rtDWork.temporalCounter_i3_g = 0U;

      // Entry 'counterFrontView': '<S2>:97'
      // Entry Internal 'counterFrontView': '<S2>:97'
      // Transition: '<S2>:103'
      rtDWork.is_counterFrontView = IN_add;

      // Entry 'add': '<S2>:99'
      // '<S2>:99:3' l_counterFrontView = l_counterFrontView +uint8(1);
      tmp = static_cast<int32_T>(static_cast<uint32_T>(static_cast<uint32_T>
        (rtDWork.l_counterFrontView) + 1U));
      if (static_cast<uint32_T>(rtDWork.l_counterFrontView) + 1U > 255U)
      {
        tmp = 255;
      }

      rtDWork.l_counterFrontView = static_cast<uint8_T>(tmp);
    }
    else
    {
      // no actions
    }
  }

  // End of Chart: '<S1>/CpcHuSwitch'

  // Outport: '<Root>/Out_HuCPCActive'
  rtY.Out_HuCPCActive = out_manualChangeAvail;

  // Chart: '<S1>/LastViewHandling' incorporates:
  //   Outport: '<Root>/Out_SVSViewModeSts'
  //   Outport: '<Root>/Out_displayedView'

  // Gateway: MainSM_Lev1/LastViewHandling
  in_displayedVewL_prev = rtDWork.in_displayedView_start;
  rtDWork.in_displayedView_start = rtY.Out_displayedView;

  // During: MainSM_Lev1/LastViewHandling
  if (!rtDWork.doneDoubleBufferReInit)
  {
    rtDWork.doneDoubleBufferReInit = true;
    in_displayedVewL_prev = rtY.Out_displayedView;
  }

  // Entry Internal: MainSM_Lev1/LastViewHandling
  // Transition: '<S6>:4'
  // '<S6>:5:1' sf_internal_predicateOutput = hasChanged(in_displayedView);
  if (in_displayedVewL_prev != rtDWork.in_displayedView_start)
  {
    // Transition: '<S6>:5'
    // Transition: '<S6>:7'
    // '<S6>:7:1' out_SVSLastView          = l_lastView;
    rtDWork.out_SVSLastView = rtDWork.l_lastView;

    // '<S6>:7:2' out_SVSViewModelLast = l_lastViewMode;
    rtDWork.out_SVSViewModelLast = rtDWork.l_lastViewMode;

    // '<S6>:7:3' l_lastView                        = in_displayedView;
    rtDWork.l_lastView = rtY.Out_displayedView;

    // '<S6>:7:4' l_lastViewMode               = in_SVSViewMode;
    rtDWork.l_lastViewMode = rtY.Out_SVSViewModeSts;

    // Transition: '<S6>:12'
  }
  else
  {
    // Transition: '<S6>:11'
  }

  // End of Chart: '<S1>/LastViewHandling'

  // Update for UnitDelay: '<S1>/Unit Delay' incorporates:
  //   Outport: '<Root>/Out_SVSCurrentView'

  rtDWork.UnitDelay_DSTATE = rtY.Out_SVSCurrentView;

  // Update for UnitDelay: '<S1>/Unit Delay1' incorporates:
  //   Outport: '<Root>/Out_SVSViewModeSts'

  rtDWork.UnitDelay1_DSTATE = rtY.Out_SVSViewModeSts;

  // Update for UnitDelay: '<S1>/Unit Delay5' incorporates:
  //   Outport: '<Root>/Out_displayedView'

  rtDWork.UnitDelay5_DSTATE = rtY.Out_displayedView;

  // Update for UnitDelay: '<S1>/Unit Delay6' incorporates:
  //   Outport: '<Root>/Out_competeReqStatus'

  rtDWork.UnitDelay6_DSTATE = rtY.Out_competeReqStatus;

  // Update for UnitDelay: '<S1>/Unit Delay7' incorporates:
  //   Outport: '<Root>/Out_ViewModeGroup'

  rtDWork.UnitDelay7_DSTATE = rtY.Out_ViewModeGroup;

  // Update for UnitDelay: '<S1>/Unit Delay8' incorporates:
  //   Outport: '<Root>/Out_SVSShowReq'

  rtDWork.UnitDelay8_DSTATE = rtY.Out_SVSShowReq;

  // Update for UnitDelay: '<S1>/Unit Delay9' incorporates:
  //   Outport: '<Root>/Out_SVSScreenType'

  rtDWork.UnitDelay9_DSTATE = rtY.Out_SVSScreenType;
}

// Model initialize function
void ViewModeStateFlowStateMachineModelClass::initialize()
{
  // Registration code

  // states (dwork)
  {
    rtDWork.UnitDelay7 = VIEWMODE_FRONT;
  }

  // external outputs
  rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FULL_R;
  rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_R;
  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

  // InitializeConditions for UnitDelay: '<S1>/Unit Delay7'
  rtDWork.UnitDelay7_DSTATE = VIEWMODE_FRONT;

  // InitializeConditions for UnitDelay: '<S1>/Unit Delay9'
  rtDWork.UnitDelay9_DSTATE = ESVSScreenType_SCREEN_NONE;

  // SystemInitialize for Outport: '<Root>/Out_competeScreenTypeReq' incorporates:
  //   Chart: '<S1>/MainSM_Lev2'

  rtY.Out_competeScreenTypeReq = ESVSScreenType_SCREEN_FULL_R;

  // SystemInitialize for Outport: '<Root>/Out_ViewModeGroup' incorporates:
  //   Chart: '<S1>/MainSM_Lev2'

  rtY.Out_ViewModeGroup = VIEWMODE_FRONT;

  // SystemInitialize for Outport: '<Root>/Out_SVSScreenType' incorporates:
  //   Chart: '<S1>/screenTypeHandling'

  rtY.Out_SVSScreenType = ESVSScreenType_SCREEN_FULL_R;

  // user code (Initialize function Body)

  // caf8si sage: init function

  // caf8si sage: End init function
}

// Model terminate function
void ViewModeStateFlowStateMachineModelClass::terminate()
{
  // user code (Terminate function Body)

  // caf8si sage: term function

  // caf8si sage: End term function
}

// Root inport: '<Root>/In_HUViewReq' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUViewReq(EScreenID
  localArgInput)
{
  rtU.In_HUViewReq = localArgInput;
}

// Root inport: '<Root>/In_HUSVSMode' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUSVSMode(ESVSViewMode
  localArgInput)
{
  rtU.In_HUSVSMode = localArgInput;
}

// Root inport: '<Root>/In_HUTouchCoorX' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUTouchCoorX(uint32_T
  localArgInput)
{
  rtU.In_HUTouchCoorX = localArgInput;
}

// Root inport: '<Root>/In_HUTouchCoorY' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUTouchCoorY(uint32_T
  localArgInput)
{
  rtU.In_HUTouchCoorY = localArgInput;
}

// Root inport: '<Root>/In_vehSpeed' set method
void ViewModeStateFlowStateMachineModelClass::setIn_vehSpeed(real_T
  localArgInput)
{
  rtU.In_vehSpeed = localArgInput;
}

// Root inport: '<Root>/In_gear' set method
void ViewModeStateFlowStateMachineModelClass::setIn_gear(EGear localArgInput)
{
  rtU.In_gear = localArgInput;
}

// Root inport: '<Root>/In_parkstatus' set method
void ViewModeStateFlowStateMachineModelClass::setIn_parkstatus(boolean_T
  localArgInput)
{
  rtU.In_parkstatus = localArgInput;
}

// Root inport: '<Root>/In_TouchSt' set method
void ViewModeStateFlowStateMachineModelClass::setIn_TouchSt(ETouchStatus
  localArgInput)
{
  rtU.In_TouchSt = localArgInput;
}

// Root inport: '<Root>/In_parkingScreenDelay' set method
void ViewModeStateFlowStateMachineModelClass::setIn_parkingScreenDelay(uint32_T
  localArgInput)
{
  rtU.In_parkingScreenDelay = localArgInput;
}

// Root inport: '<Root>/In_animationState' set method
void ViewModeStateFlowStateMachineModelClass::setIn_animationState
  (AnimationState localArgInput)
{
  rtU.In_animationState = localArgInput;
}

// Root inport: '<Root>/In_IMB_FrontSt' set method
void ViewModeStateFlowStateMachineModelClass::setIn_IMB_FrontSt(boolean_T
  localArgInput)
{
  rtU.In_IMB_FrontSt = localArgInput;
}

// Root inport: '<Root>/In_IMB_LeftSt' set method
void ViewModeStateFlowStateMachineModelClass::setIn_IMB_LeftSt(boolean_T
  localArgInput)
{
  rtU.In_IMB_LeftSt = localArgInput;
}

// Root inport: '<Root>/In_IMB_RearSt' set method
void ViewModeStateFlowStateMachineModelClass::setIn_IMB_RearSt(boolean_T
  localArgInput)
{
  rtU.In_IMB_RearSt = localArgInput;
}

// Root inport: '<Root>/In_IMB_RightSt' set method
void ViewModeStateFlowStateMachineModelClass::setIn_IMB_RightSt(boolean_T
  localArgInput)
{
  rtU.In_IMB_RightSt = localArgInput;
}

// Root inport: '<Root>/In_speedTrigIn' set method
void ViewModeStateFlowStateMachineModelClass::setIn_speedTrigIn(real_T
  localArgInput)
{
  rtU.In_speedTrigIn = localArgInput;
}

// Root inport: '<Root>/In_speedTrigOut' set method
void ViewModeStateFlowStateMachineModelClass::setIn_speedTrigOut(real_T
  localArgInput)
{
  rtU.In_speedTrigOut = localArgInput;
}

// Root inport: '<Root>/In_threatDuration' set method
void ViewModeStateFlowStateMachineModelClass::setIn_threatDuration(uint32_T
  localArgInput)
{
  rtU.In_threatDuration = localArgInput;
}

// Root inport: '<Root>/In_campositionX' set method
void ViewModeStateFlowStateMachineModelClass::setIn_campositionX(uint32_T
  localArgInput)
{
  rtU.In_campositionX = localArgInput;
}

// Root inport: '<Root>/In_campositionY' set method
void ViewModeStateFlowStateMachineModelClass::setIn_campositionY(uint32_T
  localArgInput)
{
  rtU.In_campositionY = localArgInput;
}

// Root inport: '<Root>/In_powerMode' set method
void ViewModeStateFlowStateMachineModelClass::setIn_powerMode(EPowerMode
  localArgInput)
{
  rtU.In_powerMode = localArgInput;
}

// Root inport: '<Root>/In_FID_SVSEcuInternalStatus' set method
void ViewModeStateFlowStateMachineModelClass::setIn_FID_SVSEcuInternalStatus
  (boolean_T localArgInput)
{
  rtU.In_FID_SVSEcuInternalStatus = localArgInput;
}

// Root inport: '<Root>/In_parkingGuidanceScreenDelay' set method
void ViewModeStateFlowStateMachineModelClass::setIn_parkingGuidanceScreenDelay
  (uint32_T localArgInput)
{
  rtU.In_parkingGuidanceScreenDelay = localArgInput;
}

// Root inport: '<Root>/In_smDelay' set method
void ViewModeStateFlowStateMachineModelClass::setIn_smDelay(uint32_T
  localArgInput)
{
  rtU.In_smDelay = localArgInput;
}

// Root inport: '<Root>/In_pasWarnTone' set method
void ViewModeStateFlowStateMachineModelClass::setIn_pasWarnTone(EPasWarnTone
  localArgInput)
{
  rtU.In_pasWarnTone = localArgInput;
}

// Root inport: '<Root>/In_HUImageWorkMode' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUImageWorkMode
  (EHuImageWorkMode localArgInput)
{
  rtU.In_HUImageWorkMode = localArgInput;
}

// Root inport: '<Root>/In_HUDislayModeSwitch' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUDislayModeSwitch
  (EHuDisplayModeSwitch localArgInput)
{
  rtU.In_HUDislayModeSwitch = localArgInput;
}

// Root inport: '<Root>/In_HUDislayModeExpand' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUDislayModeExpand
  (EHuDisplayModeExpand localArgInput)
{
  rtU.In_HUDislayModeExpand = localArgInput;
}

// Root inport: '<Root>/In_HUDislayModeExpandNew' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUDislayModeExpandNew
  (EHuDisplayModeExpandNew localArgInput)
{
  rtU.In_HUDislayModeExpandNew = localArgInput;
}

// Root inport: '<Root>/In_isCpcActive' set method
void ViewModeStateFlowStateMachineModelClass::setIn_isCpcActive(boolean_T
  localArgInput)
{
  rtU.In_isCpcActive = localArgInput;
}

// Root inport: '<Root>/In_cpcDelay' set method
void ViewModeStateFlowStateMachineModelClass::setIn_cpcDelay(uint32_T
  localArgInput)
{
  rtU.In_cpcDelay = localArgInput;
}

// Root inport: '<Root>/In_isCpcFuncOn' set method
void ViewModeStateFlowStateMachineModelClass::setIn_isCpcFuncOn(boolean_T
  localArgInput)
{
  rtU.In_isCpcFuncOn = localArgInput;
}

// Root inport: '<Root>/In_huPasAct' set method
void ViewModeStateFlowStateMachineModelClass::setIn_huPasAct(ESettingSts
  localArgInput)
{
  rtU.In_huPasAct = localArgInput;
}

// Root inport: '<Root>/In_huSteeringAct' set method
void ViewModeStateFlowStateMachineModelClass::setIn_huSteeringAct(ESettingSts
  localArgInput)
{
  rtU.In_huSteeringAct = localArgInput;
}

// Root inport: '<Root>/In_huDGearAct' set method
void ViewModeStateFlowStateMachineModelClass::setIn_huDGearAct(ESettingSts
  localArgInput)
{
  rtU.In_huDGearAct = localArgInput;
}

// Root inport: '<Root>/In_isFirstDGear' set method
void ViewModeStateFlowStateMachineModelClass::setIn_isFirstDGear(boolean_T
  localArgInput)
{
  rtU.In_isFirstDGear = localArgInput;
}

// Root inport: '<Root>/In_HUShowReq' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HUShowReq(boolean_T
  localArgInput)
{
  rtU.In_HUShowReq = localArgInput;
}

// Root inport: '<Root>/In_calibStatus' set method
void ViewModeStateFlowStateMachineModelClass::setIn_calibStatus(ECalibStatus
  localArgInput)
{
  rtU.In_calibStatus = localArgInput;
}

// Root inport: '<Root>/In_calibActive' set method
void ViewModeStateFlowStateMachineModelClass::setIn_calibActive(uint8_T
  localArgInput)
{
  rtU.In_calibActive = localArgInput;
}

// Root inport: '<Root>/In_androidIconActive' set method
void ViewModeStateFlowStateMachineModelClass::setIn_androidIconActive(boolean_T
  localArgInput)
{
  rtU.In_androidIconActive = localArgInput;
}

// Root inport: '<Root>/In_closeButtonPressed' set method
void ViewModeStateFlowStateMachineModelClass::setIn_closeButtonPressed(boolean_T
  localArgInput)
{
  rtU.In_closeButtonPressed = localArgInput;
}

// Root inport: '<Root>/In_distTrigLevel' set method
void ViewModeStateFlowStateMachineModelClass::setIn_distTrigLevel
  (ESonarTrigLevel localArgInput)
{
  rtU.In_distTrigLevel = localArgInput;
}

// Root inport: '<Root>/In_huSteeringAngleTrigIn' set method
void ViewModeStateFlowStateMachineModelClass::setIn_huSteeringAngleTrigIn
  (real32_T localArgInput)
{
  rtU.In_huSteeringAngleTrigIn = localArgInput;
}

// Root inport: '<Root>/In_huSteeringAngleTrigOut' set method
void ViewModeStateFlowStateMachineModelClass::setIn_huSteeringAngleTrigOut
  (real32_T localArgInput)
{
  rtU.In_huSteeringAngleTrigOut = localArgInput;
}

// Root inport: '<Root>/In_huSteeringAngleFront' set method
void ViewModeStateFlowStateMachineModelClass::setIn_huSteeringAngleFront(real_T
  localArgInput)
{
  rtU.In_huSteeringAngleFront = localArgInput;
}

// Root inport: '<Root>/In_voiceDockRequest' set method
void ViewModeStateFlowStateMachineModelClass::setIn_voiceDockRequest
  (EVoiceDockReq localArgInput)
{
  rtU.In_voiceDockRequest = localArgInput;
}

// Root inport: '<Root>/In_SRIsActive' set method
void ViewModeStateFlowStateMachineModelClass::setIn_SRIsActive(ESRActiveSts
  localArgInput)
{
  rtU.In_SRIsActive = localArgInput;
}

// Root inport: '<Root>/In_CPCSwitch_Layouts' set method
void ViewModeStateFlowStateMachineModelClass::setIn_CPCSwitch_Layouts
  (HmiUIElements localArgInput)
{
  rtU.In_CPCSwitch_Layouts = localArgInput;
}

// Root inport: '<Root>/In_competeActiveAllow' set method
void ViewModeStateFlowStateMachineModelClass::setIn_competeActiveAllow
  (ECompeteActiveAllow localArgInput)
{
  rtU.In_competeActiveAllow = localArgInput;
}

// Root inport: '<Root>/In_competeQuit' set method
void ViewModeStateFlowStateMachineModelClass::setIn_competeQuit(boolean_T
  localArgInput)
{
  rtU.In_competeQuit = localArgInput;
}

// Root inport: '<Root>/In_ignoreCompete' set method
void ViewModeStateFlowStateMachineModelClass::setIn_ignoreCompete(boolean_T
  localArgInput)
{
  rtU.In_ignoreCompete = localArgInput;
}

// Root inport: '<Root>/In_backkeyEvent' set method
void ViewModeStateFlowStateMachineModelClass::setIn_backkeyEvent(boolean_T
  localArgInput)
{
  rtU.In_backkeyEvent = localArgInput;
}

// Root inport: '<Root>/In_steeringWheelButtonPressed' set method
void ViewModeStateFlowStateMachineModelClass::setIn_steeringWheelButtonPressed
  (boolean_T localArgInput)
{
  rtU.In_steeringWheelButtonPressed = localArgInput;
}

// Root inport: '<Root>/In_ModWarning' set method
void ViewModeStateFlowStateMachineModelClass::setIn_ModWarning(boolean_T
  localArgInput)
{
  rtU.In_ModWarning = localArgInput;
}

// Root inport: '<Root>/In_SystemStr' set method
void ViewModeStateFlowStateMachineModelClass::setIn_SystemStr(ESystemStr
  localArgInput)
{
  rtU.In_SystemStr = localArgInput;
}

// Root inport: '<Root>/In_AVMError' set method
void ViewModeStateFlowStateMachineModelClass::setIn_AVMError(boolean_T
  localArgInput)
{
  rtU.In_AVMError = localArgInput;
}

// Root inport: '<Root>/In_PowerSaveMode' set method
void ViewModeStateFlowStateMachineModelClass::setIn_PowerSaveMode(boolean_T
  localArgInput)
{
  rtU.In_PowerSaveMode = localArgInput;
}

// Root inport: '<Root>/In_isFirstRGear' set method
void ViewModeStateFlowStateMachineModelClass::setIn_isFirstRGear(boolean_T
  localArgInput)
{
  rtU.In_isFirstRGear = localArgInput;
}

// Root inport: '<Root>/In_sonarDistLevel' set method
void ViewModeStateFlowStateMachineModelClass::setIn_sonarDistLevel
  (SonarDistLevel localArgInput)
{
  rtU.In_sonarDistLevel = localArgInput;
}

// Root inport: '<Root>/In_exitDelay' set method
void ViewModeStateFlowStateMachineModelClass::setIn_exitDelay(ExitDelay
  localArgInput)
{
  rtU.In_exitDelay = localArgInput;
}

// Root inport: '<Root>/In_dockAvmButtonPress' set method
void ViewModeStateFlowStateMachineModelClass::setIn_dockAvmButtonPress
  (EDockAvmButtonPress localArgInput)
{
  rtU.In_dockAvmButtonPress = localArgInput;
}

// Root inport: '<Root>/In_timeStepScaleFactor' set method
void ViewModeStateFlowStateMachineModelClass::setIn_timeStepScaleFactor(real_T
  localArgInput)
{
  rtU.In_timeStepScaleFactor = localArgInput;
}

// Root inport: '<Root>/In_SRIsTopActivity' set method
void ViewModeStateFlowStateMachineModelClass::setIn_SRIsTopActivity(boolean_T
  localArgInput)
{
  rtU.In_SRIsTopActivity = localArgInput;
}

// Root inport: '<Root>/In_HaveEverActivated' set method
void ViewModeStateFlowStateMachineModelClass::setIn_HaveEverActivated(boolean_T
  localArgInput)
{
  rtU.In_HaveEverActivated = localArgInput;
}

// Root inport: '<Root>/In_FloatViewChange' set method
void ViewModeStateFlowStateMachineModelClass::setIn_FloatViewChange
  (EFloatViewType localArgInput)
{
  rtU.In_FloatViewChange = localArgInput;
}

// Root inport: '<Root>/In_EnlargeButtonPressed' set method
void ViewModeStateFlowStateMachineModelClass::setIn_EnlargeButtonPressed
  (boolean_T localArgInput)
{
  rtU.In_EnlargeButtonPressed = localArgInput;
}

// Root inport: '<Root>/In_ParkingSearch' set method
void ViewModeStateFlowStateMachineModelClass::setIn_ParkingSearch(boolean_T
  localArgInput)
{
  rtU.In_ParkingSearch = localArgInput;
}

// Root inport: '<Root>/In_settingNarrowLaneActivate' set method
void ViewModeStateFlowStateMachineModelClass::setIn_settingNarrowLaneActivate
  (boolean_T localArgInput)
{
  rtU.In_settingNarrowLaneActivate = localArgInput;
}

// Root inport: '<Root>/in_isFreeParking' set method
void ViewModeStateFlowStateMachineModelClass::setin_isFreeParking(boolean_T
  localArgInput)
{
  rtU.in_isFreeParking = localArgInput;
}

// Root outport: '<Root>/Out_systemAvailable' get method
ESystem ViewModeStateFlowStateMachineModelClass::getOut_systemAvailable() const
{
  return rtY.Out_systemAvailable;
}

// Root outport: '<Root>/Out_systemActive' get method
boolean_T ViewModeStateFlowStateMachineModelClass::getOut_systemActive() const
{
  return rtY.Out_systemActive;
}

// Root outport: '<Root>/Out_displayedView' get method
EScreenID ViewModeStateFlowStateMachineModelClass::getOut_displayedView() const
{
  return rtY.Out_displayedView;
}

// Root outport: '<Root>/Out_SVSCurrentView' get method
EScreenID ViewModeStateFlowStateMachineModelClass::getOut_SVSCurrentView() const
{
  return rtY.Out_SVSCurrentView;
}

// Root outport: '<Root>/Out_SVSShowReq' get method
boolean_T ViewModeStateFlowStateMachineModelClass::getOut_SVSShowReq() const
{
  return rtY.Out_SVSShowReq;
}

// Root outport: '<Root>/Out_SVSViewModeSts' get method
ESVSViewMode ViewModeStateFlowStateMachineModelClass::getOut_SVSViewModeSts()
  const
{
  return rtY.Out_SVSViewModeSts;
}

// Root outport: '<Root>/Out_SVSUnavlMsgs' get method
ENotActiveReason ViewModeStateFlowStateMachineModelClass::getOut_SVSUnavlMsgs()
  const
{
  return rtY.Out_SVSUnavlMsgs;
}

// Root outport: '<Root>/Out_SVSFrViewSts' get method
uint8_T ViewModeStateFlowStateMachineModelClass::getOut_SVSFrViewSts() const
{
  return rtY.Out_SVSFrViewSts;
}

// Root outport: '<Root>/Out_SVSRiViewSts' get method
uint8_T ViewModeStateFlowStateMachineModelClass::getOut_SVSRiViewSts() const
{
  return rtY.Out_SVSRiViewSts;
}

// Root outport: '<Root>/Out_SVSReViewSts' get method
uint8_T ViewModeStateFlowStateMachineModelClass::getOut_SVSReViewSts() const
{
  return rtY.Out_SVSReViewSts;
}

// Root outport: '<Root>/Out_SVSLeViewSts' get method
uint8_T ViewModeStateFlowStateMachineModelClass::getOut_SVSLeViewSts() const
{
  return rtY.Out_SVSLeViewSts;
}

// Root outport: '<Root>/Out_isFreeMode' get method
boolean_T ViewModeStateFlowStateMachineModelClass::getOut_isFreeMode() const
{
  return rtY.Out_isFreeMode;
}

// Root outport: '<Root>/Out_isFreeModeAct' get method
boolean_T ViewModeStateFlowStateMachineModelClass::getOut_isFreeModeAct() const
{
  return rtY.Out_isFreeModeAct;
}

// Root outport: '<Root>/Out_SVSScreenType' get method
ESVSScreenType ViewModeStateFlowStateMachineModelClass::getOut_SVSScreenType()
  const
{
  return rtY.Out_SVSScreenType;
}

// Root outport: '<Root>/Out_voiceDockFeedback' get method
EVoiceDockFb ViewModeStateFlowStateMachineModelClass::getOut_voiceDockFeedback()
  const
{
  return rtY.Out_voiceDockFeedback;
}

// Root outport: '<Root>/Out_HuCPCActive' get method
boolean_T ViewModeStateFlowStateMachineModelClass::getOut_HuCPCActive() const
{
  return rtY.Out_HuCPCActive;
}

// Root outport: '<Root>/Out_competeScreenTypeReq' get method
ESVSScreenType ViewModeStateFlowStateMachineModelClass::
  getOut_competeScreenTypeReq() const
{
  return rtY.Out_competeScreenTypeReq;
}

// Root outport: '<Root>/Out_competeReqStatus' get method
ECompeteReqStatus ViewModeStateFlowStateMachineModelClass::
  getOut_competeReqStatus() const
{
  return rtY.Out_competeReqStatus;
}

// Root outport: '<Root>/Out_ViewModeGroup' get method
EViewModeGroup ViewModeStateFlowStateMachineModelClass::getOut_ViewModeGroup()
  const
{
  return rtY.Out_ViewModeGroup;
}

// Root outport: '<Root>/Out_ShowReqMod' get method
EShowReqMode ViewModeStateFlowStateMachineModelClass::getOut_ShowReqMod() const
{
  return rtY.Out_ShowReqMod;
}

// Constructor
ViewModeStateFlowStateMachineModelClass::ViewModeStateFlowStateMachineModelClass
  () :
  rtU(),
  rtY(),
  rtDWork(),
  rtM()
{
  // Currently there is no constructor body generated.
}

// Destructor
ViewModeStateFlowStateMachineModelClass::
  ~ViewModeStateFlowStateMachineModelClass()
{
  // Currently there is no destructor body generated.
}

// Real-Time Model get method
RT_MODEL * ViewModeStateFlowStateMachineModelClass::getRTM()
{
  return (&rtM);
}

//
// File trailer for generated code.
//
// [EOF]
//
