//
// File: ViewModexViewStateMachine_R2015b_private.h
//
// Code generated for Simulink model 'ViewModexViewStateMachine_R2015b'.
//
// Model version                  : 11.419
// Simulink Coder version         : 9.6 (R2021b) 14-May-2021
// C/C++ source code generated on : Fri Aug  1 18:05:47 2025
//
// Target selection: ert.tlc
// Embedded hardware selection: ARM Compatible->ARM Cortex
// Code generation objectives:
//    1. Execution efficiency
//    2. RAM efficiency
//    3. ROM efficiency
//    4. MISRA C:2012 guidelines
//    5. Debugging
//    6. Safety precaution
// Validation result: Passed (29), Warnings (4), Error (0)
//
#ifndef RTW_HEADER_ViewModexViewStateMachine_R2015b_private_h_
#define RTW_HEADER_ViewModexViewStateMachine_R2015b_private_h_
#include "rtwtypes.h"

extern real_T rt_roundd(real_T u);

#endif                // RTW_HEADER_ViewModexViewStateMachine_R2015b_private_h_

//
// File trailer for generated code.
//
// [EOF]
//
