//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityCommand.cpp
/// @brief
//=============================================================================

#include "pc/generic/util/cli/inc/CommandCallback.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "cc/target/common/inc/valin_types_api.hpp"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include "vfc/core/vfc_types.hpp"
/// @deviation NRCS2_076
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace virtualreality
{

struct ParkSlotCommand_st
{
    vfc::uint32_t  m_parkSlotId;                //Parking number
    vfc::uint32_t  m_parkSlotAvailableStatus;   //occupied, available but not selectable, selectable, selected... default = NONE
    vfc::uint32_t  m_parkSlotOrientationType;   //vertical, parallel, diagonal... default = NONE
    vfc::uint32_t  m_parkSlotEntryType;         //frontin, rearin ... default = NONE
    vfc::int32_t  m_parkSlotPosition_rearAxelCenter_x;
    vfc::int32_t  m_parkSlotPosition_rearAxelCenter_y;
    vfc::int32_t  m_parkSlotPosition_rearAxelCenter_phi;
    vfc::int32_t  m_parkSlotPosition_corner1_x;
    vfc::int32_t  m_parkSlotPosition_corner1_y;
    vfc::int32_t  m_parkSlotPosition_corner1_phi;
    vfc::int32_t  m_parkSlotPosition_corner2_x;
    vfc::int32_t  m_parkSlotPosition_corner2_y;
    vfc::int32_t  m_parkSlotPosition_corner2_phi;
};

//!
//! Set parking slots Command
//!
class SetParkingSlotsCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    ParkSlotCommand_st l_parkSlot[cc::target::common::g_parkSlotCommandQuantity];

    for (vfc::int32_t i = 0; i < cc::target::common::g_parkSlotCommandQuantity; i++)
    {
      f_input >> l_parkSlot[i].m_parkSlotId
              >> l_parkSlot[i].m_parkSlotAvailableStatus
              >> l_parkSlot[i].m_parkSlotOrientationType
              >> l_parkSlot[i].m_parkSlotEntryType
              >> l_parkSlot[i].m_parkSlotPosition_rearAxelCenter_x
              >> l_parkSlot[i].m_parkSlotPosition_rearAxelCenter_y
              >> l_parkSlot[i].m_parkSlotPosition_rearAxelCenter_phi
              >> l_parkSlot[i].m_parkSlotPosition_corner1_x
              >> l_parkSlot[i].m_parkSlotPosition_corner1_y
              >> l_parkSlot[i].m_parkSlotPosition_corner1_phi
              >> l_parkSlot[i].m_parkSlotPosition_corner2_x
              >> l_parkSlot[i].m_parkSlotPosition_corner2_y
              >> l_parkSlot[i].m_parkSlotPosition_corner2_phi
              ; // PRQA S 3803
    }

    if (f_input.fail())
    {
      f_output << parseError;    // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkSlotDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkSlotDaddy_SenderPort.reserve();

    for (vfc::int32_t i = 0; i < cc::target::common::g_parkSlotCommandQuantity; i++)
    {
      l_container.m_Data[i].m_parkSlotId              = static_cast<vfc::uint16_t           > (l_parkSlot[i].m_parkSlotId             );  // PRQA S 3013
      l_container.m_Data[i].m_parkSlotAvailableStatus = static_cast<cc::target::common::EParkSlotAvailableStatus> (l_parkSlot[i].m_parkSlotAvailableStatus);  // PRQA S 3013 // PRQA S 4899
      l_container.m_Data[i].m_parkSlotOrientationType = static_cast<cc::target::common::EParkSlotOrientationType> (l_parkSlot[i].m_parkSlotOrientationType);  // PRQA S 3013 // PRQA S 4899
      l_container.m_Data[i].m_parkSlotEntryType       = static_cast<cc::target::common::EParkSlotEntryType      > (l_parkSlot[i].m_parkSlotEntryType      );  // PRQA S 3013 // PRQA S 4899

      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_x   = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_rearAxelCenter_x) / 1000.0f * 1024.0f);  // PRQA S 3013 // PRQA S 3016
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_y   = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_rearAxelCenter_y) / 1000.0f * 1024.0f);  // PRQA S 3013 // PRQA S 3016
      l_container.m_Data[i].m_parkSlotPosition.m_slotRearAxelCenter.m_phi = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_rearAxelCenter_phi) / 180.0f * static_cast<vfc::float32_t> (osg::PI) * 4048.0f);  // PRQA S 3013 // PRQA S 3016

      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner1.m_x   = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_corner1_x) / 1000.0f * 1024.0f);  // PRQA S 3013 // PRQA S 3016
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner1.m_y   = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_corner1_y) / 1000.0f * 1024.0f);  // PRQA S 3013 // PRQA S 3016
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner1.m_phi = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_corner1_phi) / 180.0f * static_cast<vfc::float32_t> (osg::PI) * 4048.0f);  // PRQA S 3013 // PRQA S 3016

      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner2.m_x   = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_corner2_x) / 1000.0f * 1024.0f);  // PRQA S 3013 // PRQA S 3016
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner2.m_y   = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_corner2_y) / 1000.0f * 1024.0f);  // PRQA S 3013 // PRQA S 3016
      l_container.m_Data[i].m_parkSlotPosition.m_slotCorner2.m_phi = static_cast<vfc::int16_t> (static_cast<vfc::float32_t>(l_parkSlot[i].m_parkSlotPosition_corner2_phi) / 180.0f * static_cast<vfc::float32_t> (osg::PI) * 4048.0f);  // PRQA S 3013 // PRQA S 3016
    }

    cc::daddy::CustomDaddyPorts::sm_ParkSlotDaddy_SenderPort.deliver();
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set parking slots: " << newline;    // PRQA S 3803
  }
};

SetParkingSlotsCommand g_setParkingSlotsCommand;

struct PedestrianCommand_st
{
    //Receive Fusion info
    vfc::uint32_t  m_objectId;                //object id
    vfc::int32_t  m_objectPosition_x;
    vfc::int32_t  m_objectPosition_y;
    vfc::int32_t  m_objectPosition_cov_x;
    vfc::int32_t  m_objectPosition_cov_y;
    vfc::int32_t  m_objectPosition_cov_z;
    vfc::int32_t  m_objectVelocity_x;
    vfc::int32_t  m_objectVelocity_y;
    vfc::int32_t  m_objectVelocity_cov_x;
    vfc::int32_t  m_objectVelocity_cov_y;
    vfc::int32_t  m_objectVelocity_cov_z;
    vfc::int32_t  m_confidenceObject;
    vfc::int32_t  m_confidencePedestrian;
    vfc::int32_t  m_confidenceDynamic;

    //Receive Sitocp info
    //unsigned int m_criticalObjectId;
};

//!
//! Set parking slots Command
//!
class SetVirtualPedestrianCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    std::array<PedestrianCommand_st,g_pedestrianDisplayQuantity> l_object;
    vfc::uint32_t l_criticalObjectId = 0;

    for (vfc::int32_t i = 0; i < g_pedestrianDisplayQuantity; i++)
    {
      f_input >> l_object[i].m_objectId // PRQA S 3000
              >> l_object[i].m_objectPosition_x // PRQA S 3000
              >> l_object[i].m_objectPosition_y // PRQA S 3000
              // >> l_object[i].m_objectPosition_cov_x
              // >> l_object[i].m_objectPosition_cov_y
              // >> l_object[i].m_objectPosition_cov_z
              >> l_object[i].m_objectVelocity_x // PRQA S 3000
              >> l_object[i].m_objectVelocity_y // PRQA S 3000
              // >> l_object[i].m_objectVelocity_cov_x
              // >> l_object[i].m_objectVelocity_cov_y
              // >> l_object[i].m_objectVelocity_cov_z
              // >> l_object[i].m_confidenceObject
              // >> l_object[i].m_confidencePedestrian
              // >> l_object[i].m_confidenceDynamic

              //>> l_object[i].m_criticalObjectId
              ; // PRQA S 3803
    }

    f_input >>  l_criticalObjectId;

    if (f_input.fail())
    {
      f_output << parseError;    // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::FusionObjectDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_fusionObject_SenderPort.reserve();
    cc::daddy::SitOcpDaddy_t& l_sitOcpContainer = cc::daddy::CustomDaddyPorts::sm_sitOcp_SenderPort.reserve();
    for (vfc::int32_t i = 0; i < g_pedestrianDisplayQuantity; i++)
    {

      l_container.m_Data.m_fusionObject[i].m_objectID     = static_cast<vfc::uint32_t> (l_object[i].m_objectId); // PRQA S 3000
      l_container.m_Data.m_fusionObject[i].m_position.m_x = static_cast<vfc::CSI::si_metre_f32_t> (l_object[i].m_objectPosition_x); // PRQA S 3000 // PRQA S 3011
      l_container.m_Data.m_fusionObject[i].m_position.m_y = static_cast<vfc::CSI::si_metre_f32_t> (l_object[i].m_objectPosition_y); // PRQA S 3000 // PRQA S 3011

      // //l_container.m_Data.m_fusionObject[i].m_positionCovariance    = static_cast<vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_f32_t>> ( l_object[i].m_objectPosition_cov_x, l_object[i].m_objectPosition_cov_y, l_object[i].m_objectPosition_cov_y, l_object[i].m_objectPosition_cov_z );
      // l_container.m_Data.m_fusionObject[i].m_positionCovariance    = static_cast<vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_f32_t>> (static_cast<vfc::CSI::si_square_metre_f32_t>(l_object[i].m_objectPosition_cov_x), static_cast<vfc::CSI::si_square_metre_f32_t>(l_object[i].m_objectPosition_cov_y), static_cast<vfc::CSI::si_square_metre_f32_t>(l_object[i].m_objectPosition_cov_y), static_cast<vfc::CSI::si_square_metre_f32_t>(l_object[i].m_objectPosition_cov_z));
      l_container.m_Data.m_fusionObject[i].m_positionCovariance    = static_cast<vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_f32_t>> (static_cast<vfc::CSI::si_square_metre_f32_t>(0), static_cast<vfc::CSI::si_square_metre_f32_t>(0), static_cast<vfc::CSI::si_square_metre_f32_t>(0), static_cast<vfc::CSI::si_square_metre_f32_t>(0)); // PRQA S 3342

      l_container.m_Data.m_fusionObject[i].m_velocity.m_x_v   = static_cast<vfc::CSI::si_metre_per_second_f32_t> (l_object[i].m_objectVelocity_x); // PRQA S 3000 // PRQA S 3011
      l_container.m_Data.m_fusionObject[i].m_velocity.m_y_v   = static_cast<vfc::CSI::si_metre_per_second_f32_t> (l_object[i].m_objectVelocity_y); // PRQA S 3000 // PRQA S 3011
      // l_container.m_Data.m_fusionObject[i].m_velocityCovariance    = static_cast<vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_per_square_second_f32_t>> (static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(l_object[i].m_objectVelocity_cov_x), static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(l_object[i].m_objectVelocity_cov_y), static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(l_object[i].m_objectVelocity_cov_y), static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(l_object[i].m_objectVelocity_cov_z ));
      l_container.m_Data.m_fusionObject[i].m_velocityCovariance    = static_cast<vfc::linalg::TMatrix22<vfc::CSI::si_square_metre_per_square_second_f32_t>> (static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(0), static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(0), static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(0), static_cast<vfc::CSI::si_square_metre_per_square_second_f32_t>(0)); // PRQA S 3342

      // l_container.m_Data.m_fusionObject[i].m_confidenceObject   = static_cast<vfc::CSI::si_percent_f32_t> (l_object[i].m_confidenceObject);
      // l_container.m_Data.m_fusionObject[i].m_confidencePedestrian  = static_cast<vfc::CSI::si_percent_f32_t> (l_object[i].m_confidencePedestrian);
      // l_container.m_Data.m_fusionObject[i].m_confidenceDynamic = static_cast<vfc::CSI::si_percent_f32_t> (l_object[i].m_confidenceDynamic);

      l_container.m_Data.m_fusionObject[i].m_confidenceObject   = static_cast<vfc::CSI::si_percent_f32_t>(100.0);
      l_container.m_Data.m_fusionObject[i].m_confidencePedestrian  = static_cast<vfc::CSI::si_percent_f32_t>(100.0);
      l_container.m_Data.m_fusionObject[i].m_confidenceDynamic = static_cast<vfc::CSI::si_percent_f32_t> (0);

      //l_sitOcpContainer.m_Data.m_objectID = static_cast<vfc::uint32_t> (l_object[i].m_criticalObjectId);
    }

    l_sitOcpContainer.m_Data.m_objectID = l_criticalObjectId;

    cc::daddy::CustomDaddyPorts::sm_fusionObject_SenderPort.deliver();
    cc::daddy::CustomDaddyPorts::sm_sitOcp_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set pedestrian info: " << newline;
  }
};

// example: vr pedestrian 0 3 5 6 3   1 -2 2 6 1   2 6 0 6 6   3 0 -5 6 0   4 -6 -2 6 5   2
SetVirtualPedestrianCommand g_setPedestrianCommand;


//!
//! VirtualRealityCommand
//!
class VirtualRealityCommand : public pc::util::cli::CommandCallbackGroup
{
public:

  VirtualRealityCommand() // PRQA S 4054
  {
    addCommand("parkslot",       &g_setParkingSlotsCommand);
    addCommand("pedestrian",     &g_setPedestrianCommand);
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Parking slots simulation, etc." << newline;  // PRQA S 3803
  }
};

pc::util::cli::Command<VirtualRealityCommand> g_virtualRealityCommand("vr");

} // namespace virtualreality
} // namespace assets
} // namespace cc // PRQA S 1041
