//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_NODE_H
#define CC_ASSETS_FREEPARKINGOVERLAY_NODE_H

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

class FreeparkingOverlay;

//!
//! FreeparkingNode
//!
class FreeparkingNode
{
public:
    virtual ~FreeparkingNode()
    {
    }
    virtual void update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft) = 0;
};

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_NODE_H
