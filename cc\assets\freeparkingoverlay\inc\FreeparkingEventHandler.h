/// @copyright (C) 2023 Robert <PERSON>.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_EVENTHANDLER_H
#define CC_ASSETS_FREEPARKINGOVERLAY_EVENTHANDLER_H

#include "cc/util/touchmanipulator/inc/TouchEventHandler.h"
namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

class FreeparkingManager;

class FreeparkingEventHandler : public cc::util::touchmanipulator::TouchEventHandler
{
public:
    FreeparkingEventHandler();

    bool handle(
        const osgGA::GUIEventAdapter& f_ea,
        osgGA::GUIActionAdapter&      f_aa,
        osg::Object*                  f_obj,
        osg::NodeVisitor*             f_nv) override;

private:
    bool handleEvents(const osgGA::GUIEventAdapter& f_ea, FreeparkingManager* f_freeparkingManager);

private:
    bool m_touchInsideViewport = false;
};


} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_EVENTHANDLER_H
