//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: KVT2BP Kovacs Tibor (CC-DA/EAV1-Bp)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  UssZoneAdaptation.cpp
/// @brief 
//=============================================================================
#include "cc/assets/splineoverlay/inc/UssZoneAdaptation.h"
#include "pc/svs/util/math/inc/FloatComp.h" 
#include "vfc/core/vfc_types.hpp"
#include <limits>
#include <algorithm>

namespace cc
{
namespace assets
{
namespace splineoverlay
{

UssZoneAdaptation::UssZoneAdaptation()
  : m_isScaleNeeded{true}
  , m_carGeometryCenter{}
{
  m_carBoundingRectangle.left  = std::numeric_limits<vfc::float32_t>::max();
  m_carBoundingRectangle.right = std::numeric_limits<vfc::float32_t>::min();
  m_carBoundingRectangle.rear  = std::numeric_limits<vfc::float32_t>::max();
  m_carBoundingRectangle.front = std::numeric_limits<vfc::float32_t>::min();

  m_ussBoundingRectangle.left  = std::numeric_limits<vfc::float32_t>::max();
  m_ussBoundingRectangle.right = std::numeric_limits<vfc::float32_t>::min();
  m_ussBoundingRectangle.rear  = std::numeric_limits<vfc::float32_t>::max();
  m_ussBoundingRectangle.front = std::numeric_limits<vfc::float32_t>::min();
}

 void UssZoneAdaptation::carBoundingRect(const osg::Vec2f& f_Point)
 {
  m_carBoundingRectangle.front =  std::max(m_carBoundingRectangle.front, f_Point.x());
  m_carBoundingRectangle.rear  =  std::min(m_carBoundingRectangle.rear,  f_Point.x());
  m_carBoundingRectangle.right =  std::max(m_carBoundingRectangle.right, f_Point.y());
  m_carBoundingRectangle.left  = -m_carBoundingRectangle.right;
 }

 void UssZoneAdaptation::insertToUssBoundingRect(const osg::Vec2f& f_Point)
{
  m_ussBoundingRectangle.front = std::max(m_ussBoundingRectangle.front, f_Point.x());
  m_ussBoundingRectangle.rear  = std::min(m_ussBoundingRectangle.rear,  f_Point.x());
  m_ussBoundingRectangle.left  = std::min(m_ussBoundingRectangle.left,  f_Point.y());
  m_ussBoundingRectangle.right = std::max(m_ussBoundingRectangle.right, f_Point.y());
}

void UssZoneAdaptation::closeUssBoundingRect()
{
  m_ussBoundingRectangle.left = -m_ussBoundingRectangle.right;

  m_carGeometryCenter.x() = (m_ussBoundingRectangle.front + m_ussBoundingRectangle.rear)  / 2.0f;
  m_carGeometryCenter.y() = (m_ussBoundingRectangle.left  + m_ussBoundingRectangle.right) / 2.0f;
}

osg::Vec4f UssZoneAdaptation::scaleUssZone(const osg::Vec4f& f_ZoneLine) // PRQA S 4211
{
  if (!m_isScaleNeeded) 
  {
    return f_ZoneLine;
  }

  osg::Vec2f newPoint;

  // Scaling algorithm
  newPoint.x() = (f_ZoneLine.x() - m_ussBoundingRectangle.rear) * ((m_carBoundingRectangle.front - m_carBoundingRectangle.rear) / (m_ussBoundingRectangle.front - m_ussBoundingRectangle.rear)) + m_carBoundingRectangle.rear;
  newPoint.y() = (f_ZoneLine.y() - m_ussBoundingRectangle.left) * ((m_carBoundingRectangle.right - m_carBoundingRectangle.left) / (m_ussBoundingRectangle.right - m_ussBoundingRectangle.left)) + m_carBoundingRectangle.left;

  osg::Vec2f pointDiff(newPoint.x() - f_ZoneLine.x(), newPoint.y() - f_ZoneLine.y());

  osg::Vec4f result;

  // Begin of the line
  result.x() = newPoint.x();
  result.y() = newPoint.y();

  // End of the line
  result.z() = f_ZoneLine.z() + pointDiff.x();
  result.w() = f_ZoneLine.w() + pointDiff.y();

  return result;
}

// Compute the intersection between the USS zone and the car contour segment. If there is an intersection align the USS zone line to the car contour.
void UssZoneAdaptation::moveUssZoneToCarContour(osg::Vec4f& f_ZoneLine, const std::vector<vfc::float32_t>& f_ContourChunkX, const std::vector<vfc::float32_t>& f_ContourChunkY)
{
  for (size_t i = 1u; i < f_ContourChunkX.size(); ++i)
  {
    const osg::Vec4f carContourSegment(f_ContourChunkX[i], f_ContourChunkY[i], f_ContourChunkX[i - 1u], f_ContourChunkY[i - 1u]);
    bool isIntersected = false;
    osg::Vec2f intSec = intersection(f_ZoneLine, carContourSegment, &isIntersected);

    if (!isIntersected)
    {
      continue;
    }
    osg::Vec2f pointDiff(intSec.x() - f_ZoneLine.x(), intSec.y() - f_ZoneLine.y());

    // Begin of the line
    f_ZoneLine.x() = intSec.x();
    f_ZoneLine.y() = intSec.y();

    // End of the line
    f_ZoneLine.z() += pointDiff.x();
    f_ZoneLine.w() += pointDiff.y();

    break;
  }
}

// For optimization purposes the algorithm first checks if the USS zone line and the car contour segment are in the same domain.
bool UssZoneAdaptation::shiftUssLineToTheCarContour(const osg::Vec4f& f_CarContourLine, osg::Vec4f& f_UssZoneLine)
{
  const osg::Vec2f l_UssZoneLineBegin(f_UssZoneLine.x(), f_UssZoneLine.y());
  const osg::Vec2f l_CarContourLineBegin(f_CarContourLine.x(), f_CarContourLine.y());
  const osg::Vec2f l_CarContourLineEnd(f_CarContourLine.z(), f_CarContourLine.w());
  if (!isInTheSameDomain(l_UssZoneLineBegin, l_CarContourLineBegin))
  {
    if (!isInTheSameDomain(l_UssZoneLineBegin, l_CarContourLineEnd))
    {
      return false;
    }
  }

  bool isIntersected = false;
  osg::Vec2f intersect = intersection(f_UssZoneLine, f_CarContourLine, &isIntersected);

  if (!isIntersected)
  {
    return false;
  }

  osg::Vec2f pointDiff(intersect.x() - f_UssZoneLine.x(), intersect.y() - f_UssZoneLine.y());

  // Begin of the line
  f_UssZoneLine.x() = intersect.x();
  f_UssZoneLine.y() = intersect.y();

  // End of the line
  f_UssZoneLine.z() += pointDiff.x();
  f_UssZoneLine.w() += pointDiff.y();

  return true;
}

bool UssZoneAdaptation::isInTheSameDomain(const osg::Vec2f& inPoint1, const osg::Vec2f& inPoint2)
{
  const vfc::int32_t domain1x = inPoint1.x() > m_carGeometryCenter.x() ? 1 : -1;
  const vfc::int32_t domain1y = inPoint1.y() > m_carGeometryCenter.y() ? 1 : -1;
  const vfc::int32_t domain2x = inPoint2.x() > m_carGeometryCenter.x() ? 1 : -1;
  const vfc::int32_t domain2y = inPoint2.y() > m_carGeometryCenter.y() ? 1 : -1;

  return ((domain1x == domain2x) && (domain1y == domain2y));
}

// The intersection is only contsrained to the f_Segment, therefore the f_Line is more a line than a segment.
osg::Vec2f UssZoneAdaptation::intersection(const osg::Vec4f& f_Line, const osg::Vec4f& f_Segment, bool* f_IntersectedOut) // PRQA S 4678 // PRQA S 4677
{
  if (f_IntersectedOut == nullptr)
  {
    return osg::Vec2f(0.0f, 0.0f);
  }
  osg::Vec2f res(0.0f, 0.0f);
  *f_IntersectedOut = false;

  const vfc::float32_t x1 = f_Line.x();
  const vfc::float32_t x2 = f_Line.z();
  const vfc::float32_t x3 = f_Segment.x();
  const vfc::float32_t x4 = f_Segment.z();
  const vfc::float32_t y1 = f_Line.y();    // PRQA S 2504
  const vfc::float32_t y2 = f_Line.w();
  const vfc::float32_t y3 = f_Segment.y();
  const vfc::float32_t y4 = f_Segment.w();

  const vfc::float32_t d = (x1 - x2) * (y3 - y4) - (y1 - y2) * (x3 - x4);
  if (isZero(d)) 
  {
    return res;
  }
  // Get the x and y
  const vfc::float32_t pre = (x1*y2 - y1*x2), post = (x3*y4 - y3*x4);
  const vfc::float32_t x = (pre * (x3 - x4) - (x1 - x2) * post) / d;
  const vfc::float32_t y = (pre * (y3 - y4) - (y1 - y2) * post) / d;

  constexpr vfc::float32_t eps = std::numeric_limits<vfc::float32_t>::epsilon() * 5.0f;

  const vfc::float32_t minX = std::min(x3, x4) - eps;
  const vfc::float32_t maxX = std::max(x3, x4) + eps;
  const vfc::float32_t minY = std::min(y3, y4) - eps;
  const vfc::float32_t maxY = std::max(y3, y4) + eps;

  if (x < minX)
  {
    return res;
  }
  if (x > maxX)
  {
    return res;
  }
  if (y < minY)
  {
    return res;
  }
  if (y > maxY)
  {
    return res;
  }
  
  res.x() = x;
  res.y() = y;

  *f_IntersectedOut = true;
  return res;
}

} // namespace splineoverlay
} // namespace assets
} // namespace cc 

