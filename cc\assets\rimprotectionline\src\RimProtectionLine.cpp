//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/rimprotectionline/inc/RimProtectionLine.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include "osgDB/ReadFile"
#include "osg/Geometry"
#include "osg/State"
#include "osg/Texture2D"

namespace cc
{
namespace assets
{
namespace rimline
{
pc::util::coding::Item<RimLineSettings> g_rimLineSettings("RimLine");

RimProtectionLine::RimProtectionLine(ERimPlaneView f_view)
    : osg::Group{}
    , m_crossPoint{}
    , m_rimPlaneView{f_view}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

    initCrossPoint();
    osg::Geometry* const l_prallelLineGeometry = new osg::Geometry;

    l_prallelLineGeometry->setUseDisplayList(false);
    l_prallelLineGeometry->setUseVertexBufferObjects(true);

    osg::Vec3Array* const l_prallelLineVertices = new osg::Vec3Array(c_pointSum * 2);
    updateLineVertices(l_prallelLineVertices);
    l_prallelLineGeometry->setVertexArray(l_prallelLineVertices);

    osg::Vec4Array* const l_prallelLineColorArray = new osg::Vec4Array(1);
    (*l_prallelLineColorArray)[0]           = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_prallelLineGeometry->setColorArray(l_prallelLineColorArray, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices =
        new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, c_triangleSum * 2); // PRQA S 3143
    (*l_indices)[0u]  = 1u;
    (*l_indices)[1u]  = 0u;
    (*l_indices)[2u]  = 2u;
    (*l_indices)[3u]  = 1u;
    (*l_indices)[4u]  = 3u;
    (*l_indices)[5u]  = 2u;
    (*l_indices)[6u]  = 3u;
    (*l_indices)[7u]  = 4u;
    (*l_indices)[8u]  = 5u;
    (*l_indices)[9u]  = 3u;
    (*l_indices)[10u] = 5u;
    (*l_indices)[11u] = 6u;
    (*l_indices)[12u] = 6u;
    (*l_indices)[13u] = 7u;
    (*l_indices)[14u] = 8u;
    (*l_indices)[15u] = 6u;
    (*l_indices)[16u] = 8u;
    (*l_indices)[17u] = 9u;
    (*l_indices)[18u] = 9u;
    (*l_indices)[19u] = 10u;
    (*l_indices)[20u] = 11u;
    (*l_indices)[21u] = 2u;
    (*l_indices)[22u] = 9u;
    (*l_indices)[23u] = 11u;
    (*l_indices)[24u] = 2u;
    (*l_indices)[25u] = 9u;
    (*l_indices)[26u] = 6u;
    (*l_indices)[27u] = 2u;
    (*l_indices)[28u] = 3u;
    (*l_indices)[29u] = 6u;

    (*l_indices)[0u + c_triangleSum]  = 1u + c_pointSum;
    (*l_indices)[1u + c_triangleSum]  = 0u + c_pointSum;
    (*l_indices)[2u + c_triangleSum]  = 2u + c_pointSum;
    (*l_indices)[3u + c_triangleSum]  = 1u + c_pointSum;
    (*l_indices)[4u + c_triangleSum]  = 3u + c_pointSum;
    (*l_indices)[5u + c_triangleSum]  = 2u + c_pointSum;
    (*l_indices)[6u + c_triangleSum]  = 3u + c_pointSum;
    (*l_indices)[7u + c_triangleSum]  = 4u + c_pointSum;
    (*l_indices)[8u + c_triangleSum]  = 5u + c_pointSum;
    (*l_indices)[9u + c_triangleSum]  = 3u + c_pointSum;
    (*l_indices)[10u + c_triangleSum] = 5u + c_pointSum;
    (*l_indices)[11u + c_triangleSum] = 6u + c_pointSum;
    (*l_indices)[12u + c_triangleSum] = 6u + c_pointSum;
    (*l_indices)[13u + c_triangleSum] = 7u + c_pointSum;
    (*l_indices)[14u + c_triangleSum] = 8u + c_pointSum;
    (*l_indices)[15u + c_triangleSum] = 6u + c_pointSum;
    (*l_indices)[16u + c_triangleSum] = 8u + c_pointSum;
    (*l_indices)[17u + c_triangleSum] = 9u + c_pointSum;
    (*l_indices)[18u + c_triangleSum] = 9u + c_pointSum;
    (*l_indices)[19u + c_triangleSum] = 10u + c_pointSum;
    (*l_indices)[20u + c_triangleSum] = 11u + c_pointSum;
    (*l_indices)[21u + c_triangleSum] = 2u + c_pointSum;
    (*l_indices)[22u + c_triangleSum] = 9u + c_pointSum;
    (*l_indices)[23u + c_triangleSum] = 11u + c_pointSum;
    (*l_indices)[24u + c_triangleSum] = 2u + c_pointSum;
    (*l_indices)[25u + c_triangleSum] = 9u + c_pointSum;
    (*l_indices)[26u + c_triangleSum] = 6u + c_pointSum;
    (*l_indices)[27u + c_triangleSum] = 2u + c_pointSum;
    (*l_indices)[28u + c_triangleSum] = 3u + c_pointSum;
    (*l_indices)[29u + c_triangleSum] = 6u + c_pointSum;

    l_prallelLineGeometry->addPrimitiveSet(l_indices); // PRQA S 3803

    const osg::observer_ptr<osg::Geode> l_fpPlaneGeode = new osg::Geode;
    l_fpPlaneGeode->addDrawable(l_prallelLineGeometry); // PRQA S 3803

    osg::Group::addChild(l_fpPlaneGeode.get()); // PRQA S 3803
}

void RimProtectionLine::updateLineVertices(osg::Vec3Array* f_vertices)
{
    //Front/Rear Left Wheel
    (*f_vertices)[0u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f :  m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() + g_rimLineSettings->m_verticalLineLengthLeftward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[1u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() + g_rimLineSettings->m_verticalLineLengthLeftward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[2u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[3u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[4u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthDownward : m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthDownward,
        m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[5u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthDownward : m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthDownward,
        m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[6u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[7u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() - g_rimLineSettings->m_verticalLineLengthRightward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[8u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f :  m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() - g_rimLineSettings->m_verticalLineLengthRightward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[9u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[10u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthUpward : m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthUpward,
        m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[11u] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthUpward : m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthUpward,
        m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);

    //Front Right Wheel
    (*f_vertices)[0u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() - g_rimLineSettings->m_verticalLineLengthLeftward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[1u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() - g_rimLineSettings->m_verticalLineLengthLeftward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[2u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[3u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[4u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthDownward : m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthDownward,
        - m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[5u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthDownward : m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthDownward,
        - m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[6u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[7u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() + g_rimLineSettings->m_verticalLineLengthRightward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[8u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() + g_rimLineSettings->m_verticalLineLengthRightward,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[9u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_lineWidth * 0.5f : m_crossPoint.x() - g_rimLineSettings->m_lineWidth * 0.5f,
        - m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[10u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthUpward : m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthUpward,
        - m_crossPoint.y() + g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);
    (*f_vertices)[11u + c_pointSum] = osg::Vec3(
        m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_FRONT? m_crossPoint.x() + g_rimLineSettings->m_prallelLineLengthUpward : m_crossPoint.x() - g_rimLineSettings->m_prallelLineLengthUpward,
        - m_crossPoint.y() - g_rimLineSettings->m_lineWidth * 0.5f,
        g_rimLineSettings->m_groundLevel);

}

void RimProtectionLine::initCrossPoint()
{
    if (m_rimPlaneView == ERimPlaneView::RIMPLANEVIEW_REAR)
    {
        // refer to left wheel
        m_crossPoint = osg::Vec3f(
        - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear - g_rimLineSettings->m_distanceToVehicleBumper,
        - pc::vehicle::g_mechanicalData->m_width * 0.5f - g_rimLineSettings->m_distanceToVehicleSide ,
        g_rimLineSettings->m_groundLevel);
    }
    else
    {
        // front wheel
        // refer to left wheel
        m_crossPoint = osg::Vec3f(
            pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront + g_rimLineSettings->m_distanceToVehicleBumper +
                pc::vehicle::g_mechanicalData->m_wheelbase,
            - pc::vehicle::g_mechanicalData->m_width * 0.5f - g_rimLineSettings->m_distanceToVehicleSide ,
            g_rimLineSettings->m_groundLevel);
    }
}

void RimProtectionLine::update(ERimPlaneView /*f_view*/)
{
    osg::Geode*     const l_geode               = getChild(0u)->asGeode();
    osg::Geometry*  const l_prallelLineGeometry = l_geode->getDrawable(0u)->asGeometry();
    osg::Vec3Array* const l_prallelLineVertices =
        static_cast<osg::Vec3Array*>(l_prallelLineGeometry->getVertexArray()); // PRQA S 3076
    updateLineVertices(l_prallelLineVertices);                          // PRQA S 3076
    l_prallelLineVertices->dirty();
    l_prallelLineGeometry->dirtyBound();
}

void RimProtectionLine::traverse(osg::NodeVisitor& f_nv)
{
    if (g_rimLineSettings->getModifiedCount() != m_lastConfigUpdate)
        {
            m_lastConfigUpdate = g_rimLineSettings->getModifiedCount();
            initCrossPoint();
            update(m_rimPlaneView);
        }
    osg::Group::traverse(f_nv);
}

} // namespace rimline
} // namespace assets
} // namespace cc
