//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DistanceDigitalDisplay.cpp
/// @brief
//=============================================================================

#include "cc/assets/ptsoverlay/inc/PtsDistanceDigitalDisplay.h"
#include "vfc/core/vfc_types.hpp"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h" // PRQA S 1060
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/ptsoverlay/inc/PtsSettings.h"
#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include <string>
#include <algorithm>
using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace ptsdistancedigitaldisplay
{

pc::util::coding::Item<PtsDistanceDigitalDisplaySettings> g_ptsDisplaySettings("PtsDistanceDigitalDisplay");

inline osg::Texture2D* loadTexture(const std::string& f_filename)
{
  // static std::map< std::string, osg::ref_ptr<osg::Texture2D> > l_textureCache;
  // std::map< std::string, osg::ref_ptr<osg::Texture2D> >::const_iterator l_result = l_textureCache.find(f_filename);
  // if (l_result != l_textureCache.end())
  // {
  //   return l_result->second.get();
  // }
  const osg::ref_ptr<osg::Image> l_image = osgDB::readImageFile(f_filename);
  if (l_image.valid())
  {
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setResizeNonPowerOfTwoHint(true);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    // l_textureCache[f_filename] = l_texture;
    return l_texture;
  }
  else
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "DistanceDigitalDisplay::loadTexture(): Could not load " << f_filename); 
  }
  return nullptr;
}


//!
//! RctaOverlay
//!
DistanceDigitalDisplay::DistanceDigitalDisplay(pc::core::Framework* f_framework, pc::vehicle::AbstractZoneLayout* f_zoneLayout)
  : osg::MatrixTransform{}
  , m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_FrontSectorDisGeode{nullptr}
  , m_FrontSectorWarnImageGeode{nullptr}
  , m_RearSectorDisGeode{nullptr}
  , m_RearSectorWarnImageGeode{nullptr}
  , m_LeftSectorDisGeode{nullptr}
  , m_RightSectorDisGeode{nullptr}
  , m_FrontLeftSectorDisGeode{nullptr}
  , m_RearLeftSectorDisGeode{nullptr}
  , m_RearRightSectorDisGeode{nullptr}
  , m_FrontRightSectorDisGeode{nullptr}
  , m_screenID{EScreenID_SINGLE_FRONT_NORMAL}
  , m_stopTextFrontPositionShow{g_ptsDisplaySettings->m_stopTextFrontPosition}
  , m_stopTextRearPositionShow{g_ptsDisplaySettings->m_stopTextRearPosition}
  , m_zoneLayout{f_zoneLayout}
{
  setName("DistanceDigitalDisplay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


DistanceDigitalDisplay::~DistanceDigitalDisplay() = default;


void DistanceDigitalDisplay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (updateNeeded() || g_ptsDisplaySettings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      removeUpdateCallback(getUpdateCallback());
      addUpdateCallback(
        new DistanceDigitalDisplayUpdateCallback(
          m_FrontSectorDisGeode,
          m_RearSectorDisGeode,
          m_FrontSectorWarnImageGeode,
          m_RearSectorWarnImageGeode,
          m_LeftSectorDisGeode,
          m_RightSectorDisGeode,
          m_FrontLeftSectorDisGeode,
          m_RearLeftSectorDisGeode,
          m_RearRightSectorDisGeode,
          m_FrontRightSectorDisGeode,
          m_framework,
          m_zoneLayout
        )
      );
      m_settingsModifiedCount = g_ptsDisplaySettings->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}


void DistanceDigitalDisplay::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803
  m_FrontSectorDisGeode.release();    // PRQA S 3804 // PRQA S 3803
  m_RearSectorDisGeode.release();    // PRQA S 3804 // PRQA S 3803
  m_FrontSectorWarnImageGeode.release();    // PRQA S 3804 // PRQA S 3803
  m_RearSectorWarnImageGeode.release();    // PRQA S 3804 // PRQA S 3803

  //! FRONT SECTOR TEXT *********************************************************************
  {
    m_FrontSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_FrontShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_FrontShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_FrontShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_FrontShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_FrontShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_FrontShortestDis->setCharacterSize(30.0f);
    l_FrontShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_FrontShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_FrontShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_FrontSectorDisGeode->addDrawable(l_FrontShortestDis); // PRQA S 3803
    addChild(m_FrontSectorDisGeode);    // PRQA S 3803
  }

  //! REAR SECTOR TEXT **********************************************************************
  {
    m_RearSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_RearShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_RearShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_RearShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_RearShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_RearShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_RearShortestDis->setCharacterSize(30.0f);
    l_RearShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_RearShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_RearShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_RearSectorDisGeode->addDrawable(l_RearShortestDis); // PRQA S 3803
    addChild(m_RearSectorDisGeode);    // PRQA S 3803
  }

  //! LEFT SECTOR TEXT **********************************************************************
  {
    m_LeftSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_LeftShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_LeftShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_LeftShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_LeftShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_LeftShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_LeftShortestDis->setCharacterSize(30.0f);
    l_LeftShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_LeftShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_LeftShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_LeftSectorDisGeode->addDrawable(l_LeftShortestDis); // PRQA S 3803
    addChild(m_LeftSectorDisGeode);    // PRQA S 3803
  }

  //! RIGHT SECTOR TEXT **********************************************************************
  {
    m_RightSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_RightShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_RightShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_RightShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_RightShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_RightShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_RightShortestDis->setCharacterSize(30.0f);
    l_RightShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_RightShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_RightShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_RightSectorDisGeode->addDrawable(l_RightShortestDis); // PRQA S 3803
    addChild(m_RightSectorDisGeode);    // PRQA S 3803
  }

  //! FRONT LEFT SECTOR TEXT **********************************************************************
  {
    m_FrontLeftSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_FrontLeftShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_FrontLeftShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_FrontLeftShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_FrontLeftShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_FrontLeftShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_FrontLeftShortestDis->setCharacterSize(30.0f);
    l_FrontLeftShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_FrontLeftShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_FrontLeftShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_FrontLeftSectorDisGeode->addDrawable(l_FrontLeftShortestDis); // PRQA S 3803
    addChild(m_FrontLeftSectorDisGeode);    // PRQA S 3803
  }

  //! FRONT RIGHT SECTOR TEXT **********************************************************************
  {
    m_FrontRightSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_FrontRightShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_FrontRightShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_FrontRightShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_FrontRightShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_FrontRightShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_FrontRightShortestDis->setCharacterSize(30.0f);
    l_FrontRightShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_FrontRightShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_FrontRightShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_FrontRightSectorDisGeode->addDrawable(l_FrontRightShortestDis); // PRQA S 3803
    addChild(m_FrontRightSectorDisGeode);    // PRQA S 3803
  }

  //! REAR LEFT SECTOR TEXT **********************************************************************
  {
    m_RearLeftSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_RearLeftShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_RearLeftShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_RearLeftShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_RearLeftShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_RearLeftShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_RearLeftShortestDis->setCharacterSize(30.0f);
    l_RearLeftShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_RearLeftShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_RearLeftShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_RearLeftSectorDisGeode->addDrawable(l_RearLeftShortestDis); // PRQA S 3803
    addChild(m_RearLeftSectorDisGeode);    // PRQA S 3803
  }
  //! REAR RIGHT SECTOR TEXT **********************************************************************
  {
    m_RearRightSectorDisGeode = new osg::Geode;
    const osg::ref_ptr<osgText::Text> l_RearRightShortestDis = new osgText::Text;
    osg::StateSet* const l_textStateSet = l_RearRightShortestDis->getOrCreateStateSet();
    l_textStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    l_basicTexShader.apply(l_textStateSet);  // PRQA S 3803
    l_RearRightShortestDis->setFont(g_ptsDisplaySettings->m_fontType);
    l_RearRightShortestDis->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
    l_RearRightShortestDis->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
    l_RearRightShortestDis->setCharacterSize(30.0f);
    l_RearRightShortestDis->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
    l_RearRightShortestDis->setAxisAlignment(osgText::Text::XY_PLANE);
    l_RearRightShortestDis->setAlignment(osgText::Text::CENTER_TOP);
    m_RearRightSectorDisGeode->addDrawable(l_RearRightShortestDis); // PRQA S 3803
    addChild(m_RearRightSectorDisGeode);    // PRQA S 3803
  }


  //! FRONT SECTOR IMAGE
  {
    m_FrontSectorWarnImageGeode = new osg::Geode;
    osg::Texture2D* const l_stopTextTexture = loadTexture(g_ptsDisplaySettings->m_stopTextTexture);
    const osg::Image* const l_image = l_stopTextTexture->getImage();
    //assert(l_image);
    osg::Vec2i l_textureSize{l_image->s()/2, l_image->t()/2};
    const osg::Vec3f l_front  = osg::Vec3f(m_stopTextFrontPositionShow.x(), - static_cast<vfc::float32_t>(l_textureSize.x()) / 200.0f, 0.0f);
    const osg::Vec3f l_width  = osg::Vec3f(0.0f, static_cast<vfc::float32_t>(l_textureSize.x()) / 100.0f, 0.0f);
    const osg::Vec3f l_height = osg::Vec3f( static_cast<vfc::float32_t>(l_textureSize.y()) / 100.0f, 0.0f, 0.0f);
    osg::Geometry* const l_stopText = pc::util::osgx::createTexturePlane(l_front, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
    osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
    l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
    l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stopTextStateSet); // PRQA S 3803
    m_FrontSectorWarnImageGeode->addDrawable(l_stopText); // PRQA S 3803
    m_FrontSectorWarnImageGeode->setNodeMask(0u);
    addChild(m_FrontSectorWarnImageGeode); // PRQA S 3803
  }

  //! REAR SECTOR IMAGE
  {
    m_RearSectorWarnImageGeode = new osg::Geode;
    osg::Texture2D* const l_stopTextTexture = loadTexture(g_ptsDisplaySettings->m_stopTextTexture);
    const osg::Image* const l_image = l_stopTextTexture->getImage();
    //assert(l_image);
    osg::Vec2i l_textureSize{l_image->s()/2, l_image->t()/2};
    const osg::Vec3f l_rear  = osg::Vec3f(m_stopTextRearPositionShow.x(), - static_cast<vfc::float32_t>(l_textureSize.x()) / 200.0f, 0.0f);
    const osg::Vec3f l_width  = osg::Vec3f(0.0f, static_cast<vfc::float32_t>(l_textureSize.x()) / 100.0f, 0.0f);
    const osg::Vec3f l_height = osg::Vec3f(static_cast<vfc::float32_t>(l_textureSize.y()) / 100.0f, 0.0f, 0.0f);
    osg::Geometry* const l_stopText = pc::util::osgx::createTexturePlane(l_rear, l_width, l_height, 1, 1, 1.0f, 0.0f, 0.0f, 1.0f);
    osg::StateSet* const l_stopTextStateSet = l_stopText->getOrCreateStateSet();
    l_stopTextStateSet->setTextureAttribute(0u, l_stopTextTexture);
    l_stopTextStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    l_stopTextStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    l_stopTextStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stopTextStateSet); // PRQA S 3803
    m_RearSectorWarnImageGeode->addDrawable(l_stopText); // PRQA S 3803
    m_RearSectorWarnImageGeode->setNodeMask(0u);
    addChild(m_RearSectorWarnImageGeode); // PRQA S 3803
  }
}

bool DistanceDigitalDisplay::updateNeeded()
{
  bool l_isChanged = false;
  EScreenID l_currentScreenID = m_screenID;
  if (true == m_framework->asCustomFramework()->m_displayedView_ReceiverPort.isConnected())
  {
    const cc::daddy::SVSDisplayedViewDaddy_t* const l_pDataDaddy = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
    if (nullptr != l_pDataDaddy)
    {
      l_currentScreenID = l_pDataDaddy->m_Data;
    }
  }

  if (l_currentScreenID != m_screenID && l_currentScreenID == EScreenID_VERT_PARKING)  // change to vert parking view
  {
    m_stopTextFrontPositionShow = g_ptsDisplaySettings->m_stopTextFrontPosition_vert_parking;
    m_stopTextRearPositionShow = g_ptsDisplaySettings->m_stopTextRearPosition_vert_parking;
    l_isChanged = true;
  }
  else if (l_currentScreenID != m_screenID && m_screenID == EScreenID_VERT_PARKING)  // change from vert parking view
  {
    m_stopTextFrontPositionShow = g_ptsDisplaySettings->m_stopTextFrontPosition;
    m_stopTextRearPositionShow = g_ptsDisplaySettings->m_stopTextRearPosition;
    l_isChanged = true;
  }
  else
  {
    // do nothing
  }

  m_screenID = l_currentScreenID;

  return l_isChanged;
}

void DistanceDigitalDisplayUpdateCallback::initDigitalDisplayPosition()
{
  const pc::vehicle::LineData& l_leftBorder_front        = m_zoneLayout->getLeftBorderLine(0);
  const pc::vehicle::LineData& l_rightBorder_front       = m_zoneLayout->getRightBorderLine(39);
  const pc::vehicle::LineData& l_leftBorder_front_left   = m_zoneLayout->getLeftBorderLine(5);
  const pc::vehicle::LineData& l_rightBorder_front_left  = m_zoneLayout->getRightBorderLine(4);
  const pc::vehicle::LineData& l_leftBorder_left         = m_zoneLayout->getLeftBorderLine(10);
  const pc::vehicle::LineData& l_rightBorder_left        = m_zoneLayout->getRightBorderLine(9);
  const pc::vehicle::LineData& l_leftBorder_rear_left    = m_zoneLayout->getLeftBorderLine(15);
  const pc::vehicle::LineData& l_rightBorder_rear_left   = m_zoneLayout->getRightBorderLine(14);
  const pc::vehicle::LineData& l_leftBorder_rear         = m_zoneLayout->getLeftBorderLine(20);
  const pc::vehicle::LineData& l_rightBorder_rear        = m_zoneLayout->getRightBorderLine(19);
  const pc::vehicle::LineData& l_leftBorder_rear_right   = m_zoneLayout->getLeftBorderLine(25);
  const pc::vehicle::LineData& l_rightBorder_rear_right  = m_zoneLayout->getRightBorderLine(24);
  const pc::vehicle::LineData& l_leftBorder_right        = m_zoneLayout->getLeftBorderLine(30);
  const pc::vehicle::LineData& l_rightBorder_right       = m_zoneLayout->getRightBorderLine(29);
  const pc::vehicle::LineData& l_leftBorder_front_right  = m_zoneLayout->getLeftBorderLine(35);
  const pc::vehicle::LineData& l_rightBorder_front_right = m_zoneLayout->getRightBorderLine(34);
  m_textPositions[DigitalDisplayPositionID::FRONT]       = (l_leftBorder_front.m_outerPoint      + l_rightBorder_front.m_outerPoint) / 2.0f;  //front digital position
  m_textPositions[DigitalDisplayPositionID::FRONT_LEFT]  = (l_leftBorder_front_left.m_outerPoint + l_rightBorder_front_left.m_outerPoint) / 2.0f;
  m_textPositions[DigitalDisplayPositionID::LEFT]        = (l_leftBorder_left.m_outerPoint       + l_rightBorder_left.m_outerPoint)  / 2.0f;  //USS_LEFT  digital position
  m_textPositions[DigitalDisplayPositionID::REAR_LEFT]   = (l_leftBorder_rear_left.m_outerPoint  + l_rightBorder_rear_left.m_outerPoint)  / 2.0f;
  m_textPositions[DigitalDisplayPositionID::REAR]        = (l_leftBorder_rear.m_outerPoint       + l_rightBorder_rear.m_outerPoint)  / 2.0f + osg::Vec2f(g_ptsDisplaySettings->m_offset_rear.x(), 0.0f) ;  //rear  digital position
  m_textPositions[DigitalDisplayPositionID::REAR_RIGHT]  = (l_leftBorder_rear_right.m_outerPoint + l_rightBorder_rear_right.m_outerPoint)  / 2.0f;
  m_textPositions[DigitalDisplayPositionID::RIGHT]       = (l_leftBorder_right.m_outerPoint      + l_rightBorder_right.m_outerPoint) / 2.0f;  //USS_RIGHT digital position
  m_textPositions[DigitalDisplayPositionID::FRONT_RIGHT] = (l_leftBorder_front_right.m_outerPoint+ l_rightBorder_front_right.m_outerPoint) / 2.0f;
}

DistanceDigitalDisplayUpdateCallback::DistanceDigitalDisplayUpdateCallback(
  osg::ref_ptr<osg::Geode> f_FrontSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_RearSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_FrontSectorWarnImageGeode,
  osg::ref_ptr<osg::Geode> f_RearSectorWarnImageGeode,
  osg::ref_ptr<osg::Geode> f_LeftSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_RightSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_FrontLeftSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_RearLeftSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_RearRightSectorDisGeode,
  osg::ref_ptr<osg::Geode> f_FrontRightSectorDisGeode,
  pc::core::Framework* f_pFramework,
  pc::vehicle::AbstractZoneLayout* f_zoneLayout)
  : osg::NodeCallback()
  , m_FrontSectorDisGeode{f_FrontSectorDisGeode}
  , m_RearSectorDisGeode{f_RearSectorDisGeode}
  , m_FrontSectorWarnImageGeode{f_FrontSectorWarnImageGeode}
  , m_RearSectorWarnImageGeode{f_RearSectorWarnImageGeode}
  , m_LeftSectorDisGeode{f_LeftSectorDisGeode}
  , m_RightSectorDisGeode{f_RightSectorDisGeode}
  , m_FrontLeftSectorDisGeode{f_FrontLeftSectorDisGeode}
  , m_RearLeftSectorDisGeode{f_RearLeftSectorDisGeode}
  , m_RearRightSectorDisGeode{f_RearRightSectorDisGeode}
  , m_FrontRightSectorDisGeode{f_FrontRightSectorDisGeode}
  , m_UssSectorShortestDistance{}
  , m_textPositions{}
  , m_pFramework{f_pFramework}
  , m_zoneLayout{f_zoneLayout}
  , m_RadarWallState{}
{
  initDigitalDisplayPosition();
}



static bool UltrasonicZoneCompare( const pc::vehicle::UltrasonicZone& a, const pc::vehicle::UltrasonicZone& b)
{
  return  a.getDistance() < b.getDistance();
}

bool DistanceDigitalDisplayUpdateCallback::isUpdateUssData()
{
  if (true == m_pFramework->asCustomFramework()->m_ultrasonicDataReceiver.isConnected())
  {
    const pc::daddy::UltrasonicDataDaddy* const l_pDataDaddy = m_pFramework->asCustomFramework()->m_ultrasonicDataReceiver.getData();
    if (nullptr != l_pDataDaddy)
    {
      pc::vehicle::UltrasonicData l_ultrasonicData = l_pDataDaddy->m_Data;
      // map the Uss zone
      // Old ------------------------------> DAI_40
      // - 1 - 0 - 15 - 14 -                - 5-4   3-2 1-0  39-38   37-36   35-34
      // |                 |                |  FL      FML        FMR          FR|
      // 2                13                6                                   33
      // |                 |                |                                    |
      // 3                12                |                                    |
      // |                 |                |                                    |
      // 4                11                |                                    |
      // |                 |                |                                    |
      // 5                10                13 RL      RML         RMR      RR  26
      // - 6 - 7 - 8 - 9 - |                 -14-15 16-17 18-19 20-21 22-23 24-25-
      m_UssSectorShortestDistance[UssSectorId::USS_FRONT]      = std::min(std::min_element(l_ultrasonicData.begin()     , l_ultrasonicData.begin() + 4 , UltrasonicZoneCompare)->getDistance(), // PRQA S 3705 // PRQA S 3116
                                                                 std::min_element(l_ultrasonicData.begin() + 36, l_ultrasonicData.begin() + 40, UltrasonicZoneCompare)->getDistance()); // PRQA S 3705 // PRQA S 3116
      m_UssSectorShortestDistance[UssSectorId::USS_FRONT_LEFT] = std::min(l_ultrasonicData[4].getDistance(), l_ultrasonicData[5].getDistance());
      m_UssSectorShortestDistance[UssSectorId::USS_LEFT]       = std::min_element(l_ultrasonicData.begin() + 6 , l_ultrasonicData.begin() + 14, UltrasonicZoneCompare)->getDistance(); // PRQA S 3705 // PRQA S 3116
      m_UssSectorShortestDistance[UssSectorId::USS_REAR_LEFT]  = std::min(l_ultrasonicData[14].getDistance(), l_ultrasonicData[15].getDistance());
      m_UssSectorShortestDistance[UssSectorId::USS_REAR]       = std::min_element(l_ultrasonicData.begin() + 16, l_ultrasonicData.begin() + 24, UltrasonicZoneCompare)->getDistance(); // PRQA S 3705 // PRQA S 3116
      m_UssSectorShortestDistance[UssSectorId::USS_REAR_RIGHT] = std::min(l_ultrasonicData[24].getDistance(), l_ultrasonicData[25].getDistance());
      m_UssSectorShortestDistance[UssSectorId::USS_RIGHT]      = std::min_element(l_ultrasonicData.begin() + 26, l_ultrasonicData.begin() + 34, UltrasonicZoneCompare)->getDistance(); // PRQA S 3705 // PRQA S 3116
      m_UssSectorShortestDistance[UssSectorId::USS_FRONT_RIGHT]= std::min(l_ultrasonicData[34].getDistance(), l_ultrasonicData[35].getDistance());
      return true;
    }
  else
    {
      return false;
    }
  }
  return false;
}

osg::Vec4f DistanceDigitalDisplayUpdateCallback::getSplineColor(vfc::float32_t f_distance) const
{
  if(f_distance < g_ptsDisplaySettings->m_distanceR2)
  {
    return g_ptsDisplaySettings->m_splineColorR2;
  }
  else if(f_distance < g_ptsDisplaySettings->m_distanceR1)
  {
    return g_ptsDisplaySettings->m_splineColorR1;
  }
  else if(f_distance < g_ptsDisplaySettings->m_distanceO2)
  {
    return g_ptsDisplaySettings->m_splineColorO2;
  }
  else if(f_distance < g_ptsDisplaySettings->m_distanceO1)
  {
    return g_ptsDisplaySettings->m_splineColorO1;
  }
  else if(f_distance < g_ptsDisplaySettings->m_distanceY2)
  {
    return g_ptsDisplaySettings->m_splineColorY2;
  }
  else if(f_distance < g_ptsDisplaySettings->m_distanceY1)
  {
    return g_ptsDisplaySettings->m_splineColorY1;
  }
  else if(f_distance < g_ptsDisplaySettings->m_distanceB)
  {
    return g_ptsDisplaySettings->m_splineColorB;
  }
  else
  {
    return g_ptsDisplaySettings->m_splineColorB;
  }
}

  bool DistanceDigitalDisplayUpdateCallback::isShown (vfc::uint8_t f_digitalDisplayPosId)
  {
    switch (f_digitalDisplayPosId)
    {
        case  DigitalDisplayPositionID::FRONT:
        case  DigitalDisplayPositionID::FRONT_LEFT:
        case  DigitalDisplayPositionID::FRONT_RIGHT:
            {return m_RadarWallState[ pashmi::RADAR_WALL_FRONT];}
        case  DigitalDisplayPositionID::REAR:
        case  DigitalDisplayPositionID::REAR_LEFT:
        case  DigitalDisplayPositionID::REAR_RIGHT:
            {return m_RadarWallState[ pashmi::RADAR_WALL_REAR];}
        case  DigitalDisplayPositionID::LEFT:
            {return m_RadarWallState[ pashmi::RADAR_WALL_LEFT];}
        case  DigitalDisplayPositionID::RIGHT:
            {return m_RadarWallState[ pashmi::RADAR_WALL_RIGHT];}
        default:
            {break;}
    }
    return false;
  }

void DistanceDigitalDisplayUpdateCallback::updateDigitalDisplay(vfc::uint8_t f_ussId, vfc::uint8_t f_digitalPosId, osg::ref_ptr<osg::Geode> f_Geode, vfc::float32_t f_displayDistanceThresh)
{
  if(!isShown(f_digitalPosId))
  {
    f_Geode->setNodeMask(0u);
    return;
  }
  else
  {
    if (m_UssSectorShortestDistance[f_ussId] <= f_displayDistanceThresh )
    {
        f_Geode->setNodeMask(~0u);
        osgText::Text* const l_curShortestDis = static_cast<osgText::Text*>(f_Geode->getDrawable(0u)); // PRQA S 3076
        const std::string l_textString = std::to_string(pc::util::round2uInt((m_UssSectorShortestDistance[f_ussId]) * 100.0f)) + "cm";
        l_curShortestDis->setText(l_textString.c_str());
        l_curShortestDis->setColor(getSplineColor(m_UssSectorShortestDistance[f_ussId]));
        l_curShortestDis->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
        l_curShortestDis->setPosition(osg::Vec3f{m_textPositions[f_digitalPosId],0.0f});
    }
    else
    {
        f_Geode->setNodeMask(0u);
    }
  }
}

void DistanceDigitalDisplayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
//   if (m_pFramework->asCustomFramework()->m_radarWallStateReceiver.isConnected())
//   {
//     const cc::daddy::RadarWallStateDaddy* l_RadarWallStateDaddy = m_pFramework->asCustomFramework()->m_radarWallStateReceiver.getData();
//     if( l_RadarWallStateDaddy != nullptr)
//     {
//         m_RadarWallState = l_RadarWallStateDaddy->m_Data;
//     }
//   }
  m_RadarWallState[pashmi::RADAR_WALL_FRONT] = pashmi::RADAR_WALL_DIGITAL_OPEN;
  m_RadarWallState[pashmi::RADAR_WALL_REAR] = pashmi::RADAR_WALL_DIGITAL_OPEN;
  m_RadarWallState[pashmi::RADAR_WALL_LEFT] = pashmi::RADAR_WALL_DIGITAL_CLOSE;
  m_RadarWallState[pashmi::RADAR_WALL_RIGHT] = pashmi::RADAR_WALL_DIGITAL_CLOSE;

  if(isUpdateUssData())
  {
    updateDigitalDisplay(UssSectorId::USS_FRONT,       DigitalDisplayPositionID::FRONT,        m_FrontSectorDisGeode,       g_ptsDisplaySettings->m_rangeFrontConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_REAR,        DigitalDisplayPositionID::REAR ,        m_RearSectorDisGeode,        g_ptsDisplaySettings->m_rangeRearConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_FRONT_LEFT,  DigitalDisplayPositionID::FRONT_LEFT ,  m_FrontLeftSectorDisGeode,   g_ptsDisplaySettings->m_rangeSideConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_FRONT_RIGHT, DigitalDisplayPositionID::FRONT_RIGHT , m_FrontRightSectorDisGeode,  g_ptsDisplaySettings->m_rangeSideConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_REAR_LEFT,   DigitalDisplayPositionID::REAR_LEFT ,   m_RearLeftSectorDisGeode,    g_ptsDisplaySettings->m_rangeSideConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_REAR_RIGHT,  DigitalDisplayPositionID::REAR_RIGHT ,  m_RearRightSectorDisGeode,   g_ptsDisplaySettings->m_rangeSideConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_LEFT,        DigitalDisplayPositionID::LEFT ,        m_LeftSectorDisGeode,        g_ptsDisplaySettings->m_rangeSideConf_AIHC);
    updateDigitalDisplay(UssSectorId::USS_RIGHT,       DigitalDisplayPositionID::RIGHT ,       m_RightSectorDisGeode,       g_ptsDisplaySettings->m_rangeSideConf_AIHC);

  }
  traverse(f_node, f_nv);
}


} // namespace rctaoverlay
} // namespace assets
} // namespace cc
