//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  WarnSymbols.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_WARNSYMBOLS_H
#define CC_ASSETS_UIELEMENTS_WARNSYMBOLS_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{


//!
//! WarnSymbolManager
//!
class WarnSymbolManager
{
public:
  WarnSymbolManager();
  virtual ~WarnSymbolManager();

  void init(pc::assets::ImageOverlays* f_imageOverlays);
  void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
  void update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);

private:
  //! Copy constructor is not permitted.
  WarnSymbolManager (const WarnSymbolManager& other); // = delete
  //! Copy assignment operator is not permitted.
  WarnSymbolManager& operator=(const WarnSymbolManager& other); // = delete

  unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_ussWarnIcons;
  cc::views::planview::PlanViewCullCallback* m_planViewCullCall;
  bool m_mat_b;
};


//!
//! WarnSymbols
//!
class WarnSymbols: public cc::assets::uielements::CustomImageOverlays
{
public:
  WarnSymbols(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~WarnSymbols();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  WarnSymbols (const WarnSymbols& other); // = delete
  //! Copy assignment operator is not permitted.
  WarnSymbols& operator=(const WarnSymbols& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  WarnSymbolManager m_manager;
};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_WARNSYMBOLS_H
