//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: YRN1WX Yang Rui (BCSC-EPA1)
//  Department: BCSC-EPA1
//=============================================================================
/// @swcomponent SVS BYD
/// @file  HoriParkingView.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ParkingView.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "vfc/core/vfc_types.hpp"
using pc::util::logging::g_AppContext;

#define ENABLE_VERTICAL_MODE 0
#define PARKINGVIEW_VEHICLE2D_DOOR_WARNING 0

namespace cc
{
namespace assets
{
namespace uielements
{


void CustomIconGroup::addIcon(ParkingProgressType f_index, const std::string& f_filepath, osg::Vec2f f_iconPos) // PRQA S 0255
{
  CustomIconData* const l_iconData = new CustomIconData(f_filepath, f_iconPos);
  m_icons[f_index] = l_iconData;
}

void CustomIconGroup::addIcon(ParkingProgressType f_index, const std::string& f_filepath, osg::Vec2f f_iconPos, osg::Vec2f f_iconSize)
{
  CustomIconData* const l_iconData = new CustomIconData(f_filepath, f_iconPos, f_iconSize);
  m_icons[f_index] = l_iconData;
}

vfc::uint32_t CustomIconGroup::getNumIcons() const
{
  return static_cast<vfc::uint32_t> (m_icons.size());
}

CustomIconData* CustomIconGroup::getIcon(ParkingProgressType f_index)
{
  if (getNumIcons() > static_cast<vfc::uint32_t>(f_index))
  {
    return m_icons[f_index];
  }
  return nullptr;
}

void CustomIconGroup::setAllEnabled(bool f_enable)
{
  for (vfc::int32_t i = 0; i < ParkingProgressType::NUMBER_OF_ICON; i++)
  {
    m_icons[static_cast<ParkingProgressType>(i)]->setEnabled(f_enable);
  }
}

//!
//! @brief Construct a new ParkingSearching Manager:: ParkingSearching Manager object
//!
//! @param f_config
//!
ParkingViewManager::ParkingViewManager() // PRQA S 4206
  //: m_lastConfigUpdate(~0u)
  : m_settingParkView{}
  , m_ViewButtonParkModeDispSts{cc::daddy::PARK_MODE_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonParkStartDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonParkContinueDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonParkPauseDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonParkQuitDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonParkOutDirectionDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonFreeParkingConfirmDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonFreeParkingSpaceTypeDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_lastConfigUpdate{~0u}
{

}

// ! transfer to start from bottom left
osg::Vec2f ParkingViewManager::transferToBottomLeft(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(f_iconPos.x() - static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_origin.x()), static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.y()) - f_iconPos.y());
}

ParkingViewManager::~ParkingViewManager() = default;

void ParkingViewManager::init(cc::assets::uielements::CustomImageOverlays* /*f_imageOverlays*/) // PRQA S 6044
{
  // ! init icons
  // m_settingParkView.clear(f_imageOverlays);
  // parking searching
  // m_settingParkView.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkAutoPic,                              g_uiSettings->m_settingParkingUISeachingAutoIcon.m_iconCenter,                      g_uiSettings->m_settingParkingUISeachingAutoIcon.m_iconSize,                 g_uiSettings->m_settingParkingUISeachingAutoIcon.m_isHoriScreen));

  // ! init icons
  // parking searching
  // m_settingParkView.addIcon(PARKING_SEARCHING_BACKGROUND,                             g_uiSettings->m_texturePathParkingUIBackground,                g_uiSettings->m_settingParkingUIBackground.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_AUTO_PIC,                               g_uiSettings->m_texturePathParkAutoPic,                                   g_uiSettings->m_settingParkingUISeachingAutoIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_SLOT_SEARCHING,                         g_uiSettings->m_texturePathTextBoxSlotSearching,                          g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_AT_LOW_SPEED,                           g_uiSettings->m_texturePathTextBoxSearchingInLowSpeed,                    g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TEXT_TIME_OUT,                          g_uiSettings->m_texturePathSearchingTextBoxTimeOut,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TIME_OUT,                               g_uiSettings->m_texturePathSearchingTimeOut,                              g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TEXT_SLOW_DOWN,                         g_uiSettings->m_texturePathSearchingTextBoxSlowDown,                      g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_SLOW_DOWN,                              g_uiSettings->m_texturePathSearchingSlowDown,                             g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_FIND_SLOT_STOP,                         g_uiSettings->m_texturePathSearchingTextBoxFindSlotStop,                  g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_POC_DIREC_SELECT_VEHICLE,               g_uiSettings->m_texturePathSearchingPOCDirecSelectVehicle,                g_uiSettings->m_settingParkingUIParkOutSideVehicle.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_POC_DIREC_SELECT_VEHICLE_TRANS,         g_uiSettings->m_texturePathSearchingPOCDirecSelectVehicleTrans,           g_uiSettings->m_settingParkingUIParkOutSideVehicle.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_POC_DIREC_SELECT_STEERING_WHEEL,        g_uiSettings->m_texturePathSearchingPOCDirecSelectSteeringWheel,          g_uiSettings->m_settingParkingUIParkOutSideSteeringWheel.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_POC_DIREC_SELECT_STEERING_WHEEL_TRANS,  g_uiSettings->m_texturePathSearchingPOCDirecSelectSteeringWheelTrans,     g_uiSettings->m_settingParkingUIParkOutSideSteeringWheel.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_TOP,              g_uiSettings->m_texturePathTextBoxSearchingPOCDirecSelectTop,             g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_BOTTOM,           g_uiSettings->m_texturePathTextBoxSearchingPOCDirecSelectBottom,          g_uiSettings->m_settingParkingUIParkOutSideTextBottom.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_BOTTOM_TRANS,     g_uiSettings->m_texturePathTextBoxSearchingPOCDirecSelectBottomTrans,     g_uiSettings->m_settingParkingUIParkOutSideTextBottom.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_TOP_VIEW,                               g_uiSettings->m_texturePathSearchingVehicleWithDoors,                     g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_DOOR_FRONT_RIGHT_OPEN,                  g_uiSettings->m_texturePathSearchingDoorFrontRight,                       g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_DOOR_FRONT_LEFT_OPEN,                   g_uiSettings->m_texturePathSearchingDoorFrontLeft,                        g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_TRUNK_OPEN,                             g_uiSettings->m_texturePathSearchingTrunkOpen,                            g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_DOOR_REAR_RIGHT_OPEN,                   g_uiSettings->m_texturePathSearchingDoorRearRight,                        g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_DOOR_REAR_LEFT_OPEN,                    g_uiSettings->m_texturePathSearchingDoorRearLeft,                         g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_HOOD_OPEN,                              g_uiSettings->m_texturePathSearchingHoodOpen,                             g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter,              osg::Vec2f{235.0f, 235.0f});
  m_settingParkView.addIcon(PARKING_SEARCHING_DOORS_OPEN,                             g_uiSettings->m_texturePathSearchingDoorsOpen,                            g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SEARCHING_SELECT_PARKOUT_DIRECTION,               g_uiSettings->m_texturePathSearchingTextBoxSelectParkoutDirectionWhenStop,g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);

  // start parking
  m_settingParkView.addIcon(START_PARKING_BUTTON_START_PARK_IN,                       g_uiSettings->m_texturePathStarkParkInButton,                             g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);
  m_settingParkView.addIcon(START_PARKING_BUTTON_START_PARK_OUT,                      g_uiSettings->m_texturePathStarkParkOutButton,                            g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);
  m_settingParkView.addIcon(START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE,            g_uiSettings->m_texturePathStarkParkInDeactivateButton,                   g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);
  m_settingParkView.addIcon(START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE,           g_uiSettings->m_texturePathStarkParkOutDeactivateButton,                  g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);
  m_settingParkView.addIcon(START_PARKING_PARKING_IN,                                 g_uiSettings->m_texturePathTextParkingIn,                                 g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  // guidance activate
  m_settingParkView.addIcon(PARKING_GUIDANCE_GEAR_D,                                  g_uiSettings->m_texturePathGuidanceGearD,                                 g_uiSettings->m_settingParkingUIGearIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_GEAR_N,                                  g_uiSettings->m_texturePathGuidanceGearN,                                 g_uiSettings->m_settingParkingUIGearIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_GEAR_R,                                  g_uiSettings->m_texturePathGuidanceGearR,                                 g_uiSettings->m_settingParkingUIGearIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_GEAR_P,                                  g_uiSettings->m_texturePathGuidanceGearP,                                 g_uiSettings->m_settingParkingUIGearIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_APA_PARKING_IN,                          g_uiSettings->m_texturePathTextBoxAPAParkingIn,                           g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_APA_PARKING_OUT,                         g_uiSettings->m_texturePathTextBoxAPAParkingOut,                          g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_NOTICE_SUR,                              g_uiSettings->m_texturePathTextBoxParkingNoticeSur,                       g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_CONTINUE_DRIVE_DISTANCE,                 g_uiSettings->m_texturePathContinueDrivingText,                           g_uiSettings->m_settingParkingUIContinueDrivingTextUIPanel.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_MOVES_LEFT_NUMBER,                       g_uiSettings->m_texturePathMovesLeftNumberText,                           g_uiSettings->m_settingParkingUIMovesLeftNumberTextUIPanel.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_TEXT_RELEASE_BRAKE_AND_STEERING,         g_uiSettings->m_texturePathTextGuidanceReleaseBrakeAndSteering,           g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_BUTTON_SUSPEND,                          g_uiSettings->m_texturePathSuspendButton,                                 g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_GUIDANCE_PARKINGOUT_PAYATTENTION_ENV,             g_uiSettings->m_texturePathTextParkingOutPayAttentionToEnvironment,       g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);

  // park finished
  m_settingParkView.addIcon(PARKING_COMPLETE_FINISHED,                                g_uiSettings->m_texturePathTextBoxParkFinished,                      g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMPLETE_TAKEOVER,                                g_uiSettings->m_texturePathTextBoxFinishedTakeOver,                  g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);

  // suspend
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_PARKING_PAUSE,                       g_uiSettings->m_texturePathHoriSuspendParkingPause,                  g_uiSettings->m_settingParkingUISuspendIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_QUIT_IN30S,                               g_uiSettings->m_texturePathTextBoxQuitIn30s,                         g_uiSettings->m_settingQuitIn30SecondText.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_BUTTON_CONTINUE,                          g_uiSettings->m_texturePathSuspendContinueButton,                    g_uiSettings->m_settingParkinContinueButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_BUTTON_CONTINUE_GRAY,                     g_uiSettings->m_texturePathSuspendContinueButtonGray,                g_uiSettings->m_settingParkinContinueButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_OBJECT_ON_PATH,                      g_uiSettings->m_texturePathSuspendTextBoxObjectOnPath,               g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_OBJECT_ON_PATH,                           g_uiSettings->m_texturePathSuspendObjectOnPath,                      g_uiSettings->m_settingParkingUISeachingVehicleWithDoors.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_OBJECT_ON_PATH_ICON,                      g_uiSettings->m_texturePathSuspendObjectOnPathIcon,                  g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_DOOR_OPEN,                           g_uiSettings->m_texturePathSuspendTextBoxDoorOpen,                   g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_BRAKE_PEDAL,                         g_uiSettings->m_texturePathSuspendTextBrakePadal,                    g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_BRAKE_PEDAL,                              g_uiSettings->m_texturePathSuspendBrakePadal,                        g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_MIRROR_FOLD,                         g_uiSettings->m_texturePathSuspendTextMirrorFold,                    g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_MIRROR_FOLD,                              g_uiSettings->m_texturePathSuspendMirrorFold,                        g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_HOOD_OPEN,                           g_uiSettings->m_texturePathSuspendTextHoodOpen,                      g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_HOOD_OPEN,                                g_uiSettings->m_texturePathSuspendHoodOpen,                          g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_TRUNK_OPEN,                          g_uiSettings->m_texturePathSuspendTextTrunkOpen,                     g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TRUNK_OPEN,                               g_uiSettings->m_texturePathSuspendTrunkOpen,                         g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_TEXT_USER_PAUSE,                          g_uiSettings->m_texturePathSuspendTextUserTriggerPause,              g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_GUIDELINE_CROSSSLOT_LEFT,                 g_uiSettings->m_texturePathParkCrossSlotLeft,                        g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter);
  m_settingParkView.addIcon(PARKING_SUSPEND_GUIDELINE_CROSSSLOT_RIGHT,                g_uiSettings->m_texturePathParkCrossSlotRight,                       g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter);

  // assist standby
  m_settingParkView.addIcon(PARKING_ASSIST_STANDBY_RESPONSE_TIMEOUT,                g_uiSettings->m_texturePathAssistStandbyResponseTimeout,                       g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);


  // quit
  m_settingParkView.addIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER,                          g_uiSettings->m_texturePathQuitTextBoxApaQuitTakeOver,               g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_BELT_UNBUCKLE,                          g_uiSettings->m_texturePathQuitTextBoxSeatBeltUnbuckle,              g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_BELT_UNBUCKLE,                               g_uiSettings->m_texturePathSeatBeltUnbuckle,                         g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_EXCESSIVE_SLOP,                         g_uiSettings->m_texturePathQuitTextBoxExcessiveSlope,                g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_EXCESSIVE_SLOP,                              g_uiSettings->m_texturePathExcessiveSlope,                           g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_DRIVER_OVERRIDE,                        g_uiSettings->m_texturePathQuitTextDriverOverride,                   g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_DRIVER_OVERRIDE,                             g_uiSettings->m_texturePathDriverOverride,                           g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_ROUTE_PLANNING_FAILURE,                 g_uiSettings->m_texturePathQuitTextRoutePlanningFailure,             g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_ROUTE_PLANNING_FAILURE,                      g_uiSettings->m_texturePathRoutePlanningFailure,                     g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_VEHICLE_SPEED_OVER_THRESHOLD,           g_uiSettings->m_texturePathQuitTextVehicleSpeedOverthreshold,        g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_VEHICLE_SPEED_OVER_THRESHOLD,                g_uiSettings->m_texturePathVehicleSpeedOverthreshold,                g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_APS_TIMEOUT,                            g_uiSettings->m_texturePathQuitTextAPSTimeout,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_APS_TIMEOUT,                                 g_uiSettings->m_texturePathAPSTimeout,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_CURRENT_STEP_NUMBER_OVER_THRESHOLD,     g_uiSettings->m_texturePathQuitTextCurrentStepNumberOverThreshold,   g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_CURRENT_STEP_NUMBER_OVER_THRESHOLD,          g_uiSettings->m_texturePathCurrentStepNumberOverThreshold,           g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_SPACE_IS_LIMITED_IN_PARK_OUT_MODE,      g_uiSettings->m_texturePathQuitTextSpaceIsLimitedInParkOutMode,      g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_SPACE_IS_LIMITED_IN_PARK_OUT_MODE,           g_uiSettings->m_texturePathSpaceIsLimitedInParkOutMode,              g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_EPB_FAILURE,                            g_uiSettings->m_texturePathQuitTextEPBFailure,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_EPB_FAILURE,                                 g_uiSettings->m_texturePathEPBFailure,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_SCU_FAILURE,                            g_uiSettings->m_texturePathQuitTextSCUFailure,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_SCU_FAILURE,                                 g_uiSettings->m_texturePathSCUFailure,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_APA_FAILURE,                            g_uiSettings->m_texturePathQuitTextAPAFailure,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_APA_FAILURE,                                 g_uiSettings->m_texturePathAPAFailure,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_EXTERNAL_ECU_FAILURE,                   g_uiSettings->m_texturePathQuitTextExternalECUFailure,               g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_EXTERNAL_ECU_FAILURE,                        g_uiSettings->m_texturePathExternalECUFailure,                       g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_ABS_TCS_ESP_ACC_AEB_ACTIVE,             g_uiSettings->m_texturePathQuitTextABSTCSESPACCAEBActive,            g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_ABS_TCS_ESP_ACC_AEB_ACTIVE,                  g_uiSettings->m_texturePathABSTCSESPACCAEBActive,                    g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_ESC_FAILURE,                            g_uiSettings->m_texturePathQuitTextESCFailure,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_ESC_FAILURE,                                 g_uiSettings->m_texturePathESCFailure,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_TEXT_EPS_FAILURE,                            g_uiSettings->m_texturePathQuitTextEPSFailure,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_QUIT_EPS_FAILURE,                                 g_uiSettings->m_texturePathEPSFailure,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TEXT_VEHICLE_BLOCK,                             g_uiSettings->m_texturePathQuitTextVehicleBlock,                     g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_VEHICLE_BLOCK,                                  g_uiSettings->m_texturePathVehicleBlock,                             g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TEXT_INTERRUPT_NUMBER_OVER_THRESHOLD,           g_uiSettings->m_texturePathQuitTextInterruptNumberOverThreshold,     g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_INTERRUPT_NUMBER_OVER_THRESHOLD,                g_uiSettings->m_texturePathInterruptNumberOverThreshold,             g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TEXT_RADAR_DIRTY,                               g_uiSettings->m_texturePathQuitTextRadarDirty,                       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_RADAR_DIRTY,                                    g_uiSettings->m_texturePathRadarDirty,                               g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TEXT_EPB_ACTIVE,                                g_uiSettings->m_texturePathQuitTextEPBActive,                        g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_EPB_ACTIVE,                                     g_uiSettings->m_texturePathEPBActive,                                g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TEXT_DRIVE_MODE_UNSUITABLE,                     g_uiSettings->m_texturePathQuitTextBoxDriveModeUnsuitable,           g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_DRIVE_MODE_UNSUITABLE,                          g_uiSettings->m_texturePathDriveModeUnsuitable,                      g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TEXT_TRAILER_HITCH_CONNECTED,                   g_uiSettings->m_texturePathQuitTextBoxTrailerHitchConnected,         g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARK_QUIT_TRAILER_HITCH_CONNECTED,                        g_uiSettings->m_texturePathTrailerHitchConnected,                    g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);

  //park comfirming
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_SEAT_BELT,                        g_uiSettings->m_texturePathParkConfirmingTextSeatBelt,               g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_PRESS_BRAKE_PEDAL,                g_uiSettings->m_texturePathParkConfirmingTextPressBrakePedal,        g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_PRESS_BRAKE_PEDAL,                     g_uiSettings->m_texturePathParkConfirmingPressBrakePedal,            g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_CLOSE_DOOR,                       g_uiSettings->m_texturePathParkConfirmingTextCloseDoor,              g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_EXPANDED_MIRROR,                  g_uiSettings->m_texturePathParkConfirmingTextExpandedMirror,         g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_EXPANDED_MIRROR,                       g_uiSettings->m_texturePathParkConfirmingExpandedMirror,             g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_CLOSE_TRUNK,                      g_uiSettings->m_texturePathParkConfirmingTextCloseTrunk,             g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_CLOSE_HOOD,                       g_uiSettings->m_texturePathParkConfirmingTextCloseHood,              g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_STOP,                             g_uiSettings->m_texturePathParkConfirmingTextStop,                   g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_FRONT_IS_CLEAR_PARKING_PATH,      g_uiSettings->m_texturePathParkFrontIsClearParkingPath,              g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR_PARKING_PATH,           g_uiSettings->m_texturePathParkFrontIsClearPic,                      g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_FRONT_IS_CLEAR_HANDOVER,          g_uiSettings->m_texturePathParkFrontIsClearHandOver,                 g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR,                        g_uiSettings->m_texturePathParkFrontIsClear,                         g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_SMALL_PARK_SLOT,                  g_uiSettings->m_texturePathPathParkConfirmingTextSmallParkSlot,      g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_SMALL_PARK_SLOT,                       g_uiSettings->m_texturePathPathParkConfirmingSmallParkSlot,          g_uiSettings->m_settingParkingUICenterIcon.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_HOLD_BRAKE_AND_START_PARKING,     g_uiSettings->m_texturePathParkConfirmingTextHoldBrakeAndStart,      g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(PARKING_CONFIRMING_TEXT_APA_QUIT_TAKE_OVER,               g_uiSettings->m_texturePathConfirmingTextAPAquitTakeOver,            g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(PARKING_COMFIRMING_TEXT_KEEP_BRAKE_PEDAL,                 g_uiSettings->m_texturePathParkConfirmingTextKeepBrakePedal,         g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);

  // Free Parking
  m_settingParkView.addIcon(FREE_PARKING_TEXT_CHOOSE_PARKING_SPACE_TYPE,                g_uiSettings->m_texturePathFreeParkingTextChooseParkingSpaceType,    g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_TEXT_DESCRIPTION_TOP,                          g_uiSettings->m_texturePathFreeParkingTextDescriptionTop,            g_uiSettings->m_settingParkingUIFreeParkingTextDescription.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_TEXT_DESCRIPTION_BOTTOM,                       g_uiSettings->m_texturePathFreeParkingTextDescriptionBottom,         g_uiSettings->m_settingParkingUIFreeParkingOperationGuide.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_TEXT_STOP,                                     g_uiSettings->m_texturePathFreeParkingTextStop,                      g_uiSettings->m_settingParkingUITopTextBlue.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_SELECTED_PARKING_INSTRUCTIONS_BUTTON,          g_uiSettings->m_texturePathFreeParkingSelectedParkingInstructions,   g_uiSettings->m_settingParkingUIFreeParkingInstructions.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_CONFIRM_BUTTON,                                g_uiSettings->m_texturePathFreeParkingConfirmButton,                 g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);

  // RPA
  m_settingParkView.addIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE,                          g_uiSettings->m_texturePathRPAOthersBluetoothmobilephone,            g_uiSettings->m_settingParkingUIRPAMobilePhoneIcon.m_iconCenter);
  m_settingParkView.addIcon(RPA_OTHERS_BLUETOOTH_DISCONNECT,                            g_uiSettings->m_texturePathRPAOthersBluetoothDisconnect,             g_uiSettings->m_settingParkingUIRPAMobilePhoneIcon.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_BLUETOOTH_CONNECTED_PROMPT,                        g_uiSettings->m_texturePathRPATextBluetoothConnectedPrompt,          g_uiSettings->m_settingParkingUIRPATextPrompt.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_BLUETOOTH_CONNECTED,                               g_uiSettings->m_texturePathRPATextBluetoothConnected,                g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_PLEASE_PARKING,                                    g_uiSettings->m_texturePathRPATextPlease_parking,                    g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_CLICK_ON_THE_PHONE_TO_START_PARKING,               g_uiSettings->m_texturePathRPATextClickOnThePhoneToStartParking,     g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_OPEN_THE_APP_AND_CONNECT_TO_BLUETOOTH,             g_uiSettings->m_texturePathRPATextOpenTheAPPAndConnectToBluetooth,   g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_REMOTE_PARKING_IS_NOT_AVAILABLE,                   g_uiSettings->m_texturePathRPATextRemoteParkingIsNotAvailable,       g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_REMOTE_PARKING,                                    g_uiSettings->m_texturePathRPATextRemoteParking,                     g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_DRIVER_REPONSE_TIMEOUT,                            g_uiSettings->m_texturePathRPATextDriverReponseTimeout,              g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR,                              g_uiSettings->m_texturePathRPATextPleaseLeaveTheCar,                 g_uiSettings->m_settingParkingUIRPAPleaseLeaveTheCar.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_SUSPEND,                                           g_uiSettings->m_texturePathRPATextSuspend,                           g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_BLUETOOTH_DISCONNECT,                              g_uiSettings->m_texturePathRPATextBlueToothDisconnect,               g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_TERMINATE,                                         g_uiSettings->m_texturePathRPATextTerminated,                        g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);
  m_settingParkView.addIcon(RPA_TEXT_USE_PHONE,                                         g_uiSettings->m_texturePathRPATextUsePhone,                          g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter);

  // button
  m_settingParkView.addIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN,                   g_uiSettings->m_texturePathAPAFunctionSelectionButtonParkin,         g_uiSettings->m_settingParkingUIFunctionButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKOUT,                  g_uiSettings->m_texturePathAPAFunctionSelectionButtonParkout,        g_uiSettings->m_settingParkingUIFunctionButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_FUNCTION_SELECTION_FREE_PARKING,             g_uiSettings->m_texturePathAPAFunctionSelectionButtonFreeParking,    g_uiSettings->m_settingParkingUIFunctionButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_APA,               g_uiSettings->m_texturePathAPAFunctionSelectionButtonParkinAPA,      g_uiSettings->m_settingParkingUIAPARPAButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_RPA,               g_uiSettings->m_texturePathAPAFunctionSelectionButtonParkinRPA,      g_uiSettings->m_settingParkingUIAPARPAButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_UNSELECT,         g_uiSettings->m_texturePathParkOutSideParallelLeftButtonUnSelect,            g_uiSettings->m_settingParkingUIParkOutSideParallelLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_SELECTED,         g_uiSettings->m_texturePathParkOutSideParallelLeftButtonSelected,            g_uiSettings->m_settingParkingUIParkOutSideParallelLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_UNSELECT,        g_uiSettings->m_texturePathParkOutSideParallelRightButtonUnSelect,           g_uiSettings->m_settingParkingUIParkOutSideParallelRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_SELECTED,        g_uiSettings->m_texturePathParkOutSideParallelRightButtonSelected,           g_uiSettings->m_settingParkingUIParkOutSideParallelRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_UNSELECT_TRANS,   g_uiSettings->m_texturePathParkOutSideParallelLeftButtonUnSelectTrans,       g_uiSettings->m_settingParkingUIParkOutSideParallelLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_SELECTED_TRANS,   g_uiSettings->m_texturePathParkOutSideParallelLeftButtonSelectedTrans,       g_uiSettings->m_settingParkingUIParkOutSideParallelLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_UNSELECT_TRANS,  g_uiSettings->m_texturePathParkOutSideParallelRightButtonUnSelectTrans,      g_uiSettings->m_settingParkingUIParkOutSideParallelRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_SELECTED_TRANS,  g_uiSettings->m_texturePathParkOutSideParallelRightButtonSelectedTrans,      g_uiSettings->m_settingParkingUIParkOutSideParallelRightButton.m_iconCenter);

  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_UNSELECT,            g_uiSettings->m_texturePathParkOutSideCrossLeftButtonUnSelect,        g_uiSettings->m_settingParkingUIParkOutSideCrossLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_SELECTED,            g_uiSettings->m_texturePathParkOutSideCrossLeftButtonSelected,        g_uiSettings->m_settingParkingUIParkOutSideCrossLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_UNSELECT,           g_uiSettings->m_texturePathParkOutSideCrossRightButtonUnSelect,       g_uiSettings->m_settingParkingUIParkOutSideCrossRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_SELECTED,           g_uiSettings->m_texturePathParkOutSideCrossRightButtonSelected,       g_uiSettings->m_settingParkingUIParkOutSideCrossRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_UNSELECT_TRANS,      g_uiSettings->m_texturePathParkOutSideCrossLeftButtonUnSelectTrans,   g_uiSettings->m_settingParkingUIParkOutSideCrossLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_SELECTED_TRANS,      g_uiSettings->m_texturePathParkOutSideCrossLeftButtonSelectedTrans,   g_uiSettings->m_settingParkingUIParkOutSideCrossLeftButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_UNSELECT_TRANS,     g_uiSettings->m_texturePathParkOutSideCrossRightButtonUnSelectTrans,  g_uiSettings->m_settingParkingUIParkOutSideCrossRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_SELECTED_TRANS,     g_uiSettings->m_texturePathParkOutSideCrossRightButtonSelectedTrans,  g_uiSettings->m_settingParkingUIParkOutSideCrossRightButton.m_iconCenter);
  m_settingParkView.addIcon(PARKING_BUTTON_QUIT,                                        g_uiSettings->m_texturePathParkQuitButton,                           g_uiSettings->m_settingParkinQuitButton.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_CONFIRM_BUTTON_GREY,                                   g_uiSettings->m_texturePathFreeParkingConfirmButtonGrey,                         g_uiSettings->m_settingParkingStartPauseConfirmButton.m_iconCenter);

  // Free Parking
  m_settingParkView.addIcon(FREE_PARKING_SELECTED_PARKING_INSTRUCTIONS_BUTTON_GREY,             g_uiSettings->m_texturePathFreeParkingSelectedParkingInstructionsGrey,           g_uiSettings->m_settingParkingUIFreeParkingInstructions.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_SELECTED_CROSS_PARKING_INSTRUCTIONS_BUTTON,            g_uiSettings->m_texturePathFreeParkingSelectedCrossParkingInstructions,          g_uiSettings->m_settingParkingUIFreeParkingInstructions.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_SELECTED_CROSS_PARKING_INSTRUCTIONS_BUTTON_GREY,       g_uiSettings->m_texturePathFreeParkingSelectedCrossParkingInstructionsGrey,      g_uiSettings->m_settingParkingUIFreeParkingInstructions.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_SELECTED_DIAGONAL_PARKING_INSTRUCTIONS_BUTTON,         g_uiSettings->m_texturePathFreeParkingSelectedDiagonalParkingInstructions,       g_uiSettings->m_settingParkingUIFreeParkingInstructions.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_SELECTED_DIAGONAL_PARKING_INSTRUCTIONS_BUTTON_GREY,    g_uiSettings->m_texturePathFreeParkingSelectedDiagonalParkingInstructionsGrey,   g_uiSettings->m_settingParkingUIFreeParkingInstructions.m_iconCenter);
  m_settingParkView.addIcon(FREE_PARKING_TEXT_DESCRIPTION_BOTTOM_TRANS,                         g_uiSettings->m_texturePathFreeParkingTextDescriptionBottomTrans,                g_uiSettings->m_settingParkingUIFreeParkingOperationGuide.m_iconCenter);
}

void ParkingViewManager::update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework)    // PRQA S 6043 // PRQA S 6040 // PRQA S 6041 // PRQA S 6044  // PRQA S 4213 // PRQA S 2755
{
  if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
  {
      return;
  }
  // ! check if config has changed
  // if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  // {
  //   init(f_imageOverlays);
  //   m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  // }

  m_settingParkView.setAllEnabled(false);

  this->CleanButtonDispSts();

  // ! read all the signals
  cc::target::common::EPARKStatusR2L                l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld               l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L             l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndExtR2L          l_curparkDriverIndExt     = cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest;
  cc::target::common::EPARKRecoverIndR2L            l_curparkSuspend          = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  cc::target::common::EPARKQuitIndR2L               l_curparkQuitInd          = cc::target::common::EPARKQuitIndR2L::PARKQUIT_None;
  cc::target::common::EPARKQuitIndR2LExt            l_curParkQuitIndExt       = cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault;
  cc::target::common::EPARKDriverIndSearchR2L       l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                          l_curFreeParkingActive    = false;
  cc::target::common::rbp_Type_ParkManeuverType_en  l_curparkPSDirection      = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;

  cc::target::common::EAPAPARKMODE                  l_curParkAPARPAParkMode   = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::RPAAvailable                  l_curParkRPAAvaliable     = cc::target::common::RPAAvailable::RPA_NotAvailable;
  vfc::uint8_t                  l_curDoorState[pc::daddy::NUMBER_OF_CARDOORS] = {0u};
  cc::target::common::EPARKSideR2L                  l_curParkOutDirection     = cc::target::common::EPARKSideR2L::PARKSIDE_None;

  // ! signal to send
  bool                          l_dynamicGearStatus       = false;


  //free parking ps type
  freeparking::EFreeParkingSpaceType l_curpFPparkSpaceType = freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_UNKNOWN;

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndExtReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndExtDaddy_t* const l_parkDriverIndExt = f_framework->m_parkHmiParkDriverIndExtReceiver.getData();
    l_curparkDriverIndExt = l_parkDriverIndExt->m_Data;
  }

  if (f_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
  {
    const cc::daddy::ParkRecoverIndDaddy_t* const l_parkSuspend = f_framework->m_parkHmiParkingRecoverIndReceiver.getData();
    l_curparkSuspend = l_parkSuspend->m_Data;
  }

  if (f_framework->m_parkPSDirectionSelectedReceiver.hasData())
  {
    const cc::daddy::ParkPSDirectionSelected_t* const l_parkPSDirection = f_framework->m_parkPSDirectionSelectedReceiver.getData();
    l_curparkPSDirection = l_parkPSDirection->m_Data;
  }

  if (f_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = f_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (f_framework->m_parkHmiParkingQuitIndReceiver.hasData())
  {
    const cc::daddy::ParkQuitIndDaddy_t* const l_parkQuitInd = f_framework->m_parkHmiParkingQuitIndReceiver.getData();
    l_curparkQuitInd = l_parkQuitInd->m_Data;
  }

  if (f_framework->m_parkHmiParkingQuitIndExtReceiver.hasData())
  {
    const cc::daddy::ParkQuitIndExtDaddy_t* const l_parkQuitIndExt = f_framework->m_parkHmiParkingQuitIndExtReceiver.getData();
    l_curParkQuitIndExt = l_parkQuitIndExt->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
     const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
     l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curParkAPARPAParkMode = l_parkAPAPARKMODE->m_Data;
  }

  if (f_framework->m_parkHmiRPAAvailableReceiver.hasData())
  {
    const cc::daddy::ParkRPAAvaliableDaddy_t* const l_ParkRPAAvaliable = f_framework->m_parkHmiRPAAvailableReceiver.getData();
    l_curParkRPAAvaliable = l_ParkRPAAvaliable->m_Data;
  }

  if (f_framework->m_freeparkingSpaceTypeReceiver.hasData())
  {
    const cc::daddy::FreeParkingSpaceTypeButtonStDaddy_t* const l_pFPparkSpaceType = f_framework->m_freeparkingSpaceTypeReceiver.getData();
    l_curpFPparkSpaceType = l_pFPparkSpaceType->m_Data;
  }


  if (f_framework->m_doorStateReceiver.hasData())
  {
    const pc::daddy::DoorStateDaddy* const l_doorStateDaddy = f_framework->m_doorStateReceiver.getData();
    l_curDoorState[0u] = static_cast<vfc::uint8_t>(l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]);
    l_curDoorState[1u] = static_cast<vfc::uint8_t>(l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]);
    l_curDoorState[2u] = static_cast<vfc::uint8_t>(l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK]);
    l_curDoorState[3u] = static_cast<vfc::uint8_t>(l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]);
    l_curDoorState[4u] = static_cast<vfc::uint8_t>(l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT]);
    l_curDoorState[5u] = static_cast<vfc::uint8_t>(l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_HOOD]);
  }

  if (f_framework->m_parkHmiParkingSideReceiver.hasData())
  {
    const cc::daddy::ParkSideDaddy_t* const l_parkOutDirection = f_framework->m_parkHmiParkingSideReceiver.getData();
    l_curParkOutDirection = l_parkOutDirection->m_Data;
  }



  // cc::target::common::EPARKStatusR2L::PARK_Searching -> cc::target::common::EPARKStatusR2L::PARK_AssistStandby (confirmation) -> APA_EPS Handshaking -> cc::target::common::EPARKStatusR2L::PARK_Guidance_active -> cc::target::common::EPARKStatusR2L::PARK_Completed
  //                                                     (Interruption during parking guidance: cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend or cc::target::common::EPARKStatusR2L::PARK_Failure)

  static cc::target::common::EPARKRecoverIndR2L g_curparkSuspend  = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt; // apply for both of horizontal and vertical
  static  vfc::uint16_t g_ref_counter = 0u; // apply for both of horizontal and vertical

  if ( (  g_curparkSuspend != l_curparkSuspend            ) &&
       (  ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand ) ||
          ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_ObjectOnPath ) ||
          ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_DoorOpen     ) ||
          ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_BrakePadal   ) ||
          ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_MirrorFold   ) ||
          ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_HoodOpen     ) ||
          ( l_curparkSuspend == cc::target::common::EPARKRecoverIndR2L::PARKREC_TrunkOpen    )    ) )
  {
    g_curparkSuspend = l_curparkSuspend;
  }
  else if ( l_curparkStatus != cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend)
  {
    g_curparkSuspend = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  }
  else
  {
    // do nothing
  }

  if (l_curparkDriverInd != cc::target::common::EPARKDriverIndR2L::PARKDRV_SearchingProcess)
  {
    g_ref_counter = 0u;
  }




    ////////////////////////////////////////////////////
   //////        OTHER EXCEPTIONS               ///////
  ////////////////////////////////////////////////////

  //Req_407
  if (  (l_curParkngTypeSeld      == cc::target::common::EParkngTypeSeld::PARKING_OUT)          &&
        (l_curParkAPARPAParkMode  == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)      &&
        (l_curparkDriverIndExt    == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_FrontIsClear) )
  {
      displayFunctionButton(f_framework, l_curAPADriverReq_Search, l_curParkngTypeSeld,
                                        l_curparkDriverIndExt, l_curParkAPARPAParkMode, l_curparkStatus);

      m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_FRONT_IS_CLEAR_PARKING_PATH)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR_PARKING_PATH)->setEnabled(true);
  }
  //Req_406
  else if ( (l_curParkngTypeSeld     == cc::target::common::EParkngTypeSeld::PARKING_OUT)   &&
            (l_curparkDriverInd      == cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop)  &&
            (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
  {
      displayFunctionButton(f_framework, l_curAPADriverReq_Search, l_curParkngTypeSeld,
                                        l_curparkDriverIndExt, l_curParkAPARPAParkMode, l_curparkStatus);

      displayParkOutSideTransButton(f_framework);
      m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_STOP)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_SEARCHING_POC_DIREC_SELECT_VEHICLE_TRANS)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_SEARCHING_POC_DIREC_SELECT_STEERING_WHEEL_TRANS)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_BOTTOM_TRANS)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_SEARCHING_SELECT_PARKOUT_DIRECTION)->setEnabled(true);
  }
  else
  {

      switch (l_curparkStatus)
      {
          ////////////////////////////////////////////////////
          //////        PARKING SEACHING                //////
          ////////////////////////////////////////////////////
          case cc::target::common::EPARKStatusR2L::PARK_Searching:

            {//Req_502
            if (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA || l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault))
            {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
            }
            //Req_595
            else if ( (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff) && (l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_TrailerHitchConnected) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
            {
              m_settingParkView.getIcon(PARK_QUIT_TEXT_TRAILER_HITCH_CONNECTED)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
              m_settingParkView.getIcon(PARK_QUIT_TRAILER_HITCH_CONNECTED)->setEnabled(true);
            }
            //Req_596
            else if ( (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff) && (l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_DriveModeUnsuitable) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
            {
              m_settingParkView.getIcon(PARK_QUIT_TEXT_DRIVE_MODE_UNSUITABLE)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
              m_settingParkView.getIcon(PARK_QUIT_DRIVE_MODE_UNSUITABLE)->setEnabled(true);
            }
            else if ( (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA) && (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT) )
            {
              // Req_566
              m_settingParkView.getIcon(RPA_TEXT_USE_PHONE)->setEnabled(true);
              m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
              m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
            }
            else
            {
                displayFunctionButton(f_framework, l_curAPADriverReq_Search, l_curParkngTypeSeld,
                                        l_curparkDriverIndExt, l_curParkAPARPAParkMode, l_curparkStatus);

                //Req_366
                if ( (l_curParkngTypeSeld       == cc::target::common::EParkngTypeSeld::PARKING_IN)      &&
                    (l_curParkAPARPAParkMode   == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) &&
                    (l_curAPADriverReq_Search  == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleSlowdown) )
                {
                    m_settingParkView.getIcon(PARKING_SEARCHING_TEXT_SLOW_DOWN)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_SEARCHING_SLOW_DOWN)->setEnabled(true);
                }
                // Req_398
                else if ((l_curparkDriverIndExt    == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ConfirmTheParkingDirection) &&
                        (l_curParkngTypeSeld      == cc::target::common::EParkngTypeSeld::PARKING_OUT)                           &&
                        (l_curParkAPARPAParkMode  == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA))
                {
                    displayParkOutSideButton(f_framework);
                    m_settingParkView.getIcon(PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_TOP)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_SEARCHING_POC_DIREC_SELECT_VEHICLE)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_SEARCHING_POC_DIREC_SELECT_STEERING_WHEEL)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_SEARCHING_TEXT_POC_DIREC_SELECT_BOTTOM)->setEnabled(true);
                }
                else
                {
                    switch (l_curparkDriverInd)
                    {

                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest: //Req_447
                    {
                      if ((l_curFreeParkingActive   == true)              &&
                          (l_curParkngTypeSeld      == cc::target::common::EParkngTypeSeld::PARKING_IN)        &&
                          (l_curparkSuspend         == cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt)  &&
                          l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest &&
                          (l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_UNKNOWN ||
                            l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL))
                      {
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_CHOOSE_PARKING_SPACE_TYPE)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_TOP)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_SELECTED_PARKING_INSTRUCTIONS_BUTTON)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_BOTTOM)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_CONFIRM_BUTTON)->setEnabled(true);
                          m_ViewButtonFreeParkingConfirmDispSts   = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                          m_ViewButtonFreeParkingSpaceTypeDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                      }
                      else if ((l_curFreeParkingActive   == true)              &&
                          (l_curParkngTypeSeld      == cc::target::common::EParkngTypeSeld::PARKING_IN)        &&
                          (l_curparkSuspend         == cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt)  &&
                          l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest &&
                          l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS)
                      {
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_CHOOSE_PARKING_SPACE_TYPE)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_TOP)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_SELECTED_CROSS_PARKING_INSTRUCTIONS_BUTTON)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_BOTTOM)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_CONFIRM_BUTTON)->setEnabled(true);
                          m_ViewButtonFreeParkingConfirmDispSts   = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                          m_ViewButtonFreeParkingSpaceTypeDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                      }
                      else if ((l_curFreeParkingActive   == true)              &&
                          (l_curParkngTypeSeld      == cc::target::common::EParkngTypeSeld::PARKING_IN)        &&
                          (l_curparkSuspend         == cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt)  &&
                          l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest &&
                          l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL)
                      {
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_CHOOSE_PARKING_SPACE_TYPE)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_TOP)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_SELECTED_DIAGONAL_PARKING_INSTRUCTIONS_BUTTON)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_BOTTOM)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_CONFIRM_BUTTON)->setEnabled(true);
                          m_ViewButtonFreeParkingConfirmDispSts   = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                          m_ViewButtonFreeParkingSpaceTypeDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                      }
                      else
                      {
                        //Do nothing
                      }
                      break;
                    }
                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_SearchingProcess: //Req_339
                    {
                      if ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
                      {
                          // m_settingParkView.getIcon(PARKING_SEARCHING_AUTO_PIC)->setEnabled(true);
                          m_settingParkView.getIcon(PARKING_SEARCHING_SLOT_SEARCHING)->setEnabled(true);
                          m_settingParkView.getIcon(PARKING_SEARCHING_AT_LOW_SPEED)->setEnabled(true);
                          // m_settingParkView.getIcon(PARKING_SEARCHING_ARROW)->setEnabled(true);

                      }
                      break;
                    }
                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_ResponseTimeout: //Req_365
                    {
                      if ( ((l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)||(l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT)) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA))
                      {
                          m_settingParkView.getIcon(PARKING_SEARCHING_TEXT_TIME_OUT)->setEnabled(true);
                          m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                          m_settingParkView.getIcon(PARKING_SEARCHING_TIME_OUT)->setEnabled(true);
                      }
                      break;
                    }
                    // move to l_curparkDriverInd case due to req new requirement change of 370
                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
                    {
                      if ( (l_curFreeParkingActive == true) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_449
                      {
                          m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_STOP)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_STOP)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_TEXT_DESCRIPTION_BOTTOM_TRANS)->setEnabled(true);
                          m_settingParkView.getIcon(FREE_PARKING_CONFIRM_BUTTON_GREY)->setEnabled(true);
                          if(l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_UNKNOWN ||
                              l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_PARALLEL)
                          {
                            m_settingParkView.getIcon(FREE_PARKING_SELECTED_PARKING_INSTRUCTIONS_BUTTON_GREY)->setEnabled(true);
                          }
                          else if(l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_CROSS)
                          {
                            m_settingParkView.getIcon(FREE_PARKING_SELECTED_CROSS_PARKING_INSTRUCTIONS_BUTTON_GREY)->setEnabled(true);
                          }
                          else if(l_curpFPparkSpaceType   == freeparking::EFreeParkingSpaceType::IPS_PS_TYPE_DIAGONAL)
                          {
                            m_settingParkView.getIcon(FREE_PARKING_SELECTED_DIAGONAL_PARKING_INSTRUCTIONS_BUTTON_GREY)->setEnabled(true);
                          }
                          else
                          {
                            //Do nothing
                          }
                      }
                      else if ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_370
                      {
                          m_settingParkView.getIcon(PARKING_SEARCHING_FIND_SLOT_STOP)->setEnabled(true);
                      }
                      else
                      {
                        // Do nothing
                      }

                      break;
                    }
                      // move to l_curparkDriverInd case due to req new requirement change of 384 and 385
                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:
                    {// requirement 384
                      if ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
                      {
                        m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_DOOR)->setEnabled(true);
#if PARKINGVIEW_VEHICLE2D_DOOR_WARNING
                        m_settingParkView.getIcon(PARKING_SEARCHING_TOP_VIEW)->setEnabled(true); // vehicle body
                        setDoorsTrunkHoodOpenLogic(l_curDoorState, true); // use vehicle 2D overlay for respective doors, include optional trunk and hood as argument
#else
                        m_settingParkView.getIcon(PARKING_SEARCHING_DOORS_OPEN)->setEnabled(true);
#endif
                      }
                      break;
                    }
                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror:
                    {// new requirement 385
                      if ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
                      {
                        m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_EXPANDED_MIRROR)->setEnabled(true);
                        m_settingParkView.getIcon(PARKING_COMFIRMING_EXPANDED_MIRROR)->setEnabled(true);
                      }
                      break;
                    }
                    case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
                    {
                      // handle by parking slot module

                      // This is a temp modification to handle the cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection as cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop to cover the state machine bug from APA.
                      // Final solution needs to be defined later
                      if ( l_curFreeParkingActive == false && (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
                      {
                        m_settingParkView.getIcon(PARKING_SEARCHING_FIND_SLOT_STOP)->setEnabled(true);
                      }
                      else
                      {
                        // do nothing
                      }

                      break;
                    }
                    default:
                    {
                      break;
                    }
                    }
                }
            }
            break;
          }

            ////////////////////////////////////////////////////
           //////        PARKING COMFIRMATION           ///////
          ////////////////////////////////////////////////////
          case cc::target::common::EPARKStatusR2L::PARK_AssistStandby:
          {//Req_502
            if (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA || l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault))
            {
              m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
            }
            //Req_593
            else if ( (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff) && (l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_TrailerHitchConnected) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
            {
              m_settingParkView.getIcon(PARK_QUIT_TEXT_TRAILER_HITCH_CONNECTED)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
              m_settingParkView.getIcon(PARK_QUIT_TRAILER_HITCH_CONNECTED)->setEnabled(true);
            }
            //Req_594
            else if ( (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_FunctionOff) && (l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_DriveModeUnsuitable) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
            {
              m_settingParkView.getIcon(PARK_QUIT_TEXT_DRIVE_MODE_UNSUITABLE)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
              m_settingParkView.getIcon(PARK_QUIT_DRIVE_MODE_UNSUITABLE)->setEnabled(true);
            }
            else if (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_ResponseTimeout)  //Req_505, //Req_487
            {
              m_settingParkView.getIcon(RPA_TEXT_DRIVER_REPONSE_TIMEOUT)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
              m_settingParkView.getIcon(PARKING_ASSIST_STANDBY_RESPONSE_TIMEOUT)->setEnabled(true);
              displayParkinAPARPAButton(f_framework);
            }
            else if (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
            {
              displayFunctionButton(f_framework, l_curAPADriverReq_Search, l_curParkngTypeSeld,
                                  l_curparkDriverIndExt, l_curParkAPARPAParkMode, l_curparkStatus);

              switch (l_curParkngTypeSeld)
              {
              case cc::target::common::EParkngTypeSeld::PARKING_NONE:
              {// do nothing
                break;
              }
              case cc::target::common::EParkngTypeSeld::PARKING_IN:
              {
                switch (l_curparkDriverInd)
                {
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:   //Req_493
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_DOOR)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SEARCHING_DOORS_OPEN)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror:  //Req_496
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_EXPANDED_MIRROR)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_COMFIRMING_EXPANDED_MIRROR)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk:  //Req_386
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_TRUNK)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TRUNK_OPEN)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood:  //Req_387
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_HOOD)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_HOOD_OPEN)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt:  //Req_388
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_SEAT_BELT)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM: //Req_369, Req_377, Req_378, Req_382
                {
                  m_settingParkView.getIcon(START_PARKING_PARKING_IN)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN)->setEnabled(true);
                  m_ViewButtonParkStartDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot: //Req_371
                {
                  m_settingParkView.getIcon(START_PARKING_PARKING_IN)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_SMALL_PARK_SLOT)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_COMFIRMING_SMALL_PARK_SLOT)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN)->setEnabled(true);
                  m_ViewButtonParkStartDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                  break;
                }
                default:
                {
                  break;
                }
                }
                // Req_389
                if ( l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear )
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_PRESS_BRAKE_PEDAL)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_IN_DEACTIVATE)->setEnabled(true);
                }
                break;
              }
              case cc::target::common::EParkngTypeSeld::PARKING_OUT:
              {
                switch (l_curparkDriverInd)
                {
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor: //Req_403
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_DOOR)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SEARCHING_DOORS_OPEN)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror: //Req_497
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_EXPANDED_MIRROR)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_COMFIRMING_EXPANDED_MIRROR)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk: //Req_404
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_TRUNK)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TRUNK_OPEN)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood: //Req_400
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_HOOD)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_HOOD_OPEN)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt: //Req_401
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_SEAT_BELT)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM: //Req_463
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_HOLD_BRAKE_AND_START_PARKING)->setEnabled(true);
                  m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_OUT)->setEnabled(true);
                  m_ViewButtonParkStartDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
                  break;
                }
                default:
                {
                  break;
                }
                }
                // Req_405
                if ( l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear )
                {
                    m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_KEEP_BRAKE_PEDAL)->setEnabled(true);
                    m_settingParkView.getIcon(START_PARKING_BUTTON_START_PARK_OUT_DEACTIVATE)->setEnabled(true);
                }
                break;
              }
              default:
              {
                break;
              }
              }
            }
            else if ( (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA) && (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN) )
            {
              displayFunctionButton(f_framework, l_curAPADriverReq_Search, l_curParkngTypeSeld,
                                    l_curparkDriverIndExt, l_curParkAPARPAParkMode, l_curparkStatus);

              // During parking out in RPA mode, there are no driver stays in vehicle -> no UI is required
              if (l_curParkRPAAvaliable == cc::target::common::RPAAvailable::RPA_NotAvailable) //Req_479
              {
                  m_settingParkView.getIcon(RPA_TEXT_REMOTE_PARKING_IS_NOT_AVAILABLE)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
              }
              else if ( (l_curParkRPAAvaliable == cc::target::common::RPAAvailable::RPA_Available) && (l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitingForVehicleStop) )  //Req_485
              {
                  m_settingParkView.getIcon(RPA_TEXT_PLEASE_PARKING)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
              }
              else if (l_curParkRPAAvaliable == cc::target::common::RPAAvailable::RPA_Available)
              {
                switch (l_curparkDriverInd)
                {
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:  //Req_481
                {
                  m_settingParkView.getIcon(RPA_TEXT_CLICK_ON_THE_PHONE_TO_START_PARKING)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror:  //Req_482
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_EXPANDED_MIRROR)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_ConnectPhone:  //Req_483
                {
                  m_settingParkView.getIcon(RPA_TEXT_OPEN_THE_APP_AND_CONNECT_TO_BLUETOOTH)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:  //Req_504
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_DOOR)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk:  //Req_488
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_TRUNK)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood:  //Req_503
                {
                  m_settingParkView.getIcon(PARKING_COMFIRMING_TEXT_CLOSE_HOOD)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_EPBApplied:  //Req_486
                case cc::target::common::EPARKDriverIndR2L::PARKDRV_LeaveCar:
                {
                  m_settingParkView.getIcon(RPA_TEXT_BLUETOOTH_CONNECTED)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
                  m_settingParkView.getIcon(RPA_TEXT_BLUETOOTH_CONNECTED_PROMPT)->setEnabled(true);
                  break;
                }
                default:
                {break;
                }
                }
              }
              else
              {
                // Do nothing
              }

            }
            else if ( (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA) && (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT) )
            {
              // Req_566
              m_settingParkView.getIcon(RPA_TEXT_USE_PHONE)->setEnabled(true);
              m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
              m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
            }
            else
            {
              // Do nothing
            }
            break;
          }

            ////////////////////////////////////////////////////
           //////        PARKING GUIDANCE               ///////
          ////////////////////////////////////////////////////
          case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
          {
            if (cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA == l_curParkAPARPAParkMode) //Req_480
            {
              m_settingParkView.getIcon(RPA_TEXT_REMOTE_PARKING)->setEnabled(true);
              m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
              m_settingParkView.getIcon(RPA_TEXT_PLEASE_LEAVE_THE_CAR)->setEnabled(true);
            }
            else
            {
              switch (l_curParkngTypeSeld)
              {
              case cc::target::common::EParkngTypeSeld::PARKING_NONE:
              {
                break;
              }
              case cc::target::common::EParkngTypeSeld::PARKING_IN:
              {
                if (l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)) //Req_390
                {
                    // This has higher priority than PayAttentionToSurrounding
                    m_settingParkView.getIcon(PARKING_GUIDANCE_TEXT_RELEASE_BRAKE_AND_STEERING)->setEnabled(true);
                }
                else if ( (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_391
                {
                    displayParkPauseButton();
                    displayGear(f_framework);
                    m_settingParkView.getIcon(PARKING_GUIDANCE_NOTICE_SUR)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_GUIDANCE_CONTINUE_DRIVE_DISTANCE)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_GUIDANCE_MOVES_LEFT_NUMBER)->setEnabled(true);
                    l_dynamicGearStatus = true;
                }
                else if ( (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_ResponseTimeout) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_???
                {
                    m_settingParkView.getIcon(RPA_TEXT_DRIVER_REPONSE_TIMEOUT)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
                }
                else
                {
                    // DoNothing
                }
                break;
              }
              case cc::target::common::EParkngTypeSeld::PARKING_OUT:
              {
                if ( (l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_396
                {
                    // This has higher priority than PayAttentionToSurrounding
                    displayFunctionButton(f_framework, l_curAPADriverReq_Search, l_curParkngTypeSeld,
                                  l_curparkDriverIndExt, l_curParkAPARPAParkMode, l_curparkStatus);
                    m_settingParkView.getIcon(PARKING_GUIDANCE_TEXT_RELEASE_BRAKE_AND_STEERING)->setEnabled(true);
                }
                else if ( (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_397
                {
                    displayGear(f_framework);
                    displayParkPauseButton();
                    m_settingParkView.getIcon(PARKING_GUIDANCE_PARKINGOUT_PAYATTENTION_ENV)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_GUIDANCE_CONTINUE_DRIVE_DISTANCE)->setEnabled(true);
                    // m_settingParkView.getIcon(PARKING_GUIDANCE_MOVES_LEFT_NUMBER)->setEnabled(true);
                    l_dynamicGearStatus = true;
                }
                else if ( (l_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_ResponseTimeout) && (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) ) //Req_???
                {

                    m_settingParkView.getIcon(RPA_TEXT_DRIVER_REPONSE_TIMEOUT)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                    m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
                }
                else
                {
                  // DoNothing
                }
                break;
              }
              default:
              {
                break;
              }
              }
            }
            break;
          }

            ////////////////////////////////////////////////////
           //////        PARKING RECOVERY INTERRRUPT    ///////
          ////////////////////////////////////////////////////
          case cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend:
          {
            if ( ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)||(l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT) ) &&
                  (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
            {
                switch (g_curparkSuspend)
                {
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt:
                {// do nothing
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand:
                {
                  displayContentOfGuidanceSuspend(f_framework);
                  displayContinueButton(l_curparkDriverInd); //Req_459
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_USER_PAUSE)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_ObjectOnPath: //Req_437
                {
                  displayContentOfGuidanceSuspend(f_framework);
                  displayContinueButton(l_curparkDriverInd); //Req_459
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_OBJECT_ON_PATH)->setEnabled(true);
                  // m_settingParkView.getIcon(PARKING_SUSPEND_OBJECT_ON_PATH)->setEnabled(true); // use icon inside vehicle2d
                  m_settingParkView.getIcon(PARKING_SUSPEND_OBJECT_ON_PATH_ICON)->setEnabled(true); // use icon in ui submodule
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_DoorOpen: //Req_441
                {
                  displayContentOfGuidanceSuspend(f_framework); //Req_459
                  displayContinueButton(l_curparkDriverInd);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_DOOR_OPEN)->setEnabled(true);
#if PARKINGVIEW_VEHICLE2D_DOOR_WARNING
                  m_settingParkView.getIcon(PARKING_SEARCHING_TOP_VIEW)->setEnabled(true);
                  setDoorsTrunkHoodOpenLogic(l_curDoorState, false);
#else
                  m_settingParkView.getIcon(PARKING_SEARCHING_DOORS_OPEN)->setEnabled(true);
#endif
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_MirrorFold: //Req_440
                {
                  displayContentOfGuidanceSuspend(f_framework); //Req_459
                  displayContinueButton(l_curparkDriverInd);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_MIRROR_FOLD)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_MIRROR_FOLD)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_HoodOpen: //Req_442
                {
                  displayContentOfGuidanceSuspend(f_framework); //Req_459
                  displayContinueButton(l_curparkDriverInd);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_HOOD_OPEN)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_HOOD_OPEN)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_TrunkOpen: //Req_439 and //Req_498
                {
                  displayContentOfGuidanceSuspend(f_framework); //Req_459
                  displayContinueButton(l_curparkDriverInd);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_TRUNK_OPEN)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TRUNK_OPEN)->setEnabled(true);
                  break;
                }
                case cc::target::common::EPARKRecoverIndR2L::PARKREC_BrakePadal: // Req 444 // PRQA S 4011
                {
                  displayContentOfGuidanceSuspend(f_framework); //Req_459
                  displayContinueButton(l_curparkDriverInd);
                  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_BRAKE_PEDAL)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_SUSPEND_BRAKE_PEDAL)->setEnabled(true);
                }
                default:
                {
                  break;
                }
                }
            }
            else if( cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA == l_curParkAPARPAParkMode )
            {
              if (cc::target::common::EPARKRecoverIndR2L::PARKREC_BlueTooth == l_curparkSuspend)  //Req_524
              {
                m_settingParkView.getIcon(RPA_TEXT_BLUETOOTH_DISCONNECT)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_CONFIRMING_TEXT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_DISCONNECT)->setEnabled(true);
              }
              else  //Req_521
              {
                m_settingParkView.getIcon(RPA_TEXT_SUSPEND)->setEnabled(true);
                m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
              }
            }
            else
            {
              //Do nothing
            }
            break;
          }

            ////////////////////////////////////////////////////
           //////        PARKING TERMINATED             ///////
          ////////////////////////////////////////////////////
          case cc::target::common::EPARKStatusR2L::PARK_Terminated:
          {
            if ( ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)||(l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT) ) &&
                  (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
            {
              switch (l_curparkQuitInd)
              {
                //Req_492
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_None:
              {
                if (l_curParkQuitIndExt == cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_Nofault)
                {
                  m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                  m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
                }
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_UnsafeBehavior:
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_DoorOpen:
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_OtherReason:
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SeatBeltUnbuckle: //Req_419
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_BELT_UNBUCKLE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_BELT_UNBUCKLE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ExcessiveSlope: //Req_420
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_EXCESSIVE_SLOP)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_EXCESSIVE_SLOP)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_DriverOverride: //Req_418
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_DRIVER_OVERRIDE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_DRIVER_OVERRIDE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_RoutePlanningFailure: //Req_414
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_ROUTE_PLANNING_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_ROUTE_PLANNING_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_VehicleSpeedOverthreshold: //Req_415
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_VEHICLE_SPEED_OVER_THRESHOLD)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_VEHICLE_SPEED_OVER_THRESHOLD)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_APSTimeout: //Req_417
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_APS_TIMEOUT)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_APS_TIMEOUT)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_CurrentStepNumberOverThreshold: //Req_421
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_CURRENT_STEP_NUMBER_OVER_THRESHOLD)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_CURRENT_STEP_NUMBER_OVER_THRESHOLD)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SpaceIsLimitedInParkOutMode: //Req_423
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_SPACE_IS_LIMITED_IN_PARK_OUT_MODE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_SPACE_IS_LIMITED_IN_PARK_OUT_MODE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_EPBFailure: //Req_422
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_EPB_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_EPB_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_SCUFailure: //Req_427
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_SCU_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_SCU_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_APAFailure: //Req_428
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_APA_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_APA_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_InvalidVehicleSpeed: //Req_410
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ExternalECUFailure: //Req_410
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_EXTERNAL_ECU_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_EXTERNAL_ECU_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ABSTCSESPACCAEBActive: //Req_424
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_ABS_TCS_ESP_ACC_AEB_ACTIVE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_ABS_TCS_ESP_ACC_AEB_ACTIVE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_ESCFailure: //Req_425
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_ESC_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_ESC_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_EPSFailure: //Req_429
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_TEXT_EPS_FAILURE)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_EPS_FAILURE)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_VehicleBlock: //Req_509
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_TEXT_VEHICLE_BLOCK)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_VEHICLE_BLOCK)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_InterruptNumberOverThreshold: //Req_507
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_TEXT_INTERRUPT_NUMBER_OVER_THRESHOLD)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_INTERRUPT_NUMBER_OVER_THRESHOLD)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_RadarDirty: //Req_508
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_TEXT_RADAR_DIRTY)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_RADAR_DIRTY)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2L::PARKQUIT_EPBActive: //Req_506
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_TEXT_EPB_ACTIVE)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_EPB_ACTIVE)->setEnabled(true);
                break;
              }
              default:
              {break;
              }
              }
              switch (l_curParkQuitIndExt)
              {
              case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_TrailerHitchConnected:  //Req_591
              {
                m_settingParkView.getIcon(PARK_QUIT_TEXT_TRAILER_HITCH_CONNECTED)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_TRAILER_HITCH_CONNECTED)->setEnabled(true);
                break;
              }
              case cc::target::common::EPARKQuitIndR2LExt::PARKQUITExt_DriveModeUnsuitable:  //Req_592
              {
                m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_TEXT_DRIVE_MODE_UNSUITABLE)->setEnabled(true);
                m_settingParkView.getIcon(PARK_QUIT_DRIVE_MODE_UNSUITABLE)->setEnabled(true);
                break;
              }
              default:
              {
                break;
              }
              }
            }
            else if( cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA == l_curParkAPARPAParkMode )  // Req_522
            {
                m_settingParkView.getIcon(RPA_TEXT_TERMINATE)->setEnabled(true);
                m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
            }
            else
            {
              //Do nothing
            }
            break;
          }

            ////////////////////////////////////////////////////
           //////        PARKING COMPLETED              ///////
          ////////////////////////////////////////////////////
          case cc::target::common::EPARKStatusR2L::PARK_Completed: //Req_394, Req_395
          {
            if ( ( (l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)||(l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT) ) &&
                  (l_curParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)                                 &&
                  (l_curparkDriverIndExt   == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted) )
            {
                m_settingParkView.getIcon(PARKING_COMPLETE_FINISHED)->setEnabled(true);
                m_settingParkView.getIcon(PARKING_COMPLETE_TAKEOVER)->setEnabled(true);
            }
            else if ( l_curParkAPARPAParkMode  == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA )  //Req_484
            {
                m_settingParkView.getIcon(PARKING_COMPLETE_FINISHED)->setEnabled(true);
                m_settingParkView.getIcon(RPA_OTHERS_BLUETOOTH_MOBILE_PHONE)->setEnabled(true);
            }
            else
            {
                // Do nothing
            }
            break;
          }
          case cc::target::common::EPARKStatusR2L::PARK_Failure: //Req_???:
          {
            m_settingParkView.getIcon(PARKING_QUIT_APA_QUIT_TAKE_OVER)->setEnabled(true);
            m_settingParkView.getIcon(PARKING_COMFIRMING_FRONT_IS_CLEAR)->setEnabled(true);
            break;
          }
          default:
          {
            break;
          }
      }
  }
  updateIcons(f_imageOverlays);
  // Deliver button's diplay status to SM
  this->DeliverButtonDispSts(f_framework); // PRQA S 3804
  if (cc::daddy::CustomDaddyPorts::sm_dynamicGearStatus_SenderPort.isConnected())
  {
    cc::daddy::DynamicGearActive_t& l_container = cc::daddy::CustomDaddyPorts::sm_dynamicGearStatus_SenderPort.reserve();
    l_container.m_Data          = static_cast<bool>(l_dynamicGearStatus);
    cc::daddy::CustomDaddyPorts::sm_dynamicGearStatus_SenderPort.deliver();
  }
}

void ParkingViewManager::updateIcons(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  //! Removed all not in use icon first for better memory consumption
  for (vfc::int32_t i = 0; i < ParkingProgressType::NUMBER_OF_ICON; i++)
  {
    CustomIconData* const l_currIconData = m_settingParkView.getIcon(static_cast<ParkingProgressType>(i));
    if (!l_currIconData->isEnabled())
    {
      if(l_currIconData->isAdded())
      {
        // remove icon from image overlay
        f_imageOverlays->removeIcon(l_currIconData->getIconPtr());
        l_currIconData->setAdded(false);
        // pf code. #code looks fine
        XLOG_DEBUG(g_AppContext, "Removed icon [" << l_currIconData->getFilePath()  << "]");
      }
    }
  }

  //! Check for enabled icon
  for (vfc::int32_t i = 0; i < ParkingProgressType::NUMBER_OF_ICON; i++)
  {
    CustomIconData* const l_currIconData = m_settingParkView.getIcon(static_cast<ParkingProgressType>(i));
    if (l_currIconData->isEnabled())
    {
      if (!l_currIconData->isAdded())
      {
        // add icon to image overlay
        pc::assets::Icon* const l_newIcon = createIcon(l_currIconData->getFilePath(), l_currIconData->getIconPos(), l_currIconData->getIconSize(), true);
        l_newIcon->setEnabled(true);
        l_currIconData->setIconPtr(l_newIcon);
        f_imageOverlays->addIcon(l_newIcon);
        l_currIconData->setAdded(true);
        // pf code. #code looks fine
        XLOG_DEBUG(g_AppContext, "Add icon [" << l_currIconData->getFilePath()  << "]");
      }
    }
  }
}

void ParkingViewManager::clearIcon()
{
  m_settingParkView.setAllEnabled(false);
}


//!
//! @brief Construct a new ParkingView:: ParkingView object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ParkingView::ParkingView(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_HoriManager{}
  , m_theme{cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE}
{

  pc::util::coding::CodingManager* const l_codingManager = pc::util::coding::getCodingManager();
  cc::assets::uielements::UISettings* const l_uiSettings = dynamic_cast<cc::assets::uielements::UISettings*>(l_codingManager->getItem("UIElements")); // PRQA S 3077   // PRQA S 3400

  l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
  l_uiSettings->dirty();
  m_HoriManager.init(this);

  // l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
  // l_uiSettings->dirty();
  // m_VertManager.init(this);

  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


ParkingView::~ParkingView() = default;


void ParkingView::traverse(osg::NodeVisitor& f_nv) // PRQA S 6043
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // ! update theme

    if(m_customFramework->m_SVSRotateStatusDaddy_Receiver.hasData())
    {
      const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
      const cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899

      if (l_curThemeType != m_theme)
      {
          //XLOG_INFO_OS(g_AppContext) << "[THEME] UIElements start theme update" << XLOG_ENDL;

          if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
          {
            // m_VertManager.clearIcon();
          }
          else if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
          {
            m_HoriManager.clearIcon();
          }
          else // cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE
          {
            // default is horizontal
            // m_VertManager.clearIcon();
          }

          m_theme = l_curThemeType;

          //XLOG_INFO_OS(g_AppContext) << "[THEME] UIElements finish theme update" << XLOG_ENDL;
      }
      else
      {
        // Do nothing
      }
    }

    if (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
      m_HoriManager.update(this, m_customFramework);
    }
    else if (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
    {
      // m_VertManager.update(this, m_customFramework);
    }
    else // cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE
    {
      // default is horizontal
      m_HoriManager.update(this, m_customFramework);
    }

  }
  pc::assets::ImageOverlays::traverse(f_nv);
}

void ParkingViewManager::displayGear(const core::CustomFramework* f_framework)
{
if (f_framework == nullptr)
{
    return;
}
if (f_framework->m_gearReceiver.hasData())
{
    const pc::daddy::GearDaddy* const l_parkGear = f_framework->m_gearReceiver.getData();
    const EGear l_curparkGear = static_cast<EGear>(l_parkGear->m_Data);
    switch (static_cast<vfc::int32_t>(l_curparkGear))
    {
    case pc::daddy::GEAR_D:
    {
      m_settingParkView.getIcon(PARKING_GUIDANCE_GEAR_D)->setEnabled(true);
      break;
    }
    case pc::daddy::GEAR_N:
    {
      m_settingParkView.getIcon(PARKING_GUIDANCE_GEAR_N)->setEnabled(true);
      break;
    }
    case pc::daddy::GEAR_R:
    {
      m_settingParkView.getIcon(PARKING_GUIDANCE_GEAR_R)->setEnabled(true);
      break;
    }
    case pc::daddy::GEAR_P:
    {
      m_settingParkView.getIcon(PARKING_GUIDANCE_GEAR_P)->setEnabled(true);
      break;
    }
    default:
    {
      break;
    }
    }
}
}

void ParkingViewManager::displayFunctionSelectionButton(const core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return;
  }

  m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN)->setEnabled(true);

  m_ViewButtonParkModeDispSts = cc::daddy::PARK_MODE_DISP2TOUCH_PARKIN_PARKOUT_FREEPARKING_AVAILABLE;

  bool l_FreeParkingActive = false;


  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
    l_FreeParkingActive = l_pFreeparkingActiveButton->m_Data;

    if (l_FreeParkingActive == true)
    {
      m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_FREE_PARKING)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN)->setEnabled(false);
    }
  }

  if ((l_FreeParkingActive == false) && f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    const cc::target::common::EParkngTypeSeld l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
    switch (l_curParkngTypeSeld)
    {
    case cc::target::common::EParkngTypeSeld::PARKING_NONE:
    {
      break;
    }
    case cc::target::common::EParkngTypeSeld::PARKING_IN:
    {
      break;
    }
    case cc::target::common::EParkngTypeSeld::PARKING_OUT:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKOUT)->setEnabled(true);
      m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN)->setEnabled(false);
      break;
    }
    default:
    {
      break;
    }

    }
  }
}

void ParkingViewManager::displayParkinAPARPAButton(const core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return;
  }
  //default
  m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_APA)->setEnabled(true);

  m_ViewButtonParkModeDispSts = cc::daddy::PARK_MODE_DISP2TOUCH_APA_RPA_AVAILABLE;

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    const cc::target::common::EAPAPARKMODE l_curAPAPARKMODE = l_parkAPAPARKMODE->m_Data;
    switch (l_curAPAPARKMODE)
    {
      case cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE:
      {
        break;
      }
      case cc::target::common::EAPAPARKMODE::APAPARKMODE_APA:
      {
        break;
      }
      case cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA:
      {
        m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_RPA)->setEnabled(true);
        m_settingParkView.getIcon(PARKING_BUTTON_FUNCTION_SELECTION_PARKIN_APA)->setEnabled(false);
        break;
      }
      default:
      {
        break;
      }

    }
  }

}

bool ParkingViewManager::isAvailableParkingSlot(const core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return false;
  }
  if (f_framework->m_parkAPASlotsReceiver.hasData())
  {
    const cc::daddy::ParkAPA_ParkSpace_t* const l_parkAPASlots = f_framework->m_parkAPASlotsReceiver.getData();

    for(vfc::uint16_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ ) // PRQA S 4687
    {
        for(vfc::uint16_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
        {
            const cc::target::common::EPARKSlotStsR2L l_curAPASlotsAvailable = l_parkAPASlots->m_Data[l_side][l_numberPerside].m_APA_PrkgSlot;
            if ( (l_curAPASlotsAvailable == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE) ||
                  (l_curAPASlotsAvailable == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED))
            {
              cc::daddy::ParkUIAvailableParkingSlot_t& l_ParkUIAPASlotsAvailable =
                           cc::daddy::CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort.reserve() ;
              l_ParkUIAPASlotsAvailable.m_Data = true;
              cc::daddy::CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort.deliver() ;

              return true;
            }
        }
    }

  }

  cc::daddy::ParkUIAvailableParkingSlot_t& l_ParkUIAPASlotsAvailable =
               cc::daddy::CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort.reserve() ;
  l_ParkUIAPASlotsAvailable.m_Data = false;
  cc::daddy::CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort.deliver() ;

  return false;
}


void ParkingViewManager::displayFunctionButton(const core::CustomFramework* f_framework, cc::target::common::EPARKDriverIndSearchR2L /*f_curAPADriverReq_Search*/,
                                                cc::target::common::EParkngTypeSeld f_curParkngTypeSeld, cc::target::common::EPARKDriverIndExtR2L /*f_curparkDriverIndExt*/,
                                                cc::target::common::EAPAPARKMODE f_ParkAPARPAParkMode, cc::target::common::EPARKStatusR2L f_curparkStatus)
{
  if (f_framework == nullptr)
  {
      return;
  }
  // this function only be called during searching, confirmation and parking guidance
  //Req_491
  const bool l_isAvailableSlot = isAvailableParkingSlot(f_framework);

  vfc::float32_t l_curSpeed = 0.0f;

  if (f_framework->m_speedReceiver.hasData())
  {
    const pc::daddy::SpeedDaddy* const l_speedDaddy = f_framework->m_speedReceiver.getData();
    l_curSpeed = l_speedDaddy->m_Data;
  }

  if (f_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)
  {
    // From the park HMI: if there is one slot is available, it will jump to confirmation state automaticly
    // the APA-RPA button will not show
    if ( (l_isAvailableSlot && (f_ParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) && isLess(l_curSpeed,1.0f)) ||
         ((f_ParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_RPA) && (f_curparkStatus == cc::target::common::EPARKStatusR2L::PARK_AssistStandby)) )
    {
      displayParkinAPARPAButton(f_framework);
    }
    else
    {
      displayFunctionSelectionButton(f_framework);
    }
  }
  else if ( (f_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT) && (f_ParkAPARPAParkMode == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA) )
  {
    displayFunctionSelectionButton(f_framework);
  }
  else
  {
    // Do not show the button
  }

}

void ParkingViewManager::displayParkOutSideButton(const core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return;
  }

  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_UNSELECT)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_UNSELECT)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_UNSELECT)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_UNSELECT)->setEnabled(true);

  m_ViewButtonParkOutDirectionDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;

  if (f_framework->m_parkHmiParkingSideReceiver.hasData())
  {
    const cc::daddy::ParkSideDaddy_t* const l_parkParkingSide = f_framework->m_parkHmiParkingSideReceiver.getData();
    const cc::target::common::EPARKSideR2L l_curParkingSide = l_parkParkingSide->m_Data;
    switch (l_curParkingSide)
    {
    case cc::target::common::EPARKSideR2L::PARKSIDE_None:
    {
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Left:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_SELECTED)->setEnabled(true);
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Right:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_SELECTED)->setEnabled(true);
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Left:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_SELECTED)->setEnabled(true);
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Right:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_SELECTED)->setEnabled(true);
      break;
    }
    default:
    {
      break;
    }

    }
  }

}

void ParkingViewManager::displayParkOutSideTransButton(const core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return;
  }

  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_UNSELECT_TRANS)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_UNSELECT_TRANS)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_UNSELECT_TRANS)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_UNSELECT_TRANS)->setEnabled(true);

  m_ViewButtonParkOutDirectionDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;

  if (f_framework->m_parkHmiParkingSideReceiver.hasData())
  {
    const cc::daddy::ParkSideDaddy_t* const l_parkParkingSide = f_framework->m_parkHmiParkingSideReceiver.getData();
    const cc::target::common::EPARKSideR2L l_curParkingSide = l_parkParkingSide->m_Data;
    switch (l_curParkingSide)
    {
    case cc::target::common::EPARKSideR2L::PARKSIDE_None:
    {
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Left:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_LEFT_SELECTED_TRANS)->setEnabled(true);
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Right:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_PARALLEL_RIGHT_SELECTED_TRANS)->setEnabled(true);
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Left:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_LEFT_SELECTED_TRANS)->setEnabled(true);
      break;
    }
    case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Right:
    {
      m_settingParkView.getIcon(PARKING_BUTTON_PARKOUT_SIDE_CROSS_RIGHT_SELECTED_TRANS)->setEnabled(true);
      break;
    }
    default:
    {
      break;
    }

    }
  }

}

void ParkingViewManager::displayContinueButton(cc::target::common::EPARKDriverIndR2L f_curparkDriverInd)
{

  if (f_curparkDriverInd == cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM)
  {
     m_settingParkView.getIcon(PARKING_SUSPEND_BUTTON_CONTINUE)->setEnabled(true);
     m_ViewButtonParkContinueDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;

  }
  else
  {
    m_settingParkView.getIcon(PARKING_SUSPEND_BUTTON_CONTINUE_GRAY)->setEnabled(true);
    m_ViewButtonParkContinueDispSts = cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  }

}

void ParkingViewManager::displayParkPauseButton()
{
  m_ViewButtonParkPauseDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
  m_settingParkView.getIcon(PARKING_GUIDANCE_BUTTON_SUSPEND)->setEnabled(true);
}

void ParkingViewManager::displayContentOfGuidanceSuspend(const core::CustomFramework* /*f_framework*/)
{
  m_ViewButtonParkQuitDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;

  m_settingParkView.getIcon(PARKING_SUSPEND_TEXT_PARKING_PAUSE)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_SUSPEND_QUIT_IN30S)->setEnabled(true);
  m_settingParkView.getIcon(PARKING_BUTTON_QUIT)->setEnabled(true);

}

void ParkingViewManager::CleanButtonDispSts()
{
  m_ViewButtonParkModeDispSts              =  cc::daddy::PARK_MODE_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkStartDispSts             =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkContinueDispSts          =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkPauseDispSts             =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkQuitDispSts              =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkOutDirectionDispSts      =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonFreeParkingConfirmDispSts    =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonFreeParkingSpaceTypeDispSts  =  cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
}

bool ParkingViewManager::DeliverButtonDispSts(const core::CustomFramework* /*f_framework*/) // PRQA S 4211
{

  cc::daddy::ParkDisp2TouchStsDaddy_t& l_ParkDisp2TouchSts =
               cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.reserve() ;


  l_ParkDisp2TouchSts.m_Data.m_ButtonParkModeDispSts              =  m_ViewButtonParkModeDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkStartDispSts             =  m_ViewButtonParkStartDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkContinueDispSts          =  m_ViewButtonParkContinueDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkPauseDispSts             =  m_ViewButtonParkPauseDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkQuitDispSts              =  m_ViewButtonParkQuitDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkOutDirectionDispSts      =  m_ViewButtonParkOutDirectionDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonFreeParkingConfirmDispSts    =  m_ViewButtonFreeParkingConfirmDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonFreeParkingSpaceTypeDispSts  =  m_ViewButtonFreeParkingSpaceTypeDispSts;


  cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.deliver() ;

  return false;
}

/**
 * @brief check doors open logic and enable respective icons
 *
 * @param f_curDoorState door state array
 * @param f_checkTrunk   flag to enable check trunk and hood
 */
void ParkingViewManager::setDoorsTrunkHoodOpenLogic(vfc::uint8_t  f_curDoorState[pc::daddy::NUMBER_OF_CARDOORS], bool f_checkTrunk)
{
  if(pc::daddy::CARDOORSTATE_OPEN == f_curDoorState[pc::daddy::CARDOOR_FRONT_RIGHT])
  {
    m_settingParkView.getIcon(PARKING_SEARCHING_DOOR_FRONT_RIGHT_OPEN)->setEnabled(true);
  }
  if(pc::daddy::CARDOORSTATE_OPEN == f_curDoorState[pc::daddy::CARDOOR_FRONT_LEFT])
  {
    m_settingParkView.getIcon(PARKING_SEARCHING_DOOR_FRONT_LEFT_OPEN)->setEnabled(true);
  }
  if(pc::daddy::CARDOORSTATE_OPEN == f_curDoorState[pc::daddy::CARDOOR_TRUNK] && f_checkTrunk)
  {
    m_settingParkView.getIcon(PARKING_SEARCHING_TRUNK_OPEN)->setEnabled(true);
  }
  if(pc::daddy::CARDOORSTATE_OPEN == f_curDoorState[pc::daddy::CARDOOR_REAR_RIGHT])
  {
    m_settingParkView.getIcon(PARKING_SEARCHING_DOOR_REAR_RIGHT_OPEN)->setEnabled(true);
  }
  if(pc::daddy::CARDOORSTATE_OPEN == f_curDoorState[pc::daddy::CARDOOR_REAR_LEFT])
  {
    m_settingParkView.getIcon(PARKING_SEARCHING_DOOR_REAR_LEFT_OPEN)->setEnabled(true);
  }
  if(pc::daddy::CARDOORSTATE_OPEN == f_curDoorState[pc::daddy::CARDOOR_HOOD] && f_checkTrunk)
  {
    m_settingParkView.getIcon(PARKING_SEARCHING_HOOD_OPEN)->setEnabled(true);
  }
}

// ! enum values for parking modes
enum class ParkingBackgroundType : vfc::uint32_t
{
  // parking searching
  PARKING_SEARCHING_BACKGROUND,

};

//!
//! @brief Construct a new ParkingViewBackground Manager:: ParkingViewBackground Manager object
//!
//! @param f_config
//!
ParkingViewBackgroundManager::ParkingViewBackgroundManager() = default;
  //: m_lastConfigUpdate(~0u)



// ! transfer to start from bottom left
osg::Vec2f ParkingViewBackgroundManager::transferToBottomLeft(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(f_iconPos.x() - static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_origin.x()), static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.y()) - f_iconPos.y());
}

ParkingViewBackgroundManager::~ParkingViewBackgroundManager() = default;

void ParkingViewBackgroundManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_settingBackgroundParkView.clear(f_imageOverlays);
  m_settingBackgroundParkView.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkingUIBackground,                      g_uiSettings->m_settingParkingUIBackground.m_iconCenter,                            g_uiSettings->m_settingParkingUIBackground.m_iconSize,                       g_uiSettings->m_settingParkingUIBackground.m_isHoriScreen));
}

void ParkingViewBackgroundManager::update(const cc::assets::uielements::CustomImageOverlays* /*f_imageOverlays*/, const core::CustomFramework* /*f_framework*/)    // PRQA S 6043 // PRQA S 6040
{
  // ! check if config has changed
  // if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  // {
  //   init(f_imageOverlays);
  //   m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  // }

  m_settingBackgroundParkView.setAllEnabled(false);
  m_settingBackgroundParkView.getIcon(static_cast<unsigned>(ParkingBackgroundType::PARKING_SEARCHING_BACKGROUND))->setEnabled(true);

}

void ParkingViewBackgroundManager::clearIcon()
{
  m_settingBackgroundParkView.setAllEnabled(false);
}


//!
//! @brief Construct a new ParkingViewBackground:: ParkingViewBackground object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ParkingViewBackground::ParkingViewBackground(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_HoriBackgroundManager{}
  , m_VertBackgroundManager{}
  , m_theme{cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE}
{

  pc::util::coding::CodingManager* const l_codingManager = pc::util::coding::getCodingManager();
  cc::assets::uielements::UISettings* const l_uiSettings = dynamic_cast<cc::assets::uielements::UISettings*>(l_codingManager->getItem("UIElements")); // PRQA S 3077   // PRQA S 3400

  l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
  l_uiSettings->dirty();
  m_HoriBackgroundManager.init(this);

#if ENABLE_VERTICAL_MODE
  l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
  l_uiSettings->dirty();
  m_VertBackgroundManager.init(this);
#endif
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

  CustomSetRenderOrder(20u, true);

}


ParkingViewBackground::~ParkingViewBackground() = default;


void ParkingViewBackground::traverse(osg::NodeVisitor& f_nv) // PRQA S 6043
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // ! update theme

    if(m_customFramework->m_SVSRotateStatusDaddy_Receiver.hasData())
    {
      const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
      const cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899

      if (l_curThemeType != m_theme)
      {
          //XLOG_INFO_OS(g_AppContext) << "[THEME] UIElements start theme update" << XLOG_ENDL;

          if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
          {
            m_VertBackgroundManager.clearIcon();
          }
          else if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
          {
            m_HoriBackgroundManager.clearIcon();
          }
          else // cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE
          {
            // default is horizontal
            m_VertBackgroundManager.clearIcon();
          }

          m_theme = l_curThemeType;

          //XLOG_INFO_OS(g_AppContext) << "[THEME] UIElements finish theme update" << XLOG_ENDL;
      }
      else
      {
        // Do nothing
      }
    }

    if (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
      m_HoriBackgroundManager.update(this, m_customFramework);
    }
    else if (m_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
    {
      m_VertBackgroundManager.update(this, m_customFramework);
    }
    else // cc::target::common::EThemeTypeHU::ETHEME_TYPE_NONE
    {
      // default is horizontal
      m_HoriBackgroundManager.update(this, m_customFramework);
    }

  }
  pc::assets::ImageOverlays::traverse(f_nv);
}

} // namespace uielements
} // namespace assets
} // namespace cc

