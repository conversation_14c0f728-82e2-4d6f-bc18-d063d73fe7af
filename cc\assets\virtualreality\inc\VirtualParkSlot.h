//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualParkSlot.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_PARKSLOT_H
#define CC_ASSETS_VIRTUALREALITY_PARKSLOT_H

#include "pc/generic/util/coding/inc/CodingManager.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/virtualreality/inc/VirtualRealityObject.h"

#include <osg/Group>
#include <osg/MatrixTransform>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace virtualreality
{

//!
//! VirtualParkSlot
//!
class VirtualParkSlot : public VirtualRealityObject
{
public:
  VirtualParkSlot();

  VirtualParkSlot(const VirtualParkSlot& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::virtualreality, VirtualParkSlot);

  void addObjectNode() override;

  void updateObjectNode() override;

  void setSlotAvailableType(cc::target::common::EParkSlotAvailableStatus f_slotType)
  {
    m_slotType = f_slotType;
  }

  void setSlotOrientationType(cc::target::common::EParkSlotOrientationType f_slotOrientationType)
  {
    m_slotOrientationType = f_slotOrientationType;
  }

  void setSlotCornerPosition(cc::target::common::ParkSlotPostion_st f_slotCornerPostion)
  {
    m_slotCornerPostion = f_slotCornerPostion;
  }

protected:
  ~VirtualParkSlot();

private:
  osg::observer_ptr<osg::Geode> g_planeGeode;
  cc::target::common::EParkSlotAvailableStatus m_slotType;
  cc::target::common::EParkSlotOrientationType m_slotOrientationType;
  cc::target::common::ParkSlotPostion_st       m_slotCornerPostion;
};



} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_PARKSLOT_H
