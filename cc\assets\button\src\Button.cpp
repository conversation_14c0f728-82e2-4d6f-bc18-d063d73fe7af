//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------


#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/uielements/inc/CustomIcon.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
// #include "cc/views/touchfocus/inc/TouchFocusView.h"

#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060

#include <chrono>

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace button
{

std::list<IButtonUpdater*> ButtonPopController::popList;

Button::Button(cc::core::AssetId f_assetId, osg::Camera* f_referenceView)
    : pc::assets::ImageOverlays{f_assetId, f_referenceView}
    , m_icon{nullptr}
    , m_hasCustomClickArea{false}
    , m_iconCenter{0.0f, 0.0f}
    , m_customIconSize{}
    , m_responseArea{0.0f, 0.0f}
    , m_positionHori{0.0f, 0.0f}
    , m_positionVert{0.0f, 0.0f}
    , m_horiReferenceView{nullptr}
    , m_vertReferenceView{nullptr}
    , m_texturePath("")
    , m_settingModifiedCount{~0u}
    , m_customClickArea{}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


osg::Vec2f Button::getIconPositionScreen()
{
    if (getReferenceView() == nullptr || (getReferenceView()->getViewport() == nullptr))
    {
        return osg::Vec2f{0.0f, 0.0f};
    }
    const auto viewport = getReferenceView()->getViewport();
    const vfc::float32_t x = m_iconCenter.x() + static_cast<vfc::float32_t>(viewport->x());
    const vfc::float32_t y = (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI) ?
        static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - (m_iconCenter.y() + static_cast<vfc::float32_t>(viewport->y())) :
        (m_iconCenter.y() + static_cast<vfc::float32_t>(viewport->y()));
    return osg::Vec2f{x, y};
}


osg::Vec2f Button::getIconPositionHeadUnit()
{
// TODO: adapt when there's scale factor in headunit
#if 0
  using cc::target::ultrascale::HORI_SVS_WIDTH;
  using cc::target::ultrascale::HORI_SVS_HEIGHT;
  using cc::target::ultrascale::HORI_HU_WIDTH;
  using cc::target::ultrascale::HORI_HU_HEIGHT;
  using cc::target::ultrascale::HORI_X_OFFSET;
  using cc::target::ultrascale::HORI_Y_OFFSET;
  using cc::target::ultrascale::HORI_X_SCALE_FACTOR;
  using cc::target::ultrascale::HORI_Y_SCALE_FACTOR;

  using cc::target::ultrascale::VERT_SVS_WIDTH;
  using cc::target::ultrascale::VERT_SVS_HEIGHT;
  using cc::target::ultrascale::VERT_HU_WIDTH;
  using cc::target::ultrascale::VERT_HU_HEIGHT;
  using cc::target::ultrascale::VERT_X_OFFSET;
  using cc::target::ultrascale::VERT_Y_OFFSET;
  using cc::target::ultrascale::VERT_X_SCALE_FACTOR;
  using cc::target::ultrascale::VERT_Y_SCALE_FACTOR;

  osg::Vec2f iconPositionScreen = this->getIconPositionScreen();
  osg::Vec2f iconPositionHeadUnit = {};
  if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
  {
    iconPositionHeadUnit.x() = (iconPositionScreen.x() / HORI_X_SCALE_FACTOR) + HORI_X_OFFSET;
    iconPositionHeadUnit.y() = (iconPositionScreen.y() / HORI_Y_SCALE_FACTOR) + HORI_Y_OFFSET;
  }
  else
  {
    iconPositionHeadUnit.x() = (iconPositionScreen.y() / VERT_X_SCALE_FACTOR) + VERT_X_OFFSET;
    iconPositionHeadUnit.y() = (iconPositionScreen.x() / VERT_Y_SCALE_FACTOR) + VERT_Y_OFFSET;
  }
  return iconPositionHeadUnit;
#else
  return {0.0f, 0.0f};
#endif
}

// bool Button::isTouchFocused()
// {
//     return m_touchFocusView->isTouchFocused();
// }


void Button::traverseUpdate(bool blockTouch)
{
    if(!blockTouch)
    {
        update();
    }
    if (m_dirty)
    {
        init();
        m_dirty = false;
    }
    if (m_dirtyState)
    {
        switch (m_state)
        {
            case ButtonState::UNAVAILABLE:
            {
                onUnavailable();
                break;
            }
            case ButtonState::AVAILABLE:
            {
                onAvailable();
                break;
            }
            case ButtonState::PRESSED:
            {
                onPressed();
                break;
            }
            case ButtonState::RELEASED:
            {   
                onReleased();
                break;
            }
            case ButtonState::SELECTED:
            {
                onSelected();
                break;
            }
            case ButtonState::INVALID:
            default:
            {
                onInvalid();
                break;
            }
        }
        init(); // prevent showing old texture
        m_dirtyState = false;
    }
    m_icon->setEnabled(m_iconEnable);

    // IMGUI_LOG("Buttons", getName() + " Position", std::to_string(getIconPositionScreen().x()) + " " + std::to_string(getIconPositionScreen().y()));
    // IMGUI_LOG("Buttons", getName() + "HeadUnit Position", std::to_string(getIconPositionHeadUnit().x()) + " " + std::to_string(getIconPositionHeadUnit().y()));
}


void Button::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        // if (!isTouchFocused()) // only dialog is allowed to update
        // {
            invokeUpdate(f_nv);
        // }
    }
    pc::assets::ImageOverlays::traverse(f_nv);
}


void Button::init()
{
    removeIcon(m_icon);
    m_icon = new cc::assets::uielements::CustomIcon{m_texturePath, false, false, true, m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI};
    if( m_customIconSize.x()!= 0.f && m_customIconSize.y()!=0.f) // PRQA S 3270
    {
        m_icon->setSize(m_customIconSize, pc::assets::Icon::UnitType::Pixel);
    }

    const auto l_texture = const_cast<osg::Texture2D*>(m_icon->getTexture()); // PRQA S 3066
    if ( nullptr !=  l_texture)
    {
        l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
        l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    }
    m_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    m_icon->setPosition(m_iconCenter, pc::assets::Icon::UnitType::Pixel);
    m_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    m_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    m_icon->setEnabled(m_iconEnable);
    m_responseArea = m_icon->getIconSize();
    if (m_rotateTheme != cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        std::swap(m_responseArea.x(), m_responseArea.y());
        m_icon->setSize(m_responseArea, pc::assets::Icon::UnitType::Pixel);
    }
    addIcon(m_icon);
    updateIconCenter();
}


void Button::updateIconCenter()
{
    using cc::core::CustomViews;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        if (m_horiReferenceView == nullptr)
        {
            XLOG_ERROR(g_AppContext, "Button::updateIconCenter m_horiReferenceView is nullptr, maybe you forget to set it?");
            return;
        }
        m_iconCenter.x() = (m_iconAtMiddle) ? static_cast<vfc::float32_t>(m_horiReferenceView->getViewport()->width() / 2.0) : m_positionHori.x();
        m_iconCenter.y() = m_positionHori.y();
    }
    else
    {
        if (m_vertReferenceView == nullptr)
        {
            XLOG_ERROR(g_AppContext, "Button::updateIconCenter m_vertReferenceView is nullptr, maybe you forget to set it?");
            return;
        }
        m_iconCenter.x() = static_cast<vfc::float32_t>(m_vertReferenceView->getViewport()->width()) - m_positionVert.x();
        m_iconCenter.y() = (m_iconAtMiddle) ? static_cast<vfc::float32_t>(m_vertReferenceView->getViewport()->height() / 2.0) : m_positionVert.y();
    }
    m_icon->setPosition(m_iconCenter, pc::assets::Icon::UnitType::Pixel);
}


bool Button::checkTouchInsideResponseArea(osg::Vec2f f_touch)
{
    if (m_referenceView == nullptr)
    {
        XLOG_WARN(g_AppContext, "Button doesn't have reference view");
        return false;
    }
    const auto viewport = m_referenceView->getViewport();

    vfc::float32_t huX = 0.0f;
    vfc::float32_t huY = 0.0f;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        huX = static_cast<vfc::float32_t>(f_touch.x()) - static_cast<vfc::float32_t>(viewport->x());
        huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(f_touch.y());
    }
    else
    {
        huX = static_cast<vfc::float32_t>(f_touch.y()) - static_cast<vfc::float32_t>(viewport->x());
        huY = static_cast<vfc::float32_t>(f_touch.x()) - static_cast<vfc::float32_t>(viewport->y());
    }
    const vfc::float32_t lowerX = (m_iconCenter.x() - m_responseArea.x() * 0.5f);
    const vfc::float32_t upperX = (m_iconCenter.x() + m_responseArea.x() * 0.5f);
    const vfc::float32_t lowerY = (m_iconCenter.y() - m_responseArea.y() * 0.5f);
    const vfc::float32_t upperY = (m_iconCenter.y() + m_responseArea.y() * 0.5f);
    if ((lowerX <= huX) && (huX <= upperX) && (lowerY <= huY) && (huY <= upperY))
    {
        return true;
    }
    return false;
}


bool Button::checkTouchInsideResponseArea()
{
    if (m_referenceView == nullptr)
    {
        XLOG_WARN(g_AppContext, "Button doesn't have reference view");
        return false;
    }
    const auto viewport = m_referenceView->getViewport();

    vfc::float32_t huX = 0.0f;
    vfc::float32_t huY = 0.0f;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        huX = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->x());
        huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(m_huY);
    }
    else
    {
        huX = static_cast<vfc::float32_t>(m_huY) - static_cast<vfc::float32_t>(viewport->x());
        huY = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->y());
    }
    const vfc::float32_t lowerX = (m_iconCenter.x() - m_responseArea.x() * 0.5f);
    const vfc::float32_t upperX = (m_iconCenter.x() + m_responseArea.x() * 0.5f);
    const vfc::float32_t lowerY = (m_iconCenter.y() - m_responseArea.y() * 0.5f);
    const vfc::float32_t upperY = (m_iconCenter.y() + m_responseArea.y() * 0.5f);
    if ((lowerX <= huX) && (huX <= upperX) && (lowerY <= huY) && (huY <= upperY))
    {
        return true;
    }
    return false;
}

bool Button::checkTouchInsideCustomClickArea()
{
    if (m_referenceView == nullptr)
    {
        XLOG_WARN(g_AppContext, "Button doesn't have reference view");
        return false;
    }
    const auto viewport = m_referenceView->getViewport();

    vfc::float32_t huX = 0.0f;
    vfc::float32_t huY = 0.0f;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        huX = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->x());
        huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(m_huY);
    }
    else
    {
        huX = static_cast<vfc::float32_t>(m_huY) - static_cast<vfc::float32_t>(viewport->x());
        huY = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->y());
    }
    const vfc::float32_t lowerX = (m_customClickArea.m_iconCenter.x() - m_customClickArea.m_responseArea.x() * 0.5f);
    const vfc::float32_t upperX = (m_customClickArea.m_iconCenter.x() + m_customClickArea.m_responseArea.x() * 0.5f);
    const vfc::float32_t lowerY = (m_customClickArea.m_iconCenter.y() - m_customClickArea.m_responseArea.y() * 0.5f);
    const vfc::float32_t upperY = (m_customClickArea.m_iconCenter.y() + m_customClickArea.m_responseArea.y() * 0.5f);
    if ((lowerX <= huX) && (huX <= upperX) && (lowerY <= huY) && (huY <= upperY))
    {
        return true;
    }
    return false;
}

bool Button::checkTouchInsideTargetClickArea(const osg::Vec2f& f_iconCenter, const osg::Vec2f& f_responseArea) // PRQA S 4211
{
    if (m_referenceView == nullptr)
    {
        XLOG_WARN(g_AppContext, "Button doesn't have reference view");
        return false;
    }
    const auto viewport = m_referenceView->getViewport();

    vfc::float32_t huX = 0.0f;
    vfc::float32_t huY = 0.0f;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        huX = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->x());
        huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(m_huY);
    }
    else
    {
        huX = static_cast<vfc::float32_t>(m_huY) - static_cast<vfc::float32_t>(viewport->x());
        huY = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->y());
    }
    const vfc::float32_t lowerX = (f_iconCenter.x() - f_responseArea.x() * 0.5f);
    const vfc::float32_t upperX = (f_iconCenter.x() + f_responseArea.x() * 0.5f);
    const vfc::float32_t lowerY = (f_iconCenter.y() - f_responseArea.y() * 0.5f);
    const vfc::float32_t upperY = (f_iconCenter.y() + f_responseArea.y() * 0.5f);
    if ((lowerX <= huX) && (huX <= upperX) && (lowerY <= huY) && (huY <= upperY))
    {
        return true;
    }
    return false;
}

bool Button::checkTouchInsideViewport() // PRQA S 4211
{
    if (m_referenceView == nullptr)
    {
        XLOG_WARN(g_AppContext, "Button doesn't have reference view");
        return false;
    }
    const auto viewport = m_referenceView->getViewport();

    vfc::float32_t huX = 0.0f;
    vfc::float32_t huY = 0.0f;
    if (m_rotateTheme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
    {
        huX = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->x());
        huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(m_huY);
    }
    else
    {
        huX = static_cast<vfc::float32_t>(m_huY) - static_cast<vfc::float32_t>(viewport->x());
        huY = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->y());
    }
    if ((0 <= huX && huX < viewport->width()) && (0 <= huY && huY < viewport->height()))
    {
        return true;
    }
    return false;
}

} // namespace button
} // namespace assets
} // namespace cc
