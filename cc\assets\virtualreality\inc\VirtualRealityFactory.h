//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityFactory.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_FACTORY_H
#define CC_ASSETS_VIRTUALREALITY_FACTORY_H

#include "cc/assets/virtualreality/inc/VirtualRealityObject.h"
#include "cc/assets/virtualreality/inc/LowpolyVehicle.h"
#include "cc/assets/virtualreality/inc/VirtualParkSlot.h"
#include "cc/assets/virtualreality/inc/VirtualEgoVehicle.h"
#include "cc/assets/virtualreality/inc/LowpolyPedestrian.h"

enum class EObjectType
{
    SLOT              = 1,
    LOWPOLYVEHICLE    = 2,
    LOWPOLYPEDESTRAIN = 3,
    EGOVEHICLE        = 4
};

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace virtualreality
{

//!
//! VirtualRealityFactory
//!
class VirtualRealityFactory
{
public:
  VirtualRealityFactory();
  ~VirtualRealityFactory();

  VirtualRealityObject* createObject(EObjectType f_objectType);

};



} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_FACTORY_H
