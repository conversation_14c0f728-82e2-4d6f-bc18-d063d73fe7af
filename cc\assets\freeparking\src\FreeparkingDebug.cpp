/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#ifdef TARGET_STANDALONE

#include "cc/assets/freeparking/inc/FreeparkingOverlay.h"
#include "cc/imgui/inc/imgui_manager.h"

namespace cc
{
namespace assets
{
namespace freeparking
{

void FreeParkingOverlay::debug()
{
    static bool         s_overwrite{false};
    static vfc::int32_t s_color;
    static bool         s_TopLeft{false};
    static bool         s_MiddleLeft{false};
    static bool         s_BottomLeft{false};
    static bool         s_TopRight{false};
    static bool         s_MiddleRight{false};
    static bool         s_BottomRight{false};
    if (ImGui::GetCurrentContext() != nullptr)
    {
        if (ImGui::Begin("FreeParkingOverlay"))
        {
            ImGui::Checkbox("Overwrite", &s_overwrite);
            ImGui::RadioButton("Available", &s_color, 0);
            ImGui::RadioButton("Unavailable", &s_color, 1);
            ImGui::RadioButton("Moving", &s_color, 2);
            ImGui::Checkbox("TopLeft", &s_TopLeft);
            ImGui::Checkbox("MiddleLeft", &s_MiddleLeft);
            ImGui::Checkbox("BottomLeft", &s_BottomLeft);
            ImGui::Checkbox("TopRight", &s_TopRight);
            ImGui::Checkbox("MiddleRight", &s_MiddleRight);
            ImGui::Checkbox("BottomRight", &s_BottomRight);
        }
        ImGui::End();
    }

    if (!s_overwrite)
    {
        return;
    }

    // clang-format off
    switch (s_color)
    {
    case 0: { changeColor(g_freeParkingOverlaySettings->m_availableColor); break; }
    case 1: { changeColor(g_freeParkingOverlaySettings->m_unavailableColor); break; }
    case 2: { changeColor(g_freeParkingOverlaySettings->m_movingColor); break; }
    default:
    {
        changeColor(g_freeParkingOverlaySettings->m_unavailableColor);
        break;
    }
    }
    // clang-format on
    m_warningGeodes->setEnable(WarningGeodes::WarningArea::TopLeft, s_TopLeft);
    m_warningGeodes->setEnable(WarningGeodes::WarningArea::MiddleLeft, s_MiddleLeft);
    m_warningGeodes->setEnable(WarningGeodes::WarningArea::BottomLeft, s_BottomLeft);
    m_warningGeodes->setEnable(WarningGeodes::WarningArea::TopRight, s_TopRight);
    m_warningGeodes->setEnable(WarningGeodes::WarningArea::MiddleRight, s_MiddleRight);
    m_warningGeodes->setEnable(WarningGeodes::WarningArea::BottomRight, s_BottomRight);
}

} // namespace freeparking
} // namespace assets
} // namespace cc

#endif // TARGET_STANDALONE