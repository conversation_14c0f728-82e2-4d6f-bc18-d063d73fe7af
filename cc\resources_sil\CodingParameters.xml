<?xml version="1.0" encoding="UTF-8"?>
<CodingParameters>
  <System>
    <mainViewport>
      <origin>10 32</origin>
      <size>2560 1320</size>
    </mainViewport>
    <defaultModeScissorTest>0</defaultModeScissorTest>
  </System>
  <EngineStandalone>
    <staticTextureFront>cc/resources_sil/front.jpg</staticTextureFront>
    <staticTextureRight>cc/resources_sil/right.jpg</staticTextureRight>
    <staticTextureRear>cc/resources_sil/rear.jpg</staticTextureRear>
    <staticTextureLeft>cc/resources_sil/left.jpg</staticTextureLeft>
    <staticTextureRightFolded>cc/resources_sil/right.jpg</staticTextureRightFolded>
    <staticTextureLeftFolded>cc/resources_sil/left.jpg</staticTextureLeftFolded>
    <staticTextureCombine>cc/resources_sil/four.jpg</staticTextureCombine>
  </EngineStandalone>
  <MaskScreen>
    <maskColor>0.0 0.0 1.0 0.8</maskColor>
  </MaskScreen>
  <ShaderManager>
    <useBinaryShaders>0</useBinaryShaders>
    <useSamplerOES>0</useSamplerOES>
    <convertShaderSource>1</convertShaderSource>
  </ShaderManager>
  <BowlShaperDefault>
    <numRadialSections>64</numRadialSections>
    <numHeightSections>128</numHeightSections>
  </BowlShaperDefault>
  <OdometrySimulator>
    <useOdometrySimulation>1</useOdometrySimulation>
  </OdometrySimulator>
  <CalibOverlay>
    <calibSwitch>1</calibSwitch>
  </CalibOverlay>
  <Floor>
    <resolution>0.1 0.1</resolution>
  </Floor>
  <ViewModeStateMachine>
    <aAReqTimeout>5000</aAReqTimeout>
    <BC_ACK_Reception_Timeout>5000</BC_ACK_Reception_Timeout>
  </ViewModeStateMachine>
  <ViewModeSMLogs>
    <logSM_BCC>1</logSM_BCC>
  </ViewModeSMLogs>
  <TestButton>
    <exampleTexture>
        <day>
            <ReleasedTexturePath>cc/resources_sil/icon/released.png</ReleasedTexturePath>
            <PressedTexturePath>cc/resources_sil/icon/pressed.png</PressedTexturePath>
            <AvailableTexturePath>cc/resources_sil/icon/available.png</AvailableTexturePath>
            <UnavailableTexturePath>cc/resources_sil/icon/unavailable.png</UnavailableTexturePath>
        </day>
        <night>
            <ReleasedTexturePath>cc/resources_sil/icon/released.png</ReleasedTexturePath>
            <PressedTexturePath>cc/resources_sil/icon/pressed.png</PressedTexturePath>
            <AvailableTexturePath>cc/resources_sil/icon/available.png</AvailableTexturePath>
            <UnavailableTexturePath>cc/resources_sil/icon/unavailable.png</UnavailableTexturePath>
        </night>
    </exampleTexture>
    <horiPos>0.0 300.0</horiPos>
    <vertPos>100.0 0.0</vertPos>
  </TestButton>
  <SplineOverlay>
    <colors>
      <nearColor>0.953 0.0 0.039 1.0</nearColor>
      <middleColor>1.0 0.494 0.0 1.0</middleColor>
      <farColor>0.0 0.761 0.216 1.0</farColor>
      <nearColor_OffCourse>0.68 0.22 0.2 1.0</nearColor_OffCourse>
      <middleColor_OffCourse>0.96 0.72 0.32 1.0</middleColor_OffCourse>
      <farColor_OffCourse>1.0 1.0 1.0 0.4</farColor_OffCourse>
      <nearColor_OffCourse_Shadow>0.8 0.0 0.039 0.2</nearColor_OffCourse_Shadow>
      <middleColor_OffCourse_Shadow>0.8 0.6 0.0 0.2</middleColor_OffCourse_Shadow>
      <farColor_OffCourse_Shadow>1.0 1.0 1.0 0.2</farColor_OffCourse_Shadow>
      <shadowColor>0.0 0.0 0.0 0.0</shadowColor>
    </colors>
    <zPos>0.01</zPos>
    <distanceNear>0.3</distanceNear>
    <distanceMiddle>0.6</distanceMiddle>
    <distanceFar>0.9</distanceFar>
    <shadowOuterContourDist>4.0</shadowOuterContourDist>
    <splineWidth>0.04</splineWidth>
    <splineWidthShadow>1.0</splineWidthShadow>
    <maxShowDistance>2.0</maxShowDistance>
    <endPointOffset>0.7</endPointOffset>
    <handlePointScale>0.35</handlePointScale>
    <handlePointScaleMid>0.55</handlePointScaleMid>
    <farHandlePointScale>0.8</farHandlePointScale>
    <fadingWidth>10</fadingWidth>
  </SplineOverlay>
  <PanoramaViewFront>
    <horizontalStart>0.0</horizontalStart>
    <horizontalEnd>9.0</horizontalEnd>
    <verticalStart>0.0</verticalStart>
    <verticalEnd>6.0</verticalEnd>
    <resolutionHorizontal>20</resolutionHorizontal>
    <resolutionVertical>40</resolutionVertical>
    <planeWidth>9.0</planeWidth>
    <planeHeight>6.0</planeHeight>
    <cylinder>
      <cylinderRad>4.5</cylinderRad>
      <cylinderHeight>6.0</cylinderHeight>
      <cylinderFovDeg>180.0</cylinderFovDeg>
      <offsetVertical>0.5</offsetVertical>
      <offsetHorizontal>0.0</offsetHorizontal>
      <x1Factor>23</x1Factor>
      <x2Factor>61</x2Factor>
      <x3Factor>193</x3Factor>
      <x4Factor>231</x4Factor>
      <c1>1.0</c1>
      <c2>1.0</c2>
      <point0>0.0</point0>
      <point1>0.0</point1>
      <point2>0.0</point2>
      <point3>0.0</point3>
      <point4>0.0</point4>
      <point5>0.0</point5>
      <smoothing>0</smoothing>
    </cylinder>
  </PanoramaViewFront>
  <PanoramaViewRear>
    <horizontalStart>0.0</horizontalStart>
    <horizontalEnd>9.0</horizontalEnd>
    <verticalStart>0.0</verticalStart>
    <verticalEnd>6.0</verticalEnd>
    <resolutionHorizontal>20</resolutionHorizontal>
    <resolutionVertical>40</resolutionVertical>
    <planeWidth>9.0</planeWidth>
    <planeHeight>6.0</planeHeight>
    <cylinder>
      <cylinderRad>4.5</cylinderRad>
      <cylinderHeight>6.0</cylinderHeight>
      <cylinderFovDeg>180.0</cylinderFovDeg>
      <offsetVertical>1.0</offsetVertical>
      <offsetHorizontal>0.0</offsetHorizontal>
      <x1Factor>23</x1Factor>
      <x2Factor>61</x2Factor>
      <x3Factor>190</x3Factor>
      <x4Factor>227</x4Factor>
      <c1>1.0</c1>
      <c2>1.0</c2>
      <point0>0.0</point0>
      <point1>0.0</point1>
      <point2>0.0</point2>
      <point3>0.0</point3>
      <point4>0.0</point4>
      <point5>0.0</point5>
      <smoothing>0</smoothing>
    </cylinder>
  </PanoramaViewRear>
  <PlanView>
    <widthMeters>8.451</widthMeters>
    <widthMetersParkingVert>8.0</widthMetersParkingVert>
  </PlanView>
  <PtsDistanceDigitalDisplay>
    <offset_front>1.5 0.0 0.0</offset_front>
    <offset_front_parking>1.0 0.0 0.0</offset_front_parking>
    <offset_rear>-0.0 0.0 0.0</offset_rear>
    <offset_rear_parking>1.3 0.0 0.0</offset_rear_parking>
    <fixedFrontPosition>6.0</fixedFrontPosition> <!-- default front fixed position -->
    <fixedRearPosition>-2.8</fixedRearPosition> <!-- default rear fixed position -->
    <fixedFrontTopPosition>6.7</fixedFrontTopPosition>
    <fixedRearBottomPosition>-4</fixedRearBottomPosition>
    <fixedFrontPosition_vert_parking>4.7</fixedFrontPosition_vert_parking> <!-- default front fixed position -->
    <fixedRearPosition_vert_parking>-1.4</fixedRearPosition_vert_parking> <!-- default rear fixed position -->
    <stopTextFrontPosition>5.706 -0.64</stopTextFrontPosition>
    <stopTextRearPosition>-3.1 -0.64</stopTextRearPosition>
    <stopTextFrontPosition_vert_parking>4.2 -0.64</stopTextFrontPosition_vert_parking>
    <stopTextRearPosition_vert_parking>-2.5 -0.64</stopTextRearPosition_vert_parking>
    <textOffsetPercentageFront>0.6</textOffsetPercentageFront>
    <textOffsetPercentageRear>0.4</textOffsetPercentageRear>
    <splineColorB>0.0 0.14 0.8 1.0</splineColorB>
    <rangeFrontConf_AIHC>1.0</rangeFrontConf_AIHC>
    <rangeRearConf_AIHC>1.0</rangeRearConf_AIHC>
    <rangeSideConf_AIHC>1.0</rangeSideConf_AIHC>
  </PtsDistanceDigitalDisplay>
</CodingParameters>
