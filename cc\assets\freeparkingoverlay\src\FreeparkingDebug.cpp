//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingRotateButton.h"

#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "cc/core/inc/CustomFramework.h"

#ifdef TARGET_STANDALONE
#include "cc/imgui/inc/implot/implot.h"
#endif

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

void FreeparkingManager::logInternal()
{
    // clang-format off
    IMGUI_LOG("FreeParking", "m_touchPosition", std::to_string(m_touchPosition.x()) + " " + std::to_string(m_touchPosition.y()));
    IMGUI_LOG("FreeParking", "m_centerPosition", std::to_string(m_centerPosition.x()) + " " + std::to_string(m_centerPosition.y()));
    IMGUI_LOG("FreeParking", "m_rotateAngle", std::to_string(m_rotateAngle));
    IMGUI_LOG("FreeParking", "m_SlitheringFlag", m_SlitheringFlag ? "TRUE" : "FALSE");
    IMGUI_LOG("FreeParking", "m_isUserFinishedMoving", m_isUserFinishedMoving ? "TRUE" : "FALSE");
    IMGUI_LOG("FreeParking", "m_firstEntry", m_firstEntry ? "TRUE" : "FALSE");
    IMGUI_LOG("FreeParking", "m_SlitherPos", "StartPos: " +
        std::to_string(m_SlitherPos.StartPos.x()) + " " +
        std::to_string(m_SlitherPos.StartPos.y()) + ", " "EndPos: " +
        std::to_string(m_SlitherPos.EndPos.x()) + " " +
        std::to_string(m_SlitherPos.EndPos.y()));
    IMGUI_LOG("FreeParking", "m_prevSlitherActionType",
        m_prevSlitherActionType == ROTATION ? "ROTATION" :
        m_prevSlitherActionType == TRANSLATION ? "TRANSLATION" :
        m_prevSlitherActionType == TELEPORTATION ? "TELEPORTATION" : "UNKNOWN");
    IMGUI_LOG("FreeParking", "m_SlitherActionType",
        m_SlitherActionType == ROTATION ? "ROTATION" :
        m_SlitherActionType == TRANSLATION ? "TRANSLATION" :
        m_SlitherActionType == TELEPORTATION ? "TELEPORTATION" : "UNKNOWN");
    IMGUI_LOG("FreeParking", "m_SlitherBeginPos",
        std::to_string(m_SlitherBeginPos.x()) + " " +
        std::to_string(m_SlitherBeginPos.y()));
    IMGUI_LOG("FreeParking", "m_SlotOrientation", "Position: " +
        std::to_string(m_SlotOrientation.m_CenterPos.x()) + " " +
        std::to_string(m_SlotOrientation.m_CenterPos.y()) + ", " + "Yaw: " +
        std::to_string(m_SlotOrientation.m_yawAngleRaw));
    IMGUI_LOG("FreeParking", "m_touchStatus",
        m_touchStatus == Down   ? "Down" :
        m_touchStatus == Up   ? "Up" :
        m_touchStatus == Move ? "Move" : "Unknown");
    IMGUI_LOG("FreeParking", "m_isSlotMovedByUser", m_isSlotMovedByUser ? "TRUE" : "FALSE");
    IMGUI_LOG("FreeParking", "SpotWidth       :", m_spotRect.getWidth());
    IMGUI_LOG("FreeParking", "SpotLength      :", m_spotRect.getLength());
    IMGUI_LOG("FreeParking", "SpotAngle       :", m_spotRect.getAngle());
    IMGUI_LOG("FreeParking", "SpotCenterPoint :", std::to_string(m_spotRect.getCenterPoint().x()) + " " + std::to_string(m_spotRect.getCenterPoint().y()));

    IMGUI_LOG("FreeParking", "isSlotHeadingUp", isSlotHeadingUp(m_SlotOrientation.m_yawAngleRaw));
    IMGUI_LOG("FreeParking", "isSlotHeadingDown", isSlotHeadingDown(m_SlotOrientation.m_yawAngleRaw));
    IMGUI_LOG("FreeParking", "isSlotHeadingLeft", isSlotHeadingLeft(m_SlotOrientation.m_yawAngleRaw));
    IMGUI_LOG("FreeParking", "isSlotHeadingRight", isSlotHeadingRight(m_SlotOrientation.m_yawAngleRaw));
    // IMGUI_LOG("FreeParking", "RectStrippedDeliver_FrontLeft", std::to_string(m_fpRectStripped.m_fpCornerFrontLeft.m_fpPosX.value()) + " " + std::to_string(m_fpRectStripped.m_fpCornerFrontLeft.m_fpPosY.value()));
    // IMGUI_LOG("FreeParking", "RectStrippedDeliver_FrontRight", std::to_string(m_fpRectStripped.m_fpCornerFrontRight.m_fpPosX.value()) + " " + std::to_string(m_fpRectStripped.m_fpCornerFrontRight.m_fpPosY.value()));
    // IMGUI_LOG("FreeParking", "RectStrippedDeliver_RearLeft", std::to_string(m_fpRectStripped.m_fpCornerRearLeft.m_fpPosX.value()) + " " + std::to_string(m_fpRectStripped.m_fpCornerRearLeft.m_fpPosY.value()));
    // IMGUI_LOG("FreeParking", "RectStrippedDeliver_RearRight", std::to_string(m_fpRectStripped.m_fpCornerRearRight.m_fpPosX.value()) + " " + std::to_string(m_fpRectStripped.m_fpCornerRearRight.m_fpPosY.value()));

    // IMGUI_LOG("FreeParking", "m_FrontLeft_vertex_screen", std::to_string(m_FrontLeft_vertex_screen.x()) + " " + std::to_string(m_FrontLeft_vertex_screen.y()));
    // IMGUI_LOG("FreeParking", "m_FrontRight_vertex_sreen", std::to_string(m_FrontRight_vertex_sreen.x()) + " " + std::to_string(m_FrontRight_vertex_sreen.y()));
    // IMGUI_LOG("FreeParking", "m_RearLeft_vertex_screen", std::to_string(m_RearLeft_vertex_screen.x()) + " " + std::to_string(m_RearLeft_vertex_screen.y()));
    // IMGUI_LOG("FreeParking", "m_RearRight_vertex_screen", std::to_string(m_RearRight_vertex_screen.x()) + " " + std::to_string(m_RearRight_vertex_screen.y()));
    // IMGUI_LOG("FreeParking", "m_Center_vertex_screen", std::to_string(m_Center_vertex_screen.x()) + " " + std::to_string(m_Center_vertex_screen.y()));
    // clang-format on

#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
    if (m_framework->asCustomFramework()->isImguiEnabled())
    {
        std::array<osg::Vec2f, 4> l_corners;

        std::array<osg::Vec2f, 5> l_viewportRec;
        l_viewportRec.at(0) = m_viewportTopLeft;
        l_viewportRec.at(1) = m_viewportBottomLeft;
        l_viewportRec.at(2) = m_viewportBottomRight;
        l_viewportRec.at(3) = m_viewportTopRight;
        l_viewportRec.at(4) = m_viewportTopLeft;

        std::array<osg::Vec2f, 4> l_reachPoints;
        vfc::float32_t rotateAngle = m_SlotOrientation.m_yawAngleRaw;
        osg::Vec2f l_topReach    = pc::util::rotate( osg::Vec2f{m_spotSize.x(), 0.0f} * (g_freeparkingRotateButtonSettings->m_offsetPercentage + 0.5f), rotateAngle) + m_centerPosition;
        osg::Vec2f l_bottomReach = pc::util::rotate( osg::Vec2f{-m_spotSize.x(), 0.0f} * (g_freeparkingRotateButtonSettings->m_offsetPercentage + 0.5f), rotateAngle) + m_centerPosition;
        osg::Vec2f l_leftReach   = pc::util::rotate( osg::Vec2f{0.0f, m_spotSize.y()}, rotateAngle) + m_centerPosition;
        osg::Vec2f l_rightReach  = pc::util::rotate( osg::Vec2f{0.0f, -m_spotSize.y()}, rotateAngle) + m_centerPosition;

        m_spotRect.updateRectPoints(l_corners[0], l_corners[1], l_corners[2], l_corners[3]);
        osg::Vec2f l_slotCenter = m_spotRect.getCenterPoint();
        ImGui::Begin("FreeParking");
        if (ImPlot::BeginPlot("FreeParkingOut"))
        {
            // clang-format off
            ImPlot::PlotScatter("Corners", &l_corners[0]._v[0], &l_corners[0]._v[1], 4, 0, 0, 2 * sizeof(vfc::float32_t));
            ImPlot::PlotScatter("Geometry Center", &l_slotCenter.x(), &l_slotCenter.y(), 1, 0, 0, 2 * sizeof(vfc::float32_t));
            // ImPlot::PlotScatter("Rear Axle Center", &m_outputRearAxleCenter.x(), &m_outputRearAxleCenter.y(), 1, 0, 0, 2 * sizeof(vfc::float32_t));
            ImPlot::PlotLine("Viewport", &l_viewportRec[0]._v[0], &l_viewportRec[0]._v[1], 5, 0, 0, 2 * sizeof(vfc::float32_t));
            ImPlot::PlotScatter("TopReach", &l_topReach.x(), &l_topReach.y(), 1, 0, 0, 2 * sizeof(vfc::float32_t));
            ImPlot::PlotScatter("BottomReach", &l_bottomReach.x(), &l_bottomReach.y(), 1, 0, 0, 2 * sizeof(vfc::float32_t));
            ImPlot::PlotScatter("LeftReach", &l_leftReach.x(), &l_leftReach.y(), 1, 0, 0, 2 * sizeof(vfc::float32_t));
            ImPlot::PlotScatter("RightReach", &l_rightReach.x(), &l_rightReach.y(), 1, 0, 0, 2 * sizeof(vfc::float32_t));

            // clang-format on
            ImPlot::EndPlot();
        }
        ImGui::Text("Rotate Angle: %f", osg::RadiansToDegrees(m_outputAngle));
        ImGui::End();
    }
#endif
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
