//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_FRAME_H
#define CC_ASSETS_TRAJECTORY_FRAME_H

#include "cc/assets/trajectory/inc/CommonTypes.h"

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace frame
{

enum VertexDistributionMode_en
{
  Manual_enm,
  Auto_enm
};


class Frame
{
public:

  struct VertexLine_st
  {
    VertexLine_st()
      : m_controlPoints()
      , m_radius(0.001f) // Dummy value in case someone forgets to initialize the radiuses.
      , m_offset(0.0f)
      , m_fadeInEnabled(false)
      , m_fadeOutEnabled(false)
      , m_fadeInStartAngle(0.0f)
      , m_fadeInEndAngle(0.0f)
      , m_fadeOutStartAngle(0.0f)
      , m_fadeOutEndAngle(0.0f)
      , m_fadeInStartPos(0.0f)
      , m_fadeInEndPos(0.0f)
      , m_fadeOutStartPos(0.0f)
      , m_fadeOutEndPos(0.0f)
      , m_bumperLineAngle(0.0f)
      , m_bumperLinePos(0.0f)
    {
    }

    std::vector<cc::assets::trajectory::commontypes::ControlPoint_st> m_controlPoints;
    float m_radius; // For the curved trajectory
    float m_offset; // For the straight trajectory
    bool  m_fadeInEnabled;
    bool  m_fadeOutEnabled;
    float m_fadeInStartAngle;
    float m_fadeInEndAngle;
    float m_fadeOutStartAngle;
    float m_fadeOutEndAngle;
    float m_fadeInStartPos;
    float m_fadeInEndPos;
    float m_fadeOutStartPos;
    float m_fadeOutEndPos;
    float m_bumperLineAngle;
    float m_bumperLinePos;
  };

  Frame();

  void addVertexLines(unsigned int f_numOfVertexLinesToAdd);

  void setVertexLineRadius(unsigned int f_vertexLineIndex, float f_radius);
  float getVertexLineRadius(unsigned int f_vertexLineIndex) const;

  void setVertexLineOffset(unsigned int f_vertexLineIndex, float f_offset);
  float getVertexLineOffset(unsigned int f_vertexLineIndex) const;

  void setBumperLineAngle(unsigned int f_vertexLineIndex, float f_angle);
  float getBumperLineAngle(unsigned int f_vertexLineIndex) const;

  void setBumperLinePos(unsigned int f_vertexLineIndex, float f_pos);
  float getBumperLinePos(unsigned int f_vertexLineIndex) const;

  void setFadeInStartAngle(unsigned int f_vertexLineIndex, float f_angle);
  float getFadeInStartAngle(unsigned int f_vertexLineIndex) const;

  void setFadeInEndAngle(unsigned int f_vertexLineIndex, float f_angle);
  float getFadeInEndAngle(unsigned int f_vertexLineIndex) const;

  void setFadeOutStartAngle(unsigned int f_vertexLineIndex, float f_angle);
  float getFadeOutStartAngle(unsigned int f_vertexLineIndex) const;

  void setFadeOutEndAngle(unsigned int f_vertexLineIndex, float f_angle);
  float getFadeOutEndAngle(unsigned int f_vertexLineIndex) const;

  void setFadeInStartPos(unsigned int f_vertexLineIndex, float f_pos);
  float getFadeInStartPos(unsigned int f_vertexLineIndex) const;

  void setFadeInEndPos(unsigned int f_vertexLineIndex, float f_pos);
  float getFadeInEndPos(unsigned int f_vertexLineIndex) const;

  void setFadeOutStartPos(unsigned int f_vertexLineIndex, float f_pos);
  float getFadeOutStartPos(unsigned int f_vertexLineIndex) const;

  void setFadeOutEndPos(unsigned int f_vertexLineIndex, float f_pos);
  float getFadeOutEndPos(unsigned int f_vertexLineIndex) const;

  void setFadeIn(unsigned int f_vertexLineIndex, bool f_state);

  void setFadeOut(unsigned int f_vertexLineIndex, bool f_state);

  void addControlPoint(unsigned int f_vertexLineIndex, cc::assets::trajectory::commontypes::ControlPoint_st & f_controlPoint);
  void updateControlPoint(unsigned int f_vertexLineIndex, const osg::Vec4f& f_color);
  //void updateColorControlPointLateralLine(unsigned int f_vertexLineIndex, const osg::Vec4f& f_color);

  void removeAllPoints();

  unsigned int generateVertices(unsigned int f_vertexLineIndex, unsigned int f_startPointIndex,
                                unsigned int f_endPointIndex,
                                const osg::Vec2f & f_rotCenter, float f_z,
                                osg::Vec3Array* f_vertexArray,
                                osg::Vec4Array* f_colorArray,
                                VertexDistributionMode_en f_mode, unsigned int f_numOfVertsIn,
                                cc::assets::trajectory::commontypes::VehicleMovementType_en f_vehicleMovementType,
                                float f_translationAngle_Rad);

  void generateIndices(unsigned int f_leftVertexLineIndex,  unsigned int f_leftStartVertexIndex,
                       unsigned int f_rightVertexLineIndex, unsigned int f_rightStartVertexIndex,
                       unsigned int f_numOfVertsPerLine, osg::DrawElementsUShort* f_indexArray);

  void generateDIIndices(osg::DrawElementsUShort* f_indexArray, cc::assets::trajectory::commontypes::Side_en f_side,
                         unsigned int f_index_Line0_Vert0, unsigned int f_index_Line0_Vert1,
                         unsigned int f_index_Line1_Vert0, unsigned int f_index_Line1_Vert1,
                         unsigned int f_index_Line2_Vert0, unsigned int f_index_Line2_Vert1, unsigned int f_index_Line2_Vert2, unsigned int f_index_Line2_Vert3,
                         unsigned int f_index_Line3_Vert0, unsigned int f_index_Line3_Vert1,
                         unsigned int f_index_Line4_Vert0, unsigned int f_index_Line4_Vert1,
                         unsigned int f_index_Line5_Vert0, unsigned int f_index_Line5_Vert1);

  void generateDIIndices_Tex(osg::DrawElementsUShort* f_indexArray, cc::assets::trajectory::commontypes::Side_en f_side,
                             unsigned int f_index_Line0_Vert0, unsigned int f_index_Line0_Vert1,
                             unsigned int f_index_Line1_Vert0, unsigned int f_index_Line1_Vert1,
                             unsigned int f_index_Line2_Vert0, unsigned int f_index_Line2_Vert1,
                             unsigned int f_index_Line3_Vert0, unsigned int f_index_Line3_Vert1,
                             unsigned int f_index_Line4_Vert0, unsigned int f_index_Line4_Vert1);

  float getFadingInAlpha(const osg::Vec2f& f_vertex, unsigned int f_vertexLineIndex, const osg::Vec2f& f_ackermannPoint) const;

  float getFadingOutAlpha(const osg::Vec2f& f_vertex, unsigned int f_vertexLineIndex, const osg::Vec2f& f_ackermannPoint) const;

  float getFadingInAlpha_Straight(const osg::Vec2f& f_vertex, unsigned int f_vertexLineIndex) const;

  float getFadingOutAlpha_Straight(const osg::Vec2f& f_vertex, unsigned int f_vertexLineIndex) const;

  float getSmallerAngle(float f_angle) const;

private:
  std::vector<VertexLine_st> m_vertexLines;
};


} // namespace frame
} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // #ifndef CC_ASSETS_TRAJECTORY_FRAME_H
