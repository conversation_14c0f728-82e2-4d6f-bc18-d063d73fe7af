//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#ifndef CC_ASSETS_BRIGHTNESSSLIDER_ENTRYBUTTON_H
#define CC_ASSETS_BRIGHTNESSSLIDER_ENTRYBUTTON_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/settingpageoverlay/inc/BrightnessSliderOverlay.h"
#include <osg/Timer>

namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

class BrightnessSliderEntryButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(BrightnessSliderEntryButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(cc::assets::button::ButtonTexturePath, buttonTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
        ADD_FLOAT_MEMBER(exitDelay);
    }

    cc::assets::button::ButtonTexturePath m_buttonTexture;
    osg::Vec2f                            m_horiPos = osg::Vec2f(0.0f, 100.0f);
    float                                 m_exitDelay;
};

class BrightnessSliderEntryButton : public cc::assets::button::Button
{
public:
    BrightnessSliderEntryButton(
        cc::core::AssetId                                       f_assetId,
        pc::core::Framework*                                    f_framework,
        cc::assets::settingpageoverlay::BrightnessSliderSwitch* f_brightnessSliderSwitch,
        osg::Camera*                                            f_referenceView);

protected:
    void update() override;

private:
    void onInvalid() override;
    void onUnavailable() override;
    void onAvailable() override;
    void onPressed() override;
    void onReleased() override;

private:
    pc::core::Framework*                                    m_framework;
    bool                                                    m_brightnessSliderEnabledState;
    cc::assets::settingpageoverlay::BrightnessSliderSwitch* m_brightnessSliderSwitch;
    bool                                                    m_showStatus;
    osg::Timer                                              m_timer;
    osg::Timer_t                                            m_lastUpdate;
};

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BRIGHTNESSSLIDER_ENTRYBUTTON_H