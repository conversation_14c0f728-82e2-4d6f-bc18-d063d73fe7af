//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  VehicleTransIcon.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_VEHICLETRANSICON_H
#define CC_ASSETS_UIELEMENTS_VEHICLETRANSICON_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"
#include "cc/assets/button/inc/Button.h"
#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

//======================================================
// VehicleTransIconSettings
//------------------------------------------------------
/// Setting class for VehicleTransIcon
/// <AUTHOR>
//======================================================
class VehicleTransIconSettings : public pc::util::coding::ISerializable
{
public:

  VehicleTransIconSettings()
    : m_isEnabled(true)
    , m_proportion(146.0f, 400.0f)
    , m_aspectRatioOfVehicle(0.46f)
  {
  }

  SERIALIZABLE(VehicleTransIconSettings)
  {
    ADD_BOOL_MEMBER(isEnabled);
    ADD_MEMBER(osg::Vec2f, proportion);
    ADD_FLOAT_MEMBER(aspectRatioOfVehicle);
  }

  bool  m_isEnabled;
  osg::Vec2f m_proportion;  // 1st value is the valid pixel height of vehicle, 2nd is the pixel height of the pic
  float m_aspectRatioOfVehicle;

};
extern pc::util::coding::Item<VehicleTransIconSettings> g_vehicleTransIconSettings;


//!
//! VehicleTransIconManager
//!
class VehicleTransIconManager
{
public:
  VehicleTransIconManager();
  virtual ~VehicleTransIconManager();

  void init(pc::assets::ImageOverlays* f_imageOverlays);
  void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
  void update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework, bool f_blockTouch);
  void setVehicleIconSize(core::CustomFramework* f_framework);
  void calculateHoriVehicleIconSize(osg::Vec2f &f_iconSize);
  void calculateFloatVehicleIconSize(osg::Vec2f &f_iconSize);
  bool transIconChangedBySoftSwitch(core::CustomFramework* f_framework,bool f_blockTouch, bool is_floatScreen, bool f_softSwitchAvailable);
  bool getSoftSwitchStatus() const { return m_isTransparent; }
  static void setVehicleTransIcon(bool f_show);

private:
  //! Copy constructor is not permitted.
  VehicleTransIconManager (const VehicleTransIconManager& other); // = delete
  //! Copy assignment operator is not permitted.
  VehicleTransIconManager& operator=(const VehicleTransIconManager& other); // = delete

  unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_vehicleTransIcons;
  cc::views::planview::PlanViewCullCallback* m_planViewCullCall;
  bool m_mat_b;
  bool m_parkActive;
  bool m_isTransparent;
  static bool m_iconShow;
};


//!
//! VehicleTransIcon
//!
class VehicleTransIcon: public cc::assets::uielements::CustomImageOverlays, public cc::assets::button::ButtonPopController
{
public:
  VehicleTransIcon(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~VehicleTransIcon();
  virtual void traverse(osg::NodeVisitor& f_nv) override;
  void doUpdate(osg::NodeVisitor& f_nv, bool f_blockTouch) override
  {
    m_manager.update(this, m_customFramework, f_blockTouch);
  }

private:
  //! Copy constructor is not permitted.
  VehicleTransIcon (const VehicleTransIcon& other); // = delete
  //! Copy assignment operator is not permitted.
  VehicleTransIcon& operator=(const VehicleTransIcon& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  VehicleTransIconManager m_manager;
};

class FreeParkingVehicleTransIconOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(FreeParkingVehicleTransIconOverlaySettings)
    {
        ADD_STRING_MEMBER(freeParkingVehicleTransIconPath);
        ADD_MEMBER(osg::Vec2i, freeParkingVehicleTransIconPos);
        ADD_FLOAT_MEMBER(meterPerPixelFreeParking);
        ADD_FLOAT_MEMBER(viewportOffset);
    }

    std::string    m_freeParkingVehicleTransIconPath = "cc/resources/icons/freeparking/FreeParkingVehicleTransIconOverlay.png";
    osg::Vec2i     m_freeParkingVehicleTransIconPos{685, 518};
    vfc::float32_t m_meterPerPixelFreeParking = 0.01;
    vfc::float32_t m_viewportOffset = 0.0f;
};

extern pc::util::coding::Item<FreeParkingVehicleTransIconOverlaySettings> g_freeParkingVehicleTransIconOverlaySettings;

class FreeParkingVehicleTransIconOverlay : public pc::assets::ImageOverlays
{
public:
public:
    FreeParkingVehicleTransIconOverlay(
        pc::core::Framework* f_framework,
        cc::core::AssetId                 f_assetId,
        osg::Camera*                      f_referenceView = nullptr);

    void traverse(osg::NodeVisitor& f_nv) override;

private:
    void init();
    void update(vfc::float64_t f_time);

private:
    pc::core::Framework*               m_framework;
    vfc::uint32_t                     m_modifiedCount = ~0u;
    pc::assets::IconGroup             m_icons;
    std::string                       m_imagePath;
    osg::Vec2i                        m_imagePos;
};

class TurnArroundProjectionUpdateVisitor : public osg::NodeVisitor
{
public:
    TurnArroundProjectionUpdateVisitor(const std::string f_imagePath);

    void apply(osg::Node& f_node) override;

private:
    vfc::float64_t m_top    = 0.0;
    vfc::float64_t m_bottom = 0.0;
    vfc::float64_t m_left   = 0.0;
    vfc::float64_t m_right  = 0.0;
    const std::string m_imagePath;
};

class MeterPerPixelProjectionUpdateVisitor : public osg::NodeVisitor
{
public:
    MeterPerPixelProjectionUpdateVisitor(vfc::float64_t f_meterPerPixel);

    void apply(osg::Node& f_node) override;

private:
    vfc::float64_t m_top    = 0.0;
    vfc::float64_t m_bottom = 0.0;
    vfc::float64_t m_left   = 0.0;
    vfc::float64_t m_right  = 0.0;
    vfc::float64_t m_meterPerPixel  = 0.0;
};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_VEHICLETRANSICON_H
