//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_AUGMENTEDVIEWTRANSITION_SRC_PERIODICSCANANIMATION_H_
#define CC_ASSETS_AUGMENTEDVIEWTRANSITION_SRC_PERIODICSCANANIMATION_H_

#include "cc/assets/augmentedview/inc/TransitionWaveAnimation.h"

#include <limits>

namespace cc
{
namespace assets {

class PeriodicScanAnimation
{
public:

  PeriodicScanAnimation(float f_animationDuration, float f_maxWaveRadius, float f_interBurstTime, unsigned int f_burstSize,  float f_intraBurstTime)
  {
    reset(f_animationDuration, f_maxWaveRadius, f_interBurstTime, f_burstSize, f_intraBurstTime);
  }

  PeriodicScanAnimation()
  {
    reset(3.0f, 40.0f, 4.0f, 1u, 1.f);
  }

  void reset(float f_animationDuration, float f_maxWaveRadius, float f_interBurstTime, unsigned int f_burstSize,  float f_intraBurstTime)
  {
    assert(f_animationDuration > 0.0f);
    assert(f_burstSize > 0u);
    assert(f_interBurstTime >= 0.0f);
    assert(f_intraBurstTime >= 0.0f);
    assert((f_intraBurstTime > 0.0f && f_burstSize > 1u) || f_interBurstTime > 0.0f);
    f_burstSize = std::max(f_burstSize, 1u);

    m_animationDuration = f_animationDuration;
    m_maxWaveRadius = f_maxWaveRadius;
    m_stopAnimation = false;

    // Calculate the max. number of scan waves visible at a time
    float totalBurstTime = f_intraBurstTime*(static_cast<float>(f_burstSize)-1.0f)+f_interBurstTime;
    unsigned int fullConcurrentBurstWaves = static_cast<unsigned int>(std::floor(f_animationDuration/totalBurstTime));
    float partialBurstTime = f_animationDuration - totalBurstTime*static_cast<float>(fullConcurrentBurstWaves);
    unsigned int partialBurstWaves = (f_burstSize > 1u) ? static_cast<unsigned int>(std::ceil(partialBurstTime/f_intraBurstTime)) : static_cast<unsigned int>(std::ceil(partialBurstTime));
    partialBurstWaves = std::min(f_burstSize, partialBurstWaves);

    std::size_t numConcurrentAnimations = fullConcurrentBurstWaves + partialBurstWaves;

    m_scanWaveAnimations.resize(numConcurrentAnimations);
    for (std::size_t i = 0u; i < m_scanWaveAnimations.size(); ++i)
    {
      m_scanWaveAnimations[i].reset(f_animationDuration, f_maxWaveRadius, true);
    }

    // Allocate "numConcurrentAnimations" ring instances w/o alpha blending
    m_lastWaveEmissionTime = -std::numeric_limits<float>::max();

    m_activeWaves = 0u;
    m_currentBurstCount = 0u;
    m_burstSize = f_burstSize;
    m_interBurstTime = f_interBurstTime;
    m_intraBurstTime = f_intraBurstTime;
  }

  std::size_t getMaxConcurrentWaves() const
  {
    return m_scanWaveAnimations.size();
  }

  std::size_t getActiveWaves() const
  {
    return m_activeWaves;
  }

  float getWaveRadius(std::size_t f_index) const
  {
    assert(f_index < m_activeWaves);
    return getActiveWave(f_index).getWaveRadius();
  }

  float getWaveAlphaFade(std::size_t f_index) const
  {
    assert(f_index < m_activeWaves);
    return getActiveWave(f_index).getWaveAlphaFade();
  }

  float getCameraFadeFactor(std::size_t f_index) const
  {
    return getActiveWave(f_index).getCameraFadeFactor();
  }

  // this will stop the re-start of the animation
  void setStopAnimation(bool f_stopAnimation)
  {
    m_stopAnimation = f_stopAnimation;
  }

  void update(float f_currentTime)
  {
    m_activeWaves = 0u;
    for (std::size_t i = 0u; i < m_scanWaveAnimations.size(); ++i)
    {
      auto& ta = m_scanWaveAnimations[i];
      ta.update(f_currentTime);
      if (ta.isRunning())
      {
        ++m_activeWaves;
      }
    }

    float elapsedTime = f_currentTime - m_lastWaveEmissionTime;

    bool isBursting = m_currentBurstCount > 0u;

    float emissionDelay = isBursting ? m_intraBurstTime : m_interBurstTime;

    if (elapsedTime >= emissionDelay)
    {
      // Find unused animation controller
      for (std::size_t i = 0u; i < m_scanWaveAnimations.size(); ++i)
      {
        auto& ta = m_scanWaveAnimations[i];
        //! do not restart animation if stop animation has been requested
        if (!ta.isRunning() && !m_stopAnimation)
        {
          m_lastWaveEmissionTime = f_currentTime;
          ta.start(m_animationDuration, m_maxWaveRadius, true);
          ta.update(f_currentTime);
          ++m_activeWaves;

          m_currentBurstCount = (m_currentBurstCount + 1u)%m_burstSize;
          break;
        }
      }
    }
  }

private:
  const TransitionWaveAnimation& getActiveWave(std::size_t f_index) const
  {
    assert(f_index < m_activeWaves);
    std::size_t i = 0u;
    for (std::size_t j = 0u; j < m_scanWaveAnimations.size(); ++j)
    {
      const auto& ta = m_scanWaveAnimations[j];
      if (ta.isRunning())
      {
        if (i == f_index)
        {
          return ta;
        }
        ++i;
      }
    }
    assert(false);
    return m_scanWaveAnimations.front();
  }

  std::size_t m_activeWaves;
  float m_lastWaveEmissionTime;

  unsigned int m_currentBurstCount;
  unsigned int m_burstSize;
  float m_interBurstTime;
  float m_intraBurstTime;


  float m_animationDuration;
  float m_maxWaveRadius;
  float m_waveLength;
  
  bool m_stopAnimation;
  std::vector<TransitionWaveAnimation> m_scanWaveAnimations;
};

}
}


#endif
