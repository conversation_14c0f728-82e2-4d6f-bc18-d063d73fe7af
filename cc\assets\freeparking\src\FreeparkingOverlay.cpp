/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "cc/assets/freeparking/inc/FreeparkingOverlay.h"
// #include "vis-dongfeng/assets/freeparking/free_parking_overlay.hpp"
#include "cc/daddy/inc/CustomDaddyPorts.h"


#include "pc/generic/util/logging/inc/Logging.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/LineSegment2D.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
using pc::util::logging::g_AppContext;

#include <osg/Geometry>
#include <osg/Image>
#include <osg/Texture>
#include <osgDB/ReadFile>

namespace cc
{
namespace assets
{
namespace freeparking
{

pc::util::coding::Item<FreeParkingOverlaySettings> g_freeParkingOverlaySettings("FreeParkingOverlaySettings");

FreeParkingOverlayTouchEventHandler::FreeParkingOverlayTouchEventHandler()
    :cc::util::touchmanipulator::TouchEventHandler(true)
{
}

bool FreeParkingOverlayTouchEventHandler::handle(
    const osgGA::GUIEventAdapter& f_ea,
    osgGA::GUIActionAdapter&      f_aa,
    osg::Object*                  f_obj,
    osg::NodeVisitor*             f_nv)
{
    // const auto l_now = std::chrono::system_clock::now();
    auto l_freeParkingOverlay = dynamic_cast<FreeParkingOverlay*>(f_obj);
    bool l_hasEvents          = cc::util::touchmanipulator::TouchEventHandler::handle(f_ea, f_aa, f_obj, f_nv);
    if (l_hasEvents)
    {
        if (l_freeParkingOverlay)
        {
            return handleEvents(f_ea, l_freeParkingOverlay);
        }
    }
    return false;
}

bool FreeParkingOverlayTouchEventHandler::handleEvents(
    const osgGA::GUIEventAdapter& f_ea,
    FreeParkingOverlay*           f_freeParkingOverlay)
{
    if (1U != getNumTouchPoints())
    {
        return false;
    }

    const auto& l_touchPoint = getTouchPoint(0U);

    return f_freeParkingOverlay->handleClick(l_touchPoint);
}

namespace
{

osg::Geode* createTexturedGeode(osg::Geometry* f_geometry, const std::string& f_textureName)
{
    osg::ref_ptr<osg::Geode> l_geode = new osg::Geode();
    l_geode->addDrawable(f_geometry); // PRQA S 3803
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    osg::ref_ptr<osg::StateSet>              l_stateSet = l_geode->getOrCreateStateSet();
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803

    // Load texture
    osg::ref_ptr<osg::Image> l_image = osgDB::readImageFile(f_textureName);
    if (l_image.valid())
    {
        osg::ref_ptr<osg::Texture> l_texture = new osg::Texture2D(l_image);
        l_texture->setResizeNonPowerOfTwoHint(false);
        l_texture->setUnRefImageDataAfterApply(true);
        // set texture repetition
        l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
        l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::REPEAT);
        // set texture filtering
        l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
        l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
        l_stateSet->setTextureAttribute(0, l_texture, osg::StateAttribute::ON);
    }
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);

    return l_geode.release();
}

} // namespace

void FreeParkingOverlay::WarningGeodes::init()
{
    constexpr vfc::float32_t height = 0.1f;
    const auto&              s      = g_freeParkingOverlaySettings;
    const vfc::float32_t     bottom = s->m_bottomPercentage;
    const vfc::float32_t     top    = s->m_topPercentage;
    const vfc::float32_t     mid    = 1.0f - bottom - top;
    struct AreaDef
    {
        float l, r, b, t;
    };

    // clang-format off
    const AreaDef defs[NumArea] = {
        {1.0f - top , 1.0f       , 0.5f, 1.0f}, // TopLeft
        {bottom     , 1.0f - top , 0.5f, 1.0f}, // MiddleLeft
        {0.0f       , bottom     , 0.5f, 1.0f}, // BottomLeft
        {1.0f - top , 1.0f       , 0.0f, 0.5f}, // TopRight
        {bottom     , 1.0f - top , 0.0f, 0.5f}, // MiddleRight
        {0.0f       , bottom     , 0.0f, 0.5f}  // BottomRight
    };
    // clang-format on
    for (std::size_t i = 0; i < NumArea; ++i)
    {
        const auto& d = defs[i];
        osg::Vec3f  corner{d.l, d.b, height}, offset{-0.5f, -0.5f, 0.0f};
        auto&       geode = m_geodes.at(i);
        geode             = createTexturedGeode(
            osg::createTexturedQuadGeometry(
                corner + offset, osg::Vec3f{d.r - d.l, 0, 0}, osg::Vec3f{0, d.t - d.b, 0}, d.l, d.b, d.r, d.t),
            s->m_contourTexturePath);
        if (auto* g = geode->getDrawable(0u)->asGeometry())
        {
            auto* colors = new osg::Vec4Array(1u);
            (*colors)[0] = s->m_unavailableColor;
            g->setColorArray(colors, osg::Array::BIND_OVERALL);
        }
        osg::Group::addChild(geode);
    }
}

FreeParkingOverlay::FreeParkingOverlay(osg::Camera* f_view, pc::core::Framework* f_framework)
    : m_quadGeometry{osg::createTexturedQuadGeometry(
          osg::Vec3(-0.5f, -0.5f, 0.0f),
          osg::Vec3(1.0f, 0.0f, 0.0f),
          osg::Vec3(0.0f, 1.0f, 0.0f),
          0.0f,
          0.0f,
          1.0f,
          1.0f)}
    , m_view{f_view}
    , m_clicked{false}
    , m_position{g_freeParkingOverlaySettings->m_startingPos}
    , m_grabbedPosition{osg::Vec3f{0.0f, 0.0f, 0.0f}}
    , m_rotation{0.0f}
    , m_framework{f_framework}
    , m_warningGeodes{new WarningGeodes{}}
{
    addEventCallback(new FreeParkingOverlayTouchEventHandler());

    auto l_vehicleData = dynamic_cast<pc::vehicle::MechanicalData*>(
        pc::util::coding::getCodingManager()->getItem("VehicleMechanicalData"));
    m_scale = osg::Vec3f(
        l_vehicleData->m_wheelbase + l_vehicleData->m_axleToBumperDistanceFront +
            l_vehicleData->m_axleToBumperDistanceRear,
        l_vehicleData->m_widthWithMirrors,
        1.0f);

    m_quadMatrixTransform   = new osg::MatrixTransform;
    m_rotateMatrixTransform = new osg::MatrixTransform;
    m_flipMatrixTransform   = new osg::MatrixTransform;

    m_quadMatrixTransform->addChild(
        createTexturedGeode(m_quadGeometry, g_freeParkingOverlaySettings->m_contourTexturePath));
    m_quadMatrixTransform->addChild(createTexturedGeode(
        osg::createTexturedQuadGeometry(
            osg::Vec3(-0.5f, -0.5f, 0.0f),
            osg::Vec3(1.0f, 0.0f, 0.0f),
            osg::Vec3(0.0f, 1.0f, 0.0f),
            0.0f,
            0.0f,
            1.0f,
            1.0f),
        g_freeParkingOverlaySettings->m_vehicleContourTexturePath));
    m_quadMatrixTransform->addChild(m_warningGeodes);
    m_quadMatrixTransform->setMatrix(osg::Matrix::scale(m_scale));

    m_rotateMatrixTransform->addChild(
        createTexturedGeode(m_quadGeometry, g_freeParkingOverlaySettings->m_rotateTexturePath));
    m_rotateMatrixTransform->setMatrix(
        osg::Matrix::scale(osg::Vec3f(0.4f, 0.4f, 1.0f)) *
        osg::Matrix::translate(osg::Vec3f(m_scale.x() / 2.0f + 0.1f, 0.0f, 0.0f)));

    m_flipMatrixTransform->addChild(
        createTexturedGeode(m_quadGeometry, g_freeParkingOverlaySettings->m_flipTexturePath));
    m_flipMatrixTransform->setMatrix(osg::Matrix::scale(osg::Vec3f(0.4f, 0.4f, 1.0f)));

    addChild(m_quadMatrixTransform);
    addChild(m_rotateMatrixTransform);
    addChild(m_flipMatrixTransform);
    changeColor(g_freeParkingOverlaySettings->m_unavailableColor);

    setNumChildrenRequiringUpdateTraversal(3u);
    resetState();
    updateMatrix();
}

void FreeParkingOverlay::changeColor(const osg::Vec4f& f_color)
{
    osg::Vec4Array* l_colors = new osg::Vec4Array(1u);
    (*l_colors)[0u]          = f_color;
    m_quadGeometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);
}

void FreeParkingOverlay::updatePosition(const osg::Vec3f& f_position)
{
    auto l_viewport = m_view->getViewport();

    osg::Vec3f l_trCornerViewport = clickToRealWorldPosition(osg::Vec2f(l_viewport->x(), l_viewport->y()));
    osg::Vec3f l_blCornerViewport = clickToRealWorldPosition(
        osg::Vec2f(l_viewport->x() + l_viewport->width(), l_viewport->y() + l_viewport->height()));

    vfc::float32_t l_positionX = f_position.x();
    vfc::float32_t l_positionY = f_position.y();

    if (l_positionX < l_blCornerViewport.x())
    {
        l_positionX = l_blCornerViewport.x();
    }
    else if (l_positionX > l_trCornerViewport.x())
    {
        l_positionX = l_trCornerViewport.x();
    }

    if (l_positionY < l_blCornerViewport.y())
    {
        l_positionY = l_blCornerViewport.y();
    }
    else if (l_positionY > l_trCornerViewport.y())
    {
        l_positionY = l_trCornerViewport.y();
    }

    m_position = osg::Vec3f(l_positionX, l_positionY, 0.0f);

    updateMatrix();
}

void FreeParkingOverlay::updateRotation(float f_rotation)
{
    m_rotation = f_rotation;
    updateMatrix();
}

bool FreeParkingOverlay::handleClick(const cc::util::touchmanipulator::TouchEventHandler::TouchPoint& f_touch)
{
    osg::Vec2f l_clickPosition = osg::Vec2f(f_touch.x, f_touch.y);
    osg::Vec3f l_position      = clickToRealWorldPosition(l_clickPosition);
    osg::Vec4f l_localPosition = osg::Vec4f(l_position.x(), l_position.y(), l_position.z(), 1.0) * getInverseMatrix() *
                                 m_quadMatrixTransform->getInverseMatrix();

    if (f_touch.phase == osgGA::GUIEventAdapter::TOUCH_BEGAN)
    {
        if (isRotationClicked(l_position))
        {
            m_grabbedPosition = l_position - m_position;
            m_touchAction     = ROTATE;
            m_startRotation   = m_rotation;
            m_clicked         = true;
            return true;
        }
        if (isFlipClicked(l_position))
        {
            updateRotation(m_rotation + osg::PI);
        }
        else if (std::abs(l_localPosition.x()) < 0.5f && std::abs(l_localPosition.y()) < 0.5f)
        {
            m_touchAction = MOVE;
            if (!m_clicked)
            {
                m_clicked         = true;
                m_grabbedPosition = l_position - m_position;
            }

            updatePosition(l_position - m_grabbedPosition);
            return true;
        }
        else
        {
            if (m_clicked)
            {
                touchEnded();
            }
            return false;
        }
    }
    else if (f_touch.phase == osgGA::GUIEventAdapter::TOUCH_MOVED)
    {
        if (m_clicked)
        {
            if (m_touchAction == ROTATE)
            {
                osg::Vec3f l_diffEnd    = l_position - m_position;
                osg::Vec3f l_diffStart  = m_grabbedPosition;
                float      l_angleStart = std::atan2(l_diffStart.y(), l_diffStart.x());
                float      l_angleEnd   = std::atan2(l_diffEnd.y(), l_diffEnd.x());
                updateRotation(m_startRotation + (l_angleEnd - l_angleStart));
            }
            else if (m_touchAction == MOVE)
            {
                float l_angle = std::atan2(m_position.y(), m_position.x());
                XLOG_INFO(g_AppContext, "FreeParkingOverlay: l_angle: " << osg::RadiansToDegrees(l_angle));
                updatePosition(l_position - m_grabbedPosition);
            }
            return true;
        }
    }
    else if (f_touch.phase == osgGA::GUIEventAdapter::TOUCH_ENDED)
    {
        touchEnded();
        return false;
    }

    return false;
}

bool FreeParkingOverlay::isBecomeVisible()
{
    using namespace std::chrono;
    const auto l_diffMs = duration_cast<milliseconds>(system_clock::now() - m_lastEventTime).count();
    m_lastEventTime     = system_clock::now();

    if (l_diffMs > 500)
    {
        return true;
    }
    else
    {
        return false;
    }
}

osg::Vec3f FreeParkingOverlay::getPosition() const
{
    return m_position;
}

float FreeParkingOverlay::getRotation() const
{
    return m_rotation;
}

void FreeParkingOverlay::updateMatrix()
{
    setMatrix(
        osg::Matrix::scale(osg::Vec3f(1.0, 1.0f, 1.0f)) * osg::Matrix::rotate(m_rotation, osg::Z_AXIS) *
        osg::Matrix::translate(m_position));

    std::array<osg::Vec4f, 4> l_cornerPoints{};
    l_cornerPoints[0] = osg::Vec4f(m_scale.x() / 2.0f, m_scale.y() / 2.0f, 0.0f, 1.0f);
    l_cornerPoints[1] = osg::Vec4f(m_scale.x() / 2.0f, -m_scale.y() / 2.0f, 0.0f, 1.0f);
    l_cornerPoints[2] = osg::Vec4f(-m_scale.x() / 2.0f, m_scale.y() / 2.0f, 0.0f, 1.0f);
    l_cornerPoints[3] = osg::Vec4f(-m_scale.x() / 2.0f, -m_scale.y() / 2.0f, 0.0f, 1.0f);
    for (unsigned int i = 0; i < l_cornerPoints.size(); i++)
    {
        l_cornerPoints[i] = l_cornerPoints[i] * osg::Matrix::rotate(m_rotation, osg::Z_AXIS);
        l_cornerPoints[i] =
            osg::Vec4f(l_cornerPoints[i].x() + m_position.x(), l_cornerPoints[i].y() + m_position.y(), 0.0f, 1.0f);
    }
    osg::Vec2f l_frontLeft  = {l_cornerPoints[0].x(), l_cornerPoints[0].y()};
    osg::Vec2f l_frontRight = {l_cornerPoints[1].x(), l_cornerPoints[1].y()};
    osg::Vec2f l_rearLeft   = {l_cornerPoints[2].x(), l_cornerPoints[2].y()};
    osg::Vec2f l_rearRight  = {l_cornerPoints[3].x(), l_cornerPoints[3].y()};
    // send output
    // auto& l_fpSlot                           = cc::daddy::CustomDaddyPorts::sm_FreeSlotInfoDaddySenderPort.reserve();
    // l_fpSlot.m_Data.m_freeSlotFrontLeft.m_x  = l_frontLeft.x();
    // l_fpSlot.m_Data.m_freeSlotFrontRight.m_x = l_frontRight.x();
    // l_fpSlot.m_Data.m_freeSlotRearRight.m_x  = l_rearRight.x();
    // l_fpSlot.m_Data.m_freeSlotRearLeft.m_x   = l_rearLeft.x();
    // l_fpSlot.m_Data.m_freeSlotFrontLeft.m_y  = l_frontLeft.y();
    // l_fpSlot.m_Data.m_freeSlotFrontRight.m_y = l_frontRight.y();
    // l_fpSlot.m_Data.m_freeSlotRearRight.m_y  = l_rearRight.y();
    // l_fpSlot.m_Data.m_freeSlotRearLeft.m_y   = l_rearLeft.y();
    // cc::daddy::CustomDaddyPorts::sm_FreeSlotInfoDaddySenderPort.deliver();
}

void FreeParkingOverlay::resetState()
{
    m_clicked         = false;
    m_position        = g_freeParkingOverlaySettings->m_startingPos;
    m_position.x()    = pc::vehicle::g_mechanicalData->getCenter().x();
    m_grabbedPosition = osg::Vec3f{0.0f, 0.0f, 0.0f};
    m_rotation        = 0.0f;
}

void FreeParkingOverlay::touchEnded()
{
    m_touchAction = NONE;
    m_clicked     = false;
    updateMatrix();
}


void FreeParkingOverlay::updateSlotStatus()
{
    static vfc::uint16_t       l_lastSequenceNumber = 0;
    cc::core::CustomFramework* l_framework          = m_framework->asCustomFramework();
    bool                       isObstaclePresent    = false;

    const auto customFramework = m_framework->asCustomFramework();
    // if (customFramework->asCustomFramework()->m_ApaHmiDaddyReceiver.hasData())
    // {
    //     const auto& l_data              = customFramework->asCustomFramework()->m_ApaHmiDaddyReceiver.getData();
    //     m_apaHmi.m_apaStatus            = l_data->m_Data.m_apaStatus;
    //     m_apaHmi.m_apaActiveSubFunc     = l_data->m_Data.m_apaActiveSubFunc;
    //     m_apaHmi.m_apaParkType          = l_data->m_Data.m_apaParkType;
    //     m_apaHmi.m_freeParkSlotValidity = l_data->m_Data.m_freeParkSlotValidity;
    // }
    // if (customFramework->asCustomFramework()->m_AWPObstacleAreaReceiver.hasData())
    // {
    //     const auto& l_data = customFramework->asCustomFramework()->m_AWPObstacleAreaReceiver.getData();
    //     m_obstacleArea     = l_data->m_Data;
    // }
    // if (customFramework->asCustomFramework()->m_FreeParkInputDataReceiver.hasData())
    // {
    //     const auto& l_data = customFramework->asCustomFramework()->m_FreeParkInputDataReceiver.getData();
    //     m_apaHmi.m_freeParkAvailSts =
    //         static_cast<rbp::vis_dongfeng::EFreeParkAvailSts>(l_data->m_Data.m_FreeParkSlotParkableSts);
    // }

    // switch (m_apaHmi.m_freeParkAvailSts)
    // {
    // case EFreeParkAvailSts::FreePark_Only_Current_Direction_Available:
    // case EFreeParkAvailSts::FreePark_Need_Reverse:
    // {
    //     // blue
    //     changeColor(g_freeParkingOverlaySettings->m_availableColor);
    //     break;
    // }
    // case EFreeParkAvailSts::FreePark_Slot_Blocked_By_Object:
    // {
    //     // white + red
    //     changeColor(g_freeParkingOverlaySettings->m_movingColor);
    //     isObstaclePresent = true;
    //     break;
    // }
    // case EFreeParkAvailSts::FreePark_Both_Dir_Available:
    // case EFreeParkAvailSts::FreePark_Path_PlanningFailed:
    // case EFreeParkAvailSts::FreePark_Path_Calculating:
    // case EFreeParkAvailSts::FreePark_None:
    // {
    //     // white
    //     changeColor(g_freeParkingOverlaySettings->m_movingColor);
    //     break;
    // }
    // default:
    // {
    //     break;
    // }
    // }

    // if (isObstaclePresent)
    // {
    //     const bool& horiTopLeft     = m_obstacleArea.m_horizontalLine_TopLeft;
    //     const bool& horiTopRight    = m_obstacleArea.m_horizontalLine_TopRight;
    //     const bool& horiBottomLeft  = m_obstacleArea.m_horizontalLine_BottomLeft;
    //     const bool& horiBottomRight = m_obstacleArea.m_horizontalLine_BottomRight;
    //     const bool& vertTopLeft     = m_obstacleArea.m_verticalLine_TopLeft;
    //     const bool& vertCenterLeft  = m_obstacleArea.m_verticalLine_CenterLeft;
    //     const bool& vertBottomLeft  = m_obstacleArea.m_verticalLine_BottomLeft;
    //     const bool& vertTopRight    = m_obstacleArea.m_verticalLine_TopRight;
    //     const bool& vertCenterRight = m_obstacleArea.m_verticalLine_CenterRight;
    //     const bool& vertBottomRight = m_obstacleArea.m_verticalLine_BottomRight;
    //     m_warningGeodes->setEnable(WarningGeodes::WarningArea::TopLeft, horiTopLeft && vertTopLeft);
    //     m_warningGeodes->setEnable(WarningGeodes::WarningArea::MiddleLeft, vertCenterLeft);
    //     m_warningGeodes->setEnable(WarningGeodes::WarningArea::BottomLeft, horiBottomLeft && vertBottomLeft);
    //     m_warningGeodes->setEnable(WarningGeodes::WarningArea::TopRight, horiTopRight && vertTopRight);
    //     m_warningGeodes->setEnable(WarningGeodes::WarningArea::MiddleRight, vertCenterRight);
    //     m_warningGeodes->setEnable(WarningGeodes::WarningArea::BottomRight, horiBottomRight && vertBottomRight);
    // }
    // else
    // {
    //     m_warningGeodes->setAllEnable(false);
    // }
}

osg::Vec3f FreeParkingOverlay::clickToRealWorldPosition(const osg::Vec2f& f_clickPosition) const
{
    double left, right, bottom, top, zNear, zFar;
    if (m_view->getProjectionMatrixAsOrtho(left, right, bottom, top, zNear, zFar))
    {
        auto l_matViewMatrix       = m_view->getViewMatrix();
        auto l_matProjectionMatrix = m_view->getProjectionMatrix();
        auto l_MVP                 = l_matViewMatrix * l_matProjectionMatrix;
        auto viewport              = m_view->getViewport();
        auto windowMatrix          = viewport->computeWindowMatrix();
        auto inverseWindowMatrix   = osg::Matrix::inverse(windowMatrix);

        // Convert screen coordinates to NDC using inverse window matrix
        osg::Vec3f screenCoords(f_clickPosition.x(), f_clickPosition.y(), 0.0f);
        osg::Vec3f ndcCoords = screenCoords * inverseWindowMatrix;

        const osg::Vec4f clipCoords = {ndcCoords.x(), -ndcCoords.y(), -1.0f, 1.0f};
        // const osg::Vec4f   clipCoords  = {ndc_x, ndc_y, -1.0f, 1.0f};
        const osg::Matrixd inverseMVP  = osg::Matrixf::inverse(l_MVP);
        osg::Vec4f         worldCoords = clipCoords * inverseMVP;
        worldCoords /= worldCoords.w();
        return osg::Vec3f{worldCoords.x(), worldCoords.y(), 0.0f};
    }
    else
    {
        // Convert 2D click to 3D ray
        auto l_matViewMatrix       = m_view->getViewMatrix();
        auto l_matProjectionMatrix = m_view->getProjectionMatrix();
        auto l_windowMatrix        = m_view->getViewport()->computeWindowMatrix();

        auto l_MVPW        = l_matViewMatrix * l_matProjectionMatrix * l_windowMatrix;
        auto l_inverseMVPW = osg::Matrix::inverse(l_MVPW);

        // Get the click position
        const vfc::float32_t l_windowX = f_clickPosition.x();
        const vfc::float32_t l_windowY = f_clickPosition.y();

        osg::Vec3f nearPoint(l_windowX, l_windowY, 0.0);
        osg::Vec3f farPoint(l_windowX, l_windowY, 1.0);

        osg::Vec3f nearPointWorld = nearPoint * l_inverseMVPW;
        osg::Vec3f farPointWorld  = farPoint * l_inverseMVPW;

        const vfc::float32_t z1 = nearPointWorld.z();
        const vfc::float32_t z2 = farPointWorld.z();

        const vfc::float32_t x1 = nearPointWorld.x();
        const vfc::float32_t x2 = farPointWorld.x();

        const vfc::float32_t y1 = nearPointWorld.y();
        const vfc::float32_t y2 = farPointWorld.y();

        // Calculate parameter t
        const vfc::float32_t t = -z1 / (z2 - z1);

        // Calculate intersection point
        const vfc::float32_t     x = -x1 + t * (x2 - x1);
        const vfc::float32_t     y = y1 + t * (y2 - y1);
        constexpr vfc::float32_t z = 0.0;

        return osg::Vec3f(-x, y, z);
    }
}

bool FreeParkingOverlay::isFlipClicked(const osg::Vec3f& f_position) const
{
    osg::Vec4f l_vec4Position  = osg::Vec4f(f_position.x(), f_position.y(), f_position.z(), 1.0f);
    osg::Vec4f l_localPosition = l_vec4Position * getInverseMatrix() * m_flipMatrixTransform->getInverseMatrix();

    return std::abs(l_localPosition.x()) < 0.5f && std::abs(l_localPosition.y()) < 0.5f;
}

bool FreeParkingOverlay::isRotationClicked(const osg::Vec3f& f_position) const
{
    osg::Vec4f l_vec4Position  = osg::Vec4f(f_position.x(), f_position.y(), f_position.z(), 1.0f);
    osg::Vec4f l_localPosition = l_vec4Position * getInverseMatrix() * m_rotateMatrixTransform->getInverseMatrix();

    return std::abs(l_localPosition.x()) < 0.5f && std::abs(l_localPosition.y()) < 0.5f;
}

void FreeParkingOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        updateSlotStatus();
    #ifdef TARGET_STANDALONE
        debug();
    #endif
    }

    osg::MatrixTransform::traverse(f_nv);
}

} // namespace freeparking
} // namespace assets
} // namespace cc