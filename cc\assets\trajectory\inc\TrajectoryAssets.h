//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_ASSETS_H
#define CC_ASSETS_TRAJECTORY_ASSETS_H

#include "cc/assets/trajectory/inc/DL1.h"
#include "cc/assets/trajectory/inc/OutermostLine.h"
#include "cc/assets/trajectory/inc/OutermostLineColorful.h"
#include "pc/svs/core/inc/Asset.h"

namespace cc
{
namespace assets
{
namespace trajectory
{
struct TrajectoryParams_st;
struct DIDescriptor_st;

extern assets::trajectory::TrajectoryParams_st g_trajParams;
extern assets::trajectory::DIDescriptor_st     g_DIDescriptor;
extern assets::trajectory::DIDescriptor_st     g_DIColorfulDescriptor;

//!
//! @brief composit of all outermost lines
//!
//!
class OutermostLinesAsset : public pc::core::Asset
{
public:
    OutermostLinesAsset(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params,
        const DIDescriptor_st&     f_diDescriptor);

    OutermostLine* getLeft()
    {
        return m_left.get();
    }

    OutermostLine* getRight()
    {
        return m_right.get();
    }

private:
    osg::ref_ptr<OutermostLine> m_left;
    osg::ref_ptr<OutermostLine> m_right;
};

class OutermostLinesAssetColorful : public pc::core::Asset
{
public:
    OutermostLinesAssetColorful(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params,
        const DIDescriptor_st&     f_diDescriptor);

    OutermostLineColorful* getLeft()
    {
        return m_left.get();
    }

    OutermostLineColorful* getRight()
    {
        return m_right.get();
    }

private:
    osg::ref_ptr<OutermostLineColorful> m_left;
    osg::ref_ptr<OutermostLineColorful> m_right;
};

//!
//! @brief DistanceLineAsset
//!
//!
class DistanceLineAsset : public pc::core::Asset
{
public:
    DistanceLineAsset(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params,
        const OutermostLine*       f_leftOutermostLine,
        const OutermostLine*       f_rightOutermostLine,
        unsigned int               f_numLayoutPoints);

    DL1* getDistanceLine()
    {
        return m_distanceLine.get();
    }

private:
    osg::ref_ptr<DL1> m_distanceLine;
};

//!
//! @brief Composit asset of wheel tracks
//!
//!
class WheelTracksAsset : public pc::core::Asset
{
public:
    WheelTracksAsset(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params,
        const DL1*                 f_distanceLine);
};

//!
//! @brief Composit asset of Trailer Assist Line
//!
//!
class TrailerAssistLineAsset : public pc::core::Asset
{
public:
    TrailerAssistLineAsset(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params);
};

//!
//! ActionPointsAsset
//!
class ActionPointsAsset : public pc::core::Asset
{
public:
    ActionPointsAsset(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params,
        const DL1*                 f_distanceLine);
};

//!
//! CoverPlateAsset
//!
class CoverPlateAsset : public pc::core::Asset
{
public:
    CoverPlateAsset(
        cc::core::AssetId          f_assetId,
        pc::core::Framework*       f_framework,
        const TrajectoryParams_st& f_params,
        unsigned int               f_numOfVerts);
};

//!
//! RefLineAsset
//!
class RefLineAsset : public pc::core::Asset
{
public:
    RefLineAsset(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, const TrajectoryParams_st& f_params);
};

//!
//! TrailerHitchAsset
//!
// class TrailerHitchTrajectoryAsset : public pc::core::Asset
// {
// public:

//   TrailerHitchTrajectoryAsset(
//     cc::core::AssetId f_assetId,
//     pc::core::Framework* f_framework,
//     const TrajectoryParams_st& f_params);

// };

} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_ASSETS_H
