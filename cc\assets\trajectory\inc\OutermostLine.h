//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_OUTERMOSTLINE
#define CC_ASSETS_TRAJECTORY_SUBASSETS_OUTERMOSTLINE

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
class MainLogic;
struct ModelData_st;
struct Inputs_st;
} // namespace mainlogic

struct DI_st
{
    DI_st(float f_pos, float f_thickness)
        : Pos(f_pos)
        , Thickness(f_thickness)
    {
    }

    float Pos; // [m] The distance of the middle of the indicator from the reference point (bumper line), measured on
               // the arc.
    float Thickness; // [m] The longitudinal dimension of the distance indicators (in car coordinates).
};

struct DIDescriptor_st
{
    float              DILength; // [m] The lateral dimension of the distance indicators (in car coordinates).
    std::vector<DI_st> DIs;      // Individual DI parameters.
};

class OutermostLine : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:
    OutermostLine(
        pc::core::Framework*                               f_framework,
        cc::assets::trajectory::commontypes::Side_en       f_side,
        float                                              f_height,
        const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
        const DIDescriptor_st&                             f_DIDescriptor,
        unsigned int                                       f_numOfVerts_BeforeDIs,
        unsigned int                                       f_numOfVerts_BetweenDIs,
        unsigned int                                       f_numOfVerts_AfterDIs);

    virtual void generateVertexData();

    osg::Image* create1DTexture() const;

    void getOuterRadiusAndOffset(float& f_visibleOuterRadius, float& f_visibleOuterLateralOffset) const;

protected:
    virtual ~OutermostLine();

    void         loadTexture();
    void         generateVertexData_usingTexture();
    unsigned int getDynamicNumOfDIs(float f_bumperLineAngle, float f_endAngle);
    unsigned int getDynamicNumOfDIs_Straight(float f_bumperLinePos, float f_endPos);

    void addDistIndPoints_Tex(
        float                                                  f_distIndAngle,
        float                                                  f_distIndThickness,
        cc::assets::trajectory::frame::Frame&                  f_frame,
        cc::assets::trajectory::mainlogic::DrivingDirection_en f_drivingDir,
        float                                                  f_leftRightDirMul);
    void addDistIndPoints_Tex_Straight(
        float                                                  f_distIndPos,
        float                                                  f_distIndThickness,
        cc::assets::trajectory::frame::Frame&                  f_frame,
        cc::assets::trajectory::mainlogic::DrivingDirection_en f_drivingDir,
        float                                                  f_leftRightDirMul);

    const DIDescriptor_st& m_DIDescriptor;

    const unsigned int mc_numOfDIs;
    const unsigned int mc_numOfVerts_BeforeDIs, mc_numOfVerts_BetweenDIs, mc_numOfVerts_AfterDIs;

    std::vector<float> m_DIGeometryWidths; // Longitudinal dimension of the DI (related to the car) [m].
    float              m_DIGeometryLength; // Lateral dimension of the DI (related to the car) [m].
    float m_visibleOuterRadius; // The radius of the outer edge of the visible outermost line. To be output for the DL1
                                // asset.
    float m_visibleOuterLateralOffset; // The lateral position of the outer edge of the visible outermost line. To be
                                       // output for the DL1 asset.

private:
    //! Copy constructor is not permitted.
    OutermostLine(const OutermostLine& other); // = delete
    //! Copy assignment operator is not permitted.
    OutermostLine& operator=(const OutermostLine& other); // = delete
};

} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_OUTERMOSTLINE
