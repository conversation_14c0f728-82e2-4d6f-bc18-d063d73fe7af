//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomIcon.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_CUSTOMICON_H
#define CC_ASSETS_UIELEMENTS_CUSTOMICON_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/core/inc/CustomScene.h"
#include <osgDB/ReadFile>

namespace cc
{
namespace assets
{
namespace uielements
{

//!
//! CustomIcon
//!
class CustomIcon : public pc::assets::Icon
{
public:

  enum AnimationStyle : unsigned int
  {
    NONE_EFFECT = 0u,
    FLASHING_EFFECT = 1u,
    FADEIN_FADEOUT_EFFECT = 2u,
    AUGMENTED_WAVE_EFFECT = 3u,
    ALPHA_MASK = 4u
  };

  enum AnimationDir : unsigned int
  {
    START_FROM_TOP = 0u,
    START_FROM_BOTTOM = 1u
  };

  CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen);
  CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen,
             const AnimationStyle f_animationStyle, const AnimationDir f_animationDir);

  void setRotateAngle(const float& f_rotAngle);
  void setTexture(osg::Texture2D* f_texture);
  void setAnimation(const AnimationStyle f_animationStyle);

  AnimationStyle getAnimation();
  void updateShaderUniform();

  const osg::Vec2f getIconSize() const { return m_iconSize; }

protected:
  virtual ~CustomIcon() = default;
  //! Copy constructor is not permitted.
  CustomIcon (const CustomIcon& other); // = delete
  //! Copy assignment operator is not permitted.
  CustomIcon& operator=(const CustomIcon& other); // = delete

  void updateGeometry(
    osg::Geometry* f_geometry,
    const osg::Vec2f& f_origin,
    const osg::Vec2f& f_size,
    float f_left,
    float f_bottom,
    float f_right,
    float f_top) const override;

  osg::Geometry* createGeometry() const override;

private:
  float m_rotAngle;
  bool  m_isLeft;
  bool  m_isParkSpace;
  bool  m_isParkUI;
  bool  m_isHoriScreen;
  AnimationStyle m_animationStyle;
  AnimationDir m_animationDir;
  osg::Vec2f m_iconSize;
};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_CUSTOMICON_H
