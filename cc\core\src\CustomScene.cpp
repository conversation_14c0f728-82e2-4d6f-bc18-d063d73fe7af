//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomScene.cpp
/// @brief
//=============================================================================

#include "osgDB/ReadFile"
#include "osg/PositionAttitudeTransform"
#include "osg/Scissor"

#include "cc/core/inc/CustomScene.h"

#include <cassert>

#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/views/fusiview/inc/FuSiView.h"
#include "pc/svs/views/perfview/inc/ProfilingView.h"
#include "pc/svs/views/rawfisheyeview/inc/RawFisheyeView.h"
#include "pc/svs/views/warpfisheyeview/inc/FisheyeModels.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"
#include "pc/svs/worker/bowlshaping/inc/PolarBowlLayoutGenerator.h"
#include "pc/svs/worker/core/inc/TaskManager.h"

#include "cc/assets/augmentedview/inc/AugmentedViewTransition.h"
#include "cc/assets/augmentedview/inc/ParkViewRenderManager.h"
#include "cc/assets/common/inc/Bowl.h"
#include "cc/assets/common/inc/Floor.h"
#include "cc/assets/common/inc/FloorSingleCam.h"
#include "cc/assets/common/inc/SingleCam.h"
#include "cc/assets/common/inc/Vehicle.h"
#include "cc/assets/common/inc/Vehicle2D.h"
#include "cc/assets/fisheyeassets/inc/FisheyeTrajectories.h"
#include "cc/assets/impostor/inc/FixedImpostor.h"
#include "cc/assets/splineoverlay/inc/SplineOverlay.h"
#include "cc/assets/streetoverlay/inc/StreetOverlay.h"
#include "cc/assets/tileoverlay/inc/TileOverlay.h"
#include "cc/assets/trajectory/inc/ExpModeTrajectory.h"
#include "cc/assets/trajectory/inc/TrajectoryAssets.h"
// #include "cc/assets/caliboverlay/inc/CalibOverlay.h"
#include "cc/assets/tileoverlay/inc/TileCallback.h"
// #include "cc/assets/rctaoverlay/inc/RCTAOverlay.h"
#include "cc/assets/debugoverlay/inc/SwInfoOverlay.h"
#include "cc/assets/splineoverlay/inc/DistanceDigitalDisplay.h"
#include "cc/assets/stb/inc/SeeThroughBonnet.h"
#include "cc/assets/uielements/inc/DynamicDistance.h"
#include "cc/assets/uielements/inc/RemainingMoveNumber.h"
#include "cc/assets/uielements/inc/SpeedOverlay.h"
#include "cc/views/bonnetview/inc/BonnetView.h"
#include "cc/views/daynightview/inc/DayNightView.h"
#include "cc/views/nfsengineeringview/inc/NfsEngineeringView.h"
// #include "cc/views/nfsengineeringview/inc/CpcDebugScreen.h"
#include "cc/views/customrawfisheyeview/inc/CustomRawFisheyeView.h"
#include "cc/views/customwarpfisheyeview/inc/CustomWarpFisheyeView.h"
#include "cc/views/panoramaview/inc/PanoramaView.h"
#include "cc/views/parkview/inc/ParkView.h"
#include "cc/views/planview/inc/FixedImpostorPlanView.h"
#include "cc/views/surroundview/inc/SurroundView.h"
// #include "cc/views/touchfocus/inc/TouchFocusView.h"

#include "cc/virtcam/inc/CameraPositions.h"
#include "cc/virtcam/inc/HeadUnitHemisphereCameraUpdater.h"

#include "cc/assets/common/inc/CustomRenderManager.h"
#include "cc/assets/ptsoverlay/inc/PtsDistanceDigitalDisplay.h"
#include "cc/assets/ptsoverlay/inc/PtsOverlay.h"

#include "cc/daddy/inc/CustomDaddyPorts.h"

#include "cc/assets/overlaycallback/inc/OverlayCallback.h"

#include "cc/assets/uielements/inc/ParkingSearching.h"
#include "cc/assets/uielements/inc/ParkingSlot.h"
#include "cc/assets/uielements/inc/ParkingView.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"
#include "cc/assets/uielements/inc/Vehicle2dIcon.h"
#include "cc/assets/uielements/inc/VehicleTransIcon.h"
#include "cc/assets/uielements/inc/WarnSymbols.h"
#include "cc/assets/uielements/inc/ViewInfoOverlay.h"

#include "cc/assets/ECALprogressoverlay/inc/ECALprogressoverlay.h"
#include "cc/assets/dynamicgearoverlay/inc/dynamicgearoverlay.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingManager.h"
// #include "cc/assets/freeparking/inc/FreeparkingOverlay.h"
#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "cc/assets/parkingspace/inc/ParkingSpaceMark.h"
#include "cc/assets/parkingspots/inc/ParkingSpotManager.h"
#include "cc/assets/uielements/inc/ParkingConfirmInterface.h"
#include "cc/assets/uielements/inc/SettingBar.h"
#include "cc/assets/uielements/inc/TextSymbols.h"
#include "cc/assets/uielements/inc/WheelSeparator.h"
#include "cc/assets/vehicle2dwheels/inc/Vehicle2DWheels.h"
#include "cc/assets/virtualreality/inc/VirtualRealityManager.h"
// #include "cc/assets/button/inc/TestButton.h"
#include "cc/assets/button/inc/CustomButtons.h"
#include "cc/assets/button/inc/ViewChangeButton.h"
#include "cc/assets/button/inc/ViewModeButton.h"
#include "cc/mod/inc/ModFisheyeView.h"
#include "cc/assets/distanceoverlay/inc/DistanceOverlay.h"
#include "cc/assets/settingpageoverlay/inc/SettingPageEntryButton.h"
#include "cc/assets/settingpageoverlay/inc/BrightnessSliderEntryButton.h"
#include "cc/assets/settingpageoverlay/inc/BrightnessSliderOverlay.h"
#include "cc/oncal/inc/OncalOverlay.h"
#include "cc/assets/rimprotectionline/inc/RimProtectionLine.h"

#ifdef CHAMAELEON
#include "pc/svs/imp/chamaeleon/inc/chamaeleon.hpp"
#include "pc/svs/imp/cwd/inc/custom_window_dump.hpp"
#include "pc/svs/imp/iq/inc/image_quality_root.hpp"
#include "pc/svs/imp/sh/inc/sharpness_harmonization.hpp"
#include "pc/svs/imp/chamaeleon/inc/chamaeleon_tabs.hpp"
#include "pc/svs/imp/tnf/inc/temporal_noise_filter_root.hpp"
#endif

#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
#include "cc/imgui/inc/imgui_view.h"
#endif
#include "cc/mod/inc/ModOverlay.hpp"

#include "CustomSystemConf.h" //target+cc specific

using pc::util::logging::g_EngineContext;

#define USE_FISHEYE_VIEWS 1
#define USE_FISHEYE_VIEWS_SIDE 1
#define USE_RESIZE_2DVEHICLEMODEL_PLANVIEW 1
#define USE_PARKSPACE_MARK 1
#define USE_RADAR_WALL 1
#define USE_RADAR_WALL_SHOW 0
#define USE_RADAR_WALL_DISTANCE 0
#define USE_PARKING_VIEW 0
#define USE_IN_NISSAN 0  // used in nissan

namespace cc
{

namespace virtcam
{
pc::util::coding::Item<cc::virtcam::CarCenter> g_carCenter("CarCenter");
} // namespace virtcam

namespace core
{

pc::util::coding::Item<cc::views::planview::PlanViewSettings> g_planView("PlanView");
pc::util::coding::Item<CustomViews>                           g_views("Views");

static const cc::assets::trajectory::DL1*           g_pDL1;
static const cc::assets::trajectory::OutermostLine* g_leftOutermostLine;
static const cc::assets::trajectory::OutermostLine* g_rightOutermostLine;
static const cc::assets::trajectory::OutermostLine* g_leftOutermostLineFish;
static const cc::assets::trajectory::OutermostLine* g_rightOutermostLineFish;

constexpr float        g_height                 = 0.005f;
constexpr unsigned int g_numVerticesWheelTracks = 48u;
constexpr unsigned int g_numVerticesCoverPlate  = 48u;
constexpr unsigned int g_numLayoutPointsDL1     = 48u;
// const unsigned int g_numVerticesTrailerAssistLines = 48u;

//! Viewports are defined for LHD per default. This function checks if they need to be mirrored for RHD vehicles and
//! performs it.
pc::core::Viewport
getCorrectDriveHandSideViewport(const pc::core::Viewport& f_viewport, const pc::core::Viewport& f_usableCanvasViewport)
{
    const bool         lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
    pc::core::Viewport l_out(f_viewport);

    if (true == lhd)
    {
        return l_out;
    }

    // otherwise it is a rhd...
    l_out.m_origin.x() = f_usableCanvasViewport.m_size.x() - f_viewport.m_size.x() - f_viewport.m_origin.x();

    return l_out;
}

//! Viewports are defined for LHD per default. This function checks if they need to be mirrored for RHD vehicles and
//! performs it (just for DA views).
pc::core::Viewport getCorrectDriveHandSideViewportDA(const pc::core::Viewport& f_viewport, float f_origin)
{
    const bool         lhd = pc::vehicle::g_mechanicalData->m_leftHandDrive;
    pc::core::Viewport l_out(f_viewport);

    if (true == lhd)
    {
        return l_out;
    }

    // otherwise it is a rhd...

    l_out.m_origin.x() = static_cast<int>(f_origin);

    return l_out;
}

//!
//! CustomScene
//!
CustomScene::CustomScene(bool f_enableImgui)
    : pc::core::Scene(CustomViews::SURROUND_VIEW)
    , m_augmentedViewTransitionHori{}
    , m_augmentedViewTransitionVert{}
    , m_vehicleModelName{}
    , m_enableImgui(f_enableImgui)
{
}

const std::string CustomScene::getVehicleModelName() const
{
    return m_vehicleModelName;
}

void CustomScene::setVehicleModelName(const std::string& f_nodeName)
{
    m_vehicleModelName = f_nodeName;
}

#if USE_FISHEYE_VIEWS
static osg::Group* createFisheyeWheelTracks(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);
// static osg::Group* createFisheyeTrailerAssistLines( cc::core::CustomFramework* f_framework,
// pc::core::sysconf::Cameras f_cam, pc::views::warpfisheye::FisheyeModel* f_model,
// pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings  );
static osg::Group* createFisheyeOutermostLines(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);
static osg::Group* createFisheyeOutermostLinesColorful(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);
static osg::Group* createFisheyeCoverPlate(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);
static osg::Group* createFisheyeDL1(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);
static osg::Group* createFisheyeDL1Colorful(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);
static osg::Group* createFisheyeRctaOverlay(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings);

// Horizontal Fisheye Settings
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearSettings("RearFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_frontSettings("FrontFisheye");
// pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_rearModelSettings("RearFisheyeModel");
// pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_frontModelSettings("FrontFisheyeModel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_leftSettings("LeftFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rightSettings("RightFisheye");

static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishFront("PartialUnfishFront");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRear("PartialUnfishRear");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeft("PartialUnfishLeft");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRight("PartialUnfishRight");

// Horizontal Fisheye junction view settings
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_rearPanoSettings("RearPanoFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_frontPanoSettings("FrontPanoFisheye");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishFrontPano("PartialUnfishFrontPano");
static pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearPano("PartialUnfishRearPano");

// mod views param
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_modFrontSettings("ModFisheyeFront");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_modRightSettings("ModFisheyeRight");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_modRearSettings("ModFisheyeRear");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_modLeftSettings("ModFisheyeLeft");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_modFrontSettingsModel("ModFisheyeFrontModel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_modRightSettingsModel("ModFisheyeRightModel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_modRearSettingsModel("ModFisheyeRearModel");
static pc::util::coding::Item<pc::views::warpfisheye::FisheyeModelSettings> l_modLeftSettingsModel("ModFisheyeLeftModel");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings> l_modFrontSettingsCrop("ModFisheyeFrontCrop");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings> l_modRightSettingsCrop("ModFisheyeRightCrop");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings> l_modRearSettingsCrop("ModFisheyeRearCrop");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings> l_modLeftSettingsCrop("ModFisheyeLeftCrop");


#if ENABLE_VERTICAL_MODE
// Vertical Fisheye settings
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertRearSettings("VertRearFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertFrontSettings("VertFrontFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertLeftSettings("VertLeftFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>   l_vertRightSettings("VertRightFisheye");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishFrontVert("PartialUnfishFrontVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRearVert("PartialUnfishRearVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishLeftVert("PartialUnfishLeftVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings> l_partUnfishRightVert("PartialUnfishRightVert");

// Vertical Fisheye junction view settings
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertRearPanoSettings("VertRearPanoFisheye");
pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings> l_vertFrontPanoSettings("VertFrontPanoFisheye");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishFrontPanoVert("PartialUnfishFrontPanoVert");
pc::util::coding::Item<pc::views::warpfisheye::PartialUnfishSettings>
    l_partUnfishRearPanoVert("PartialUnfishRearPanoVert");

// Vertical fisheye view crop setting
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontCropBoundsVert("FisheyeCropSettingsFrontVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearCropBoundsVert("FisheyeCropSettingsRearVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontPanoCropBoundsVert("FisheyeCropSettingsFrontPanoVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearPanoCropBoundsVert("FisheyeCropSettingsRearPanoVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsVert("FisheyeCropSettingsLeftViewVert");
pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsVert("FisheyeCropSettingRightViewVert");
#endif

// Horizontal fisheye view crop setting
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontCropBoundsHori("FisheyeCropSettingsFrontView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearCropBoundsHori("FisheyeCropSettingsRearView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_frontPanoCropBoundsHori("FisheyeCropSettingsFrontPano");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rearPanoCropBoundsHori("FisheyeCropSettingsRearPano");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_leftCropBoundsHori("FisheyeCropSettingsLeftView");
static pc::util::coding::Item<cc::views::warpfisheye::CustomWarpFisheyeViewCropSettings>
    l_rightCropBoundsHori("FisheyeCropSettingRightView");

#endif

void CustomScene::init() // PRQA S 6044
{
    // initialize base class
    Scene::init();

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "Beginning of Custom Scene initialization");

    pc::factory::RenderManagerRegistry* const l_pRenderManagerRegistry =
        new pc::factory::RenderManagerRegistry(getFramework());
    addUpdateCallback(l_pRenderManagerRegistry);

    const auto l_pLayoutGenerator = createSuperEllipseLayoutGenerator();
#if USE_RADAR_WALL
    const osg::ref_ptr<cc::core::CustomZoneLayout> l_zoneLayout = new cc::core::CustomZoneLayout(true);
#endif

    //! Assets
    //! **********************************************************************************************************
    const auto l_pFrontWheels  = new pc::core::Asset(AssetId::EASSETS_FRONT_WHEELS);
    const auto l_pAllWheels    = new pc::core::Asset(AssetId::EASSETS_ALL_WHEELS);
    const auto l_pVehicleDoors = new assets::impostor::ImpostorDoors(AssetId::EASSETS_VEHICLE_DOORS, getFramework());
    const auto l_pVehicle      = new assets::common::Vehicle(
        AssetId::EASSETS_VEHICLE, getFramework(), l_pFrontWheels, l_pAllWheels, l_pVehicleDoors);
    const auto l_pBowl      = new assets::common::Bowl(AssetId::EASSETS_BOWL, getFramework(), l_pLayoutGenerator);
    // auto l_pVehicle2D = new assets::common::Vehicle2D(AssetId::EASSETS_TV2D_IMPOSTOR, getFramework());
    const auto l_pFloor =
        new assets::common::Floor(AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout(), true);
#ifndef USE_FISHEYE_VIEWS
    auto l_pFloorFront = new assets::common::Floor(
        AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout(), cc::assets::common::FLOOR_FRONT);
    auto l_pFloorRear = new assets::common::Floor(
        AssetId::EASSETS_FLOOR, getFramework(), l_pLayoutGenerator->getPolarLayout(), cc::assets::common::FLOOR_REAR);
    auto l_pSingleCamFront = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_FRONT_CAM,
        l_pBowl->getSV3DNode(),
        l_pFloorFront->getSV3DNode(),
        pc::factory::SINGLE_CAM_FRONT);
#endif
    const auto l_dynWheelMaskAsset = new cc::assets::DynWheelMaskAsset(AssetId::EASSETS_DYNAMIC_WHEEL_MASK, getFramework());
    l_pFloor->addDynamicWheelMaskDependency(l_dynWheelMaskAsset);

    const auto l_pSingleCamLeft = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_LEFT_CAM, l_pBowl->getSV3DNode(), l_pFloor->getSV3DNode(), pc::factory::SINGLE_CAM_LEFT);
    const auto l_pSingleCamRight = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_RIGHT_CAM,
        l_pBowl->getSV3DNode(),
        l_pFloor->getSV3DNode(),
        pc::factory::SINGLE_CAM_RIGHT);
#ifndef USE_FISHEYE_VIEWS
    auto l_pSingleCamRear = new assets::common::SingleCam(
        AssetId::EASSETS_BOWL_REAR_CAM,
        l_pBowl->getSV3DNode(),
        l_pFloorRear->getSV3DNode(),
        pc::factory::SINGLE_CAM_REAR);
#endif
    // auto l_pCalibOverlay    = new pc::core::Asset( AssetId::EASSETS_CALIB_OVERLAY, new
    // assets::caliboverlay::CalibOverlay(getFramework())); auto l_pBackground      = new pc::core::Asset(
    // AssetId::EASSETS_BACKGROUND, new assets::Background());
    // auto l_pRCTAOverlay     = new pc::core::Asset( AssetId::EASSETS_RCTA_OVERLAY, new
    // assets::rctaoverlay::RctaOverlay(getFramework()));
#if USE_RADAR_WALL_DISTANCE
    auto l_pDigitalDisplay = new pc::core::Asset(
        AssetId::EASSETS_DIGITAL_DISTANCE_DISPLAY,
        new assets::ptsdistancedigitaldisplay::DistanceDigitalDisplay(getFramework(), l_zoneLayout.get()));
#else
    const auto l_pDigitalDisplay = new pc::core::Asset(
        AssetId::EASSETS_DIGITAL_DISTANCE_DISPLAY,
        new assets::distancedigitaldisplay::DistanceDigitalDisplay(getFramework()));
#endif
#if USE_RADAR_WALL
#else
    auto l_pObstacleOverlay = new pc::core::Asset(
        AssetId::EASSETS_OBSTACLE_OVERLAY, new assets::tileoverlay::TileOverlay(getFramework(), false, false));
    auto l_pObstacleOverlay_vehOffset = new pc::core::Asset(
        AssetId::EASSETS_OBSTACLE_OVERLAY_VEH_OFFSET,
        new assets::tileoverlay::TileOverlay(getFramework(), true, false));
    auto l_pObstacleOverlay_Perspective = new pc::core::Asset(
        AssetId::EASSETS_OBSTACLE_OVERLAY_VEH_OFFSET,
        new assets::tileoverlay::TileOverlay(getFramework(), false, true));
#endif
    const auto l_pSwInfoOverlay =
        new pc::core::Asset(AssetId::EASSETS_SWINFO_OVERLAY, new assets::swinfooverlay::SwInfoOverlay(getFramework()));
#ifdef ENABLE_ONCAL
    const auto l_pOncalDebugOverlay =
        new pc::core::Asset(AssetId::EASSETS_SWINFO_OVERLAY, new cc::assets::OncalDebugOverlay::OncalDebugOverlay(getFramework()));
#endif

#if USE_RADAR_WALL
#else
    l_pObstacleOverlay->setCullCallback(new cc::assets::tileoverlay::TileCallback(getFramework(), false));
    l_pObstacleOverlay_vehOffset->setCullCallback(new cc::assets::tileoverlay::TileCallback(getFramework(), false));
    l_pObstacleOverlay_Perspective->setCullCallback(new cc::assets::tileoverlay::TileCallback(getFramework(), false));
#endif

    const auto l_pDistanceOverlay =
        new cc::assets::distanceoverlay::DistanceOverlayAsset(AssetId::EASSETS_DISTANCE_OVERLAY, getFramework());
    const auto l_pDistanceOverlayMini =
        new cc::assets::distanceoverlay::DistanceOverlayAsset(AssetId::EASSETS_DISTANCE_OVERLAY, getFramework());
    const auto l_pDistanceOverlayFloating =
        new cc::assets::distanceoverlay::DistanceOverlayAsset(AssetId::EASSETS_DISTANCE_OVERLAY, getFramework());
    const auto l_pDistanceOverlayFreeparking =
        new cc::assets::distanceoverlay::DistanceOverlayAsset(AssetId::EASSETS_DISTANCE_OVERLAY, getFramework());
    l_pDistanceOverlayMini->enableMiniVehicle2d(true);

    l_pDistanceOverlay->setTextSize(40.0f);
    l_pDistanceOverlayMini->setTextSize(30.0f);
    l_pDistanceOverlayFloating->setTextSize(30.0f);
    l_pDistanceOverlayFreeparking->setTextSize(30.0f);
    m_pBowlAsset  = l_pBowl;
    m_pFloorAsset = l_pFloor;

    const auto l_pFloorPlate = l_pFloor->getBasePlateAsset();

    //! Trajectories ***********************************************************************************************
    // const cc::assets::trajectory::TrajectoryCodingParams& l_trajCodingParams =
    // cc::assets::trajectory::g_trajCodingParams; assets::trajectory::TrajectoryParams_st l_trajParams;
    // assets::trajectory::DIDescriptor_st l_DIDescriptor;
    initTrajectoryParams(assets::trajectory::g_trajParams, assets::trajectory::g_DIDescriptor, false);
    initOutermostLineColofulParams(assets::trajectory::g_DIColorfulDescriptor);

    const auto l_pTopViewOverlayCullCallback = new assets::overlaycallback::TopViewOverlayCullCallback(
        getFramework(), assets::overlaycallback::VEHICLE_DRIVING_TUBE);

    const auto l_pOutermostLines = new assets::trajectory::OutermostLinesAsset(
        AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES,
        getFramework(),
        assets::trajectory::g_trajParams,
        assets::trajectory::g_DIDescriptor);
    l_pOutermostLines->addCullCallback(l_pTopViewOverlayCullCallback);

    g_leftOutermostLine  = l_pOutermostLines->getLeft();
    g_rightOutermostLine = l_pOutermostLines->getRight();

    const auto l_pOutermostLinesColorful = new
    assets::trajectory::OutermostLinesAssetColorful(AssetId::EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL,
    getFramework(), assets::trajectory::g_trajParams, assets::trajectory::g_DIColorfulDescriptor);
    l_pOutermostLinesColorful->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_pDL1 = new assets::trajectory::DistanceLineAsset(
        AssetId::EASSETS_TRAJECTORY_DL1,
        getFramework(),
        assets::trajectory::g_trajParams,
        l_pOutermostLines->getLeft(),
        l_pOutermostLines->getRight(),
        g_numLayoutPointsDL1);
    l_pDL1->addCullCallback(l_pTopViewOverlayCullCallback);
    g_pDL1 = l_pDL1->getDistanceLine();

    const auto l_pWheelTracks = new assets::trajectory::WheelTracksAsset(
        AssetId::EASSETS_TRAJECTORY_WHEEL_TRACKS,
        getFramework(),
        assets::trajectory::g_trajParams,
        l_pDL1->getDistanceLine());
    l_pWheelTracks->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_actionPoints = new assets::trajectory::ActionPointsAsset(
        AssetId::EASSETS_TRAJECTORY_ACTION_POINTS,
        getFramework(),
        assets::trajectory::g_trajParams,
        l_pDL1->getDistanceLine());
    l_actionPoints->addUpdateCallback(l_pTopViewOverlayCullCallback);
    l_actionPoints->addCullCallback(l_pTopViewOverlayCullCallback);

    const auto l_pCoverPlate = new assets::trajectory::CoverPlateAsset(
        AssetId::EASSETS_TRAJECTORY_COVERPLATE,
        getFramework(),
        assets::trajectory::g_trajParams,
        g_numVerticesCoverPlate);
    l_pCoverPlate->addCullCallback(l_pTopViewOverlayCullCallback);

    // assets::trajectory::RefLineAsset* l_pRefLine = nullptr;
    // if (l_trajCodingParams.m_refLineVisible)
    // {
    //   l_pRefLine = new assets::trajectory::RefLineAsset(AssetId::EASSETS_TRAJECTORY_REFLINE, getFramework(),
    //   l_trajParams);
    // }

    //! Plan view parameters
    //! **********************************************************************************************
    const float l_total_width_meters  = g_planView->m_widthMeters;
    const float l_total_length_meters = static_cast<float>(g_views->m_planViewport.m_size.y()) *
                                        g_planView->m_widthMeters /
                                        static_cast<float>(g_views->m_planViewport.m_size.x());

    const float l_total_width_meters_vehicle2d  = g_planView->m_widthMeters - g_planView->m_widthMetersVeh2dDiff;
    const float l_total_length_meters_vehicle2d = static_cast<float>(g_views->m_planViewport.m_size.y()) *
                                                  l_total_width_meters_vehicle2d /
                                                  static_cast<float>(g_views->m_planViewport.m_size.x());

    const float l_total_width_meters_parking_hori  = g_planView->m_widthMetersParkingHori;
    const float l_total_length_meters_parking_hori = static_cast<float>(g_views->m_floatFreeParkingViewport.m_size.y()) *
                                                     g_planView->m_widthMetersParkingHori /
                                                     static_cast<float>(g_views->m_floatFreeParkingViewport.m_size.x());
    const float l_total_width_meters_parking_hori_vehicle2d  = g_planView->m_widthMetersParkingHori - g_planView->m_widthMetersVeh2dDiff;
    const float l_total_length_meters_parking_hori_vehicle2d = static_cast<float>(g_views->m_floatFreeParkingViewport.m_size.y()) *
                                                  l_total_width_meters_parking_hori_vehicle2d /
                                                  static_cast<float>(g_views->m_floatFreeParkingViewport.m_size.x());
#if ENABLE_VERTICAL_MODE
    const float l_total_width_meters_parking_vert  = g_planView->m_widthMetersParkingVert;
    const float l_total_length_meters_parking_vert = static_cast<float>(g_views->m_vertMainViewport.m_size.y()) *
                                                     g_planView->m_widthMetersParkingVert /
                                                     static_cast<float>(g_views->m_vertMainViewport.m_size.x());
#endif
    // osg::Vec2f l_viewSize(l_total_width_meters, l_total_length_meters);
    // osg::Vec3f l_cameraPosition(virtcam::g_positions->getPosition(virtcam::VCAM_PLAN_VIEW).m_eye);

    // ! elements in searching perspective view
//    const auto l_pParkingSearchingElements = new pc::core::Asset(
        // AssetId::EASSETS_UI_PARKING_SEARCHING,
        // new assets::uielements::ParkingSearching(
        //     getFramework()->asCustomFramework(), AssetId::EASSETS_UI_PARKING_SEARCHING));
    // // ! quit button
    // auto l_pParkTypeConfirm = new pc::core::Asset( AssetId::EASSETS_PARKINGTYPECONFIRM,
    //                       new assets::parkingtypeconfirm::ParkingTypeConfirm(getFramework()->asCustomFramework(),
    //                       EASSETS_PARKINGTYPECONFIRM));

    // ! park confirm interface
    // auto l_pParkConfirmInterface = new pc::core::Asset( AssetId::EASSETS_PARKINGCONFIRMINTERFACE,
    //                             new
    //                             assets::parkconfirminterface::ParkingConfirmInterface(getFramework()->asCustomFramework(),
    //                             AssetId::EASSETS_PARKINGCONFIRMINTERFACE));

    //! Wheel separator
    const auto l_pWheelSeparator_Horizontal = new pc::core::Asset(
        AssetId::EASSETS_UI_WHEELSEPARATOR_HORIZONTAL,
        new assets::uielements::wheelseparatorhorizontal::WheelSeparatorHorizontal(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WHEELSEPARATOR_HORIZONTAL));
#if ENABLE_VERTICAL_MODE
    auto l_pWheelSeparator_Vertical = new pc::core::Asset(
        AssetId::EASSETS_UI_WHEELSEPARATOR_VERTICAL,
        new assets::uielements::wheelseparatorvertical::WheelSeparatorVertical(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WHEELSEPARATOR_VERTICAL));
#endif
    //! text symbol
//    const auto l_pTextSymbol = new pc::core::Asset(
        // AssetId::EASSETS_UI_WARNSYMBOL_TEXT,
        // new assets::uielements::TextSymbols(getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WARNSYMBOL_TEXT));

    //! parking plan view icon symbol
//    const auto l_pParkingPlanSymbol = new pc::core::Asset(
        // AssetId::EASSETS_UI_PARKINGPLANICON,
        // new assets::parkingspace::ParkingSpaceMarkSymbols(
        //     getFramework()->asCustomFramework(), AssetId::EASSETS_UI_PARKINGPLANICON));

#if USE_RADAR_WALL
    //! PTS
    // PTS overlay assets
    const auto l_ptsOverlay      = new assets::ptsoverlay::PtsOverlay(getFramework(), l_zoneLayout.get());
//    const auto l_obstacleOverlay = new pc::core::Asset(AssetId::EASSETS_OBSTACLE_OVERLAY, l_ptsOverlay);
    const auto l_ptsTransform    = new assets::ptsoverlay::PtsMatrixTransform(getFramework());
    l_ptsTransform->addChild(l_ptsOverlay); // PRQA S 3803

//    const auto l_obstacleOverlayParking = new pc::core::Asset(AssetId::EASSETS_OBSTACLE_OVERLAY, l_ptsTransform);
    // auto l_miniPts = new cc::assets::ptsoverlay::MiniPts(AssetId::MiniTopView,
    // static_cast<pc::vehiclemodel::VehicleModel*> (l_vehicle->getAsset()), getFramework());
#endif

    //! Views
    //! ***********************************************************************************************************

    // rear view
    // ********************************************************************************************************
    osg::Matrixf l_mirrorTheWorld;
    l_mirrorTheWorld(0, 0) = -1.f;
#if USE_FISHEYE_VIEWS
    pc::views::warpfisheye::PartialUnfishModel* const l_pRearModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRear.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRearView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Rear View",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::REAR_CAMERA,
        l_pRearModel,
        l_rearSettings.get(),
        l_rearCropBoundsHori.get(),
        rbp::vis::imp::sh::ESharpnessView::FIXED,
        rbp::vis::imp::tnf::ETnfView::DEFAULT);

    // mirror the view
    l_pRearView->setProjectionMatrix(l_pRearView->getProjectionMatrix() * l_mirrorTheWorld);

    pc::core::Asset* const l_pRearWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));

    // l_pRearWheelTracks->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));
    l_pRearView->addAsset(l_pRearWheelTracks);

    pc::core::Asset* const l_pRearOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        createFisheyeOutermostLines(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));

    pc::core::Asset* const l_pRearOutermostLinesWheeltrack = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));
    // l_pRearOutermostLinesWheeltrack->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));
    l_pRearView->addAsset(l_pRearOutermostLines);
    l_pRearView->addAsset(l_pRearOutermostLinesWheeltrack);

    pc::core::Asset* const l_pRearCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));

    l_pRearView->addAsset(l_pRearCoverPlate);

    pc::core::Asset* const l_pRearDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));

    l_pRearView->addAsset(l_pRearDL1);

    pc::core::Asset* const l_pRearRctaOverlay = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_RCTAOVERLAY,
        createFisheyeRctaOverlay(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_rearSettings));
    l_pRearView->addAsset(l_pRearRctaOverlay);

    // pc::core::Asset* l_pRearTrailerAssistLine = new pc::core::Asset( AssetId::EASSETS_TRAJECTORY_TRAILER_TRAJECTORY,
    //                                                             createFisheyeTrailerAssistLines(
    //                                                             getFramework()->asCustomFramework(),
    //                                                                                       pc::core::sysconf::REAR_CAMERA,
    //                                                                                       l_pRearModel,
    //                                                                                       l_rearSettings ) );

    // l_pRearWheelTracks->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));
    // l_pRearView->addAsset(l_pRearTrailerAssistLine);

    // l_pRearView->addAsset(l_pParkingIcon);

#if ENABLE_VERTICAL_MODE
    // Vertical mode Rear
    // ------------------------------------------------------------------------------------------------- Rotate 90
    // degrees----------------------------------------------------------------------------------------------------
    pc::views::warpfisheye::PartialUnfishModel* l_pRearModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearVert);

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertRearView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Vert Rear View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::REAR_CAMERA,
        l_pRearModelVert,
        l_vertRearSettings.get(),
        l_rearCropBoundsVert.get());

    // mirror the view
    l_pVertRearView->setProjectionMatrix(l_pVertRearView->getProjectionMatrix() * l_mirrorTheWorld);

    pc::core::Asset* l_pVertRearWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));
    l_pVertRearView->addAsset(l_pVertRearWheelTracks);

    pc::core::Asset* l_pVertRearOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    l_pVertRearView->addAsset(l_pVertRearOutermostLines);

    pc::core::Asset* l_pVertRearCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    l_pVertRearView->addAsset(l_pVertRearCoverPlate);

    pc::core::Asset* l_pVertRearDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::REAR_CAMERA, l_pRearModel, l_vertRearSettings));

    l_pVertRearView->addAsset(l_pVertRearDL1);
#endif
#else
    auto l_pRearView = new pc::core::View(
        "Rear View",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rear);
    l_pRearView->addAsset(l_pWheelTracks);
    // l_pRearView->addAsset(l_pSplineOverlay);
    l_pRearView->addAsset(l_pOutermostLines);
    l_pRearView->addAsset(l_pSingleCamRear);
    l_pRearView->addAsset(l_pCoverPlate);
    // l_pRearView->addAsset(l_pRCTAOverlay);
    l_pRearView->addAsset(l_pDL1);
    // l_pRearView->addUpdateCallback(new pc::factory::RenderManager(getFramework(), virtcam::VCAM_REAR_VIEW));

    // mirror the view
    l_pRearView->setProjectionMatrix(l_pRearView->getProjectionMatrix() * l_mirrorTheWorld);

    // l_pRearView->addAsset(l_pParkingIcon);
#endif

    // front view
    // *******************************************************************************************************
#if USE_FISHEYE_VIEWS
    pc::views::warpfisheye::PartialUnfishModel* const l_pFrontModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFront.data());

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFrontView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Front View",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::FRONT_CAMERA,
        l_pFrontModel,
        l_frontSettings.get(),
        l_frontCropBoundsHori.get(),
        rbp::vis::imp::sh::ESharpnessView::FIXED,
        rbp::vis::imp::tnf::ETnfView::DEFAULT);

    pc::core::Asset* const l_pFrontWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    // l_pFrontWheelTracks->setCullCallback(new cc::assets::overlaycallback::OverlayCallback(getFramework(),
    // cc::assets::overlaycallback::VEHICLE_WHEEL_TRACK));
    l_pFrontView->addAsset(l_pFrontWheelTracks);

    pc::core::Asset* const l_pFrontOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        createFisheyeOutermostLines(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    pc::core::Asset* const l_pFrontOutermostLinesWheelTrack = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    l_pFrontView->addAsset(l_pFrontOutermostLinesWheelTrack);
    l_pFrontView->addAsset(l_pFrontOutermostLines);

    pc::core::Asset* const l_pFrontCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    l_pFrontView->addAsset(l_pFrontCoverPlate);

    pc::core::Asset* const l_pFrontDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));

    l_pFrontView->addAsset(l_pFrontDL1);

    pc::core::Asset* const l_pFrontRctaOverlay = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_RCTAOVERLAY,
        createFisheyeRctaOverlay(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_frontSettings));
    l_pFrontView->addAsset(l_pFrontRctaOverlay);

    // l_pFrontView->addAsset(l_pParkingIcon);

#if ENABLE_VERTICAL_MODE
    // Vertical mode Front
    // ------------------------------------------------------------------------------------------------- Rotate 90
    // degrees----------------------------------------------------------------------------------------------------
    pc::views::warpfisheye::PartialUnfishModel* l_pFrontModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontVert);

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertFrontView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        // pc::views::warpfisheye::WarpFisheyeView* l_pVertFrontView = new pc::views::warpfisheye::WarpFisheyeView(
        "Vert Front View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::FRONT_CAMERA,
        l_pFrontModelVert,
        l_vertFrontSettings.get(),
        l_frontCropBoundsVert.get());

    pc::core::Asset* l_pVertFrontWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        createFisheyeWheelTracks(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));
    l_pVertFrontView->addAsset(l_pVertFrontWheelTracks);

    pc::core::Asset* l_pVertFrontOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        createFisheyeOutermostLines(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    l_pVertFrontView->addAsset(l_pVertFrontOutermostLines);

    pc::core::Asset* l_pVertFrontCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        createFisheyeCoverPlate(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    l_pVertFrontView->addAsset(l_pVertFrontCoverPlate);

    pc::core::Asset* l_pVertFrontDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1,
        createFisheyeDL1(
            getFramework()->asCustomFramework(), pc::core::sysconf::FRONT_CAMERA, l_pFrontModel, l_vertFrontSettings));

    l_pVertFrontView->addAsset(l_pVertFrontDL1);
#endif
#else
    auto l_pFrontView = new pc::core::View(
        "Front View",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_front);
    l_pFrontView->addAsset(l_pOutermostLines);
    l_pFrontView->addAsset(l_pWheelTracks);
    // l_pFrontView->addAsset(l_pSplineOverlay);
    l_pFrontView->addAsset(l_pSingleCamFront);
    l_pFrontView->addAsset(l_pCoverPlate);
    // l_pFrontView->addAsset(l_pRCTAOverlay);
    l_pFrontView->addAsset(l_pDL1);
    // l_pFrontView->addUpdateCallback(new pc::factory::RenderManager(getFramework(), virtcam::VCAM_FRONT_VIEW));

    // l_pFrontView->addAsset(l_pParkingIcon);
#endif

    // //plan (top) view
    // **************************************************************************************************
    // //Realize the Plan View as an ortho camera

    // //! Vehicle impostor - needs the total dimensions
    // assets::impostor::FixedImpostor* l_pTV2D_CarImpostor = new assets::impostor::FixedImpostor(
    //     AssetId::EASSETS_TV2D_IMPOSTOR, l_pVehicle,
    //     static_cast<pc::vehiclemodel::VehicleModel*> (l_pVehicle->getAsset()),
    //     assets:: impostor::g_settings->m_textureWidth, assets:: impostor::g_settings->m_textureHeight,
    //     l_total_width_meters, l_total_length_meters,  // plan view dimensions
    //     0.5f * l_total_width_meters, 0.7f* l_total_length_meters, // vehicle dimensions or area covered by RTT
    //     virtcam::g_positions->getPosition(virtcam::VCAM_PLAN_VIEW),
    //     getFramework()
    // );

    // //! Impostor Brightness
    // ********************************************************************************************* osg::Uniform
    // *l_impostorBrightnessUniform =
    // l_pTV2D_CarImpostor->getOrCreateStateSet()->getOrCreateUniform("impostorBrightness", osg::Uniform::FLOAT);
    // l_impostorBrightnessUniform->set( 1.0f );  // PRQA S 3803
    // l_impostorBrightnessUniform->setUpdateCallback( new BrightnessUpdateCallback(getFramework()) );

    // //! Vertical mode Vehicle impostor - needs the total dimensions
    //   assets::impostor::FixedImpostor* l_pTV2D_CarImpostorVert = new assets::impostor::FixedImpostor(
    //     AssetId::EASSETS_TV2D_IMPOSTOR, l_pVehicle,
    //     static_cast<pc::vehiclemodel::VehicleModel*> (l_pVehicle->getAsset()),
    //     assets:: impostor::g_settings->m_textureHeight, assets:: impostor::g_settings->m_textureWidth,
    //     l_total_length_meters, l_total_width_meters,  // plan view dimensions
    //     0.7f* l_total_length_meters, 0.5f * l_total_width_meters, // vehicle dimensions or area covered by RTT
    //     virtcam::g_positions->getPosition(virtcam::VCAM_VERT_PLAN_VIEW),
    //     getFramework()
    // );

    // //! Vertical mode Impostor Brightness
    // ********************************************************************************************* osg::Uniform
    // *l_impostorBrightnessUniformVert =
    // l_pTV2D_CarImpostorVert->getOrCreateStateSet()->getOrCreateUniform("impostorBrightness", osg::Uniform::FLOAT);
    // l_impostorBrightnessUniformVert->set( 1.0f );  // PRQA S 3803
    // l_impostorBrightnessUniformVert->setUpdateCallback( new BrightnessUpdateCallback(getFramework()) );

    //   auto l_pTestButton = new cc::assets::button::TestButton( cc::core::AssetId::EASSETS_TEST_BUTTON,
    //   getFramework());

    const auto l_pPlanView = new cc::views::planview::PlanView(
        "Plan View",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    const auto l_renderManagerPlanView = new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_PLAN_VIEW,
                                                                    rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT,
                                                                    rbp::vis::imp::sh::ESharpnessView::FIXED,
                                                                    rbp::vis::imp::tnf::ETnfView::DEFAULT);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerPlanView);
    l_renderManagerPlanView->assignCamera(l_pPlanView);

    // l_pPlanView->addAsset(l_pOutermostLines);
    // l_pPlanView->addAsset(l_pOutermostLinesColorful);
    l_pPlanView->addAsset(l_pFloor);
    l_pPlanView->addAsset(l_pFloorPlate);
    l_pPlanView->addAsset(l_pDistanceOverlay);
    l_pDistanceOverlay->setReferenceView(l_pPlanView);
#ifdef ENABLE_ONCAL
    l_pPlanView->addAsset(l_pOncalDebugOverlay);
#endif
    //   l_pPlanView->addAsset(l_pTestButton);
    //   l_pTestButton->setHoriReferenceView(l_pPlanView);
    //   l_pTestButton->setVertReferenceView(l_pPlanView);

    // l_pPlanView->addAsset(l_pVehicle); // replace with impostor
    // l_pPlanView->setImpostor(l_pTV2D_CarImpostor);
    // l_pPlanView->setWheels(l_pAllWheels);

    // l_pPlanView->addAsset(l_pSplineOverlay);
    // l_pPlanView->addAsset(l_pSplineOverlayShadow);
    // l_pPlanView->addAsset(l_pObstacleOverlay_vehOffset);
    //  l_pPlanView->addAsset(l_pCalibOverlay);
    //  l_pPlanView->addAsset(l_pWheelTracks);
    // l_pPlanView->addAsset(l_pRCTAOverlay);
    // l_pPlanView->addAsset(l_pDigitalDisplay);
    // l_pPlanView->addAsset(l_pSwInfoOverlay);

    const auto l_pPlanViewUSSOverlay = new cc::views::planview::PlanView(
        "Plan View USS Overlay",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewUSSOverlay->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    const auto l_renderManagerPlanViewUSSOverlay =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_PLAN_VIEW);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerPlanViewUSSOverlay);
    l_renderManagerPlanViewUSSOverlay->assignCamera(l_pPlanViewUSSOverlay);

#if USE_RADAR_WALL
#else
    l_pPlanViewUSSOverlay->addAsset(l_pObstacleOverlay_vehOffset);
#endif
    l_pPlanViewUSSOverlay->addAsset(l_pDigitalDisplay);
    l_pPlanViewUSSOverlay->addAsset(l_pSwInfoOverlay);
    l_pPlanViewUSSOverlay->setRenderOrder(osg::Camera::POST_RENDER, 10);

    // auto l_pParkSpacePlanViewMat = new cc::views::planview::PlanViewCullCallback();
    // l_pPlanView->addCullCallback(l_pParkSpacePlanViewMat);

    //! parking space on plan view
    // auto l_pParkingspacePlanView  = new pc::core::Asset( AssetId::EASSETS_TV2D_PARKING_SPACE,
    //                                 new assets::parkingspace::ParkingSpace(getFramework()->asCustomFramework(),
    //                                 AssetId::EASSETS_TV2D_PARKING_SPACE, l_pPlanView, l_pParkSpacePlanViewMat));
    // l_pPlanView->addAsset(l_pParkingspacePlanView);

    // auto l_pCameraIconPlanViewMat = new cc::views::planview::PlanViewCullCallback();
    // l_pPlanView->addCullCallback(l_pCameraIconPlanViewMat);

    // auto l_pCameraIcon = new pc::core::Asset( AssetId::EASSETS_UI_CAMERA_ICON,
    //                       new assets::uielements::CameraIcon(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_UI_CAMERA_ICON, l_pPlanView));
    // l_pPlanView->addAsset(l_pCameraIcon);

    const auto l_pWarnSymbolUss = new pc::core::Asset(
        AssetId::EASSETS_UI_WARNSYMBOL_USS,
        new assets::uielements::WarnSymbols(getFramework()->asCustomFramework(), AssetId::EASSETS_UI_WARNSYMBOL_USS));
    // l_pPlanView->addAsset(l_pWarnSymbolUss);

    const auto l_pVehicleTransIcon = new pc::core::Asset(
        AssetId::EASSETS_UI_VEHICLE_TRANS_ICON,
        new assets::uielements::VehicleTransIcon(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_VEHICLE_TRANS_ICON));

    const auto l_pVehicle2D = new pc::core::Asset( AssetId::EASSETS_VEHICLE_2D_OVERLAY,
                          new assets::uielements::Vehicle2DOverlay(getFramework()->asCustomFramework(),
                          AssetId::EASSETS_VEHICLE_2D_OVERLAY));

    // auto l_pVehicle2DIcon = new pc::core::Asset(
    //     AssetId::EASSETS_VEHICLE_2D_ICON,
    //     new assets::uielements::Vehicle2dIcon(getFramework()->asCustomFramework(), AssetId::EASSETS_VEHICLE_2D_ICON));

    const auto l_pVehicle2DWheels = new pc::core::Asset(
        AssetId::EASSETS_VEHICLE_2D_WHEELS, new assets::vehicle2dwheels::Vehicle2DWheels(getFramework()));
    // resize the vehicle 2d for plan view
    const auto l_pPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());

    l_pPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d / 2.,
        l_total_width_meters_vehicle2d / 2.,
        -l_total_length_meters_vehicle2d / 2.,
        l_total_length_meters_vehicle2d / 2.);

#if USE_RESIZE_2DVEHICLEMODEL_PLANVIEW

    // l_pPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pPlanViewVehicle2D->addAsset(l_pVehicle2D);
    l_pPlanViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pPlanViewVehicle2D->addAsset(l_pWheelTracks);
    l_pPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
    l_pPlanViewVehicle2D->addAsset(l_pVehicleTransIcon);
    // l_pPlanViewVehicle2D->setWheels(l_pAllWheels);
    l_pPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);
#if USE_RADAR_WALL_SHOW
    l_pPlanViewVehicle2D->addAsset(l_obstacleOverlay);
#endif

#else
    // l_pPlanView->addAsset(l_pVehicle2DIcon);
    l_pPlanView->addAsset(l_pVehicle2D);
    l_pPlanView->addAsset(l_pOutermostLines);
    l_pPlanView->addAsset(l_pWheelTracks);
    l_pPlanView->addAsset(l_pWarnSymbolUss);
    l_pPlanView->addAsset(l_pVehicleTransIcon);

#endif

#if ENABLE_VERTICAL_MODE
    //! Vertical Plan view
    auto l_pVertPlanView = new cc::views::planview::PlanView(
        "Vert Plan View",
        getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters / 2., l_total_length_meters / 2., -l_total_width_meters / 2., l_total_width_meters / 2.);

    auto l_renderManagerVertPlanView =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_PLAN_VIEW);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerVertPlanView);
    l_renderManagerVertPlanView->assignCamera(l_pVertPlanView);

    // l_pVertPlanView->addAsset(l_pOutermostLines);
    l_pVertPlanView->addAsset(l_pFloor);
    // l_pVertPlanView->addAsset(l_pVehicle); // replace with impostor
    // l_pVertPlanView->setImpostor(l_pTV2D_CarImpostorVert);
    // l_pVertPlanView->setWheels(l_pAllWheels);
    // l_pVertPlanView->addAsset(l_pVehicle2D);

    // l_pVertPlanView->addAsset(l_pSplineOverlay);
    // l_pVertPlanView->addAsset(l_pSplineOverlayShadow);
#if USE_RADAR_WALL
#else
    l_pVertPlanView->addAsset(l_pObstacleOverlay);
#endif
    // l_pVertPlanView->addAsset(l_pCalibOverlay);
    // l_pVertPlanView->addAsset(l_pWheelTracks);
    l_pVertPlanView->addAsset(l_pDigitalDisplay);
    // l_pVertPlanView->addAsset(l_pRCTAOverlay);

    // l_pVertPlanView->addAsset(l_pWarnSymbolUss);

    // resize the vehicle 2d for plan view
    auto l_pVertPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Vert Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertPlan,
        getFramework());

    l_pVertPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_length_meters_vehicle2d / 2.,
        l_total_length_meters_vehicle2d / 2.,
        -l_total_width_meters_vehicle2d / 2.,
        l_total_width_meters_vehicle2d / 2.);

#if USE_RESIZE_2DVEHICLEMODEL_PLANVIEW

    // l_pVertPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pVertPlanViewVehicle2D->addAsset(l_pVehicle2D);
    l_pVertPlanViewVehicle2D->addAsset(l_pOutermostLines);
    l_pVertPlanViewVehicle2D->addAsset(l_pWheelTracks);
    l_pVertPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
    l_pVertPlanViewVehicle2D->addAsset(l_pVehicleTransIcon);

#else
    // l_pVertPlanView->addAsset(l_pVehicle2DIcon);
    l_pVertPlanView->addAsset(l_pVehicle2D);
    l_pVertPlanView->addAsset(l_pOutermostLines);
    l_pVertPlanView->addAsset(l_pWheelTracks);
    l_pVertPlanView->addAsset(l_pWarnSymbolUss);
    l_pVertPlanView->addAsset(l_pVehicleTransIcon);

#endif

#endif
    // horizontal parking plan view
#if ENABLE_VERTICAL_MODE
    // vertical parking plan view
    auto l_pVertParkingPlanView = new cc::views::planview::PlanView(
        "Vert Parking Plan View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertParkingPlan,
        getFramework());

    l_pVertParkingPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_vert / 2.,
        l_total_width_meters_parking_vert / 2.,
        -l_total_length_meters_parking_vert / 2.,
        l_total_length_meters_parking_vert / 2.);

    auto l_renderManagerVertParkingPlanView =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_VERT_PARKING_PLAN_VIEW);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerVertParkingPlanView);
    l_renderManagerVertParkingPlanView->assignCamera(l_pVertParkingPlanView);

    l_pVertParkingPlanView->addAsset(l_pOutermostLines);
    // l_pVertParkingPlanView->addAsset(l_pFloor);
    // l_pVertParkingPlanView->addAsset(l_pVehicle2DIcon);
    l_pVertParkingPlanView->addAsset(l_pVehicle2D);

#if USE_RADAR_WALL
#else
    l_pVertParkingPlanView->addAsset(l_pObstacleOverlay);
#endif
    l_pVertParkingPlanView->addAsset(l_pWheelTracks);
    l_pVertParkingPlanView->addAsset(l_pDigitalDisplay);
    l_pVertParkingPlanView->addAsset(l_freeparkingManager);

    l_pVertParkingPlanView->addAsset(l_pTextSymbol);

#if USE_PARKSPACE_MARK
    l_pVertParkingPlanView->addAsset(l_pParkingPlanSymbol);
#endif

    // ! parking space on vertical plan view
    auto l_pParkSpacePlanViewMat_Vert = new cc::views::planview::PlanViewCullCallback();
    auto l_pParkingspacePlanView_Vert = new pc::core::Asset(
        AssetId::EASSETS_TV2D_PARKING_SPACE,
        new assets::parkingspace::ParkingSpace(
            getFramework()->asCustomFramework(),
            AssetId::EASSETS_TV2D_PARKING_SPACE,
            l_pVertParkingPlanView,
            g_views->m_vertMainViewport,
            false,
            l_pParkSpacePlanViewMat_Vert));

    l_pVertParkingPlanView->addCullCallback(l_pParkSpacePlanViewMat_Vert);
    l_pVertParkingPlanView->addAsset(l_pParkingspacePlanView_Vert);
    l_pVertParkingPlanView->setRenderOrder(osg::Camera::POST_RENDER, 100);

    // vertical parking floor plan view
    auto l_pVertParkingFloorPlanView = new cc::views::planview::PlanView(
        "Vert Parking Floor Plan View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertParkingPlan,
        getFramework());

    l_pVertParkingFloorPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_vert / 2.,
        l_total_width_meters_parking_vert / 2.,
        -l_total_length_meters_parking_vert / 2.,
        l_total_length_meters_parking_vert / 2.);

    auto l_renderManagerVertParkingFloorPlanView =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_VERT_PARKING_PLAN_VIEW);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerVertParkingFloorPlanView);
    l_renderManagerVertParkingFloorPlanView->assignCamera(l_pVertParkingFloorPlanView);

    l_pVertParkingFloorPlanView->addAsset(l_pFloor);
    l_pVertParkingFloorPlanView->setRenderOrder(osg::Camera::POST_RENDER, 10);
    ;
#endif

#if USE_IN_NISSAN
    // create a fake plan view to display the vehicle impostor on LSMG screen
    pc::core::Viewport m_impostorViewport = g_views->m_planViewport;
    if (true == pc::vehicle::g_mechanicalData->m_leftHandDrive)
    {
        m_impostorViewport.m_origin.x() = g_views->m_planViewport.m_origin.x() + g_views->m_planViewport.m_size.x();
    }

    auto l_pImpostorPlanView = new cc::views::planview::PlanView(
        "Impostor Plan View", m_impostorViewport, virtcam::g_positions->m_plan, getFramework());

    l_pImpostorPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    auto l_pRefPlanViewMat = new cc::views::planview::PlanViewCullCallback();
    l_pImpostorPlanView->addCullCallback(l_pRefPlanViewMat);

    // l_pImpostorPlanView->setImpostor(l_pTV2D_CarImpostor);
    //   l_pImpostorPlanView->setWheels(l_pAllWheels);
    l_pImpostorPlanView->addAsset(l_pVehicle2D);
    // l_pImpostorPlanView->addAsset(l_pVehicle2DIcon);

    // {
    //   // render the vehicle doors on top of the impostor
    //   auto l_pDoorView = new pc::core::View(
    //       "Doors View",
    //       getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
    //       virtcam::g_positions->m_plan);
    //   l_pDoorView->setProjectionMatrixAsOrtho2D(
    //       - l_total_width_meters /2., l_total_width_meters /2.,
    //       - l_total_length_meters /2. , l_total_length_meters /2.);
    //   l_pDoorView->addAsset(l_pVehicleDoors);
    //   l_pDoorView->setRenderOrder(osg::Camera::POST_RENDER);

    //   l_pVehicleDoors->getOrCreateStateSet()->setRenderBinDetails(cc::core::RENDERBIN_ORDER_TV2D_VEHICLE_DOORS,
    //   "RenderBin"); l_pVehicleDoors->getOrCreateStateSet()->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    //   l_pPlanView->setImpostorDoors(l_pDoorView);
    //   l_pImpostorPlanView->setImpostorDoors(l_pDoorView);

    //   // Vertical mode: render the vehicle doors on top of the impostor
    //   auto l_pVertDoorView = new pc::core::View(
    //       "Vert Doors View",
    //       getCorrectDriveHandSideViewport(g_views->m_vertPlanViewport, g_views->m_usableCanvasViewport),
    //       virtcam::g_positions->m_vertPlan);
    //   l_pVertDoorView->setProjectionMatrixAsOrtho2D(
    //       - l_total_length_meters /2., l_total_length_meters /2.,
    //       - l_total_width_meters /2. , l_total_width_meters /2.);
    //   l_pVertDoorView->addAsset(l_pVehicleDoors);
    //   l_pVertDoorView->setRenderOrder(osg::Camera::POST_RENDER);

    //   // l_pVehicleDoors->getOrCreateStateSet()->setRenderBinDetails(cc::core::RENDERBIN_ORDER_TV2D_VEHICLE_DOORS,
    //   "RenderBin");
    //   // l_pVehicleDoors->getOrCreateStateSet()->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);

    //   l_pVertPlanView->setImpostorDoors(l_pVertDoorView);
    // }

    //! give this view a unique StateSet whitout replacing its the default StateSet
    osg::StateSet* l_PlanViewStateSet = new osg::StateSet(*l_pPlanView->getOrCreateStateSet());
    l_pPlanView->setStateSet(l_PlanViewStateSet);
    osg::Uniform* l_pAlphaUniformPlaView =
        l_PlanViewStateSet->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);
    l_pAlphaUniformPlaView->set(1.0f); // PRQA S 3803

    //! give the Eng View LSMG (impostor) view a unique StateSet whitout replacing its the default StateSet
    osg::StateSet* l_impostorPlanViewStateSet = new osg::StateSet(*l_pImpostorPlanView->getOrCreateStateSet());
    l_pImpostorPlanView->setStateSet(l_impostorPlanViewStateSet);
    l_pAlphaUniformPlaView = l_impostorPlanViewStateSet->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);
    l_pAlphaUniformPlaView->set(1.0f); // PRQA S 3803

#endif

    // Park Assist Plan View

    // threat views
    // ***************************************************************************************************** The zoom
    // views will be realized with an ortho camera, so the world sizes for the vp are needed. For now hard-coded in the
    // SW.
    const osg::Vec2i l_vpSize = g_views->m_mainViewport.m_size;
    // const double length = 4.0; // corresponds to vehicle length
    // const double width = length * static_cast<double>(l_vpSize.x()) / static_cast<double>(l_vpSize.y());

#if USE_FISHEYE_VIEWS_SIDE

    // left view *******************************************************************************************************
    pc::views::warpfisheye::PartialUnfishModel* const l_pLeftModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeft.data());
    pc::views::warpfisheye::PartialUnfishModel* const l_pRightModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRight.data());
#if ENABLE_VERTICAL_MODE
    pc::views::warpfisheye::PartialUnfishModel* l_pVertLeftModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishLeftVert);
    pc::views::warpfisheye::PartialUnfishModel* l_pVertRightModel =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRightVert);

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertLeftView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Vertical leftView",
        getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pVertLeftModel,
        l_vertLeftSettings.get(),
        l_leftCropBoundsVert.get());

    // Vertical mode right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertRightView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Vertical rightView",
        getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pVertRightModel,
        l_vertRightSettings.get(),
        l_rightCropBoundsVert.get());
#endif
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pLeftView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "leftView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::LEFT_CAMERA,
        l_pLeftModel,
        l_leftSettings.get(),
        l_leftCropBoundsHori.get(),
        rbp::vis::imp::sh::ESharpnessView::FIXED,
        rbp::vis::imp::tnf::ETnfView::DEFAULT);

    // right view
    // *******************************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRightView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "rightView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::RIGHT_CAMERA,
        l_pRightModel,
        l_rightSettings.get(),
        l_rightCropBoundsHori.get(),
        rbp::vis::imp::sh::ESharpnessView::FIXED,
        rbp::vis::imp::tnf::ETnfView::DEFAULT);

#else
    auto l_pLeftView = new pc::core::View(
        "leftView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_leftView);
    l_pLeftView->addAsset(l_pSingleCamLeft);
    // l_pLeftView->addUpdateCallback(new pc::factory::RenderManager(getFramework(), virtcam::VCAM_SINGLE_LEFT_VIEW));

    // right view
    // *******************************************************************************************************
    auto l_pRightView = new pc::core::View(
        "rightView",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rightView);
    l_pRightView->addAsset(l_pSingleCamRight);
    // l_pRightView->addUpdateCallback(new pc::factory::RenderManager(getFramework(), virtcam::VCAM_SINGLE_RIGHT_VIEW));

#if ENABLE_VERTICAL_MODE
    // Vertical mode left view
    // *******************************************************************************************************
    auto l_pVertLeftView = new pc::core::View(
        "Vertical leftView",
        getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertLeftView);
    l_pVertLeftView->addAsset(l_pSingleCamLeft);

    // Vertical mode right view
    // *******************************************************************************************************
    auto l_pVertRightView = new pc::core::View(
        "Vertical rightView",
        getCorrectDriveHandSideViewport(g_views->m_vertSideViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertRightView);
    l_pVertRightView->addAsset(l_pSingleCamRight);
#endif
#endif
    const auto l_rimProtectionLineFront = new pc::core::Asset(AssetId::EASSETS_RIM_PROTECTION_LINE, new cc::assets::rimline::RimProtectionLine(cc::assets::rimline::ERimPlaneView::RIMPLANEVIEW_FRONT));

    // front wheel left view
    // *******************************************************************************************************
    const auto l_pFrontWheelLeftView = new pc::core::View(
        "Front Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelLeft, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_frontWheelLeft);
    l_pFrontWheelLeftView->addAsset(l_pSingleCamLeft);
    // l_pFrontWheelLeftView->addUpdateCallback(new pc::factory::RenderManager(getFramework(),
    // virtcam::VCAM_FRONT_WHEEL_LEFT_VIEW));
    l_pFrontWheelLeftView->addAsset(l_rimProtectionLineFront);

    // set the offset in the middle of viewport
    constexpr float l_additionalViewportOffset = 0.0f;
    osg::Matrix l_viewportOffsetMatrixLeft, l_viewportOffsetMatrixRight;
    l_viewportOffsetMatrixLeft.makeTranslate(osg::Vec3f(l_additionalViewportOffset, 0.0f, 0.0f));
    l_viewportOffsetMatrixRight.makeTranslate(osg::Vec3f(-l_additionalViewportOffset, 0.0f, 0.0f));

    l_pFrontWheelLeftView->setProjectionMatrix(
        l_pFrontWheelLeftView->getProjectionMatrix() * l_viewportOffsetMatrixLeft);
#if ENABLE_VERTICAL_MODE
    // vertical front wheel left view
    auto l_pVertFrontWheelLeftView = new pc::core::View(
        "Vertical Front Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelLeftViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertWheelLeftView);
    l_pVertFrontWheelLeftView->addAsset(l_pSingleCamLeft);
    osg::Matrix l_viewportOffsetMatrixVertLeft;
    l_viewportOffsetMatrixVertLeft.makeTranslate(osg::Vec3f(0.0f, l_additionalViewportOffset, 0.0f));
    l_pVertFrontWheelLeftView->setProjectionMatrix(
        l_pVertFrontWheelLeftView->getProjectionMatrix() * l_viewportOffsetMatrixVertLeft);

    // vertical front wheel right view
    auto l_pVertFrontWheelRightView = new pc::core::View(
        "Vertical Front Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_vertWheelRightViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_vertWheelRightView);
    l_pVertFrontWheelRightView->addAsset(l_pSingleCamRight);
    osg::Matrix l_viewportOffsetMatrixVertRight;
    l_viewportOffsetMatrixVertRight.makeTranslate(osg::Vec3f(0.0f, -l_additionalViewportOffset, 0.0f));
    l_pVertFrontWheelRightView->setProjectionMatrix(
        l_pVertFrontWheelRightView->getProjectionMatrix() * l_viewportOffsetMatrixVertRight);

#endif

    // front wheel right view
    // *******************************************************************************************************
    const auto l_pFrontWheelRightView = new pc::core::View(
        "Front Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_frontWheelRight, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_frontWheelRight);
    l_pFrontWheelRightView->addAsset(l_pSingleCamRight);
    // l_pFrontWheelRightView->addUpdateCallback(new pc::factory::RenderManager(getFramework(),
    // virtcam::VCAM_FRONT_WHEEL_RIGHT_VIEW));
    l_pFrontWheelRightView->setProjectionMatrix(
        l_pFrontWheelRightView->getProjectionMatrix() * l_viewportOffsetMatrixRight);
    l_pFrontWheelRightView->addAsset(l_rimProtectionLineFront);

    // rear wheel left view
    // *******************************************************************************************************
    const auto l_rimProtectionLineRear = new pc::core::Asset(AssetId::EASSETS_RIM_PROTECTION_LINE, new cc::assets::rimline::RimProtectionLine(cc::assets::rimline::ERimPlaneView::RIMPLANEVIEW_REAR));
    const auto l_pRearWheelLeftView = new pc::core::View(
        "Rear Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_rearWheelLeft, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rearWheelLeft);
    l_pRearWheelLeftView->addAsset(l_pSingleCamLeft);
    l_pRearWheelLeftView->addAsset(l_rimProtectionLineRear);
    // l_pRearWheelLeftView->addUpdateCallback(new pc::factory::RenderManager(getFramework(),
    // virtcam::VCAM_REAR_WHEEL_LEFT_VIEW));
    l_pRearWheelLeftView->setProjectionMatrix(
        l_pRearWheelLeftView->getProjectionMatrix() * l_mirrorTheWorld * l_viewportOffsetMatrixLeft);
    // rear wheel right view
    // *******************************************************************************************************
    const auto l_pRearWheelRightView = new pc::core::View(
        "Rear Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_rearWheelRight, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rearWheelRight);
    l_pRearWheelRightView->addAsset(l_pSingleCamRight);
    l_pRearWheelRightView->addAsset(l_rimProtectionLineRear);
    // l_pRearWheelRightView->addUpdateCallback(new pc::factory::RenderManager(getFramework(),
    // virtcam::VCAM_REAR_WHEEL_RIGHT_VIEW));
    l_pRearWheelRightView->setProjectionMatrix(
        l_pRearWheelRightView->getProjectionMatrix() * l_mirrorTheWorld * l_viewportOffsetMatrixRight);
    // Rear Junction View
    // ************************************************************************************************
#if 0
  // auto l_pRearJunctionView = new views::panoramaview::PanoramaView(
  //   getFramework(),
  //   views::panoramaview::PanoramaView::PANORAMAVIEW_REAR,
  //   "Rear Junction View",
  //   getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));

    auto l_pRearJunctionView = new views::panoramaviewtriple::PanoramaViewTriple(
    getFramework(),
    views::panoramaviewtriple::PanoramaViewTriple::PANORAMAVIEW_REAR,
    "Rear Junction View",
    getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));
#else
    // pc::views::warpfisheye::StereoGraphicModel* l_pRearModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_rearSettings->m_horizontalHalfFov);
    // pc::views::warpfisheye::StereoGraphicModel* l_pRearModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_rearModelSettings);
    // pc::views::warpfisheye::PinholeModel* l_pRearModel = new
    // pc::views::warpfisheye::PinholeModel(l_rearSettings->m_horizontalHalfFov);

    pc::views::warpfisheye::StereoGraphicModel* const l_pRearJunctionModel =
        new pc::views::warpfisheye::StereoGraphicModel(l_partUnfishRearPano->m_modelSettings);
    // pc::views::warpfisheye::PartialUnfishModel* l_pRearJunctionModel = new
    // pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearPano);

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pRearJunctionView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Rear Junction View",
            getCorrectDriveHandSideViewport(g_views->m_fullScreenViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModel,
            l_rearPanoSettings.get(),
            l_rearPanoCropBoundsHori.get(),
            rbp::vis::imp::sh::ESharpnessView::FIXED,
            rbp::vis::imp::tnf::ETnfView::DEFAULT);

    pc::core::Asset* const l_pRearJunctionWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        createFisheyeWheelTracks(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModel,
            l_rearPanoSettings));

    pc::core::Asset* const l_pRearJunctionOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        createFisheyeOutermostLines(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModel,
            l_rearPanoSettings));

    pc::core::Asset* const l_pRearJunctionOutermostLinesWheeltrack = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModel,
            l_rearPanoSettings));

    pc::core::Asset* const l_pRearJunctionCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        createFisheyeCoverPlate(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModel,
            l_rearPanoSettings));

    pc::core::Asset* const l_pRearJunctionDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        createFisheyeDL1(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModel,
            l_rearPanoSettings));

    l_pRearJunctionView->addAsset(l_pRearJunctionWheelTracks);
    l_pRearJunctionView->addAsset(l_pRearJunctionOutermostLines);
    l_pRearJunctionView->addAsset(l_pRearJunctionOutermostLinesWheeltrack);
    l_pRearJunctionView->addAsset(l_pRearJunctionCoverPlate);
    l_pRearJunctionView->addAsset(l_pRearJunctionDL1);
    l_pRearJunctionView->setProjectionMatrix(l_pRearJunctionView->getProjectionMatrix() * l_mirrorTheWorld);
    // l_pRearJunctionView->addAsset(l_pRearTrailerAssistLine);
    l_pRearJunctionView->addAsset(l_pFloorPlate);
#if ENABLE_VERTICAL_MODE
    // ! Vertical rear junction view
    pc::views::warpfisheye::PartialUnfishModel* l_pRearJunctionModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishRearPanoVert);

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertRearJunctionView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vertical Rear Junction View",
            getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::REAR_CAMERA,
            l_pRearJunctionModelVert,
            l_vertRearPanoSettings.get(),
            l_rearPanoCropBoundsVert.get());

    l_pVertRearJunctionView->setProjectionMatrix(l_pVertRearJunctionView->getProjectionMatrix() * l_mirrorTheWorld);

    // ! Vertical front junction view
    pc::views::warpfisheye::PartialUnfishModel* l_pFrontJunctionModelVert =
        new pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontPanoVert);

    cc::views::warpfisheye::CustomWarpFisheyeView* l_pVertFrontJunctionView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Vertical Front Junction View",
            getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModelVert,
            l_vertFrontPanoSettings.get(),
            l_frontPanoCropBoundsVert.get());

#endif // ENABLE_VERTICAL_MODE
#endif

// Front Junction View ***********************************************************************************************
#if 0

  // auto l_pFrontJunctionView = new views::panoramaview::PanoramaView(
  //   getFramework(),
  //   views::panoramaview::PanoramaView::PANORAMAVIEW_FRONT,
  //   "Front Junction View",
  //   getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));

  auto l_pFrontJunctionView = new views::panoramaviewtriple::PanoramaViewTriple(
    getFramework(),
    views::panoramaviewtriple::PanoramaViewTriple::PANORAMAVIEW_FRONT,
    "Front Junction View",
    getCorrectDriveHandSideViewport(g_views->m_usableCanvasViewport, g_views->m_usableCanvasViewport));
#else
    // pc::views::warpfisheye::StereoGraphicModel* l_pFrontModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_frontSettings->m_horizontalHalfFov);
    // pc::views::warpfisheye::StereoGraphicModel* l_pFrontModel = new
    // pc::views::warpfisheye::StereoGraphicModel(l_frontModelSettings);
    // pc::views::warpfisheye::PinholeModel* l_pRearModel = new
    // pc::views::warpfisheye::PinholeModel(l_rearSettings->m_horizontalHalfFov);

    pc::views::warpfisheye::StereoGraphicModel* const l_pFrontJunctionModel =
        new pc::views::warpfisheye::StereoGraphicModel(l_partUnfishFrontPano->m_modelSettings);
    // pc::views::warpfisheye::PartialUnfishModel* l_pFrontJunctionModel = new
    // pc::views::warpfisheye::PartialUnfishModel(l_partUnfishFrontPano);

    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFrontJunctionView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Front Junction View",
            getCorrectDriveHandSideViewport(g_views->m_fullScreenViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModel,
            l_frontPanoSettings.get(),
            l_frontPanoCropBoundsHori.get(),
            rbp::vis::imp::sh::ESharpnessView::FIXED,
            rbp::vis::imp::tnf::ETnfView::DEFAULT);

    pc::core::Asset* const l_pFrontJunctionWheelTracks = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_WHEELTRACKS,
        createFisheyeWheelTracks(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModel,
            l_frontPanoSettings));

    pc::core::Asset* const l_pFrontJunctionOutermostLines = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES,
        createFisheyeOutermostLines(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModel,
            l_frontPanoSettings));

    pc::core::Asset* const l_pFrontJunctionOutermostLinesWheeltrack = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
        createFisheyeOutermostLinesColorful(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModel,
            l_frontPanoSettings));

    pc::core::Asset* const l_pFrontJunctionCoverPlate = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_COVERPLATE,
        createFisheyeCoverPlate(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModel,
            l_frontPanoSettings));

    pc::core::Asset* const l_pFrontJunctionDL1 = new pc::core::Asset(
        AssetId::EASSETS_FISHEYE_DL1_COLORFUL,
        createFisheyeDL1(
            getFramework()->asCustomFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontJunctionModel,
            l_frontPanoSettings));

    l_pFrontJunctionView->addAsset(l_pFrontJunctionWheelTracks);
    l_pFrontJunctionView->addAsset(l_pFrontJunctionOutermostLines);
    l_pFrontJunctionView->addAsset(l_pFrontJunctionOutermostLinesWheeltrack);
    l_pFrontJunctionView->addAsset(l_pFrontJunctionCoverPlate);
    l_pFrontJunctionView->addAsset(l_pFrontJunctionDL1);
    l_pFrontJunctionView->addAsset(l_pFloorPlate);
#endif

#if USE_PARKING_VIEW
    // parking mode select view
    // ***************************************************************************************************

    auto l_pParkingView = new pc::core::View(
        "Parking UI View",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_horiParkingPlan);

    m_augmentedViewTransitionHori = new cc::assets::AugmentedViewTransition(getFramework(), cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
    auto l_pAugmentedViewTransitionHori =
        new pc::core::Asset(AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION, m_augmentedViewTransitionHori);

    m_augmentedViewTransitionVert = new cc::assets::AugmentedViewTransition(getFramework(), cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
    auto l_pAugmentedViewTransitionVert =
        new pc::core::Asset(AssetId::EASSETS_AUGMENTED_VIEW_TRANSITION_VERT, m_augmentedViewTransitionVert);

    auto l_pParkingViewElement = new pc::core::Asset(
        AssetId::EASSETS_UI_PARKING_SEARCHING,
        new assets::uielements::ParkingView(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_PARKING_SEARCHING));

    auto l_pParkingViewBackground = new pc::core::Asset(
        AssetId::EASSETS_UI_PARKING_BACKGROUND,
        new assets::uielements::ParkingViewBackground(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_PARKING_BACKGROUND));

    auto l_pHoriParkingSlotElement = new pc::core::Asset(
        AssetId::EASSETS_UI_PARKING_SEARCHING,
        new assets::uielements::HoriParkingSlot(
            getFramework()->asCustomFramework(), AssetId::EASSETS_UI_PARKING_SEARCHING));

    auto l_pSpeedOverlay =
        new pc::core::Asset(AssetId::EASSETS_SPEED_OVERLAY, new assets::uielements::SpeedOverlay(getFramework()));

    l_pParkingView->addAsset(l_pParkingViewBackground);
    l_pParkingView->addAsset(l_pParkingViewElement);
    l_pParkingView->addAsset(l_pHoriParkingSlotElement);
    l_pParkingView->addAsset(l_pSpeedOverlay);

    auto l_dynamicGearView = new pc::core::View(
        "Gear UI View",
        getCorrectDriveHandSideViewport(g_views->m_planViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan);

    l_dynamicGearView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);

    auto l_pDynamicGearOverlaysUIPanel = new pc::core::Asset(
        AssetId::EASSETS_DYNAMIC_GEAR_OVERLAYS,
        new assets::dynamicgearoverlay::DynamicGearOverlays(
            getFramework()->asCustomFramework(), cc::assets::dynamicgearoverlay::DynamicGear::UI_PANEL));
    auto l_pRemainingMoveNumber = new pc::core::Asset(
        AssetId::EASSETS_REMAINING_MOVE_NUMBER, new assets::uielements::RemainingMoveNumber(getFramework()));
    auto l_pDynamicDistance = new pc::core::Asset(
        AssetId::EASSETS_DYNAMIC_DISTANCE_OVERLAYS, new assets::uielements::DynamicDistance(getFramework()));

    l_dynamicGearView->addAsset(l_pDynamicGearOverlaysUIPanel);
    l_dynamicGearView->addAsset(l_pDynamicDistance);
    l_dynamicGearView->addAsset(l_pRemainingMoveNumber);

    const float              l_scalingFactor = 2.0f / 3.0f;
    pc::core::UpscalingData* l_upscalingData = new pc::core::UpscalingData(l_scalingFactor, g_views->m_planViewport);

    // parking searching view
    // ******************************************************************************************************
    auto l_pParkSearchView = new cc::views::parkview::ParkInView(
        l_upscalingData,
        g_views->m_planViewport,
        virtcam::g_positions->m_parkView,
        getFramework(),
        AssetId::EASSETS_PARKINGSPOTS);

    auto l_pStreetOverlay =
        new pc::core::Asset(AssetId::EASSETS_STREETOVERLAY, new assets::streetoverlay::StreetOverlay(getFramework()));

    m_augmentedViewTransitionHori->registerWithView(l_pParkSearchView);
    m_augmentedViewTransitionVert->registerWithView(l_pParkSearchView);

    l_pParkSearchView->addAsset(l_pAugmentedViewTransitionHori, true);
    l_pParkSearchView->addAsset(l_pAugmentedViewTransitionVert, true);

    l_pParkSearchView->addAsset(l_pStreetOverlay);
    l_pParkSearchView->addAsset(l_pParkingSearchingElements);

    // rpa guidance View **************************************************************************************
    // auto l_pParkingRPAGuidanceView = new pc::core::View("Parking RPA Guidance View",
    // g_views->m_usableCanvasViewport);

    // auto l_pParkingRPAGuidance = new pc::core::Asset( AssetId::EASSETS_RPA_GUIDANCE,
    //                       new assets::uielements::ParkingRPAGuidance(getFramework()->asCustomFramework(),
    //                       AssetId::EASSETS_RPA_GUIDANCE));

    // l_pParkingRPAGuidanceView->addAsset(l_pParkingRPAGuidance);

#ifdef USE_VIRTUAL_OBJECT
    auto l_pVirtualRealityManager = new pc::core::Asset(
        AssetId::EASSETS_VIRTUAL_REALITY, new assets::virtualreality::VirtualRealityManager(getFramework()));
    l_pParkSearchView->addAsset(l_pVirtualRealityManager);
#else
    auto l_pParkingSpotManager = new pc::core::Asset(
        AssetId::EASSETS_PARKINGSPOTS, new assets::parkingspots::ParkingSpotManager(getFramework()));
    l_pParkSearchView->addAsset(l_pParkingSpotManager);
#endif

#endif

    // Surround View
    // *****************************************************************************************************
    //  const unsigned int l_testCullSetting =  ~(pc::vehiclemodel::VehicleModel::CULL_SETTING_INTERIOR |
    //  pc::vehiclemodel::VehicleModel::CULL_SETTING_WHEELS);
    cc::views::surroundview::SurroundView* const l_pSurroundView = new cc::views::surroundview::SurroundView(
        "Surround View",
        getCorrectDriveHandSideViewport(g_views->m_mainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->getPosition(virtcam::VCAM_DEFAULT_VIEW));

    const auto l_pRenderManagerSurroundView =
        new cc::assets::common::CustomRenderManager(l_pRenderManagerRegistry, virtcam::VCAM_DEFAULT_VIEW,
                                                    rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT,
                                                    rbp::vis::imp::sh::ESharpnessView::FIXED,
                                                    rbp::vis::imp::tnf::ETnfView::DEFAULT);
    l_pRenderManagerSurroundView->assignCamera(l_pSurroundView);
    l_pSurroundView->addAsset(l_pFloor);
    l_pSurroundView->addAsset(l_pFloorPlate);
    l_pSurroundView->addAsset(l_pBowl);
    // l_pSurroundView->addAsset(l_pVehicle);
    l_pSurroundView->addAsset(l_pOutermostLines);
    // l_pSurroundView->addAsset(l_pOutermostLinesColorful);
    // l_pSurroundView->addAsset(l_pWheelTracks);
    // l_pSurroundView->addAsset(l_pParkingIcon);
#if USE_RADAR_WALL_SHOW
    l_pSurroundView->addAsset(l_obstacleOverlay);
#endif
#if USE_RADAR_WALL
#else
    l_pSurroundView->addAsset(l_pObstacleOverlay_Perspective);
#endif
    // l_pSurroundView->addAsset(l_pSplineOverlay);

#if DISABLE_ROTATABLE_TRANSPARENT_MODEL
    // transparent impostor as vehicle model asset
    assets::impostor::TransparentVehicleImpostor* l_pTV2D_Transparent2dImpostor =
        new assets::impostor::TransparentVehicleImpostor(
            getFramework()->asCustomFramework(),
            AssetId::EASSETS_TV2D_TRANSPARENT_IMPOSTOR,
            static_cast<pc::vehiclemodel::VehicleModel*>(l_pVehicle->getAsset()),
            osg::Vec2us(
                static_cast<unsigned short>(l_pSurroundView->getViewport()->width()),
                static_cast<unsigned short>(l_pSurroundView->getViewport()->height())));
    l_pTV2D_Transparent2dImpostor->setName("TV2D_Transparent2dImpostor");

    l_pSurroundView->addAsset(l_pTV2D_Transparent2dImpostor);
#else
    l_pSurroundView->addAsset(l_pVehicle);
#endif
    //! give this view a unique StateSet whitout replacing its the default StateSet
    osg::StateSet* const l_SurroundViewStateSet = l_pSurroundView->getOrCreateStateSet();
    osg::Uniform*  const l_pAlphaUniform =
        l_SurroundViewStateSet->getOrCreateUniform("VehicleTransparency", osg::Uniform::FLOAT);
    l_pAlphaUniform->set(1.0f); // PRQA S 3803

    // Install hemispherical camera controller on 3D surroundview
    const auto l_hemisphereCameraUpdaterHori = new cc::virtcam::HeadUnitHemisphereCameraUpdater(getFramework());

    // Set center to match the default SV virtual camera position
    osg::Vec3f carCenterHori;
    // if (!l_hemisphereCameraUpdater->estimateCenter(cc::virtcam::g_positions->m_overTheRoof.toViewMatrix(),
    // carCenter))
    // {
    //   carCenter = osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 5.0f); // change to z=1
    // }
    carCenterHori =
        osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 0.0f) + cc::virtcam::g_carCenter->m_carCenterHori;
    l_hemisphereCameraUpdaterHori->setHemisphereCenter(carCenterHori);

    l_pSurroundView->setCameraUpdater(l_hemisphereCameraUpdaterHori);

    // Attach a return channel updater, which will send the current camera parameters (expressed in hemispherical
    // coordinates) to the HU (via state machine)
    // l_hemisphereCameraUpdater->attachReturnChannelUpdateCallback(l_pSurroundView, this);

#if ENABLE_VERTICAL_MODE
    // ! Vertical mode surround view
    cc::views::surroundview::SurroundView* l_pVertSurroundView = new cc::views::surroundview::SurroundView(
        "Vertical Surround View",
        getCorrectDriveHandSideViewport(g_views->m_vertMainViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->getPosition(virtcam::VCAM_VERT_REAR_LEFT_VIEW));

    auto l_pRenderManagerVertSurroundView =
        new cc::assets::common::CustomRenderManager(l_pRenderManagerRegistry, virtcam::VCAM_VERT_REAR_LEFT_VIEW);
    l_pRenderManagerVertSurroundView->assignCamera(l_pVertSurroundView);

    auto       l_hemisphereCameraUpdaterVert = new cc::virtcam::HeadUnitHemisphereCameraUpdater(getFramework());
    osg::Vec3f carCenterVert;
    carCenterVert =
        osg::Vec3f(pc::vehicle::g_mechanicalData->getCenter(), 0.0f) + cc::virtcam::g_carCenter->m_carCenterVert;
    l_hemisphereCameraUpdaterVert->setHemisphereCenter(carCenterVert);

    l_pVertSurroundView->addAsset(l_pFloor);
    l_pVertSurroundView->addAsset(l_pBowl);
    // l_pVertSurroundView->addAsset(l_pVehicle);
    l_pVertSurroundView->addAsset(l_pOutermostLines);
    l_pVertSurroundView->addAsset(l_pWheelTracks);
    // l_pVertSurroundView->addAsset(l_pParkingIcon);

    l_pVertSurroundView->addAsset(l_pTV2D_Transparent2dImpostor);
    l_pVertSurroundView->setCameraUpdater(l_hemisphereCameraUpdaterVert);
#if USE_RADAR_WALL
#else
    l_pVertSurroundView->addAsset(l_pObstacleOverlay_Perspective);
#endif

    //! give this view a unique StateSet whitout replacing its the default StateSet
    // osg::StateSet* l_vertSurroundViewStateSet = l_pVertSurroundView->getOrCreateStateSet();
    // osg::Uniform *l_pVertAlphaUniform = l_vertSurroundViewStateSet->getOrCreateUniform("VehicleTransparency",
    // osg::Uniform::FLOAT); l_pVertAlphaUniform->set(1.0f);  // PRQA S 3803
#endif // ENABLE_VERTICAL_MODE

    //! Fisheye Views
    //! ***************************************************************************************************
    const auto l_pRawFisheyeFront = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        0u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeFront->setName("RawFisheyeFront");

    const auto l_pRawFisheyeRear = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        2u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeRear->setName("RawFisheyeRear");

    const auto l_pRawFisheyeLeft = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        3u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeLeft->setName("RawFisheyeLeft");

    const auto l_pRawFisheyeRight = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(
        1u,
        false,
        getFramework(),
        getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    l_pRawFisheyeRight->setName("RawFisheyeRight");

    // auto l_pRawFisheyeQuad  = new cc::views::rawfisheyeview::CustomRawFisheyeView(0, true, getFramework(),
    // getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    // l_pRawFisheyeQuad->setName("RawFisheyeQuad");

    //! EOL Calibration View
    //! ***************************************************************************************************
    //   auto l_pRawFisheyeQuadCalibration  = new cc::views::CustomRawFisheyeView::CustomRawFisheyeView(0u, true,
    //   getFramework(), getCorrectDriveHandSideViewport(g_views->m_fisheyeViewport, g_views->m_usableCanvasViewport));
    //   l_pRawFisheyeQuadCalibration->setName("RawFisheyeQuadCalibration");
    //   auto l_ECALprogressoverlay  = new pc::core::Asset( AssetId::EASSETS_ECAL_PROGRESS_OVERLAY,
    //                                   new
    //                                   assets::ECALprogressoverlay::ECALprogressoverlay(getFramework()->asCustomFramework(),
    //                                   AssetId::EASSETS_ECAL_PROGRESS_OVERLAY));

    //   l_pRawFisheyeQuadCalibration->addAsset(l_ECALprogressoverlay);

    //! engineering views
    //! ***********************************************************************************************
    const auto l_pEngineeringView = new cc::views::nfsengineeringview::NfsEngineeringView(
        getFramework(), cc::views::nfsengineeringview::ENGSCREEN_DEFAULT, nullptr);
    l_pEngineeringView->setName("EngineeringView Default");

    const auto l_pEnginneringViewCalib = new cc::views::nfsengineeringview::NfsEngineeringView(
        getFramework(), cc::views::nfsengineeringview::ENGSCREEN_NFSCALIB, nullptr);
    l_pEnginneringViewCalib->setName("EnginneringViewCalib");

    const auto l_CPCCpcDebugScreen = new cc::views::nfsengineeringview::NfsEngineeringView(
        getFramework(), cc::views::nfsengineeringview::ENGSCREEN_CPC, nullptr);
    l_CPCCpcDebugScreen->setName("CPCCpcDebugView");

#if USE_IN_NISSAN
    auto l_pProfilingView = new pc::views::perfview::ProfilingView();
    l_pProfilingView->addAsset(l_pVehicle);
#endif

#if USE_IN_NISSAN
    // separator between plan view and main view
    auto l_pDayNightView = new cc::views::daynightview::DayNightView(
        "DayNight",
        getCorrectDriveHandSideViewport(g_views->m_dayNightViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        false,
        g_views->m_usableCanvasViewport,
        0u);
#endif

    // separator between wheel left and wheel right view
    const auto l_pWheelSeparatorHorizontalView =
        new pc::core::View("WheelSeparatorHorizontalView", g_views->m_usableCanvasViewport);
    l_pWheelSeparatorHorizontalView->addAsset(l_pWheelSeparator_Horizontal);
#if ENABLE_VERTICAL_MODE
    auto l_pWheelSeparatorVerticalView =
        new pc::core::View("WheelSeparatorVerticalView", g_views->m_usableCanvasViewport);
    l_pWheelSeparatorVerticalView->addAsset(l_pWheelSeparator_Vertical);
#endif // ENABLE_VERTICAL_MODE
    // See through bonnet **********************************************************************************************
    // auto l_pSTB = new pc::core::Asset(AssetId::EASSETS_SEE_THROUGH_BONNET, new
    // assets::stb::SeeThroughBonnet(getFramework()->asCustomFramework()));
    const auto l_pSTB = new pc::core::Asset(
        AssetId::EASSETS_SEE_THROUGH_BONNET,
        new assets::stb::SeeThroughBonnet(
            getFramework()->asCustomFramework(),
            (static_cast<assets::trajectory::OutermostLine*>(l_pOutermostLines->asGroup()->getChild(0u)))
                ->getMainLogicPtr()
                ->getInputDataRef()));

    // bonnet View
    // ******************************************************************************************************

    //! * [l_pBonnetView](@ref cc.views.bonnetview.BonnetView): See Through Bonnet
    const auto l_pBonnetView = new cc::views::bonnetview::BonnetView(
        "Bonnet View", g_views->m_mainViewport, virtcam::g_positions->m_bonnet, getFramework());

    const auto l_pRenderManagerBonnetView =
        new pc::factory::RenderManager( l_pRenderManagerRegistry, virtcam::VCAM_BONNET_VIEW,
                                        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT,
                                        rbp::vis::imp::sh::ESharpnessView::FIXED,
                                        rbp::vis::imp::tnf::ETnfView::DEFAULT);
    l_pRenderManagerBonnetView->assignCamera(l_pBonnetView);
    l_pBonnetView->addAsset(l_pFloor);      // needed for floor-plate text
    l_pBonnetView->addAsset(l_pFloorPlate); // not retangle baseplate
    // l_pBonnetView->addAsset(l_pWheelTracks);
    l_pBonnetView->addAsset(l_pOutermostLines);
    l_pBonnetView->addAsset(l_pSTB);
    // l_pBonnetView->addAsset(l_pOutermostLinesColorful);

    //! Buttons view
    const auto l_pButtonsView       = new pc::core::View("Buttons View", g_views->m_fullScreenViewport);
    const auto l_pViewModeButtonBar = new cc::assets::button::viewmodebutton::ViewModeButtonGroup(
        cc::core::AssetId::EASSETS_VIEW_BUTTON_BAR, getFramework(), l_pButtonsView);
    const auto l_pViewChangeButtonGroup = new cc::assets::button::viewchangebutton::ViewChangeButtonGroup(
        cc::core::AssetId::EASSETS_VIEW_CHANGE_BUTTON_GROUP, getFramework(), l_pButtonsView);
#ifdef ENABLE_APAUI
    const auto l_pParkButton =
        new cc::assets::button::ParkButton(cc::core::AssetId::EASSETS_PARK_BUTTON, getFramework(), l_pButtonsView);
    const auto l_pSonarLoudSpeakerButton = new cc::assets::button::SonarLoudSpeakerButton(
        cc::core::AssetId::EASSETS_SONAR_SPEAKER_LOUD_BUTTON, getFramework(), l_pButtonsView);
#endif
    const auto l_pCloseButton =
        new cc::assets::button::CloseButton(cc::core::AssetId::EASSETS_CLOSE_BUTTON, getFramework(), l_pButtonsView);
    const auto l_pSettingPageOverlay = new cc::assets::settingpageoverlay::SettingPageOverlay(
        cc::core::AssetId::EASSETS_SETTINGPAGE_OVERLAY, getFramework(), l_pButtonsView);
    const auto l_pSettingPageEntryButton = new cc::assets::settingpageoverlay::SettingPageEntryButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_ENTRY_BUTTON, getFramework(), static_cast<cc::assets::settingpageoverlay::SettingPageSwitch*>(l_pSettingPageOverlay), l_pButtonsView);
    const auto l_pBrightnessSliderOverlay = new cc::assets::settingpageoverlay::BrightnessSliderOverlay(
        cc::core::AssetId::EASSETS_SETTINGPAGE_OVERLAY, getFramework(), l_pButtonsView);
    const auto l_pBrightnessSliderEntryButton = new cc::assets::settingpageoverlay::BrightnessSliderEntryButton(
        cc::core::AssetId::EASSETS_BRIGHTNESSSLIDER_ENTRY_BUTTON, getFramework(), static_cast<cc::assets::settingpageoverlay::BrightnessSliderSwitch*>(l_pBrightnessSliderOverlay), l_pButtonsView);
    const auto l_pViewInfoOverlay = new pc::core::Asset(
        AssetId::EASSETS_UI_WARNSYMBOL_USS,
        new assets::uielements::ViewInfoOverlays(getFramework()->asCustomFramework(), AssetId::EASSETS_VIEWINFO_OVERLAY));
    const auto l_modOverlay = new cc::mod::ModOverlay(AssetId::EASSETS_MOD_OVERLAY, getFramework()->asCustomFramework());
    l_pButtonsView->addAsset(l_modOverlay);
    l_pButtonsView->addAsset(l_pViewInfoOverlay);
    l_pButtonsView->addAsset(l_pViewChangeButtonGroup);
#ifdef ENABLE_APAUI
    l_pButtonsView->addAsset(l_pParkButton);
    l_pButtonsView->addAsset(l_pSonarLoudSpeakerButton);
#endif
    l_pButtonsView->addAsset(l_pViewModeButtonBar);
    l_pButtonsView->addAsset(l_pCloseButton);
    l_pButtonsView->addAsset(l_pSettingPageEntryButton);
    l_pButtonsView->addAsset(l_pBrightnessSliderEntryButton);
    l_pButtonsView->addAsset(l_pBrightnessSliderOverlay);
    l_pButtonsView->addAsset(l_pSettingPageOverlay);
    l_hemisphereCameraUpdaterHori->addButtonGroup(l_pSettingPageOverlay);
    l_hemisphereCameraUpdaterHori->addButtonGroup(l_pViewModeButtonBar);
    l_hemisphereCameraUpdaterHori->addButton(l_pSettingPageEntryButton);
    l_hemisphereCameraUpdaterHori->addButton(l_pBrightnessSliderEntryButton);
    l_hemisphereCameraUpdaterHori->addButtonGroup(l_pBrightnessSliderOverlay);
    //! setting bar view
    const auto l_pSettingBarView = new pc::core::View("Setting Bar View", g_views->m_mainViewport);
    const auto l_pSettingBar     = new pc::core::Asset(
        AssetId::EASSETS_UI_SETTINGBAR_ICON,
        new assets::uielements::SettingBar(getFramework()->asCustomFramework(), AssetId::EASSETS_UI_SETTINGBAR_ICON));
    l_pSettingBarView->addAsset(l_pSettingBar);

//   auto l_modView = new pc::core::View("ModView", g_views->m_mainViewport);


#ifdef CHAMAELEON
    // Image Quality
    const auto l_imageQualityRoot = new rbp::vis::imp::iq::ImageQualityRoot();
    vfc::nop(osg::Group::addChild(l_imageQualityRoot));

    // Brightness and Color Harmonization
    const auto l_chamaeleon =
        new rbp::vis::imp::chamaeleon::Chamaeleon(getFramework(),
                                                  l_pRenderManagerRegistry,
                                                  l_pFloor->getSV3DNode(),
                                                  &virtcam::g_positions->getPosition(virtcam::VCAM_PLAN_VIEW));
    l_imageQualityRoot->addFeature(l_chamaeleon);

    // Sharpness Harmonization
    const auto l_sharpnessHarmonization = new rbp::vis::imp::sh::SharpnessHarmonization(getFramework());
    l_imageQualityRoot->addFeature(l_sharpnessHarmonization);

    // Custom Window Dump for IMP KPI framework
    const auto l_customWindowDump = new rbp::vis::imp::cwd::CustomWindowDump(getFramework(), l_pPlanView);
    l_imageQualityRoot->addFeature(l_customWindowDump);
#endif

#if ENABLE_FLOATING_MODE
    //! floatting views
    // Floating plan view **********************************************************************************************
    const auto l_pFloatPlanView = new cc::views::planview::PlanView(
        "Floating Plan View",
        getCorrectDriveHandSideViewport(g_views->m_floatPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());
    l_pFloatPlanView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);
    const auto l_renderManagerFloatPlanView =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_PLAN_VIEW,
                                        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT,
                                        rbp::vis::imp::sh::ESharpnessView::FIXED,
                                        rbp::vis::imp::tnf::ETnfView::DEFAULT);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerFloatPlanView);
    l_renderManagerFloatPlanView->assignCamera(l_pFloatPlanView);
    l_pFloatPlanView->addAsset(l_pFloor);
    l_pFloatPlanView->addAsset(l_pFloorPlate);
    l_pFloatPlanView->addAsset(l_pDistanceOverlayFloating);
    l_pDistanceOverlayFloating->setReferenceView(l_pFloatPlanView);

    // Floating vehicle 2d
    // **********************************************************************************************
    const auto l_pFloatPlanViewVehicle2D = new cc::views::planview::PlanView(
        "Floating Plan View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_floatPlanViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());
    l_pFloatPlanViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_vehicle2d / 2.,
        l_total_width_meters_vehicle2d / 2.,
        -l_total_length_meters_vehicle2d / 2.,
        l_total_length_meters_vehicle2d / 2.);
    // l_pFloatPlanViewVehicle2D->addAsset(l_pVehicle2DIcon);
    l_pFloatPlanViewVehicle2D->addAsset(l_pVehicle2D);
    l_pFloatPlanViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pFloatPlanViewVehicle2D->addAsset(l_pWheelTracks);
    l_pFloatPlanViewVehicle2D->addAsset(l_pWarnSymbolUss);
    l_pFloatPlanViewVehicle2D->addAsset(l_pVehicleTransIcon);
    l_pFloatPlanViewVehicle2D->addAsset(l_pVehicle2DWheels);

    // Floating front view
    // **********************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFloatFrontView =
        new cc::views::warpfisheye::CustomWarpFisheyeView(
            "Floating Front View",
            getCorrectDriveHandSideViewport(g_views->m_floatMainViewport, g_views->m_usableCanvasViewport),
            getFramework(),
            pc::core::sysconf::FRONT_CAMERA,
            l_pFrontModel,
            l_frontSettings.get(),
            l_frontCropBoundsHori.get(),
            rbp::vis::imp::sh::ESharpnessView::FIXED,
            rbp::vis::imp::tnf::ETnfView::DEFAULT);

    l_pFloatFrontView->addAsset(l_pFrontWheelTracks);
    l_pFloatFrontView->addAsset(l_pFrontOutermostLines);
    l_pFloatFrontView->addAsset(l_pFrontOutermostLinesWheelTrack);
    l_pFloatFrontView->addAsset(l_pFrontCoverPlate);
    l_pFloatFrontView->addAsset(l_pFrontDL1);
    l_pFloatFrontView->addAsset(l_pFrontRctaOverlay);
    l_pFloatFrontView->addAsset(l_pFloorPlate);

    // Floating rear view **********************************************************************************************
    cc::views::warpfisheye::CustomWarpFisheyeView* const l_pFloatRearView = new cc::views::warpfisheye::CustomWarpFisheyeView(
        "Rear View Float",
        getCorrectDriveHandSideViewport(g_views->m_floatMainViewport, g_views->m_usableCanvasViewport),
        getFramework(),
        pc::core::sysconf::REAR_CAMERA,
        l_pRearModel,
        l_rearSettings.get(),
        l_rearCropBoundsHori.get(),
        rbp::vis::imp::sh::ESharpnessView::FIXED,
        rbp::vis::imp::tnf::ETnfView::DEFAULT);

    // mirror the view  **********************************************************************************************
    l_pFloatRearView->setProjectionMatrix(l_pFloatRearView->getProjectionMatrix() * l_mirrorTheWorld);
    l_pFloatRearView->addAsset(l_pRearWheelTracks);
    l_pFloatRearView->addAsset(l_pRearOutermostLines);
    l_pFloatRearView->addAsset(l_pRearOutermostLinesWheeltrack);
    l_pFloatRearView->addAsset(l_pRearCoverPlate);
    l_pFloatRearView->addAsset(l_pRearDL1);
    l_pFloatRearView->addAsset(l_pRearRctaOverlay);
    l_pFloatRearView->addAsset(l_pFloorPlate);

    // Floating front wheel left  view
    // **********************************************************************************************
    const auto l_pFloatFrontWheelLeftView = new pc::core::View(
        "Float Front Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_floatWheelLeftViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_frontWheelLeft);
    l_pFloatFrontWheelLeftView->addAsset(l_pSingleCamLeft);
    // set the offset in the middle of viewport
    l_pFloatFrontWheelLeftView->setProjectionMatrix(
        l_pFloatFrontWheelLeftView->getProjectionMatrix() * l_viewportOffsetMatrixLeft);
    l_pFloatFrontWheelLeftView->addAsset(l_rimProtectionLineFront);

    // Floating front wheel right view
    // **********************************************************************************************
    const auto l_pFloatFrontWheelRightView = new pc::core::View(
        "Float Front Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_floatWheelRightViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_frontWheelRight);
    l_pFloatFrontWheelRightView->addAsset(l_pSingleCamRight);
    l_pFloatFrontWheelRightView->setProjectionMatrix(
        l_pFrontWheelRightView->getProjectionMatrix() * l_viewportOffsetMatrixRight);
    l_pFloatFrontWheelRightView->addAsset(l_rimProtectionLineFront);


    // Floating rear  wheel left  view
    // **********************************************************************************************
    const auto l_pFloatRearWheelLeftView = new pc::core::View(
        "Float Rear Wheel Left View",
        getCorrectDriveHandSideViewport(g_views->m_floatWheelLeftViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rearWheelLeft);
    l_pFloatRearWheelLeftView->addAsset(l_pSingleCamLeft);
    l_pFloatRearWheelLeftView->setProjectionMatrix(
        l_pRearWheelLeftView->getProjectionMatrix() * l_mirrorTheWorld * l_viewportOffsetMatrixLeft);

    // Floating rear  wheel right view
    // **********************************************************************************************
    const auto l_pFloatRearWheelRightView = new pc::core::View(
        "Float Rear Wheel Right View",
        getCorrectDriveHandSideViewport(g_views->m_floatWheelRightViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_rearWheelRight);
    l_pFloatRearWheelRightView->addAsset(l_pSingleCamRight);
    // l_pRearWheelRightView->addUpdateCallback(new pc::factory::RenderManager(getFramework(),
    // virtcam::VCAM_REAR_WHEEL_RIGHT_VIEW));
    l_pFloatRearWheelRightView->setProjectionMatrix(
        l_pRearWheelRightView->getProjectionMatrix() * l_mirrorTheWorld * l_viewportOffsetMatrixRight);
    // Floating setting bar view
    const auto l_pFloatSettingBarView = new pc::core::View("Float Button View", g_views->m_floatSettingBarViewport);
    l_pFloatSettingBarView->addAsset(l_pViewInfoOverlay);
    const auto l_pFloatCloseButton =
        new cc::assets::button::CloseButton(cc::core::AssetId::EASSETS_FLOAT_CLOSE_BUTTON, getFramework(), l_pFloatSettingBarView);
    const auto l_pFloatEnlargeButton =
        new cc::assets::button::EnlargeButton(cc::core::AssetId::EASSETS_ENLARGE_BUTTON, getFramework(), l_pFloatSettingBarView);
    const auto l_pFloatSonarLoudSpeakerButton = new cc::assets::button::SonarLoudSpeakerButton(
        cc::core::AssetId::EASSETS_FLOAT_SONAR_SPEAKER_LOUD_BUTTON, getFramework(), l_pFloatSettingBarView);
    const auto l_pFloatViewChangeButtonGroup = new cc::assets::button::FloatViewChangeButtonGroup(
        cc::core::AssetId::EASSETS_FLOAT_VIEW_CHANGE_BUTTON_GROUP, getFramework(), l_pFloatSettingBarView);
    l_pFloatSettingBarView->addAsset(l_pFloatCloseButton);
    l_pFloatSettingBarView->addAsset(l_pFloatEnlargeButton);
    l_pFloatSettingBarView->addAsset(l_pFloatSonarLoudSpeakerButton);
    l_pFloatSettingBarView->addAsset(l_pFloatViewChangeButtonGroup);
    l_pFloatSettingBarView->addAsset(l_modOverlay);
    l_pFloatSettingBarView->addAsset(l_pSettingBar);

    // free parking manager asset
    auto const l_freeparkingManager = new pc::core::Asset(
        AssetId::EASSETS_FREEPARKING_OVERLAY, new cc::assets::freeparkingoverlay::FreeparkingManager(getFramework(),  &g_views->m_floatFreeParkingViewport));

    // Floating freeparking view **********************************************************************************************
    const auto l_pFloatFreeParkingView = new cc::views::planview::PlanView(
        "Floating FreeParking View",
        getCorrectDriveHandSideViewport(g_views->m_floatFreeParkingViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());
    l_pFloatFreeParkingView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori / 2., l_total_width_meters_parking_hori / 2., -l_total_length_meters_parking_hori / 2., l_total_length_meters_parking_hori / 2.);
    const auto l_renderManagerFloatFreeParkingView =
        new pc::factory::RenderManager(l_pRenderManagerRegistry, virtcam::VCAM_PLAN_VIEW,
                                        rbp::vis::imp::chamaeleon::EChamaeleonView::DEFAULT,
                                        rbp::vis::imp::sh::ESharpnessView::FIXED,
                                        rbp::vis::imp::tnf::ETnfView::DEFAULT);
    l_pRenderManagerRegistry->setDefaultRenderManager(l_renderManagerFloatFreeParkingView);
    l_renderManagerFloatFreeParkingView->assignCamera(l_pFloatFreeParkingView);
    l_pFloatFreeParkingView->addAsset(l_pFloor);
    l_pFloatFreeParkingView->addAsset(l_pFloorPlate);
    l_pFloatFreeParkingView->addAsset(l_pDistanceOverlayFreeparking);
    l_pDistanceOverlayFreeparking->setReferenceView(l_pFloatFreeParkingView);
    // auto l_freeparkingOverlay = new pc::core::Asset(
    //         AssetId::EASSETS_FREEPARKING_OVERLAY,
    //         new cc::assets::freeparking::FreeParkingOverlay(l_pFloatFreeParkingView, getFramework()));
    l_pFloatFreeParkingView->addAsset(l_freeparkingManager);
    // Floating vehicle 2d
    // **********************************************************************************************

    const auto l_pFloatFreeParkingViewVehicle2D = new cc::views::planview::PlanView(
        "Floating FreeParking View Vehicle2D",
        getCorrectDriveHandSideViewport(g_views->m_floatFreeParkingViewport, g_views->m_usableCanvasViewport),
        virtcam::g_positions->m_plan,
        getFramework());
    l_pFloatFreeParkingViewVehicle2D->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters_parking_hori_vehicle2d / 2.,
        l_total_width_meters_parking_hori_vehicle2d / 2.,
        -l_total_length_meters_parking_hori_vehicle2d / 2.,
        l_total_length_meters_parking_hori_vehicle2d / 2.);
    l_pFloatFreeParkingViewVehicle2D->addAsset(l_pOutermostLines);
    // l_pFloatFreeParkingViewVehicle2D->addAsset(new cc::assets::uielements::FreeParkingVehicleTransIconOverlay(getFramework(), AssetId::EASSETS_FREEPARKING_VEHICLE_TRANS_ICON_OVERLAY, l_pFloatFreeParkingViewVehicle2D));
    l_pFloatFreeParkingViewVehicle2D->addAsset(l_pVehicle2D);
    l_pFloatFreeParkingViewVehicle2D->addAsset(l_pVehicle2DWheels);

#endif // ENABLE_FLOATING_MODE
    pc::views::warpfisheye::EquidistantModel* const l_frontEquiDist = new pc::views::warpfisheye::EquidistantModel(l_modFrontSettingsModel.data());
    pc::views::warpfisheye::EquidistantModel* const l_rearEquiDist = new pc::views::warpfisheye::EquidistantModel(l_modRearSettingsModel.data());
    pc::views::warpfisheye::EquidistantModel* const l_leftEquiDist = new pc::views::warpfisheye::EquidistantModel(l_modLeftSettingsModel.data());
    pc::views::warpfisheye::EquidistantModel* const l_rightEquiDist = new pc::views::warpfisheye::EquidistantModel(l_modRightSettingsModel.data());

    const auto l_modViewFront = new cc::mod::ModFisheyeView("MOD VIEW - FRONT", g_views->m_viewForMod, getFramework(),
        pc::core::sysconf::FRONT_CAMERA, l_frontEquiDist, l_modFrontSettings.get(), l_modFrontSettingsCrop.get());

    const auto l_modViewLeft = new cc::mod::ModFisheyeView("MOD VIEW - LEFT", g_views->m_viewForMod, getFramework(),
        pc::core::sysconf::LEFT_CAMERA, l_leftEquiDist, l_modLeftSettings.get(), l_modLeftSettingsCrop.get());

    const auto l_modViewRear = new cc::mod::ModFisheyeView("MOD VIEW - REAR", g_views->m_viewForMod, getFramework(),
        pc::core::sysconf::REAR_CAMERA, l_rearEquiDist, l_modRearSettings.get(), l_modRearSettingsCrop.get());

    const auto l_modViewRight = new cc::mod::ModFisheyeView("MOD VIEW - RIGHT", g_views->m_viewForMod, getFramework(),
        pc::core::sysconf::RIGHT_CAMERA, l_rightEquiDist, l_modRightSettings.get(), l_modRightSettingsCrop.get());

    // const auto l_miniVehicle2D = new pc::core::Asset( AssetId::EASSETS_VEHICLE_2D_OVERLAY,
    //     new assets::uielements::Vehicle2DOverlay(getFramework()->asCustomFramework(),
    //     AssetId::EASSETS_VEHICLE_2D_OVERLAY));

    const auto l_pMiniUssView = new pc::core::View("Mini Uss View", g_views->m_miniUssViewport, virtcam::g_positions->m_plan);
    l_pMiniUssView->setProjectionMatrixAsOrtho2D(
        -l_total_width_meters / 2., l_total_width_meters / 2., -l_total_length_meters / 2., l_total_length_meters / 2.);
    l_pMiniUssView->addAsset(l_pDistanceOverlayMini);
    l_pDistanceOverlayMini->setReferenceView(l_pMiniUssView);
    l_pMiniUssView->addAsset(l_pVehicle2D);
    // //! FocusView
    // auto l_touchFocusView = new cc::views::touchfocus::TouchFocusView{"Touch Focus View", g_views->m_usableCanvasViewport, getFramework()->asCustomFramework()};
    // cc::assets::button::Button::setTouchFocusView(l_touchFocusView);
    // l_touchFocusView->setRenderOrder(osg::Camera::POST_RENDER, std::numeric_limits<int>::max());

    // insert the Views in the Scene
    // *************************************************************************************
    addView(CustomViews::PLAN_VIEW, l_pPlanView);
    addView(CustomViews::PLAN_VIEW_USS_OVERLAYS, l_pPlanViewUSSOverlay);
    addView(CustomViews::PLAN_VIEW_VEHICLE2D, l_pPlanViewVehicle2D);
    addView(CustomViews::FRONT_VIEW, l_pFrontView);
    addView(CustomViews::REAR_VIEW, l_pRearView);
    addView(CustomViews::REAR_JUNCTION_VIEW, l_pRearJunctionView);
    addView(CustomViews::FRONT_JUNCTION_VIEW, l_pFrontJunctionView);
    addView(CustomViews::BONNET_VIEW, l_pBonnetView);
    addView(CustomViews::SURROUND_VIEW, l_pSurroundView);
    addView(CustomViews::ENGINEERING_VIEW, l_pEngineeringView, false); // set true for debugging.
    addView(CustomViews::CALIB_ENGINEERING_VIEW, l_pEnginneringViewCalib);
#if USE_IN_NISSAN
    addView(CustomViews::LSMGLSAEB_IMPOSTORPLAN_VIEW, l_pImpostorPlanView);
    addView(CustomViews::PROFILING_VIEW, l_pProfilingView);
    addView(CustomViews::DAY_NIGHT_VIEW, l_pDayNightView);
#endif
    addView(CustomViews::RAW_FISHEYE_LEFT, l_pRawFisheyeLeft);
    addView(CustomViews::RAW_FISHEYE_RIGHT, l_pRawFisheyeRight);
    addView(CustomViews::RAW_FISHEYE_FRONT, l_pRawFisheyeFront);
    addView(CustomViews::RAW_FISHEYE_REAR, l_pRawFisheyeRear);
    //   addView(CustomViews::RAW_FISHEYE_QUAD, l_pRawFisheyeQuadCalibration);
    addView(CustomViews::SINGLE_LEFT_VIEW, l_pLeftView);
    addView(CustomViews::SINGLE_RIGHT_VIEW, l_pRightView);
    addView(CustomViews::FRONT_WHEEL_LEFT_VIEW, l_pFrontWheelLeftView);
    addView(CustomViews::FRONT_WHEEL_RIGHT_VIEW, l_pFrontWheelRightView);
    addView(CustomViews::REAR_WHEEL_LEFT_VIEW, l_pRearWheelLeftView);
    addView(CustomViews::REAR_WHEEL_RIGHT_VIEW, l_pRearWheelRightView);
    addView(CustomViews::ECUSTOMVIEW_MOD_VIEW_FRONT,      l_modViewFront);
    addView(CustomViews::ECUSTOMVIEW_MOD_VIEW_RIGHT,      l_modViewRight);
    addView(CustomViews::ECUSTOMVIEW_MOD_VIEW_REAR,       l_modViewRear);
    addView(CustomViews::ECUSTOMVIEW_MOD_VIEW_LEFT,       l_modViewLeft);
    addView(CustomViews::MINI_USS_VIEW, l_pMiniUssView);
#if ENABLE_VERTICAL_MODE
    addView(CustomViews::VERTICAL_PLAN_VIEW, l_pVertPlanView);
    addView(CustomViews::VERTICAL_PLAN_VIEW_VEHICLE2D, l_pVertPlanViewVehicle2D);
    addView(CustomViews::VERTICAL_FRONT_VIEW, l_pVertFrontView);
    addView(CustomViews::VERTICAL_REAR_VIEW, l_pVertRearView);
    addView(CustomViews::VERTICAL_LEFT_VIEW, l_pVertLeftView);
    addView(CustomViews::VERTICAL_RIGHT_VIEW, l_pVertRightView);
    addView(CustomViews::VERTICAL_WHEEL_LEFT_VIEW, l_pVertFrontWheelLeftView);
    addView(CustomViews::VERTICAL_WHEEL_RIGHT_VIEW, l_pVertFrontWheelRightView);
    addView(CustomViews::VERTICAL_FRONT_JUNCTION_VIEW, l_pVertFrontJunctionView);
    addView(CustomViews::VERTICAL_REAR_JUNCTION_VIEW, l_pVertRearJunctionView);
    addView(CustomViews::VERTICAL_SURROUND_VIEW, l_pVertSurroundView);
    addView(CustomViews::WHEEL_SEPARATOR_VERTICAL_VIEW, l_pWheelSeparatorVerticalView);
#endif
    addView(CustomViews::WHEEL_SEPARATOR_HORIZONTAL_VIEW, l_pWheelSeparatorHorizontalView);
    addView(CustomViews::CPC_DEBUG_OVERLAY_VIEW, l_CPCCpcDebugScreen);
    //   addView(CustomViews::HORI_PARKING_PLAN_VIEW,          l_pHoriParkingPlanView);
    //   addView(CustomViews::HORI_PARKING_FLOOR_PLAN_VIEW,    l_pHoriParkingFloorPlanView);
#if ENABLE_VERTICAL_MODE
    addView(CustomViews::VERT_PARKING_PLAN_VIEW, l_pVertParkingPlanView);
    addView(CustomViews::VERT_PARKING_FLOOR_PLAN_VIEW, l_pVertParkingFloorPlanView);
#endif
#if USE_PARKING_VIEW
    addView(CustomViews::PARKING_VIEW, l_pParkingView);
    addView(CustomViews::PARK_SEARCHING_VIEW, l_pParkSearchView);
    addView(CustomViews::PARK_DYNAMIC_GEAR_VIEW, l_dynamicGearView);
#endif
    addView(CustomViews::SETTING_BAR_VIEW, l_pSettingBarView);
    // addView(CustomViews::ECUSTOMVIEW_MOD_OVERLAY_VIEW,    l_modView);
    addView(CustomViews::BUTTONS_VIEW, l_pButtonsView);
#if ENABLE_FLOATING_MODE
    addView(CustomViews::FlOAT_PLAN_VIEW, l_pFloatPlanView);
    addView(CustomViews::FLOAT_PLAN_VIEW_VEHICLE2D, l_pFloatPlanViewVehicle2D);
    addView(CustomViews::FlOAT_FRONT_VIEW, l_pFloatFrontView);
    addView(CustomViews::FlOAT_REAR_VIEW, l_pFloatRearView);
    addView(CustomViews::FlOAT_FRONT_WHEEL_LEFT_VIEW, l_pFloatFrontWheelLeftView);
    addView(CustomViews::FlOAT_FRONT_WHEEL_RIGHT_VIEW, l_pFloatFrontWheelRightView);
    addView(CustomViews::FlOAT_REAR_WHEEL_LEFT_VIEW, l_pFloatRearWheelLeftView);
    addView(CustomViews::FlOAT_REAR_WHEEL_RIGHT_VIEW, l_pFloatRearWheelRightView);
    addView(CustomViews::FLOAT_FREE_PARKING_VIEW, l_pFloatFreeParkingView);
    addView(CustomViews::FLOAT_FREE_PARKING_VIEW_VEHICLE2D, l_pFloatFreeParkingViewVehicle2D);
    addView(CustomViews::FLOAT_SETTING_BAR_VIEW, l_pFloatSettingBarView);
#endif // ENABLE_FLOATING_MODE
    // addView(CustomViews::FOCUS_VIEW, l_touchFocusView, true);
#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
    if (m_enableImgui)
    {
        auto imguiView = new cc::views::imgui::ImGuiView(
            "ImGui View",
            g_views->m_usableCanvasViewport,
            virtcam::g_positions->getPosition(virtcam::VCAM_FRONT_VIEW),
            getFramework());
        addView(CustomViews::IMGUI_VIEW, imguiView);
    }
#endif

    // pf code. #code looks fine
    XLOG_INFO(g_EngineContext, "End of Custom Scene initialization");
}

#if USE_FISHEYE_VIEWS
static osg::Group* createFisheyeWheelTracks(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::WheelTrack* const l_left_WheelTrack_Whole = new assets::fisheyeassets::FisheyeWheelTrack(
        f_framework,
        cc::assets::trajectory::commontypes::Left_enm,
        g_height,
        assets::trajectory::g_trajParams,
        g_pDL1,
        g_numVerticesWheelTracks,
        assets::trajectory::WheelTrack::WHOLE,
        f_cam,
        f_model,
        f_settings);
    l_left_WheelTrack_Whole->setName("WheelTrack Left");

    assets::trajectory::WheelTrack* const l_right_WheelTrack_Whole = new assets::fisheyeassets::FisheyeWheelTrack(
        f_framework,
        cc::assets::trajectory::commontypes::Right_enm,
        g_height,
        assets::trajectory::g_trajParams,
        g_pDL1,
        g_numVerticesWheelTracks,
        assets::trajectory::WheelTrack::WHOLE,
        f_cam,
        f_model,
        f_settings);
    l_right_WheelTrack_Whole->setName("WheelTrack Right");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_WheelTrack_Whole);  // PRQA S 3803
    l_group->addChild(l_right_WheelTrack_Whole); // PRQA S 3803

    l_group->setName("FisheyeWheelTracks");

    return l_group;
}

// static osg::Group* createFisheyeTrailerAssistLines(
//   cc::core::CustomFramework* f_framework,
//   pc::core::sysconf::Cameras f_cam,
//   pc::views::warpfisheye::FisheyeModel* f_model,
//   pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings )
// {
//   assets::trajectory::TrailerAssistLine* l_left_TrailerAssistLine = new
//   assets::fisheyeassets::FisheyeTrailerAssistLine(
//       f_framework, g_height, assets::trajectory::g_trajParams,
//       g_numVerticesTrailerAssistLines,
//       f_cam,
//       f_model,
//       f_settings);
//   l_left_TrailerAssistLine->setName("TrailerAssistLine");

//   osg::Group* l_group = new osg::Group;
//   l_group->addChild(l_left_TrailerAssistLine);    // PRQA S 3803
//   l_group->setName("FisheyeTrailerAssistLines");

//   return l_group;
// }

static osg::Group* createFisheyeOutermostLines(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::OutermostLine* const l_left_OutermostLine = new assets::fisheyeassets::FisheyeOutermostLine(
        f_framework,
        cc::assets::trajectory::commontypes::Left_enm,
        g_height,
        assets::trajectory::g_trajParams,
        assets::trajectory::g_DIDescriptor,
        22u,
        4u,
        25u,
        f_cam,
        f_model,
        f_settings);
    l_left_OutermostLine->setName("OutermostLineLeft");

    assets::trajectory::OutermostLine* const l_right_OutermostLine = new assets::fisheyeassets::FisheyeOutermostLine(
        f_framework,
        cc::assets::trajectory::commontypes::Right_enm,
        g_height,
        assets::trajectory::g_trajParams,
        assets::trajectory::g_DIDescriptor,
        22u,
        4u,
        25u,
        f_cam,
        f_model,
        f_settings);
    l_right_OutermostLine->setName("OutermostLineRight");

    g_leftOutermostLineFish = l_left_OutermostLine;
    g_rightOutermostLineFish = l_right_OutermostLine;

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_OutermostLine);  // PRQA S 3803
    l_group->addChild(l_right_OutermostLine); // PRQA S 3803

    l_group->setName("FisheyeOutmostLines");

    return l_group;
}

static osg::Group* createFisheyeOutermostLinesColorful(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::OutermostLineColorful* const l_left_OutermostLine =
        new assets::fisheyeassets::FisheyeOutermostLineColorful(
            f_framework,
            cc::assets::trajectory::commontypes::Left_enm,
            g_height,
            assets::trajectory::g_trajParams,
            assets::trajectory::g_DIColorfulDescriptor,
            22u,
            4u,
            25u,
            f_cam,
            f_model,
            f_settings);
    l_left_OutermostLine->setName("OutermostLineLeftColorful");

    assets::trajectory::OutermostLineColorful* const l_right_OutermostLine =
        new assets::fisheyeassets::FisheyeOutermostLineColorful(
            f_framework,
            cc::assets::trajectory::commontypes::Right_enm,
            g_height,
            assets::trajectory::g_trajParams,
            assets::trajectory::g_DIColorfulDescriptor,
            22u,
            4u,
            25u,
            f_cam,
            f_model,
            f_settings);
    l_right_OutermostLine->setName("OutermostLineRightColorful");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_left_OutermostLine);  // PRQA S 3803
    l_group->addChild(l_right_OutermostLine); // PRQA S 3803

    l_group->setName("FisheyeOutmostLinesColorful");

    return l_group;
}

static osg::Group* createFisheyeCoverPlate(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::CoverPlate* const l_fisheye_CoverPlate = new assets::fisheyeassets::FisheyeCoverPlate(
        f_framework,
        g_height + 0.001f,
        assets::trajectory::g_trajParams,
        g_numVerticesCoverPlate,
        f_cam,
        f_model,
        f_settings);
    l_fisheye_CoverPlate->setName("CoverPlate");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_CoverPlate); // PRQA S 3803
    l_group->setName("FisheyeCoverPlate");

    return l_group;
}

static osg::Group* createFisheyeDL1(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::DL1* const l_fisheye_DL1 = new assets::fisheyeassets::FisheyeDL1(
        f_framework,
        g_height + 0.003f,
        assets::trajectory::g_trajParams,
        g_leftOutermostLineFish,
        g_rightOutermostLineFish,
        g_numLayoutPointsDL1,
        f_cam,
        f_model,
        f_settings);
    l_fisheye_DL1->setName("DistanceLine");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_DL1); // PRQA S 3803
    l_group->setName("FisheyeDistanceLine");

    return l_group;
}

static osg::Group* createFisheyeDL1Colorful(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::trajectory::DL1* const l_fisheye_DL1 = new assets::fisheyeassets::FisheyeDL1(
        f_framework,
        g_height + 0.003f,
        assets::trajectory::g_trajParams,
        g_leftOutermostLine,
        g_rightOutermostLine,
        g_numLayoutPointsDL1,
        f_cam,
        f_model,
        f_settings);
    l_fisheye_DL1->setName("DistanceLineColorful");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_DL1); // PRQA S 3803
    l_group->setName("FisheyeDistanceLineColorful");

    return l_group;
}

static osg::Group* createFisheyeRctaOverlay(
    cc::core::CustomFramework*                                           f_framework,
    pc::core::sysconf::Cameras                                           f_cam,
    pc::views::warpfisheye::FisheyeModel*                                f_model,
    pc::util::coding::Item<pc::views::warpfisheye::FisheyeViewSettings>& f_settings)
{
    assets::rctaoverlay::RctaOverlay* const l_fisheye_RctaOverlay =
        new assets::fisheyeassets::FisheyeRctaOverlay(f_framework, f_cam, f_model, f_settings);
    l_fisheye_RctaOverlay->setName("RctaOverlay");

    osg::Group* const l_group = new osg::Group;
    l_group->addChild(l_fisheye_RctaOverlay); // PRQA S 3803
    l_group->setName("FisheyeRctaOverlay");

    return l_group;
}

#endif

pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator* CustomScene::createSuperEllipseLayoutGenerator()
{
    return new pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator(
        pc::worker::bowlshaping::g_bowlShaperDefault->m_numRadialSections,
        pc::vehicle::g_mechanicalData->getCenter(),
        pc::worker::bowlshaping::g_bowlShaperDefault->m_bowlDefault.m_semiaxis.x(),
        pc::worker::bowlshaping::g_bowlShaperDefault->m_bowlDefault.m_semiaxis.y(),
        static_cast<int>(pc::worker::bowlshaping::g_bowlShaperDefault->m_bowlDefault.m_n));
}

pc::core::Asset* CustomScene::getBowlAsset() const
{
    return m_pBowlAsset;
}

pc::core::Asset* CustomScene::getFloorAsset() const
{
    return m_pFloorAsset;
}

void CustomScene::initTrajectoryParams(
    assets::trajectory::TrajectoryParams_st& f_trajParams,
    assets::trajectory::DIDescriptor_st&     f_DIDescriptor,
    const bool                               f_Is_Colorful)
{
    const cc::assets::trajectory::TrajectoryCodingParams& l_trajCodingParams =
        cc::assets::trajectory::g_trajCodingParams.data();

    f_trajParams.OutermostLine_Width                 = l_trajCodingParams.m_outermostLine_Width;
    f_trajParams.OutermostLine_Color_Manual          = l_trajCodingParams.m_outermostLine_Color_Manual;
    f_trajParams.OutermostLine_Color_Auto            = l_trajCodingParams.m_outermostLine_Color_Auto;
    f_trajParams.OutermostLine_Colorful_Color_1      = l_trajCodingParams.m_outermostLine_Colorful_Color_1;
    f_trajParams.OutermostLine_Colorful_Color_2      = l_trajCodingParams.m_outermostLine_Colorful_Color_2;
    f_trajParams.OutermostLine_Colorful_Color_3      = l_trajCodingParams.m_outermostLine_Colorful_Color_3;
    f_trajParams.OL_WT_minGap                        = l_trajCodingParams.m_outermostLine_OL_WT_minGap;
    f_trajParams.OutermostLineColoful_DI_MagicOffset = l_trajCodingParams.m_outermostLineColoful_DI_MagicOffset;

    f_trajParams.WheelTrack_Width_Whole                 = l_trajCodingParams.m_wheelTrack_Width_Whole;
    f_trajParams.WheelTrack_Width_BorderLine            = l_trajCodingParams.m_wheelTrack_Width_BorderLine;
    f_trajParams.WheelTrack_Color_Manual_Inside         = l_trajCodingParams.m_wheelTrack_Color_Manual_Inside;
    f_trajParams.WheelTrack_Color_Manual_BorderLine     = l_trajCodingParams.m_wheelTrack_Color_Manual_BorderLine;
    f_trajParams.WheelTrack_Color_Auto_Close_Inside     = l_trajCodingParams.m_wheelTrack_Color_Auto_Close_Inside;
    f_trajParams.WheelTrack_Color_Auto_Close_BorderLine = l_trajCodingParams.m_wheelTrack_Color_Auto_Close_BorderLine;
    f_trajParams.WheelTrack_Color_Auto_Far_Inside       = l_trajCodingParams.m_wheelTrack_Color_Auto_Far_Inside;
    f_trajParams.WheelTrack_Color_Auto_Far_BorderLine   = l_trajCodingParams.m_wheelTrack_Color_Auto_Far_BorderLine;

    f_trajParams.ParkingTraj_Width_Whole      = l_trajCodingParams.m_parkingTraj_Width_Whole;
    f_trajParams.ParkingTraj_Width_Shadow     = l_trajCodingParams.m_parkingTraj_Width_Shadow;
    f_trajParams.ParkingTraj_Width_BorderLine = l_trajCodingParams.m_parkingTraj_Width_BorderLine;
    f_trajParams.ParkingTraj_Color_Inside     = l_trajCodingParams.m_parkingTraj_Color_Inside;
    f_trajParams.ParkingTraj_Color_BorderLine = l_trajCodingParams.m_parkingTraj_Color_BorderLine;

    f_trajParams.ActionPoint_Length = l_trajCodingParams.m_actionPoint_Length;
    f_trajParams.ActionPoint_Color  = l_trajCodingParams.m_actionPoint_Color;

    f_trajParams.THTraj_Width                 = l_trajCodingParams.m_THTraj_Width;
    f_trajParams.THTraj_Length                = l_trajCodingParams.m_THTraj_Length;
    f_trajParams.THTraj_Color                 = l_trajCodingParams.m_THTraj_Color;
    f_trajParams.THTrajBorder_Color           = l_trajCodingParams.m_THTrajBorder_Color;
    f_trajParams.THTraj_ColorGradientPosRatio = l_trajCodingParams.m_THTraj_ColorGradientPosRatio;

    f_trajParams.DL1_Width        = l_trajCodingParams.m_DL1_Width;
    f_trajParams.DL1_Offset_Front = l_trajCodingParams.m_DL1_Offset_Front;
    f_trajParams.DL1_Offset_Rear  = l_trajCodingParams.m_DL1_Offset_Rear;
    f_trajParams.DL1_Color        = l_trajCodingParams.m_DL1_Color;

    f_trajParams.Length =
        l_trajCodingParams.m_length; // The length of the normal trajectory lines (not TH, it is separate).
    f_trajParams.GradientWidth = l_trajCodingParams.m_gradientWidth; // Gradient width of all trajectory lines.
    f_trajParams.RenderOffset_Front =
        l_trajCodingParams.m_renderOffset_Front; // Offset that will be added at the rendering stage so the actionPoints
                                                 // nearPosition is touching the distanceLine
    f_trajParams.RenderOffset_Rear = l_trajCodingParams.m_renderOffset_Rear;

    f_trajParams.AnimDurRemainingDistance = l_trajCodingParams.m_animDurRemainingDistance;

    f_DIDescriptor.DILength = l_trajCodingParams.m_DIs.m_DIlength;
    if (f_Is_Colorful)
    {
        f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(
            l_trajCodingParams.m_DIs.m_DI1_Pos, l_trajCodingParams.m_DIs.m_DI1_Thickness));
        f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(
            l_trajCodingParams.m_DIs.m_DI2_Pos, l_trajCodingParams.m_DIs.m_DI2_Thickness));
        // f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI3_Pos,
        // l_trajCodingParams.m_DIs.m_DI3_Thickness));
        // f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI4_Pos,
        // l_trajCodingParams.m_DIs.m_DI4_Thickness));
    }
}

void CustomScene::initOutermostLineColofulParams(assets::trajectory::DIDescriptor_st& f_DIDescriptor)
{
    const cc::assets::trajectory::TrajectoryCodingParams& l_trajCodingParams =
        cc::assets::trajectory::g_trajCodingParams.data();

    f_DIDescriptor.DILength = l_trajCodingParams.m_DIs.m_DIlength;

    f_DIDescriptor.DIs.push_back(
        cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI1_Pos, l_trajCodingParams.m_DIs.m_DI1_Thickness));
    f_DIDescriptor.DIs.push_back(
        cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI2_Pos, l_trajCodingParams.m_DIs.m_DI2_Thickness));
    f_DIDescriptor.DIs.push_back(
        cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI3_Pos, l_trajCodingParams.m_DIs.m_DI3_Thickness));
    // f_DIDescriptor.DIs.push_back(
    //     cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI4_Pos, l_trajCodingParams.m_DIs.m_DI4_Thickness));
}

} // namespace core
} // namespace cc
