//
// File: ViewModexViewStateMachine_R2015b_types.h
//
// Code generated for Simulink model 'ViewModexViewStateMachine_R2015b'.
//
// Model version                  : 11.419
// Simulink Coder version         : 9.6 (R2021b) 14-May-2021
// C/C++ source code generated on : Fri Aug  1 18:05:47 2025
//
// Target selection: ert.tlc
// Embedded hardware selection: ARM Compatible->ARM Cortex
// Code generation objectives:
//    1. Execution efficiency
//    2. RAM efficiency
//    3. ROM efficiency
//    4. MISRA C:2012 guidelines
//    5. Debugging
//    6. Safety precaution
// Validation result: Passed (29), Warnings (4), Error (0)
//
#ifndef RTW_HEADER_ViewModexViewStateMachine_R2015b_types_h_
#define RTW_HEADER_ViewModexViewStateMachine_R2015b_types_h_
#include "rtwtypes.h"

// Model Code Variants
#ifndef DEFINED_TYPEDEF_FOR_EScreenID_
#define DEFINED_TYPEDEF_FOR_EScreenID_

typedef enum
{
  EScreenID_NO_CHANGE = 0,             // Default value
  EScreenID_NO_VIDEO_USER,
  EScreenID_LSMG,
  EScreenID_CONTEXT_ON_ROAD,
  EScreenID_CONTEXT_OFF_ROAD,
  EScreenID_CONTEXT_TOWING,
  EScreenID_CONTEXT_JAPANESE,
  EScreenID_CONTEXT_THREAT,
  EScreenID_SINGLE_FRONT_NORMAL,
  EScreenID_SINGLE_STB,
  EScreenID_SINGLE_FRONT_JUNCTION,
  EScreenID_SINGLE_REAR_NORMAL_OFF_ROAD,
  EScreenID_PARK_ASSIST_FRONT,
  EScreenID_SINGLE_FRONT_RAW,
  EScreenID_SINGLE_REAR_NORMAL_ON_ROAD,
  EScreenID_SINGLE_REAR_JUNCTION,
  EScreenID_SINGLE_REAR_RAW_DIAG,
  EScreenID_SINGLE_REAR_HITCH,
  EScreenID_SINGLE_REAR_HITCH_ZOOM,
  EScreenID_PARK_ASSIST_REAR,
  EScreenID_SINGLE_REAR_TRAILER,
  EScreenID_PERSPECTIVE_KL,
  EScreenID_PARK_ASSIST_FRONT_JAP,
  EScreenID_PARK_ASSIST_REAR_JAP,
  EScreenID_SINGLE_ML_RAW,
  EScreenID_PERSPECTIVE_KR,
  EScreenID_SINGLE_LEFT,
  EScreenID_SINGLE_RIGHT,
  EScreenID_SINGLE_MR_RAW,
  EScreenID_THREAT_FRONT,
  EScreenID_THREAT_REAR,
  EScreenID_WHEEL_FRONT_DUAL,
  EScreenID_PERSPECTIVE_FL,
  EScreenID_PERSPECTIVE_FR,
  EScreenID_PERSPECTIVE_RL,
  EScreenID_PERSPECTIVE_RR,
  EScreenID_DUAL_FRONT_ML,
  EScreenID_DUAL_FRONT_MR,
  EScreenID_PERSPECTIVE_PRE,
  EScreenID_PERSPECTIVE_PLE,
  EScreenID_DUAL_FRONT_JAP,
  EScreenID_CAM_CALIB_ENG,
  EScreenID_DUAL_REAR_JAP,
  EScreenID_TOW_ASSIST_ENG,
  EScreenID_LSM_ENG,
  EScreenID_WHEEL_REAR_DUAL,
  EScreenID_LSMG_LSAEB_ENG,
  EScreenID_FULL_SCREEN,
  EScreenID_TRIPLE_ML_FV_MR,
  EScreenID_TRIPLE_REAR_ML_MR_TOW_ASSIST,
  EScreenID_QUAD_RAW,
  EScreenID_NO_VIDEO_SYSTEM,
  EScreenID_PERSPECTIVE_PFR,
  EScreenID_PERSPECTIVE_PRI,
  EScreenID_BACK,
  EScreenID_PRK_MODE_SELECT,
  EScreenID_PRK_SEARCHING,
  EScreenID_PRK_CONFIRMING,
  EScreenID_VERT_SINGLE_FRONT_LEFT,
  EScreenID_VERT_SINGLE_FRONT_RIGHT,
  EScreenID_VERT_SINGLE_REAR_LEFT,
  EScreenID_VERT_SINGLE_REAR_RIGHT,
  EScreenID_VERT_SINGLE_FRONT_JUNCTION_LEFT,
  EScreenID_VERT_SINGLE_FRONT_JUNCTION_RIGHT,
  EScreenID_VERT_SINGLE_REAR_JUNCTION_LEFT,
  EScreenID_VERT_SINGLE_REAR_JUNCTION_RIGHT,
  EScreenID_VERT_WHEEL_FRONT_DUAL_LEFT,
  EScreenID_VERT_WHEEL_FRONT_DUAL_RIGHT,
  EScreenID_VERT_PERSPECTIVE_PFR_LEFT,
  EScreenID_VERT_PERSPECTIVE_PFR_RIGHT,
  EScreenID_VERT_PERSPECTIVE_PRE_LEFT,
  EScreenID_VERT_PERSPECTIVE_PRE_RIGHT,
  EScreenID_VERT_PERSPECTIVE_RL_LEFT,
  EScreenID_VERT_PERSPECTIVE_RL_RIGHT,
  EScreenID_VERT_PERSPECTIVE_RR_LEFT,
  EScreenID_VERT_PERSPECTIVE_RR_RIGHT,
  EScreenID_VERT_PERSPECTIVE_FL_LEFT,
  EScreenID_VERT_PERSPECTIVE_FL_RIGHT,
  EScreenID_VERT_PERSPECTIVE_FR_LEFT,
  EScreenID_VERT_PERSPECTIVE_FR_RIGHT,
  EScreenID_HORI_PARKING,
  EScreenID_VERT_PARKING,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_FL,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_FR,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_RL,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_RR,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_PFR,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_PRI,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_PRE,
  EScreenID_PARK_COMPLETED_PERSPECTIVE_PLE,
  EScreenID_PLANVIEW_WITH_SEPARATOR,
  EScreenID_FLOAT_SINGLE_FRONT,
  EScreenID_FLOAT_SINGLE_REAR,
  EScreenID_FLOAT_WHEEL_FRONT_DUAL,
  EScreenID_FLOAT_WHEEL_REAR_DUAL,
  EScreenID_FLOAT_FRONT_PLAN_VIEW,
  EScreenID_FLOAT_FRONT_VIEW,
  EScreenID_FLOAT_REAR_VIEW,
  EScreenID_FLOAT_PARKING_REAR_VIEW,
  EScreenID_DEBUG,
  EScreenID_FLOAT_PARKING_FRONT_VIEW,
  EScreenID_FLOAT_REAR_PLAN_VIEW,
  EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW,
  EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW,
  EScreenID_FLOAT_FREE_PARKING_PLAN_VIEW
}
EScreenID;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EAnimationState_
#define DEFINED_TYPEDEF_FOR_EAnimationState_

typedef enum
{
  EAnimationState_ANIM_INIT = 0,       // Default value
  EAnimationState_ANIM_ONGOING,
  EAnimationState_ANIM_FINISHED
}
EAnimationState;

#endif

#ifndef DEFINED_TYPEDEF_FOR_AnimationState_
#define DEFINED_TYPEDEF_FOR_AnimationState_

struct AnimationState
{
  EScreenID screenID;
  EAnimationState state;
};

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuImageWorkMode_
#define DEFINED_TYPEDEF_FOR_EHuImageWorkMode_

typedef enum
{
  WORK_MODE_2DMODE = 0,                // Default value
  WORK_MODE_FULLSCREEN,
  WORK_MODE_RESERVED,
  WORK_MODE_SMALLWIDGET,
  WORK_MODE_REVERSE_FRONTRIGHT,
  WORK_MODE_REVERSE,
  WORK_MODE_3DMODE,
  WORK_MODE_RESERVED_OTHERS,
  WORK_MODE_INVALID
}
EHuImageWorkMode;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayModeSwitch_
#define DEFINED_TYPEDEF_FOR_EHuDisplayModeSwitch_

typedef enum
{
  DISPLAY_MODE_INVALID = 0,            // Default value
  DISPLAY_MODE_TURNOFF,
  DISPLAY_MODE_FRONTVIEW,
  DISPLAY_MODE_BACKVIEW,
  DISPLAY_MODE_LEFTVIEW,
  DISPLAY_MODE_RIGHTVIEW,
  DISPLAY_MODE_LOOKDOWN,
  DISPLAY_MODE_STARTCAL,
  DISPLAY_MODE_FRONTLEFT,
  DISPLAY_MODE_FRONTRIGHT,
  DISPLAY_MODE_BACKLEFT,
  DISPLAY_MODE_BACKRIGHT,
  DISPLAY_MODE_FRONTWIDE,
  DISPLAY_MODE_BACKWIDE,
  DISPLAY_MODE_WIDTHLIMIT,
  DISPLAY_MODE_RESERVED
}
EHuDisplayModeSwitch;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayModeExpand_
#define DEFINED_TYPEDEF_FOR_EHuDisplayModeExpand_

typedef enum
{
  DISPLAY_EXP_INVALID = 0,             // Default value
  DISPLAY_EXP_2DFRONTWIDELEFT,
  DISPLAY_EXP_2DFRONTWIDERIGHT,
  DISPLAY_EXP_2DREARWIDELEFT,
  DISPLAY_EXP_2DREARWIDERIGHT,
  DISPLAY_EXP_2DLIMITEDWIDTHLEFT,
  DISPLAY_EXP_2DLIMITEDWIDTHRIGHT,
  DISPLAY_EXP_RESERVED_1,
  DISPLAY_EXP_RESERVED_2,
  DISPLAY_EXP_3DLEFT_2DLEFT,
  DISPLAY_EXP_3DLEFT_2DRIGHT,
  DISPLAY_EXP_3DRIGHT_2DLEFT,
  DISPLAY_EXP_3DRIGHT_2DRIGHT,
  DISPLAY_EXP_LEFT_FRONT,
  DISPLAY_EXP_RIGHT_FRONT,
  DISPLAY_EXP_RESERVED_3
}
EHuDisplayModeExpand;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EHuDisplayModeExpandNew_
#define DEFINED_TYPEDEF_FOR_EHuDisplayModeExpandNew_

typedef enum
{
  DISPLAY_EXPNEW_INVALID = 0,          // Default value
  DISPLAY_EXPNEW_3DLEFTFRONT_2DLEFT,
  DISPLAY_EXPNEW_3DLEFTFRONT_2DRIGHT,
  DISPLAY_EXPNEW_3DRIGHTFRONT_2DLEFT,
  DISPLAY_EXPNEW_3DRIGHTFRONT_2DRIGHT,
  DISPLAY_DOUBLE_FRONT,
  DISPLAY_DOUBLE_REAR,
  DISPLAY_3D_BONNET_VIEW
}
EHuDisplayModeExpandNew;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESettingSts_
#define DEFINED_TYPEDEF_FOR_ESettingSts_

typedef enum
{
  ESettingSts_Set_OFF = 0,             // Default value
  ESettingSts_Set_ON
}
ESettingSts;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ECalibStatus_
#define DEFINED_TYPEDEF_FOR_ECalibStatus_

typedef enum
{
  ECalibStatus_CALIBSTATUS_NONE = 0,   // Default value
  ECalibStatus_CALIBSTATUS_IN_CALIBRATION,
  ECalibStatus_CALIBSTATUS_FINISH_SUCCESS,
  ECalibStatus_CALIBSTATUS_FINISH_FAIL
}
ECalibStatus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiArray_
#define DEFINED_TYPEDEF_FOR_HmiArray_

struct HmiArray
{
  uint16_T x;
  uint16_T y;
};

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiLayout_
#define DEFINED_TYPEDEF_FOR_HmiLayout_

struct HmiLayout
{
  HmiArray iconCenter;
  HmiArray responseArea;
};

#endif

#ifndef DEFINED_TYPEDEF_FOR_HmiUIElements_
#define DEFINED_TYPEDEF_FOR_HmiUIElements_

struct HmiUIElements
{
  HmiLayout CPCSwitchTopView;
  HmiLayout CPCSwitchFrontView;
};

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESystemStr_
#define DEFINED_TYPEDEF_FOR_ESystemStr_

typedef enum
{
  ESystemStr_SYSTEM_STR_NONE = 0,      // Default value
  ESystemStr_SYSTEM_STR_ENTER
}
ESystemStr;

#endif

#ifndef DEFINED_TYPEDEF_FOR_SonarDistLevel_
#define DEFINED_TYPEDEF_FOR_SonarDistLevel_

struct SonarDistLevel
{
  uint8_T DistFrontLeftSide;
  uint8_T DistFrontCenter;
  uint8_T DistFrontLeft;
  uint8_T DistFrontRight;
  uint8_T DistFrontRightSide;
  uint8_T DistRearLeftSide;
  uint8_T DistRearLeft;
  uint8_T DistRearCenter;
  uint8_T DistRearRight;
  uint8_T DistRearRightSide;
};

#endif

#ifndef DEFINED_TYPEDEF_FOR_ExitDelay_
#define DEFINED_TYPEDEF_FOR_ExitDelay_

struct ExitDelay
{
  uint32_T PassiveExitDelay;
  uint32_T ActiveExitDelay;
  uint32_T WarningExitDelay;
};

#endif

#ifndef DEFINED_TYPEDEF_FOR_EDockAvmButtonPress_
#define DEFINED_TYPEDEF_FOR_EDockAvmButtonPress_

typedef uint8_T EDockAvmButtonPress;

// enum EDockAvmButtonPress
const EDockAvmButtonPress EDockAvmButtonPress_NONE = 0U;// Default value
const EDockAvmButtonPress EDockAvmButtonPress_AVM_PRESS = 1U;
const EDockAvmButtonPress EDockAvmButtonPress_CLOSE = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EFloatViewType_
#define DEFINED_TYPEDEF_FOR_EFloatViewType_

typedef uint8_T EFloatViewType;

// enum EFloatViewType
const EFloatViewType EFloatViewType_None = 0U;// Default value
const EFloatViewType EFloatViewType_PlanView = 1U;
const EFloatViewType EFloatViewType_FRView = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESonarTrigLevel_
#define DEFINED_TYPEDEF_FOR_ESonarTrigLevel_

typedef enum
{
  ESonarTrigLevel_NONE = 0,            // Default value
  ESonarTrigLevel_CLOSE,
  ESonarTrigLevel_MIDDLE,
  ESonarTrigLevel_FAR
}
ESonarTrigLevel;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ECompeteActiveAllow_
#define DEFINED_TYPEDEF_FOR_ECompeteActiveAllow_

typedef uint8_T ECompeteActiveAllow;

// enum ECompeteActiveAllow
const ECompeteActiveAllow ECompeteActiveAllow_ACTIVE_NONE = 0U;// Default value
const ECompeteActiveAllow ECompeteActiveAllow_ACTIVE_ALLOW = 1U;
const ECompeteActiveAllow ECompeteActiveAllow_ACTIVE_NOT_ALLOW = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ETouchStatus_
#define DEFINED_TYPEDEF_FOR_ETouchStatus_

typedef enum
{
  ETouchStatus_NONE = 0,               // Default value
  ETouchStatus_Press,
  ETouchStatus_Release,
  ETouchStatus_Slither
}
ETouchStatus;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESVSViewMode_
#define DEFINED_TYPEDEF_FOR_ESVSViewMode_

typedef enum
{
  ESVSViewMode_VM_Default = 0,         // Default value
  ESVSViewMode_VM_Standard,
  ESVSViewMode_VM_Perspective,
  ESVSViewMode_VM_Wheel,
  ESVSViewMode_VM_Wide,
  ESVSViewMode_VM_STB,
  ESVSViewMode_VM_Floating
}
ESVSViewMode;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EGear_
#define DEFINED_TYPEDEF_FOR_EGear_

typedef uint8_T EGear;

// enum EGear
const EGear EGear_Init = 0U;           // Default value
const EGear EGear_Gear_1 = 1U;
const EGear EGear_Gear_2 = 2U;
const EGear EGear_Gear_3 = 3U;
const EGear EGear_Gear_4 = 4U;
const EGear EGear_Gear_5 = 5U;
const EGear EGear_Gear_6 = 6U;
const EGear EGear_Gear_7 = 7U;
const EGear EGear_Gear_8 = 8U;
const EGear EGear_Gear_9 = 9U;
const EGear EGear_N = 10U;
const EGear EGear_R = 11U;
const EGear EGear_P = 12U;
const EGear EGear_D = 13U;
const EGear EGear_Gear_Invalid = 14U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPowerMode_
#define DEFINED_TYPEDEF_FOR_EPowerMode_

typedef uint8_T EPowerMode;

// enum EPowerMode
const EPowerMode EPowerMode_KEY_OUT = 0U;// Default value
const EPowerMode EPowerMode_KEY_RECENTLY_OUT = 1U;
const EPowerMode EPowerMode_KEY_APPROVED = 2U;
const EPowerMode EPowerMode_POST_ACCESSORY = 3U;
const EPowerMode EPowerMode_ACCESSORY = 4U;
const EPowerMode EPowerMode_POST_IGNITION = 5U;
const EPowerMode EPowerMode_IGNITION_ON = 6U;
const EPowerMode EPowerMode_RUNNING = 7U;
const EPowerMode EPowerMode_CRANK = 8U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EShowReqMode_
#define DEFINED_TYPEDEF_FOR_EShowReqMode_

typedef enum
{
  SHOWREQ_NONE = 0,                    // Default value
  SHOWREQ_FULL_SCREEN,
  SHOWREQ_FLOAT_SCREEN_PARK,
  SHOWREQ_FLOAT_SCREEN_NOTPARK
}
EShowReqMode;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESRActiveSts_
#define DEFINED_TYPEDEF_FOR_ESRActiveSts_

typedef uint8_T ESRActiveSts;

// enum ESRActiveSts
const ESRActiveSts ESRActiveSts_NONE = 0U;// Default value
const ESRActiveSts ESRActiveSts_ACTIVE = 1U;
const ESRActiveSts ESRActiveSts_NOT_ACTIVE = 2U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EVoiceDockReq_
#define DEFINED_TYPEDEF_FOR_EVoiceDockReq_

typedef uint8_T EVoiceDockReq;

// enum EVoiceDockReq
const EVoiceDockReq EVoiceDockReq_NONE = 0U;// Default value
const EVoiceDockReq EVoiceDockReq_OPEN_FRONT = 1U;
const EVoiceDockReq EVoiceDockReq_OPEN_FRONTWHEEL = 2U;
const EVoiceDockReq EVoiceDockReq_OPEN_FRONTWIDE = 3U;
const EVoiceDockReq EVoiceDockReq_OPEN_SKELETON = 4U;
const EVoiceDockReq EVoiceDockReq_OPEN_3D = 5U;
const EVoiceDockReq EVoiceDockReq_OPEN_REAR = 6U;
const EVoiceDockReq EVoiceDockReq_OPEN_REARWIDE = 7U;
const EVoiceDockReq EVoiceDockReq_OPEN_REARWHEEL = 8U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ECompeteReqStatus_
#define DEFINED_TYPEDEF_FOR_ECompeteReqStatus_

typedef uint8_T ECompeteReqStatus;

// enum ECompeteReqStatus
const ECompeteReqStatus ECompeteReqStatus_COMPETE_REQ_NONE = 0U;// Default value 
const ECompeteReqStatus ECompeteReqStatus_COMPETE_REQ_STARTED = 1U;
const ECompeteReqStatus ECompeteReqStatus_COMPETE_REQ_SUCCEED = 2U;
const ECompeteReqStatus ECompeteReqStatus_COMPETE_REQ_TIMEOUT = 3U;
const ECompeteReqStatus ECompeteReqStatus_COMPETE_REFUSED = 4U;
const ECompeteReqStatus ECompeteReqStatus_COMPETE_IGNORE_REPONSE = 5U;
const ECompeteReqStatus ECompeteReqStatus_COMPETE_IGNORE_TIMEOUT = 6U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EViewModeGroup_
#define DEFINED_TYPEDEF_FOR_EViewModeGroup_

typedef enum
{
  VIEWMODE_NONE = 0,
  VIEWMODE_FRONT,                      // Default value
  VIEWMODE_REAR
}
EViewModeGroup;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESVSScreenType_
#define DEFINED_TYPEDEF_FOR_ESVSScreenType_

typedef enum
{
  ESVSScreenType_SCREEN_NONE = 0,
  ESVSScreenType_SCREEN_FULL_R,        // Default value
  ESVSScreenType_SCREEN_FULL_NOTR,
  ESVSScreenType_SCREEN_FLOAT_PARK,
  ESVSScreenType_SCREEN_FLOAT_NOTPARK,
  ESVSScreenType_SCREEN_CALIBRATION,
  ESVSScreenType_SCREEN_FLOAT_PLAN_R,
  ESVSScreenType_SCREEN_FLOAT_PLAN_NOTR,
  ESVSScreenType_SCREEN_FLOAT_FR_R,
  ESVSScreenType_SCREEN_FLOAT_FR_NOTR,
  ESVSScreenType_SCREEN_FLOAT_PLAN_PARK,
  ESVSScreenType_SCREEN_FLOAT_FR_PARK
}
ESVSScreenType;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ESystem_
#define DEFINED_TYPEDEF_FOR_ESystem_

typedef enum
{
  ESystem_Unavailable = 0,             // Default value
  ESystem_Available,
  ESystem_PartiallyAvailable
}
ESystem;

#endif

#ifndef DEFINED_TYPEDEF_FOR_ENotActiveReason_
#define DEFINED_TYPEDEF_FOR_ENotActiveReason_

typedef uint8_T ENotActiveReason;

// enum ENotActiveReason
const ENotActiveReason ENotActiveReason_NONE = 0U;// Default value
const ENotActiveReason ENotActiveReason_QNX_AVM_ERROR = 1U;
const ENotActiveReason ENotActiveReason_ADCU_IN_SLEEP = 2U;
const ENotActiveReason ENotActiveReason_SPEED_TOO_HIGH = 3U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EVoiceDockFb_
#define DEFINED_TYPEDEF_FOR_EVoiceDockFb_

typedef uint8_T EVoiceDockFb;

// enum EVoiceDockFb
const EVoiceDockFb EVoiceDockFb_NONE = 0U;// Default value
const EVoiceDockFb EVoiceDockFb_OPEN_SUCCESS = 1U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_NOTPN = 2U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_SPDHIGH = 3U;
const EVoiceDockFb EVoiceDockFb_OPEN_RESERVED = 4U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_OTHER = 5U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_CONFIGNOTSUPPORT = 6U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_AVM_ERROR = 7U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_POWER_SAVEMODE = 8U;
const EVoiceDockFb EVoiceDockFb_OPEN_FAIL_UNAVAILABLE_IN_SR = 9U;

#endif

#ifndef DEFINED_TYPEDEF_FOR_EPasWarnTone_
#define DEFINED_TYPEDEF_FOR_EPasWarnTone_

typedef enum
{
  EPasWarnTone_SOUND_OFF = 0,          // Default value
  EPasWarnTone_SOUND_LONG_BEEP,
  EPasWarnTone_SOUND_Fast,
  EPasWarnTone_SOUND_Medium,
  EPasWarnTone_SOUND_Slow,
  EPasWarnTone_SOUND_Mute
}
EPasWarnTone;

#endif

// Forward declaration for rtModel
typedef struct tag_RTM RT_MODEL;

#endif                  // RTW_HEADER_ViewModexViewStateMachine_R2015b_types_h_

//
// File trailer for generated code.
//
// [EOF]
//
