//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_MAINLOGIC_H
#define CC_ASSETS_TRAJECTORY_MAINLOGIC_H

#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
#include "cc/assets/trajectory/inc/Frame.h"

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/ref_ptr>
#include <osg/Geode>
#include <osg/ShapeDrawable>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace trajectory
{
class GeneralTrajectoryLine;


//!
//! Distance indicators
//!
class DistanceIndicatorParams : public pc::util::coding::ISerializable
{
public:
  DistanceIndicatorParams()
  : m_DIlength(0.055f)
  , m_DI1_Pos(0.5f)
  , m_DI1_Thickness(0.055f)
  , m_DI2_Pos(1.0f)
  , m_DI2_Thickness(0.055f)
  , m_DI3_Pos(1.5f)
  , m_DI3_Thickness(0.055f)
  , m_DI4_Pos(2.0f)
  , m_DI4_Thickness(0.055f)
  , m_DI5_Pos(2.5f)
  , m_DI5_Thickness(0.055f)
  {
  }

  SERIALIZABLE(DistanceIndicatorParams)
  {
    ADD_FLOAT_MEMBER(DIlength);
    ADD_FLOAT_MEMBER(DI1_Pos);
    ADD_FLOAT_MEMBER(DI1_Thickness);
    ADD_FLOAT_MEMBER(DI2_Pos);
    ADD_FLOAT_MEMBER(DI2_Thickness);
    ADD_FLOAT_MEMBER(DI3_Pos);
    ADD_FLOAT_MEMBER(DI3_Thickness);
    ADD_FLOAT_MEMBER(DI4_Pos);
    ADD_FLOAT_MEMBER(DI4_Thickness);
    ADD_FLOAT_MEMBER(DI5_Pos);
    ADD_FLOAT_MEMBER(DI5_Thickness);
  }

  vfc::float32_t m_DIlength;
  vfc::float32_t m_DI1_Pos;
  vfc::float32_t m_DI1_Thickness;
  vfc::float32_t m_DI2_Pos;
  vfc::float32_t m_DI2_Thickness;
  vfc::float32_t m_DI3_Pos;
  vfc::float32_t m_DI3_Thickness;
  vfc::float32_t m_DI4_Pos;
  vfc::float32_t m_DI4_Thickness;
  vfc::float32_t m_DI5_Pos;
  vfc::float32_t m_DI5_Thickness;
};


//!
//! Trajectory coding parameters
//!
class TrajectoryCodingParams : public pc::util::coding::ISerializable
{
public:
  TrajectoryCodingParams()
  : m_outermostLine_Width(0.055f)
  , m_outermostLine_Color_Manual(osg::Vec4f(1.0f, 0.75f, 0.0f, 0.9f))
  , m_outermostLine_Color_Auto(osg::Vec4f(0.0f, 0.9f, 0.0f, 0.9f))
  , m_outermostLine_Colorful_Color_1(osg::Vec4f(1.0f, 0.0f, 0.0f, 1.0f))
  , m_outermostLine_Colorful_Color_2(osg::Vec4f(1.0f, 1.0f, 0.0f, 1.0f))
  , m_outermostLine_Colorful_Color_3(osg::Vec4f(0.0f, 1.0f, 0.0f, 1.0f))
  , m_outermostLine_OL_WT_minGap(0.01f)
  , m_outermostLineColoful_DI_MagicOffset(0.014f)
  , m_outermostLine_rear_point_offset(0.15f)
  , m_outermostLine_front_point_offset(0.2f)
  , m_outermostLine_VehContour_Gap(0.0f)
  , m_wheelTrack_Width_Whole(0.228f)
  , m_wheelTrack_Width_BorderLine(0.0275f)
  , m_wheelTrack_Color_Manual_Inside(osg::Vec4f(1.0f, 0.75f, 0.0f, 0.3f))
  , m_wheelTrack_Color_Manual_BorderLine(osg::Vec4f(1.0f, 0.75f, 0.0f, 0.9f))
  , m_wheelTrack_Color_Auto_Close_Inside(osg::Vec4f(0.0f, 0.9f, 0.0f, 0.3f))
  , m_wheelTrack_Color_Auto_Close_BorderLine(osg::Vec4f(0.0f, 0.9f, 0.0f, 0.9f))
  , m_wheelTrack_Color_Auto_Far_Inside(osg::Vec4f(0.5f, 0.5f, 0.5f, 0.3f))
  , m_wheelTrack_Color_Auto_Far_BorderLine(osg::Vec4f(0.5f, 0.5f, 0.5f, 0.9f))

  , m_parkingTraj_Width_Whole(0.3f)
  , m_parkingTraj_Width_Shadow(0.12f)
  , m_parkingTraj_Width_BorderLine(0.033f)
  , m_parkingTraj_Color_Inside(osg::Vec4f(1.0f, 0.9412f, 0.117647f, 0.25f))
  , m_parkingTraj_Color_BorderLine(osg::Vec4f(1.0f, 0.9412f, 0.117647f, 1.0f))

  , m_actionPoint_Length(0.1f)
  , m_actionPoint_Color(osg::Vec4f(1.0f, 0.0f, 0.0f, 0.9f))
  , m_THTraj_StartPoint(osg::Vec3f(-1.0f, 0.0f, 0.0))
  , m_THTraj_Width(0.055f)
  , m_THTraj_Length(1.0f)
  , m_THTraj_Color(osg::Vec4f(1.0f, 0.75f, 0.0f, 0.9f))
  , m_THTrajBorder_Color(osg::Vec4f(1.0f, 0.75f, 0.0f, 0.9f))
  , m_THTraj_ColorGradientPosRatio(0.f)
  , m_DL1_Width(0.035f)
  , m_DL1_Offset_Front(0.3f)
  , m_DL1_Offset_Rear(0.3f)
  , m_DL1_Color(osg::Vec4f(1.0f, 0.0f, 0.0f, 0.9f))
  , m_length(5.0f)
  , m_gradientWidth(0.007f)
  , m_renderOffset_Front(0.29f) // if the apdist is 0.0, the Actionpoint will touch the Distanceline with its near position
  , m_renderOffset_Rear(0.29f)
  , m_refLineVisible(true)
  , m_animDurRemainingDistance(1000.f) // animation duration for the remaining distance

  , m_trailerTraj_Width(0.055f)
  , m_trailerTraj_BorderWidth(0.015f)
  , m_trailerTraj_InnerColor_InDrivingDir          (osg::Vec4f(0.314f, 0.392f, 0.157f, 0.95f))
  , m_trailerTraj_InnerColor_OppositeToDrivingDir  (osg::Vec4f(0.314f, 0.392f, 0.157f, 0.45f))
  , m_trailerTraj_BorderColor_InDrivingDir         (osg::Vec4f(0.078f, 0.471f, 0.118f, 0.95f))
  , m_trailerTraj_BorderColor_OppositeToDrivingDir (osg::Vec4f(0.078f, 0.471f, 0.118f, 0.45f))

  , m_trailerTraj_MaxStraightAngle(3.0f)
  , m_trailerTraj_Color_Straight                  (osg::Vec3f(0.286f, 0.768f, 0.466f))
  , m_trailerTraj_Color_NotStraight               (osg::Vec3f(1.0f, 0.2f, 1.0f))
  , m_trailerTraj_Color_JackKnife                 (osg::Vec3f(0.98f, 0.121f, 0.019f))
  , m_trailerTraj_Alpha_InDrivingDir_Inner        (0.55f)
  , m_trailerTraj_Alpha_InDrivingDir_Border       (0.75f)
  , m_trailerTraj_Alpha_OppositeDrivingDir_Inner  (0.25f)
  , m_trailerTraj_Alpha_OppositeDrivingDir_Border (0.15f)
  {
  }

  SERIALIZABLE(TrajectoryCodingParams)
  {
    ADD_FLOAT_MEMBER(outermostLine_Width);
    ADD_MEMBER (osg::Vec4f, outermostLine_Color_Manual);
    ADD_MEMBER (osg::Vec4f, outermostLine_Color_Auto);
    ADD_MEMBER (osg::Vec4f, outermostLine_Colorful_Color_1);
    ADD_MEMBER (osg::Vec4f, outermostLine_Colorful_Color_2);
    ADD_MEMBER (osg::Vec4f, outermostLine_Colorful_Color_3);
    ADD_FLOAT_MEMBER(outermostLine_OL_WT_minGap);
    ADD_FLOAT_MEMBER(outermostLineColoful_DI_MagicOffset);
    ADD_FLOAT_MEMBER(outermostLine_rear_point_offset);
    ADD_FLOAT_MEMBER(outermostLine_front_point_offset);
    ADD_FLOAT_MEMBER(outermostLine_VehContour_Gap);

    ADD_FLOAT_MEMBER(wheelTrack_Width_Whole);
    ADD_FLOAT_MEMBER(wheelTrack_Width_BorderLine);
    ADD_MEMBER (osg::Vec4f, wheelTrack_Color_Manual_Inside);
    ADD_MEMBER (osg::Vec4f, wheelTrack_Color_Manual_BorderLine);
    ADD_MEMBER (osg::Vec4f, wheelTrack_Color_Auto_Close_Inside);
    ADD_MEMBER (osg::Vec4f, wheelTrack_Color_Auto_Close_BorderLine);
    ADD_MEMBER (osg::Vec4f, wheelTrack_Color_Auto_Far_Inside);
    ADD_MEMBER (osg::Vec4f, wheelTrack_Color_Auto_Far_BorderLine);

    ADD_FLOAT_MEMBER(parkingTraj_Width_Whole);
    ADD_FLOAT_MEMBER(parkingTraj_Width_Shadow);
    ADD_FLOAT_MEMBER(parkingTraj_Width_BorderLine);
    ADD_MEMBER (osg::Vec4f, parkingTraj_Color_Inside);
    ADD_MEMBER (osg::Vec4f, parkingTraj_Color_BorderLine);

    ADD_FLOAT_MEMBER(actionPoint_Length);
    ADD_MEMBER (osg::Vec4f, actionPoint_Color);

    ADD_MEMBER (osg::Vec3f, THTraj_StartPoint);
    ADD_FLOAT_MEMBER(THTraj_Width);
    ADD_FLOAT_MEMBER(THTraj_Length);
    ADD_MEMBER (osg::Vec4f, THTraj_Color);
    ADD_MEMBER (osg::Vec4f, THTrajBorder_Color);
    ADD_FLOAT_MEMBER(THTraj_ColorGradientPosRatio);

    ADD_FLOAT_MEMBER(DL1_Width);
    ADD_FLOAT_MEMBER(DL1_Offset_Front);
    ADD_FLOAT_MEMBER(DL1_Offset_Rear);
    ADD_MEMBER (osg::Vec4f, DL1_Color);

    ADD_FLOAT_MEMBER(length);
    ADD_FLOAT_MEMBER(gradientWidth);
    ADD_FLOAT_MEMBER(renderOffset_Front);
    ADD_FLOAT_MEMBER(renderOffset_Rear);

    ADD_BOOL_MEMBER(refLineVisible);

    ADD_FLOAT_MEMBER(animDurRemainingDistance);

    ADD_MEMBER(DistanceIndicatorParams, DIs);

    ADD_FLOAT_MEMBER(trailerTraj_Width);
    ADD_FLOAT_MEMBER(trailerTraj_BorderWidth);
    ADD_MEMBER (osg::Vec4f, trailerTraj_InnerColor_InDrivingDir);
    ADD_MEMBER (osg::Vec4f, trailerTraj_InnerColor_OppositeToDrivingDir);
    ADD_MEMBER (osg::Vec4f, trailerTraj_BorderColor_InDrivingDir);
    ADD_MEMBER (osg::Vec4f, trailerTraj_BorderColor_OppositeToDrivingDir);

    ADD_FLOAT_MEMBER  (trailerTraj_MaxStraightAngle);
    ADD_MEMBER   (osg::Vec3f, trailerTraj_Color_Straight);
    ADD_MEMBER   (osg::Vec3f, trailerTraj_Color_NotStraight);
    ADD_MEMBER   (osg::Vec3f, trailerTraj_Color_JackKnife);
    ADD_FLOAT_MEMBER  (trailerTraj_Alpha_InDrivingDir_Inner);
    ADD_FLOAT_MEMBER  (trailerTraj_Alpha_InDrivingDir_Border);
    ADD_FLOAT_MEMBER  (trailerTraj_Alpha_OppositeDrivingDir_Inner);
    ADD_FLOAT_MEMBER  (trailerTraj_Alpha_OppositeDrivingDir_Border);
  }

  vfc::float32_t      m_outermostLine_Width;
  osg::Vec4f m_outermostLine_Color_Manual;
  osg::Vec4f m_outermostLine_Color_Auto;
  osg::Vec4f m_outermostLine_Colorful_Color_1;
  osg::Vec4f m_outermostLine_Colorful_Color_2;
  osg::Vec4f m_outermostLine_Colorful_Color_3;
  vfc::float32_t      m_outermostLine_OL_WT_minGap;
  vfc::float32_t      m_outermostLineColoful_DI_MagicOffset;
  vfc::float32_t      m_outermostLine_rear_point_offset;
  vfc::float32_t      m_outermostLine_front_point_offset;
  vfc::float32_t      m_outermostLine_VehContour_Gap;

  vfc::float32_t      m_wheelTrack_Width_Whole;
  vfc::float32_t      m_wheelTrack_Width_BorderLine;
  osg::Vec4f m_wheelTrack_Color_Manual_Inside;
  osg::Vec4f m_wheelTrack_Color_Manual_BorderLine;
  osg::Vec4f m_wheelTrack_Color_Auto_Close_Inside;
  osg::Vec4f m_wheelTrack_Color_Auto_Close_BorderLine;
  osg::Vec4f m_wheelTrack_Color_Auto_Far_Inside;
  osg::Vec4f m_wheelTrack_Color_Auto_Far_BorderLine;

  vfc::float32_t      m_parkingTraj_Width_Whole;
  vfc::float32_t      m_parkingTraj_Width_Shadow;
  vfc::float32_t      m_parkingTraj_Width_BorderLine;
  osg::Vec4f m_parkingTraj_Color_Inside;
  osg::Vec4f m_parkingTraj_Color_BorderLine;

  vfc::float32_t      m_actionPoint_Length;
  osg::Vec4f m_actionPoint_Color;

  osg::Vec3f m_THTraj_StartPoint;
  vfc::float32_t      m_THTraj_Width;
  vfc::float32_t      m_THTraj_Length;
  osg::Vec4f m_THTraj_Color;
  osg::Vec4f m_THTrajBorder_Color;
  vfc::float32_t      m_THTraj_ColorGradientPosRatio;

  vfc::float32_t      m_DL1_Width;
  vfc::float32_t      m_DL1_Offset_Front;
  vfc::float32_t      m_DL1_Offset_Rear;
  osg::Vec4f m_DL1_Color;

  vfc::float32_t      m_length;        // The length of the normal trajectory lines (not TH, it is separate).
  vfc::float32_t      m_gradientWidth; // Gradient width of all trajectory lines.
  vfc::float32_t      m_renderOffset_Front;
  vfc::float32_t      m_renderOffset_Rear;

  bool       m_refLineVisible;

  vfc::float32_t      m_animDurRemainingDistance;

  DistanceIndicatorParams m_DIs;

  vfc::float32_t      m_trailerTraj_Width;
  vfc::float32_t      m_trailerTraj_BorderWidth;
  osg::Vec4f m_trailerTraj_InnerColor_InDrivingDir;
  osg::Vec4f m_trailerTraj_InnerColor_OppositeToDrivingDir;
  osg::Vec4f m_trailerTraj_BorderColor_InDrivingDir;
  osg::Vec4f m_trailerTraj_BorderColor_OppositeToDrivingDir;

  vfc::float32_t       m_trailerTraj_MaxStraightAngle;
  osg::Vec3f  m_trailerTraj_Color_Straight;
  osg::Vec3f  m_trailerTraj_Color_NotStraight;
  osg::Vec3f  m_trailerTraj_Color_JackKnife;
  vfc::float32_t       m_trailerTraj_Alpha_InDrivingDir_Inner;
  vfc::float32_t       m_trailerTraj_Alpha_InDrivingDir_Border;
  vfc::float32_t       m_trailerTraj_Alpha_OppositeDrivingDir_Inner;
  vfc::float32_t       m_trailerTraj_Alpha_OppositeDrivingDir_Border;

  const std::string asString() const;
};


extern pc::util::coding::Item<TrajectoryCodingParams> g_trajCodingParams;


class VehicleOutlinePoints : public pc::util::coding::ISerializable
{
public:
  VehicleOutlinePoints()
  : m_p00(osg::Vec2f(0.0f, 0.0f))
  , m_p01(osg::Vec2f(0.0f, 0.0f))
  , m_p02(osg::Vec2f(0.0f, 0.0f))
  , m_p03(osg::Vec2f(0.0f, 0.0f))
  , m_p04(osg::Vec2f(0.0f, 0.0f))
  , m_p05(osg::Vec2f(0.0f, 0.0f))
  , m_p06(osg::Vec2f(0.0f, 0.0f))
  , m_p07(osg::Vec2f(0.0f, 0.0f))
  , m_p08(osg::Vec2f(0.0f, 0.0f))
  , m_p09(osg::Vec2f(0.0f, 0.0f))
  , m_p10(osg::Vec2f(0.0f, 0.0f))
  , m_p11(osg::Vec2f(0.0f, 0.0f))
  , m_p12(osg::Vec2f(0.0f, 0.0f))
  , m_p13(osg::Vec2f(0.0f, 0.0f))
  , m_p14(osg::Vec2f(0.0f, 0.0f))
  , m_p15(osg::Vec2f(0.0f, 0.0f))
  , m_p16(osg::Vec2f(0.0f, 0.0f))
  , m_p17(osg::Vec2f(0.0f, 0.0f))
  , m_p18(osg::Vec2f(0.0f, 0.0f))
  , m_p19(osg::Vec2f(0.0f, 0.0f))
  , m_p20(osg::Vec2f(0.0f, 0.0f))
  , m_p21(osg::Vec2f(0.0f, 0.0f))
  , m_p22(osg::Vec2f(0.0f, 0.0f))
  , m_p23(osg::Vec2f(0.0f, 0.0f))
  , m_p24(osg::Vec2f(0.0f, 0.0f))
  , m_p25(osg::Vec2f(0.0f, 0.0f))
  , m_p26(osg::Vec2f(0.0f, 0.0f))
  , m_p27(osg::Vec2f(0.0f, 0.0f))
  , m_p28(osg::Vec2f(0.0f, 0.0f))
  , m_p29(osg::Vec2f(0.0f, 0.0f))
  , m_p30(osg::Vec2f(0.0f, 0.0f))
  , m_p31(osg::Vec2f(0.0f, 0.0f))
  , m_p32(osg::Vec2f(0.0f, 0.0f))
  , m_p33(osg::Vec2f(0.0f, 0.0f))
  , m_p34(osg::Vec2f(0.0f, 0.0f))
  , m_p35(osg::Vec2f(0.0f, 0.0f))
  , m_p36(osg::Vec2f(0.0f, 0.0f))
  , m_p37(osg::Vec2f(0.0f, 0.0f))
  , m_p38(osg::Vec2f(0.0f, 0.0f))
  , m_p39(osg::Vec2f(0.0f, 0.0f))
  , m_p40(osg::Vec2f(0.0f, 0.0f))
  , m_p41(osg::Vec2f(0.0f, 0.0f))
  , m_p42(osg::Vec2f(0.0f, 0.0f))
  , m_p43(osg::Vec2f(0.0f, 0.0f))
  , m_p44(osg::Vec2f(0.0f, 0.0f))
  , m_p45(osg::Vec2f(0.0f, 0.0f))
  , m_p46(osg::Vec2f(0.0f, 0.0f))
  , m_p47(osg::Vec2f(0.0f, 0.0f))
  , m_p48(osg::Vec2f(0.0f, 0.0f))
  , m_p49(osg::Vec2f(0.0f, 0.0f))
  {
  }

  SERIALIZABLE(VehicleOutlinePoints)
  {
    ADD_MEMBER(osg::Vec2f, p00);
    ADD_MEMBER(osg::Vec2f, p01);
    ADD_MEMBER(osg::Vec2f, p02);
    ADD_MEMBER(osg::Vec2f, p03);
    ADD_MEMBER(osg::Vec2f, p04);
    ADD_MEMBER(osg::Vec2f, p05);
    ADD_MEMBER(osg::Vec2f, p06);
    ADD_MEMBER(osg::Vec2f, p07);
    ADD_MEMBER(osg::Vec2f, p08);
    ADD_MEMBER(osg::Vec2f, p09);
    ADD_MEMBER(osg::Vec2f, p10);
    ADD_MEMBER(osg::Vec2f, p11);
    ADD_MEMBER(osg::Vec2f, p12);
    ADD_MEMBER(osg::Vec2f, p13);
    ADD_MEMBER(osg::Vec2f, p14);
    ADD_MEMBER(osg::Vec2f, p15);
    ADD_MEMBER(osg::Vec2f, p16);
    ADD_MEMBER(osg::Vec2f, p17);
    ADD_MEMBER(osg::Vec2f, p18);
    ADD_MEMBER(osg::Vec2f, p19);
    ADD_MEMBER(osg::Vec2f, p20);
    ADD_MEMBER(osg::Vec2f, p21);
    ADD_MEMBER(osg::Vec2f, p22);
    ADD_MEMBER(osg::Vec2f, p23);
    ADD_MEMBER(osg::Vec2f, p24);
    ADD_MEMBER(osg::Vec2f, p25);
    ADD_MEMBER(osg::Vec2f, p26);
    ADD_MEMBER(osg::Vec2f, p27);
    ADD_MEMBER(osg::Vec2f, p28);
    ADD_MEMBER(osg::Vec2f, p29);
    ADD_MEMBER(osg::Vec2f, p30);
    ADD_MEMBER(osg::Vec2f, p31);
    ADD_MEMBER(osg::Vec2f, p32);
    ADD_MEMBER(osg::Vec2f, p33);
    ADD_MEMBER(osg::Vec2f, p34);
    ADD_MEMBER(osg::Vec2f, p35);
    ADD_MEMBER(osg::Vec2f, p36);
    ADD_MEMBER(osg::Vec2f, p37);
    ADD_MEMBER(osg::Vec2f, p38);
    ADD_MEMBER(osg::Vec2f, p39);
    ADD_MEMBER(osg::Vec2f, p40);
    ADD_MEMBER(osg::Vec2f, p41);
    ADD_MEMBER(osg::Vec2f, p42);
    ADD_MEMBER(osg::Vec2f, p43);
    ADD_MEMBER(osg::Vec2f, p44);
    ADD_MEMBER(osg::Vec2f, p45);
    ADD_MEMBER(osg::Vec2f, p46);
    ADD_MEMBER(osg::Vec2f, p47);
    ADD_MEMBER(osg::Vec2f, p48);
    ADD_MEMBER(osg::Vec2f, p49);
  }

  osg::Vec2f m_p00;
  osg::Vec2f m_p01;
  osg::Vec2f m_p02;
  osg::Vec2f m_p03;
  osg::Vec2f m_p04;
  osg::Vec2f m_p05;
  osg::Vec2f m_p06;
  osg::Vec2f m_p07;
  osg::Vec2f m_p08;
  osg::Vec2f m_p09;
  osg::Vec2f m_p10;
  osg::Vec2f m_p11;
  osg::Vec2f m_p12;
  osg::Vec2f m_p13;
  osg::Vec2f m_p14;
  osg::Vec2f m_p15;
  osg::Vec2f m_p16;
  osg::Vec2f m_p17;
  osg::Vec2f m_p18;
  osg::Vec2f m_p19;
  osg::Vec2f m_p20;
  osg::Vec2f m_p21;
  osg::Vec2f m_p22;
  osg::Vec2f m_p23;
  osg::Vec2f m_p24;
  osg::Vec2f m_p25;
  osg::Vec2f m_p26;
  osg::Vec2f m_p27;
  osg::Vec2f m_p28;
  osg::Vec2f m_p29;
  osg::Vec2f m_p30;
  osg::Vec2f m_p31;
  osg::Vec2f m_p32;
  osg::Vec2f m_p33;
  osg::Vec2f m_p34;
  osg::Vec2f m_p35;
  osg::Vec2f m_p36;
  osg::Vec2f m_p37;
  osg::Vec2f m_p38;
  osg::Vec2f m_p39;
  osg::Vec2f m_p40;
  osg::Vec2f m_p41;
  osg::Vec2f m_p42;
  osg::Vec2f m_p43;
  osg::Vec2f m_p44;
  osg::Vec2f m_p45;
  osg::Vec2f m_p46;
  osg::Vec2f m_p47;
  osg::Vec2f m_p48;
  osg::Vec2f m_p49;
};


class VehicleOutline : public pc::util::coding::ISerializable
{
public:
  VehicleOutline()
  : m_pointCount((vfc::uint32_t)1)
  , m_frontBumperStartPointIndex((vfc::uint32_t)0)
  , m_frontBumperEndPointIndex((vfc::uint32_t)0)
  , m_mirrorPointIndex((vfc::uint32_t)0)
  , m_rearBumperStartPointIndex((vfc::uint32_t)0)
  , m_rearBumperEndPointIndex((vfc::uint32_t)0)
  {
  }

  SERIALIZABLE(VehicleOutline)
  {
    ADD_UINT32_MEMBER(pointCount);
    ADD_UINT32_MEMBER(frontBumperStartPointIndex);
    ADD_UINT32_MEMBER(frontBumperEndPointIndex);
    ADD_UINT32_MEMBER(mirrorPointIndex);
    ADD_UINT32_MEMBER(rearBumperStartPointIndex);
    ADD_UINT32_MEMBER(rearBumperEndPointIndex);
    ADD_MEMBER       (VehicleOutlinePoints, points);
  }

  vfc::uint32_t         m_pointCount;
  vfc::uint32_t         m_frontBumperStartPointIndex;
  vfc::uint32_t         m_frontBumperEndPointIndex;
  vfc::uint32_t         m_mirrorPointIndex;
  vfc::uint32_t         m_rearBumperStartPointIndex;
  vfc::uint32_t         m_rearBumperEndPointIndex;
  VehicleOutlinePoints m_points;

  const osg::Vec2f* getPointPtr(vfc::uint32_t f_index) const
  {
    if ((m_pointCount <= 50u) && (f_index < m_pointCount))
      return (&(m_points.m_p00)) + f_index;
    else
      return &(m_points.m_p00);
  }

  bool setPoint(vfc::uint32_t f_index, vfc::float32_t x, vfc::float32_t y)
  {
    bool l_ret = false;
    if ((m_pointCount <= 50u) && (f_index < m_pointCount))
    {
      osg::Vec2f* l_pt = (&(m_points.m_p00)) + f_index;
      l_pt->set(x,y);
      l_ret = true;
    }
    return l_ret;
  }

  const std::string asString() const;

};


extern pc::util::coding::Item<VehicleOutline> g_vehicleOutline;


namespace mainlogic
{


enum DrivingDirection_en
{
  Forward_enm,
  Backward_enm
};

struct Vector2D_st
{
  osg::Vec2f startPos;
  osg::Vec2f direction;
};

struct Car_st
{
  // If the 2 steering angles are the same, it means a special steering mode: translation.
  // In this case all the wheels have the same steering angle, therefore the car is doing pure straight-line movement.
  vfc::float32_t SteeringAngle_Front; // Steering angle in the middle of the front axis. [deg]
  vfc::float32_t SteeringAngle_Rear;  // Steering angle in the middle of the rear axis. [deg]
  DrivingDirection_en DrivingDirection;
  bool m_exteriorMirrorFolded_Left;
  bool m_exteriorMirrorFolded_Right;
  vfc::float32_t Wheelbase;       // [m]
  vfc::float32_t FrontTrack;      // The distance between the center points of the front wheels. [m]
  vfc::float32_t RearTrack;       // The distance between the center points of the rear wheels. [m]
  vfc::float32_t FrontWheelWidth; // [m]
  vfc::float32_t RearWheelWidth;  // [m]
  vfc::float32_t FrontBumperPos;  // Measured on the Dyn70k X axis. [m]
  vfc::float32_t RearBumperPos;   // Measured on the Dyn70k X axis. [m]
  osg::Vec3f THBallPos;  // Trailer Hitch Ball position in Dyn70k. [m]
  osg::ref_ptr<osg::Vec2Array> VehicleContour_Left;
  osg::ref_ptr<osg::Vec2Array> VehicleContour_Right;
  vfc::uint32_t MirrorPointIndex;
  vfc::uint32_t FrontBumperStartPointIndex;
  vfc::uint32_t FrontBumperEndPointIndex;
  vfc::uint32_t RearBumperStartPointIndex;
  vfc::uint32_t RearBumperEndPointIndex;
};

struct Parking_st
{
  Parking_st()
    : AutomaticParking(false)
    , ActionPointDist_Front(1.3f)
    , ActionPointDist_Rear(1.3f)
  {
  }

  bool  AutomaticParking;
  vfc::float32_t ActionPointDist_Front; // Measured from the same reference point as all other longitudinal trajectory distance values. [m]
  vfc::float32_t ActionPointDist_Rear;  // Measured from the same reference point as all other longitudinal trajectory distance values. [m]
};

struct External_st
{
  External_st()
    : Car()
    , Parking()
  {
  }

  Car_st     Car;
  Parking_st Parking;
};

struct Internal_st
{
  Internal_st()
    : VehicleMovementType(cc::assets::trajectory::commontypes::Translation_enm)
    , TurningDirection(cc::assets::trajectory::commontypes::ToLeft_enm)
    , AckermannPoint()
    , TranslationAngle(0.0f)
    , VehicleContour_Left_Rotated()
    , VehicleContour_Right_Rotated()
  {
  }
  // In Dyn70k coordinate system:

  // If "SteeringAngle_Front" and "SteeringAngle_Rear" differs, then it is set to "Rotation_enm".
  // If the angles are the same, then it is set to "Translation_enm" to represent straight-line movement.
  cc::assets::trajectory::commontypes::VehicleMovementType_en VehicleMovementType;

  // Stores the turning direction assuming that the vehicle is moving forward.
  // Not affected by the driving direction (forward or backward).
  cc::assets::trajectory::commontypes::TurningDirection_en    TurningDirection;

  osg::Vec2f             AckermannPoint;      // Turning circle center. [m]
                                              // Only used when "VehicleMovementType" is "Rotation_enm".

  vfc::float32_t                  TranslationAngle;    // Only used when "VehicleMovementType" is "Translation_enm".

  osg::ref_ptr<osg::Vec2Array> VehicleContour_Left_Rotated;
  osg::ref_ptr<osg::Vec2Array> VehicleContour_Right_Rotated;
};

struct Inputs_st
{
  External_st External;
  Internal_st Internal;
};

// Base Points are used to calculate the Frame Points of the trajectory.
struct BasePoint_st
{
  BasePoint_st()
    : Pos()
    , Angle(0.0f)
    , Radius(0.0f)
  {
  }

  osg::Vec2f Pos;
  vfc::float32_t      Angle; // This is the angle of the wheel center point from the Ackermann point. [rad]
  vfc::float32_t      Radius;
};

struct WheelCenter_st
{
  BasePoint_st FL;
  BasePoint_st FR;
  BasePoint_st RL;
  BasePoint_st RR;
};

struct ModelData_st
{
  ModelData_st()
    : LeftRightDirMul(0.0f)
    , ForwardBackwardDirMul(0.0f)
    , ToLeftOffsetMul(0.0f)
    , ToRightOffsetMul(0.0f)
    , LeftRightDirMulHitch(0.0f)
    , ToLeftOffsetMulHitch(0.0f)
    , ToRightOffsetMulHitch(0.0f)
    , LongitudinalTouchPointDefault(0.0f)
    , LongitudinalTouchPoint()
    , LeftTouchPoint()
    , RightTouchPoint()
    , ExtraTouchPoint()
    , LeftWheelCenter_InDrvDir()
    , RightWheelCenter_InDrvDir()
    , TrailerAssistCenter()
    , LeftWheelCenter_OppToDrvDir()
    , RightWheelCenter_OppToDrvDir()
    , BumperCenterPoint()
    , THBallCenter()
    , HalfWheelWidth(0.0f)
    , m_modifiedCount(0u)
  {
  }

  vfc::float32_t        LeftRightDirMul;
  vfc::float32_t        ForwardBackwardDirMul;
  vfc::float32_t        ToLeftOffsetMul;
  vfc::float32_t        ToRightOffsetMul;
  vfc::float32_t        LeftRightDirMulHitch;
  vfc::float32_t        ToLeftOffsetMulHitch;
  vfc::float32_t        ToRightOffsetMulHitch;
  vfc::float32_t        LongitudinalTouchPointDefault;
  BasePoint_st LongitudinalTouchPoint;
  BasePoint_st LeftTouchPoint;
  BasePoint_st RightTouchPoint;
  BasePoint_st ExtraTouchPoint;
  BasePoint_st LeftWheelCenter_InDrvDir;
  BasePoint_st RightWheelCenter_InDrvDir;
  BasePoint_st LeftWheelCenter_OppToDrvDir;
  BasePoint_st RightWheelCenter_OppToDrvDir;
  BasePoint_st TrailerAssistCenter;
  BasePoint_st BumperCenterPoint;
  BasePoint_st THBallCenter;
  vfc::float32_t        HalfWheelWidth;
  vfc::uint32_t m_modifiedCount;
};

struct AABB_2D_st
{
  AABB_2D_st()
    : minX(0.0f)
    , maxX(0.0f)
    , minY(0.0f)
    , maxY(0.0f)
  {
  }

  vfc::float32_t minX;
  vfc::float32_t maxX;
  vfc::float32_t minY;
  vfc::float32_t maxY;
};

enum BaseOfInterpolation
{
  RADIUS,
  ANGLE,
  LATERAL,
  LONGITUDINAL
};

enum TrajectoriesState
{
  SHOW,   // Trajectories will be rendered
  HIDE,   // Trajectories will not be rendered e.g. gear is N or P
  CHANGED,// Trajectories have changed since the last traverse and need to be updated
  SHOWING_TRAILER_ASSIST_LINE
};

//! Gear
enum EGear : vfc::int32_t // PRQA S 2502
{
  GEAR_INIT    = 0,
  GEAR_1       = 1,
  GEAR_2       = 2,
  GEAR_3       = 3,
  GEAR_4       = 4,
  GEAR_5       = 5,
  GEAR_6       = 6,
  GEAR_7       = 7,
  GEAR_8       = 8,
  GEAR_9       = 9,
  GEAR_N       = 10,
  GEAR_R       = 11,
  GEAR_P       = 12,
  GEAR_D       = 13,
  GEAR_INVALID = 14
};

class MainLogic : public osg::NodeCallback
{
public:

  MainLogic(pc::core::Framework* f_framework);

  const ModelData_st getModelDataRef() const;

  const Inputs_st getInputDataRef() const;

  bool getYFromTurningCircleEquation(vfc::float32_t f_in_radius, vfc::float32_t f_in_x, vfc::float32_t & f_out_y) const;

  bool getYFromLineEquation(vfc::float32_t f_in_offset, vfc::float32_t f_in_x, vfc::float32_t & f_out_y) const;

  virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override;

  void update();

protected:
  virtual ~MainLogic();

private:

  //! Copy constructor is not permitted.
  MainLogic (const MainLogic& other); // = delete
  //! Copy assignment operator is not permitted.
  MainLogic& operator=(const MainLogic& other); // = delete

  pc::core::Framework* m_framework;
  vfc::uint32_t m_modifiedCount = ~0u;
  vfc::uint32_t m_lastUpdate;
  ModelData_st m_modelData;
  TrajectoriesState m_trajectoryState;
  Inputs_st    m_inputs;
  EGear m_pre_Gear;
  vfc::float32_t m_actionPointNearPosLeft;
  vfc::float32_t m_actionPointNearPosRight;
  vfc::uint16_t  m_calibSeqNumber;   //!< Seq Number for camera calibration

  void updateVehicleContour(bool f_onLeft, bool f_mirrorFolded);
  void readInputs_Static();
  TrajectoriesState readInputs_Dynamic();
  void calculateTrajectoryModelData();

  static vfc::float32_t get2DPointDistance(const osg::Vec2f & f_point1, const osg::Vec2f & f_point2);

  static osg::Vec2f getPrevContourPoint(vfc::uint32_t f_currentIndex,
                                 const osg::Vec2Array* const f_currentContour,
                                 const osg::Vec2Array* const f_otherContour);
  static osg::Vec2f getNextContourPoint(vfc::uint32_t f_currentIndex,
                                 const osg::Vec2Array* const f_currentContour,
                                 const osg::Vec2Array* const f_otherContour);

  osg::Vec2f interpolateVehicleContourPoints(
    const osg::Vec2f & f_point,
    const vfc::float32_t f_value,
    const vfc::uint32_t f_pointIndex,
    const osg::Vec2Array* const f_currentContour,
    const osg::Vec2Array* const f_otherContour,
    const BaseOfInterpolation f_baseOfInterpolation) const;

  void getOutermostTouchPoints(osg::Vec2f & f_nearPoint, osg::Vec2f & f_farPoint, osg::Vec2f & f_farPoint_extra,
                               vfc::float32_t & f_nearRadius, vfc::float32_t & f_farRadius, vfc::float32_t & f_farRadius_extra) const;
  void getOutermostTouchPoints_Straight(osg::Vec2f & f_leftPoint, osg::Vec2f & f_rightPoint) const;
  void getFrontmostTouchPointAngleInCurrentDrivingDirection(DrivingDirection_en f_drivingDir, vfc::float32_t & f_angle, vfc::float32_t & f_radius) const;
  void getFrontmostTouchPointPosInCurrentDrivingDirection(DrivingDirection_en f_drivingDir, osg::Vec2f & f_rotatedPos) const;
  osg::Vec2f calculateAckermannPoint() const;
  bool ackermannPointIsInsideTheVehicleContour() const;
  void getVehicleAABB_2D(AABB_2D_st & f_AABB) const;
  vfc::float32_t getAngleFromAckermannPoint(osg::Vec2f f_point) const;
  vfc::float32_t getDistanceFromAckermannPoint(osg::Vec2f f_point) const;
};


} // namespace mainlogic
} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_MAINLOGIC_H
