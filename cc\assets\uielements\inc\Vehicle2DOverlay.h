//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Vehicle2DOverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_VEHICLE2D_OVERLAY_H
#define CC_ASSETS_UIELEMENTS_VEHICLE2D_OVERLAY_H

#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/Matrixf>

namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

//======================================================
// VehicleVehicle2DSettings
//------------------------------------------------------
/// Setting class for VehicleVehicle2D
/// <AUTHOR>
//======================================================
class Vehicle2DOverlaySettings : public pc::util::coding::ISerializable
{
public:
    Vehicle2DOverlaySettings()
        // hori open
        : m_frontLeftDoorOpen("cc/vehicle_model/vehicle2d/gray/front_left_door/open.png")
        , m_frontRightDoorOpen("cc/vehicle_model/vehicle2d/gray/front_right_door/open.png")
        , m_hoodOpen("cc/vehicle_model/vehicle2d/gray/hood/open.png")
        , m_rearLeftDoorOpen("cc/vehicle_model/vehicle2d/gray/rear_left_door/open.png")
        , m_rearRightDoorOpen("cc/vehicle_model/vehicle2d/gray/rear_right_door/open.png")
        , m_trunkOpen("cc/vehicle_model/vehicle2d/gray/trunk/open.png")
        , m_vehicleBodyOpen("cc/vehicle_model/vehicle2d/gray/vehicle_body/body_without_doors.png")
        , m_frontLeftDoorClose("cc/vehicle_model/vehicle2d/gray/front_left_door/close.png")
        , m_frontRightDoorClose("cc/vehicle_model/vehicle2d/gray/front_right_door/close.png")
        , m_hoodClose("cc/vehicle_model/vehicle2d/gray/hood/close.png")
        , m_rearLeftDoorClose("cc/vehicle_model/vehicle2d/gray/rear_left_door/close.png")
        , m_rearRightDoorClose("cc/vehicle_model/vehicle2d/gray/rear_right_door/close.png")
        , m_trunkClose("cc/vehicle_model/vehicle2d/gray/trunk/close.png")
    {
    }

    SERIALIZABLE(Vehicle2DOverlaySettings)
    {
        ADD_STRING_MEMBER(frontLeftDoorOpen);
        ADD_STRING_MEMBER(frontRightDoorOpen);
        ADD_STRING_MEMBER(hoodOpen);
        ADD_STRING_MEMBER(rearLeftDoorOpen);
        ADD_STRING_MEMBER(rearRightDoorOpen);
        ADD_STRING_MEMBER(trunkOpen);
        ADD_STRING_MEMBER(vehicleBodyOpen);
        ADD_STRING_MEMBER(frontLeftDoorClose);
        ADD_STRING_MEMBER(frontRightDoorClose);
        ADD_STRING_MEMBER(hoodClose);
        ADD_STRING_MEMBER(rearLeftDoorClose);
        ADD_STRING_MEMBER(rearRightDoorClose);
        ADD_STRING_MEMBER(trunkClose);
    }

    std::string m_frontLeftDoorOpen;
    std::string m_frontRightDoorOpen;
    std::string m_hoodOpen;
    std::string m_rearLeftDoorOpen;
    std::string m_rearRightDoorOpen;
    std::string m_trunkOpen;
    std::string m_vehicleBodyOpen;
    std::string m_frontLeftDoorClose;
    std::string m_frontRightDoorClose;
    std::string m_hoodClose;
    std::string m_rearLeftDoorClose;
    std::string m_rearRightDoorClose;
    std::string m_trunkClose;
};

class Vehicle2DIndicatorSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(Vehicle2DIndicatorSettings)
    {
        ADD_STRING_MEMBER(frontIndicatorOnTexturePath);
        ADD_STRING_MEMBER(rearIndicatorOnTexturePath);
        ADD_STRING_MEMBER(doorOpenIndicatorTexturePath);
        ADD_STRING_MEMBER(doorCloseIndicatorTexturePath);
        ADD_MEMBER(osg::Vec2f, frontIndicatorOnPosOffset);
        ADD_MEMBER(osg::Vec2f, rearIndicatorOnPosOffset);
        ADD_MEMBER(osg::Vec2f, doorOpenIndicatorPosOffset);
        ADD_MEMBER(osg::Vec2f, doorCloseIndicatorPosOffset);
    }

    std::string m_frontIndicatorOnTexturePath;
    std::string m_rearIndicatorOnTexturePath;
    std::string m_doorOpenIndicatorTexturePath;
    std::string m_doorCloseIndicatorTexturePath;
    osg::Vec2f m_frontIndicatorOnPosOffset;
    osg::Vec2f m_rearIndicatorOnPosOffset;
    osg::Vec2f m_doorOpenIndicatorPosOffset;
    osg::Vec2f m_doorCloseIndicatorPosOffset;
};

//!
//! Vehicle2DSettings
//!
class Vehicle2DSettings : public pc::util::coding::ISerializable
{
public:
    Vehicle2DSettings()
        : m_proportion(489.0f, 491.0f)
        , m_aspectRatioOfVehicle(0.46f)
        , m_planViewPos(215.0f, 355.0f)
        , m_parkingPlanViewPos(854.0f, 355.0f)
        , m_avmBackgroundPos(0.0f, 0.0f)
        , m_planViewPosRemote(400.0f, 540.0f)
        , m_planViewFLDoorOpenPosOffset(0.0f, 0.0f)
        , m_planViewFRDoorOpenPosOffset(0.0f, 0.0f)
        , m_planViewRLDoorOpenPosOffset(0.0f, 0.0f)
        , m_planViewRRDoorOpenPosOffset(0.0f, 0.0f)
        , m_planViewTrunkOpenPosOffset(0.0f, 0.0f)
        , m_planViewFLDoorClosePosOffset(0.0f, 0.0f)
        , m_planViewFRDoorClosePosOffset(0.0f, 0.0f)
        , m_planViewRLDoorClosePosOffset(0.0f, 0.0f)
        , m_planViewRRDoorClosePosOffset(0.0f, 0.0f)
        , m_planViewTrunkClosePosOffset(0.0f, 0.0f)
        , m_planViewDoorScale(0.0f, 0.0f)
        , m_planViewHoodPosScale(0.0f, 0.0f)
        , m_planViewTrunkPosScale(0.0f, 0.0f)
        , m_horiParkingLeftMaskOffset(0.0f, 0.0f)
        , m_horiParkingRightMaskOffset(0.0f, 0.0f)
        , m_horiParkingMaskScaleFactor(1.11f)
        , m_vertParkingLeftMaskOffset(0.0f, 0.0f)
        , m_vertParkingRightMaskOffset(0.0f, 0.0f)
        , m_vertParkingMaskScaleFactor(1.11f)
    {
    }

    SERIALIZABLE(Vehicle2DSettings)
    {
        // color texture path
        ADD_MEMBER(Vehicle2DOverlaySettings, pink);
        ADD_MEMBER(Vehicle2DOverlaySettings, blue);
        ADD_MEMBER(Vehicle2DOverlaySettings, sliver);
        ADD_MEMBER(Vehicle2DOverlaySettings, white);
        ADD_MEMBER(Vehicle2DOverlaySettings, black);
        ADD_MEMBER(Vehicle2DOverlaySettings, purple);
        ADD_MEMBER(Vehicle2DOverlaySettings, transparent);

        // Indicator texture path
        ADD_MEMBER(Vehicle2DIndicatorSettings, leftIndicator);
        ADD_MEMBER(Vehicle2DIndicatorSettings, rightIndicator);
        // image properties
        ADD_MEMBER(osg::Vec2f, proportion);
        ADD_FLOAT_MEMBER(aspectRatioOfVehicle);
        ADD_MEMBER(osg::Vec2f, planViewPos);
        ADD_MEMBER(osg::Vec2f, planViewPosVert);
        ADD_MEMBER(osg::Vec2f, parkingPlanViewPos);
        ADD_MEMBER(osg::Vec2f, avmBackgroundPos);
        ADD_MEMBER(osg::Vec2f, planViewPosRemote);
        // offset
        ADD_MEMBER(osg::Vec2f, planViewBodyPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFLDoorOpenPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFRDoorOpenPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewRLDoorOpenPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewRRDoorOpenPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTrunkOpenPosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFLDoorClosePosOffset);
        ADD_MEMBER(osg::Vec2f, planViewFRDoorClosePosOffset);
        ADD_MEMBER(osg::Vec2f, planViewRLDoorClosePosOffset);
        ADD_MEMBER(osg::Vec2f, planViewRRDoorClosePosOffset);
        ADD_MEMBER(osg::Vec2f, planViewTrunkClosePosOffset);
        // scale
        ADD_MEMBER(osg::Vec2f, planViewDoorScale);
        ADD_MEMBER(osg::Vec2f, planViewHoodPosScale);
        ADD_MEMBER(osg::Vec2f, planViewTrunkPosScale);
        // mask settings
        ADD_MEMBER(osg::Vec2f, horiParkingLeftMaskOffset);
        ADD_MEMBER(osg::Vec2f, horiParkingRightMaskOffset);
        ADD_FLOAT_MEMBER(horiParkingMaskScaleFactor);
        ADD_MEMBER(osg::Vec2f, vertParkingLeftMaskOffset);
        ADD_MEMBER(osg::Vec2f, vertParkingRightMaskOffset);
        ADD_FLOAT_MEMBER(vertParkingMaskScaleFactor);
    }

    Vehicle2DOverlaySettings m_pink;
    Vehicle2DOverlaySettings m_blue;
    Vehicle2DOverlaySettings m_sliver;
    Vehicle2DOverlaySettings m_white;
    Vehicle2DOverlaySettings m_black;
    Vehicle2DOverlaySettings m_purple;
    Vehicle2DOverlaySettings m_transparent;

    Vehicle2DIndicatorSettings m_leftIndicator;
    Vehicle2DIndicatorSettings m_rightIndicator;

    osg::Vec2f     m_proportion; // 1st value is the valid pixel height of vehicle, 2nd is the pixel height of the pic
    vfc::float32_t m_aspectRatioOfVehicle;
    osg::Vec2f     m_planViewPos;
    osg::Vec2f     m_planViewPosVert;
    osg::Vec2f     m_parkingPlanViewPos;
    osg::Vec2f     m_avmBackgroundPos;
    osg::Vec2f     m_planViewPosRemote;

    // offset
    osg::Vec2f m_planViewBodyPosOffset;
    osg::Vec2f m_planViewFLDoorOpenPosOffset;
    osg::Vec2f m_planViewFRDoorOpenPosOffset;
    osg::Vec2f m_planViewRLDoorOpenPosOffset;
    osg::Vec2f m_planViewRRDoorOpenPosOffset;
    osg::Vec2f m_planViewTrunkOpenPosOffset;
    osg::Vec2f m_planViewFLDoorClosePosOffset;
    osg::Vec2f m_planViewFRDoorClosePosOffset;
    osg::Vec2f m_planViewRLDoorClosePosOffset;
    osg::Vec2f m_planViewRRDoorClosePosOffset;
    osg::Vec2f m_planViewTrunkClosePosOffset;
    // scale
    osg::Vec2f m_planViewDoorScale;
    osg::Vec2f m_planViewHoodPosScale;
    osg::Vec2f m_planViewTrunkPosScale;
    // mask
    osg::Vec2f     m_horiParkingLeftMaskOffset;
    osg::Vec2f     m_horiParkingRightMaskOffset;
    vfc::float32_t m_horiParkingMaskScaleFactor;
    osg::Vec2f     m_vertParkingLeftMaskOffset;
    osg::Vec2f     m_vertParkingRightMaskOffset;
    vfc::float32_t m_vertParkingMaskScaleFactor;
};

extern pc::util::coding::Item<Vehicle2DSettings> g_Vehicle2DSettings;

enum Vehicle2DOverlayType : vfc::uint32_t
{
    VEHICLE2D_BODY_OPEN,
    VEHICLE2D_FRONT_LEFT_DOOR_OPEN,
    VEHICLE2D_FRONT_RIGHT_DOOR_OPEN,
    VEHICLE2D_REAR_LEFT_DOOR_OPEN,
    VEHICLE2D_REAR_RIGHT_DOOR_OPEN,
    VEHICLE2D_TRUNK_OPEN,
    VEHICLE2D_FRONT_LEFT_DOOR_CLOSE,
    VEHICLE2D_FRONT_RIGHT_DOOR_CLOSE,
    VEHICLE2D_REAR_LEFT_DOOR_CLOSE,
    VEHICLE2D_REAR_RIGHT_DOOR_CLOSE,
    VEHICLE2D_TRUNK_CLOSE,
    VEHICLE2D_LEFT_FRONT_INDICATOR_ON,
    VEHICLE2D_LEFT_REAR_INDICATOR_ON,
    VEHICLE2D_LEFT_DOOR_OPEN_INDICATOR_ON,
    VEHICLE2D_LEFT_DOOR_CLOSE_INDICATOR_ON,
    VEHICLE2D_RIGHT_FRONT_INDICATOR_ON,
    VEHICLE2D_RIGHT_REAR_INDICATOR_ON,
    VEHICLE2D_RIGHT_DOOR_OPEN_INDICATOR_ON,
    VEHICLE2D_RIGHT_DOOR_CLOSE_INDICATOR_ON,
    NUM_OVERLAYS,
};

//!
//! Vehicle2DOverlayManager
//!
class Vehicle2DOverlayManager
{
public:
    typedef std::vector<osg::ref_ptr<pc::assets::Icon>> IconList;

    Vehicle2DOverlayManager();
    virtual ~Vehicle2DOverlayManager();

    void init(
        pc::assets::ImageOverlays* f_imageOverlays,
        core::CustomFramework*     f_framework,
        const vfc::uint8_t         f_colorIndex);
    void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
    void update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);
    void setVehicleIconPosition(core::CustomFramework* f_framework);
    void setVehicleIconSize(core::CustomFramework* f_framework);
    static const Vehicle2DOverlaySettings getColorFromIndex(vfc::uint8_t f_colorIndex);
    bool                                  updateReferenceView(
                                         pc::assets::ImageOverlays* f_imageOverlays,
                                         core::CustomFramework*     f_framework,
                                         vfc::uint32_t&             f_viewId);

    void updateIconsSizePosition(
        const vfc::uint32_t        f_viewId,
        pc::assets::ImageOverlays* f_imageOverlays,
        core::CustomFramework*     f_framework); // PRQA S 2755
    void updateIconSizePosition(
        const cc::assets::uielements::Vehicle2DOverlayType f_index,
        const vfc::float64_t                               f_iconScaler,
        const osg::Vec2f&                                  f_iconCenter,
        const osg::Vec2f&                                  f_iconPosOffset);

private:
    //! Copy constructor is not permitted.
    Vehicle2DOverlayManager(const Vehicle2DOverlayManager& other); // = delete
    //! Copy assignment operator is not permitted.
    Vehicle2DOverlayManager& operator=(const Vehicle2DOverlayManager& other); // = delete

    unsigned int                               m_lastConfigUpdate;
    pc::assets::IconGroup                      m_Vehicle2DOverlays;
    cc::views::planview::PlanViewCullCallback* m_planViewCullCall;
    bool                                       m_mat_b;
    vfc::uint8_t                               m_colorIndex;
    bool                                       m_parkActive;
    vfc::uint32_t                              m_viewId;
};

//!
//! Vehicle2DOverlay
//!
class Vehicle2DOverlay : public cc::assets::uielements::CustomImageOverlays
{
public:
    Vehicle2DOverlay(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
    virtual ~Vehicle2DOverlay();

    virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
    //! Copy constructor is not permitted.
    Vehicle2DOverlay(const Vehicle2DOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    Vehicle2DOverlay& operator=(const Vehicle2DOverlay& other); // = delete

    cc::core::CustomFramework* m_customFramework;
    Vehicle2DOverlayManager    m_manager;
};

bool       checkParkingActive(cc::target::common::EPARKStatusR2L f_parkStatus);
osg::Vec2f calculateIconSize(bool f_parkActive);

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_VEHICLE2D_OVERLAY_H
