//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  ParkingCommand.cpp
/// @brief
//=============================================================================

#include "pc/generic/util/cli/inc/CommandCallback.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/target/common/inc/valin_types_api.hpp"
#include "vfc/core/vfc_types.hpp"
/// @deviation NRCS2_076
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace parkingspots
{

//!
//! ParkInTestCommand
//!
class ParkInTest : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint32_t l_park = 0u;
    f_input >> l_park;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::ParkingSpotsListDaddy& l_parkingSpotsListDaddy = cc::daddy::CustomDaddyPorts::sm_parkingInSpots_SenderPort.reserve();
    if (l_park == 1u)
    {
      cc::daddy::ParkingSpot& l_parkingSpotLeft0 = l_parkingSpotsListDaddy.m_Data[0u][0u];
      l_parkingSpotLeft0.m_position = osg::Vec2f(2.0f, 2.3f);
      l_parkingSpotLeft0.m_length = 4.7f;
      l_parkingSpotLeft0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL;
      l_parkingSpotLeft0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX;
      l_parkingSpotLeft0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotLeft0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotLeft0.m_uid = 2u;

      cc::daddy::ParkingSpot& l_parkingSpotLeft1 = l_parkingSpotsListDaddy.m_Data[0u][1u];
      l_parkingSpotLeft1.m_position = osg::Vec2f(-3.0f, 2.3f);
      l_parkingSpotLeft1.m_length = 5.5f;
      l_parkingSpotLeft1.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL;
      l_parkingSpotLeft1.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX;
      l_parkingSpotLeft1.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotLeft1.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotLeft1.m_uid = 3u;

      cc::daddy::ParkingSpot& l_parkingSpotRight0 = l_parkingSpotsListDaddy.m_Data[1u][0u];
      l_parkingSpotRight0.m_position = osg::Vec2f(-6.0f, -2.5f);
      l_parkingSpotRight0.m_length = 3.0f;
      l_parkingSpotRight0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PERPENDICULAR;
      l_parkingSpotRight0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_RIGHT_SIDE_PSX;
      l_parkingSpotRight0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;//UNSUITABLE;
      l_parkingSpotRight0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotRight0.m_uid = 4u;

      cc::daddy::ParkingSpot& l_parkingSpotRight1 = l_parkingSpotsListDaddy.m_Data[1u][1u];
      l_parkingSpotRight1.m_position = osg::Vec2f(-3.0f, -2.5f);
      l_parkingSpotRight1.m_length = 3.0f;
      l_parkingSpotRight1.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PERPENDICULAR;
      l_parkingSpotRight1.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_RIGHT_SIDE_PSX;
      l_parkingSpotRight1.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotRight1.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;//PARKABLE_POS_OK;
      l_parkingSpotRight1.m_uid = 5u;
    }
    else if (l_park == 2u)
    {
      cc::daddy::ParkingSpot& l_parkingSpotLeft0 = l_parkingSpotsListDaddy.m_Data[0u][0u];
      l_parkingSpotLeft0.m_position = osg::Vec2f(-7.0f, 2.3f);
      l_parkingSpotLeft0.m_length = 4.7f;
      l_parkingSpotLeft0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PERPENDICULAR;
      l_parkingSpotLeft0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX;
      l_parkingSpotLeft0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotLeft0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotLeft0.m_uid = 2u;

      cc::daddy::ParkingSpot& l_parkingSpotRight0 = l_parkingSpotsListDaddy.m_Data[1u][0u];
      l_parkingSpotRight0.m_position = osg::Vec2f(-4.0f, -2.5f);
      l_parkingSpotRight0.m_length = 3.0f;
      l_parkingSpotRight0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL;
      l_parkingSpotRight0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_RIGHT_SIDE_PSX;
      l_parkingSpotRight0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;//UNSUITABLE;
      l_parkingSpotRight0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotRight0.m_uid = 5u;
    }
    else if (l_park == 3u)
    {
      cc::daddy::ParkingSpot& l_parkingSpotLeft0 = l_parkingSpotsListDaddy.m_Data[0u][0u];
      l_parkingSpotLeft0.m_position = osg::Vec2f(-7.0f, 2.3f);
      l_parkingSpotLeft0.m_length = 4.7f;
      l_parkingSpotLeft0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PERPENDICULAR;
      l_parkingSpotLeft0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX;
      l_parkingSpotLeft0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotLeft0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotLeft0.m_uid = 2u;

      cc::daddy::ParkingSpot& l_parkingSpotRight0 = l_parkingSpotsListDaddy.m_Data[1u][0u];
      l_parkingSpotRight0.m_position = osg::Vec2f(-4.0f, -2.5f);
      l_parkingSpotRight0.m_length = 3.0f;
      l_parkingSpotRight0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL;
      l_parkingSpotRight0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_RIGHT_SIDE_PSX;
      l_parkingSpotRight0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;//UNSUITABLE;
      l_parkingSpotRight0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotRight0.m_uid = 3u;
    }
    else if (l_park == 4u)
    {
      cc::daddy::ParkingSpot& l_parkingSpotLeft0 = l_parkingSpotsListDaddy.m_Data[0u][0u];
      l_parkingSpotLeft0.m_position = osg::Vec2f(-7.0f, 2.3f);
      l_parkingSpotLeft0.m_length = 4.7f;
      l_parkingSpotLeft0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PERPENDICULAR;
      l_parkingSpotLeft0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX;
      l_parkingSpotLeft0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotLeft0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_PARKABLE_POS_OK;
      l_parkingSpotLeft0.m_uid = 5u;

      cc::daddy::ParkingSpot& l_parkingSpotRight0 = l_parkingSpotsListDaddy.m_Data[1u][0u];
      l_parkingSpotRight0.m_position = osg::Vec2f(-4.0f, -2.5f);
      l_parkingSpotRight0.m_length = 3.0f;
      l_parkingSpotRight0.m_type = cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL;
      l_parkingSpotRight0.m_side = cc::daddy::ParkingSpot::PARKINGSIDE_RIGHT_SIDE_PSX;
      l_parkingSpotRight0.m_stateForward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;//UNSUITABLE;
      l_parkingSpotRight0.m_stateBackward = cc::daddy::ParkingSpot::PARKINGSTATE_UNSUITABLE;
      l_parkingSpotRight0.m_uid = 6u;
    }
    else
    {
      //Do nothing
    }
    cc::daddy::CustomDaddyPorts::sm_parkingInSpots_SenderPort.deliver();
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates a test data set of detected parking in spots" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkInTest g_parkInTestCommand;


//!
//! Set parking slots Command
//!
class SetParkingSpaceStatusCommand : public pc::util::cli::CommandCallback
{
public:
  bool invoke(std::istream& f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint32_t l_parameter0 = 0u;
    vfc::float32_t l_parameter1 = 0u;
    vfc::uint32_t l_parameter2 = 0u;
    vfc::uint32_t l_parameter3 = 0u;
    vfc::uint32_t l_parameter4 = 0u;
    vfc::uint32_t l_parameter5 = 0u;
    vfc::uint32_t l_parameter6 = 0u;
    vfc::uint32_t l_parameter7 = 0u;
    vfc::uint32_t l_parameter8 = 0u;
    vfc::uint32_t l_parameter9 = 0u;
    vfc::uint32_t l_parameter10 = 0u;
    vfc::float32_t l_parameter11 = 0u;
    vfc::uint32_t l_parameter12 = 0u;
    vfc::uint32_t l_parameter13 = 0u;
    vfc::uint32_t l_parameter14 = 0u;
    vfc::uint32_t l_parameter15 = 0u;
    vfc::uint32_t l_parameter16 = 0u;
    vfc::uint32_t l_parameter17 = 0u;
    vfc::uint32_t l_parameter18 = 0u;
    vfc::uint32_t l_parameter19 = 0u;
    vfc::uint32_t l_parameter20 = 0u;
    vfc::float32_t l_parameter21 = 0u;
    vfc::uint32_t l_parameter22 = 0u;
    vfc::uint32_t l_parameter23 = 0u;
    vfc::uint32_t l_parameter24 = 0u;
    vfc::uint32_t l_parameter25 = 0u;
    vfc::uint32_t l_parameter26 = 0u;
    vfc::uint32_t l_parameter27 = 0u;
    vfc::uint32_t l_parameter28 = 0u;
    vfc::uint32_t l_parameter29 = 0u;
    vfc::uint32_t l_parameter30 = 0u;
    vfc::float32_t l_parameter31 = 0u;
    vfc::uint32_t l_parameter32 = 0u;
    vfc::uint32_t l_parameter33 = 0u;
    vfc::uint32_t l_parameter34 = 0u;
    vfc::uint32_t l_parameter35 = 0u;
    vfc::uint32_t l_parameter36 = 0u;
    vfc::uint32_t l_parameter37 = 0u;
    vfc::uint32_t l_parameter38 = 0u;
    vfc::uint32_t l_parameter39 = 0u;
    vfc::uint32_t l_parameter40 = 0u;
    vfc::float32_t l_parameter41 = 0u;
    vfc::uint32_t l_parameter42 = 0u;
    vfc::uint32_t l_parameter43 = 0u;
    vfc::uint32_t l_parameter44 = 0u;
    vfc::uint32_t l_parameter45 = 0u;
    vfc::uint32_t l_parameter46 = 0u;
    vfc::uint32_t l_parameter47 = 0u;
    vfc::uint32_t l_parameter48 = 0u;
    vfc::uint32_t l_parameter49 = 0u;
    vfc::uint32_t l_parameter50 = 0u;
    vfc::float32_t l_parameter51 = 0u;
    vfc::uint32_t l_parameter52 = 0u;
    vfc::uint32_t l_parameter53 = 0u;
    vfc::uint32_t l_parameter54 = 0u;
    vfc::uint32_t l_parameter55 = 0u;
    vfc::uint32_t l_parameter56 = 0u;
    vfc::uint32_t l_parameter57 = 0u;
    vfc::uint32_t l_parameter58 = 0u;
    vfc::uint32_t l_parameter59 = 0u;
    vfc::uint32_t l_parameter60 = 0u;
    vfc::float32_t l_parameter61 = 0u;
    vfc::uint32_t l_parameter62 = 0u;
    vfc::uint32_t l_parameter63 = 0u;
    vfc::uint32_t l_parameter64 = 0u;
    vfc::uint32_t l_parameter65 = 0u;
    vfc::uint32_t l_parameter66 = 0u;
    vfc::uint32_t l_parameter67 = 0u;
    vfc::uint32_t l_parameter68 = 0u;
    vfc::uint32_t l_parameter69 = 0u;
    vfc::uint32_t l_parameter70 = 0u;
    vfc::float32_t l_parameter71 = 0u;
    vfc::uint32_t l_parameter72 = 0u;
    vfc::uint32_t l_parameter73 = 0u;
    vfc::uint32_t l_parameter74 = 0u;
    vfc::uint32_t l_parameter75 = 0u;
    vfc::uint32_t l_parameter76 = 0u;
    vfc::uint32_t l_parameter77 = 0u;
    vfc::uint32_t l_parameter78 = 0u;
    vfc::uint32_t l_parameter79 = 0u;
    f_input >> l_parameter0  >> l_parameter1  >> l_parameter2  >> l_parameter3  >> l_parameter4  >> l_parameter5  >> l_parameter6  >> l_parameter7  >> l_parameter8  >> l_parameter9
            >> l_parameter10 >> l_parameter11 >> l_parameter12 >> l_parameter13 >> l_parameter14 >> l_parameter15 >> l_parameter16 >> l_parameter17 >> l_parameter18 >> l_parameter19
            >> l_parameter20 >> l_parameter21 >> l_parameter22 >> l_parameter23 >> l_parameter24 >> l_parameter25 >> l_parameter26 >> l_parameter27 >> l_parameter28 >> l_parameter29
            >> l_parameter30 >> l_parameter31 >> l_parameter32 >> l_parameter33 >> l_parameter34 >> l_parameter35 >> l_parameter36 >> l_parameter37 >> l_parameter38 >> l_parameter39
            >> l_parameter40 >> l_parameter41 >> l_parameter42 >> l_parameter43 >> l_parameter44 >> l_parameter45 >> l_parameter46 >> l_parameter47 >> l_parameter48 >> l_parameter49
            >> l_parameter50 >> l_parameter51 >> l_parameter52 >> l_parameter53 >> l_parameter54 >> l_parameter55 >> l_parameter56 >> l_parameter57 >> l_parameter58 >> l_parameter59
            >> l_parameter60 >> l_parameter61 >> l_parameter62 >> l_parameter63 >> l_parameter64 >> l_parameter65 >> l_parameter66 >> l_parameter67 >> l_parameter68 >> l_parameter69
            >> l_parameter70 >> l_parameter71 >> l_parameter72 >> l_parameter73 >> l_parameter74 >> l_parameter75 >> l_parameter76 >> l_parameter77 >> l_parameter78 >> l_parameter79    // PRQA S 3803
            ; // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;    // PRQA S 3803
      getHelp(f_output);
      return false;
    }
    cc::daddy::ParkAPA_ParkSpace_t& l_container = cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.reserve();
    l_container.m_Data[0][0].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter0); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][0].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter1); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter2); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][0].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter3); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][0].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter4); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter5); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter6); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter7); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter8); // PRQA S 3013
    l_container.m_Data[0][0].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter9); // PRQA S 3013

    l_container.m_Data[0][1].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter10); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][1].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter11); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter12); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][1].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter13); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][1].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter14); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter15); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter16); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter17); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter18); // PRQA S 3013
    l_container.m_Data[0][1].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter19); // PRQA S 3013

    l_container.m_Data[0][2].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter20); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][2].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter21); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter22); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][2].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter23); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][2].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter24); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter25); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter26); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter27); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter28); // PRQA S 3013
    l_container.m_Data[0][2].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter29); // PRQA S 3013

    l_container.m_Data[0][3].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter30); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][3].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter31); // PRQA S 3013
    l_container.m_Data[0][3].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter32); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][3].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter33); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[0][3].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter34); // PRQA S 3013
    l_container.m_Data[0][3].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter35); // PRQA S 3013
    l_container.m_Data[0][3].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter36); // PRQA S 3013
    l_container.m_Data[0][3].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter37); // PRQA S 3013
    l_container.m_Data[0][3].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter38); // PRQA S 3013
    l_container.m_Data[0][3].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter39); // PRQA S 3013

    l_container.m_Data[1][0].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter40); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][0].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter41); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter42); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][0].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter43); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][0].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter44); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter45); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter46); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter47); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter48); // PRQA S 3013
    l_container.m_Data[1][0].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter49); // PRQA S 3013

    l_container.m_Data[1][1].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter50); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][1].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter51); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter52); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][1].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter53); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][1].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter54); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter55); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter56); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter57); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter58); // PRQA S 3013
    l_container.m_Data[1][1].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter59); // PRQA S 3013

    l_container.m_Data[1][2].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter60); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][2].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter61); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter62); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][2].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter63); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][2].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter64); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter65); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter66); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter67); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter68); // PRQA S 3013
    l_container.m_Data[1][2].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter69); // PRQA S 3013

    l_container.m_Data[1][3].m_APA_PrkgSlot             = static_cast<cc::target::common::EPARKSlotStsR2L>               (l_parameter70); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][3].m_APA_PrkgSlotSta_f32      = static_cast<vfc::float32_t>                (l_parameter71); // PRQA S 3013
    l_container.m_Data[1][3].m_APA_PSType               = static_cast<cc::target::common::EFAPAParkSlotType>             (l_parameter72); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][3].m_APA_ParkManeuverType     = static_cast<cc::target::common::rbp_Type_ParkManeuverType_en>  (l_parameter73); // PRQA S 3013 // PRQA S 4899
    l_container.m_Data[1][3].m_APA_PSCorner2X_i16       = static_cast<vfc::int16_t>                  (l_parameter74); // PRQA S 3013
    l_container.m_Data[1][3].m_APA_PSCorner2Y_i16       = static_cast<vfc::int16_t>                  (l_parameter75); // PRQA S 3013
    l_container.m_Data[1][3].m_APA_PSLength_u16         = static_cast<vfc::uint16_t>                 (l_parameter76); // PRQA S 3013
    l_container.m_Data[1][3].m_APA_PSCorner1X_i16       = static_cast<vfc::int16_t>                  (l_parameter77); // PRQA S 3013
    l_container.m_Data[1][3].m_APA_PSCorner1Y_i16       = static_cast<vfc::int16_t>                  (l_parameter78); // PRQA S 3013
    l_container.m_Data[1][3].m_APA_PSId_u16             = static_cast<vfc::uint16_t>                 (l_parameter79); // PRQA S 3013

    cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.deliver();
    return true;
  }
  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Set parking space status & angle & type & maneuver Type" << newline;    // PRQA S 3803
  }
  void getHelp(std::ostream& f_output) const override
  {
    f_output << "8 slots including status <x> angle <x> type <x> naneuver type <x> for each" << newline; // PRQA S 3803
  }
};

static SetParkingSpaceStatusCommand g_setParkingSpaceCommand;


//!
//! ParkEndPostionSearchingCommand
//!
class ParkEndPostionSearching : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_x_cm = 0;
    vfc::int32_t l_y_cm = 0;
    vfc::int32_t l_phi_deg = 0;

    f_input >> l_x_cm >> l_y_cm >> l_phi_deg;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkEndPositionSearchingDaddy_t& l_parkingEndPostionSearchingDaddy = cc::daddy::CustomDaddyPorts::sm_ParkEndPositionSearchingDaddy_SenderPort.reserve();
    l_parkingEndPostionSearchingDaddy.m_Data.m_x = static_cast<vfc::int16_t>(static_cast<vfc::float32_t>(l_x_cm)); // PRQA S 3016
    l_parkingEndPostionSearchingDaddy.m_Data.m_y = static_cast<vfc::int16_t>(static_cast<vfc::float32_t>(l_y_cm)); // PRQA S 3016
    const vfc::float32_t l_temp = static_cast<vfc::float32_t>(l_phi_deg)*static_cast<vfc::float32_t> (osg::PI)/180.0f*4096.0f;
    l_parkingEndPostionSearchingDaddy.m_Data.m_phi = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016
    cc::daddy::CustomDaddyPorts::sm_ParkEndPositionSearchingDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park end postion during searching" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkEndPostionSearching g_parkEndPostionSearchingCommand;


//!
//! ParkFinalEndPostionCommand
//!
class ParkFinalEndPosition : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_x_mm = 0;
    vfc::int32_t l_y_mm = 0;
    vfc::int32_t l_phi_deg = 0;

    f_input >> l_x_mm >> l_y_mm >> l_phi_deg;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkFinalEndPositionDaddy_t& l_pData = cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.reserve();

    vfc::float32_t l_temp = static_cast<vfc::float32_t>(l_x_mm)/1000.0f*1024.0f;
    l_pData.m_Data.m_x = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

    l_temp = static_cast<vfc::float32_t>(l_y_mm)/1000.0f*1024.0f;
    l_pData.m_Data.m_y = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

    l_temp = static_cast<vfc::float32_t>(l_phi_deg)*static_cast<vfc::float32_t> (osg::PI)/180.0f*4096.0f;
    l_pData.m_Data.m_phi = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

    cc::daddy::CustomDaddyPorts::sm_ParkFinalEndPositionDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park final end postion during manoeuver" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkFinalEndPosition g_parkFinalEndPositionCommand;


//!
//! ParkFinalEndPostionCommand
//!
class ParkCurMoveTargetPosition : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::int32_t l_x_mm = 0;
    vfc::int32_t l_y_mm = 0;
    vfc::int32_t l_phi_deg = 0;

    f_input >> l_x_mm >> l_y_mm >> l_phi_deg;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkCurMoveTargetPositionDaddy_t& l_pData = cc::daddy::CustomDaddyPorts::sm_ParkCurMoveTargetPositionDaddy_SenderPort.reserve();

    vfc::float32_t l_temp = static_cast<vfc::float32_t>(l_x_mm)/1000.0f*1024.0f;
    l_pData.m_Data.m_x = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

    l_temp = static_cast<vfc::float32_t>(l_y_mm)/1000.0f*1024.0f;
    l_pData.m_Data.m_y = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

    l_temp = static_cast<vfc::float32_t>(l_phi_deg)*static_cast<vfc::float32_t> (osg::PI)/180.0f*4096.0f;
    l_pData.m_Data.m_phi = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

    cc::daddy::CustomDaddyPorts::sm_ParkCurMoveTargetPositionDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park current move target position during manoeuver" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkCurMoveTargetPosition g_parkCurMoveTargetPositionCommand;


//!
//! ParkCarmoveNumberCommand
//!
class ParkCarmoveNumber : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint32_t l_parameter = 0u;

    f_input >> l_parameter;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkCarmoveNumberDaddy_t& l_pData = cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.reserve();
    l_pData.m_Data = static_cast<uint8_t>(l_parameter);
    cc::daddy::CustomDaddyPorts::sm_ParkCarmoveNumberDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park car moving number" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park car moving number is steps: 1..2..3..4..." << newline;  // PRQA S 3803
  }
};

static ParkCarmoveNumber g_parkCarmoveNumberCommand;


//!
//! ParkCartravelDistDesiredCommand
//!
class ParkCartravelDistDesired : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint32_t l_parameter = 0u;

    f_input >> l_parameter;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkCartravelDistDesiredDaddy_t& l_pData = cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.reserve();
    l_pData.m_Data = static_cast<vfc::int16_t>(l_parameter);
    cc::daddy::CustomDaddyPorts::sm_ParkCartravelDistDesiredDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park car travel distance desired" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park car travel distance desired, in cm" << newline;  // PRQA S 3803
  }
};

static ParkCartravelDistDesired g_parkCartravelDistDesiredCommand;


//!
//! ParkIsLastMoveCommand
//!
class ParkIsLastMove : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&  f_input, std::ostream& f_output, const pc::util::cli::CommandLineInterface&) override
  {
    vfc::uint32_t l_parameter = 0u;

    f_input >> l_parameter;  // PRQA S 3803
    if (f_input.fail())
    {
      f_output << parseError;  // PRQA S 3803
      getHelp(f_output);
      return false;
    }

    cc::daddy::ParkIsLastMoveDaddy_t& l_pData = cc::daddy::CustomDaddyPorts::sm_ParkIsLastMoveDaddy_SenderPort.reserve();
    l_pData.m_Data = static_cast<bool>(l_parameter);
    cc::daddy::CustomDaddyPorts::sm_ParkIsLastMoveDaddy_SenderPort.deliver();

    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Generates park whether it is last move" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output << "<park lots (1 = 4 lots | 2 = 2 lots )>" << newline;  // PRQA S 3803
  }
};

static ParkIsLastMove g_parkIsLastMoveCommand;


class ParkPmaActionPointDistanceCommand : public pc::util::cli::NumericSignalSetter<vfc::float32_t>
{
public:

  ParkPmaActionPointDistanceCommand()
    : pc::util::cli::NumericSignalSetter<vfc::float32_t>{"PMA ActionPoint distance"}
  {
  }

  void sendSignal(vfc::float32_t f_value) override
  {
    cc::daddy::PMA_TravelDistDesiredDaddy& l_actionPointDistance = cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.reserve();
    l_actionPointDistance.m_Data = f_value;
    cc::daddy::CustomDaddyPorts::sm_PMA_TravelDistDesired_SenderPort.deliver();
  }
};
ParkPmaActionPointDistanceCommand g_ParkPmaActionPointDistanceCommand;


class ParkGuideMovesLeftCommand : public pc::util::cli::NumericSignalSetter<vfc::float32_t>
{
public:

  ParkGuideMovesLeftCommand()
    : pc::util::cli::NumericSignalSetter<vfc::float32_t>{"park moves left number during guidance"}
  {
  }

  void sendSignal(vfc::float32_t f_value) override
  {
    cc::daddy::ParkGuideMovesLeftDaddy_t& l_MovesLeftNumber = cc::daddy::CustomDaddyPorts::sm_ParkGuideMovesLeftDaddy_SenderPort.reserve();
    l_MovesLeftNumber.m_Data = f_value; // PRQA S 3011
    cc::daddy::CustomDaddyPorts::sm_ParkGuideMovesLeftDaddy_SenderPort.deliver();
  }
};
ParkGuideMovesLeftCommand g_ParkGuideMovesLeftCommand;


//!
//! ParkCommand
//!
class ParkCommand : public pc::util::cli::CommandCallbackGroup
{
public:

  ParkCommand() // PRQA S 4054
  {
    addCommand("testin",       &g_parkInTestCommand);
    addCommand("space",        &g_setParkingSpaceCommand);
    addCommand("endsearching", &g_parkEndPostionSearchingCommand);
    addCommand("endfinal",     &g_parkFinalEndPositionCommand);
    addCommand("target",       &g_parkCurMoveTargetPositionCommand);
    addCommand("lastmove",     &g_parkIsLastMoveCommand);
    addCommand("movenum",      &g_parkCarmoveNumberCommand);
    addCommand("traveldist",   &g_parkCartravelDistDesiredCommand);
    addCommand("distdesired",  &g_ParkPmaActionPointDistanceCommand);
    addCommand("movesleft",    &g_ParkGuideMovesLeftCommand);
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Parking spot selection simulation, etc." << newline;  // PRQA S 3803
  }
};

static pc::util::cli::Command<ParkCommand> g_parkCommand("park");

} // namespace parkingspots
} // namespace assets
} // namespace cc

