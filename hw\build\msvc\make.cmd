::==============================================================================
:: Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
:: This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
:: distribution is an offensive act against international law and may be
:: prosecuted under federal law. Its content is company confidential.
::==============================================================================
@echo off

if not "%VisualStudioVersion%"=="14.0" (
  call "C:\Program Files\Microsoft Visual Studio\2022\Preview\VC\Auxiliary\Build\vcvarsall.bat" amd64
)

set COMPILER_NAME=msvc

:: Operating System
set OS_MODULE=os_windows

:: Target module (standalone, less, ...)
if not defined TARGET_MODULE (
  set TARGET_MODULE=target_standalone
)

::echo "Start time: %time%" > CHRONO.TXT
make.exe %*
::echo "  End time: %time%" >> CHRONO.TXT
