//-------------------------------------------------------------------------------
// Copyright (c) 2019 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PTSOVERLAY_PTSANIMATION_H
#define CC_ASSETS_PTSOVERLAY_PTSANIMATION_H

#include "cc/assets/ptsoverlay/inc/PtsOverlay.h"
#include "cc/views/parkview/inc/ParkView.h"
#include "cc/core/inc/CustomScene.h"

#include "pc/svs/animation/inc/Animation.h"
#include "pc/svs/core/inc/View.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

namespace cc
{
namespace assets
{
namespace ptsoverlay
{

class PtsPositions : public pc::util::coding::ISerializable
{
public:

  enum PtsStates
  {
    PTSPOSITIONS_PARKIN,
    PTSPOSITIONS_PARKOUT_PARA_LEFT,
    PTSPOSITIONS_PARKOUT_PARA_RIGHT,
    PTSPOSITIONS_PARKOUT_CROSS_FRONT,
    PTSPOSITIONS_PARKOUT_CROSS_REAR,
    PTSPOSITIONS_DEFAULT3D,
    PTSPOSITIONS_DEFAULT2D
  };

  PtsPositions()
    : m_parkIn(1.2f, 1.f, 0.7f, 0.0f)             // scaleX, scaleY, translationX, translationY
    , m_parkOutParaLeft(1.1f, 1.f, 0.3f, -0.1f)    // ParkOut is for all the same for now but can be modified to change the PTS size specific
    , m_parkOutParaRight(1.1f, 1.f, 0.3f, 0.1f)
    , m_parkOutCrossFront(1.1f, 1.1f, 0.5f, 0.0f)
    , m_parkOutCrossRear(1.1f, 1.f, 0.5f, 0.0f)
    , m_default3D(1.f, 1.f, 0.f, 0.f)
    , m_default2D(1.f, 1.f, 0.f, 0.f)
    , m_animationDuration(0.5f)   // it would also be possible to calculate each time the AnimationDuration like in CameraFlightPathGenerator::calcAniDuration()
    , m_heightMinThreshold(0.25f)
    {
    }

    SERIALIZABLE(PtsPositions)
    {
      ADD_MEMBER(osg::Vec4f, parkIn);
      ADD_MEMBER(osg::Vec4f, parkOutParaLeft);
      ADD_MEMBER(osg::Vec4f, parkOutParaRight);
      ADD_MEMBER(osg::Vec4f, parkOutCrossFront);
      ADD_MEMBER(osg::Vec4f, parkOutCrossRear);
      ADD_MEMBER(osg::Vec4f, default3D);
      ADD_MEMBER(osg::Vec4f, default2D);
      ADD_FLOAT_MEMBER(animationDuration);
      ADD_FLOAT_MEMBER(heightMinThreshold);
    }

    osg::Vec4f m_parkIn;
    osg::Vec4f m_parkOutParaLeft;
    osg::Vec4f m_parkOutParaRight;
    osg::Vec4f m_parkOutCrossFront;
    osg::Vec4f m_parkOutCrossRear;
    osg::Vec4f m_default3D;
    osg::Vec4f m_default2D;

    float m_animationDuration;
    float m_heightMinThreshold;
};

extern pc::util::coding::Item<PtsPositions> g_ptsPositions;

///
/// PtsAnimation
///
class PtsAnimation : public pc::animation::Animation
{
public:

  PtsAnimation(
    pc::core::View* f_defaultView,
    PtsPositions::PtsStates f_startState,
    PtsPositions::PtsStates f_targetState,
    bool f_animateParkView = true);

  virtual float getFixedDuration() const override;

  virtual bool hasFixedDuration() const override;

  virtual bool supportsCancellation() const override;

  void initAnimation();

  osg::Vec4f getStatePosition(bool f_start = true) const;

  //static PtsPositions::PtsStates determinePtsState(cc::views::parkview::ParkOutView::ParkoutStates f_parkOutState);

  void onCameraFlightEnded();

private:

  virtual void onBegin() override;

  virtual bool onUpdate(float f_animationTime) override;

  osg::ref_ptr<pc::core::View> m_defaultView;

  PtsPositions::PtsStates m_startState;
  PtsPositions::PtsStates m_targetState;

  bool m_animateParkView; // tells us if the parkView is animated, else the defaultView
};


///
/// CameraFlightEndedAction
///
class CameraFlightEndedAction : public pc::animation::Action
{
public:

  CameraFlightEndedAction(PtsAnimation* f_ptsAnimation);

  virtual void onAction() override;

private:

  osg::ref_ptr<PtsAnimation> m_ptsAnimation;
};

} // namespace ptsoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTSOVERLAY_PTSANIMATION_H
