//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_TEST_BUTTON_H
#define CC_ASSETS_BUTTON_TEST_BUTTON_H

#include "cc/assets/button/inc/Button.h"

namespace cc
{
namespace assets
{
namespace button
{

class TestButton : public Button
{
public:
    TestButton(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView=nullptr);

protected:
    void update() override;

private:
    void onInvalid() override;
    void onUnavailable() override;
    void onAvailable() override;
    void onPressed() override;
    void onReleased() override;

private:
    pc::core::Framework* m_framework;
    vfc::uint32_t m_modifiedCount = ~0u;
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_TEST_BUTTON_H
