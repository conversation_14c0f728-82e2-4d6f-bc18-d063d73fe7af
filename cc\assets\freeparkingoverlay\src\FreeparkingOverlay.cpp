//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname:
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EPF2-CN)
//  Department: CC-DA/EPF
//=============================================================================
/// @swcomponent
/// @file  FreeParkingOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/freeparkingoverlay/inc/FreeparkingOverlay.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingUtils.h"
#include "cc/assets/freeparkingoverlay/inc/FreeparkingRotateButton.h"

#include "pc/generic/util/logging/inc/Logging.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "pc/svs/core/inc/ShaderManager.h"
// #include "pc/svs/util/math/FloatComp.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/imgui/inc/imgui_manager.h"

#include "osgDB/ReadFile"
#include "osg/Depth"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osg/Vec2ui"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

static osg::ref_ptr<osg::Texture2D> g_defaultTexture;
static osg::ref_ptr<osg::Texture2D> g_availableTexture;
static osg::ref_ptr<osg::Texture2D> g_movingTexture;
static osg::ref_ptr<osg::Texture2D> g_unavailableTexture;
static osg::ref_ptr<osg::Texture2D> g_judgingTexture;
static osg::ref_ptr<osg::Geode>     g_fpPlaneGeode;
static osg::ref_ptr<osg::StateSet>  g_sharedParkingSpotStateSet;

pc::util::coding::Item<FreeparkingSettings> g_freeparkingSettings("Freeparking");

static osg::Texture2D* createTexture(const std::string& f_filename)
{
    osg::Image* const l_image = osgDB::readImageFile(f_filename);
    if (l_image == nullptr)
    {
        // XLOG_ERROR_OS(g_AppContext) << "Failed to load image file" << f_filename << XLOG_ENDL;
        return nullptr;
    }
    // assert(l_image);
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    // set texture options
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setUseHardwareMipMapGeneration(true);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
}

//!
//! FreeparkingPlane
//!
FreeparkingPlane::FreeparkingPlane()
    : m_visible(true)
    , m_spotSide(cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX)
    , m_spotType(cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL)
    , m_textureState(cc::assets::parkingspots::ParkingSpot::POTENTIAL)
{
    if (!g_fpPlaneGeode.valid())
    {
        // build the plane for the parking slot itself
        osg::Geometry* const l_geometry = new osg::Geometry;
        l_geometry->setUseDisplayList(false);
        l_geometry->setUseVertexBufferObjects(true);

        osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
        (*l_vertices)[0u]                = osg::Vec3f(-0.5f, -0.5f, g_freeparkingSettings->m_groundLevel);
        (*l_vertices)[1u]                = osg::Vec3f(0.5f, -0.5f, g_freeparkingSettings->m_groundLevel);
        (*l_vertices)[2u]                = osg::Vec3f(0.5f, 0.5f, g_freeparkingSettings->m_groundLevel);
        (*l_vertices)[3u]                = osg::Vec3f(-0.5f, 0.5f, g_freeparkingSettings->m_groundLevel);
        l_geometry->setVertexArray(l_vertices);

        osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
        (*l_texCoords)[0u]                = osg::Vec2f(0.0f, 0.0f);
        (*l_texCoords)[1u]                = osg::Vec2f(0.0f, 1.0f);
        (*l_texCoords)[2u]                = osg::Vec2f(1.0f, 1.0f);
        (*l_texCoords)[3u]                = osg::Vec2f(1.0f, 0.0f);
        l_geometry->setTexCoordArray(0u, l_texCoords);

        osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
        (*l_colours)[0u]                = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f);
        l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

        osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
        (*l_normals)[0u]                = osg::Z_AXIS;
        l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

        osg::DrawElementsUByte* const l_indices =
            new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
        (*l_indices)[0u] = 1u;
        (*l_indices)[1u] = 0u;
        (*l_indices)[2u] = 2u;
        (*l_indices)[3u] = 2u;
        (*l_indices)[4u] = 0u;
        (*l_indices)[5u] = 3u;
        l_geometry->addPrimitiveSet(l_indices); // PRQA S 3803

        g_fpPlaneGeode = new osg::Geode;
        g_fpPlaneGeode->addDrawable(l_geometry); // PRQA S 3803
    }
    osg::Group::addChild(g_fpPlaneGeode.get()); // PRQA S 3803
}

FreeparkingPlane::FreeparkingPlane(const FreeparkingPlane& f_other, const osg::CopyOp& f_copyOp)
    : osg::MatrixTransform(f_other, f_copyOp)
    , m_visible(f_other.m_visible)
    , m_spotType(cc::daddy::ParkingSpot::PARKINGTYPE_PARALLEL)
    , m_spotSide(cc::daddy::ParkingSpot::PARKINGSIDE_LEFT_SIDE_PSX)
    , m_textureState(cc::assets::parkingspots::ParkingSpot::POTENTIAL) // PRQA S 3143  #code looks fine
{
}

FreeparkingPlane::~FreeparkingPlane() = default;

void FreeparkingPlane::update(FreeparkingOverlay* f_freeparkingOverlay, const bool /*f_isLeft*/)
{
    if (f_freeparkingOverlay == nullptr)
    {
        XLOG_ERROR(g_AppContext, "FreeparkingPlane::update - f_freeparkingOverlay is nullptr");
        return;
    }
    osg::StateSet* const l_stateSet = getOrCreateStateSet();

    osg::Uniform* const l_mipmapBiasUniform = l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT);
    l_mipmapBiasUniform->set(g_freeparkingSettings->m_mipmapBias); // PRQA S 3803

    const osg::Vec2f         l_size            = f_freeparkingOverlay->getSize();
    constexpr vfc::float32_t l_typeAngleOffset = 0.0f;

    getOrCreateStateSet()->setTextureAttribute(
        0u,
        f_freeparkingOverlay->getParkingPlanState() == ParkingPlaneState::DEFAULT     ? g_movingTexture.get()
        : f_freeparkingOverlay->getParkingPlanState() == ParkingPlaneState::AVAILABLE ? g_availableTexture.get()
                                                                                      : g_movingTexture.get(),
        osg::StateAttribute::OVERRIDE);

    f_freeparkingOverlay->setMatrix(
        osg::Matrix::rotate(osg::DegreesToRadians(l_typeAngleOffset), osg::Z_AXIS) * f_freeparkingOverlay->getMatrix());
    m_visible = f_freeparkingOverlay->getVisibility();
    const osg::Vec3f l_scale(l_size, 1.0f);
    setMatrix(osg::Matrix::scale(l_scale));
}

void FreeparkingPlane::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
    {
        if (m_visible)
        {
            osg::MatrixTransform::traverse(f_nv);
        }
    }
    else
    {
        osg::MatrixTransform::traverse(f_nv);
    }
}

//!
//! FrontPlane
//!
FrontPlane::FrontPlane()
    : FreeparkingPlane()
{
    if (g_defaultTexture == nullptr)
    {
        g_defaultTexture = createTexture(g_freeparkingSettings->m_defalutTexture);
    }

    const auto stateset = getOrCreateStateSet();
    stateset->setTextureAttribute(0u, g_defaultTexture.get());
}

void FrontPlane::update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft)
{
    if (f_freeparkingOverlay == nullptr)
    {
        XLOG_ERROR(g_AppContext, "FrontPlane::update - f_freeparkingOverlay is nullptr");
        return;
    }
    osg::StateSet* const l_stateSet = getOrCreateStateSet();

    vfc::float32_t l_typeAngleOffset = 0.0f;

    const vfc::uint32_t l_girdSizeIdx = f_freeparkingOverlay->getType(); // PRQA S 3803
    if (l_girdSizeIdx == cc::daddy::ParkingSpot::Type::PARKINGTYPE_PERPENDICULAR)
    {
        l_typeAngleOffset = (f_isLeft) ? 90.0f : -90.0f;
    }

    // if (IMGUI_GET_CHECKBOX_BOOL("FreeparkingOverlay", "Add 90"))
    // {
    //   l_typeAngleOffset = 90.0f;
    // }
    // if (IMGUI_GET_CHECKBOX_BOOL("FreeparkingOverlay", "Add -90"))
    // {
    //   l_typeAngleOffset = -90.0f;
    // }

    osg::Uniform* const l_mipmapBiasUniform = l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT);
    l_mipmapBiasUniform->set(g_freeparkingSettings->m_mipmapBias); // PRQA S 3803
    m_visible = (f_freeparkingOverlay->getParkable() == EFreeParkingSlotState::AVAILABLE) &&
                f_freeparkingOverlay->getVisibility();

    const osg::Vec3f l_scale(g_freeparkingSettings->m_pSize, 1.0f);
    setMatrix(osg::Matrix::scale(l_scale) * osg::Matrix::rotate(osg::DegreesToRadians(l_typeAngleOffset), osg::Z_AXIS));
}

//!
//! FreeparkingWarningOverlay
//!

FreeparkingWarningOverlay::FreeparkingWarningOverlay(osg::Vec2f f_size)
    : osg::MatrixTransform{}
    , FreeparkingNode{}
    , m_size(f_size)
{
    init();
}

void FreeparkingWarningOverlay::init()
{
    removeChildren(0u, getNumChildren());
    //! LEFT GEOMETRY
    pc::core::TextureShaderProgramDescriptor l_advancedTexShader("advancedTex");
    m_texture = createTexture(g_freeparkingSettings->m_unavailableTexture);
    {
        m_topLeftGeode     = createGeode(getTextureSelection(WarningArea::TopLeft));
        m_middleLeftGeode  = createGeode(getTextureSelection(WarningArea::MiddleLeft));
        m_bottomLeftGeode  = createGeode(getTextureSelection(WarningArea::BottomLeft));
        m_topRightGeode    = createGeode(getTextureSelection(WarningArea::TopRight));
        m_middleRightGeode = createGeode(getTextureSelection(WarningArea::MiddleRight));
        m_bottomRightGeode = createGeode(getTextureSelection(WarningArea::BottomRight));
        addChild(m_topLeftGeode);
        addChild(m_middleLeftGeode);
        addChild(m_bottomLeftGeode);
        addChild(m_topRightGeode);
        addChild(m_middleRightGeode);
        addChild(m_bottomRightGeode);
    }
    updateGeometry();
}

osg::Vec4f FreeparkingWarningOverlay::getTextureSelection(WarningArea f_area) const
{
    osg::Vec4f l_textureSelection{};
    switch (f_area)
    {
    case WarningArea::MiddleLeft:
    {
        vfc::float32_t l_textureLength =
            1.0f - g_freeparkingSettings->m_lowerPercentage - g_freeparkingSettings->m_upperPercentage;
        l_textureSelection = {0.5f, l_textureLength, 0.0f, g_freeparkingSettings->m_lowerPercentage};
        break;
    }
    case WarningArea::BottomLeft:
    {
        l_textureSelection = {0.5f, g_freeparkingSettings->m_lowerPercentage, 0.0f, 0.0f};
        break;
    }
    case WarningArea::TopRight:
    {
        l_textureSelection = {
            0.5f, g_freeparkingSettings->m_upperPercentage, 0.5f, 1.0f - g_freeparkingSettings->m_upperPercentage};
        break;
    }
    case WarningArea::MiddleRight:
    {
        vfc::float32_t l_textureLength =
            1.0f - g_freeparkingSettings->m_lowerPercentage - g_freeparkingSettings->m_upperPercentage;
        l_textureSelection = {0.5f, l_textureLength, 0.5f, g_freeparkingSettings->m_lowerPercentage};
        break;
    }
    case WarningArea::BottomRight:
    {
        l_textureSelection = {0.5f, g_freeparkingSettings->m_lowerPercentage, 0.5f, 0.0f};
        break;
    }
    case WarningArea::TopLeft:
    default:
    {
        l_textureSelection = {
            0.5f, g_freeparkingSettings->m_upperPercentage, 0.0f, 1.0f - g_freeparkingSettings->m_upperPercentage};
        break;
    }
    }
    return l_textureSelection;
}

osg::Geode* FreeparkingWarningOverlay::createGeode(osg::Vec4f f_textureSelection) const
{
    osg::Geode*    l_geode    = new osg::Geode{};
    osg::Geometry* l_geometry = createGeometry();
    osg::StateSet* l_stateSet = createStateSet(f_textureSelection);
    l_stateSet->setTextureAttribute(0u, m_texture.get());
    l_geometry->setStateSet(l_stateSet);
    l_geode->addDrawable(l_geometry);
    return l_geode;
}

osg::StateSet* FreeparkingWarningOverlay::createStateSet(osg::Vec4f f_textureSelection) const
{
    osg::StateSet* l_stateSet = new osg::StateSet{};
    // l_stateSet->setTextureAttribute(0u, m_texture.get());
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(core::RENDERBIN_ORDER_FREEPARKING_OVERLAY, "RenderBin");
    // l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4) ->set(f_textureSelection);
    l_stateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f);
    l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);
    return l_stateSet;
}

osg::Geometry* FreeparkingWarningOverlay::createGeometry() const
{
    /**   texture selection
     *    ***********
     *    *         *
     *    *         *       y
     *    *         *       ^
     *    *         *       |
     *    O**********       ----> x
     *    (0, 0)
     */
    vfc::float32_t  l_leftTexture   = 0.0f;
    vfc::float32_t  l_rightTexture  = 1.0f;
    vfc::float32_t  l_bottomTexture = 0.0f;
    vfc::float32_t  l_topTexture    = 1.0f;
    osg::Geometry*  l_geometry      = new osg::Geometry{};
    osg::Vec3Array* l_vertices      = new osg::Vec3Array{};
    l_geometry->setVertexArray(l_vertices);
    osg::Vec2Array* l_texCoords = new osg::Vec2Array{};
    l_texCoords->push_back(osg::Vec2f{l_leftTexture, l_bottomTexture});
    l_texCoords->push_back(osg::Vec2f{l_rightTexture, l_bottomTexture});
    l_texCoords->push_back(osg::Vec2f{l_rightTexture, l_topTexture});
    l_texCoords->push_back(osg::Vec2f{l_leftTexture, l_topTexture});
    l_geometry->setTexCoordArray(0u, l_texCoords);
    osg::DrawElementsUByte* l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::QUADS, 4u);
    (*l_indices)[0u]                  = 0u;
    (*l_indices)[1u]                  = 1u;
    (*l_indices)[2u]                  = 2u;
    (*l_indices)[3u]                  = 3u;
    l_geometry->addPrimitiveSet(l_indices);
    return l_geometry;
}

void FreeparkingWarningOverlay::updateGeometry()
{
    /**
     * @brief Geometry Layout
     *
     *          |            top           |                    ^ x+
     *          x__________________________x____________        |
     *          |                          |                    |
     *     upper|                          |               y+<--o
     *          |__________________________|
     *          |                          |
     *          |                          |
     *          |                          |
     *     left |          center          | right     length
     *          |                          |
     *          |                          |
     *          |                          |
     *          |__________________________|
     *          |                          |
     *     lower|                          |
     *          x__________________________x____________
     *          |           bottom         |
     *          |                          |
     *          |           width          |
     *
     */
    const vfc::float32_t l_width  = m_size.y();
    const vfc::float32_t l_length = m_size.x();
    const vfc::float32_t l_left   = 0.5f * l_width;
    const vfc::float32_t l_right  = -0.5f * l_width;
    const vfc::float32_t l_bottom = -0.5f * l_length;
    const vfc::float32_t l_top    = 0.5f * l_length;
    const vfc::float32_t l_center = 0.0f;
    const vfc::float32_t l_z      = g_freeparkingSettings->m_groundLevel;

    //! TOP LEFT
    {
        const vfc::float32_t l_upper    = l_top;
        const vfc::float32_t l_lower    = l_top - l_length * g_freeparkingSettings->m_upperPercentage;
        auto                 l_geometry = m_topLeftGeode->getDrawable(0u)->asGeometry();
        const auto           l_vertices = static_cast<osg::Vec3Array*>(l_geometry->getVertexArray());
        l_vertices->clear();
        l_vertices->push_back(osg::Vec3f{l_lower, l_left, l_z});
        l_vertices->push_back(osg::Vec3f{l_lower, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_left, l_z});
        l_vertices->dirty();
        l_geometry->dirtyBound();
        osg::StateSet* l_stateSet = l_geometry->getStateSet();
        osg::Vec4f     l_textureSelection{
            0.5f,                                           // width
            g_freeparkingSettings->m_upperPercentage,       // length
            0.0f,                                           // left
            1.0f - g_freeparkingSettings->m_upperPercentage // bottom
        };
        l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(l_textureSelection);
    }
    //! BOTTOM LEFT
    {
        const vfc::float32_t l_upper    = l_bottom + l_length * g_freeparkingSettings->m_lowerPercentage;
        const vfc::float32_t l_lower    = l_bottom;
        auto                 l_geometry = m_bottomLeftGeode->getDrawable(0u)->asGeometry();
        const auto           l_vertices = static_cast<osg::Vec3Array*>(l_geometry->getVertexArray());
        l_vertices->clear();
        l_vertices->push_back(osg::Vec3f{l_lower, l_left, l_z});
        l_vertices->push_back(osg::Vec3f{l_lower, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_left, l_z});
        l_vertices->dirty();
        l_geometry->dirtyBound();
        osg::StateSet* l_stateSet = l_geometry->getStateSet();
        osg::Vec4f     l_textureSelection{
            0.5f,                                     // width
            g_freeparkingSettings->m_lowerPercentage, // length
            0.0f,                                     // left
            0.0f                                      // bottom
        };
        l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(l_textureSelection);
    }
    //! MIDDLE LEFT
    {
        const vfc::float32_t l_upper    = l_top - l_length * g_freeparkingSettings->m_upperPercentage;
        const vfc::float32_t l_lower    = l_bottom + l_length * g_freeparkingSettings->m_lowerPercentage;
        auto                 l_geometry = m_middleLeftGeode->getDrawable(0u)->asGeometry();
        const auto           l_vertices = static_cast<osg::Vec3Array*>(l_geometry->getVertexArray());
        l_vertices->clear();
        l_vertices->push_back(osg::Vec3f{l_lower, l_left, l_z});
        l_vertices->push_back(osg::Vec3f{l_lower, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_left, l_z});
        l_vertices->dirty();
        l_geometry->dirtyBound();
        osg::StateSet* l_stateSet = l_geometry->getStateSet();
        vfc::float32_t l_textureLength =
            1.0f - g_freeparkingSettings->m_lowerPercentage - g_freeparkingSettings->m_upperPercentage;
        osg::Vec4f l_textureSelection{
            0.5f,                                    // width
            l_textureLength,                         // length
            0.0f,                                    // left
            g_freeparkingSettings->m_lowerPercentage // bottom
        };
        l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(l_textureSelection);
    }

    //! TOP RIGHT
    {
        const vfc::float32_t l_upper    = l_top;
        const vfc::float32_t l_lower    = l_top - l_length * g_freeparkingSettings->m_upperPercentage;
        auto                 l_geometry = m_topRightGeode->getDrawable(0u)->asGeometry();
        const auto           l_vertices = static_cast<osg::Vec3Array*>(l_geometry->getVertexArray());
        l_vertices->clear();
        l_vertices->push_back(osg::Vec3f{l_lower, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_lower, l_right, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_right, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_center, l_z});
        l_vertices->dirty();
        l_geometry->dirtyBound();
        osg::StateSet* l_stateSet = l_geometry->getStateSet();
        osg::Vec4f     l_textureSelection{
            0.5f,                                           // width
            g_freeparkingSettings->m_upperPercentage,       // length
            0.5f,                                           // left
            1.0f - g_freeparkingSettings->m_upperPercentage // bottom
        };
        l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(l_textureSelection);
    }
    //! BOTTOM RIGHT
    {
        const vfc::float32_t l_upper    = l_bottom + l_length * g_freeparkingSettings->m_lowerPercentage;
        const vfc::float32_t l_lower    = l_bottom;
        auto                 l_geometry = m_bottomRightGeode->getDrawable(0u)->asGeometry();
        const auto           l_vertices = static_cast<osg::Vec3Array*>(l_geometry->getVertexArray());
        l_vertices->clear();
        l_vertices->push_back(osg::Vec3f{l_lower, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_lower, l_right, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_right, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_center, l_z});
        l_vertices->dirty();
        l_geometry->dirtyBound();
        osg::StateSet* l_stateSet = l_geometry->getStateSet();
        osg::Vec4f     l_textureSelection{
            0.5f,                                     // width
            g_freeparkingSettings->m_lowerPercentage, // length
            0.5f,                                     // left
            0.0f                                      // bottom
        };
        l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(l_textureSelection);
    }
    //! MIDDLE RIGHT
    {
        const vfc::float32_t l_upper    = l_top - l_length * g_freeparkingSettings->m_upperPercentage;
        const vfc::float32_t l_lower    = l_bottom + l_length * g_freeparkingSettings->m_lowerPercentage;
        auto                 l_geometry = m_middleRightGeode->getDrawable(0u)->asGeometry();
        const auto           l_vertices = static_cast<osg::Vec3Array*>(l_geometry->getVertexArray());
        l_vertices->clear();
        l_vertices->push_back(osg::Vec3f{l_lower, l_center, l_z});
        l_vertices->push_back(osg::Vec3f{l_lower, l_right, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_right, l_z});
        l_vertices->push_back(osg::Vec3f{l_upper, l_center, l_z});
        l_vertices->dirty();
        l_geometry->dirtyBound();
        osg::StateSet* l_stateSet = l_geometry->getStateSet();
        vfc::float32_t l_textureLength =
            1.0f - g_freeparkingSettings->m_lowerPercentage - g_freeparkingSettings->m_upperPercentage;
        osg::Vec4f l_textureSelection{
            0.5f,                                    // width
            l_textureLength,                         // length
            0.5f,                                    // left
            g_freeparkingSettings->m_lowerPercentage // bottom
        };
        l_stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(l_textureSelection);
    }
}

void FreeparkingWarningOverlay::update(FreeparkingOverlay* f_freeparkingOverlay, const bool /*f_isLeft*/)
{
    const auto size = f_freeparkingOverlay->getSize();
    if (size.x() != m_size.x() || size.y() != m_size.y())
    {
        m_size = size;
        updateGeometry();
    }

    // TODO adapt signals
    m_bottomLeftGeode->setNodeMask(
        IMGUI_GET_CHECKBOX_BOOL("FreeparkingWarningOverlay", "BottomLeft") ? 0xFFFFFFFF : 0x0);
    m_topLeftGeode->setNodeMask(IMGUI_GET_CHECKBOX_BOOL("FreeparkingWarningOverlay", "TopLeft") ? 0xFFFFFFFF : 0x0);
    m_middleLeftGeode->setNodeMask(
        IMGUI_GET_CHECKBOX_BOOL("FreeparkingWarningOverlay", "MiddleLeft") ? 0xFFFFFFFF : 0x0);

    m_bottomRightGeode->setNodeMask(
        IMGUI_GET_CHECKBOX_BOOL("FreeparkingWarningOverlay", "BottomRight") ? 0xFFFFFFFF : 0x0);
    m_topRightGeode->setNodeMask(IMGUI_GET_CHECKBOX_BOOL("FreeparkingWarningOverlay", "TopRight") ? 0xFFFFFFFF : 0x0);
    m_middleRightGeode->setNodeMask(
        IMGUI_GET_CHECKBOX_BOOL("FreeparkingWarningOverlay", "MiddleRight") ? 0xFFFFFFFF : 0x0);
}

//!
//! ParkingSpot
//!
FreeparkingOverlay::FreeparkingOverlay()
    : cc::assets::parkingspots::ParkingSpot() // PRQA S 4050
    , m_type{cc::daddy::ParkingSpot::PARKINGTYPE_INVALID}
    , m_position{osg::Vec2f{0.0f, 0.0f}}
    , m_middle{osg::Vec2f{0.0f, 0.0f}} // , m_size{osg::Vec2f{0.0f, 0.0f}}
    , m_dirty{false}                   // , m_visible{false}
    , m_rotateButtonVisible{}
    , m_angle{0.0f}
    , m_isLeft(true)
    , m_parkingPlaneState(ParkingPlaneState::DEFAULT)
{
    getChild(0u)->setNodeMask(0u);
    m_freeparkingPlane = new FreeparkingPlane;
    m_rotateButton     = new RotateButton;
    m_warnOverlay      = new FreeparkingWarningOverlay{};
    // m_frontPlane       = new FrontPlane;
    osg::MatrixTransform::addChild(m_freeparkingPlane); // PRQA S 3803
    osg::MatrixTransform::addChild(m_rotateButton);     // PRQA S 3803
    osg::MatrixTransform::addChild(m_warnOverlay);      // PRQA S 3803
    setNumChildrenRequiringUpdateTraversal(getNumChildren());
    // osg::MatrixTransform::addChild(m_frontPlane);       // PRQA S 3803

    if (!g_sharedParkingSpotStateSet.valid())
    {
        g_sharedParkingSpotStateSet = new osg::StateSet;

        g_defaultTexture = createTexture(g_freeparkingSettings->m_defalutTexture);
        g_availableTexture = createTexture(g_freeparkingSettings->m_availableTexture);
        g_movingTexture = createTexture(g_freeparkingSettings->m_movingTexture);
        g_unavailableTexture = createTexture(g_freeparkingSettings->m_unavailableTexture);
        g_judgingTexture = createTexture(g_freeparkingSettings->m_judgingTexuture);
        g_sharedParkingSpotStateSet->setTextureAttribute(0u, g_movingTexture.get());
        g_sharedParkingSpotStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
        g_sharedParkingSpotStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
        g_sharedParkingSpotStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
        // g_sharedParkingSpotStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_FREEPARKING_OVERLAY, "RenderBin");

        pc::core::TextureShaderProgramDescriptor l_parkingSpotShader("advancedTex");
        g_sharedParkingSpotStateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)
            ->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));                                              // PRQA S 3803
        g_sharedParkingSpotStateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f); // PRQA S 3803
        g_sharedParkingSpotStateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);  // PRQA S 3803
        l_parkingSpotShader.apply(g_sharedParkingSpotStateSet.get());                               // PRQA S 3803
    }
    // m_ParkingSpotPlane->setStateSet(g_sharedParkingSpotStateSet.get());
    setStateSet(g_sharedParkingSpotStateSet.get());
}

FreeparkingOverlay::FreeparkingOverlay(const FreeparkingOverlay& f_other, const osg::CopyOp& f_copyOp)
    : cc::assets::parkingspots::ParkingSpot(f_other, f_copyOp)
    , m_type{f_other.m_type}
    , m_position{f_other.m_position}
    , m_middle{f_other.m_middle} // , m_size{f_other.m_size}
    , m_dirty{f_other.m_dirty}   // , m_visible{f_other.m_visible}
    , m_rotateButtonVisible{f_other.m_rotateButtonVisible}
    , m_angle{f_other.m_angle}
    , m_isLeft{f_other.m_isLeft}
    , m_mousePressed{f_other.m_mousePressed}
    , m_slitherActionType{f_other.m_slitherActionType}
    , m_parkable{f_other.m_parkable}
    , m_freeparkingPlane{f_other.m_freeparkingPlane}
    , m_rotateButton{f_other.m_rotateButton}
    , m_frontPlane{f_other.m_frontPlane}
{
}

FreeparkingOverlay::~FreeparkingOverlay() = default;

void FreeparkingOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (m_dirty)
        {
            const osg::Vec3f l_position(m_position, g_freeparkingSettings->m_groundLevel);
            setMatrix(
                osg::Matrix::rotate(osg::DegreesToRadians(m_angle), osg::Z_AXIS) * osg::Matrix::translate(l_position));
            m_freeparkingPlane->update(this, m_isLeft);
            // m_frontPlane->update(this, m_isLeft);
            m_dirty = false;
        }
        m_rotateButton->update(this, m_isLeft);
        m_warnOverlay->update(this, m_isLeft);
    }
    osg::MatrixTransform::traverse(f_nv);
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
