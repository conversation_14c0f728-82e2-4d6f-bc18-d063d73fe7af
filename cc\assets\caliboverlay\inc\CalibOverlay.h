//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CalibOverlay.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_CALIBOVERLAY_CALIBOVERLAY_H
#define CC_ASSETS_CALIBOVERLAY_CALIBOVERLAY_H

#include "pc/svs/core/inc/AsyncNode.h"
#include "pc/svs/worker/core/inc/Task.h"
#include "pc/svs/util/math/inc/FilterSpatial.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/MatrixTransform>
#include <osg/observer_ptr>


//! forward declarations
namespace pc
{
namespace core
{
class Framework;
} // namespace core
namespace worker
{
} // namespace worker
} // namespace pc

namespace cc
{
namespace assets
{
namespace caliboverlay
{

class CalibSettings : public pc::util::coding::ISerializable
{
public:

  CalibSettings()
    : m_calibSwitch(0U)
    , m_calibOverlayColor(1.0f, 0.0f, 0.0f, 1.0f)
  {
  }
  SERIALIZABLE(CalibSettings)
  {
    ADD_UINT32_MEMBER(calibSwitch);
    ADD_MEMBER(osg::Vec4f, calibOverlayColor);
  }
  unsigned int m_calibSwitch;
  osg::Vec4f    m_calibOverlayColor;

};

extern pc::util::coding::Item<CalibSettings> g_calibSettings;

//!
//! CalibOverlayComposite
//!
class CalibOverlayComposite : public osg::MatrixTransform
{
public:

  CalibOverlayComposite(pc::core::Framework* f_framework = nullptr);
  CalibOverlayComposite(const CalibOverlayComposite& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::caliboverlay, CalibOverlayComposite);  // PRQA S 2504

  void setFamework(pc::core::Framework* f_framework)
  {
    m_framework = f_framework;
  }

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

  virtual ~CalibOverlayComposite();

private:

//! Copy constructor is not permitted.
  CalibOverlayComposite (const CalibOverlayComposite& other); // = delete
  //! Copy assignment operator is not permitted.
  CalibOverlayComposite& operator=(const CalibOverlayComposite& other); // = delete

  pc::core::Framework* m_framework;
  unsigned int m_idxSpline2D;
};


//======================================================
// CalibOverlay
//------------------------------------------------------
/// Displays the Calibration Overlay
/// Calibration overlay is a rectangle around vehicle.
/// <AUTHOR>
//======================================================
class CalibOverlay : public pc::core::AsyncNode
{
public:

  CalibOverlay(pc::core::Framework* f_framework);

protected:

  virtual ~CalibOverlay();

private:
  //! Copy constructor is not permitted.
  CalibOverlay (const CalibOverlay& other); // = delete
  //! Copy assignment operator is not permitted.
  CalibOverlay& operator=(const CalibOverlay& other); // = delete


};



//!
//! ProcessingTask
//!
class ProcessingTask : public pc::worker::core::Task
{
public:

  typedef pc::util::SpatialFilter<pc::util::FloatList> SpatialFilter;

  ProcessingTask(CalibOverlay* f_calibOverlay);

  virtual bool onRun(pc::worker::core::TaskManager* f_manager);

protected:

  virtual ~ProcessingTask();

private:
  //! Copy constructor is not permitted.
  ProcessingTask (const ProcessingTask& other); // = delete
  //! Copy assignment operator is not permitted.
  ProcessingTask& operator=(const ProcessingTask& other); // = delete

  osg::observer_ptr<CalibOverlay> m_calibOverlay;
  bool m_staticStateReached;
  osg::ref_ptr<SpatialFilter> m_distanceFilter;
};

} // namespace caliboverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_CALIBOVERLAY_CALIBOVERLAY_H