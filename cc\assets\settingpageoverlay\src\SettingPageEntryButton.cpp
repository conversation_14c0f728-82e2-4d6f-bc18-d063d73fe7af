//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#include "cc/assets/settingpageoverlay/inc/SettingPageEntryButton.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui_manager.h"

#include <osg/Group>
namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

static pc::util::coding::Item<SettingPageEntryButtonSettings> g_settingPageEntryButtonSettings("SettingPageEntryButton");

SettingPageEntryButton::SettingPageEntryButton(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    SettingPageSwitch*   f_settingPageSwtich,
    osg::Camera*         f_referenceView = nullptr)
    : Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_settingPageEnabledState{false}
    , m_settingPageSwitch{f_settingPageSwtich}
    , m_showStatus{false}
{
    setState(AVAILABLE);
    setName("SettingPage Button");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(g_settingPageEntryButtonSettings->m_horiPos);
}

void SettingPageEntryButton::onInvalid()
{
    setIconEnable(false);
}

void SettingPageEntryButton::onUnavailable()
{
    setIconEnable(false);
}

void SettingPageEntryButton::onAvailable()
{
    setIconEnable(true);
    setTexturePath(g_settingPageEntryButtonSettings->m_buttonTexture.m_AvailableTexturePath);
}

void SettingPageEntryButton::onPressed()
{
    setIconEnable(true);
    setTexturePath(g_settingPageEntryButtonSettings->m_buttonTexture.m_AvailableTexturePath);
    if (!m_settingPageEnabledState && m_settingPageSwitch->getSettingPageStatus() == false)
    {
        m_settingPageSwitch->openSettingPage();
    }
    else
    {
        m_settingPageSwitch->closeSettingPage();
    }
}

void SettingPageEntryButton::onReleased()
{
    setIconEnable(true);
}

void SettingPageEntryButton::update()
{
    setSettingModifiedCount(g_settingPageEntryButtonSettings->getModifiedCount());
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)
    
    
    
    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)

    GET_PORT_DATA(showReqContainer, m_framework->asCustomFramework()->m_showReq_ReceiverPort, showReqPortHaveData)


    if (showReqPortHaveData)
    {
        const bool showReq = showReqContainer->m_Data;
        if (showReq == false && m_showStatus!= false)
        {
            m_settingPageSwitch->closeSettingPage();
        }
        m_showStatus = showReq;
    }

    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

#ifdef SUP_HSAE
    if ((getTouchStatus() == TOUCH_MOVE ||getTouchStatus() == TOUCH_DOWN)&&
#endif
#ifdef SUP_MEGA
    if (getTouchStatus() == PRESSED &&
#endif    
        !checkTouchInsideTargetClickArea(
            g_settingPageOverlaySettings->m_settingPageBackgroundPos,
            g_settingPageOverlaySettings->m_settingPageBackgroundSize) &&
        !checkTouchInsideResponseArea() && m_settingPageSwitch->getSettingPageStatus())
    {
        m_settingPageSwitch->closeSettingPage();
    }

    if (g_settingPageEntryButtonSettings->getModifiedCount() != getSettingModifiedCount())
    {
        setSettingModifiedCount(g_settingPageEntryButtonSettings->getModifiedCount());
    }
    const bool        touchInsideResponseArea = checkTouchInsideResponseArea();
//    const TouchStatus touchSts                = touchStatus(); // PRQA S 3803

    ButtonState currentState = getState();

    switch (currentState)
    {
    case AVAILABLE:
    {
        if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
        {
            currentState = PRESSED;
        }
        break;}
    case PRESSED:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = RELEASED;
        }
        break;
    }
    case RELEASED:
    {
        currentState = AVAILABLE;
        break;
    }
    case INVALID:
    case UNAVAILABLE:
    default:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = AVAILABLE;
        }
        break;
    }
    }

    setState(currentState);

    switch (getState())
    {
    case INVALID:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    case UNAVAILABLE:
    {   
        IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
        break;
    }
    case AVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
        break;
    }
    case PRESSED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
        break;
    }
    case RELEASED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "RELEASED");
        break;
    }
    default:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    }
}

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc // PRQA S 1041
