//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/ptsoverlay/inc/PtsLookupTexture.h"
#include "cc/assets/ptsoverlay/inc/PtsSettings.h"
#include "cc/assets/ptsoverlay/inc/PtsUtils.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "vfc/core/vfc_types.hpp"
#include <osg/Image>
#include <osg/Texture2D>
#include <array>

#define DEBUG_LOOKUP_TEXTURE 0
#if DEBUG_LOOKUP_TEXTURE
#include <osgDB/WriteFile>
#endif

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace ptsoverlay
{

const vfc::uint32_t LookupTexture::s_size = 32u;
const LookupTexture::TexCoord::value_type LookupTexture::s_maxTexCoord = std::numeric_limits<LookupTexture::TexCoord::value_type>::max();

const LookupTexture::ValueRange LookupTexture::s_distanceColor(0.0f, 0.9f); // use 90% of the texture for the distance color mapping
const LookupTexture::ValueRange LookupTexture::s_onColor(0.9f, 0.925f); // the remaining 10% are split-up equally for the static colors
const LookupTexture::ValueRange LookupTexture::s_outlineColor(0.925f, 0.975f);
const LookupTexture::ValueRange LookupTexture::s_shadowColor(0.975f, 1.0f);


//!
//! LookupTexture::ValueRange
//!
LookupTexture::ValueRange::ValueRange(vfc::float32_t f_begin, vfc::float32_t f_end)
  : m_begin{f_begin}
  , m_end{f_end}
{
}


LookupTexture::TexCoord::value_type LookupTexture::ValueRange::get(vfc::float32_t f_v) const
{
  return static_cast<LookupTexture::TexCoord::value_type>(pc::util::round2uInt(static_cast<vfc::float32_t>(LookupTexture::s_maxTexCoord) * std::min(1.0f, lerp(m_begin, m_end, f_v)))); // PRQA S 3011
}


LookupTexture::TexCoord LookupTexture::ValueRange::get(vfc::float32_t f_u, vfc::float32_t f_v) const
{
  return TexCoord(static_cast<LookupTexture::TexCoord::value_type>(pc::util::round2uInt(f_u * static_cast<vfc::float32_t>(LookupTexture::s_maxTexCoord))),get(f_v)); // PRQA S 3011
}

namespace
{

//!
//! DistanceColorInterpolator
//!
class DistanceColorInterpolator : public pc::util::math::LinearInterpolator<osg::Vec4f>
{
public:

  DistanceColorInterpolator() // PRQA S 4054
  {
    for (vfc::uint32_t i = 0u; i < NUM_COLORS; ++i)
    {
      addSample(
        g_ptsSettings->getDistances().getDistance(i),
        toVec4f(g_ptsSettings->m_colors.getColor(i)));
    }
    addSample(g_ptsSettings->getDistances().m_default, toVec4f(g_ptsSettings->m_colorOn));
    init();
  }

};


//!
//! @brief This look-up texture serves as look-up table for alpha values for lines with smoothed edges in U-direction,
//! as well as a look-up table for color values in V-direction
//!
//! @return osg::Image* the look-up texture image
//!
osg::Image* createLookupTexture()
{
  DistanceColorInterpolator l_colorInterpolator;

  const vfc::float32_t l_gradientWidth = osg::clampTo(g_ptsSettings->m_smoothingGradientSize, std::numeric_limits<vfc::float32_t>::epsilon(), 0.5f);

  typedef std::array<osg::Vec4ub::value_type, LookupTexture::s_size> AlphaValueArray;
  AlphaValueArray l_regularAlphaValues;
  for (vfc::uint32_t i = 0u; i < LookupTexture::s_size; ++i)
  {
    const vfc::float32_t l_iNormalized = i / static_cast<vfc::float32_t> (LookupTexture::s_size - 1u);
    vfc::float32_t l_alpha = 1.0f;
    if (l_gradientWidth > l_iNormalized)
    {
      l_alpha = pc::util::smoothstep(0.0f, l_gradientWidth, l_iNormalized);
    }
    else if ((1.0f - l_gradientWidth) < l_iNormalized)
    {
      l_alpha = pc::util::smoothstep(1.0f, (1.0f - l_gradientWidth), l_iNormalized);
    }
    else{}
    l_regularAlphaValues[i] = float2UByte(l_alpha);
  }

  AlphaValueArray l_shadowAlphaValues;
  for (vfc::uint32_t i = 0u; i < LookupTexture::s_size; ++i)
  {
    const vfc::float32_t l_iNormalized = i / static_cast<vfc::float32_t> (LookupTexture::s_size - 1u);
    const vfc::float32_t l_alpha = pc::util::smoothstep(0.0f, 1.0f, l_iNormalized);
    l_shadowAlphaValues[i] = float2UByte(l_alpha);
  }

  std::array<osg::Vec4ub, LookupTexture::s_size> l_colorValues;
  for (vfc::uint32_t i = 0u; i < LookupTexture::s_size; ++i)
  {
    const vfc::float32_t l_iNormalized = i / static_cast<vfc::float32_t> (LookupTexture::s_size - 1u);
    if (LookupTexture::s_distanceColor.contains(l_iNormalized))
    {
      // map global range to local range
      const vfc::float32_t l_distanceColorSample = LookupTexture::s_distanceColor.normalize(l_iNormalized);
      // map normalized sampling range to valid distance range
      const vfc::float32_t l_distance = g_ptsSettings->getDistances().denormalize(l_distanceColorSample);
      l_colorValues[i] = toVec4ub(l_colorInterpolator.getValue(l_distance));
    }
    else if (LookupTexture::s_onColor.contains(l_iNormalized))
    {
      l_colorValues[i] = toVec4ub(g_ptsSettings->m_colorOn);
    }
    else if (LookupTexture::s_outlineColor.contains(l_iNormalized))
    {
      l_colorValues[i] = toVec4ub(g_ptsSettings->m_colorOutline);
    }
    else
    {
      l_colorValues[i] = toVec4ub(g_ptsSettings->m_colorShadow);
    }
  }

  // construct final image by combing the precomputed values
  osg::Image* const l_image = new osg::Image;
  l_image->allocateImage(LookupTexture::s_size, LookupTexture::s_size, 1, GL_RGBA, GL_UNSIGNED_BYTE);
  osg::Vec4ub* const l_data = reinterpret_cast<osg::Vec4ub*> (l_image->data()); // PRQA S 3030
  l_image->setDataVariance(osg::Object::STATIC);
  for (vfc::uint32_t l_v = 0u; l_v < LookupTexture::s_size; ++l_v)
  {
    const osg::Vec4ub& l_color = l_colorValues[l_v];
    const vfc::float32_t l_vNormalized = l_v / static_cast<vfc::float32_t> (LookupTexture::s_size - 1u);
    const AlphaValueArray& l_alphaValues = LookupTexture::s_shadowColor.contains(l_vNormalized) ? l_shadowAlphaValues : l_regularAlphaValues;
    for (vfc::uint32_t l_u = 0u; l_u < LookupTexture::s_size; ++l_u)
    {
      l_data[l_v * LookupTexture::s_size + l_u] = osg::Vec4ub(l_color.r(), l_color.g(), l_color.b(), l_alphaValues[l_u]);
    }
  }

  #if DEBUG_LOOKUP_TEXTURE
  osgDB::writeImageFile(*l_image, "pts_lookup_texture.png");
  #endif // DEBUG_LOOKUP_TEXTURE
  return l_image;
}

} // namespace

static osg::observer_ptr<osg::StateSet> s_stateSet;
osg::StateSet* getOrCreateTexturingStateSet()
{
  if (!s_stateSet.valid())
  {
    //! create look-up texture
    osg::Image* const l_image = createLookupTexture();
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setUnRefImageDataAfterApply(true);

    //! state set with look-up texture
    osg::StateSet* const l_stateSet = new osg::StateSet;
    l_stateSet->setTextureAttribute(0u, l_texture);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
    s_stateSet = l_stateSet;
  }
  return s_stateSet.get();
}


} // namespace ptsoverlay
} // namespace assets
} // namespace cc
