//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityObject.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_OBJECT_H
#define CC_ASSETS_VIRTUALREALITY_OBJECT_H

#include "pc/generic/util/coding/inc/CodingManager.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace virtualreality
{


class VirtualRealityObjectSettings : public pc::util::coding::ISerializable
{
public:
  VirtualRealityObjectSettings()
    : m_slotHalfWidth(1.3f)
    , m_slotRear2RearAxelDistance(1.2f)
    , m_slotFront2RearAxelDistance(4.0f)
    , m_vehicleModelScaleFactor(osg::Vec3(1,1,1))
    , m_filenameSlotOccupied("cc/resources/virualReality/parkslot_selectable.png")
    , m_filenameSlotNotSelectable("cc/resources/virualReality/parkslot_selectable.png")
    , m_filenameSlotSelectable("cc/resources/virualReality/parkslot_selectable.png")
    , m_filenameSlotSelected("cc/resources/virualReality/parkslot_selectable.png")
  {
  }

  SERIALIZABLE(VirtualRealityObjectSettings)
  {
    ADD_FLOAT_MEMBER(slotHalfWidth);
    ADD_FLOAT_MEMBER(slotRear2RearAxelDistance);
    ADD_FLOAT_MEMBER(slotFront2RearAxelDistance);
    ADD_MEMBER(osg::Vec3f, vehicleModelScaleFactor);
    ADD_STRING_MEMBER(filenameSlotOccupied);
    ADD_STRING_MEMBER(filenameSlotNotSelectable);
    ADD_STRING_MEMBER(filenameSlotSelectable);
    ADD_STRING_MEMBER(filenameSlotSelected);
  }

  float m_slotHalfWidth;
  float m_slotRear2RearAxelDistance;
  float m_slotFront2RearAxelDistance;
  osg::Vec3   m_vehicleModelScaleFactor;
  std::string m_filenameSlotOccupied;
  std::string m_filenameSlotNotSelectable;
  std::string m_filenameSlotSelectable;
  std::string m_filenameSlotSelected;
};


extern pc::util::coding::Item<VirtualRealityObjectSettings> g_virtualRealityObjectSetting;

struct Position_st
{
  vfc::float32_t  m_x;   // m
  vfc::float32_t  m_y;   // m
  vfc::float32_t  m_z;   // m
  vfc::float32_t  m_phi; // rad
};

//!
//! VirtualRealityObject
//!
class VirtualRealityObject : public osg::MatrixTransform
{
public:
  VirtualRealityObject();

  VirtualRealityObject(const VirtualRealityObject& f_other, const osg::CopyOp& f_copyOp);

  virtual void addObjectNode() = 0;

  virtual void updateObjectNode(){}

  const Position_st& getPosition() const
  {
    return m_position;
  }

  void setPosition(const Position_st& f_position)
  {
    m_position = f_position;
  }

  const bool getVisibility() const
  {
    return m_visible;
  }

  void setVisibility(bool f_visible)
  {
    m_visible = f_visible;
  }

  void dirty()
  {
    m_dirty = true;
  }

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
  virtual ~VirtualRealityObject();

private:
  Position_st m_position;
  bool m_dirty;
  bool m_visible;
};


} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_OBJECT_H
