//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  WheelSeparator.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_WHEELSEPARATOR_H
#define CC_ASSETS_UIELEMENTS_WHEELSEPARATOR_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

namespace wheelseparatorhorizontal
{
  
  //!
  //! WheelSeparatorManagerHorizontal
  //!
  class WheelSeparatorManagerHorizontal
  {
  public:
    WheelSeparatorManagerHorizontal();
    virtual ~WheelSeparatorManagerHorizontal();

    void init(pc::assets::ImageOverlays* f_imageOverlays);
    void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
    void update(pc::assets::ImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework);

  private:
    //! Copy constructor is not permitted.
    WheelSeparatorManagerHorizontal (const WheelSeparatorManagerHorizontal& other); // = delete
    //! Copy assignment operator is not permitted.
    WheelSeparatorManagerHorizontal& operator=(const WheelSeparatorManagerHorizontal& other); // = delete

    unsigned int m_lastConfigUpdate;
    pc::assets::IconGroup m_wheelSeparatorIcons;
    bool m_mat_b;
  };


  //!
  //! WheelSeparatorHorizontal
  //!
  class WheelSeparatorHorizontal: public cc::assets::uielements::CustomImageOverlays
  {
  public:
    WheelSeparatorHorizontal(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
    virtual ~WheelSeparatorHorizontal();

    virtual void traverse(osg::NodeVisitor& f_nv) override;

  private:
    //! Copy constructor is not permitted.
    WheelSeparatorHorizontal (const WheelSeparatorHorizontal& other); // = delete
    //! Copy assignment operator is not permitted.
    WheelSeparatorHorizontal& operator=(const WheelSeparatorHorizontal& other); // = delete

    cc::core::CustomFramework* m_customFramework;
    WheelSeparatorManagerHorizontal m_manager;
};
} // namespace wheelseparatorhorizontal

namespace wheelseparatorvertical
{
  //!
  //! WheelSeparatorManagerVertical
  //!
  class WheelSeparatorManagerVertical
  {
  public:
    WheelSeparatorManagerVertical();
    virtual ~WheelSeparatorManagerVertical();

    void init(pc::assets::ImageOverlays* f_imageOverlays);
    void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
    void update(pc::assets::ImageOverlays* f_imageOverlays, const core::CustomFramework* f_framework);

  private:
    //! Copy constructor is not permitted.
    WheelSeparatorManagerVertical (const WheelSeparatorManagerVertical& other); // = delete
    //! Copy assignment operator is not permitted.
    WheelSeparatorManagerVertical& operator=(const WheelSeparatorManagerVertical& other); // = delete

    unsigned int m_lastConfigUpdate;
    pc::assets::IconGroup m_wheelSeparatorIcons;
    bool m_mat_b;
  };


  //!
  //! WheelSeparatorVertical
  //!
  class WheelSeparatorVertical: public cc::assets::uielements::CustomImageOverlays
  {
  public:
    WheelSeparatorVertical(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
    virtual ~WheelSeparatorVertical();

    virtual void traverse(osg::NodeVisitor& f_nv) override;

  private:
    //! Copy constructor is not permitted.
    WheelSeparatorVertical (const WheelSeparatorVertical& other); // = delete
    //! Copy assignment operator is not permitted.
    WheelSeparatorVertical& operator=(const WheelSeparatorVertical& other); // = delete

    cc::core::CustomFramework* m_customFramework;
    WheelSeparatorManagerVertical m_manager;
  };
} // namespace wheelseparatorvertical

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_WHEELSEPARATOR_H
