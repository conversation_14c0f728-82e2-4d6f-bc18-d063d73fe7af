//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/ptsoverlay/inc/PtsUtils.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "vfc/core/vfc_types.hpp"
namespace cc
{
namespace assets
{
namespace ptsoverlay
{

constexpr vfc::int32_t g_maxByte = std::numeric_limits<osg::Vec4ub::value_type>::max();
constexpr vfc::int32_t g_minShort = std::numeric_limits<osg::Vec2s::value_type>::min();
constexpr vfc::int32_t g_maxShort = std::numeric_limits<osg::Vec2s::value_type>::max();

vfc::float32_t int2Float(vfc::int32_t f_value)
{
  return osg::clampTo(f_value, 0, g_maxByte) / static_cast<vfc::float32_t> (g_maxByte);
}

osg::Vec4ub::value_type float2UByte(vfc::float32_t f_value)
{
  return static_cast<osg::Vec4ub::value_type> (osg::clampTo<vfc::int32_t>(f_value * static_cast<vfc::float32_t>(g_maxByte), 0, g_maxByte));
}

osg::Vec4ub::value_type int2UByte(vfc::int32_t f_value)
{
  return static_cast<osg::Vec4ub::value_type> (osg::clampTo(f_value, 0, g_maxByte));
}

osg::Vec2s::value_type float2Short(vfc::float32_t f_value)
{
  return static_cast<osg::Vec2s::value_type> (osg::clampTo<vfc::int32_t>(f_value * static_cast<vfc::float32_t>(g_maxShort), g_minShort, g_maxShort));
}

osg::Vec4f toVec4f(const osg::Vec4i& f_rgba)
{
  return osg::Vec4f(int2Float(f_rgba.r()),
                    int2Float(f_rgba.g()),
                    int2Float(f_rgba.b()),
                    int2Float(f_rgba.a()));
}

osg::Vec4ub toVec4ub(const osg::Vec4i& f_rgba)
{
  return osg::Vec4ub(int2UByte(f_rgba.r()),
                     int2UByte(f_rgba.g()),
                     int2UByte(f_rgba.b()),
                     int2UByte(f_rgba.a()));
}

osg::Vec4ub toVec4ub(const osg::Vec4f& f_color)
{
  return osg::Vec4ub(float2UByte(f_color.r()),
                     float2UByte(f_color.g()),
                     float2UByte(f_color.b()),
                     float2UByte(f_color.a()));
}


enum class PasZoneRangeEnd : vfc::uint8_t
{
  PAS_ZONE_RANGE_END_FRONT = cc::core::DAIPasZonesDescription::NUM_PAS_ZONES_FRONT,
  PAS_ZONE_RANGE_END_LEFT = static_cast<vfc::uint8_t>(PAS_ZONE_RANGE_END_FRONT) + static_cast<vfc::uint8_t>(cc::core::DAIPasZonesDescription::NUM_PAS_ZONES_SIDE),
  PAS_ZONE_RANGE_END_REAR = static_cast<vfc::uint8_t>(PAS_ZONE_RANGE_END_LEFT) + (2u *   static_cast<vfc::uint8_t>(cc::core::DAIPasZonesDescription::NUM_PAS_ZONES_REAR)),
  PAS_ZONE_RANGE_END_RIGHT = static_cast<vfc::uint8_t>(PAS_ZONE_RANGE_END_REAR) + static_cast<vfc::uint8_t>(cc::core::DAIPasZonesDescription::NUM_PAS_ZONES_SIDE)
};


PasZoneArea getPasZoneArea(vfc::uint32_t f_zone, const osg::Vec2f& f_origin)
{
  if (f_zone < static_cast<unsigned>(PasZoneRangeEnd::PAS_ZONE_RANGE_END_FRONT)) // PRQA S 3000
  {
    return PAS_ZONE_FRONT;
  }
  else if (f_zone < static_cast<unsigned>(PasZoneRangeEnd::PAS_ZONE_RANGE_END_LEFT)) // PRQA S 3000
  {
    return (f_origin.x() > (pc::vehicle::g_mechanicalData->m_wheelbase * 0.5f)) ?
      PAS_ZONE_LEFT_FRONT : PAS_ZONE_LEFT_REAR;
  }
  else if (f_zone < static_cast<unsigned>(PasZoneRangeEnd::PAS_ZONE_RANGE_END_REAR)) // PRQA S 3000
  {
    return PAS_ZONE_REAR;
  }
  else if (f_zone < static_cast<unsigned>(PasZoneRangeEnd::PAS_ZONE_RANGE_END_RIGHT)) // PRQA S 3000
  {
    return (f_origin.x() > (pc::vehicle::g_mechanicalData->m_wheelbase * 0.5f)) ?
      PAS_ZONE_RIGHT_FRONT : PAS_ZONE_RIGHT_REAR;
  }
  else
  {
    return PAS_ZONE_FRONT; // higher indices belong to the front area again
  }
}



vfc::uint32_t intersectLineCircle( // PRQA S 4678
  const pc::util::LineSegment2D& f_line,
  const osg::Vec2f& f_center,
  vfc::float32_t f_radius,
  osg::Vec2f& f_intersectionA, // PRQA S 4287
  osg::Vec2f& f_intersectionB) // PRQA S 4287
{
  const osg::Vec2f& l_p0 = f_line.getP0();
  const osg::Vec2f& l_p1 = f_line.getP1();

  const vfc::float32_t l_a = (l_p1.x() - l_p0.x()) * (l_p1.x() - l_p0.x()) +
    (l_p1.y() - l_p0.y()) * (l_p1.y() - l_p0.y());

  const vfc::float32_t l_b = 2.0f * ((l_p1.x() - l_p0.x()) * (l_p0.x() - f_center.x()) +
    (l_p1.y() - l_p0.y()) * (l_p0.y() - f_center.y()));


  const vfc::float32_t l_c = f_center.x() * f_center.x() + f_center.y() * f_center.y() +
    l_p0.x() * l_p0.x() + l_p0.y() * l_p0.y() -
    2.0f * (f_center.x() * l_p0.x() + f_center.y() * l_p0.y()) - f_radius * f_radius;

  const vfc::float32_t l_deter = l_b * l_b - 4.0f * l_a * l_c;

  if (l_deter < 0.0f)
  {
    return 0; // no intersection
  }
  else if (isZero(l_deter))
  {
    return 0; // is tanget -> treat as no intersection for now
  }
  else
  {
    const vfc::float32_t l_e = std::sqrt(l_deter);
    const vfc::float32_t l_x0 = (-l_b + l_e) / (2.0f * l_a);
    const vfc::float32_t l_x1 = (-l_b - l_e) / (2.0f * l_a);

    vfc::uint32_t l_numIntersections = 0u;
    if ((0.0f <= l_x0) && (l_x0 <= 1.0f))
    {
      f_intersectionA = lerp(l_p0, l_p1, l_x0);
      ++l_numIntersections;
    }
    if ((0.0f <= l_x1) && (l_x1 <= 1.0f))
    {
      f_intersectionB = lerp(l_p0, l_p1, l_x1);
      ++l_numIntersections;
    }
    return l_numIntersections;
  }
}


bool isInsideBox(const pc::util::Box2D& f_box, const osg::Vec2f& f_t)
{
  return (f_box.getXMin() <= f_t.x()) && (f_t.x() <= f_box.getXMax()) &&
    (f_box.getYMin() <= f_t.y()) && (f_t.y() <= f_box.getYMax());
}


osg::Vec2f getCorner(const pc::util::Box2D& f_box, AxisBitMask f_axis)
{
  return osg::Vec2f(
    (f_axis & static_cast<vfc::uint32_t>(X_AXIS_BIT)) ? f_box.getXMax() : f_box.getXMin(), // PRQA S 3144
    (f_axis & static_cast<vfc::uint32_t>(Y_AXIS_BIT)) ? f_box.getYMax() : f_box.getYMin()); // PRQA S 3144
}


//!
//! LineSegment
//!
LineSegment::LineSegment(const pc::util::LineSegment2D& f_lineSegment)
  : m_data{f_lineSegment}
{
}


GeometricShape::PointList LineSegment::intersect(const pc::util::LineSegment2D& f_lineSegment) const
{
  PointList l_result;
  osg::Vec2f l_intersection;
  if (pc::util::findIntersection(m_data, f_lineSegment, l_intersection))
  {
    l_result.push_back(l_intersection);
  }
  return l_result;
}


//!
//! Circle
//!
Circle::Circle(const osg::Vec2f& f_center, vfc::float32_t f_radius)
  : m_center{f_center}
  , m_radius{f_radius}
{
}


GeometricShape::PointList Circle::intersect(const pc::util::LineSegment2D& f_lineSegment) const
{
  PointList l_result;
  osg::Vec2f l_intersectionA;
  osg::Vec2f l_intersectionB;
  const vfc::uint32_t l_numIntersections = intersectLineCircle(
    f_lineSegment,
    m_center,
    m_radius,
    l_intersectionA,
    l_intersectionB);
  if (l_numIntersections > 0u)
  {
    l_result.push_back(l_intersectionA);
    if (l_numIntersections > 1u)
    {
      l_result.push_back(l_intersectionB);
    }
  }
  return l_result;
}



//!
//! CornerArc
//!
CornerArc::CornerArc(const osg::Vec2f& f_center, vfc::float32_t f_radius, Quadrant f_quadrant)
  : m_circle{f_center, f_radius}
  , m_quadrant{f_quadrant}
{
}


pc::util::Box2D CornerArc::getBoundingBox() const
{
  osg::Vec2f l_oppositePoint;
  switch (m_quadrant)
  {
    case CORNER_ARC_X_POS_Y_POS:
    {
      l_oppositePoint = m_circle.m_center + osg::Vec2f( m_circle.m_radius,  m_circle.m_radius);
      break;
    }
    case CORNER_ARC_X_NEG_Y_POS:
    {
      l_oppositePoint = m_circle.m_center + osg::Vec2f(-m_circle.m_radius,  m_circle.m_radius);
      break;
    }
    case CORNER_ARC_X_NEG_Y_NEG:
    {
      l_oppositePoint = m_circle.m_center + osg::Vec2f(-m_circle.m_radius, -m_circle.m_radius);
      break;
    }
    case CORNER_ARC_X_POS_Y_NEG:
    {
      l_oppositePoint = m_circle.m_center + osg::Vec2f( m_circle.m_radius, -m_circle.m_radius);
      break;
    }
    default:
    {
      break;
    } //! should never reach here
  }
  //! return sorted point pair
  const vfc::float32_t l_xMin = std::min(l_oppositePoint.x(), m_circle.m_center.x());
  const vfc::float32_t l_xMax = std::max(l_oppositePoint.x(), m_circle.m_center.x());
  const vfc::float32_t l_yMin = std::min(l_oppositePoint.y(), m_circle.m_center.y());
  const vfc::float32_t l_yMax = std::max(l_oppositePoint.y(), m_circle.m_center.y());
  return pc::util::Box2D(l_xMin, l_yMin, l_xMax, l_yMax);
}


GeometricShape::PointList CornerArc::intersect(const pc::util::LineSegment2D& f_lineSegment) const
{
  PointList l_result;
  osg::Vec2f l_intersectionA;
  osg::Vec2f l_intersectionB;
  const vfc::uint32_t l_numIntersections = intersectLineCircle(
    f_lineSegment,
    m_circle.m_center,
    m_circle.m_radius,
    l_intersectionA,
    l_intersectionB);

  if (l_numIntersections > 0u)
  {
    const pc::util::Box2D l_bounds = getBoundingBox();
    if (isInsideBox(l_bounds, l_intersectionA))
    {
      l_result.push_back(l_intersectionA);
    }
    if (l_numIntersections > 1u)
    {
      if (isInsideBox(l_bounds, l_intersectionB))
      {
        l_result.push_back(l_intersectionB);
      }
    }
  }
  return l_result;
}


} // namespace ptsoverlay
} // namespace assets
} // namespace cc // PRQA S 1041
