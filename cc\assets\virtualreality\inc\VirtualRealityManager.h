//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityManager.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYMANAGER_H
#define CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYMANAGER_H

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/virtualreality/inc/VirtualRealityFactory.h"
#include "cc/assets/virtualreality/inc/VirtualRealityDataHandler.h"
#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"

#include <osg/Group>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace virtualreality
{

//!
//! VirtualRealityManager
//!
class VirtualRealityManager : public osg::Group
{
public:
  VirtualRealityManager(pc::core::Framework* f_framework);

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
  ~VirtualRealityManager();

private:
  //! Copy constructor is not permitted.
  VirtualRealityManager(const VirtualRealityManager& other) = delete;
  //! Copy assignment operator is not permitted.
  VirtualRealityManager& operator=(const VirtualRealityManager& other) = delete;

  void update();

  VirtualRealityObject* getVirtualObject(vfc::uint8_t f_index);

  VirtualParkSlot* getVirtualSlot(vfc::uint8_t f_index);

  LowpolyPedestrian* getVirtualPedestrian(vfc::uint8_t f_index);

  pc::core::Framework* m_framework;
  osg::ref_ptr<osg::Switch> m_virtualRealityAssets;
  unsigned int m_lastUpdate;

  osg::Matrixf m_MVPW_matrix;
  osg::Matrixf m_MVP_matrix;

  VirtualRealityDataHandler* m_dataHandler;

};



} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_VIRTUALREALITYMANAGER_H
