//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/WheelTrack.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060

#include "osgDB/ReadFile"
#include "osgDB/WriteFile"
#include "osg/Texture2D"
#include "vfc/core/vfc_types.hpp"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h"

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{

// This multiplier is to make the quad stripe bigger to have enough room for the blur on the downsampled mipmaps
constexpr vfc::float32_t g_blurMul = 1.2f; // (1 <= )

/**
 * Calculates the parameter for each track regarding to the f_side it is and pass them to Frame.cpp to generate the
 * vertices for the geometry
 * @param f_side: If it is for the tyres on the right or left side
 * @param f_trajParams: General const parameters for all trackTypes that are given by TrajectoryCodingParams with
 * CodingParamters.xml
 * @param f_numOfVerts: Number of vertices to generate the geometry afterwards
 * @param f_wheelTrackType: Defines which type/part of track it is referring to - look at definition of WheelTrackType
 */
WheelTrack::WheelTrack(
    pc::core::Framework*                               f_framework,
    cc::assets::trajectory::commontypes::Side_en       f_side,
    vfc::float32_t                                              f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    const cc::assets::trajectory::DL1* const           f_distanceLine,
    vfc::uint32_t                                       f_numOfVerts,
    WheelTrackType                                     f_wheelTrackType)
    : GeneralTrajectoryLine{f_framework, f_side, 2u, f_height, f_trajParams, true}
    , mc_numOfVerts{f_numOfVerts}
    , m_currActionPointDist{0.0f}
    , m_lastActionPointDist{0.0f}
    , m_wheelTrackType{f_wheelTrackType}
    , m_distanceLine{f_distanceLine}
{
    m_geometry->addPrimitiveSet( // PRQA S 3803
        new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES))); // PRQA S 3804  // PRQA S 3803

    osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*>(m_geometry->getColorArray()); // PRQA S 3076
    l_colors->setBinding(osg::Array::BIND_PER_VERTEX);

    osg::Vec2ubArray* const l_texCoords = new osg::Vec2ubArray;
    l_texCoords->setNormalize(true);
    l_texCoords->reserve(2u * mc_numOfVerts);
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
    {
        l_texCoords->push_back(osg::Vec2ub(0u, 127u));
    }
    for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < mc_numOfVerts; l_vertexIndex++)
    {
        l_texCoords->push_back(osg::Vec2ub(255u, 127u));
    }
    m_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

    const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
    const vfc::float32_t lc_halfWholeWidth =
        m_trajParams.WheelTrack_Width_Whole * 0.5f; // Half of the whole width of the wheel track
    const vfc::float32_t lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;
    m_lineGeometryWidth              = lc_halfGeometryWidth * 2.0f;
    m_geomHeight                     = (m_trajParams.ActionPoint_Length + m_trajParams.GradientWidth) * g_blurMul;
    const vfc::float32_t l_blurHeight        = (m_geomHeight - (m_trajParams.ActionPoint_Length + m_trajParams.GradientWidth)) * 0.5f;
    m_actionPointFarToMidDist = l_blurHeight + m_trajParams.GradientWidth * 0.5f;
}

WheelTrack::~WheelTrack() = default;

osg::Image* WheelTrack::create1DTexture() const // PRQA S 6043
{
    constexpr vfc::uint32_t lc_imageWidth         = 256u; // Image width in pixels.
    constexpr vfc::uint32_t lc_imageHeight        = 1u;   // Image height in pixels.
    constexpr vfc::uint32_t lc_imageDepth         = 1u;   // Image depth in pixels, in case of a 3D image.
    constexpr vfc::float32_t        lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1);

    const vfc::float32_t lc_halfGradientWidth = std::abs(m_trajParams.GradientWidth) * 0.5f;
    const vfc::float32_t lc_halfWholeWidth =
        m_trajParams.WheelTrack_Width_Whole * 0.5f; // Half of the whole width of the wheel track
    const vfc::float32_t lc_halfGeometryWidth = (lc_halfWholeWidth + lc_halfGradientWidth) * g_blurMul;

    std::array<vfc::float32_t, 4> l_absDistancesFromCenter; // 0..3: From outermost to innermost
    l_absDistancesFromCenter[0u] = lc_halfWholeWidth + lc_halfGradientWidth;
    l_absDistancesFromCenter[1u] = lc_halfWholeWidth - lc_halfGradientWidth;
    l_absDistancesFromCenter[2u] = lc_halfWholeWidth - m_trajParams.WheelTrack_Width_BorderLine + lc_halfGradientWidth;
    l_absDistancesFromCenter[3u] = lc_halfWholeWidth - m_trajParams.WheelTrack_Width_BorderLine - lc_halfGradientWidth;

    std::array<vfc::float32_t, 8> l_normalizedPositions; // 0..7: From left to right
    l_normalizedPositions[0u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0u]) / m_lineGeometryWidth;
    l_normalizedPositions[1u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1u]) / m_lineGeometryWidth;
    l_normalizedPositions[2u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[2u]) / m_lineGeometryWidth;
    l_normalizedPositions[3u] = (lc_halfGeometryWidth - l_absDistancesFromCenter[3u]) / m_lineGeometryWidth;
    l_normalizedPositions[4u] = 1.0f - l_normalizedPositions[3u];
    l_normalizedPositions[5u] = 1.0f - l_normalizedPositions[2u];
    l_normalizedPositions[6u] = 1.0f - l_normalizedPositions[1u];
    l_normalizedPositions[7u] = 1.0f - l_normalizedPositions[0u];

    osg::Vec4ub l_lineColor_Inside;
    osg::Vec4ub l_lineColor_BorderLine;

    switch (m_wheelTrackType)
    {
    case CLOSER_PART:
    {
        l_lineColor_Inside     = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Auto_Close_Inside);
        l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Auto_Close_BorderLine);
    }
    break;

    case FURTHER_PART:
    {
        l_lineColor_Inside     = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Auto_Far_Inside);
        l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Auto_Far_BorderLine);
    }
    break;

    case WHOLE:
    {
        l_lineColor_Inside     = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Manual_Inside);
        l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Manual_BorderLine);
    }
    break;

    case EXTRA_MANUAL:
    {
        l_lineColor_Inside     = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Manual_Inside);
        l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Manual_BorderLine);
    }
    break;

    case EXTRA_AUTO:
    default:
    {
        l_lineColor_Inside     = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Auto_Close_Inside);
        l_lineColor_BorderLine = pc::util::osgx::toVec4ub(m_trajParams.WheelTrack_Color_Auto_Close_BorderLine);
    }
    break;
    }

    osg::Vec4ub l_lineColor_Outside = l_lineColor_BorderLine;
    l_lineColor_Outside.a()         = 0u;

    osg::Image* const l_image = new osg::Image;
    l_image->allocateImage(
        static_cast<vfc::int32_t>(lc_imageWidth),
        static_cast<vfc::int32_t>(lc_imageHeight),
        static_cast<vfc::int32_t>(lc_imageDepth),
        static_cast<GLenum>(GL_RGBA),
        static_cast<GLenum>(GL_UNSIGNED_BYTE));
    osg::Vec4ub* l_data = reinterpret_cast<osg::Vec4ub*>(l_image->data());
    for (vfc::uint32_t x = 0u; x < lc_imageWidth; ++x)
    {
        const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

        if ((l_x_normalized < l_normalizedPositions[0u]) || (l_x_normalized > l_normalizedPositions[7u]))
        {
            // Outside the wheel track
            (*l_data) = l_lineColor_Outside;
        }
        else if ((l_x_normalized > l_normalizedPositions[3u]) && (l_x_normalized < l_normalizedPositions[4u]))
        {
            // Middle of the wheel track
            (*l_data) = l_lineColor_Inside;
        }
        else if ((l_x_normalized > l_normalizedPositions[1u]) && (l_x_normalized < l_normalizedPositions[2u]))
        {
            // Middle of the left border line
            (*l_data) = l_lineColor_BorderLine;
        }
        else if ((l_x_normalized > l_normalizedPositions[5u]) && (l_x_normalized < l_normalizedPositions[6u]))
        {
            // Middle of the right border line
            (*l_data) = l_lineColor_BorderLine;
        }
        else
        {
            // Gradient
            if (l_x_normalized <= l_normalizedPositions[1u])
            {
                // Left border line, left gradient
                (*l_data) = helper::smoothstep_Vec4ub( // PRQA S 2759
                    l_lineColor_Outside,
                    l_lineColor_BorderLine,
                    l_normalizedPositions[0u],
                    l_normalizedPositions[1u],
                    l_x_normalized);
            }
            else if (l_x_normalized >= l_normalizedPositions[6u])
            {
                // Right border line, right gradient
                (*l_data) = helper::smoothstep_Vec4ub( // PRQA S 2759
                    l_lineColor_BorderLine,
                    l_lineColor_Outside,
                    l_normalizedPositions[6u],
                    l_normalizedPositions[7u],
                    l_x_normalized);
            }
            else
            {
                // Inner gradient of either border lines
                if (l_x_normalized <= l_normalizedPositions[3u])
                {
                    // Left border line, right gradient
                    (*l_data) = helper::smoothstep_Vec4ub( // PRQA S 2759
                        l_lineColor_BorderLine,
                        l_lineColor_Inside,
                        l_normalizedPositions[2u],
                        l_normalizedPositions[3u],
                        l_x_normalized);
                }
                else //(l_x_normalized >= l_normalizedPositions[4u])
                {
                    // Right border line, left gradient
                    (*l_data) = helper::smoothstep_Vec4ub( // PRQA S 2759
                        l_lineColor_Inside,
                        l_lineColor_BorderLine,
                        l_normalizedPositions[4u],
                        l_normalizedPositions[5u],
                        l_x_normalized);
                }
            }
        }
        ++l_data;
    }

    return l_image;
}

void WheelTrack::generateVertexData()
{
    generateVertexData_usingTexture();
}

void WheelTrack::setType(WheelTrackType f_wheelTrackType)
{
    m_wheelTrackType = f_wheelTrackType;
}

void WheelTrack::generateVertexData_usingTexture() // PRQA S 6040  // PRQA S 6041  // PRQA S 6044 // PRQA S 2755
{
    // *** 1. Create frame ***
    m_frame.removeAllPoints();

    vfc::float32_t       l_wheelCenterAngle              = 0.0f;
    vfc::float32_t       l_wheelCenterRadius             = 0.0f;
    vfc::float32_t       l_wheelCenterLongitudinalPos    = 0.0f;
    const vfc::float32_t lc_halfGeometryWidth            = m_lineGeometryWidth * 0.5f;
    const vfc::float32_t lc_halfGeometryWidthWithoutBlur = m_lineGeometryWidth * 0.5f / 1.2f;

    if (cc::assets::trajectory::commontypes::Rotation_enm ==
        sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType)
    {
        // When it's rotation
        if ((EXTRA_MANUAL == m_wheelTrackType) || (EXTRA_AUTO == m_wheelTrackType))
        {
            if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
            {
                // Left wheel track:
                m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

                m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
                m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
                l_wheelCenterAngle  = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Angle;
                l_wheelCenterRadius = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Radius;
            }
            else
            {
                // Right wheel track:
                m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

                m_frame.setVertexLineRadius(0u, m_frameRadiuses[1u]);
                m_frame.setVertexLineRadius(1u, m_frameRadiuses[0u]);
                l_wheelCenterAngle  = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Angle;
                l_wheelCenterRadius = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Radius;
            }
        }
        else
        {
            if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
            {
                // Left wheel track:
                m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

                m_frame.setVertexLineRadius(0u, m_frameRadiuses[0u]);
                m_frame.setVertexLineRadius(1u, m_frameRadiuses[1u]);
                l_wheelCenterAngle  = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Angle;
                l_wheelCenterRadius = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius;
            }
            else
            {
                // Right wheel track:
                m_frameRadiuses[0u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                m_frameRadiuses[1u] = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius +
                                      lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

                m_frame.setVertexLineRadius(0u, m_frameRadiuses[1u]);
                m_frame.setVertexLineRadius(1u, m_frameRadiuses[0u]);
                l_wheelCenterAngle  = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Angle;
                l_wheelCenterRadius = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius;
            }
        }
    }
    else
    {
        // When it's translation
        if ((EXTRA_MANUAL == m_wheelTrackType) || (EXTRA_AUTO == m_wheelTrackType))
        {
            if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
            {
                // Left wheel track:
                m_frameLateralOffsets[0u] =
                    sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                m_frameLateralOffsets[1u] =
                    sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

                m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
                m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
                l_wheelCenterLongitudinalPos =
                    sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_OppToDrvDir.Pos.x();
            }
            else
            {
                // Right wheel track:
                m_frameLateralOffsets[0u] =
                    sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                m_frameLateralOffsets[1u] =
                    sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

                m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[1u]);
                m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[0u]);
                l_wheelCenterLongitudinalPos =
                    sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_OppToDrvDir.Pos.x();
            }
        }
        else
        {
            if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
            {
                // Left wheel track:
                m_frameLateralOffsets[0u] =
                    sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                m_frameLateralOffsets[1u] =
                    sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;

                m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[0u]);
                m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[1u]);
                l_wheelCenterLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.x();
            }
            else
            {
                // Right wheel track:
                m_frameLateralOffsets[0u] =
                    sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                m_frameLateralOffsets[1u] =
                    sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() +
                    lc_halfGeometryWidth * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;

                m_frame.setVertexLineOffset(0u, m_frameLateralOffsets[1u]);
                m_frame.setVertexLineOffset(1u, m_frameLateralOffsets[0u]);
                l_wheelCenterLongitudinalPos = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.x();
            }
        }
    }
    m_frame.setBumperLineAngle(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Angle);
    m_frame.setBumperLinePos(0u, sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPoint.Pos.x());

    const osg::Vec4f l_lineColor = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f); // With texturing just the alpha matters in the shader.
    cc::assets::trajectory::commontypes::ControlPoint_st l_controlPoint; // PRQA S 4102

    vfc::float32_t l_actionPointMidPos = 0.0f;
    if (cc::assets::trajectory::mainlogic::Forward_enm ==
        sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
    {
        l_actionPointMidPos = sm_mainLogicRefPtr->getInputDataRef().External.Parking.ActionPointDist_Front;
    }
    else
    {
        l_actionPointMidPos = sm_mainLogicRefPtr->getInputDataRef().External.Parking.ActionPointDist_Rear;
    }

    if (m_trajParams.Length < l_actionPointMidPos)
    {
        l_actionPointMidPos = m_trajParams.Length;
    }

    // save the current ActionPointPosition to know when the animation is done
    m_currActionPointDist = l_actionPointMidPos;
    const vfc::float32_t l_geomHeight    = 3.0f * m_actionPointFarToMidDist;

    vfc::float32_t      l_radius      = 1.0f;
    vfc::float32_t      l_radiusLeft  = 1.0f;
    vfc::float32_t      l_radiusRight = 1.0f;
    vfc::float32_t      l_newXNear    = 0.0f;
    osg::Vec2f l_startPoint;
    if ((CLOSER_PART == m_wheelTrackType) || (FURTHER_PART == m_wheelTrackType))
    {
        // preparation for ROTATION
        // l_newXNear = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault -
        // sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x()
        //           + (m_lastActionPointDist + m_trajParams.RenderOffset + m_distanceLine->getDistanceLineWidth()) *
        //           sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
        if (cc::assets::trajectory::mainlogic::Forward_enm ==
            sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
        {
            l_newXNear =
                sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault -
                sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x() +
                (m_lastActionPointDist + m_trajParams.RenderOffset_Front + m_distanceLine->getDistanceLineWidth()) *
                    sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
        }
        else
        {
            l_newXNear =
                sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault -
                sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x() +
                (m_lastActionPointDist + m_trajParams.RenderOffset_Rear + m_distanceLine->getDistanceLineWidth()) *
                    sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
        }

        // for the case Back- or FrontSteering is getting larger or smaller than the other so Left and RightTurn is
        // changing it must be calculated which radius will be used
        if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
        {
            l_radiusLeft = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius +
                           lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
            l_radiusRight = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Radius +
                            lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
        }
        else // if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
        {
            l_radiusLeft = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius +
                           lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
            l_radiusRight = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Radius +
                            lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
        }

        vfc::float32_t l_startPointLeft =
            std::cos(-std::acos(l_newXNear / l_radiusLeft) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul) *
            m_frameRadiuses[1u];
        vfc::float32_t l_startPointRight =
            std::cos(-std::acos(l_newXNear / l_radiusRight) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul) *
            m_frameRadiuses[1u];
        l_startPointLeft += sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x();
        l_startPointRight += sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint.x();

        if (sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * l_startPointRight >
            sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * l_startPointLeft)
        {
            l_radius = l_radiusRight;
        }
        else
        {
            l_radius = l_radiusLeft;
        }

        // preparation TRANSLATION
        vfc::float32_t l_offset = 0.0f;
        if (isEqual(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul, 1.0f))
        {
            if (sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle >= 0.0f)
            {
                if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                }
                else // if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                }
            }
            // right turn
            else // if(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle < 0.0)
            {
                if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                }
                else // if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                }
            }
        }
        else // if(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul == -1.0)
        {
            if (sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle >= 0.0)
            {
                if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                }
                else // if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToRightOffsetMul;
                }
            }
            // right turn
            else // if(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle < 0.0)
            {
                if (cc::assets::trajectory::commontypes::Left_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().LeftWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                }
                else // if(cc::assets::trajectory::commontypes::Right_enm == mc_side)
                {
                    l_offset = sm_mainLogicRefPtr->getModelDataRef().RightWheelCenter_InDrvDir.Pos.y() +
                               lc_halfGeometryWidthWithoutBlur * sm_mainLogicRefPtr->getModelDataRef().ToLeftOffsetMul;
                }
            }
        }

        // l_startPoint.x() = sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault
        //                     + sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul
        //                       * (m_lastActionPointDist + m_trajParams.RenderOffset +
        //                       m_distanceLine->getDistanceLineWidth());
        if (cc::assets::trajectory::mainlogic::Forward_enm ==
            sm_mainLogicRefPtr->getInputDataRef().External.Car.DrivingDirection)
        {
            l_startPoint.x() =
                sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault +
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                    (m_lastActionPointDist + m_trajParams.RenderOffset_Front + m_distanceLine->getDistanceLineWidth());
        }
        else
        {
            l_startPoint.x() =
                sm_mainLogicRefPtr->getModelDataRef().LongitudinalTouchPointDefault +
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                    (m_lastActionPointDist + m_trajParams.RenderOffset_Rear + m_distanceLine->getDistanceLineWidth());
        }
        l_startPoint.y() = l_offset;

        const vfc::float32_t l_angleRad = osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
        const cc::assets::trajectory::helper::RotationFunctor l_translationAngle(l_angleRad);
        // rotate the offset(y) and the length(x) seperately around 0/0
        osg::Vec2f l_rotatedOffset   = osg::Vec2f(0.0f, l_startPoint.y());
        osg::Vec2f l_rotatedLongitud = osg::Vec2f(l_startPoint.x(), 0.0);
        l_translationAngle.rotate(l_rotatedOffset);
        l_translationAngle.rotate(l_rotatedLongitud);

        // factor to multiply the old x value with - substract before that the rotatedOffset
        const vfc::float32_t l_factor = (l_startPoint.x() - l_rotatedOffset.x()) / l_rotatedLongitud.x();
        l_startPoint.x() *= l_factor;
    }

    vfc::float32_t l_startAngle = 0.0f;
    vfc::float32_t l_endAngle   = 0.0f;
    vfc::float32_t l_startPos   = 0.0f;
    vfc::float32_t l_endPos     = 0.0f;
    // green part from wheelbase till the ActionPoint begins
    switch (m_wheelTrackType)
    {
    case CLOSER_PART:
    {
        l_startAngle = l_wheelCenterAngle;
        // endAngle is the same angle like ActionPointsStartAngle
        l_endAngle = -std::acos(l_newXNear / l_radius) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul;
        l_startPos = l_wheelCenterLongitudinalPos;
        // endPosition is also the startAngle of the ActionPoint
        l_endPos = l_startPoint.x();

        // calculate the l_endAngleAP of the far Actionpoint
        const vfc::float32_t l_endAngleAPLeft =
            -std::acos(l_newXNear / l_radiusLeft) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul +
            sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * std::atan(l_geomHeight / l_radius);
        const vfc::float32_t l_endAngleAPRight =
            -std::acos(l_newXNear / l_radiusRight) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul +
            sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * std::atan(l_geomHeight / l_radius);

        // if the actionPoint far is already INF then the trajectory should be rendered already longer
        // Forward
        if (isEqual(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul, 1.0f))
        {
            // Left turn
            if ((helper::isInfinity(l_endAngleAPLeft) || helper::isInfinity(l_endAngleAPRight)) &&
                isEqual(sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul, 1.0f))
            {
                l_endAngle = static_cast<vfc::float32_t>(osg::PI_2);
            }
            // Right turn
            else if (
                (helper::isInfinity(l_endAngleAPLeft) || helper::isInfinity(l_endAngleAPRight)) &&
                isEqual(sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul, -1.0f))
            {
                l_endAngle = static_cast<vfc::float32_t>(-osg::PI_2);
            }
            else
            {
                // Do nothing
            }
        }
        else // if(sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul == -1.0) // Backward
        {
            // Left turn
            if ((helper::isInfinity(l_endAngleAPLeft) || helper::isInfinity(l_endAngleAPRight)) &&
                isEqual(sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul, 1.0f))
            {
                l_endAngle = static_cast<vfc::float32_t>(-1.5 * osg::PI);
            }
            // Right turn
            else if (
                (helper::isInfinity(l_endAngleAPLeft) || helper::isInfinity(l_endAngleAPRight)) &&
                isEqual(sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul, -1.0f))
            {
                l_endAngle = static_cast<vfc::float32_t>(1.5 * osg::PI);
            }
            else
            {
                // Do nothing
            }
        }
    }
    break;
    // grey part from where the ActionPoint ends till visibel end
    case FURTHER_PART:
    {
        // calculate the l_endAngleAP of the far Actionpoint
        const vfc::float32_t l_endAngleAPLeft =
            -std::acos(l_newXNear / l_radiusLeft) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul +
            sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * std::atan(l_geomHeight / l_radius);
        const vfc::float32_t l_endAngleAPRight =
            -std::acos(l_newXNear / l_radiusRight) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul +
            sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * std::atan(l_geomHeight / l_radius);

        // if the actionPoint far is already INF then the FurtherPart shouldnt be rendered anymore - at the moment is
        // the angle just set to 0.0
        if (helper::isInfinity(l_endAngleAPLeft) || helper::isInfinity(l_endAngleAPRight))
        {
            l_startAngle = 0.0f;
            l_endAngle   = 0.0f;
        }
        else
        {
            // startAngle is the EndAngle of the ActionPoint
            l_startAngle = -std::acos(l_newXNear / l_radius) * sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul +
                           sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                               sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                               std::atan(l_geomHeight / l_radius);
            l_endAngle = m_frame.getBumperLineAngle(0u) +
                         sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                             sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                             (m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);
        }

        // startPos is the EndPos of the ActionPoint
        l_startPos = l_startPoint.x() + l_geomHeight * sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul;
        l_endPos   = m_frame.getBumperLinePos(0u) + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                                      sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                                                      m_trajParams.Length;
    }
    break;
    case WHOLE:
    {
        l_startAngle = l_wheelCenterAngle;
        l_endAngle   = m_frame.getBumperLineAngle(0u) +
                     sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                         sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                         (m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);
        l_startPos = l_wheelCenterLongitudinalPos;
        l_endPos   = m_frame.getBumperLinePos(0u) + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                                      sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                                                      m_trajParams.Length;
    }
    break;
    default: // if ((EXTRA_MANUAL == m_wheelTrackType) || (EXTRA_AUTO == m_wheelTrackType))
    {
        l_startAngle = l_wheelCenterAngle;
        l_endAngle   = l_startAngle +
                     sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                         sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                         ((sm_mainLogicRefPtr->getInputDataRef().External.Car.Wheelbase - 0.3f) / l_wheelCenterRadius);
        l_startPos = l_wheelCenterLongitudinalPos;
        l_endPos   = l_startPos + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                    sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                                    (sm_mainLogicRefPtr->getInputDataRef().External.Car.Wheelbase - 0.3f);
    }
    break;
    }

    // Controllpoint left(0),right(1)
    l_controlPoint.Angle           = l_startAngle;
    l_controlPoint.LongitudinalPos = l_startPos;
    l_controlPoint.Color           = l_lineColor;
    l_controlPoint.Index           = 0u; // Dummy value. Will be calculated later.
    m_frame.addControlPoint(0u, l_controlPoint);
    m_frame.addControlPoint(1u, l_controlPoint);

    const vfc::float32_t l_fadeInStartAngle = l_wheelCenterAngle + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                                        sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                                                        (0.3f / m_frame.getVertexLineRadius(0u));

    const vfc::float32_t l_fadeInStartPos =
        l_wheelCenterLongitudinalPos + sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                           sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * 0.3f;

    const vfc::float32_t l_fadeInEndAngle = l_wheelCenterAngle;

    const vfc::float32_t l_fadeInEndPos = l_wheelCenterLongitudinalPos;

    m_frame.setFadeInStartAngle(0u, l_fadeInStartAngle);
    m_frame.setFadeInStartAngle(1u, l_fadeInStartAngle);

    m_frame.setFadeInStartPos(0u, l_fadeInStartPos);
    m_frame.setFadeInStartPos(1u, l_fadeInStartPos);

    m_frame.setFadeInEndAngle(0u, l_fadeInEndAngle);
    m_frame.setFadeInEndAngle(1u, l_fadeInEndAngle);

    m_frame.setFadeInEndPos(0u, l_fadeInEndPos);
    m_frame.setFadeInEndPos(1u, l_fadeInEndPos);

    // Controllpoint left(0),right(1)
    l_controlPoint.Angle           = l_endAngle;
    l_controlPoint.LongitudinalPos = l_endPos;
    l_controlPoint.Color           = l_lineColor;
    m_frame.addControlPoint(0u, l_controlPoint);
    m_frame.addControlPoint(1u, l_controlPoint);

    vfc::float32_t l_fadeOutStartAngle = 0.0f;
    vfc::float32_t l_fadeOutStartPos   = 0.0f;
    vfc::float32_t l_fadeOutEndAngle   = 0.0f;
    vfc::float32_t l_fadeOutEndPos     = 0.0f;

    if ((EXTRA_MANUAL == m_wheelTrackType) || (EXTRA_AUTO == m_wheelTrackType))
    {
        l_fadeOutStartAngle = l_endAngle - sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                               sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                                               (1.0f / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);

        l_fadeOutStartPos = l_endPos - sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                           sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * 1.0f;

        l_fadeOutEndAngle = l_endAngle;

        l_fadeOutEndPos = l_endPos;
    }
    else
    {
        const vfc::float32_t l_trajEndAngle =
            m_frame.getBumperLineAngle(0u) +
            sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul *
                (m_trajParams.Length / sm_mainLogicRefPtr->getModelDataRef().BumperCenterPoint.Radius);

        l_fadeOutStartAngle = m_frame.getBumperLineAngle(0u) + 0.5f * (l_trajEndAngle - m_frame.getBumperLineAngle(0u));

        const vfc::float32_t l_trajEndPos = m_frame.getBumperLinePos(0u) +
                             sm_mainLogicRefPtr->getModelDataRef().LeftRightDirMul *
                                 sm_mainLogicRefPtr->getModelDataRef().ForwardBackwardDirMul * m_trajParams.Length;

        l_fadeOutStartPos = m_frame.getBumperLinePos(0u) + 0.5f * (l_trajEndPos - m_frame.getBumperLinePos(0u));

        l_fadeOutEndAngle = l_trajEndAngle;

        l_fadeOutEndPos = l_trajEndPos;
    }

    m_frame.setFadeOutStartAngle(0u, l_fadeOutStartAngle);
    m_frame.setFadeOutStartAngle(1u, l_fadeOutStartAngle);

    m_frame.setFadeOutStartPos(0u, l_fadeOutStartPos);
    m_frame.setFadeOutStartPos(1u, l_fadeOutStartPos);

    m_frame.setFadeOutEndAngle(0u, l_fadeOutEndAngle);
    m_frame.setFadeOutEndAngle(1u, l_fadeOutEndAngle);

    m_frame.setFadeOutEndPos(0u, l_fadeOutEndPos);
    m_frame.setFadeOutEndPos(1u, l_fadeOutEndPos);

    // *** 2. Create vertices (and colors) ***
    // Generate line vertices
    osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3076
    l_vertices->clear();

    osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*>(m_geometry->getColorArray()); // PRQA S 3076
    l_colors->clear();

    const vfc::float32_t l_translationAngle_Rad =
        osg::DegreesToRadians(sm_mainLogicRefPtr->getInputDataRef().Internal.TranslationAngle);
    m_frame.generateVertices( // PRQA S 3803
        0u,
        0u,
        1u,
        sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint,
        m_height,
        l_vertices,
        l_colors,
        cc::assets::trajectory::frame::Manual_enm,
        mc_numOfVerts,
        sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);

    m_frame.generateVertices( // PRQA S 3803
        1u,
        0u,
        1u,
        sm_mainLogicRefPtr->getInputDataRef().Internal.AckermannPoint,
        m_height,
        l_vertices,
        l_colors,
        cc::assets::trajectory::frame::Manual_enm,
        mc_numOfVerts,
        sm_mainLogicRefPtr->getInputDataRef().Internal.VehicleMovementType,
        l_translationAngle_Rad);

    // *** 3. Create indices ***
    osg::DrawElementsUShort* const l_indices = static_cast<osg::DrawElementsUShort*>(m_geometry->getPrimitiveSet(0u)); // PRQA S 3076
    l_indices->clear();
    m_frame.generateIndices(0u, 0u, 1u, 0u, mc_numOfVerts, l_indices);

    l_vertices->dirty();
    l_colors->dirty();
    l_indices->dirty();
    m_geometry->dirtyBound();

    setCull(false);
}

void WheelTrack::setLastActionpointDist(vfc::float32_t f_lastActionPointDist)
{
    m_lastActionPointDist = f_lastActionPointDist;
}

void WheelTrack::animate()
{
    if (m_currActionPointDist > m_lastActionPointDist)
    {
        generateVertexData();
    }
}

} // namespace trajectory
} // namespace assets
} // namespace cc
