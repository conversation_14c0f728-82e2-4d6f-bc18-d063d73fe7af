//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: YRN1WX Yang Rui (BCSC-EPA1)
//  Department: BCSC-EPA1
//=============================================================================
/// @swcomponent SVS BYD
/// @file  ParkingSlot.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ParkingSlot.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"

using pc::util::logging::g_AppContext;

#define PARKINGSLOT_DELAY 0
namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for parking modes
enum class ParkingSlotType : vfc::uint8_t
{
  // parking slots
  //guideline
  GUIDELINE_CROSSSLOT_RIGHT,
  GUIDELINE_CROSSSLOT_LEFT,
  GUIDELINE_PARASLOT_RIGHT,
  GUIDELINE_PARASLOT_LEFT,
  GUIDELINE_DIAGSLOT_RIGHT,
  GUIDELINE_DIAGSLOT_LEFT,
  // selected slot
  // combination of diagonal slots & guideline
  COMBINATION_DIAGSLOT_RIGHT,
  COMBINATION_DIAGSLOT_LEFT,
  // combination of diagonal slot & vehicle in complete
  COMBINATION_DIAGSLOT_REARIN_RIGHT,
  COMBINATION_DIAGSLOT_REARIN_LEFT,
  COMBINATION_DIAGSLOT_FRONTIN_RIGHT,
  COMBINATION_DIAGSLOT_FRONTIN_LEFT,
  // park out slot
  PARKING_OUT_COMBINATION_PARA_RIGHT,
  PARKING_OUT_COMBINATION_PARA_LEFT,
  PARKING_OUT_COMBINATION_PARA_RIGHT_GUIDANCE_AVTIVE,
  PARKING_OUT_COMBINATION_PARA_LEFT_GUIDANCE_AVTIVE,
  PARKING_OUT_PARASLOT_RIGHT,
  PARKING_OUT_PARASLOT_LEFT,
  PARKING_OUT_COMPLETE_COMBINATION_PARA_RIGHT,
  PARKING_OUT_COMPLETE_COMBINATION_PARA_LEFT,
  PARKING_OUT_COMBINATION_CROSS_RIGHT,
  PARKING_OUT_COMBINATION_CROSS_LEFT,
  PARKING_OUT_COMBINATION_CROSS_RIGHT_GUIDANCE_AVTIVE,
  PARKING_OUT_COMBINATION_CROSS_LEFT_GUIDANCE_AVTIVE,
  PARKING_OUT_COMPLETE_COMBINATION_CROSS_RIGHT,
  PARKING_OUT_COMPLETE_COMBINATION_CROSS_LEFT,
  //vehicle icon
  PARKING_VEHICLE_CROSS_NO_SLOT,
  PARKING_VEHICLE_CROSS,
  PARKING_VEHICLE_PARALLEL,
  PARKING_VEHICLE_DIAGONAL,
  // parking type selection
  PARKING_SEARCHING_TEXT_SELECT_TYPE,
  PARKING_SEARCHING_BUTTON_FRONT_IN,
  PARKING_SEARCHING_BUTTON_REAR_IN,

};

enum class ParkSlotSide : vfc::uint8_t
{
  LEFTPARKSLOTS,
  RIGHTPARKSLOTS,
};

static vfc::uint16_t g_ConversionTicks = 0u;
pc::util::coding::Item<ParkingSlotSettings> g_managerSettings("ParkingSlot");

//!
//! @brief Construct a new ParkingSearching Manager:: ParkingSearching Manager object
//!
//! @param f_config
//!
ParkingSlotManager::ParkingSlotManager()
  : m_lastConfigUpdate{~0u}
  , m_settingParkSlot{}
  , m_ViewButtonParkInSlotsSelectingDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
  , m_ViewButtonParkingInTypeDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
{
}

// ! transfer to start from bottom left
osg::Vec2f ParkingSlotManager::transferToBottomLeft(const osg::Vec2f f_iconPos)
{
  if (s_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
  {
    return osg::Vec2f(f_iconPos.y() + static_cast<vfc::float32_t>(cc::core::g_views->m_vertMainViewport.m_size.x()), f_iconPos.x());
  }
  else
  {
    return osg::Vec2f(f_iconPos.x() - static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_origin.x()), static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.y()) - f_iconPos.y());
  }

}

ParkingSlotManager::~ParkingSlotManager() = default;

void ParkingSlotManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_settingParkSlot.clear(f_imageOverlays);
  // parking slots
  // guideline
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkCrossSlotRight,            g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,                     g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkCrossSlotLeft,             g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,                     g_uiSettings->m_settingHoriGuidelineVertLeftRearIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkParaSlotRight,             g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,                     g_uiSettings->m_settingHoriGuidelineVertRightFrontIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkParaSlotLeft,              g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,                     g_uiSettings->m_settingHoriGuidelineVertLeftFrontIn.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkDiagSlotRight,             g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,                     g_uiSettings->m_settingHoriGuidelineParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkDiagSlotLeft,              g_uiSettings->m_settingHoriGuidelineVertRightRearIn.m_iconCenter,                     g_uiSettings->m_settingHoriGuidelineParaLeft.m_isHoriScreen));
  // selected slot
  // combination
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCombinationDiagRightRearIn,g_uiSettings->m_settingHoriCombinationDiagRight.m_iconCenter,         g_uiSettings->m_settingHoriCombinationDiagRight.m_iconSize,         g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCombinationDiagLeftRearIn, g_uiSettings->m_settingHoriCombinationDiagLeft.m_iconCenter,          g_uiSettings->m_settingHoriCombinationDiagLeft.m_iconSize,          g_uiSettings->m_settingHoriCombinationDiagLeft.m_isHoriScreen));
  // combination of diagonal slot & vehicle in complete
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagRightRearIn,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconCenter,       g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconSize,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagLeftRearIn,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconCenter,        g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconSize,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagRightFrontIn, g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconCenter,       g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_iconSize,  g_uiSettings->m_settingHoriCompleteCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriCompleteCombinationDiagLeftFrontIn,  g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconCenter,        g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_iconSize,   g_uiSettings->m_settingHoriCompleteCombinationDiagLeft.m_isHoriScreen));
  // park out slot
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriParkOutGuidanceCombinationParaRight, g_uiSettings->m_settingParkOutGuidanceCombinationParaRight.m_iconCenter,    g_uiSettings->m_settingParkOutGuidanceCombinationParaRight.m_iconSize,    g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationParaLeft,      g_uiSettings->m_settingParkOutGuidanceCombinationParaLeft.m_iconCenter,     g_uiSettings->m_settingParkOutGuidanceCombinationParaLeft.m_iconSize,     g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationParaRightGuidanceActive, g_uiSettings->m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconCenter,    g_uiSettings->m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconSize,    g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationParaLeftGuidanceActive,      g_uiSettings->m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconCenter,     g_uiSettings->m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconSize,     g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriParkOutSlotParaRight,                g_uiSettings->m_settingHoriParkOutSlotParaRight.m_iconCenter,               g_uiSettings->m_settingHoriParkOutSlotParaRight.m_iconSize,               g_uiSettings->m_settingHoriParkOutSlotParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutSlotParaLeft,                     g_uiSettings->m_settingParkOutSlotParaLeft.m_iconCenter,                    g_uiSettings->m_settingParkOutSlotParaLeft.m_iconSize,                    g_uiSettings->m_settingParkOutSlotParaLeft.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutCompleteCombinationParaRight,     g_uiSettings->m_settingHoriParkOutSlotParaRight.m_iconCenter,               g_uiSettings->m_settingHoriParkOutSlotParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutCompleteCombinationParaLeft,      g_uiSettings->m_settingParkOutSlotParaLeft.m_iconCenter,                    g_uiSettings->m_settingParkOutSlotParaLeft.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathHoriParkOutGuidanceCombinationCrossRight, g_uiSettings->m_settingParkOutGuidanceCombinationParaRight.m_iconCenter,    g_uiSettings->m_settingParkOutGuidanceCombinationParaRight.m_iconSize,    g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationCrossLeft,      g_uiSettings->m_settingParkOutGuidanceCombinationParaLeft.m_iconCenter,     g_uiSettings->m_settingParkOutGuidanceCombinationParaLeft.m_iconSize,     g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationCrossRightGuidanceActive, g_uiSettings->m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconCenter,    g_uiSettings->m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconSize,    g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutGuidanceCombinationCrossLeftGuidanceActive,      g_uiSettings->m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconCenter,     g_uiSettings->m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconSize,     g_uiSettings->m_settingHoriCombinationDiagRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutCompleteCombinationCrossRight,     g_uiSettings->m_settingHoriParkOutSlotParaRight.m_iconCenter,               g_uiSettings->m_settingHoriParkOutSlotParaRight.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParkOutCompleteCombinationCrossLeft,      g_uiSettings->m_settingParkOutSlotParaLeft.m_iconCenter,                    g_uiSettings->m_settingParkOutSlotParaLeft.m_isHoriScreen));
  //vehicle icon
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCrossVehicleNoSlotPic,                   g_uiSettings->m_settingCrossVehiclePic.m_iconCenter,                        g_uiSettings->m_settingCrossVehicleNoSlotPic.m_iconSize,                  g_uiSettings->m_settingCrossVehiclePic.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathCrossVehiclePic,                         g_uiSettings->m_settingCrossVehiclePic.m_iconCenter,                        g_uiSettings->m_settingCrossVehiclePic.m_iconSize,                        g_uiSettings->m_settingCrossVehiclePic.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathParallelVehiclePic,                      g_uiSettings->m_settingCrossVehiclePic.m_iconCenter,                        g_uiSettings->m_settingParallelVehiclePic.m_iconSize,                     g_uiSettings->m_settingCrossVehiclePic.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathDiagonalVehiclePic,                      g_uiSettings->m_settingCrossVehiclePic.m_iconCenter,                        g_uiSettings->m_settingDiagonalVehiclePic.m_iconSize,                     g_uiSettings->m_settingCrossVehiclePic.m_isHoriScreen));
  // parking type selection
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSearchingTextBoxSelectType,              g_uiSettings->m_settingParkingUITopTextWhite.m_iconCenter,                  g_uiSettings->m_settingParkingUITopTextWhite.m_iconSize,                  g_uiSettings->m_settingParkingUITopTextWhite.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSearchingRearInButton,                   g_uiSettings->m_settingSuspendContinueButton.m_iconCenter,                  g_uiSettings->m_settingSuspendContinueButton.m_iconSize,                  g_uiSettings->m_settingSuspendContinueButton.m_isHoriScreen));
  m_settingParkSlot.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathSearchingFrontInButton,                  g_uiSettings->m_settingSuspendQuitButton.m_iconCenter,                      g_uiSettings->m_settingSuspendQuitButton.m_iconSize,                      g_uiSettings->m_settingSuspendQuitButton.m_isHoriScreen));
}

void ParkingSlotManager::update(cc::assets::uielements::CustomImageOverlays* /*f_imageOverlays*/, const core::CustomFramework* f_framework)    // PRQA S 6043 // PRQA S 6040  // PRQA S 6041  // PRQA S 4213
{
  if (f_framework == nullptr)
  {
      return;
  }
  // ! check if config has changed
  // if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  // {
  //   init(f_imageOverlays);
  //   m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  // }
  m_settingParkSlot.setAllEnabled(false);
  this->CleanButtonDispSts();

  // ! read all the signals
  cc::target::common::EAPAPARKMODE                 l_curAPAPARKMODE          = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::EPARKStatusR2L               l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld              l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L            l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndExtR2L         l_curparkDriverIndExt     = cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest;
  cc::target::common::EPARKRecoverIndR2L           l_curparkSuspend          = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  cc::target::common::EPARKDriverIndSearchR2L      l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                         l_curFreeParkingActive    = false;
  cc::target::common::rbp_Type_ParkManeuverType_en l_curparkPSDirection      = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;
  cc::target::common::EPARKSideR2L                 l_curParkOutDirection     = cc::target::common::EPARKSideR2L::PARKSIDE_None;
  std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> l_curparkSpace;

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curAPAPARKMODE = l_parkAPAPARKMODE->m_Data;
  }

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndExtReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndExtDaddy_t* const l_parkDriverIndExt = f_framework->m_parkHmiParkDriverIndExtReceiver.getData();
    l_curparkDriverIndExt = l_parkDriverIndExt->m_Data;
  }

  if (f_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
  {
    const cc::daddy::ParkRecoverIndDaddy_t* const l_parkSuspend = f_framework->m_parkHmiParkingRecoverIndReceiver.getData();
    l_curparkSuspend = l_parkSuspend->m_Data;
  }

  if (f_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = f_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
    l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (f_framework->m_parkPSDirectionSelectedReceiver.hasData())
  {
    const cc::daddy::ParkPSDirectionSelected_t* const l_parkPSDirection = f_framework->m_parkPSDirectionSelectedReceiver.getData();
    l_curparkPSDirection = l_parkPSDirection->m_Data;
  }

  if(f_framework->m_parkAPASlotsReceiver.hasData())
  {
    const cc::daddy::ParkAPA_ParkSpace_t *const l_parkSpace = f_framework->m_parkAPASlotsReceiver.getData();
    for(vfc::uint16_t l_side = 0; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
      for(vfc::uint16_t l_numberPerside = 0; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
      {
        l_curparkSpace[l_side][l_numberPerside] = l_parkSpace->m_Data[l_side][l_numberPerside];
        // XLOG_INFO_OS(g_AppContext) << "!!! local variable l_curparkSpace has been updated "  << XLOG_ENDL;
      }
    }
  }

  if (f_framework->m_parkHmiParkingSideReceiver.hasData())
  {
    const cc::daddy::ParkSideDaddy_t* const l_parkOutDirection = f_framework->m_parkHmiParkingSideReceiver.getData();
    l_curParkOutDirection = l_parkOutDirection->m_Data;
  }

  if (l_curAPAPARKMODE == cc::target::common::EAPAPARKMODE::APAPARKMODE_APA)
  {
    switch (l_curparkStatus) // PRQA S 3139  #code looks fine
    {
    case cc::target::common::EPARKStatusR2L::PARK_Standby:
    {
      g_ConversionTicks = 0u;
      break;
    }
    case cc::target::common::EPARKStatusR2L::PARK_Searching:
    {
      switch (l_curparkDriverInd) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
        // This is a temp modification to handle the cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection as cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop to cover the state machine bug from APA.
        // Final solution needs to be defined later

        // m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setPosition(g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter,pc::assets::Icon::UnitType::Pixel);
        // m_settingParkSlot.getIcon(PARKING_VEHICLE_PIC)->setEnabled(true);
        // m_settingParkSlot.getIcon(PARKING_SEARCHING_TEXT_SELECT_TYPE)->setEnabled(true);
        // m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
        // if ( isASlotSelected(l_curparkSpace) )
        // {
        //   SelectedSlot l_selectedSlot = getSelectedSlot(l_curparkSpace);
        //   if (l_curparkSpace[l_selectedSlot.m_selectedSlotSide][l_selectedSlot.m_selectedSlotId].m_APA_ParkManeuverType == cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBFwdIn_enm)
        //   {
        //     m_settingParkSlot.getIcon(PARKING_SEARCHING_BUTTON_FRONT_IN)->setEnabled(true);
        //     m_settingParkSlot.getIcon(PARKING_SEARCHING_BUTTON_REAR_IN)->setEnabled(true);
        //     m_ViewButtonParkingInTypeDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
        //   }
        // }
        // break;
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
      {
        if (l_curFreeParkingActive == false && l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN)
        {
        }
        break;
      }
      default:
      { 
        break;
      }
      }
      break;
    }
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
    {
      switch (l_curParkngTypeSeld) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EParkngTypeSeld::PARKING_NONE:
      {// do nothing
        break;
      }
      case cc::target::common::EParkngTypeSeld::PARKING_IN:
      {
        #ifndef USE_VIRTUAL_OBJECT
        if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding || l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark)
        {
          SelectedSlot l_selectedSlot = getSelectedSlot(l_curparkSpace);
          manageDisplayLogicInGuidance(l_selectedSlot, l_curparkPSDirection);
        }
        #endif
        break;
      }
      case cc::target::common::EParkngTypeSeld::PARKING_OUT:
      {
        if (l_curparkDriverIndExt    == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_PayAttentionToSurrounding ||  //Req_397
            l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleDriverConfirmPark) //Req_396
        {
          if ((l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Left))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_LEFT_GUIDANCE_AVTIVE))->setEnabled(true);
          }
          if ((l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Right))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_RIGHT_GUIDANCE_AVTIVE))->setEnabled(true);
          }
          if ((l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Left))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_CROSS_LEFT_GUIDANCE_AVTIVE))->setEnabled(true);
          }
          if ((l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Right))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_CROSS_RIGHT_GUIDANCE_AVTIVE))->setEnabled(true);
          }
        }
        else
        {
            //do nothing
        }

        break;
      }
      default:
      {
        break;
      }
      }
      break;
    }
    case cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend:
    {
      if(cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM == l_curparkDriverInd) //Req_443
      {
          if (l_curparkSuspend    == cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand &&
              l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_IN )
          {
              #ifndef USE_VIRTUAL_OBJECT
              SelectedSlot l_selectedSlot = getSelectedSlot(l_curparkSpace);
              manageDisplayLogicInGuidance(l_selectedSlot, l_curparkPSDirection);
              #endif
          }
          else if (l_curparkSuspend    == cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand &&
                    l_curParkngTypeSeld == cc::target::common::EParkngTypeSeld::PARKING_OUT)
          {
            if (l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
            {
              m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_LEFT_GUIDANCE_AVTIVE))->setEnabled(true);
            }
            if (l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
            {
              m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_RIGHT_GUIDANCE_AVTIVE))->setEnabled(true);
            }
          }
          else
          {
          //do nothing
          }
      }

      break;
    }
    case cc::target::common::EPARKStatusR2L::PARK_Completed:
    {
      switch (l_curParkngTypeSeld) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EParkngTypeSeld::PARKING_NONE:
      {// do nothing
        break;
      }
      case cc::target::common::EParkngTypeSeld::PARKING_IN:
      {
        if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted)
        {
          const SelectedSlot l_selectedSlot = getSelectedSlot(l_curparkSpace);
          manageDisplayLogicInComplete(l_selectedSlot);
        }
        break;
      }
      case cc::target::common::EParkngTypeSeld::PARKING_OUT:
      {
        if (l_curparkDriverIndExt == cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_ParkingHasBeenCompleted)
        {
          if ((l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Left))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMPLETE_COMBINATION_PARA_LEFT))->setEnabled(true); // Req 395
          }
          if ((l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Right))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMPLETE_COMBINATION_PARA_RIGHT))->setEnabled(true); // Req 395
          }
          if ((l_curparkSpace[0][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Left))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMPLETE_COMBINATION_CROSS_LEFT))->setEnabled(true); // Req 395
          }
          if ((l_curparkSpace[1][0].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED) && (l_curParkOutDirection == cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Right))
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMPLETE_COMBINATION_CROSS_RIGHT))->setEnabled(true); // Req 395
          }
        }
        else
        {
          // do nothing
        }

        break;
      }
      default:
      {
        break;
      }
      }
      break;
    }
    case  cc::target::common::EPARKStatusR2L::PARK_AssistStandby:
    {
      switch (l_curParkngTypeSeld) // PRQA S 3139  #code looks fine
      {
      case cc::target::common::EParkngTypeSeld::PARKING_NONE:
      {// do nothing
        break;
      }
      case cc::target::common::EParkngTypeSeld::PARKING_IN:
      {
        switch (l_curparkDriverInd) // PRQA S 3139  #code looks fine
        {
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot:
        {
          m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
          break;
        }
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:
        {
          m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
          break;
        }
        default:
        {
          break;
        }
        }
        break;
      }
      case cc::target::common::EParkngTypeSeld::PARKING_OUT:
      {
        switch (l_curparkDriverInd)
        {
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor: //Req_403
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror: //Req_497
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk: //Req_386
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood: //Req_400
        {
          break;
        }
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt: //Req_401
        case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM: //Req_463
        {
          switch (l_curParkOutDirection)
          {
            case cc::target::common::EPARKSideR2L::PARKSIDE_None:
            {
              break;
            }
            case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Left:
            {
              m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_LEFT))->setEnabled(true);
              break;
            }
            case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Right:
            {
              m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_RIGHT))->setEnabled(true);
              break;
            }
            case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Left:
            {
              m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_CROSS_LEFT))->setEnabled(true);
              break;
            }
            case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Right:
            {
              m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_CROSS_RIGHT))->setEnabled(true);
              break;
            }
            default:
            {
              break;
            }

          }
          break;
        }
        default:
        {
          break;
        }
        }
        // Req_405
        if (l_curAPADriverReq_Search == cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear)
        {
          switch (l_curParkOutDirection)
          {
          case cc::target::common::EPARKSideR2L::PARKSIDE_None:
          {
            break;
          }
          case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Left:
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_LEFT))->setEnabled(true);
            break;
          }
          case cc::target::common::EPARKSideR2L::PARKSIDE_Parallel_Right:
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_PARA_RIGHT))->setEnabled(true);
            break;
          }
          case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Left:
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_CROSS_LEFT))->setEnabled(true);
            break;
          }
          case cc::target::common::EPARKSideR2L::PARKSIDE_Cross_Right:
          {
            m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_OUT_COMBINATION_CROSS_RIGHT))->setEnabled(true);
            break;
          }
          default:
          {
            break;
          }

          }
        }
        break;
      }
      default:
      {
        break;
      }
      }
      break;
    }
    default:
    {
      break;
    }
    }
  }
  //this->DeliverButtonDispSts(f_framework);    // PRQA S 3804
}


void ParkingSlotManager::clearIcon()
{
  m_settingParkSlot.setAllEnabled(false);
}

//!
//! @brief Construct a new HoriParkingSlot:: HoriParkingSlot object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
HoriParkingSlot::HoriParkingSlot(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_HoriManager{}
{
  pc::util::coding::CodingManager* const l_codingManager = pc::util::coding::getCodingManager();
  cc::assets::uielements::UISettings* const l_uiSettings = dynamic_cast<cc::assets::uielements::UISettings*>(l_codingManager->getItem("UIElements")); // PRQA S 3077  // PRQA S 3400

  l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
  l_uiSettings->dirty();
  m_HoriManager.init(this);

  // l_uiSettings->updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT);
  // l_uiSettings->dirty();
  // m_VertManager.init(this);

  m_pAutoPicCenter                 = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallParkAutoPic.m_iconCenter                ));    // PRQA S 3066
  m_pParkOutAutoPicCenter          = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingParkOutSmallAuto.m_iconCenter                ));    // PRQA S 3066
  m_pSmallSlotAutoPicCenter        = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallSlotAutoPic.m_iconCenter                ));    // PRQA S 3066
  m_pSelectedSlotParaRightCenter   = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter       ));    // PRQA S 3066
  m_pAutoPicPara                   = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallAutoPicPara.m_iconCenter                ));    // PRQA S 3066
  m_pAutoPicVertRearInLeft         = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallAutoPicVertRearInLeft.m_iconCenter      ));    // PRQA S 3066
  m_pAutoPicVertRearInRight        = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallAutoPicVertRearInRight.m_iconCenter     ));    // PRQA S 3066
  m_pAutoPicVertFrontInLeft        = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallAutoPicVertFrontInLeft.m_iconCenter     ));    // PRQA S 3066
  m_pAutoPicVertFrontInRight       = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingSmallAutoPicVertFrontInRight.m_iconCenter    ));    // PRQA S 3066
  m_pSelectedSlotParaLeft          = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotParaLeft.m_iconCenter        ));    // PRQA S 3066
  m_pSelectedSlotParaRight         = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotParaRight.m_iconCenter       ));    // PRQA S 3066
  m_pSelectedSlotVertLeftRearIn    = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn.m_iconCenter  ));    // PRQA S 3066
  m_pSelectedSlotVertLeftFrontIn   = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn.m_iconCenter ));    // PRQA S 3066
  m_pSelectedSlotVertRightRearIn   = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn.m_iconCenter ));    // PRQA S 3066
  m_pSelectedSlotVertRightFrontIn  = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn.m_iconCenter));    // PRQA S 3066

  m_pParaSlotSize                  = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingParallelSlot.m_iconSize));    // PRQA S 3066
  m_pVertSlotSize                  = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingVerticalSlot.m_iconSize));    // PRQA S 3066
  m_pDiagSlotSize                  = const_cast<osg::Vec2f*>(&(g_uiSettings->m_settingDiagonalSlot.m_iconSize));    // PRQA S 3066

  m_pParaSlotPoseX                 = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingParaSlotPoseX         ));    // PRQA S 3066
  m_pVertSlotPoseX                 = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingVertSlotPoseX         ));    // PRQA S 3066
  m_pCalculateLeftSlotPoseX        = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingCalculateLeftSlotPoseX));    // PRQA S 3066
  m_pSlotPositionY                 = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingSlotPositionY         ));    // PRQA S 3066
  m_pDistanceBetweenPP             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenPP     ));    // PRQA S 3066
  m_pDistanceBetweenPC             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenPC     ));    // PRQA S 3066
  m_pDistanceBetweenCC             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenCC     ));    // PRQA S 3066
  m_pDistanceBetweenDD             = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingDistanceBetweenDD     ));    // PRQA S 3066
  m_pMaxHeight                     = const_cast<vfc::uint32_t*>(&(g_uiSettings->m_parkingMaxHeight             ));    // PRQA S 3066

  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


HoriParkingSlot::~HoriParkingSlot() = default;


void HoriParkingSlot::traverse(osg::NodeVisitor& f_nv)    // PRQA S 6043
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // ! update theme
    pc::util::coding::CodingManager* const l_codingManager = pc::util::coding::getCodingManager();
//    cc::assets::uielements::UISettings* const l_uiSettings = dynamic_cast<cc::assets::uielements::UISettings*>(l_codingManager->getItem("UIElements")); // PRQA S 3077  // PRQA S 3803  // PRQA S 3400
    if(m_customFramework->m_SVSRotateStatusDaddy_Receiver.hasData())
    {
      const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
      const cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data);    // PRQA S 3013

      if (l_curThemeType != s_theme)
      {
        XLOG_INFO(g_AppContext, "[THEME] UIElements start theme update");

        if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
        {
          // m_VertManager.clearIcon();
        }
        else if (l_curThemeType == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
        {
          m_HoriManager.clearIcon();
        }
        else
        {
          // m_VertManager.clearIcon();
        }

        s_theme = l_curThemeType;
        // pf code. #code looks fine
        XLOG_INFO(g_AppContext, "[THEME] UIElements finish theme update");
      }
      else
      {
        // Do nothing
      }
    }

    if (s_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT)
    {
      // m_VertManager.update(this, m_customFramework);
      // *m_pAutoPicCenter                = transferToBottomLeftVert(g_uiSettings->m_settingSmallParkAutoPic_Vert.m_iconCenter);
      // *m_pParkOutAutoPicCenter         = transferToBottomLeftVert(g_uiSettings->m_settingParkOutSmallAuto_Vert.m_iconCenter);
      // *m_pSmallSlotAutoPicCenter       = transferToBottomLeftVert(g_uiSettings->m_settingSmallSlotAutoPic_Vert.m_iconCenter);
      // *m_pSelectedSlotParaRightCenter  = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotParaRight_Vert.m_iconCenter);
      // *m_pAutoPicPara                  = transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicPara_Vert.m_iconCenter);
      // *m_pAutoPicVertRearInLeft        = transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertRearInLeft_Vert.m_iconCenter);
      // *m_pAutoPicVertRearInRight       = transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertRearInRight_Vert.m_iconCenter);
      // *m_pAutoPicVertFrontInLeft       = transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertFrontInLeft_Vert.m_iconCenter);
      // *m_pAutoPicVertFrontInRight      = transferToBottomLeftVert(g_uiSettings->m_settingSmallAutoPicVertFrontInRight_Vert.m_iconCenter);
      // *m_pSelectedSlotParaLeft         = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotParaLeft_Vert.m_iconCenter);
      // *m_pSelectedSlotParaRight        = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotParaRight_Vert.m_iconCenter);
      // *m_pSelectedSlotVertLeftRearIn   = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn_Vert.m_iconCenter);
      // *m_pSelectedSlotVertLeftFrontIn  = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn_Vert.m_iconCenter);
      // *m_pSelectedSlotVertRightRearIn  = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn_Vert.m_iconCenter);
      // *m_pSelectedSlotVertRightFrontIn = transferToBottomLeftVert(g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn_Vert.m_iconCenter);

      // *m_pParaSlotSize = getSlotImageSizeVert(g_uiSettings->m_texturePathHoriUnselectedSlotParaRight);
      // *m_pVertSlotSize = getSlotImageSizeVert(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight);
      // *m_pDiagSlotSize = getSlotImageSizeVert(g_uiSettings->m_texturePathHoriUnselectedSlotDiagRight);

      // *m_pParaSlotPoseX          = 237;
      // *m_pVertSlotPoseX          = 254;
      // *m_pCalculateLeftSlotPoseX = static_cast<vfc::uint32_t>(cc::core::g_views->m_vertMainViewport.m_size.y());
      // *m_pSlotPositionY          = 214;
      // *m_pDistanceBetweenPP      = 118;
      // *m_pDistanceBetweenPC      = 93;
      // *m_pDistanceBetweenCC      = 63;
      // *m_pDistanceBetweenDD      = 73;
      // *m_pMaxHeight              = 455;
    }
    else
    {
      m_HoriManager.update(this, m_customFramework);
      *m_pAutoPicCenter                = transferToBottomLeftHori(g_uiSettings->m_settingSmallParkAutoPic_Hori.m_iconCenter);
      *m_pParkOutAutoPicCenter         = transferToBottomLeftHori(g_uiSettings->m_settingParkOutSmallAuto_Hori.m_iconCenter);
      *m_pSmallSlotAutoPicCenter       = transferToBottomLeftHori(g_uiSettings->m_settingSmallSlotAutoPic_Hori.m_iconCenter);
      *m_pSelectedSlotParaRightCenter  = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotParaRight_Hori.m_iconCenter);
      *m_pAutoPicPara                  = transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicPara_Hori.m_iconCenter);
      *m_pAutoPicVertRearInLeft        = transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertRearInLeft_Hori.m_iconCenter);
      *m_pAutoPicVertRearInRight       = transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertRearInRight_Hori.m_iconCenter);
      *m_pAutoPicVertFrontInLeft       = transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertFrontInLeft_Hori.m_iconCenter);
      *m_pAutoPicVertFrontInRight      = transferToBottomLeftHori(g_uiSettings->m_settingSmallAutoPicVertFrontInRight_Hori.m_iconCenter);
      *m_pSelectedSlotParaLeft         = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotParaLeft_Hori.m_iconCenter);
      *m_pSelectedSlotParaRight        = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotParaRight_Hori.m_iconCenter);
      *m_pSelectedSlotVertLeftRearIn   = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertLeftRearIn_Hori.m_iconCenter);
      *m_pSelectedSlotVertLeftFrontIn  = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertLeftFrontIn_Hori.m_iconCenter);
      *m_pSelectedSlotVertRightRearIn  = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertRightRearIn_Hori.m_iconCenter);
      *m_pSelectedSlotVertRightFrontIn = transferToBottomLeftHori(g_uiSettings->m_settingHoriSelectedSlotVertRightFrontIn_Hori.m_iconCenter);

      *m_pParaSlotSize = getImageSizeHori(g_uiSettings->m_texturePathHoriUnselectedSlotParaRight);
      *m_pVertSlotSize = getImageSizeHori(g_uiSettings->m_texturePathHoriUnselectedSlotVertRight);
      *m_pDiagSlotSize = getImageSizeHori(g_uiSettings->m_texturePathHoriUnselectedSlotDiagRight);

      *m_pParaSlotPoseX = 64;
      *m_pVertSlotPoseX = 91;
      *m_pCalculateLeftSlotPoseX = static_cast<vfc::uint32_t>(cc::core::g_views->m_planViewport.m_size.x());  //the width of ui view
      *m_pSlotPositionY          = 253;                                           // position of the first slot icon in y direction
      *m_pDistanceBetweenPP      = 146;                                           // distance in y direction between slot icons
      *m_pDistanceBetweenPC      = 115;
      *m_pDistanceBetweenCC      = 84;
      *m_pDistanceBetweenDD      = 95;
      *m_pMaxHeight              = 580;
    }
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}

vfc::uint8_t ParkingSlotManager::getSlotType( cc::target::common::EFAPAParkSlotType f_generalType, vfc::float32_t f_angle)
{
  vfc::uint8_t l_realSlotType = 255u;
  if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL)
  {
    l_realSlotType = static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL);
    return l_realSlotType;
  }
  else if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL)
  {
    l_realSlotType = static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL);
    return l_realSlotType;
  }
  else if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL)
  {
    l_realSlotType = static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL);        // diagonal slot
    return l_realSlotType;
  }
  else if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_CROSS &&
  isGreater(f_angle, g_managerSettings->m_parkingDiagSlotAngleLowerLimit) &&
  isLess(f_angle, g_managerSettings->m_parkingDiagSlotAngleUpperLimit))
  {
    l_realSlotType = static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL);
    return l_realSlotType;
  }
  else if (f_generalType == cc::target::common::EFAPAParkSlotType::APASLOT_CROSS )
  {
    l_realSlotType = static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS);
    return l_realSlotType;
  }
  else
  {
    //Do nothing
  }
  return l_realSlotType;
}

void ParkingSlotManager::setSlot(vfc::uint16_t f_slot, std::string f_textureSlot,const osg::Vec2f& f_size,osg::Vec2f f_position,
                                        cc::target::common::EFAPAParkSlotType f_slotType, cc::daddy::ParkUISpotData_t& f_rParkSpotUIDataContainer)
{
  m_settingParkSlot.getIcon(f_slot)->setImage(f_textureSlot);
  m_settingParkSlot.getIcon(f_slot)->setSize(f_size, pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_slot)->setPosition(transferToBottomLeft(f_position),pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_slot)->setEnabled(true);
  // set slot selecting press center & response area
  if (s_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
  {
    f_position.x() = ( f_position.x() * 582.0f / 432.0f ) + 228.0f;
    f_position.y() = f_position.y() * 972.0f / 720.0f;
  }
  else
  {
    // For Slots display in searching the height has same direction with y in HMI
    f_position.x() = f_position.x() * 1.5f;                // f_position.x() = f_position.x() * 1080 / 720;
    f_position.y() = ( f_position.y() * 1.5f ) + 696.0f;       // f_position.y() = ( f_position.y() * 852 / 568 ) + 696;
  }
  osg::Vec2f l_comResponseArea;
  if (f_slotType == cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL)
  {
    l_comResponseArea = g_uiSettings->m_settingPARKParaSlotValid.m_responseArea;
  }
  else if (f_slotType == cc::target::common::EFAPAParkSlotType::APASLOT_CROSS)
  {
    l_comResponseArea = g_uiSettings->m_settingPARKVertSlotValid.m_responseArea;
  }
  else if (f_slotType == cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL)
  {
    l_comResponseArea = g_uiSettings->m_settingPARKDiagSlotValid.m_responseArea;
  }
  else
  {
    //Do nothing
  }

  switch (f_slot) // PRQA S 3139  #code looks fine
  {
  case 0u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_responseArea = l_comResponseArea;
    break;
  }
  case 1u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_responseArea = l_comResponseArea;
    break;
  }
  case 2u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_responseArea = l_comResponseArea;
    break;
  }
  case 3u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_responseArea = l_comResponseArea;
    break;
  }
  case 4u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_responseArea = l_comResponseArea;
    break;
  }
  case 5u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_responseArea = l_comResponseArea;
    break;
  }
  case 6u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_responseArea = l_comResponseArea;
    break;
  }
  case 7u:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_iconCenter = f_position;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_responseArea = l_comResponseArea;
    break;
  }
  default:
  {
    break;
  }
  }
}

void ParkingSlotManager::displaySelectedSlot(vfc::uint8_t f_vehicle, vfc::uint8_t f_guideline, vfc::uint8_t f_slot, const osg::Vec2f& f_position)
{
  m_settingParkSlot.getIcon(f_vehicle)->setPosition(f_position,pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_vehicle)->setEnabled(true);
  m_settingParkSlot.getIcon(f_slot)->setEnabled(true);
  m_settingParkSlot.getIcon(f_guideline)->setEnabled(true);
}

void ParkingSlotManager::displaySelectedSlot(vfc::uint8_t f_vehicle,  vfc::uint8_t f_slot, const osg::Vec2f& f_position)
{
  m_settingParkSlot.getIcon(f_vehicle)->setPosition(f_position,pc::assets::Icon::UnitType::Pixel);
  m_settingParkSlot.getIcon(f_vehicle)->setEnabled(true);
  m_settingParkSlot.getIcon(f_slot)->setEnabled(true);
}

void ParkingSlotManager::manageDisplayLogicInGuidance(SelectedSlot f_selectedParkSpace, cc::target::common::rbp_Type_ParkManeuverType_en /*f_curparkPSDirection*/)    // PRQA S 6043
{
  const vfc::uint8_t l_curSlotType = getSlotType(f_selectedParkSpace.m_selectedSlotType, f_selectedParkSpace.m_slotAngle);
  if (static_cast<vfc::uint8_t>(ParkSlotSide::LEFTPARKSLOTS) == f_selectedParkSpace.m_selectedSlotSide ) //Left side
  {
    if (static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL) == l_curSlotType)         // parallel slot
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::GUIDELINE_PARASLOT_LEFT))->setEnabled(true);
    }
    else if (static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS) == l_curSlotType)
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::GUIDELINE_CROSSSLOT_LEFT))->setEnabled(true);
    }
    else if (static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL) == l_curSlotType)          // diagonal slot
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::GUIDELINE_DIAGSLOT_LEFT))->setEnabled(true);
    }
    else
    {
      //do nothing
    }
  }
  else if (static_cast<vfc::uint8_t>(ParkSlotSide::RIGHTPARKSLOTS) == f_selectedParkSpace.m_selectedSlotSide ) //Right side
  {
    if ( static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL) == l_curSlotType)
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::GUIDELINE_PARASLOT_RIGHT))->setEnabled(true);
    }
    else if ( static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS) == l_curSlotType)
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::GUIDELINE_CROSSSLOT_RIGHT))->setEnabled(true);
    }
    else if (static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL) == l_curSlotType)
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::GUIDELINE_DIAGSLOT_RIGHT))->setEnabled(true);
    }
    else
    {
      //Do nothing
    }
  }
  else
  {
    //do nothing
  }

}

void ParkingSlotManager::manageDisplayLogicInComplete(SelectedSlot f_selectedParkSpace)    // PRQA S 6043
{
#if PARKINGSLOT_DELAY
  if (g_ConversionTicks<= g_managerSettings->m_parkingCrossSlotConversionCycles)
  {
    g_ConversionTicks += 1u;
#endif
    const vfc::uint8_t l_curSlotType = getSlotType(f_selectedParkSpace.m_selectedSlotType, f_selectedParkSpace.m_slotAngle);
    if ( static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL) == l_curSlotType)
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_VEHICLE_PARALLEL))->setEnabled(true);
    }
    else if ( static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS) == l_curSlotType)
    {
        m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_VEHICLE_CROSS))->setEnabled(true);
    }
    else if ( static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL) == l_curSlotType)
    {
      m_settingParkSlot.getIcon(static_cast<unsigned>(ParkingSlotType::PARKING_VEHICLE_DIAGONAL))->setEnabled(true);
    }
    else
    {
      //do nothing
    }
#if PARKINGSLOT_DELAY
  }
  else
  {
    m_settingParkSlot.getIcon(ParkingSlotType::PARKING_VEHICLE_CROSS_NO_SLOT)->setEnabled(true);
  }
#endif
}

SelectedSlot ParkingSlotManager::getSelectedSlot(std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace)
{
  SelectedSlot l_selectedSlot;
  l_selectedSlot.m_selectedSlotSide = 255u;
  l_selectedSlot.m_selectedSlotId   = 255u;
  l_selectedSlot.m_selectedSlotType = cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT;
  l_selectedSlot.m_slotAngle        = 999.f;
  for(vfc::uint8_t l_side = 0; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
        for(vfc::uint8_t l_numberPerside = 0; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
        {
          if (f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
          {
            l_selectedSlot.m_selectedSlotSide = l_side;
            l_selectedSlot.m_selectedSlotId   = l_numberPerside;
            l_selectedSlot.m_selectedSlotType = f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PSType;
            l_selectedSlot.m_slotAngle        = f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlotSta_f32;
            return l_selectedSlot;
          }
          else
          {
            //do nothing
          }
        }
    }
  return l_selectedSlot;
}

bool ParkingSlotManager::isASlotSelected(std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_curSelectedParkSpace)
{
  for(vfc::uint8_t l_side = 0; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
  {
    for(vfc::uint8_t l_numberPerside = 0; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
    {
      if (f_curSelectedParkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
      {
        return true;
      }
      else
      {
        //do nothing
      }
    }
  }
  return false;
}

void ParkingSlotManager::CleanButtonDispSts()
{
  m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
  m_ViewButtonParkingInTypeDispSts        = cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
}

bool ParkingSlotManager::DeliverButtonDispSts(const core::CustomFramework* /*f_framework*/)
{
  cc::daddy::ParkDisp2TouchStsDaddy_t& l_ParkDisp2TouchSts =
              cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.reserve() ;

  l_ParkDisp2TouchSts.m_Data.m_ButtonParkSlotsSelectingDispSts = m_ViewButtonParkInSlotsSelectingDispSts;
  l_ParkDisp2TouchSts.m_Data.m_ButtonParkInTypeDispSts         = m_ViewButtonParkingInTypeDispSts;

  cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.deliver() ;

  return false;
}

} // namespace uielements
} // namespace assets
} // namespace cc

