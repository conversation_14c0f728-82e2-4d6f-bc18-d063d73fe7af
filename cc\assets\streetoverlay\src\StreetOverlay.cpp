//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC GAC
/// @file  StreetOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/streetoverlay/inc/StreetOverlay.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"
#include "vfc/core/vfc_types.hpp"
using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace streetoverlay
{

pc::util::coding::Item<StreetOverlayCodingParams> g_settings("StreetOverlay");


osg::Texture2D* loadTexture(const std::string& f_filename, vfc::uint32_t f_minFilterMode)
{
  osg::Image* const l_image = osgDB::readImageFile(f_filename);
  if (l_image == nullptr)
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "StreetOverlay::loadTexture(): Could not load " << f_filename);
  }
  osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
  l_texture->setDataVariance(osg::Object::STATIC);
  l_texture->setUnRefImageDataAfterApply(true);
  l_texture->setFilter(osg::Texture::MIN_FILTER, StreetOverlayCodingParams::getFilterMode(f_minFilterMode));
  l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
  l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::REPEAT);
  return l_texture;
}


//!
//! ColorOperator
//!
struct ColorOperator
{
  ColorOperator(vfc::float32_t f_begin, vfc::float32_t f_end)
    : m_begin{f_begin}
    , m_end{f_end}
  {
  }

  vfc::float32_t m_begin;
  vfc::float32_t m_end;
};


//!
//! RoadColorFrontOperator
//!
struct RoadColorFrontOperator : public ColorOperator
{
  using ColorOperator::ColorOperator;  // Inherit from the base class constructor




  void compute(const osg::Vec3f& f_vertex, osg::Vec4ub& f_color) const // PRQA S 4287
  {
    const unsigned char l_intensity = pc::util::osgx::toUByte(pc::util::smoothstep(m_begin, m_end, f_vertex.x()));
    f_color = osg::Vec4ub(l_intensity, l_intensity, l_intensity, l_intensity);
  }
};


//!
//! RoadColorFrontOperator
//!
struct RoadColorRearOperator : public ColorOperator
{
  using ColorOperator::ColorOperator;  // Inherit from the base class constructor




  void compute(const osg::Vec3f& f_vertex, osg::Vec4ub& f_color) const // PRQA S 4287
  {
    const unsigned char l_intensity = pc::util::osgx::toUByte(pc::util::smoothstep(m_begin, m_end, f_vertex.x()));
    f_color = osg::Vec4ub(255u, 255u, 255u, l_intensity);
  }
};


//!
//! GridColorOperator
//!
struct GridColorOperator : public ColorOperator
{
  using ColorOperator::ColorOperator;  // Inherit from the base class constructor




  void compute(const osg::Vec3f& f_vertex, osg::Vec4ub& f_color) const // PRQA S 4287
  {
    const vfc::float32_t l_distance = osg::Vec2f(f_vertex.x() - g_settings->m_gridXOffset, f_vertex.y()).length();
    const unsigned char l_intensity = pc::util::osgx::toUByte(pc::util::smoothstep(m_end, m_begin, l_distance));
    f_color = osg::Vec4ub(255u, 255u, 255u, l_intensity);
  }
};


//! utility function for iterating over the vertex array of a given geometry and generate color values depending on the vertex position
template <class T>
void generateColors(osg::Geometry* f_geometry, const T& f_operator)
{
  const osg::Vec3Array* l_vertices = dynamic_cast<const osg::Vec3Array*> (f_geometry->getVertexArray());
  if (l_vertices)
  {
    const unsigned int l_numVertices = l_vertices->size();
    osg::Vec4ubArray* l_colors = new osg::Vec4ubArray(l_numVertices);
    l_colors->setNormalize(true);
    for (unsigned int i = 0; i < l_numVertices; ++i)
    {
      f_operator.compute((*l_vertices)[i], (*l_colors)[i]);
    }
    l_colors->dirty();
    f_geometry->setColorArray(l_colors, osg::Array::BIND_PER_VERTEX);
  }
}


//!
//! StreetOverlay
//!
StreetOverlay::StreetOverlay(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_lastUpdateTime{0.0f}
  , m_drivenDistance{0.0f}
  , m_animXDirection{false}
{
  setName("StreetOverlay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


StreetOverlay::~StreetOverlay() = default;


void StreetOverlay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (g_settings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      m_settingsModifiedCount = g_settings->getModifiedCount();
    }
    update();

    const vfc::float32_t l_currentTime = static_cast<vfc::float32_t> (f_nv.getFrameStamp()->getReferenceTime());
    const vfc::float32_t l_diffTime = l_currentTime - m_lastUpdateTime;
    m_lastUpdateTime = l_currentTime;

    vfc::float32_t l_diffDistance = 0.0f;
    const pc::daddy::SpeedDaddy* const l_sd = m_framework->m_speedReceiver.getData();
    if (l_sd != nullptr)
    {
      const vfc::float32_t l_metersPerSecond = l_sd->m_Data / 3.6f;
      l_diffDistance = l_diffTime * l_metersPerSecond;
    }
    //! check driving direction
    bool l_drivingForward = true;
    const pc::daddy::DrivingDirDaddy* const l_dd = m_framework->m_drivingDirReceiver.getData();
    if (l_dd != nullptr)
    {
      if (pc::daddy::DRVDIR_BACKWARD == l_dd->m_Data)
      {
        l_drivingForward = false;
      }
    }
    if (!l_drivingForward)
    {
      l_diffDistance = -l_diffDistance;
    }

    m_drivenDistance += l_diffDistance;

    //! Update background geode uniform
    {
      // osg::StateSet* l_stateSet = m_backgroundGeode->getOrCreateStateSet();
      // osg::StateSet* l_stateSetMirrored = m_backgroundGeodeMirrored->getOrCreateStateSet();
      // osg::Vec2f l_uvScale = osg::componentDivide(osg::Vec2f(1.0f, 1.0f), g_settings->m_gridTileSize);
      // osg::Vec2f l_uvOffset = osg::componentMultiply(g_settings->m_gridTileOffset, l_uvScale);

      // if (false == m_animXDirection)
      // {
      //   l_uvOffset.y() += std::fmod(m_drivenDistance * l_uvScale.y(), 1.0f);
      // }
      // else
      // {
      //   l_uvOffset.x() -= std::fmod(m_drivenDistance * l_uvScale.x(), 1.0f);
      // }
      // osg::Uniform* l_uvOffsetUniform = l_stateSet->getOrCreateUniform("u_uvOffset", osg::Uniform::FLOAT_VEC2);
      // l_uvOffsetUniform->set(l_uvOffset);
      // osg::Uniform* l_uvOffsetUniformMirrored = l_stateSetMirrored->getOrCreateUniform("u_uvOffset", osg::Uniform::FLOAT_VEC2);
      // l_uvOffsetUniformMirrored->set(l_uvOffset);
    }
  }
  osg::Group::traverse(f_nv);
}


void StreetOverlay::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803
  // const osg::Vec2f l_vehicleCenter = pc::vehicle::g_mechanicalData->getCenter();

  // const float l_gridOffsetRight = 0.2f;
  // const float l_gridFoundOffset = 11.f;
  // //! grid slot not found right side************************************************************************************************************
  // m_gridNotFoundGeodeRight = new osg::Geode;
  // addChild(m_gridNotFoundGeodeRight);    // PRQA S 3803
  // {
  //   osg::Vec3f l_startPoint = osg::Vec3f(g_settings->m_roadBegin, -g_settings->m_gridYOffset - g_settings->m_gridWidth - l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, -g_settings->m_gridYOffset- g_settings->m_gridWidth- l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_width = osg::Vec3f(0.0f, g_settings->m_gridWidth, 0.0f);
  //   osg::Vec3f l_height = l_endPoint - l_startPoint;

  //   osg::Geometry* l_gridNotFound = pc::util::osgx::createTexturePlane(l_startPoint, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
  //   // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
  //   osg::StateSet* l_gridNotFoundStateSet = l_gridNotFound->getOrCreateStateSet();
  //   osg::Texture2D* l_gridNotFoundTexture = loadTexture(g_settings->m_gridNotFoundTexture, g_settings->m_gridTexMinFilterMode);
  //   l_gridNotFoundStateSet->setTextureAttribute(0u, l_gridNotFoundTexture);
  //   m_gridNotFoundGeodeRight->addDrawable(l_gridNotFound);    // PRQA S 3803
  // }

  // //! grid slot found right side************************************************************************************************************
  // m_gridFoundGeodeRight = new osg::Geode;
  // addChild(m_gridFoundGeodeRight);    // PRQA S 3803
  // {
  //   osg::Vec3f l_startPoint = osg::Vec3f(g_settings->m_roadBegin + l_gridFoundOffset, -g_settings->m_gridYOffset - g_settings->m_gridWidth - l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, -g_settings->m_gridYOffset - g_settings->m_gridWidth- l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_width = osg::Vec3f(0.0f, g_settings->m_gridWidth, 0.0f);
  //   osg::Vec3f l_height = l_endPoint - l_startPoint;

  //   osg::Geometry* l_gridFound = pc::util::osgx::createTexturePlane(l_startPoint, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
  //   // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
  //   osg::StateSet* l_gridFoundStateSet = l_gridFound->getOrCreateStateSet();
  //   osg::Texture2D* l_gridFoundTexture = loadTexture(g_settings->m_gridFoundTexture, g_settings->m_gridTexMinFilterMode);
  //   l_gridFoundStateSet->setTextureAttribute(0u, l_gridFoundTexture);
  //   m_gridFoundGeodeRight->addDrawable(l_gridFound);    // PRQA S 3803
  // }
  // m_gridNotFoundGeodeRight->setNodeMask(~0u);
  // m_gridFoundGeodeRight->setNodeMask(0u);
  // addUpdateCallback(new StreetOverlayUpdateCallback(m_gridNotFoundGeodeRight, m_gridFoundGeodeRight, m_framework, false));


  // //! grid slot not found left side************************************************************************************************************
  // m_gridNotFoundGeodeLeft = new osg::Geode;
  // addChild(m_gridNotFoundGeodeLeft);    // PRQA S 3803
  // {
  //   osg::Vec3f l_startPoint = osg::Vec3f(g_settings->m_roadBegin, g_settings->m_gridYOffset + g_settings->m_gridWidth + l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, g_settings->m_gridYOffset + g_settings->m_gridWidth + l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_width = osg::Vec3f(0.0f, -g_settings->m_gridWidth, 0.0f);
  //   osg::Vec3f l_height = l_endPoint - l_startPoint;

  //   osg::Geometry* l_gridNotFound = pc::util::osgx::createTexturePlane(l_startPoint, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
  //   // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
  //   osg::StateSet* l_gridNotFoundStateSet = l_gridNotFound->getOrCreateStateSet();
  //   osg::Texture2D* l_gridNotFoundTexture = loadTexture(g_settings->m_gridNotFoundTexture, g_settings->m_gridTexMinFilterMode);
  //   l_gridNotFoundStateSet->setTextureAttribute(0u, l_gridNotFoundTexture);
  //   m_gridNotFoundGeodeLeft->addDrawable(l_gridNotFound);    // PRQA S 3803
  // }

  // //! grid slot found left side************************************************************************************************************
  // m_gridFoundGeodeLeft = new osg::Geode;
  // addChild(m_gridFoundGeodeLeft);    // PRQA S 3803
  // {
  //   osg::Vec3f l_startPoint = osg::Vec3f(g_settings->m_roadBegin + l_gridFoundOffset, g_settings->m_gridYOffset + g_settings->m_gridWidth + l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, g_settings->m_gridYOffset + g_settings->m_gridWidth + l_gridOffsetRight, g_settings->m_gridHeight);
  //   osg::Vec3f l_width = osg::Vec3f(0.0f, -g_settings->m_gridWidth, 0.0f);
  //   osg::Vec3f l_height = l_endPoint - l_startPoint;

  //   osg::Geometry* l_gridFound = pc::util::osgx::createTexturePlane(l_startPoint, l_width, l_height, 1u, 1u, 0.0f, 1.0f, 0.0f, 1.0f);
  //   // generateColors(l_gridNotFound, GridColorOperator(g_settings->m_gridFadeOutBegin, g_settings->m_gridFadeOutEnd));
  //   osg::StateSet* l_gridFoundStateSet = l_gridFound->getOrCreateStateSet();
  //   osg::Texture2D* l_gridFoundTexture = loadTexture(g_settings->m_gridFoundTexture, g_settings->m_gridTexMinFilterMode);
  //   l_gridFoundStateSet->setTextureAttribute(0u, l_gridFoundTexture);
  //   m_gridFoundGeodeLeft->addDrawable(l_gridFound);    // PRQA S 3803
  // }
  // m_gridNotFoundGeodeLeft->setNodeMask(~0u);
  // m_gridFoundGeodeLeft->setNodeMask(0u);
  // addUpdateCallback(new StreetOverlayUpdateCallback(m_gridNotFoundGeodeLeft, m_gridFoundGeodeLeft, m_framework, true));

  //! road common *****************************************************************************************************
  osg::Texture2D* const l_streetTexture = loadTexture(g_settings->m_roadTexture, g_settings->m_roadTexMinFilterMode);
  l_streetTexture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_streetTexture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  osg::StateSet* const l_roadStateSet = new osg::StateSet;
  l_roadStateSet->setTextureAttribute(0u, l_streetTexture);

  const osg::ref_ptr<osg::Geode> l_roadRearGeode = new osg::Geode;
  m_streetOverlaySwitch = new osg::Switch();
  m_streetOverlaySwitch->addChild(l_roadRearGeode);    // PRQA S 3803
  {
    const osg::Vec3f l_startPoint = osg::Vec3f(g_settings->m_roadBegin, -g_settings->m_roadWidth, g_settings->m_roadHeight);
    const osg::Vec3f l_endPoint = osg::Vec3f(g_settings->m_roadEnd, -g_settings->m_roadWidth, g_settings->m_roadHeight);
    const osg::Vec3f l_width = osg::Vec3f(0.0f, 2.0f * g_settings->m_roadWidth, 0.0f);
    const osg::Vec3f l_height = l_endPoint - l_startPoint;

    // float l_vIterations = l_height.length() / g_settings->m_roadWidth;
    const osg::ref_ptr<osg::Geometry> l_surface = pc::util::osgx::createTexturePlane(l_startPoint, l_width, l_height,
      1u, 1u, //! u, v segments
      0.0f, 1.0f, // left, right
      1.0f, 0.0f); //! bottom, top

    // generateColors(l_surface, RoadColorRearOperator(g_settings->m_roadBegin, g_settings->m_roadBegin + g_settings->m_roadFadeOutRear));
    l_surface->setStateSet(l_roadStateSet);
    l_roadRearGeode->addDrawable(l_surface);    // PRQA S 3803
  }

  const osg::ref_ptr <osg::StateSet> l_groupStateSet = getOrCreateStateSet();
  const osg::ref_ptr <osg::Depth> l_depth = new osg::Depth;
  l_depth->setWriteMask(false);
  l_groupStateSet->setAttribute(l_depth);
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  l_basicTexShader.apply(l_groupStateSet);    // PRQA S 3803
  l_groupStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
  l_groupStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_baseplate + 1, "RenderBin");
  //l_groupStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);

  addChild(m_streetOverlaySwitch); // PRQA S 3803
}

void StreetOverlay::update()
{
  if(isViewIncludeStreetOverlay())
  {
    m_streetOverlaySwitch->setValue(0u,true);
  }
  else
  {
    m_streetOverlaySwitch->setValue(0u,false);
  }

}

bool StreetOverlay::isViewIncludeStreetOverlay() // PRQA S 6041
{
  bool l_ret = false;

  // ! read all the signals
  cc::target::common::EPARKStatusR2L                l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld               l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L             l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndExtR2L          l_curparkDriverIndExt     = cc::target::common::EPARKDriverIndExtR2L::PARKDRVEXT_NoRequest;
  cc::target::common::EPARKRecoverIndR2L            l_curparkSuspend          = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  cc::target::common::EPARKQuitIndR2L               l_curparkQuitInd          = cc::target::common::EPARKQuitIndR2L::PARKQUIT_None;
  cc::target::common::EPARKDriverIndSearchR2L       l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                          l_curFreeParkingActive    = false;
  cc::target::common::rbp_Type_ParkManeuverType_en  l_curparkPSDirection      = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;

  cc::target::common::EAPAPARKMODE                  l_curParkAPARPAParkMode   = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::RPAAvailable                  l_curParkRPAAvaliable     = cc::target::common::RPAAvailable::RPA_NotAvailable;

  cc::core::CustomFramework* const l_framework = m_framework->asCustomFramework();

  if(l_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = l_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (l_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = l_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (l_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = l_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (l_framework->m_parkHmiParkDriverIndExtReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndExtDaddy_t* const l_parkDriverIndExt = l_framework->m_parkHmiParkDriverIndExtReceiver.getData();
    l_curparkDriverIndExt = l_parkDriverIndExt->m_Data;
  }

  if (l_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
  {
    const cc::daddy::ParkRecoverIndDaddy_t* const l_parkSuspend = l_framework->m_parkHmiParkingRecoverIndReceiver.getData();
    l_curparkSuspend = l_parkSuspend->m_Data;
  }

  if (l_framework->m_parkPSDirectionSelectedReceiver.hasData())
  {
    const cc::daddy::ParkPSDirectionSelected_t* const l_parkPSDirection = l_framework->m_parkPSDirectionSelectedReceiver.getData();
    l_curparkPSDirection = l_parkPSDirection->m_Data;
  }

  if (l_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = l_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (l_framework->m_parkHmiParkingQuitIndReceiver.hasData())
  {
    const cc::daddy::ParkQuitIndDaddy_t* const l_parkQuitInd = l_framework->m_parkHmiParkingQuitIndReceiver.getData();
    l_curparkQuitInd = l_parkQuitInd->m_Data;
  }

  if (l_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = l_framework->m_freeparkingActiveReceiver.getData();
    l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (l_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = l_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curParkAPARPAParkMode = l_parkAPAPARKMODE->m_Data;
  }

  if (l_framework->m_parkHmiRPAAvailableReceiver.hasData())
  {
    const cc::daddy::ParkRPAAvaliableDaddy_t* const l_ParkRPAAvaliable = l_framework->m_parkHmiRPAAvailableReceiver.getData();
    l_curParkRPAAvaliable = l_ParkRPAAvaliable->m_Data;
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curParkAPARPAParkMode && cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SearchingProcess:
        {l_ret = true;
        break;}
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
        {if (false == l_curFreeParkingActive)
        {
          l_ret = true;
        }
        break;}
      default:
        {break;}
    }
    // if (cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForVehicleSlowdown == l_curAPADriverReq_Search)
    // {
    //   l_ret = true;
    // }
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curParkAPARPAParkMode && cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor: // req_493
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror: // req_497
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk: // req_386
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood: // req_387
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot: // req_371
        {break;}
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:
        {l_ret = true;
        break;}
      default:
        {break;}
    }
    if (cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear == l_curAPADriverReq_Search)
    {
        l_ret = true;
    }
  }

  return l_ret;
}

StreetOverlayUpdateCallback::StreetOverlayUpdateCallback(
                                                          osg::ref_ptr<osg::Geode> f_geodeNotFound,
                                                          osg::ref_ptr<osg::Geode> f_geodeFound,
                                                          pc::core::Framework* f_pFramework,
                                                          const bool f_isLeft
                                                          )
  : m_geodeNotFound{f_geodeNotFound}
  , m_geodeFound{f_geodeFound}
  , m_pFramework{f_pFramework}
  , m_isLeft{f_isLeft}
{

}

StreetOverlayUpdateCallback::~StreetOverlayUpdateCallback() = default;

void StreetOverlayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if (m_pFramework->asCustomFramework()->m_parkHmiParkingSpaceReceiver.hasNewData())
  {
    const cc::daddy::ParkSpaceDaddy_t*  const l_pParkSpace = m_pFramework->asCustomFramework()->m_parkHmiParkingSpaceReceiver.getData();
    // check left grid road status
    if (m_isLeft)
    {
      if ( (3u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_1L) || (4u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_1L)
        || (3u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_2L) || (4u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_2L) )
      {
        m_geodeNotFound->setNodeMask(0u);
        m_geodeFound->setNodeMask(~0u);
      }
      else
      {
        m_geodeNotFound->setNodeMask(~0u);
        m_geodeFound->setNodeMask(0u);
      }
    }
    else
    {
      if ( (3u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_1R) || (4u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_1R)
        || (3u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_2R) || (4u == l_pParkSpace->m_Data.m_APA_ParkSlotSt_2R) )
      {
        m_geodeNotFound->setNodeMask(0u);
        m_geodeFound->setNodeMask(~0u);
      }
      else
      {
        m_geodeNotFound->setNodeMask(~0u);
        m_geodeFound->setNodeMask(0u);
      }
    }

  }

  traverse(f_node, f_nv);
}



} // namespace streetoverlay
} // namespace assets
} // namespace cc
