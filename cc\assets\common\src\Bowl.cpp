//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Bowl.cpp
/// @brief 
//=============================================================================


#include "cc/assets/common/inc/Bowl.h"

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/factory/inc/SV3DCullCallback.h"
#include "pc/svs/factory/inc/SV3DUpdateCallback.h"


namespace cc
{
namespace assets
{
namespace common
{

pc::util::coding::Item<pc::worker::bowlshaping::BowlShaperData> g_bowlShaperSmall("BowlShaperSmall");
pc::util::coding::Item<pc::worker::bowlshaping::BowlShaperData> g_bowlShaperMedium("BowlShaperMedium");


pc::worker::bowlshaping::BowlWallGenerator* createBowlWallGenerator(const pc::worker::bowlshaping::BowlShaperData& f_data)
{
  return new pc::worker::bowlshaping::ParabolicSectionBowlWallGenerator(
    f_data.m_numHeightSections,
    f_data.m_bowlDefault.m_height,
    f_data.m_bowlDefault.m_depth,
    f_data.m_bowlDefault.m_slope,
    f_data.m_bowlDefault.m_densityShift,
    false);
}

Bowl::Bowl(cc::core::AssetId f_assetId, pc::core::Framework* f_pFramework, pc::worker::bowlshaping::BowlLayoutGenerator* f_pLayoutGenerator)
  : Asset{f_assetId}
  , m_asset{}
  , m_pFramework{f_pFramework}
{
  if ((f_pFramework != nullptr) && (f_pLayoutGenerator != nullptr))
  {
    pc::worker::bowlshaping::Bowl* const l_bowl = new pc::worker::bowlshaping::Bowl(1u); // PRQA S 4262 // PRQA S 4264
    l_bowl->setName("Bowl static");

    const auto l_staticBowlWallGenerator = createBowlWallGenerator(pc::worker::bowlshaping::g_bowlShaperDefault.data());
    const auto l_bowlUpdateVisitor = new pc::worker::bowlshaping::BowlUpdateVisitor(
      f_pFramework->m_cameraCalibrationReceiver, // PRQA S 0251
      f_pFramework->m_cameraMasksReceiver,
      f_pLayoutGenerator,
      l_staticBowlWallGenerator,
      true
    );

    l_bowl->accept(*l_bowlUpdateVisitor);

    l_bowl->addUpdateCallback(new pc::factory::SV3DUpdateCallback(l_bowlUpdateVisitor));
    l_bowl->addCullCallback(new pc::factory::SV3DCullCallback);

    m_asset = l_bowl;
    osg::Group::addChild(m_asset);  // PRQA S 3803
  }
}



} // namespace common
} // namespace assets
} // namespace cc 

