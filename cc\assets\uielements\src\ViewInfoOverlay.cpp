//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ViewInfoOverlays.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ViewInfoOverlay.h"
#include "CustomSystemConf.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class ViewInfoIconType : vfc::uint32_t
{
    VIEWINFO_SINGLE_FRONT,
    VIEWINFO_SINGLE_REAR,
    VIEWINFO_PERSPECTIVE,
    VIEWINFO_FRONT_WHEEL,
    VIEWINFO_REAR_WHEEL,
    VIEWINFO_FRONT_JUNCTION,
    VIEWINFO_REAR_JUNCTION,
    VIEWINFO_STB,
    PLEASECARESURROUDING,
    // FRONTWHEEL_LIMITLINE,
    // REARWHEEL_LIMITLINE,
    DEGRATION_NOT_CALIBRATED,
    DEGRATION_NOT_CALIBRATED_JUNCTIONVIEW,
    DEGRATION_ANDROID_ERROR,
    DEGRATION_ANDROID_ERROR_JUNCTIONVIEW,
    DEGRATION_YELLOW_ICON_PLANVIEW,
    DEGRATION_RED_ICON_PLANVIEW,
    VIEWINFO_SINGLE_FRONT_FLOAT,
    VIEWINFO_SINGLE_REAR_FLOAT,
    VIEWINFO_FRONT_WHEEL_FLOAT,
    VIEWINFO_REAR_WHEEL_FLOAT,
    PLEASECARESURROUDING_FLOAT_PLAN,
    PLEASECARESURROUDING_FLOAT_FR,
    // FRONTWHEEL_LIMITLINE_FLOAT,
    // REARWHEEL_LIMITLINE_FLOAT,
    DEGRATION_NOT_CALIBRATED_FLOAT,
    DEGRATION_ANDROID_ERROR_FLOAT,
    DEGRATION_YELLOW_ICON_PLANVIEW_FLOAT,
    DEGRATION_RED_ICON_PLANVIEW_FLOAT,
    FLOAT_CORNER_TOP_LEFT,
    FLOAT_CORNER_TOP_RIGHT,
    FLOAT_CORNER_BUTTOM_LEFT,
    FLOAT_CORNER_BUTTOM_RIGHT,
};

enum class BlackBackgroundLineType : vfc::uint32_t
{
    FULL_SCREEN_BLACK_LINE,
    FULL_SCREEN_WHEEL_VIEW_BLACK_LINE,
    FLOAT_SCREEN_BLACK_LINE,
    FLOAT_SCREEN_WHEEL_VIEW_BLACK_LINE
};

enum class FloatScreenCornerType : vfc::uint32_t
{
    FLOAT_PLAN_VIEW_LEFT_TOP,
    FLOAT_PLAN_VIEW_LEFT_BOTTOM,
    FLOAT_FR_VIEW_LEFT_TOP,
    FLOAT_FR_VIEW_LEFT_BOTTOM,
    FLOAT_FR_VIEW_RIGHT_TOP,
    FLOAT_FR_VIEW_RIGHT_BOTTOM,
    FLOAT_PLAN_VIEW_RIGHT_TOP,
    FLOAT_PLAN_VIEW_RIGHT_BOTTOM
};

enum class FullScreenCornerType :vfc::uint32_t
{
    FULL_SCREEN_LEFT_TOP,
    // FULL_SCREEN_LEFT_BOTTOM,
    FULL_SCREEN_RIGHT_TOP
    // FULL_SCREEN_RIGHT_BOTTOM
};
//!
//! @brief Construct a new ViewInfoOverlays Manager:: ViewInfoOverlays Manager object
//!
//! @param f_config
//!
ViewInfoOverlayManager::ViewInfoOverlayManager()
    : m_hasAndroidError{false}
    , m_hasCalibError{false}
    , m_hasExtrinsicFileError{false}
    , m_vhmDegrated{false}
    , m_hasCameraError{false}
    , m_lastConfigUpdate{~0u}
    , m_viewInfoIcons{}
    , m_backBackgroundLines{}
    , m_floatScreenCornerTextures{}
    , m_fullScreenCornerTextures{}
{
}

ViewInfoOverlayManager::~ViewInfoOverlayManager() = default;

void ViewInfoOverlayManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
    //! Init backline overlay
    m_backBackgroundLines.clear(f_imageOverlays);
    // FULL_SCREEN_BLACK_LINE
    m_backBackgroundLines.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathFullScreenBlackLineIcon,
            osg::Vec2f(
                (cc::core::g_views->m_planViewport.m_origin.x() + cc::core::g_views->m_planViewport.m_size.x() + // PRQA S 3011
                 cc::core::g_views->m_mainViewport.m_origin.x()) /
                    2.f,
                cc::core::g_views->m_mainViewport.m_origin.y() + cc::core::g_views->m_mainViewport.m_size.y() / 2.f), // PRQA S 3011
            osg::Vec2f(
                cc::core::g_views->m_mainViewport.m_origin.x() - cc::core::g_views->m_planViewport.m_origin.x() - // PRQA S 3011
                    cc::core::g_views->m_planViewport.m_size.x(),
                cc::core::g_views->m_mainViewport.m_size.y()))); // fill between planview and mainview // PRQA S 3011
    // FULL_SCREEN_WHEEL_VIEW_BLACK_LINE
    m_backBackgroundLines.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathFullScreenBlackLineIcon,
            osg::Vec2f(
                (cc::core::g_views->m_frontWheelLeft.m_origin.x() + cc::core::g_views->m_frontWheelLeft.m_size.x() + // PRQA S 3011
                 cc::core::g_views->m_frontWheelRight.m_origin.x()) /
                    2.f,
                cc::core::g_views->m_mainViewport.m_origin.y() + cc::core::g_views->m_mainViewport.m_size.y() / 2.f), // PRQA S 3011
            osg::Vec2f(
                cc::core::g_views->m_frontWheelRight.m_origin.x() - cc::core::g_views->m_frontWheelLeft.m_origin.x() - // PRQA S 3011
                    cc::core::g_views->m_frontWheelLeft.m_size.x(),
                cc::core::g_views->m_mainViewport.m_size.y()))); // PRQA S 3011
    // FLOAT_SCREEN_BLACK_LINE
    m_backBackgroundLines.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathFloatScreenBlackLineIcon,
            osg::Vec2f(
                (cc::core::g_views->m_floatPlanViewport.m_origin.x() + // PRQA S 3011
                 cc::core::g_views->m_floatPlanViewport.m_size.x() +
                 cc::core::g_views->m_floatMainViewport.m_origin.x()) /
                    2.f,
                cc::core::g_views->m_floatMainViewport.m_origin.y() + // PRQA S 3011
                    cc::core::g_views->m_floatMainViewport.m_size.y() / 2.f), // PRQA S 3011
            osg::Vec2f(
                cc::core::g_views->m_floatMainViewport.m_origin.x() -
                    cc::core::g_views->m_floatPlanViewport.m_origin.x() - // PRQA S 3011
                    cc::core::g_views->m_floatPlanViewport.m_size.x(),
                cc::core::g_views->m_floatMainViewport.m_size.y()))); // PRQA S 3011
    // FLOAT_SCREEN_WHEEL_VIEW_BLACK_LINE
    m_backBackgroundLines.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathFloatScreenBlackLineIcon,
            osg::Vec2f(
                (cc::core::g_views->m_floatWheelLeftViewport.m_origin.x() + // PRQA S 3011
                 cc::core::g_views->m_floatWheelLeftViewport.m_size.x() +
                 cc::core::g_views->m_floatWheelRightViewport.m_origin.x()) /
                    2.f,
                cc::core::g_views->m_floatMainViewport.m_origin.y() + // PRQA S 3011
                    cc::core::g_views->m_floatMainViewport.m_size.y() / 2.f), // PRQA S 3011
            osg::Vec2f(
                cc::core::g_views->m_floatWheelRightViewport.m_origin.x() -
                    cc::core::g_views->m_floatWheelLeftViewport.m_origin.x() - // PRQA S 3011
                    cc::core::g_views->m_floatWheelLeftViewport.m_size.x(),
                cc::core::g_views->m_floatMainViewport.m_size.y()))); // PRQA S 3011

    // ! init ViewInfoOverlays icons
    m_viewInfoIcons.clear(f_imageOverlays);

    // VIEWINFO_SINGLE_FRONT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(g_uiSettings->m_texturePathViewInfoSingleFront, g_uiSettings->m_viewInfoSingleFront.m_iconCenter));
    // VIEWINFO_SINGLE_REAR
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(g_uiSettings->m_texturePathViewInfoSingleRear, g_uiSettings->m_viewInfoSingleRear.m_iconCenter));
    // VIEWINFO_PERSPECTIVE
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(g_uiSettings->m_texturePathViewInfoPerspective, g_uiSettings->m_viewInfoSingleFront.m_iconCenter));
    // VIEWINFO_FRONT_WHEEL
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(g_uiSettings->m_texturePathViewInfoFrontWheel, g_uiSettings->m_viewInfoFrontWheel.m_iconCenter));
    // VIEWINFO_REAR_WHEEL
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(g_uiSettings->m_texturePathViewInfoRearWheel, g_uiSettings->m_viewInfoRearWheel.m_iconCenter));
    // VIEWINFO_FRONT_JUNCTION
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathViewInfoFrontJunction, g_uiSettings->m_viewInfoFrontJunction.m_iconCenter));
    // VIEWINFO_REAR_JUNCTION
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(g_uiSettings->m_texturePathViewInfoRearJunction, g_uiSettings->m_viewInfoRearJunction.m_iconCenter));
    // VIEWINFO_STB
    m_viewInfoIcons.addIcon(
        f_imageOverlays, createIcon(g_uiSettings->m_texturePathViewInfoSTB, g_uiSettings->m_viewInfoSTB.m_iconCenter));
    // PLEASECARESURROUDING
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathPleaseCareSurrounding, g_uiSettings->m_pleaseCareSurrounding.m_iconCenter));
    // // FRONTWHEEL_LIMITLINE
    // m_viewInfoIcons.addIcon(
    //     f_imageOverlays,
    //     createIcon(g_uiSettings->m_texturePathFrontWheelLimitLine, g_uiSettings->m_frontWheelLimitLine.m_iconCenter));
    // // REARWHEEL_LIMITLINE
    // m_viewInfoIcons.addIcon(
    //     f_imageOverlays,
    //     createIcon(g_uiSettings->m_texturePathRearWheelLimitLine, g_uiSettings->m_rearWheelLimitLine.m_iconCenter));
    // DEGRATION_NOT_CALIBRATED
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationNotCalirated, g_uiSettings->m_degrationNotCalirated.m_iconCenter));
    // DEGRATION_NOT_CALIBRATED_JUNCTIONVIEW
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationNotCalirated,
            g_uiSettings->m_degrationNotCaliratedJunctionView.m_iconCenter));
    // DEGRATION_ANDROID_ERROR
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationAndroidError, g_uiSettings->m_degrationAndroidError.m_iconCenter));
    // DEGRATION_ANDROID_ERROR_JUNCTIONVIEW
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationAndroidError,
            g_uiSettings->m_degrationAndroidErrorJunctionView.m_iconCenter));
    // DEGRATION_YELLOW_ICON_PLANVIEW
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationPlanviewIcon, g_uiSettings->m_degrationPlanviewIcon.m_iconCenter));
    // DEGRATION_RED_ICON_PLANVIEW
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationPlanviewRedIcon, g_uiSettings->m_degrationPlanviewIcon.m_iconCenter));
    // VIEWINFO_SINGLE_FRONT_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathViewInfoSingleFront,
            g_uiSettings->m_viewInfoSingleFrontFloat.m_iconCenter,
            g_uiSettings->m_viewInfoSingleFrontFloat.m_iconSize));
    // VIEWINFO_SINGLE_REAR_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathViewInfoSingleRear,
            g_uiSettings->m_viewInfoSingleRearFloat.m_iconCenter,
            g_uiSettings->m_viewInfoSingleRearFloat.m_iconSize));
    // VIEWINFO_FRONT_WHEEL_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathViewInfoFrontWheel,
            g_uiSettings->m_viewInfoFrontWheelFloat.m_iconCenter,
            g_uiSettings->m_viewInfoFrontWheelFloat.m_iconSize));
    // VIEWINFO_REAR_WHEEL_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathViewInfoRearWheel,
            g_uiSettings->m_viewInfoRearWheelFloat.m_iconCenter,
            g_uiSettings->m_viewInfoRearWheelFloat.m_iconSize));
    // PLEASECARESURROUDING_FLOAT_PLAN
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathFloatPleaseCareSurrounding,
            g_uiSettings->m_pleaseCareSurroundingFloatPlan.m_iconCenter));
    //PLEASECARESURROUDING_FLOAT_FR
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathFloatPleaseCareSurrounding,
            g_uiSettings->m_pleaseCareSurroundingFloatFR.m_iconCenter));
    // // FRONTWHEEL_LIMITLINE_FLOAT
    // m_viewInfoIcons.addIcon(
    //     f_imageOverlays,
    //     createIcon(
    //         g_uiSettings->m_texturePathFrontWheelLimitLine,
    //         g_uiSettings->m_frontWheelLimitLineFloat.m_iconCenter,
    //         g_uiSettings->m_frontWheelLimitLineFloat.m_iconSize));
    // // REARWHEEL_LIMITLINE_FLOAT
    // m_viewInfoIcons.addIcon(
    //     f_imageOverlays,
    //     createIcon(
    //         g_uiSettings->m_texturePathRearWheelLimitLine,
    //         g_uiSettings->m_rearWheelLimitLineFloat.m_iconCenter,
    //         g_uiSettings->m_rearWheelLimitLineFloat.m_iconSize));
    // DEGRATION_NOT_CALIBRATED_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationNotCalirated, g_uiSettings->m_degrationNotCaliratedFloat.m_iconCenter));
    // DEGRATION_ANDROID_ERROR_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationAndroidError, g_uiSettings->m_degrationAndroidErrorFloat.m_iconCenter));
    // DEGRATION_ICON_PLANVIEW_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationPlanviewIcon, g_uiSettings->m_degrationPlanviewIconFloat.m_iconCenter));
    // DEGRATION_RED_ICON_PLANVIEW_FLOAT
    m_viewInfoIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathDegrationPlanviewRedIcon, g_uiSettings->m_degrationPlanviewIconFloat.m_iconCenter));
    // FLOAT_PLAN_VIEW_LEFT_TOP
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatTopLeftCorner,
            osg::Vec2f(
                cc::core::g_views->m_floatPlanViewport.m_origin.x() + // PRQA S 3011
                g_uiSettings->m_floatCornerTextureSize.x()/2.0f, cc::core::g_views->m_floatPlanViewport.m_origin.y()
                + // PRQA S 3011
                    cc::core::g_views->m_floatPlanViewport.m_size.y() -
                    g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    // FLOAT_PLAN_VIEW_LEFT_BOTTOM
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatButtomLeftCorner,
            osg::Vec2f(
                cc::core::g_views->m_floatPlanViewport.m_origin.x() + // PRQA S 3011
                g_uiSettings->m_floatCornerTextureSize.x()/2.0f, cc::core::g_views->m_floatPlanViewport.m_origin.y() // PRQA S 3011
                + g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    //FLOAT_FR_VIEW_LEFT_TOP
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatTopLeftCorner,
            osg::Vec2f(
                cc::core::g_views->m_floatMainViewport.m_origin.x() + // PRQA S 3011
                g_uiSettings->m_floatCornerTextureSize.x()/2.0f, cc::core::g_views->m_floatMainViewport.m_origin.y()
                + // PRQA S 3011
                    cc::core::g_views->m_floatMainViewport.m_size.y() -
                    g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));

    // m_floatScreenCornerTextures.addIcon(
    //     f_imageOverlays,
    //     cc::assets::uielements::createAlphaMaskIcon(
    //         g_uiSettings->m_texturePathfloatButtomRightCorner,
    //         osg::Vec2f(
    //           cc::core::g_views->m_floatMainViewport.m_origin.x() + cc::core::g_views->m_floatMainViewport.m_size.x() // PRQA S 3011
    //           -  g_uiSettings->m_floatCornerTextureSize.x()/2.0f,
    //             cc::core::g_views->m_floatMainViewport.m_origin.y() + // PRQA S 3011
    //             g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    // FLOAT_FR_VIEW_LEFT_BOTTOM
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatButtomLeftCorner,
            osg::Vec2f(
                cc::core::g_views->m_floatMainViewport.m_origin.x() + // PRQA S 3011
                g_uiSettings->m_floatCornerTextureSize.x()/2.0f, cc::core::g_views->m_floatMainViewport.m_origin.y() // PRQA S 3011
                + g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));

    // FLOAT_FR_VIEW_RIGHT_TOP
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatTopRightCorner,
            osg::Vec2f(
                cc::core::g_views->m_floatMainViewport.m_origin.x() + // PRQA S 3011
                cc::core::g_views->m_floatMainViewport.m_size.x() -  g_uiSettings->m_floatCornerTextureSize.x()/2.0f,
                cc::core::g_views->m_floatMainViewport.m_origin.y() + // PRQA S 3011
                    cc::core::g_views->m_floatMainViewport.m_size.y() -
                    g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    // FLOAT_FR_VIEW_RIGHT_BOTTOM
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatButtomRightCorner,
            osg::Vec2f(
              cc::core::g_views->m_floatMainViewport.m_origin.x() + cc::core::g_views->m_floatMainViewport.m_size.x() // PRQA S 3011
              -  g_uiSettings->m_floatCornerTextureSize.x()/2.0f,
                cc::core::g_views->m_floatMainViewport.m_origin.y() + // PRQA S 3011
                g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    //FLOAT_PLAN_VIEW_RIGHT_TOP
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatTopRightCorner,
            osg::Vec2f(
                cc::core::g_views->m_floatPlanViewport.m_origin.x() + // PRQA S 3011
                cc::core::g_views->m_floatPlanViewport.m_size.x() -  g_uiSettings->m_floatCornerTextureSize.x()/2.0f,
                cc::core::g_views->m_floatPlanViewport.m_origin.y() + // PRQA S 3011
                    cc::core::g_views->m_floatPlanViewport.m_size.y() -
                    g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    //FLOAT_PLAN_VIEW_RIGHT_BOTTOM
    m_floatScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createAlphaMaskIcon(
            g_uiSettings->m_texturePathfloatButtomRightCorner,
            osg::Vec2f(
              cc::core::g_views->m_floatPlanViewport.m_origin.x() + cc::core::g_views->m_floatPlanViewport.m_size.x() // PRQA S 3011
              -  g_uiSettings->m_floatCornerTextureSize.x()/2.0f,
                cc::core::g_views->m_floatPlanViewport.m_origin.y() + // PRQA S 3011
                g_uiSettings->m_floatCornerTextureSize.y()/2.0f)));
    //FULL_SCREEN_LEFT_TOP
    m_fullScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createIcon(
            g_uiSettings->m_texturePathFullTopLeftCorner,
            osg::Vec2f(15.0f, 1305.0f)));
    // // FULL_SCREEN_LEFT_BOTTOM
    //  m_fullScreenCornerTextures.addIcon(
    //     f_imageOverlays,
    //     cc::assets::uielements::createIcon(
    //         g_uiSettings->m_texturePathFullButtomLeftCorner,
    //         osg::Vec2f(15,15)));
    // FULL_SCREEN_RIGHT_TOP
    m_fullScreenCornerTextures.addIcon(
        f_imageOverlays,
        cc::assets::uielements::createIcon(
            g_uiSettings->m_texturePathFullTopRightCorner,
            osg::Vec2f(2545.0f, 1305.0f)));
    // // FULL_SCREEN_RIGHT_BOTTOM
    //  m_fullScreenCornerTextures.addIcon(
    //     f_imageOverlays,
    //     cc::assets::uielements::createIcon(
    //         g_uiSettings->m_texturePathFullButtomRightCorner,
    //         osg::Vec2f(2545,15)));
}

void ViewInfoOverlayManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)
{
    if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
    {
        return;
    }
    // ! check if config has changed
    if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
    {
        init(f_imageOverlays);
        m_lastConfigUpdate = g_uiSettings->getModifiedCount();
    }
    m_viewInfoIcons.setAllEnabled(false);
    m_backBackgroundLines.setAllEnabled(false);
    m_floatScreenCornerTextures.setAllEnabled(false);
    m_fullScreenCornerTextures.setAllEnabled(false);
    EScreenID       l_displayedView = EScreenID::EScreenID_SINGLE_FRONT_NORMAL;
    cc::target::common::EAVMScreen l_avmScreenType = cc::target::common::EAVMScreen::SCREEN_NONE;

    // Android Error
    if (f_framework->m_androidAlive_Receiver.isConnected())
    {
        if (f_framework->m_androidAlive_Receiver.hasData())
        {
            const cc::daddy::AndroidAliveDaddy_t* const l_androidAliveDaddy = f_framework->m_androidAlive_Receiver.getData();
            m_hasAndroidError = (l_androidAliveDaddy->m_Data == false); // false represents android not alive
        }
    }

    // Not calibrated
    if (f_framework->m_calibOrNot_Receiver.isConnected())
    {
        if (f_framework->m_calibOrNot_Receiver.hasData())
        {
            const cc::daddy::CalibOrNotDaddy_t* const l_calibOrNotDaddy = f_framework->m_calibOrNot_Receiver.getData();
            m_hasCalibError = (l_calibOrNotDaddy->m_Data == false); // false represents not calibrated
        }
    }

    // Not calibrated
    // if (f_framework->m_extrinsicFileError_Receiver.isConnected())
    // {
    //     if (f_framework->m_extrinsicFileError_Receiver.hasData())
    //     {
    //         const cc::daddy::ExtrinsicFileErrorDaddy_t* l_extrinsicFileErrorDaddy = f_framework->m_extrinsicFileError_Receiver.getData();
    //         m_hasExtrinsicFileError = (l_extrinsicFileErrorDaddy->m_Data); // true represents error
    //     }
    // }

    // Vhm Error
    if (f_framework->m_svsFid_ReceiverPort.isConnected())
    {
        if (f_framework->m_svsFid_ReceiverPort.hasData())
        {
            const cc::daddy::SvsFidDaddy_t* const l_SvsFiDaddy = f_framework->m_svsFid_ReceiverPort.getData();
            m_vhmDegrated = (l_SvsFiDaddy->m_Data.m_FID_RxVhmOverall == 0u); // 0u represents error
        }
    }


    if (f_framework->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayedViewDaddy =
            f_framework->m_displayedView_ReceiverPort.getData();
        l_displayedView = l_displayedViewDaddy->m_Data;
    }

    if (f_framework->m_avmScreenType_ReceiverPort.hasData())
    {
        const cc::daddy::AvmScreenTypeDaddy_t* const l_screenTypeDaddy = f_framework->m_avmScreenType_ReceiverPort.getData();
        l_avmScreenType                                          = l_screenTypeDaddy->m_Data;
    }

    const pc::daddy::CameraDegradationMaskDaddy* const l_camDegradationMaskDaddy = f_framework->m_degradationMaskReceiver.getData();
    if (nullptr != l_camDegradationMaskDaddy)
    {
        //! Camera Degradation mask - Each bit corresponds to a camera. bit value 1 => degraded, 0 => nominal
        m_hasCameraError = (0U != l_camDegradationMaskDaddy->m_Data);
    }
    const bool l_showNotCalibOrCalibError = m_hasCalibError /*|| m_hasExtrinsicFileError*/;
    const bool l_showAVMError = m_hasAndroidError || m_vhmDegrated || m_hasCameraError;
    ViewInfoIconType l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_SINGLE_FRONT;
    ViewInfoIconType l_displayedWarnIconPlanView   = ViewInfoIconType::DEGRATION_YELLOW_ICON_PLANVIEW;
    ViewInfoIconType l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
    bool l_needDisplayedWarnIconPlanView = true;
    if (l_showNotCalibOrCalibError)
    {
        if (l_displayedView == EScreenID_SINGLE_FRONT_JUNCTION || l_displayedView == EScreenID_SINGLE_REAR_JUNCTION)
        {
            m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(ViewInfoIconType::DEGRATION_NOT_CALIBRATED_JUNCTIONVIEW))->setEnabled(true);
            l_displayedWarnIconPlanView = ViewInfoIconType::DEGRATION_RED_ICON_PLANVIEW; // Not Show actually
        }
        else if (
            (l_displayedView == EScreenID_FLOAT_SINGLE_FRONT) || (l_displayedView == EScreenID_FLOAT_SINGLE_REAR) ||
            (l_displayedView == EScreenID_FLOAT_WHEEL_FRONT_DUAL) ||
            (l_displayedView == EScreenID_FLOAT_WHEEL_REAR_DUAL)  ||
            (l_displayedView == EScreenID_FLOAT_FRONT_VIEW)   ||
            (l_displayedView == EScreenID_FLOAT_REAR_VIEW)||
            (l_displayedView == EScreenID_FLOAT_REAR_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW)  )
        {
            m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(ViewInfoIconType::DEGRATION_NOT_CALIBRATED_FLOAT))->setEnabled(true);
            l_needDisplayedWarnIconPlanView=false;
            // l_displayedWarnIconPlanView = DEGRATION_RED_ICON_PLANVIEW_FLOAT;
        }
        else if(
            (l_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW)   ||
            (l_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW))
        {
            l_displayedWarnIconPlanView = ViewInfoIconType::DEGRATION_RED_ICON_PLANVIEW_FLOAT;
        }
        else
        {
            m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(ViewInfoIconType::DEGRATION_NOT_CALIBRATED))->setEnabled(true);
            l_displayedWarnIconPlanView = ViewInfoIconType::DEGRATION_RED_ICON_PLANVIEW;
        }
    }

    if (l_showAVMError)
    {
        if( m_hasAndroidError)
        {
            XLOG_ERROR(g_AppContext, "Degration Log: Android APP Error!\n");                                                    \
        }
        // else if (m_vhmDegrated)
        // {
        //     XLOG_ERROR(g_AppContext, "Degration Log: VHM Error!\n");                                                    \ // PRQA S 1054
        // }
        // else
        // {
        //     // NO log
        // }

        if (l_displayedView == EScreenID_SINGLE_FRONT_JUNCTION || l_displayedView == EScreenID_SINGLE_REAR_JUNCTION)
        {
            m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(ViewInfoIconType::DEGRATION_ANDROID_ERROR_JUNCTIONVIEW))->setEnabled(true);
            l_displayedWarnIconPlanView   = ViewInfoIconType::DEGRATION_YELLOW_ICON_PLANVIEW; // Not Show actually
        }
        else if (
            (l_displayedView == EScreenID_FLOAT_SINGLE_FRONT) || (l_displayedView == EScreenID_FLOAT_SINGLE_REAR) ||
            (l_displayedView == EScreenID_FLOAT_WHEEL_FRONT_DUAL) ||
            (l_displayedView == EScreenID_FLOAT_WHEEL_REAR_DUAL)||
            (l_displayedView == EScreenID_FLOAT_FRONT_VIEW)   ||
            (l_displayedView == EScreenID_FLOAT_REAR_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_PARKING_FRONT_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_PARKING_REAR_VIEW) )
        {
            m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(ViewInfoIconType::DEGRATION_ANDROID_ERROR_FLOAT))->setEnabled(true);
            // l_displayedWarnIconPlanView   =  ViewInfoIconType::DEGRATION_YELLOW_ICON_PLANVIEW_FLOAT;
            l_needDisplayedWarnIconPlanView=false;
        }
        else if(
            (l_displayedView == EScreenID_FLOAT_FRONT_PLAN_VIEW)   ||
            (l_displayedView == EScreenID_FLOAT_REAR_PLAN_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW) ||
            (l_displayedView == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW))
        {
            l_displayedWarnIconPlanView =  ViewInfoIconType::DEGRATION_YELLOW_ICON_PLANVIEW_FLOAT;
        }
        else
        {
            m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(ViewInfoIconType::DEGRATION_ANDROID_ERROR))->setEnabled(true);
            l_displayedWarnIconPlanView   = ViewInfoIconType::DEGRATION_YELLOW_ICON_PLANVIEW;
        }
    }

    switch (l_displayedView)
    {
    case EScreenID_SINGLE_FRONT_NORMAL:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_SINGLE_FRONT;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_SINGLE_REAR;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_PERSPECTIVE_PFR:
    case EScreenID_PERSPECTIVE_FL:
    case EScreenID_PERSPECTIVE_FR:
    case EScreenID_PERSPECTIVE_PRE:
    case EScreenID_PERSPECTIVE_RL:
    case EScreenID_PERSPECTIVE_RR:
    case EScreenID_PERSPECTIVE_PLE:
    case EScreenID_PERSPECTIVE_PRI:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_PERSPECTIVE;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_WHEEL_FRONT_DUAL:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_FRONT_WHEEL;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_WHEEL_REAR_DUAL:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_REAR_WHEEL;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_SINGLE_FRONT_JUNCTION:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_FRONT_JUNCTION;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_SINGLE_REAR_JUNCTION:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_REAR_JUNCTION;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    case EScreenID_SINGLE_STB:
    {
        l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_STB;
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING;
        break;
    }
    // case EScreenID_FLOAT_SINGLE_FRONT: // PRQA S 4066
    //     l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_SINGLE_FRONT_FLOAT;
    //     l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
    //     break;
    // case EScreenID_FLOAT_SINGLE_REAR: // PRQA S 4066
    //     l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_SINGLE_REAR_FLOAT;
    //     l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
    //     break;
    // case EScreenID_FLOAT_WHEEL_FRONT_DUAL: // PRQA S 4066
    //     l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_FRONT_WHEEL_FLOAT;
    //     l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
    //     break;
    // case EScreenID_FLOAT_WHEEL_REAR_DUAL: // PRQA S 4066
    //     l_displayedIconSingleView = ViewInfoIconType::VIEWINFO_REAR_WHEEL_FLOAT;
    //     l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
    //     break;
    case EScreenID_FLOAT_FRONT_PLAN_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
        break;
    }
    case EScreenID_FLOAT_REAR_PLAN_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
        break;
    }
    case EScreenID_FLOAT_FRONT_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_FR;
        break;
    }
    case EScreenID_FLOAT_REAR_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_FR;
        break;
    }
    case EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
        break;
    }
    case EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_PLAN;
        break;
    }
    case EScreenID_FLOAT_PARKING_FRONT_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_FR;
        break;
    }
    case EScreenID_FLOAT_PARKING_REAR_VIEW:
    {
        l_displayedIconPlanView   = ViewInfoIconType::PLEASECARESURROUDING_FLOAT_FR;
        break;
    }
    default:
    {
        break;
    }
    }
    switch(l_displayedView)
    {
        case EScreenID_FLOAT_FRONT_PLAN_VIEW:
        case EScreenID_FLOAT_REAR_PLAN_VIEW:
        case EScreenID_FLOAT_FRONT_VIEW:
        case EScreenID_FLOAT_REAR_VIEW:
        case EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW:
        case EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW:
        case EScreenID_FLOAT_PARKING_FRONT_VIEW:
        case EScreenID_FLOAT_PARKING_REAR_VIEW:{break;}
        default:
            {m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(l_displayedIconSingleView))->setEnabled(true);break;}

    }
    m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(l_displayedIconPlanView))->setEnabled(true);

    if ((l_displayedView != EScreenID_SINGLE_FRONT_JUNCTION && l_displayedView != EScreenID_SINGLE_REAR_JUNCTION) &&
          (l_showNotCalibOrCalibError || l_showAVMError)&&l_needDisplayedWarnIconPlanView)
    {
        m_viewInfoIcons.getIcon(static_cast<vfc::uint32_t>(l_displayedWarnIconPlanView))->setEnabled(true);
    }

    // background black line
    if ((l_avmScreenType == cc::target::common::EAVMScreen::SCREEN_FULL_NOTR || l_avmScreenType == cc::target::common::EAVMScreen::SCREEN_FULL_R) &&
        (l_displayedView != EScreenID_SINGLE_REAR_JUNCTION && l_displayedView != EScreenID_SINGLE_FRONT_JUNCTION))
    {
        m_fullScreenCornerTextures.setAllEnabled(true);
        m_backBackgroundLines.getIcon(static_cast<vfc::uint32_t>(BlackBackgroundLineType::FULL_SCREEN_BLACK_LINE))->setEnabled(true);
        if((l_displayedView == EScreenID_WHEEL_FRONT_DUAL || l_displayedView == EScreenID_WHEEL_REAR_DUAL))
        {
            m_backBackgroundLines.getIcon(static_cast<vfc::uint32_t>(BlackBackgroundLineType::FULL_SCREEN_WHEEL_VIEW_BLACK_LINE))->setEnabled(true);
        }
    }
    else if (l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_R||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_R||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_NOTR||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_NOTR||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_PARK||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_PARK)
    {
        if(l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_R||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_NOTR||
            l_avmScreenType== cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_PARK)
        {
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_PLAN_VIEW_LEFT_BOTTOM))->setEnabled(true);
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_PLAN_VIEW_LEFT_TOP))->setEnabled(true);
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_PLAN_VIEW_RIGHT_BOTTOM))->setEnabled(true);
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_PLAN_VIEW_RIGHT_TOP))->setEnabled(true);
        }
        else
        {
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_FR_VIEW_LEFT_BOTTOM))->setEnabled(true);
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_FR_VIEW_LEFT_TOP))->setEnabled(true);
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_FR_VIEW_RIGHT_BOTTOM))->setEnabled(true);
            m_floatScreenCornerTextures.getIcon(static_cast<vfc::uint32_t>(FloatScreenCornerType::FLOAT_FR_VIEW_RIGHT_TOP))->setEnabled(true);
        }

    }
    else
    {
        m_fullScreenCornerTextures.setAllEnabled(true);
        // Do nothing
    }

    // if (l_displayedView == EScreenID_WHEEL_FRONT_DUAL)
    // {
    //     m_viewInfoIcons.getIcon(FRONTWHEEL_LIMITLINE)->setEnabled(true);
    // }
    // else if (l_displayedView == EScreenID_WHEEL_REAR_DUAL)
    // {
    //     m_viewInfoIcons.getIcon(REARWHEEL_LIMITLINE)->setEnabled(true);
    // }
    // else if (l_displayedView == EScreenID_FLOAT_WHEEL_FRONT_DUAL)
    // {
    //     m_viewInfoIcons.getIcon(FRONTWHEEL_LIMITLINE_FLOAT)->setEnabled(true);
    // }
    // else if (l_displayedView == EScreenID_FLOAT_WHEEL_REAR_DUAL)
    // {
    //     m_viewInfoIcons.getIcon(REARWHEEL_LIMITLINE_FLOAT)->setEnabled(true);
    // }
    // else
    // {
    //     // do nothing
    // }
}

//!
//! @brief Construct a new ViewInfoOverlays:: ViewInfoOverlays object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ViewInfoOverlays::ViewInfoOverlays(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
    : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
    , m_customFramework{f_customFramework}
    , m_manager{}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}

ViewInfoOverlays::~ViewInfoOverlays() = default;

void ViewInfoOverlays::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        m_manager.update(this, m_customFramework);
    }
    pc::assets::ImageOverlays::traverse(f_nv);
}

} // namespace uielements
} // namespace assets
} // namespace cc
