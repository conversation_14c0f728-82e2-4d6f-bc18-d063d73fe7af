/// @copyright (C) 2025 Robert <PERSON> GmbH.
/// The reproduction, distribution and utilization of this file as well as the
/// communication of its contents to others without express authorization is
/// prohibited. Offenders will be held liable for the payment of damages. All
/// rights reserved in the event of the grant of a patent, utility model or
/// design.
/// @file

#include "cc/assets/dynamicwheelmask/inc/DynamicWheelMask.h"
// #include "cc/core/inc/ shader_manager.hpp"

#include "pc/svs/factory/inc/SV3DGeode.h"

#include "vfc/core/vfc_math.hpp"

#include "pc/svs/util/osgx/inc/Utils.h"

namespace cc
{
namespace assets
{

namespace
{
pc::util::coding::Item<DynamicWheelMaskSettings> g_dynamicWheelMaskSettings("DynamicWheelMask");
} // namespace

DynWheelMaskAsset::DynWheelMaskAsset(const cc::core::AssetId f_assetId, pc::core::Framework* const f_framework)
    : Asset(f_assetId)
    , m_dynWheelMaskGeode{}
    , m_dynWheelMaskCallback{}
{
    m_dynWheelMaskGeode = osg_ext::make_ref<DynWheelMaskGeode>(f_framework, osg_ext::make_ref<osg::Geometry>());
    m_dynWheelMaskGeode->setName("DynWheelMaskGeode");
    m_dynWheelMaskCallback = osg_ext::make_ref<DynWheelMaskCallback>();

    getOrCreateStateSet()->setRenderBinDetails(pc::core::g_renderOrder->m_floor, "RenderBin");

    addCullCallback(m_dynWheelMaskCallback);
    addUpdateCallback(m_dynWheelMaskCallback);

    osg::Group::addChild(m_dynWheelMaskGeode.get()); // PRQA S 3803
}

void DynWheelMaskAsset::addSingleStateSet(osg::StateSet* f_stateSet)
{
    m_dynWheelMaskCallback->setSingleStateSet(f_stateSet);
}
void DynWheelMaskAsset::addTwoStateSets(osg::StateSet* f_stateSet1, osg::StateSet* f_stateSet2)
{
    m_dynWheelMaskCallback->setTwoStateSets(f_stateSet1, f_stateSet2);
}

DynWheelMaskGeode::DynWheelMaskGeode(pc::core::Framework* f_framework, osg::Geometry* f_geometry)
    : m_geometry{f_geometry}
    , m_framework{f_framework}
    , m_show{true}
    , m_xMin{}
    , m_xMax{}
    , m_left{}
    , m_right{}
    , m_currentShape{}
    , m_updateRequired{}
{
    const auto l_vertices = osg_ext::make_ref<osg::Vec3Array>(2 * (NUMBER_CURVE_VERTICES*2 + NUMBER_BASIC_VERTICES));
    m_geometry->setVertexArray(l_vertices);

    const auto l_indices = osg_ext::make_ref<osg::DrawElementsUByte>(osg::PrimitiveSet::TRIANGLES);

    // right wheel indices --------------------------------------------------------------------------------
    //
    // 0 --- 1 -\
    // |   / |    \
    // | /   |     |
    // 2 --- 3 --- 4
    // |        /  |
    // |     /     |
    // |  /        |
    // 5---  6 --- 7
    // |   / |     |
    // | /   |    /
    // 8 --- 9 -/
    l_indices->push_back(0);
    l_indices->push_back(1);
    l_indices->push_back(2);

    l_indices->push_back(1);
    l_indices->push_back(3);
    l_indices->push_back(2);

    l_indices->push_back(2);
    l_indices->push_back(4);
    l_indices->push_back(5);

    l_indices->push_back(4);
    l_indices->push_back(7);
    l_indices->push_back(5);

    l_indices->push_back(5);
    l_indices->push_back(6);
    l_indices->push_back(8);

    l_indices->push_back(6);
    l_indices->push_back(9);
    l_indices->push_back(8);

    // right-lower Rounded area
    // first
    l_indices->push_back(7);
    l_indices->push_back(NUMBER_BASIC_VERTICES);
    l_indices->push_back(6);

    // center of curve
    for (auto i = NUMBER_BASIC_VERTICES; i < NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES - 1; ++i)
    {
        l_indices->push_back(i);
        l_indices->push_back(i + 1);
        l_indices->push_back(6);
    }
    // last
    l_indices->push_back(NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES - 1);
    l_indices->push_back(9);
    l_indices->push_back(6);

    // right-higher Rounded area
    // first
    l_indices->push_back(1);
    l_indices->push_back(NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES);
    l_indices->push_back(3);

    // center of curve
    for (auto i = NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES; i < NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES * 2 - 1; ++i)
    {
        l_indices->push_back(i);
        l_indices->push_back(i + 1);
        l_indices->push_back(3);
    }
    // last
    l_indices->push_back(NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES * 2 - 1);
    l_indices->push_back(4);
    l_indices->push_back(3);

    m_right.m_yCurrent = m_right.m_yTarget;


    //    /- 0 --- 1
    //  /    |   / |
    // |     | /   |
    // 2 --- 3 --- 4
    // |        /  |
    // |     /     |
    // |  /        |
    // 5 --- 6 --- 7
    // |     |   / |
    //  \    | /   |
    //    \- 8 --- 9

    // left wheel indices --------------------------------------------------------------------------------
    const auto l_leftStart = NUMBER_CURVE_VERTICES * 2 + NUMBER_BASIC_VERTICES;
    //
    l_indices->push_back(l_leftStart + 0);
    l_indices->push_back(l_leftStart + 1);
    l_indices->push_back(l_leftStart + 3);

    l_indices->push_back(l_leftStart + 1);
    l_indices->push_back(l_leftStart + 4);
    l_indices->push_back(l_leftStart + 3);

    l_indices->push_back(l_leftStart + 2);
    l_indices->push_back(l_leftStart + 4);
    l_indices->push_back(l_leftStart + 5);

    l_indices->push_back(l_leftStart + 4);
    l_indices->push_back(l_leftStart + 7);
    l_indices->push_back(l_leftStart + 5);

    l_indices->push_back(l_leftStart + 6);
    l_indices->push_back(l_leftStart + 7);
    l_indices->push_back(l_leftStart + 8);

    l_indices->push_back(l_leftStart + 7);
    l_indices->push_back(l_leftStart + 9);
    l_indices->push_back(l_leftStart + 8);

    // left-lower rounded area
    // first
    l_indices->push_back(l_leftStart + 8);
    l_indices->push_back(l_leftStart + NUMBER_BASIC_VERTICES);
    l_indices->push_back(l_leftStart + 6);

    // center of curve
    for (auto i = NUMBER_BASIC_VERTICES; i < NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES - 1; ++i)
    {
        l_indices->push_back(l_leftStart + i);
        l_indices->push_back(l_leftStart + i + 1);
        l_indices->push_back(l_leftStart + 6);
    }
    // last
    l_indices->push_back(l_leftStart + NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES - 1);
    l_indices->push_back(l_leftStart + 5);
    l_indices->push_back(l_leftStart + 6);

    // left-higher rounded area
    // first
    l_indices->push_back(l_leftStart + 2);
    l_indices->push_back(l_leftStart + NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES);
    l_indices->push_back(l_leftStart + 3);

    // center of curve
    for (auto i = NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES; i < NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES*2 - 1; ++i)
    {
        l_indices->push_back(l_leftStart + i);
        l_indices->push_back(l_leftStart + i + 1);
        l_indices->push_back(l_leftStart + 3);
    }
    // last
    l_indices->push_back(l_leftStart + NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES*2 - 1);
    l_indices->push_back(l_leftStart + 0);
    l_indices->push_back(l_leftStart + 3);

    // Add an additional geometry to link the left and right sides
    // Join left-1,9 with right-0,8

    //    /- 0 --- 1             0 --- 1 -\
    //  /    |   / |             |   / |    \
    // |     | /   |             | /   |     |
    // 2 --- 3 --- 4             2 --- 3 --- 4
    // |        /  |             |        /  |
    // |     /     |             |     /     |
    // |  /        |             |  /        |
    // 5 --- 6 --- 7             5---  6 --- 7
    // |     |   / |             |   / |     |
    //  \    | /   |             | /   |    /
    //    \- 8 --- 9             8 --- 9 -/

    // First triangle: L1-R0-L9
    l_indices->push_back(l_leftStart + 1);
    l_indices->push_back(0);
    l_indices->push_back(l_leftStart + 9);
    // Second triangle: R0-R8-L9
    l_indices->push_back(0);
    l_indices->push_back(8);
    l_indices->push_back(l_leftStart + 9);

    m_geometry->addPrimitiveSet(l_indices); // PRQA S 3803
    osg::Geode::addDrawable(m_geometry);    // PRQA S 3803

    // Call update a first time so the geometry coordinates are correctly initialized
    update();
}

void DynWheelMaskGeode::update()
{
    updateMembersFromSettings();
    updateTargetShape();
    animateVerticesBasic();

    if (m_updateRequired)
    {
        /// Update vertices.
        osg::Vec3Array* const l_vertices =
            dynamic_cast<osg::Vec3Array*>(m_geometry->getVertexArray()); // PRQA S 3077 // PRQA S 3400
        if (nullptr != l_vertices)
        {
            // update left
            updateRightVertices(l_vertices);

            // update right
            updateLeftVertices(l_vertices);

            l_vertices->dirty();
        }
        m_geometry->dirtyBound();
        if (m_left.m_yCurrent == m_left.m_yTarget && m_right.m_yCurrent == m_right.m_yTarget)
        {
            m_updateRequired = false;
        }
    }
}

void DynWheelMaskGeode::updateRightVertices(osg::Vec3Array* f_vertices)
{

    // The right geometry has the following shape
    //
    // 0 --- 1 -\
    // |   / |    \
    // | /   |     |
    // 2 --- 3 --- 4
    // |        /  |
    // |     /     |
    // |  /        |
    // 5---  6 --- 7
    // |   / |     |
    // | /   |    /
    // 8 --- 9 -/

    // Width and length of shape
    const vfc::float32_t l_width = vfc::abs(m_right.m_yCurrent - m_right.m_y);
    const vfc::float32_t l_R     = l_width / 2.0f;

    // Right geometry takes first half of the array
    const vfc::int32_t l_startIndex = 0;

    /// @brief Vertices z coordinate.
    const vfc::float32_t l_ground = 0.0f;

    // Upper row of points - 0, 1
    (*f_vertices)[l_startIndex]     = osg::Vec3(m_xMax, m_right.m_y, l_ground);
    (*f_vertices)[l_startIndex + 1] = osg::Vec3(m_xMax, m_right.m_yCurrent + l_R, l_ground);
    const auto l_rightTop = osg::Vec3(m_xMax, m_right.m_yCurrent, l_ground);

    // Middle higher row of points - 2, 3, 4
    const osg::Vec3 l_offset1(-l_R, 0.0f, l_ground);
    (*f_vertices)[l_startIndex + 2] = (*f_vertices)[l_startIndex] + l_offset1;
    (*f_vertices)[l_startIndex + 3] = (*f_vertices)[l_startIndex + 1] + l_offset1;
    (*f_vertices)[l_startIndex + 4] = l_rightTop + l_offset1;

    // Middle lower row of points - 5, 6, 7
    const osg::Vec3 l_offset2(m_xMin - m_xMax + l_R, 0.0f, l_ground);
    (*f_vertices)[l_startIndex + 5] = (*f_vertices)[l_startIndex] + l_offset2;
    (*f_vertices)[l_startIndex + 6] = (*f_vertices)[l_startIndex + 1] + l_offset2;
    (*f_vertices)[l_startIndex + 7] = l_rightTop + l_offset2;

    // Lower row of points - 8, 9
    const osg::Vec3 l_offset3(m_xMin - m_xMax, 0.0f, l_ground);
    (*f_vertices)[l_startIndex + 8] = (*f_vertices)[l_startIndex] + l_offset3;
    (*f_vertices)[l_startIndex + 9] = (*f_vertices)[l_startIndex + 1] + l_offset3;

    // Curvature on the lower-right corner.
    // Take the segment 6-7 and rotate
    const vfc::float32_t l_ang = osg::DegreesToRadians(90.0f) / static_cast<vfc::float32_t>(NUMBER_CURVE_VERTICES + 1);
    const osg::Vec3      l_startVec = (*f_vertices)[l_startIndex + 7] - (*f_vertices)[l_startIndex + 6]; // segment 6-7
    const osg::Vec3      l_center   = (*f_vertices)[l_startIndex + 6]; // rotate over 6
    for (vfc::int32_t i = 1; i <= 2; ++i)
    {
        const osg::Vec3 l_rot = l_startVec * osg::Matrix::rotate(-l_ang * i, osg::Z_AXIS);
        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = l_center + l_rot;
    }
    const osg::Vec3 l_mid = (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + 1];

    osg::Vec3 A = (*f_vertices)[l_startIndex + 9];
    osg::Vec3 B = l_mid;
    osg::Vec3 vec_AB = B - A;

    float dot_AB = vec_AB[0]*vec_AB[0] + vec_AB[1]*vec_AB[1] + vec_AB[2]*vec_AB[2];

    for (vfc::int32_t i = 3; i <= NUMBER_CURVE_VERTICES; ++i)
    {
        const osg::Vec3 l_rot = l_startVec * osg::Matrix::rotate(-l_ang * i, osg::Z_AXIS);
        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = l_center + l_rot;
    }
    for (vfc::int32_t i = 3; i <= NUMBER_CURVE_VERTICES; ++i)
    {
        osg::Vec3 P = (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1];

        osg::Vec3 vec_AP = P - A;
        float t = (vec_AP[0]*vec_AB[0] + vec_AP[1]*vec_AB[1] + vec_AP[2]*vec_AB[2]) / dot_AB;
        osg::Vec3 M = A + vec_AB * t;

        osg::Vec3 P_symmetric = M * 2.0f - P;

        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = P_symmetric;
    }

    // Curvature on the higher-right corner.
    // Take the segment 3-1 and rotate
    const vfc::float32_t l_angLow = osg::DegreesToRadians(90.0f) / static_cast<vfc::float32_t>(NUMBER_CURVE_VERTICES + 1);
    const osg::Vec3      l_startVecLow = (*f_vertices)[l_startIndex + 1] - (*f_vertices)[l_startIndex + 3]; // segment 3-1
    const osg::Vec3      l_centerLow   = (*f_vertices)[l_startIndex + 3]; // rotate over 3
    for (vfc::int32_t i = 1; i <= NUMBER_CURVE_VERTICES; ++i)
    {
        const osg::Vec3 l_rot = l_startVecLow * osg::Matrix::rotate(-l_angLow * i, osg::Z_AXIS);
        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES + i - 1] = l_centerLow + l_rot;
    }
}

void DynWheelMaskGeode::updateLeftVertices(osg::Vec3Array* f_vertices)
{

    // The left geometry has the following shape
    //
    //    /- 0 --- 1
    //  /    |   / |
    // |     | /   |
    // 2 --- 3 --- 4
    // |        /  |
    // |     /     |
    // |  /        |
    // 5 --- 6 --- 7
    // |     |   / |
    //  \    | /   |
    //    \- 8 --- 9

    // Width and length of shape
    const vfc::float32_t l_width = vfc::abs(m_left.m_yCurrent - m_left.m_y);
    const vfc::float32_t l_R     = l_width / 2.0f;

    // Left geometry takes second half of the array
    const vfc::int32_t l_startIndex = NUMBER_CURVE_VERTICES*2 + NUMBER_BASIC_VERTICES;

    /// @brief Vertices z coordinate.
    const vfc::float32_t l_ground = 0.0f;

    // Upper row of points - 0, 1
    const auto l_leftTop = osg::Vec3(m_xMax, m_left.m_yCurrent, l_ground);
    (*f_vertices)[l_startIndex + 0] = osg::Vec3(m_xMax, m_left.m_yCurrent - l_R, l_ground);
    (*f_vertices)[l_startIndex + 1] = osg::Vec3(m_xMax, m_left.m_y, l_ground);

    // Middle higher row of points - 2, 3, 4
    const osg::Vec3 l_offset1( - l_R, 0.0f, l_ground);
    (*f_vertices)[l_startIndex + 2] = l_leftTop + l_offset1;
    (*f_vertices)[l_startIndex + 3] = (*f_vertices)[l_startIndex + 0] + l_offset1;
    (*f_vertices)[l_startIndex + 4] = (*f_vertices)[l_startIndex + 1] + l_offset1;

    // Middle lower row of points - 5, 6, 7
    const osg::Vec3 l_offset2(m_xMin - m_xMax + l_R, 0.0f, l_ground);
    (*f_vertices)[l_startIndex + 5] = l_leftTop + l_offset2;
    (*f_vertices)[l_startIndex + 6] = (*f_vertices)[l_startIndex + 0] + l_offset2;
    (*f_vertices)[l_startIndex + 7] = (*f_vertices)[l_startIndex + 1] + l_offset2;

    // Lower row of points - 8, 9
    const osg::Vec3 l_offset3(m_xMin - m_xMax, 0.0f, l_ground);
    (*f_vertices)[l_startIndex + 8] = (*f_vertices)[l_startIndex + 0] + l_offset3;
    (*f_vertices)[l_startIndex + 9] = (*f_vertices)[l_startIndex + 1] + l_offset3;

    // Curvature on the lower-left corner.
    // Take the segment 8-6 and rotate
    const vfc::float32_t l_ang = osg::DegreesToRadians(90.0f) / static_cast<vfc::float32_t>(NUMBER_CURVE_VERTICES + 1);
    const osg::Vec3      l_startVec = (*f_vertices)[l_startIndex + 8] - (*f_vertices)[l_startIndex + 6]; // segment 8-6
    const osg::Vec3      l_center   = (*f_vertices)[l_startIndex + 6]; // rotate over 6

    for (vfc::int32_t i = 4; i <= NUMBER_CURVE_VERTICES; ++i)
    {
        const osg::Vec3 l_rot = l_startVec * osg::Matrix::rotate(-l_ang * i, osg::Z_AXIS);
        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = l_center + l_rot;
    }
    // const osg::Vec3      l_mid      = (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + 3];
    // for (vfc::int32_t i = 1; i <= 3; ++i)
    // {
    //     const osg::Vec3 l_rot = l_startVec * osg::Matrix::rotate(-l_ang * i, osg::Z_AXIS);
    //     (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = l_center + l_rot;
    // }
    const osg::Vec3 l_mid = (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + 3];

    osg::Vec3 A = (*f_vertices)[l_startIndex + 8];
    osg::Vec3 B = l_mid;
    osg::Vec3 vec_AB = B - A;

    float dot_AB = vec_AB[0]*vec_AB[0] + vec_AB[1]*vec_AB[1] + vec_AB[2]*vec_AB[2];

    for (vfc::int32_t i = 1; i <= 3; ++i)
    {
        const osg::Vec3 l_rot = l_startVec * osg::Matrix::rotate(-l_ang * i, osg::Z_AXIS);
        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = l_center + l_rot;
    }
    for (vfc::int32_t i = 1; i <= 3; ++i)
    {
        osg::Vec3 P = (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1];

        osg::Vec3 vec_AP = P - A;
        float t = (vec_AP[0]*vec_AB[0] + vec_AP[1]*vec_AB[1] + vec_AP[2]*vec_AB[2]) / dot_AB;
        osg::Vec3 M = A + vec_AB * t;

        osg::Vec3 P_symmetric = M * 2.0f - P;

        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + i - 1] = P_symmetric;
    }

    // Curvature on the higher-left corner.
    // Take the segment 2-3 and rotate
    const vfc::float32_t l_angTop = osg::DegreesToRadians(90.0f) / static_cast<vfc::float32_t>(NUMBER_CURVE_VERTICES + 1);
    const osg::Vec3      l_startVecTop = (*f_vertices)[l_startIndex + 2] - (*f_vertices)[l_startIndex + 3]; // segment 2-3
    const osg::Vec3      l_centerTop   = (*f_vertices)[l_startIndex + 3]; // rotate over 3
    for (vfc::int32_t i = 1; i <= NUMBER_CURVE_VERTICES; ++i)
    {
        const osg::Vec3 l_rot = l_startVecTop * osg::Matrix::rotate(-l_angTop * i, osg::Z_AXIS);
        (*f_vertices)[l_startIndex + NUMBER_BASIC_VERTICES + NUMBER_CURVE_VERTICES + i - 1] = l_centerTop + l_rot;
    }
}

vfc::float32_t DynWheelMaskGeode::getSteeringAngle()
{
    const auto l_steeringFront = m_framework->m_steeringAngleFrontReceiver.getData();
    if (nullptr != l_steeringFront)
    {
        vfc::CSI::si_radian_f32_t l_angleData = l_steeringFront->m_Data;
        return osg::RadiansToDegrees(l_angleData.value());
    }
    return 0.0f;
}

void DynWheelMaskGeode::updateMembersFromSettings()
{
    m_xMin = g_dynamicWheelMaskSettings->m_min_x;
    m_xMax = g_dynamicWheelMaskSettings->m_max_x;

    // LEFT -------------------------------------------------------
    m_left.m_y      = g_dynamicWheelMaskSettings->m_min_y;
    m_left.m_ySmall = g_dynamicWheelMaskSettings->m_width_small;
    m_left.m_yBig   = g_dynamicWheelMaskSettings->m_width_big;

    // RIGHT ------------------------------------------------------
    m_right.m_y      = -g_dynamicWheelMaskSettings->m_min_y;
    m_right.m_ySmall = -g_dynamicWheelMaskSettings->m_width_small;
    m_right.m_yBig   = -g_dynamicWheelMaskSettings->m_width_big;
}

void DynWheelMaskGeode::updateTargetShape()
{
    const auto l_wheelAngleDeg = getSteeringAngle();
    const auto l_lowThresDeg   = g_dynamicWheelMaskSettings->m_low_thres_deg;
    const auto l_highThresDeg  = g_dynamicWheelMaskSettings->m_high_thres_deg;

    switch (m_currentShape)
    {
    case EShape::INIT:
    {
        // Jump to small shape without animation
        m_left.m_yTarget  = m_left.m_ySmall;
        m_right.m_yTarget = m_right.m_ySmall;

        m_left.m_yCurrent  = m_left.m_yTarget;
        m_right.m_yCurrent = m_right.m_yTarget;
        m_currentShape     = EShape::SMALL_SHAPE;
        m_updateRequired   = true;
        break;
    }
    case EShape::SMALL_SHAPE:
    {
        // Previous shape: SMALL
        if ((l_wheelAngleDeg > l_highThresDeg || l_wheelAngleDeg < -l_highThresDeg) ||
            (vfc::isEqual(l_highThresDeg, 0.0f)))
        {
            // Animate to BIG
            m_left.m_yTarget  = m_left.m_yBig;
            m_right.m_yTarget = m_right.m_yBig;
            m_currentShape    = EShape::BIG_SHAPE;
            m_updateRequired  = true;
        }
        // No shape change, just apply Y-Offset update (if there was any)
        else if (m_left.m_yTarget != m_left.m_ySmall || m_right.m_yTarget != m_right.m_ySmall)
        {
            // Animate Y-Offset change
            m_left.m_yTarget  = m_left.m_ySmall;
            m_right.m_yTarget = m_right.m_ySmall;
            m_updateRequired  = true;
        }
        else{}
        break;
    }
    case EShape::BIG_SHAPE:
    {
        // Previous shape: BIG
        if ((l_wheelAngleDeg > -l_lowThresDeg && l_wheelAngleDeg < l_lowThresDeg) ||
            (vfc::notEqual(l_highThresDeg, 0.0f) && vfc::isEqual(l_wheelAngleDeg, 0.0f)))
        {
            // Animate to SMALL
            m_left.m_yTarget  = m_left.m_ySmall;
            m_right.m_yTarget = m_right.m_ySmall;
            m_currentShape    = EShape::SMALL_SHAPE;
            m_updateRequired  = true;
        }
        // No shape change, just apply Y-Offset update (if there was any)
        else if (m_left.m_yTarget != m_left.m_yBig || m_right.m_yTarget != m_right.m_yBig)
        {
            // Animate Y-Offset change
            m_left.m_yTarget  = m_left.m_yBig;
            m_right.m_yTarget = m_right.m_yBig;
            m_updateRequired  = true;
        }
        else{}
        break;
    }
    default:
    {
        break; // invalid
    }
    }
}

void DynWheelMaskGeode::animateVerticesBasic()
{
    // Update target borders for left and right geometries
    m_left.m_yCurrent  = m_left.m_yTarget * m_animationSpeed + m_left.m_yCurrent * (1.0f - m_animationSpeed);
    m_right.m_yCurrent = m_right.m_yTarget * m_animationSpeed + m_right.m_yCurrent * (1.0f - m_animationSpeed);

    // If animation is near the target position, jump to it
    if (vfc::abs(m_left.m_yCurrent - m_left.m_yTarget) < l_targetReachedTh)
    {
        m_left.m_yCurrent = m_left.m_yTarget;
    }
    if (vfc::abs(m_right.m_yCurrent - m_right.m_yTarget) < l_targetReachedTh)
    {
        m_right.m_yCurrent = m_right.m_yTarget;
    }
}

DynWheelMaskCallback::DynWheelMaskCallback()
    : osg::NodeCallback()
    , m_stateSet1{}
    , m_stateSet2{}
{
}

void DynWheelMaskCallback::setSingleStateSet(osg::StateSet* f_stateSet)
{
    m_stateSet1 = f_stateSet;
}

void DynWheelMaskCallback::setTwoStateSets(osg::StateSet* f_stateSet1, osg::StateSet* f_stateSet2)
{
    m_stateSet1 = f_stateSet1;
    m_stateSet2 = f_stateSet2;
}

void DynWheelMaskCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
    const auto l_DynWheelMaskAsset = dynamic_cast<DynWheelMaskAsset*>(f_node); // PRQA S 3077 // PRQA S 3400
    if (nullptr != l_DynWheelMaskAsset)
    {
        const auto l_DynMaskGeode =
            dynamic_cast<DynWheelMaskGeode*>(l_DynWheelMaskAsset->getChild(0u)); // PRQA S 3077 // PRQA S 3400
        if (nullptr != l_DynMaskGeode)
        {
            // Cull
            const auto l_cv = dynamic_cast<osgUtil::CullVisitor*>(f_nv); // PRQA S 3077 // PRQA S 3400
            if (nullptr != l_cv)
            {
                if (g_dynamicWheelMaskSettings->m_show)
                {
                    /// Render pass #1
                    /// Usually "noCam" stateSet.
                    if (nullptr != m_stateSet1)
                    {
                        l_cv->pushStateSet(m_stateSet1);
                        traverse(f_node, f_nv);
                        l_cv->popStateSet();
                    }

                    /// TODO: temporary disable depth test here, maybe?

                    /// Render pass #2
                    /// After pass #1 it will draw on top in the same draw call. Usually the buffered image.
                    if (nullptr != m_stateSet2)
                    {
                        l_cv->pushStateSet(m_stateSet2);
                        traverse(f_node, f_nv);
                        l_cv->popStateSet();
                    }
                }
            }

            // Update
            const auto l_uv = dynamic_cast<osgUtil::UpdateVisitor*>(f_nv); // PRQA S 3077 // PRQA S 3400
            if (nullptr != l_uv)
            {
                l_DynMaskGeode->update();
            }
        }
    }
}

} // namespace assets
} // namespace cc