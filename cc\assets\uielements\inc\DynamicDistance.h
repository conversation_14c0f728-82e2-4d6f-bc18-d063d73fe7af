//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: TANG Chencheng (XC-DX/EPF2)
//  Department: XC-DX/EPF2
//=============================================================================
/// @swcomponent SVS DENSA
/// @file  DynamicDistance.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_DYNAMIC_DISTANCE_H
#define CC_ASSETS_DYNAMIC_DISTANCE_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/views/engineeringview/inc/EngineeringView.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace uielements
{    


//======================================================
// DynamicDistanceSettings
//------------------------------------------------------
/// Setting class for DynamicDistance
/// <AUTHOR>
//======================================================
class DynamicDistanceSettings : public pc::util::coding::ISerializable
{
public:

  DynamicDistanceSettings()
    : m_position_UIPanel(osg::Vec3(0.0f, 0.0f, 0.0f))
    , m_position_MainView(osg::Vec3(0.0f, 0.0f, 0.0f))
    , m_fontType("cc/resources/Roboto-Regular.ttf")
    , m_color(osg::Vec4f(0.756f, 0.780f, 0.816f, 1.0f))
    , m_charSize(30.0f)
    , m_FontResolutionWidth(64)
    , m_FontResolutionHeight(64)
  {
  }

  SERIALIZABLE(DynamicDistanceSettings)
  {
    ADD_MEMBER(osg::Vec3f, position_UIPanel);
    ADD_MEMBER(osg::Vec3f, position_MainView);
    ADD_STRING_MEMBER(fontType);
    ADD_MEMBER(osg::Vec4f, color);
    ADD_FLOAT_MEMBER(charSize);
    ADD_UINT32_MEMBER(FontResolutionWidth);
    ADD_UINT32_MEMBER(FontResolutionHeight);
  }
  osg::Vec3 m_position_UIPanel;
  osg::Vec3 m_position_MainView;
  std::string m_fontType;
  osg::Vec4f m_color;
  float m_charSize;
  unsigned int m_FontResolutionWidth;
  unsigned int m_FontResolutionHeight;

};

extern pc::util::coding::Item<DynamicDistanceSettings> g_dynamicDistanceSettings;

class DynamicDistanceCallback: public osg::NodeCallback
{

public:
  DynamicDistanceCallback(
                                osg::ref_ptr<osg::Geode> f_DynamicDisGeode, 
                                pc::core::Framework* f_pFramework
                                );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;
  
  void updateDistance(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);

protected:
  virtual ~DynamicDistanceCallback();

private:

  //! Copy constructor is not permitted.
  DynamicDistanceCallback (const DynamicDistanceCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  DynamicDistanceCallback& operator=(const DynamicDistanceCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_DynamicDisGeode;
  bool isUIPanel;
  pc::core::Framework* m_pFramework;
};

//!
//! DynamicDistance
//!
class   DynamicDistance : public osg::MatrixTransform
{
public:

    DynamicDistance(pc::core::Framework* f_framework);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    virtual void init();

    virtual ~DynamicDistance();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_DynamicDisGeode;

private:
    //! Copy constructor is not permitted.
    DynamicDistance (const DynamicDistance& other); // = delete
    //! Copy assignment operator is not permitted.
    DynamicDistance& operator=(const DynamicDistance& other); // = delete

};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_DYNAMIC_DISTANCE_H
