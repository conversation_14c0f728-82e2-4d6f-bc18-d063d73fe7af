//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SingleCam.cpp
/// @brief
//=============================================================================

#include "cc/assets/common/inc/SingleCam.h"
#include "pc/svs/factory/inc/RenderManager.h"
#include "pc/svs/factory/inc/SV3DCullCallback.h"
#include "pc/svs/factory/inc/SV3DNode.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060

#include "osgUtil/CullVisitor"

using pc::util::logging::g_EngineContext;

namespace cc
{
namespace assets
{
namespace common
{

//!
//! SingleCamCullCallback
//!
class SingleCamCullCallback : public pc::factory::SV3DCullCallback // PRQA S 2119
{
public:

  SingleCamCullCallback()
    : osg::Object{}
    , pc::factory::SV3DCullCallback()
    , m_camArea{pc::factory::SINGLE_CAM_FRONT}
    , m_shadingStyle{pc::factory::SV3DNode::DEFAULT_SHADING}
  {
  }

  SingleCamCullCallback(pc::factory::SingleCamArea f_area, vfc::uint32_t f_shadingStyle)
    : m_camArea{f_area}
    , m_shadingStyle{f_shadingStyle}
  {
  }

  SingleCamCullCallback(const SingleCamCullCallback& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY)
    : osg::Object{f_other, f_copyOp}
    , pc::factory::SV3DCullCallback(f_other, f_copyOp)
    , m_camArea{f_other.m_camArea}
    , m_shadingStyle{f_other.m_shadingStyle}
  {
  }

  META_Object(cc::assets::common, SingleCamCullCallback); // PRQA S 3077 

  osg::StateSet* getStateSet(
    pc::factory::SV3DGeometry* /* f_geometry */,
    const pc::factory::SV3DStateGraph& /* f_stateGraph */) const override
  {
    return nullptr;
  }

  void operator () (osg::Node* f_node, osg::NodeVisitor* f_nv) override
  {
    if ((f_node == nullptr) || (f_nv == nullptr))
    {
        return;
    }
//     using namespace pc::factory;
    osg::Group* const l_singleCamNode = f_node->asGroup();
    if ((l_singleCamNode != nullptr) && (osg::NodeVisitor::CULL_VISITOR == f_nv->getVisitorType()))
    {
      osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (f_nv);
      pc::factory::RenderManager* const l_renderManager = pc::factory::RenderManager::get(l_cv->getCurrentCamera());
      if (l_renderManager != nullptr)
      {
        const pc::factory::SV3DStateGraph& l_stateGraph = l_renderManager->getStateGraph(m_shadingStyle);

        //! common StateSet
        vfc::uint32_t l_numPushedStateSets = 0u;
        osg::StateSet* l_commonStateSet = l_singleCamNode->getStateSet();
        if (l_commonStateSet == nullptr)
        {
          l_commonStateSet = l_stateGraph.m_common.get();
        }
        else // (l_commonStateSet != nullptr)
        {
          l_cv->pushStateSet(l_commonStateSet);
          ++l_numPushedStateSets;
        }

        //! single cam StateSet
        osg::StateSet* const l_singleCamStateSet = l_stateGraph.m_singleCam[m_camArea].get();
        if (l_singleCamStateSet != nullptr)
        {
          l_cv->pushStateSet(l_singleCamStateSet);
          ++l_numPushedStateSets;
        }

        const vfc::uint32_t l_numChildren = l_singleCamNode->getNumChildren();
        for (vfc::uint32_t i = 0u; i < l_numChildren; ++i)
        {
          pc::factory::SV3DGeode* const l_geode = dynamic_cast<pc::factory::SV3DGeode*> (l_singleCamNode->getChild(i)); // PRQA S 3077  // PRQA S 3400
          if (l_geode != nullptr)
          {
            handleGeode(l_geode, l_cv, l_stateGraph);
          }
        }

        for (vfc::uint32_t i = 0u; i < l_numPushedStateSets; ++i)
        {
          l_cv->popStateSet();
        }
      }
      else
      {
        traverse(f_node, f_nv);
      }
    }
    else
    {
      traverse(f_node, f_nv);
    }
  }

protected:

  ~SingleCamCullCallback() override = default;
  //! Copy constructor is not permitted.
  SingleCamCullCallback (const SingleCamCullCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  SingleCamCullCallback& operator=(const SingleCamCullCallback& other); // = delete // PRQA S 2051

private:

  pc::factory::SingleCamArea m_camArea;
  vfc::uint32_t m_shadingStyle;

};


//!
//! SingleCamNode
//!
SingleCamNode::SingleCamNode(pc::factory::SV3DNode* f_sv3dNode, pc::factory::SingleCamArea f_area)
  : m_sv3dNode{f_sv3dNode}
  , m_lastVertexArrayUpdate{0u}
{
//   using namespace pc::factory;
  setNumChildrenRequiringUpdateTraversal(1u);
  addCullCallback(new SingleCamCullCallback(f_area, m_sv3dNode->getShadingStyle()));
  const pc::factory::SV3DGeode* const l_singleCam = m_sv3dNode->getSingleCamGeode();
  const pc::factory::SV3DGeode* const l_twoCam = m_sv3dNode->getTwoCamGeode();
  pc::factory::SV3DGeode* const l_geode = new pc::factory::SV3DGeode;
  osg::Group::addChild(l_geode);  // PRQA S 3803

  std::vector<osg::Geometry*> l_geometries;
  switch (f_area)
  {
    case pc::factory::SINGLE_CAM_FRONT:
    {
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_FRONT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_FRONT_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_FRONT_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
    } break;
    case pc::factory::SINGLE_CAM_RIGHT:
    {
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_FRONT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_REAR)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_FRONT_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_REAR_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
    } break;
    case pc::factory::SINGLE_CAM_REAR:
    {
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_REAR)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_REAR_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_REAR_RIGHT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
    } break;
    case pc::factory::SINGLE_CAM_LEFT:
    {
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_REAR)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_singleCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::SINGLE_CAM_FRONT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_REAR_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
      l_geometries.push_back(new pc::factory::SV3DGeometry(*l_twoCam->getGeometry(static_cast<vfc::uint32_t>(pc::factory::TWO_CAM_FRONT_LEFT)), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
    } break;

    default:
      {break;}
  }

  const pc::factory::NoCamGeode* const l_noCam = m_sv3dNode->getNoCamGeode();
  l_geometries.push_back(new pc::factory::SV3DGeometry(*l_noCam->getGeometry(), osg::CopyOp::SHALLOW_COPY));    // PRQA S 3143
  //! overwrite tex coord binding and add to geode
  osg::Array* const l_texCoord = l_geometries[0u]->getTexCoordArray(0u);
  for (auto l_itr = l_geometries.begin(); // PRQA S 4297 // PRQA S 4687
       l_itr != l_geometries.end();
       ++l_itr)
  {
    osg::Geometry* const l_geometry = *l_itr;
    l_geometry->setTexCoordArray(0u, l_texCoord);
    l_geode->addDrawable(l_geometry);  // PRQA S 3803
  }
}


SingleCamNode::~SingleCamNode() = default;


void SingleCamNode::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    //! SV3D nodes are typically updated if the sat cam calib or mask changed.
    //! However, this will only happen if the corresponding SV3D node is active in the current scene graph.
    //! To ensure a correct update behavior we manually forward the update visitor to the related SV3D node.
    m_sv3dNode->accept(f_nv);

    if (m_sv3dNode->getVertexArray()->getModifiedCount() != m_lastVertexArrayUpdate)
    {
      //! since we're using a shared/external vertex array, the bounding spheres of the drawables of this node
      //! might not be up-to-date any more, if the vertex array of the associated SV3D node has been modified
      osg::Geode* const l_geode = getChild(0u)->asGeode();
      const vfc::uint32_t l_numDrawables = l_geode->getNumDrawables();
      for (vfc::uint32_t i = 0u; i < l_numDrawables; ++i)
      {
        l_geode->getDrawable(i)->dirtyBound();
      }
      m_lastVertexArrayUpdate = m_sv3dNode->getVertexArray()->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}

//!
//! SingleCam
//!
SingleCam::SingleCam(cc::core::AssetId f_assetId,
  pc::factory::SV3DNode* f_pBowl,
  pc::factory::SV3DNode* f_pFloor,
  pc::factory::SingleCamArea f_cam)
  : Asset{f_assetId}
{
  osg::Group::addChild(new SingleCamNode(f_pBowl, f_cam));  // PRQA S 3803
  osg::Group::addChild(new SingleCamNode(f_pFloor, f_cam));  // PRQA S 3803
}


SingleCam::~SingleCam() = default;

} // namespace common
} // namespace assets
} // namespace cc

