#===============================================================================
# Copyright (c) 2017 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

include hw/build/module_head.mk

SOURCEFILES = \
ActionPoint.cpp \
CoverPlate.cpp \
DL1.cpp \
ExpModeTrajectory.cpp \
ExtraOutermostLine.cpp \
Frame.cpp \
GeneralTrajectoryLine.cpp \
Helper.cpp \
MainLogic.cpp \
OutermostLine.cpp \
OutermostLineColorful.cpp\
ParkingCallback.cpp \
RefLine.cpp \
TrajectoryAssets.cpp \
WheelTrack.cpp\
TrailerAssistLine.cpp

BINARY = object

include hw/build/$(COMPILER_NAME)/module_tail.mk