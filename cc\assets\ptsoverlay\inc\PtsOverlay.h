//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PTSOVERLAY_PTSOVERLAY_H
#define CC_ASSETS_PTSOVERLAY_PTSOVERLAY_H

#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "pc/svs/core/inc/AsyncNode.h"
#include <osg/MatrixTransform>
#include <array>


//! forward declarations
namespace osgUtil
{
class CullVisitor;
} // namespace osgUtil

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace ptsoverlay
{

const char* getPtsHmiStateName(unsigned int f_state);

bool isHidden(const cc::daddy::PtsHmiStateOutput& f_ptsHmiState);


//!
//! PtsOverlayComposite
//!
class PtsOverlayComposite : public osg::MatrixTransform
{
public:

  enum Component
  {
    SHADOW,
    SPLINE_2D,
    SHIELD_3D,
    NUM_COMPONENTS
  };

  PtsOverlayComposite(pc::core::Framework* f_framework = nullptr);
  PtsOverlayComposite(const PtsOverlayComposite& f_other, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);

  META_Node(cc::assets::ptsoverlay, PtsOverlayComposite);

  osg::Node* getComponent(Component f_id);

  void dirty()
  {
    m_updateRequired = true;
  }

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

  virtual ~PtsOverlayComposite() = default;

private:

  PtsOverlayComposite& operator = (const PtsOverlayComposite&) = delete;

  void update();

  pc::core::Framework* m_framework;
  std::array<unsigned int, NUM_COMPONENTS> m_componentIds;
  bool m_updateRequired;
  int  m_renderBinOrder;
};


//!
//! PtsOverlay
//!
class PtsOverlay : public pc::core::AsyncNode
{
public:

  PtsOverlay(pc::core::Framework* f_framework, pc::vehicle::AbstractZoneLayout* f_zoneLayout);

  void setTransitionAnimationActive(bool f_transitionAnimation)
  {
    m_transitionAnimation = f_transitionAnimation;
  }

  bool isTransitionAnimationActive() const
  {
    return m_transitionAnimation;
  }

  void setIs3D(bool f_is3D)
  {
    m_is3D = f_is3D;
  }

  bool getIs3D() const
  {
    return m_is3D;
  }

protected:

  virtual ~PtsOverlay() = default;

private:

  bool m_transitionAnimation;
  bool m_is3D;

};


//!
//! PtsMatrixTransform
//!
class PtsMatrixTransform : public osg::MatrixTransform
{
public:

  explicit PtsMatrixTransform(pc::core::Framework* f_framework);

protected:

  virtual ~PtsMatrixTransform() = default;

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  void update();

  osg::Vec4f determinePtsPosition() const;

  pc::core::Framework* m_framework;
};


//!
//! PtsParkViewCullCallback
//!
class PtsParkViewCullCallback : public osg::NodeCallback
{
public:

  explicit PtsParkViewCullCallback(pc::core::Framework* f_framework);

  virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override;

  void addAffectedCamera(const osg::Camera* f_camera);

  bool isPtsVisible() const;

protected:

  typedef std::set<const osg::Camera*> CameraList;

  virtual ~PtsParkViewCullCallback() = default;

  virtual void cull(PtsOverlayComposite* f_pts, osgUtil::CullVisitor* f_cv);

  pc::core::Framework* m_framework;
  CameraList m_affectedCameras;
};


} // namespace ptsoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTSOVERLAY_PTSOVERLAY_H