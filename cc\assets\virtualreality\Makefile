#===============================================================================
# Copyright (c) 2017 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

include hw/build/module_head.mk

SOURCEFILES = \
VirtualRealityObject.cpp \
VirtualEgoVehicle.cpp \
LowpolyVehicle.cpp \
LowpolyPedestrian.cpp \
VirtualParkSlot.cpp \
VirtualRealityFactory.cpp \
VirtualRealityManager.cpp \
VirtualRealityUtil.cpp \
VirtualRealityCommand.cpp \
VirtualRealityDataHandler.cpp

BINARY = object

include hw/build/$(COMPILER_NAME)/module_tail.mk
