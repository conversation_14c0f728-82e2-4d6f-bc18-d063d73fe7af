//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)

#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
// #include "cc/util/logging/inc/LoggingContexts.h"
// #include "pc/generic/coding/CodingManager.h"
#include "cc/imgui/inc/imgui_internal.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/imgui/inc/imgui_view.h"
#include "cc/mod/inc/ModTypes.hpp"
#include "pc/svs/daddy/inc/BaseDaddyTypes.h"
#define SEND_PORT(port, data)                                                                                          \
    {                                                                                                                  \
        auto& container  = port.reserve();                                                                             \
        container.m_Data = (data);                                                                                     \
        port.deliver();                                                                                                \
    }

namespace cc
{
namespace views
{
namespace imgui
{

static int ConvertFromOSGKey(int key)
{
    using KEY = osgGA::GUIEventAdapter::KeySymbol;
    switch (key)
    {
    case KEY::KEY_Tab:
        return ImGuiKey_Tab;
    case KEY::KEY_Left:
        return ImGuiKey_LeftArrow;
    case KEY::KEY_Right:
        return ImGuiKey_RightArrow;
    case KEY::KEY_Up:
        return ImGuiKey_UpArrow;
    case KEY::KEY_Down:
        return ImGuiKey_DownArrow;
    case KEY::KEY_Page_Up:
        return ImGuiKey_PageUp;
    case KEY::KEY_Page_Down:
        return ImGuiKey_PageDown;
    case KEY::KEY_Home:
        return ImGuiKey_Home;
    case KEY::KEY_End:
        return ImGuiKey_End;
    case KEY::KEY_Delete:
        return ImGuiKey_Delete;
    case KEY::KEY_BackSpace:
        return ImGuiKey_Backspace;
    case KEY::KEY_Return:
        return ImGuiKey_Enter;
    case KEY::KEY_Escape:
        return ImGuiKey_Escape;
    default:
        return -1;
    }
}

//!
//! ImGuiNewFrameCallback
//!
class ImGuiView::ImGuiNewFrameCallback : public osg::Camera::DrawCallback
{
public:
    ImGuiNewFrameCallback(ImGuiView& handler)
        : m_handler(handler)
    {
    }
    void operator()(osg::RenderInfo& renderInfo) const override
    {
        m_handler.newFrame(renderInfo);
    }

private:
    ImGuiView& m_handler;
};

//!
//! ImGuiRenderCallback
//!
class ImGuiView::ImGuiRenderCallback : public osg::Camera::DrawCallback
{
public:
    ImGuiRenderCallback(ImGuiView& handler)
        : m_handler(handler)
    {
    }
    void operator()(osg::RenderInfo& renderInfo) const override
    {
        m_handler.render(renderInfo);
    }

private:
    ImGuiView& m_handler;
};

//!
//! ImGuiInitOperation
//!
class ImGuiView::ImGuiInitOperation : public osg::Operation
{
public:
    ImGuiInitOperation()
        : osg::Operation("ImGuiInitOperation", false)
        , m_initialized(false)
    {
    }

    void operator()(osg::Object* object) override
    {
        osg::GraphicsContext* context = dynamic_cast<osg::GraphicsContext*>(object);
        if (!context)
            return;
        if (!m_initialized)
        {
            if (!ImGui_ImplOpenGL3_Init("#version 100"))
            {
                std::cout << "ImGui_ImplOpenGL3_Init() Failed\n";
            }
            m_initialized = true;
        }
    }

private:
    bool m_initialized;
};

//!
//! ImGuiView
//!
ImGuiView::ImGuiView(
    const std::string&                f_name,
    const pc::core::Viewport&         f_viewport,
    const pc::virtcam::VirtualCamera& f_camPos,
    pc::core::Framework*              f_pFramework)
    : pc::core::View(f_name, f_viewport, f_camPos)
    , m_time(0.0f)
    , m_mousePressed{false}
    , m_mouseWheel(0.0f)
    , m_initialized(false)
    , m_pFramework(f_pFramework)
{
    assert(m_pFramework);
    m_pFramework->getView()->addEventHandler(this);

    // work arround for imgui threading issue
    osgViewer::ViewerBase* l_viewer = m_pFramework->getViewerBase();
    l_viewer->setThreadingModel(osgViewer::ViewerBase::SingleThreaded);

    initImgui();

    // add draw callbacks
    setPreDrawCallback(new ImGuiNewFrameCallback(*this));
    setPostDrawCallback(new ImGuiRenderCallback(*this));

    // ImGui render order should be the last
    setRenderOrder(osg::Camera::POST_RENDER, std::numeric_limits<int>::max() - 1);
    l_viewer->setRealizeOperation(new ImGuiInitOperation);
    l_viewer->setReleaseContextAtEndOfFrameHint(false);
    l_viewer->realize();
}

void ImGuiView::initImgui()
{
    // init imgui
    IMGUI_CHECKVERSION();
    ImGui::CreateContext();
    ImPlot::CreateContext();
    ImGuiIO& io    = ImGui::GetIO();
    io.DisplaySize = ImVec2(
        cc::core::g_views->m_usableCanvasViewport.m_size.x(), cc::core::g_views->m_usableCanvasViewport.m_size.y());
    io.ConfigFlags |= ImGuiConfigFlags_DockingEnable; // Enable Docking
    io.Fonts->AddFontFromFileTTF("cc/resources/Roboto-Regular.ttf", 18.0f);
    io.IniFilename        = "cc/resources_sil/imgui.ini";
    io.KeyMap[ImGuiKey_A] = osgGA::GUIEventAdapter::KeySymbol::KEY_A;
    io.KeyMap[ImGuiKey_C] = osgGA::GUIEventAdapter::KeySymbol::KEY_C;
    io.KeyMap[ImGuiKey_V] = osgGA::GUIEventAdapter::KeySymbol::KEY_V;
    io.KeyMap[ImGuiKey_X] = osgGA::GUIEventAdapter::KeySymbol::KEY_X;
    io.KeyMap[ImGuiKey_Y] = osgGA::GUIEventAdapter::KeySymbol::KEY_Y;
    io.KeyMap[ImGuiKey_Z] = osgGA::GUIEventAdapter::KeySymbol::KEY_Z;

    m_initialized = true;
}

void ImGuiView::newFrame()
{
    static osg::Timer_t startTick = osg::Timer::instance()->getStartTick();
    ImGui_ImplOpenGL3_NewFrame();
    ImGuiIO& io    = ImGui::GetIO();
    io.DisplaySize = ImVec2(
        cc::core::g_views->m_usableCanvasViewport.m_size.x(), cc::core::g_views->m_usableCanvasViewport.m_size.y());
    osg::Timer_t currentTick = osg::Timer::instance()->tick();
    io.DeltaTime             = osg::Timer::instance()->delta_s(startTick, currentTick) + 0.0000001;
    io.MousePos              = m_mousePos;
    for (int i = 0; i < 3; i++)
    {
        io.MouseDown[i] = m_mousePressed[i];
    }
    io.MouseWheel = m_mouseWheel;
    m_mouseWheel  = 0.0f;
    ImGui::NewFrame();
    m_newFrame = true;
}

void ImGuiView::render()
{
    if (!m_newFrame)
    {
        return;
    }
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());
    m_newFrame = false;
}

void ImGuiView::newFrame(osg::RenderInfo& renderInfo)
{
}

void ImGuiView::renderDockspace()
{
    static bool               dockspace_open  = true;
    static bool               opt_fullscreen  = true;
    static bool               opt_padding     = false;
    static ImGuiDockNodeFlags dockspace_flags = ImGuiDockNodeFlags_PassthruCentralNode;

    // We are using the ImGuiWindowFlags_NoDocking flag to make the parent window
    // not dockable into, because it would be confusing to have two docking
    // targets within each others.
    ImGuiWindowFlags window_flags = ImGuiWindowFlags_NoDocking;
    if (getNodeMask() != 0u && false)
    {
        window_flags |= ImGuiWindowFlags_MenuBar;
    }
    if (opt_fullscreen)
    {
        const ImGuiViewport* viewport = ImGui::GetMainViewport();
        ImGui::SetNextWindowPos(viewport->WorkPos);
        ImGui::SetNextWindowSize(viewport->WorkSize);
        ImGui::SetNextWindowViewport(viewport->ID);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowRounding, 0.0f);
        ImGui::PushStyleVar(ImGuiStyleVar_WindowBorderSize, 0.0f);
        window_flags |= ImGuiWindowFlags_NoTitleBar | ImGuiWindowFlags_NoCollapse | ImGuiWindowFlags_NoResize |
                        ImGuiWindowFlags_NoMove;
        window_flags |= ImGuiWindowFlags_NoBringToFrontOnFocus | ImGuiWindowFlags_NoNavFocus;
    }
    else
    {
        dockspace_flags &= ~ImGuiDockNodeFlags_PassthruCentralNode;
    }

    // When using ImGuiDockNodeFlags_PassthruCentralNode, DockSpace() will render
    // our background and handle the pass-thru hole, so we ask Begin() to not
    // render a background.
    if (dockspace_flags & ImGuiDockNodeFlags_PassthruCentralNode)
        window_flags |= ImGuiWindowFlags_NoBackground;

    // Important: note that we proceed even if Begin() returns false (aka window
    // is collapsed). This is because we want to keep our DockSpace() active. If a
    // DockSpace() is inactive, all active windows docked into it will lose their
    // parent and become undocked. We cannot preserve the docking relationship
    // between an active window and an inactive docking, otherwise any change of
    // dockspace/settings would lead to windows being stuck in limbo and never
    // being visible.
    if (!opt_padding)
        ImGui::PushStyleVar(ImGuiStyleVar_WindowPadding, ImVec2(0.0f, 0.0f));
    ImGui::Begin("DockSpace Demo", &dockspace_open, window_flags);
    if (!opt_padding)
        ImGui::PopStyleVar();

    if (opt_fullscreen)
        ImGui::PopStyleVar(2);

    // Submit the DockSpace
    ImGuiIO& io = ImGui::GetIO();
    if (io.ConfigFlags & ImGuiConfigFlags_DockingEnable)
    {
        ImGuiID dockspace_id = ImGui::GetID("MyDockSpace");
        ImGui::DockSpace(dockspace_id, ImVec2(0.0f, 0.0f), dockspace_flags);
    }
    else
    {
        ImGuiIO& io = ImGui::GetIO();
        ImGui::Text("ERROR: Docking is not enabled! See Demo > Configuration.");
        ImGui::Text("Set io.ConfigFlags |= ImGuiConfigFlags_DockingEnable in your code, "
                    "or ");
        ImGui::SameLine(0.0f, 0.0f);
        if (ImGui::SmallButton("click here"))
            io.ConfigFlags |= ImGuiConfigFlags_DockingEnable;
    }
    if (getNodeMask() != 0u && false)
    {
        if (ImGui::BeginMenuBar())
        {
            if (ImGui::BeginMenu("Docking Options"))
            {
                // Disabling fullscreen would allow the window to be moved to the front
                // of other windows, which we can't undo at the moment without finer
                // window depth/z control.
                ImGui::MenuItem("Fullscreen", NULL, &opt_fullscreen);
                ImGui::MenuItem("Padding", NULL, &opt_padding);
                ImGui::Separator();

                if (ImGui::MenuItem(
                        "Flag: NoDockingOverCentralNode",
                        "",
                        (dockspace_flags & ImGuiDockNodeFlags_NoDockingOverCentralNode) != 0))
                {
                    dockspace_flags ^= ImGuiDockNodeFlags_NoDockingOverCentralNode;
                }
                if (ImGui::MenuItem(
                        "Flag: NoDockingSplit", "", (dockspace_flags & ImGuiDockNodeFlags_NoDockingSplit) != 0))
                {
                    dockspace_flags ^= ImGuiDockNodeFlags_NoDockingSplit;
                }
                if (ImGui::MenuItem("Flag: NoUndocking", "", (dockspace_flags & ImGuiDockNodeFlags_NoUndocking) != 0))
                {
                    dockspace_flags ^= ImGuiDockNodeFlags_NoUndocking;
                }
                if (ImGui::MenuItem("Flag: NoResize", "", (dockspace_flags & ImGuiDockNodeFlags_NoResize) != 0))
                {
                    dockspace_flags ^= ImGuiDockNodeFlags_NoResize;
                }
                if (ImGui::MenuItem(
                        "Flag: AutoHideTabBar", "", (dockspace_flags & ImGuiDockNodeFlags_AutoHideTabBar) != 0))
                {
                    dockspace_flags ^= ImGuiDockNodeFlags_AutoHideTabBar;
                }
                if (ImGui::MenuItem(
                        "Flag: PassthruCentralNode",
                        "",
                        (dockspace_flags & ImGuiDockNodeFlags_PassthruCentralNode) != 0,
                        opt_fullscreen))
                {
                    dockspace_flags ^= ImGuiDockNodeFlags_PassthruCentralNode;
                }
                ImGui::Separator();

                ImGui::EndMenu();
            }
            ImGui::EndMenuBar();
        }
    }
    ImGui::End();
}

void ImGuiView::render(osg::RenderInfo& renderInfo)
{
    if (!m_newFrame)
    {
        return;
    }
    static float transparency = 0.5f;
    ImGui::PushStyleColor(ImGuiCol_WindowBg, ImVec4(0.0f, 0.0f, 0.0f, transparency));
    // renderDockspace();
    cc::imgui::ImGuiManager*             l_manager  = cc::imgui::ImGuiManager::getInstance();
    cc::imgui::ImGuiManager::ContextMap* l_contexts = l_manager->getContexts();
    for (auto ltr = l_contexts->begin(); ltr != l_contexts->end(); ++ltr)
    {
        ltr->second->update(renderInfo);
    }
    customCommands();
    SonarSignals();
    MODSignals();
    ImGui::Begin("Settings");
    ImGui::SliderFloat("Transparency", &transparency, 0.0f, 1.0f);
    ImGui::End();
    ImGui::ShowDemoWindow();
    ImPlot::ShowDemoWindow();
    ImGui::PopStyleColor(); // Transparency
    ImGui::Render();
    ImGui_ImplOpenGL3_RenderDrawData(ImGui::GetDrawData());

    m_newFrame = false;
}

bool ImGuiView::handle(const osgGA::GUIEventAdapter& ea, osgGA::GUIActionAdapter& aa)
{
    using KEY                      = osgGA::GUIEventAdapter::KeySymbol;
    ImGuiIO&   io                  = ImGui::GetIO();
    const bool wantCaptureMouse    = io.WantCaptureMouse;
    const bool wantCaptureKeyboard = io.WantCaptureKeyboard;

    IMGUI_LOG("Settings", "m_mousePressed[0]", m_mousePressed[0] ? "TRUE" : "FALSE");
    IMGUI_LOG("Settings", "m_mousePressed[1]", m_mousePressed[1] ? "TRUE" : "FALSE");
    IMGUI_LOG("Settings", "m_mousePressed[2]", m_mousePressed[2] ? "TRUE" : "FALSE");

    IMGUI_LOG("Settings", "MousePos", std::to_string(io.MousePos.x) + " " + std::to_string(io.MousePos.y));
    IMGUI_LOG("Settings", "MousePosPrev", std::to_string(io.MousePosPrev.x) + " " + std::to_string(io.MousePosPrev.y));
    IMGUI_LOG("Settings", "MouseDelta", std::to_string(io.MouseDelta.x) + " " + std::to_string(io.MouseDelta.y));

    //! check whether top view area is clicked or not
    switch (ea.getEventType())
    {
    case osgGA::GUIEventAdapter::KEYDOWN:
    case osgGA::GUIEventAdapter::KEYUP:
    {
        const bool isKeyDown   = ea.getEventType() == osgGA::GUIEventAdapter::KEYDOWN;
        const int  c           = ea.getKey();
        const int  special_key = ConvertFromOSGKey(c);
        if (special_key > 0)
        {
            assert((special_key >= 0 && special_key < 645) && "ImGui KeysMap is an array of 645");

            io.KeysDown[special_key] = isKeyDown;

            io.KeyCtrl  = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_CTRL;
            io.KeyShift = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_SHIFT;
            io.KeyAlt   = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_ALT;
            io.KeySuper = ea.getModKeyMask() & osgGA::GUIEventAdapter::MODKEY_SUPER;
        }
        else if (isKeyDown && c > 0 && c < 0xFF)
        {
            io.AddInputCharacter((unsigned short)c);
        }

        if (ImGuiKey_Tab == special_key && ea.getEventType() == osgGA::GUIEventAdapter::KEYUP)
        {
            auto scene = m_pFramework->getScene();
            if (scene)
            {
                bool enable = scene->getView(cc::core::CustomViews::IMGUI_VIEW)->getNodeMask() != 0u;
                scene->setViewEnabled(cc::core::CustomViews::IMGUI_VIEW, !enable);
            }
        }
        return wantCaptureKeyboard;
    }
    case (osgGA::GUIEventAdapter::RELEASE):
    case (osgGA::GUIEventAdapter::PUSH):
    {
        m_mousePos        = ImVec2(ea.getX(), io.DisplaySize.y - ea.getY());
        m_mousePressed[0] = ea.getButtonMask() & osgGA::GUIEventAdapter::LEFT_MOUSE_BUTTON;
        m_mousePressed[1] = ea.getButtonMask() & osgGA::GUIEventAdapter::RIGHT_MOUSE_BUTTON;
        m_mousePressed[2] = ea.getButtonMask() & osgGA::GUIEventAdapter::MIDDLE_MOUSE_BUTTON;
        return wantCaptureMouse;
    }
    case (osgGA::GUIEventAdapter::DRAG):
    case (osgGA::GUIEventAdapter::MOVE):
    {
        m_mousePos = ImVec2(ea.getX(), io.DisplaySize.y - ea.getY());
        return wantCaptureMouse;
    }
    case (osgGA::GUIEventAdapter::SCROLL):
    {
        m_mouseWheel = ea.getScrollingMotion() == osgGA::GUIEventAdapter::SCROLL_UP ? 1.0 : -1.0;
        return wantCaptureMouse;
    }
    default:
    {
        return false;
    }
    }
    return false;
}

void ImGuiView::customCommands()
{
    using namespace pc::daddy;
    using cc::daddy::CustomDaddyPorts;
    static bool showDemoWindow = true;

    ImGui::Begin("Commands");
    {
        static int s_competeActiveAllow      = 0;
        bool       competeActiveAllowChanged = false;
        competeActiveAllowChanged =
            competeActiveAllowChanged || ImGui::RadioButton("CompeteActiveAllow_NONE", &s_competeActiveAllow, 0);

        competeActiveAllowChanged =
            competeActiveAllowChanged || ImGui::RadioButton("CompeteActiveAllow", &s_competeActiveAllow, 1);

        competeActiveAllowChanged =
            competeActiveAllowChanged || ImGui::RadioButton("CompeteActiveNotAllow", &s_competeActiveAllow, 2);

        if (competeActiveAllowChanged)
        {
            cc::daddy::CompeteActiveAllowDaddy_t& l_competeActiveAllow =
                cc::daddy::CustomDaddyPorts::sm_competeActiveAllow_SenderPort.reserve(); // keep sending
            l_competeActiveAllow.m_Data = (cc::target::common::ECompeteActiveAllow)s_competeActiveAllow;
            cc::daddy::CustomDaddyPorts::sm_competeActiveAllow_SenderPort.deliver();
        }

        static bool s_pressAndroidIcon            = false;
        bool        PressAndroidIconStatusChanged = false;
        PressAndroidIconStatusChanged             = ImGui::Checkbox("PressAndroidIcon", &s_pressAndroidIcon);
        if (PressAndroidIconStatusChanged)
        {
            auto& l_AndroidIconStatus  = cc::daddy::CustomDaddyPorts::sm_androidIconActive_SenderPort.reserve();
            l_AndroidIconStatus.m_Data = s_pressAndroidIcon;
            cc::daddy::CustomDaddyPorts::sm_androidIconActive_SenderPort.deliver();
        }

        bool               APAEnterSwitchStatusChanged                = false;
        static const char* APAEnterSwitchStatusValues[] = {
            "ENTERSTS_HIDDEN",
            "ENTERSTS_AVAILABLE",
            "ENTERSTS_HIGHLIGHT",
            "ENTERSTS_INVALID"
        };
        static int APAEnterSwitchStatus = 0;
        APAEnterSwitchStatusChanged                   = APAEnterSwitchStatusChanged ||
                ImGui::Combo("APAEnterSwitchStatus", &APAEnterSwitchStatus, APAEnterSwitchStatusValues, IM_ARRAYSIZE(APAEnterSwitchStatusValues));

        if (APAEnterSwitchStatusChanged)
        {
            auto& l_APAEnterSwitchStatus  = cc::daddy::CustomDaddyPorts::sm_APAEnterSwitchStatus_SenderPort.reserve();
            l_APAEnterSwitchStatus.m_Data = static_cast<cc::target::common::EAPAEnterSwitchStatus>(APAEnterSwitchStatus);
            cc::daddy::CustomDaddyPorts::sm_APAEnterSwitchStatus_SenderPort.deliver();
        }

        static bool SRIsReady        = false;
        bool        SRIsReadyStatusChanged = false;
        SRIsReadyStatusChanged             = ImGui::Checkbox("SRIsReady", &SRIsReady);
        if (SRIsReadyStatusChanged)
        {
            auto& l_SRIsReadyStatus  = cc::daddy::CustomDaddyPorts::sm_UnityIsReady_SenderPort.reserve();
            l_SRIsReadyStatus.m_Data = static_cast< vfc::uint8_t >(SRIsReady);
            cc::daddy::CustomDaddyPorts::sm_UnityIsReady_SenderPort.deliver();
        }
        static int s_gear      = pc::daddy::EGear::GEAR_INIT;
        bool       gearChanged = false;
        gearChanged            = gearChanged || ImGui::RadioButton("Gear_NONE", &s_gear, 0);

        gearChanged = gearChanged || ImGui::RadioButton("Gear_N", &s_gear, 10);

        gearChanged = gearChanged || ImGui::RadioButton("Gear_R", &s_gear, 11);

        gearChanged = gearChanged || ImGui::RadioButton("Gear_P", &s_gear, 12);

        gearChanged = gearChanged || ImGui::RadioButton("Gear_D", &s_gear, 13);

        //if (gearChanged)
        {
            GearDaddy& l_gearState = BaseDaddyPorts::sm_gearSenderPort.reserve(); // keep sending
            l_gearState.m_Data     = (pc::daddy::EGear)s_gear;
            BaseDaddyPorts::sm_gearSenderPort.deliver();
        }

        static bool leftIndicatorBinkStatus        = false;
        bool        leftIndicatorBinkStatusChanged = false;
        leftIndicatorBinkStatusChanged             = ImGui::Checkbox("LeftIndicatorBinkStatus", &leftIndicatorBinkStatus);
        static bool rightIndicatorBinkStatus        = false;
        bool        rightIndicatorBinkStatusChanged = false;
        rightIndicatorBinkStatusChanged             = ImGui::Checkbox("RightIndicatorBinkStatus", &rightIndicatorBinkStatus);
        if (leftIndicatorBinkStatusChanged || rightIndicatorBinkStatusChanged)
        {
            auto& l_CustomVehicleLightsStatus  = cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.reserve();
            l_CustomVehicleLightsStatus.m_Data.m_leftIndicatorBlinkState = static_cast<uint8_t >(leftIndicatorBinkStatus);
            l_CustomVehicleLightsStatus.m_Data.m_rightIndicatorBlinkState = static_cast<uint8_t >(rightIndicatorBinkStatus);
            cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.deliver();
        }

        static int s_str        = static_cast<vfc::int32_t>(cc::target::common::ESystemStr::SYSTEM_STR_NONE);
        bool       l_strChanged = false;
        l_strChanged            = l_strChanged || ImGui::RadioButton("STR_NONE", &s_str, 0);
        l_strChanged = l_strChanged || ImGui::RadioButton("STR_ENTER", &s_str, 1);
        if (l_strChanged)
        {
            auto& l_strState = cc::daddy::CustomDaddyPorts::sm_systemStr_SenderPort.reserve(); // keep sending
            l_strState.m_Data     = (cc::target::common::ESystemStr)s_str;
            cc::daddy::CustomDaddyPorts::sm_systemStr_SenderPort.deliver();
        }

        static bool s_srActive        = false;
        bool        l_srActiveChanged = false;
        l_srActiveChanged             = ImGui::Checkbox("SRActive", &s_srActive);
        if (l_srActiveChanged)
        {
            auto& l_srActiveStatus  = cc::daddy::CustomDaddyPorts::sm_SRIsActive_SenderPort.reserve();
            l_srActiveStatus.m_Data = static_cast<cc::target::common::ESRActiveSts>(s_srActive);
            cc::daddy::CustomDaddyPorts::sm_SRIsActive_SenderPort.deliver();
        }

//         static bool s_pressDockAvmButton            = false;
//         bool        PressDockAvmButtonChanged = false;
//         PressDockAvmButtonChanged             = ImGui::Checkbox("PressDockAvm", &s_pressDockAvmButton);
//         if (PressDockAvmButtonChanged)
//         {
//             auto& l_DockAvmPress  = cc::daddy::CustomDaddyPorts::sm_dockAvmButtonPress_SenderPort.reserve();
//             l_DockAvmPress.m_Data = s_pressDockAvmButton;
//             cc::daddy::CustomDaddyPorts::sm_dockAvmButtonPress_SenderPort.deliver();
//         }
        bool               l_dockAvmDirty                = false;
        static const char* dockAvmStatusValues[] = {
            "NONE",
            "PRESS",
            "CLOSE",
            "INVALID"
        };
        static int dockAvmStatus = 0;
        l_dockAvmDirty                   = l_dockAvmDirty ||
                ImGui::Combo("dockAvmButtonStatus", &dockAvmStatus, dockAvmStatusValues, IM_ARRAYSIZE(dockAvmStatusValues));
        if (l_dockAvmDirty)
        {
            auto& container  = cc::daddy::CustomDaddyPorts::sm_dockAvmButtonPress_SenderPort.reserve();
            container.m_Data = static_cast<cc::target::common::EDockReq>(dockAvmStatus);
            cc::daddy::CustomDaddyPorts::sm_dockAvmButtonPress_SenderPort.deliver();
        }

        bool               l_floatViewChangeDirty                = false;
        static const char* floatViewChangeValues[] = {
            "NONE",
            "PLANVIEW",
            "FRVIEW"
        };
        static int floatViewChange = 0;
        l_floatViewChangeDirty                   = l_floatViewChangeDirty ||
                ImGui::Combo("floatViewChangeButton", &floatViewChange, floatViewChangeValues, IM_ARRAYSIZE(floatViewChangeValues));
        if (l_floatViewChangeDirty)
        {
            auto& container  = cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort.reserve();
            container.m_Data = static_cast<cc::daddy::EFloatViewType> (floatViewChange);
            cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort.deliver();
            l_floatViewChangeDirty=false;
        }

        bool               l_apaDirty                = false;
        static const char* apaFunStatusValues[] = {
            "FUNCSTS_PASSIVE",
            "FUNCSTS_STANDBY",
            "FUNCSTS_SEARCHINGINOUT",
            "cc::target::common::EAPAFunctionStatus::FUNCSTS_PARKINGINOUT",
            "FUNCSTS_PAUSED",
            "FUNCSTS_FAILURE",
            "FUNCSTS_RESERVE"
        };
        static int apaFunStatus = 0;
        l_apaDirty                   = l_apaDirty ||
                ImGui::Combo("apaFunStatus", &apaFunStatus, apaFunStatusValues, IM_ARRAYSIZE(apaFunStatusValues));
        if (l_apaDirty)
        {
            auto& container  = cc::daddy::CustomDaddyPorts::sm_APAFuncStatus_SenderPort.reserve();
            container.m_Data = static_cast<cc::target::common::EAPAFunctionStatus>(apaFunStatus);
            cc::daddy::CustomDaddyPorts::sm_APAFuncStatus_SenderPort.deliver();
        }

        bool               l_powerSaveModeDirty                = false;
        static const char*powerSaveModeValues[] = {
            "ADCUPOWERSAVESTS_OFF"     ,
            "ADCUPOWERSAVESTS_ONGOING" ,
            "ADCUPOWERSAVESTS_ON_L1"   ,
            "ADCUPOWERSAVESTS_ON_L2"   ,
            "ADCUPOWERSAVESTS_ON_L3"
        };
        static int powerSaveMode = 0;
        l_powerSaveModeDirty                   = l_powerSaveModeDirty ||
                ImGui::Combo("powerSaveMode", &powerSaveMode, powerSaveModeValues, IM_ARRAYSIZE(powerSaveModeValues));
        if (l_powerSaveModeDirty)
        {
            auto& container  = cc::daddy::CustomDaddyPorts::sm_ADCUPowerSavModeSts_SenderPort.reserve();
            container.m_Data = static_cast<cc::target::common::EADCUPowerSavModeSts>(powerSaveMode);
            cc::daddy::CustomDaddyPorts::sm_ADCUPowerSavModeSts_SenderPort.deliver();
        }

        static float   driverSteeringAngle = 0.f;
        bool driverSteeringAngleChanged = false;
        driverSteeringAngleChanged |= ImGui::SliderFloat("DriverSteeringAngle", &driverSteeringAngle, -360.0f, 360.0f);
        if (driverSteeringAngleChanged)
        {
            auto& l_driverSteeringAngle =
                cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.reserve();
            l_driverSteeringAngle.m_Data = driverSteeringAngle;
            cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.deliver();
        }

        static int   displayBrightness = 0;
        bool displayBrightnessChanged = false;
        displayBrightnessChanged |= ImGui::SliderInt("DisplayBrightness", &displayBrightness, 0, 100);
        if (displayBrightnessChanged)
        {
            auto& l_displayBrightness =
                cc::daddy::CustomDaddyPorts::sm_displayBrightnessStatus_SenderPort.reserve();
            l_displayBrightness.m_Data = displayBrightness;
            cc::daddy::CustomDaddyPorts::sm_displayBrightnessStatus_SenderPort.deliver();
        }

        bool               dirty                = false;
        static const char* voiceDockReqValues[] = {
            "EVOICEDOCKREQ_NONE",
            "EVOICEDOCKREQ_OPEN_FRONT",
            "EVOICEDOCKREQ_OPEN_FRONTWHEEL",
            "EVOICEDOCKREQ_OPEN_FRONTWIDE",
            "EVOICEDOCKREQ_OPEN_SKELETON",
            "EVOICEDOCKREQ_OPEN_3D",
            "EVOICEDOCKREQ_OPEN_REAR",
            "EVOICEDOCKREQ_OPEN_REARWIDE",
            "EVOICEDOCKREQ_OPEN_REARWHEEL",
            "EVOICEDOCKREQ_OPEN_9",
            "EVOICEDOCKREQ_OPEN_10",
            "EVOICEDOCKREQ_OPEN_11",
            "EVOICEDOCKREQ_OPEN_12",
            "EVOICEDOCKREQ_OPEN_13",
            "EVOICEDOCKREQ_OPEN_14",
            "EVOICEDOCKREQ_OPEN_15",
            "EVOICEDOCKREQ_OPEN_DOCK_AVM",
            "EVOICEDOCKREQ_OPEN_DOCK_CLOSE",
        };
        static int voiceDockReq = 0;
        dirty                   = dirty ||
                ImGui::Combo("voiceDockReqValues", &voiceDockReq, voiceDockReqValues, IM_ARRAYSIZE(voiceDockReqValues));
        if (dirty)
        {
            auto& container  = cc::daddy::CustomDaddyPorts::sm_voiceDockRequest_SenderPort.reserve();
            container.m_Data = static_cast<cc::target::common::EVoiceDockReq>(voiceDockReq);
            cc::daddy::CustomDaddyPorts::sm_voiceDockRequest_SenderPort.deliver();
        }
        if (ImGui::Button("DarkTheme"))
        {
            SEND_PORT(
                cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort,
                cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT);
        }
        ImGui::SameLine();
        if (ImGui::Button("LightTheme"))
        {
            SEND_PORT(
                cc::daddy::CustomDaddyPorts::sm_dayNightThemeDaddy_SenderPort,
                cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY);
        }

        static bool s_androidAlive        = true;
        ImGui::Checkbox("AndroidAlive", &s_androidAlive);
        auto& l_androidAliveStatus  = cc::daddy::CustomDaddyPorts::sm_androidAlive_SenderPort.reserve();
        l_androidAliveStatus.m_Data = s_androidAlive;
        cc::daddy::CustomDaddyPorts::sm_androidAlive_SenderPort.deliver();

        static bool s_competeQuit      = false;
        bool        CompeteQuitChanged = false;
        CompeteQuitChanged             = ImGui::Checkbox("CompeteQuit", &s_competeQuit);
        if (CompeteQuitChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_competeQuit_SenderPort.reserve();
            l_pData.m_Data = s_competeQuit;
            cc::daddy::CustomDaddyPorts::sm_competeQuit_SenderPort.deliver();
        }

        static bool s_backKey      = false;
        bool        BackKeyChanged = false;
        BackKeyChanged             = ImGui::Checkbox("BackKey", &s_backKey);
        if (BackKeyChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_backKeyEvent_SenderPort.reserve();
            l_pData.m_Data = s_backKey;
            cc::daddy::CustomDaddyPorts::sm_backKeyEvent_SenderPort.deliver();
        }

        static bool s_swBtSet   = false;
        bool        SwBtChanged = false;
        SwBtChanged             = ImGui::Checkbox("SteerWBttonSet", &s_swBtSet);
        if (SwBtChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonDefinition_SenderPort.reserve();
            l_pData.m_Data = s_swBtSet;
            cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonDefinition_SenderPort.deliver();
        }

        static bool s_swBtPress  = false;
        bool        SwBtPChanged = false;
        SwBtPChanged             = ImGui::Checkbox("SteerWBttonPress", &s_swBtPress);
        if (SwBtPChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonPress_SenderPort.reserve();
            l_pData.m_Data = s_swBtPress;
            cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonPress_SenderPort.deliver();
        }

        static bool s_fileError  = false;
        bool        FileErrorChanged = false;
        FileErrorChanged             = ImGui::Checkbox("FileError", &s_fileError);
        if (FileErrorChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.reserve();
            l_pData.m_Data = s_fileError;
            cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.deliver();
        }

        static bool s_VhmAlive  = true;
        bool        VhmAliveChanged = false;
        VhmAliveChanged             = ImGui::Checkbox("VhmAlive", &s_VhmAlive);
        if (VhmAliveChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_svsFid_SenderPort.reserve();
            l_pData.m_Data.m_FID_RxVhmOverall = static_cast<uint8_t>(s_VhmAlive);
            cc::daddy::CustomDaddyPorts::sm_svsFid_SenderPort.deliver();
        }

        static int   vehicleSpeed = 0;
        bool vehicleSpeedChanged = false;
        vehicleSpeedChanged |= ImGui::SliderInt("VehicleSpeed", &vehicleSpeed, 0, 100);
        if (vehicleSpeedChanged)
        {
            auto& l_pData = pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.reserve();
            l_pData.m_Data = vehicleSpeed;
            pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.deliver();
        }

        static bool s_SRIsTopActivity  = false;
        bool        SRIsTopActivityChanged = false;
        SRIsTopActivityChanged             = ImGui::Checkbox("SRIsTopActivity", &s_SRIsTopActivity);
        if (SRIsTopActivityChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_SRIsTop_SenderPort.reserve();
            l_pData.m_Data = static_cast<vfc::uint8_t >(s_SRIsTopActivity);
            cc::daddy::CustomDaddyPorts::sm_SRIsTop_SenderPort.deliver();
        }

        static bool s_settingRestore  = false;
        bool        SettingRestoreChanged = false;
        SettingRestoreChanged             = ImGui::Checkbox("SettingRestore", &s_settingRestore);
        if (SettingRestoreChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_settingRestoreDaddy_SenderPort.reserve();
            l_pData.m_Data = static_cast<vfc::uint8_t >(s_settingRestore);
            cc::daddy::CustomDaddyPorts::sm_settingRestoreDaddy_SenderPort.deliver();
        }

        static bool s_freeparkingActive = false;
        bool        l_freeparkingActiveChanged = false;
        l_freeparkingActiveChanged             = ImGui::Checkbox("FreeparkingActive", &s_freeparkingActive);
        if (l_freeparkingActiveChanged)
        {
            auto& l_pData  = cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.reserve();
            if (s_freeparkingActive)
            {
                l_pData.m_Data = true;
            }
            cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.deliver();
        }

        // static bool s_calibOrNot        = true;
        // ImGui::Checkbox("CalibOrNot", &s_calibOrNot);
        // auto& l_calibOrNotStatus  = cc::daddy::CustomDaddyPorts::sm_calibOrNot_SenderPort.reserve();
        // l_calibOrNotStatus.m_Data = s_calibOrNot;
        // cc::daddy::CustomDaddyPorts::sm_calibOrNot_SenderPort.deliver();

    }

    ImGui::End();
}

void ImGuiView::SonarSignals()
{
    using namespace pc::daddy;
    using cc::daddy::CustomDaddyPorts;
    static bool showDemoWindow = true;

    ImGui::Begin("SonarSignals");
    {
        {
            bool               dirty            = false;
            static const char* distanceValues[] = {
                "SONARDISTRANGECENTER_NONE",
                "SONARDISTRANGECENTER_LEVEL4",
                "SONARDISTRANGECENTER_LEVEL3",
                "SONARDISTRANGECENTER_LEVEL2",
                "SONARDISTRANGECENTER_LEVEL1",
                "SONARDISTRANGECENTER_NOT_USED",
            };
            static int  frontLeftSideDistance           = 0;
            static int  frontLeftDistance               = 0;
            static int  frontMiddleDistance             = 0;
            static int  frontRightDistance              = 0;
            static int  frontRightSideDistance          = 0;
            static bool frontLeftSideDistanceAvailable  = false;
            static bool frontLeftDistanceAvailable      = false;
            static bool frontMiddleDistanceAvailable    = false;
            static bool frontRightDistanceAvailable     = false;
            static bool frontRightSideDistanceAvailable = false;
            ImGui::Text("Pas Front");
            dirty = dirty ||
                    ImGui::Combo(
                        "frontLeftSideDistance", &frontLeftSideDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("frontLeftSideAvailable", &frontLeftSideDistanceAvailable);

            dirty = dirty ||
                    ImGui::Combo("frontLeftDistance", &frontLeftDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("frontLeftAvailable", &frontLeftDistanceAvailable);

            dirty =
                dirty ||
                ImGui::Combo("frontMiddleDistance", &frontMiddleDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("frontMiddleAvailable", &frontMiddleDistanceAvailable);

            dirty =
                dirty ||
                ImGui::Combo("frontRightDistance", &frontRightDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("frontRightAvailable", &frontRightDistanceAvailable);

            dirty =
                dirty ||
                ImGui::Combo(
                    "frontRightSideDistance", &frontRightSideDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("frontRightSideAvailable", &frontRightSideDistanceAvailable);

            static int  rearLeftSideDistance           = 0;
            static int  rearLeftDistance               = 0;
            static int  rearMiddleDistance             = 0;
            static int  rearRightDistance              = 0;
            static int  rearRightSideDistance          = 0;
            static bool rearLeftSideDistanceAvailable  = false;
            static bool rearLeftDistanceAvailable      = false;
            static bool rearMiddleDistanceAvailable    = false;
            static bool rearRightDistanceAvailable     = false;
            static bool rearRightSideDistanceAvailable = false;
            ImGui::Text("Pas Rear");
            dirty = dirty ||
                    ImGui::Combo(
                        "rearLeftSideDistance", &rearLeftSideDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("rearLeftSideAvailable", &rearLeftSideDistanceAvailable);

            dirty = dirty ||
                    ImGui::Combo("rearLeftDistance", &rearLeftDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("rearLeftAvailable", &rearLeftDistanceAvailable);

            dirty =
                dirty ||
                ImGui::Combo("rearMiddleDistance", &rearMiddleDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("rearMiddleAvailable", &rearMiddleDistanceAvailable);

            dirty = dirty ||
                    ImGui::Combo("rearRightDistance", &rearRightDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("rearRightAvailable", &rearRightDistanceAvailable);

            dirty = dirty ||
                    ImGui::Combo(
                        "rearRightSideDistance", &rearRightSideDistance, distanceValues, IM_ARRAYSIZE(distanceValues));

            dirty = dirty || ImGui::Checkbox("rearRightSideAvailable", &rearRightSideDistanceAvailable);

            static bool distanceDisplayRequest    = false;
            static int  distanceDetectStatusRear  = 0;
            static int  distanceDetectStatusFront = 0;
            static int  shortestDistanceZoneRear  = 0;
            static int  shortestDistanceZoneFront = 0;
            static int  sonarStatusDisplayRequest = 0;
            dirty = dirty || ImGui::Checkbox("SonarDisplayRequest", &distanceDisplayRequest);

            static int         soundLevelRequest         = 0;
            static const char* soundLevelRequestValues[] = {
                "SONARSOUND_NONE",
                "SONARSOUND_BEEP",
                "SONARSOUND_MUTE",
                "SONARSOUND_RESERVED",
            };
            static const char* distanceDetectValues[] = {
                "NO_DETECTION",        "DETECT_DIST_STOP",   "DETECT_DIST_CM_30",  "DETECT_DIST_CM_40",
                "DETECT_DIST_CM_50",   "DETECT_DIST_CM_60",  "DETECT_DIST_CM_70",  "DETECT_DIST_CM_80",
                "DETECT_DIST_CM_90",   "DETECT_DIST_CM_100", "DETECT_DIST_CM_110", "DETECT_DIST_CM_120",
                "DETECT_DIST_CM_130",  "DETECT_DIST_CM_140", "DETECT_DIST_CM_150", "DETECT_DIST_CM_160",
                "DETECT_DIST_CM_170",  "DETECT_DIST_CM_180", "DETECT_DIST_CM_190", "DETECT_DIST_CM_200",
                "DETECT_DIST_RESERVE",
            };
            static const char* distanceDetectZones[] = {
                "NO_REQUEST",
                "LEFT",
                "CENTER",
                "RIGHT",
                "LEFT_SIDE",
                "RIGHT_SIDE",
                "RESERVE",
            };

            static const char* SonarStatusDisplayRequestValues[] = {
                "SONARSTATUS_NONE" ,
                "SONARSTATUS_REQUEST" ,
                "SONARSTATUS_DEACTIVE" ,
                "SONARSTATUS_ERROR" ,
                "SONARSTATUS_FRONT_ERROR" ,
                "SONARSTATUS_RESERVED" ,
            };

            dirty = dirty || ImGui::Combo(
                                 "soundLevelRequest",
                                 &soundLevelRequest,
                                 soundLevelRequestValues,
                                 IM_ARRAYSIZE(soundLevelRequestValues));

            dirty = dirty || ImGui::Combo(
                                 "distanceDetectStatusRear",
                                 &distanceDetectStatusRear,
                                 distanceDetectValues,
                                 IM_ARRAYSIZE(distanceDetectValues));
            dirty = dirty || ImGui::Combo(
                                 "distanceDetectStatusFront",
                                 &distanceDetectStatusFront,
                                 distanceDetectValues,
                                 IM_ARRAYSIZE(distanceDetectValues));

            dirty = dirty || ImGui::Combo(
                                 "ShortestDistanceZoneRear",
                                 &shortestDistanceZoneRear,
                                 distanceDetectZones,
                                 IM_ARRAYSIZE(distanceDetectZones));

            dirty = dirty || ImGui::Combo(
                                 "ShortestDistanceZoneFront",
                                 &shortestDistanceZoneFront,
                                 distanceDetectZones,
                                 IM_ARRAYSIZE(distanceDetectZones));

            dirty = dirty || ImGui::Combo(
                                 "sonarStatusDisplayRequest",
                                 &sonarStatusDisplayRequest,
                                 SonarStatusDisplayRequestValues,
                                 IM_ARRAYSIZE(SonarStatusDisplayRequestValues));

            if (dirty)
            {
                auto& container = cc::daddy::CustomDaddyPorts::sm_SonarAPPData_SenderPort.reserve();
                container.m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)] =
                    static_cast<cc::target::common::ESonarDistRange>(frontLeftSideDistance);
                container.m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)] =
                    static_cast<cc::target::common::ESonarDistRange>(frontLeftDistance);
                container.m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)] =
                    static_cast<cc::target::common::ESonarDistRange>(frontMiddleDistance);
                container.m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)] =
                    static_cast<cc::target::common::ESonarDistRange>(frontRightDistance);
                container.m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)] =
                    static_cast<cc::target::common::ESonarDistRange>(frontRightSideDistance);

                container.m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)]  = (frontLeftSideDistanceAvailable);
                container.m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)]       = (frontLeftDistanceAvailable);
                container.m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)]     = (frontMiddleDistanceAvailable);
                container.m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)]      = (frontRightDistanceAvailable);
                container.m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)] = (frontRightSideDistanceAvailable);

                container.m_Data.m_sonarDistRear[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)] =
                    static_cast<cc::target::common::ESonarDistRange>(rearLeftSideDistance);
                container.m_Data.m_sonarDistRear[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)] =
                    static_cast<cc::target::common::ESonarDistRange>(rearLeftDistance);
                container.m_Data.m_sonarDistRear[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)] =
                    static_cast<cc::target::common::ESonarDistRange>(rearMiddleDistance);
                container.m_Data.m_sonarDistRear[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)] =
                    static_cast<cc::target::common::ESonarDistRange>(rearRightDistance);
                container.m_Data.m_sonarDistRear[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)] =
                    static_cast<cc::target::common::ESonarDistRange>(rearRightSideDistance);

                container.m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)]  = (rearLeftSideDistanceAvailable);
                container.m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)]       = (rearLeftDistanceAvailable);
                container.m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)]     = (rearMiddleDistanceAvailable);
                container.m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)]      = (rearRightDistanceAvailable);
                container.m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)] = (rearRightSideDistanceAvailable);

                container.m_Data.m_sonarDetectionDisplayReq = distanceDisplayRequest;
                container.m_Data.m_soundLevelRequest = static_cast<cc::target::common::ESonarSoundLevelRequest>(soundLevelRequest);
                container.m_Data.m_distShortestRear =
                    static_cast<cc::target::common::ESonarDetectDistanceStatus>(distanceDetectStatusRear);
                container.m_Data.m_distShortestFront =
                    static_cast<cc::target::common::ESonarDetectDistanceStatus>(distanceDetectStatusFront);
                container.m_Data.m_distShortestRearZone =
                    static_cast<cc::target::common::ESoundObstacleZone>(shortestDistanceZoneRear);
                container.m_Data.m_distShortestFrontZone =
                    static_cast<cc::target::common::ESoundObstacleZone>(shortestDistanceZoneFront);
                container.m_Data.m_sonarStatusDisplayRequest = static_cast<cc::target::common::ESonarStatusDisplayRequest>(sonarStatusDisplayRequest);
                cc::daddy::CustomDaddyPorts::sm_SonarAPPData_SenderPort.deliver();
            }
        }
    }
    ImGui::End();
}
void ImGuiView::MODSignals()
{
    using namespace pc::daddy;
    using cc::daddy::CustomDaddyPorts;
    static bool showDemoWindow = true;

    ImGui::Begin("MODSignals");
    {
        using namespace cc::mod;
        // MOD
        auto&       container                   = cc::daddy::CustomDaddyPorts::sm_ModStateDaddy.reserve();
        uint32_t    flag                        = 0u;
        static bool s_WARN_SYMBOL_SINGLE_CAM    = false;
        static bool s_WARN_SYMBOL_TOPVIEW_FRONT = false;
        static bool s_WARN_SYMBOL_TOPVIEW_RIGHT = false;
        static bool s_WARN_SYMBOL_TOPVIEW_REAR  = false;
        static bool s_WARN_SYMBOL_TOPVIEW_LEFT  = false;
        static bool s_MOD_SINGLE_CAM_VIEW       = false;
        static bool s_MOD_TOPVIEW               = false;
        static bool s_MOD_ABNORMAL              = false;
        static bool s_MOD_UNAVAILABLE           = false;
        static bool s_MOD_ABNORMAL_SINGLE       = false;
        static bool s_MOD_UNAVAILABLE_SIGNLE    = false;
        static bool s_END_OF_ICON               = false;
        bool        dirty                       = false;
        dirty = dirty || ImGui::Checkbox("MOD_WARN_SYMBOL_SINGLE_CAM", &s_WARN_SYMBOL_SINGLE_CAM);
        dirty = dirty || ImGui::Checkbox("MOD_WARN_SYMBOL_TOPVIEW_FRONT", &s_WARN_SYMBOL_TOPVIEW_FRONT);
        dirty = dirty || ImGui::Checkbox("MOD_WARN_SYMBOL_TOPVIEW_RIGHT", &s_WARN_SYMBOL_TOPVIEW_RIGHT);
        dirty = dirty || ImGui::Checkbox("MOD_WARN_SYMBOL_TOPVIEW_REAR", &s_WARN_SYMBOL_TOPVIEW_REAR);
        dirty = dirty || ImGui::Checkbox("MOD_WARN_SYMBOL_TOPVIEW_LEFT", &s_WARN_SYMBOL_TOPVIEW_LEFT);
        dirty = dirty || ImGui::Checkbox("MOD_SINGLE_CAM_VIEW", &s_MOD_SINGLE_CAM_VIEW);
        dirty = dirty || ImGui::Checkbox("MOD_TOPVIEW", &s_MOD_TOPVIEW);
        dirty = dirty || ImGui::Checkbox("MOD_ABNORMAL", &s_MOD_ABNORMAL);
        dirty = dirty || ImGui::Checkbox("MOD_UNAVAILABLE", &s_MOD_UNAVAILABLE);
        dirty = dirty || ImGui::Checkbox("MOD_ABNORMAL_SINGLE ", &s_MOD_ABNORMAL_SINGLE);
        dirty = dirty || ImGui::Checkbox("MOD_UNAVAILABLE_SIGNLE ", &s_MOD_UNAVAILABLE_SIGNLE);
        dirty = dirty || ImGui::Checkbox("END_OF_ICON ", &s_END_OF_ICON);

        if (s_WARN_SYMBOL_SINGLE_CAM)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::WARN_SYMBOL_SINGLE_CAM));
        }

        if (s_WARN_SYMBOL_TOPVIEW_FRONT)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::WARN_SYMBOL_TOPVIEW_FRONT));
        }

        if (s_WARN_SYMBOL_TOPVIEW_RIGHT)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::WARN_SYMBOL_TOPVIEW_RIGHT));
        }

        if (s_WARN_SYMBOL_TOPVIEW_REAR)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::WARN_SYMBOL_TOPVIEW_REAR));
        }

        if (s_WARN_SYMBOL_TOPVIEW_LEFT)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::WARN_SYMBOL_TOPVIEW_LEFT));
        }

        if (s_MOD_SINGLE_CAM_VIEW)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::MOD_SINGLE_CAM_VIEW));
        }

        if (s_MOD_TOPVIEW)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::MOD_TOPVIEW));
        }

        if (s_MOD_ABNORMAL)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::MOD_ABNORMAL));
        }

        if (s_MOD_UNAVAILABLE)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::MOD_UNAVAILABLE));
        }

        if (s_MOD_ABNORMAL_SINGLE)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::MOD_ABNORMAL_SINGLE));
        }

        if (s_MOD_UNAVAILABLE_SIGNLE)
        {
            flag |= (1u << static_cast<vfc::uint32_t>(cc::mod::IconIndex::MOD_UNAVAILABLE_SIGNLE));
        }

        container.m_Data = flag;
        cc::daddy::CustomDaddyPorts::sm_ModStateDaddy.deliver();
    }
    ImGui::End();
}
ImGuiView::~ImGuiView()
{
    ImPlot::DestroyContext();
    ImGui::DestroyContext();
}

} // namespace imgui
} // namespace views
} // namespace cc

#endif // #if defined(TARGET_STANDALONE) && defined(ENABLE_IMGUI)
