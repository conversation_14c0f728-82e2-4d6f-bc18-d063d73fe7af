//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_COVERPLATE
#define CC_ASSETS_TRAJECTORY_SUBASSETS_COVERPLATE

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"


namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic



class CoverPlate : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:

  CoverPlate(
    pc::core::Framework* f_framework,
    float f_height,
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    unsigned int f_numOfVerts);

  virtual void generateVertexData();

protected:
  virtual ~CoverPlate();

private:
  //! Copy constructor is not permitted.
  CoverPlate (const CoverPlate& other); // = delete
  //! Copy assignment operator is not permitted.
  CoverPlate& operator=(const CoverPlate& other); // = delete

  const unsigned int m_numOfVerts;
  unsigned int m_numOfRenderedVerts;
};


} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_COVERPLATE
