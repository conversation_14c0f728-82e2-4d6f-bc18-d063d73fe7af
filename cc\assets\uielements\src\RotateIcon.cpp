//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "cc/assets/uielements/inc/RotateIcon.h"
#include "cc/imgui/inc/imgui_manager.h" // PRQA S 1060
#include "cc/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

RotateIcon::RotateIcon(const std::string& f_filename)
    : pc::assets::Icon{f_filename, false}
    , m_spotCorners{std::make_shared<vfc::TCArray<osg::Vec2f, 4>>()}
    , m_spotRect{std::make_shared<cc::util::polygonmath::RectangleMath>(osg::Vec2f{0.0f, 0.0f})}
{
    const auto texture = Icon::getTexture();
    if (texture == nullptr)
    {
        XLOG_WARN(g_AppContext, "RotateIcon: " << f_filename << " not found!");
        return;
    }
    osg::Vec2i l_textureSize(texture->getTextureWidth(), texture->getTextureHeight());
    if ((0 == l_textureSize.x()) || (0 == l_textureSize.y()))
    {
        const osg::Image* const l_image = texture->getImage();
        // assert(l_image);
        l_textureSize.x() = l_image->s();
        l_textureSize.y() = l_image->t();
    }
    m_iconSize =
        osg::Vec2f{static_cast<vfc::float32_t>(l_textureSize.x()), static_cast<vfc::float32_t>(l_textureSize.y())};
}

void RotateIcon::setRotateAngle(const vfc::float32_t& f_rotAngle)
{
    if (vfc::isEqual(m_rotAngle, f_rotAngle))
    {
        return;
    }
    m_rotAngle = f_rotAngle;
    // magic to set dirty
    osg::Vec2f                 originalPos;
    pc::assets::Icon::UnitType originalUnitType{pc::assets::Icon::UnitType::Pixel};
    pc::assets::Icon::getPosition(originalPos, originalUnitType);
    pc::assets::Icon::setPosition(originalPos * 0.5f + osg::Vec2f{1.0f, 1.0f}, originalUnitType);
    pc::assets::Icon::setPosition(originalPos, originalUnitType);
}

osg::Geometry* RotateIcon::createGeometry() const
{
    osg::Geometry* const l_geometry = new osg::Geometry;
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);
    l_geometry->setVertexArray(new osg::Vec3Array(4));

    osg::Vec4Array* const l_colors = new osg::Vec4Array(1);
    (*l_colors)[0]           = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4);
    l_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, 6u);
    (*l_indices)[0]                   = 0u;
    (*l_indices)[1]                   = 1u;
    (*l_indices)[2]                   = 2u;
    (*l_indices)[3]                   = 2u;
    (*l_indices)[4]                   = 1u;
    (*l_indices)[5]                   = 3u;
    l_geometry->addPrimitiveSet(l_indices); // PRQA S 3803

    return l_geometry;
}

void RotateIcon::updateGeometry(
    osg::Geometry*    f_geometry,
    const osg::Vec2f& f_origin,
    const osg::Vec2f& f_size,
    vfc::float32_t    f_left,
    vfc::float32_t    f_bottom,
    vfc::float32_t    f_right,
    vfc::float32_t    f_top) const
{
    if (f_geometry == nullptr)
    {
        return;
    }
    const auto l_alignmentHorizontal = getAlignmentHorizontal();
    const auto l_alignmentVertical = getAlignmentVertical();
//    const auto l_originType = getOrigin(); // PRQA S 3803
    auto l_origin = f_origin;
    osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*>(f_geometry->getVertexArray()); // PRQA S 3076
    //! handle horizontal alignment
    switch (l_alignmentHorizontal)
    {
    case Alignment::Center:
    {
        l_origin.x() += std::floor(0.5f * f_size.x());
        break;
    }
    case Alignment::Right:
    {
        l_origin.x() += f_size.x();
        break;
    }
    default:
    {
        break;
    }
    }
    //! handle vertical alignment
    switch (l_alignmentVertical)
    {
    case Alignment::Center:
    {
        l_origin.y() += std::floor(0.5f * f_size.y());
        break;
    }
    case Alignment::Bottom:
    {
        l_origin.y() += f_size.y();
        break;
    }
    default:
    {
        break;
    }
    }
    m_spotRect->setLength(f_size.x());
    m_spotRect->setWidth(f_size.y());
    m_spotRect->setAngle(m_rotAngle);
    m_spotRect->setCenterPoint(l_origin);
    m_spotRect->updateRectPoints(
        (*m_spotCorners)[0u], (*m_spotCorners)[1u], (*m_spotCorners)[2u], (*m_spotCorners)[3u]);

    (*l_vertices)[0u] = osg::Vec3f{(*m_spotCorners)[2u], 0.0f};
    (*l_vertices)[1u] = osg::Vec3f{(*m_spotCorners)[3u], 0.0f};
    (*l_vertices)[2u] = osg::Vec3f{(*m_spotCorners)[1u], 0.0f};
    (*l_vertices)[3u] = osg::Vec3f{(*m_spotCorners)[0u], 0.0f};
    l_vertices->dirty();

    osg::Vec2Array* const l_texCoords = static_cast<osg::Vec2Array*>(f_geometry->getTexCoordArray(0u)); // PRQA S 3076
    (*l_texCoords)[0u]          = osg::Vec2f(f_left, f_bottom);
    (*l_texCoords)[1u]          = osg::Vec2f(f_right, f_bottom);
    (*l_texCoords)[2u]          = osg::Vec2f(f_left, f_top);
    (*l_texCoords)[3u]          = osg::Vec2f(f_right, f_top);
    l_texCoords->dirty();

    f_geometry->dirtyBound();
}

} // namespace uielements
} // namespace assets
} // namespace cc
