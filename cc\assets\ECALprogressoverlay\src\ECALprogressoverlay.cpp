//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ECALprogressoverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/ECALprogressoverlay/inc/ECALprogressoverlay.h"
#include "cc/assets/uielements/inc/Utils.h"
//#include <osg/MatrixTransform>
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "CustomSystemConf.h"



using pc::util::logging::g_AppContext;



namespace cc
{
namespace assets
{
namespace ECALprogressoverlay
{

// ! enum values for ECAL progess overlay
enum ECALStatusIconType
{
  ECAL_STATUS_ERROR_ICON_REAR_MASK,
  ECAL_STATUS_OK_ICON_REAR_MASK,
  ECAL_STATUS_ERROR_ICON_LEFT_MASK,
  ECAL_STATUS_OK_ICON_LEFT_MASK,
  ECAL_STATUS_ERROR_ICON_FRONT_MASK,
  ECAL_STATUS_OK_ICON_FRONT_MASK,
  ECAL_STATUS_ERROR_ICON_RIGHT_MASK,
  ECAL_STATUS_OK_ICON_RIGHT_MASK,
  ECAL_STATUS_ICON_CALIBRATIONING,
  ECAL_STATUS_ICON_CALIBRATION_SUCCESSED,
  ECAL_STATUS_ICON_CALIBRATION_FAILED,
};



//!
//! @brief Construct a new ECALprogressoverlay Manager:: ECALprogressoverlay Manager object
//!
//! @param f_config
//!
ECALprogressoverlayManager::ECALprogressoverlayManager()
  : m_lastConfigUpdate(~0u), 
    m_image_Calibrating_path("cc/resources/ECAL/Calibrating.png"),
    m_image_CalibratedFailed_path("cc/resources/ECAL/CalibratedFailed.png"),
    m_image_CalibratedSuccessful_path("cc/resources/ECAL/CalibratedSuccessful.png"),
    m_image_mask_red_horizontal_path("cc/resources/ECAL/mask_red_horizontal.png"),
    m_image_mask_green_horizontal_path("cc/resources/ECAL/mask_green_horizontal.png")
{

}


ECALprogressoverlayManager::~ECALprogressoverlayManager() = default;


bool isCalibrationError(vfc::int16_t f_camPos, vfc::uint32_t errCode)
{
  bool result = false;
  cc::cpc::ECPCErrorReport cornerDetectError = cc::cpc::CPC_ERROR_REPORT_OK;
  // cc::cpc::ECPCErrorReport deviationError = cc::cpc::CPC_ERROR_REPORT_OK;
  // switch(f_camPos)
  // {
  //   case cc::cpc::CAMERA_REAR:
  //     cornerDetectError = cc::cpc::CPC_INSUFFICIENT_VERTEX_FOUND;
  //     deviationError = cc::cpc::CPC_RESULT_DEVIATION;
  //     break;
  //   case cc::cpc::CAMERA_LEFT:
  //     cornerDetectError = cc::cpc::CPC_INSUFFICIENT_VERTEX_FOUND;
  //     deviationError = cc::cpc::CPC_RESULT_DEVIATION;
  //     break;
  //   case cc::cpc::CAMERA_FRONT:
  //     cornerDetectError = cc::cpc::CPC_INSUFFICIENT_VERTEX_FOUND;
  //     deviationError = cc::cpc::CPC_RESULT_DEVIATION;
  //     break;
  //   case cc::cpc::CAMERA_RIGHT:
  //     cornerDetectError = cc::cpc::CPC_INSUFFICIENT_VERTEX_FOUND;
  //     deviationError = cc::cpc::CPC_RESULT_DEVIATION;
  //     break;
  //   default: break;
  // }
  // if((errCode & static_cast<vfc::uint32_t>(cornerDetectError)) || (errCode & static_cast<vfc::uint32_t>(deviationError)))
  if (errCode != static_cast<vfc::uint32_t>(cornerDetectError))
    {
      result = true;
    }
  return result;
}
void ECALprogressoverlayManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  const vfc::float32_t l_view_width_half = static_cast<float>(cc::core::g_views->m_fisheyeViewport.m_size.x())*0.5f;
  const vfc::float32_t l_view_height_half = static_cast<float>(cc::core::g_views->m_fisheyeViewport.m_size.y())*0.5f;

  // ! init ECALprogressoverlay icons
  m_ECALprogressoverlayIcons.clear(f_imageOverlays);

  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_red_horizontal_path, osg::Vec2f(l_view_width_half*1.5f, l_view_height_half*0.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));
  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_green_horizontal_path, osg::Vec2f(l_view_width_half*1.5f, l_view_height_half*0.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));

  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_red_horizontal_path, osg::Vec2f(l_view_width_half*0.5f, l_view_height_half*1.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));
  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_green_horizontal_path, osg::Vec2f(l_view_width_half*0.5f, l_view_height_half*1.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));

  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_red_horizontal_path, osg::Vec2f(l_view_width_half*0.5f, l_view_height_half*0.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));
  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_green_horizontal_path, osg::Vec2f(l_view_width_half*0.5f, l_view_height_half*0.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));

  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_red_horizontal_path, osg::Vec2f(l_view_width_half*1.5f, l_view_height_half*1.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));
  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_mask_green_horizontal_path, osg::Vec2f(l_view_width_half*1.5f, l_view_height_half*1.5f) , osg::Vec2f(l_view_width_half,l_view_height_half) ));

  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_Calibrating_path,          osg::Vec2f(l_view_width_half, l_view_height_half)   , uielements::getImageSize(m_image_Calibrating_path) ));
  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_CalibratedSuccessful_path, osg::Vec2f(l_view_width_half, l_view_height_half)   , uielements::getImageSize(m_image_CalibratedSuccessful_path) ));
  m_ECALprogressoverlayIcons.addIcon(f_imageOverlays, createIcon(m_image_CalibratedFailed_path,     osg::Vec2f(l_view_width_half, l_view_height_half)   , uielements::getImageSize(m_image_CalibratedFailed_path) ));

  m_ECALprogressoverlayIcons.setAllEnabled(false);
}


void ECALprogressoverlayManager::update(const pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework) // PRQA S 6043
{
  // ! check if config has changed
  // if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  // {
  //  init(f_imageOverlays);
  //   m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  // }

    // initialization
    //m_ECALprogressoverlayIcons.setAllEnabled(false);


  // ! check the PP Mute button status

  if ( true == f_framework->asCustomFramework()->m_cpcStatus_Receiverport.isConnected() )
  {
    static int l_ctr = -1 ;
    const cc::daddy::CpcStatus_st_t* l_pCalibStatus = f_framework->asCustomFramework()->m_cpcStatus_Receiverport.getData();

    if ((nullptr != l_pCalibStatus) && (static_cast<int>(l_pCalibStatus->m_sequenceNumber) != l_ctr))
    {
      l_ctr = static_cast<int>(l_pCalibStatus->m_sequenceNumber) ;

      unsigned int l_CalibratedSuccessfulCounter = 0u;
      unsigned int l_CalibratedFailedCounter = 0u;
      unsigned int l_CameraCounter = 0u;

      for (unsigned int i = 0u; i < 4u; ++i)
      {

        if (l_pCalibStatus->m_Data.m_calibstatus[i].m_camAvailability != cc::cpc::CPC_AWAKE)
        {

          l_CameraCounter++;

          if (l_pCalibStatus->m_Data.m_calibstatus[i].m_calibrationError == cc::cpc::CPC_ERROR_REPORT_OK)
          {
            m_ECALprogressoverlayIcons.getIcon(i * 2u + 1u)->setEnabled(true);
            m_ECALprogressoverlayIcons.getIcon(i * 2u)->setEnabled(false);

            l_CalibratedSuccessfulCounter++;
          }
          else if ((l_pCalibStatus->m_Data.m_calibstatus[i].m_calibrationError == cc::cpc::CPC_TIMEOUT) ||
                    (isCalibrationError(static_cast<vfc::int16_t>(i), l_pCalibStatus->m_Data.m_calibstatus[i].m_calibrationError)))
          {
            m_ECALprogressoverlayIcons.getIcon(i * 2u)->setEnabled(true);
            m_ECALprogressoverlayIcons.getIcon(i * 2u + 1u)->setEnabled(false);

            l_CalibratedFailedCounter++;
          }
          else
          {
            //Do nothing
          }

        }
        else
        {
          m_ECALprogressoverlayIcons.getIcon(i * 2u)->setEnabled(false);
          m_ECALprogressoverlayIcons.getIcon(i * 2u + 1u)->setEnabled(false);
        }

      }

      m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATION_SUCCESSED))->setEnabled(false);
      m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATION_FAILED))->setEnabled(false);
      m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATIONING))->setEnabled(false);

      if(l_CalibratedSuccessfulCounter == 4u)
      {
        m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATION_SUCCESSED))->setEnabled(true);
      }
      else if ((l_CalibratedFailedCounter > 0u) && (l_CameraCounter == 4u))
      {
        m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATION_FAILED))->setEnabled(true);
      }
      else if (((l_CalibratedSuccessfulCounter>0u) || (l_CalibratedFailedCounter>0u)) && (l_CameraCounter <4u))
      {
        m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATIONING))->setEnabled(true);
      }
      else //Display calibrationing in begining of CPC
      {
        m_ECALprogressoverlayIcons.getIcon(static_cast<unsigned int>(ECAL_STATUS_ICON_CALIBRATIONING))->setEnabled(true);
      }
    }

  }




}


pc::assets::Icon* ECALprogressoverlayManager::createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize) const
{
  pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


//!
//! @brief Construct a new ECALprogressoverlay:: ECALprogressoverlay object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ECALprogressoverlay::ECALprogressoverlay(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays(f_assetId , nullptr)    // PRQA S 2966  
  , m_customFramework(f_customFramework)
{
  m_manager.init(this);
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


ECALprogressoverlay::~ECALprogressoverlay() = default;


void ECALprogressoverlay::traverse(osg::NodeVisitor& f_nv)
{

  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);


  }

  pc::assets::ImageOverlays::traverse(f_nv);
}





} // namespace ECALprogressoverlay
} // namespace assets
} // namespace cc 
 
