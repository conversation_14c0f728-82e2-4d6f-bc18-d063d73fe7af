//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_ASSETS_PARKINGSPACE_ICON_H
#define CC_ASSETS_PARKINGSPACE_ICON_H

#include "cc/core/inc/CustomScene.h"
#include "cc/util/polygonmath/inc/PolygonMath.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include <osgDB/ReadFile>

namespace cc
{
namespace assets
{
namespace uielements
{

//!
//! ParkingSpaceIcon
//!
class RotateIcon : public pc::assets::Icon
{
public:
    RotateIcon(const std::string& f_filename);

    void setHorizontal(bool f_isHori)
    {
        m_isHori = f_isHori;
    }
    void setRotateAngle(const vfc::float32_t& f_rotAngle);

protected:
    virtual ~RotateIcon() = default;

    // //! Copy constructor is not permitted.
    // RotateIcon (const RotateIcon& other); // = delete
    // //! Copy assignment operator is not permitted.
    // RotateIcon& operator=(const RotateIcon& other); // = delete

    void updateGeometry(
        osg::Geometry*    f_geometry,
        const osg::Vec2f& f_origin,
        const osg::Vec2f& f_size,
        vfc::float32_t    f_left,
        vfc::float32_t    f_bottom,
        vfc::float32_t    f_right,
        vfc::float32_t    f_top) const override;

    osg::Geometry* createGeometry() const override;

private:
    vfc::float32_t                                        m_rotAngle = 0.0f;
    osg::Vec2f                                            m_iconSize = {0.0f, 0.0f};
    bool                                                  m_isHori   = true;
    std::shared_ptr<vfc::TCArray<osg::Vec2f, 4>>          m_spotCorners;
    std::shared_ptr<cc::util::polygonmath::RectangleMath> m_spotRect;
};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKINGSPACE_ICON_H