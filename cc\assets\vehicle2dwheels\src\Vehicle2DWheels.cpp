//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD_DENZA&MR
/// @file  Vehicle2DWheels.h
/// @brief
//=============================================================================

#include "cc/assets/vehicle2dwheels/inc/Vehicle2DWheels.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h"   //for isZero float compare
// #include "pc/svs/util/osgx/inc/Utils.h"
// #include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace vehicle2dwheels
{

pc::util::coding::Item<Vehicle2DWheelsSetting> g_settings("Vehicle2DWheels"); 

osg::Texture2D* loadTexture(const std::string& f_filename)
{
    osg::Image* const l_image = osgDB::readImageFile(f_filename);
    if (l_image == nullptr)
    {
        XLOG_ERROR(g_AppContext, "Vehicle2DWheels::loadTexture(): Could not load " << f_filename); 
    }
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
    return l_texture;
}

Vehicle2DWheels::Vehicle2DWheels(pc::core::Framework* f_framework)
    : m_framework{f_framework}
    , m_vehicle2DWheelsFL{}
    , m_vehicle2DWheelsFR{}
    , m_vehicle2DWheelsRL{}
    , m_vehicle2DWheelsRR{}
    , m_position{}
    , m_size{}
    , m_steeringWheelAngle{0}
    , m_settingsModifiedCount{~0u}
    , m_lastUpdateTime{0.0f}
    , m_drivenDistance{0.0f}
    , m_animXDirection{false}
{
    setName("Vehicle2DWheels");
    setNumChildrenRequiringUpdateTraversal(1u);
}

static osg::observer_ptr<osg::StateSet> g_sharedVehicle2DWheelStateSet;

void Vehicle2DWheels::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        if (g_settings->getModifiedCount() != m_settingsModifiedCount)
        {
            init();
            m_settingsModifiedCount = g_settings->getModifiedCount();
        }
        update();
    }
    osg::Group::traverse(f_nv);
}

void Vehicle2DWheels::init()
{
    removeChildren(0u, getNumChildren());    // PRQA S 3803

    g_sharedVehicle2DWheelStateSet = new osg::StateSet;

    osg::Texture2D* const l_wheelTexture = loadTexture(g_settings->m_wheel);
    // osg::StateSet* l_wheelStateSet = new osg::StateSet;
    g_sharedVehicle2DWheelStateSet->setTextureAttribute(0u, l_wheelTexture);
    g_sharedVehicle2DWheelStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    g_sharedVehicle2DWheelStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    g_sharedVehicle2DWheelStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    g_sharedVehicle2DWheelStateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));    // PRQA S 3803
    g_sharedVehicle2DWheelStateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f);    // PRQA S 3803
    g_sharedVehicle2DWheelStateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);    // PRQA S 3803

    pc::core::TextureShaderProgramDescriptor l_vehicel2DWheelsShader("advancedTex");
    l_vehicel2DWheelsShader.apply(g_sharedVehicle2DWheelStateSet.get()); // PRQA S 3803


    const osg::ref_ptr<osg::Geode> l_wheelsFLGeode = new osg::Geode;
    const osg::ref_ptr<osg::Geode> l_wheelsFRGeode = new osg::Geode;
    const osg::ref_ptr<osg::Geode> l_wheelsRLGeode = new osg::Geode;
    const osg::ref_ptr<osg::Geode> l_wheelsRRGeode = new osg::Geode;

    const osg::ref_ptr<osg::Geometry> l_wheelsFLGeometry = new osg::Geometry;
    const osg::ref_ptr<osg::Geometry> l_wheelsFRGeometry = new osg::Geometry;
    const osg::ref_ptr<osg::Geometry> l_wheelsRLGeometry = new osg::Geometry;
    const osg::ref_ptr<osg::Geometry> l_wheelsRRGeometry = new osg::Geometry;

    osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
    (*l_vertices)[0u] = osg::Vec3(-0.5f * g_settings->m_wheelSize.x(), -0.5f * g_settings->m_wheelSize.y(), g_settings->m_groundLevel); // create a high planar to make it placed upper than vehicle model
    (*l_vertices)[1u] = osg::Vec3( 0.5f * g_settings->m_wheelSize.x(), -0.5f * g_settings->m_wheelSize.y(), g_settings->m_groundLevel); // just set the height as 0.1 also be fine
    (*l_vertices)[2u] = osg::Vec3( 0.5f * g_settings->m_wheelSize.x(),  0.5f * g_settings->m_wheelSize.y(), g_settings->m_groundLevel); // 14.5 ~ maximum value of height that we can we see thought planview camera
    (*l_vertices)[3u] = osg::Vec3(-0.5f * g_settings->m_wheelSize.x(),  0.5f * g_settings->m_wheelSize.y(), g_settings->m_groundLevel); 
    l_wheelsFLGeometry->setVertexArray(l_vertices);
    l_wheelsFRGeometry->setVertexArray(l_vertices);
    l_wheelsRLGeometry->setVertexArray(l_vertices);
    l_wheelsRRGeometry->setVertexArray(l_vertices);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
    (*l_texCoords)[0u] = osg::Vec2(1.0f, 0.0f);
    (*l_texCoords)[1u] = osg::Vec2(1.0f, 1.0f);
    (*l_texCoords)[2u] = osg::Vec2(0.0f, 1.0f);
    (*l_texCoords)[3u] = osg::Vec2(0.0f, 0.0f);
    l_wheelsFLGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsFRGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsRLGeometry->setTexCoordArray(0u, l_texCoords);
    l_wheelsRRGeometry->setTexCoordArray(0u, l_texCoords);

    osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
    (*l_colours)[0u] = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_wheelsFLGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);
    l_wheelsFRGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);
    l_wheelsFLGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);
    l_wheelsRRGeometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

    osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
    (*l_normals)[0u] = osg::Z_AXIS;
    l_wheelsFLGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);
    l_wheelsFRGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);
    l_wheelsRLGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);
    l_wheelsRRGeometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, 6u); // PRQA S 3143
    (*l_indices)[0u] = 1u;
    (*l_indices)[1u] = 0u;
    (*l_indices)[2u] = 2u;
    (*l_indices)[3u] = 2u;
    (*l_indices)[4u] = 0u;
    (*l_indices)[5u] = 3u;
    l_wheelsFLGeometry->addPrimitiveSet(l_indices);    // PRQA S 3803
    l_wheelsFRGeometry->addPrimitiveSet(l_indices);    // PRQA S 3803
    l_wheelsRLGeometry->addPrimitiveSet(l_indices);    // PRQA S 3803
    l_wheelsRRGeometry->addPrimitiveSet(l_indices);    // PRQA S 3803

    l_wheelsFLGeode->addDrawable(l_wheelsFLGeometry); // PRQA S 3803
    l_wheelsFRGeode->addDrawable(l_wheelsFRGeometry); // PRQA S 3803
    l_wheelsRLGeode->addDrawable(l_wheelsRLGeometry); // PRQA S 3803
    l_wheelsRRGeode->addDrawable(l_wheelsRRGeometry); // PRQA S 3803

    m_vehicle2DWheelsFL = new osg::MatrixTransform();
    m_vehicle2DWheelsFR = new osg::MatrixTransform();
    m_vehicle2DWheelsRL = new osg::MatrixTransform();
    m_vehicle2DWheelsRR = new osg::MatrixTransform();

    m_vehicle2DWheelsFL->addChild(l_wheelsFLGeode);    // PRQA S 3803
    m_vehicle2DWheelsFR->addChild(l_wheelsFRGeode);    // PRQA S 3803
    m_vehicle2DWheelsRL->addChild(l_wheelsRLGeode);    // PRQA S 3803
    m_vehicle2DWheelsRR->addChild(l_wheelsRRGeode);    // PRQA S 3803
    m_vehicle2DWheelsFL->setName("wheelsFLNode");
    m_vehicle2DWheelsFR->setName("wheelsFRNode");
    m_vehicle2DWheelsRL->setName("wheelsRLNode");
    m_vehicle2DWheelsRR->setName("wheelsRRNode");

    m_vehicle2DWheelsFL->setStateSet(g_sharedVehicle2DWheelStateSet.get());
    m_vehicle2DWheelsFR->setStateSet(g_sharedVehicle2DWheelStateSet.get());
    m_vehicle2DWheelsRL->setStateSet(g_sharedVehicle2DWheelStateSet.get());
    m_vehicle2DWheelsRR->setStateSet(g_sharedVehicle2DWheelStateSet.get());

    addChild(m_vehicle2DWheelsFL); // PRQA S 3803
    addChild(m_vehicle2DWheelsFR); // PRQA S 3803
    addChild(m_vehicle2DWheelsRL); // PRQA S 3803
    addChild(m_vehicle2DWheelsRR); // PRQA S 3803

    const osg::Vec3 l_positionRL(0.f, pc::vehicle::g_mechanicalData->m_trackFront * 0.5f, g_settings->m_groundLevel);
    m_vehicle2DWheelsRL->setMatrix(osg::Matrix::translate(l_positionRL));
    const osg::Vec3 l_positionRR(0.f, - (pc::vehicle::g_mechanicalData->m_trackFront * 0.5f), g_settings->m_groundLevel);
    m_vehicle2DWheelsRR->setMatrix(osg::Matrix::translate(l_positionRR));
}

void Vehicle2DWheels::update()
{
    updateWheelSteering();
    setupCommonState();
}

void Vehicle2DWheels::updateWheelSteering()
{
    const pc::daddy::SteeringAngleDaddy* const l_pData = m_framework->asCustomFramework()->m_steeringAngleFrontReceiver.getData();
    if (nullptr != l_pData)
    {
        vfc::CSI::si_radian_f32_t l_value_rad = l_pData->m_Data;
        const vfc::float32_t l_value = l_value_rad.value();
        if( false == isZero(m_steeringWheelAngle - l_value) )
        {
            m_steeringWheelAngle = l_value;
        }
    }
}

void Vehicle2DWheels::setupCommonState() // PRQA S 4211
{
    const osg::Vec3 l_positionFL(pc::vehicle::g_mechanicalData->m_wheelbase, pc::vehicle::g_mechanicalData->m_trackFront * 0.5f, g_settings->m_groundLevel);
    m_vehicle2DWheelsFL->setMatrix(osg::Matrix::rotate(m_steeringWheelAngle, osg::Z_AXIS) * osg::Matrix::translate(l_positionFL));

    const osg::Vec3 l_positionFR(pc::vehicle::g_mechanicalData->m_wheelbase, - (pc::vehicle::g_mechanicalData->m_trackFront * 0.5f), g_settings->m_groundLevel);
    m_vehicle2DWheelsFR->setMatrix(osg::Matrix::rotate(m_steeringWheelAngle, osg::Z_AXIS) * osg::Matrix::translate(l_positionFR));
}

} // namespace vehicle2dwheels
} // namespace assets
} // namespace cc 
 
