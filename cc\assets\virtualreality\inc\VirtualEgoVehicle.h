//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualEgoVehicle.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_VIRTUALREALITY_EGOVEHICLE_H
#define CC_ASSETS_VIRTUALREALITY_EGOVEHICLE_H

#include "pc/generic/util/coding/inc/CodingManager.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/virtualreality/inc/VirtualRealityObject.h"

#include <osg/Group>
#include <osg/MatrixTransform>

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace virtualreality
{

class VirtualEgoVehicleSettings : public pc::util::coding::ISerializable
{
public:
  VirtualEgoVehicleSettings()
    : m_vehicleModelFilename("cc/resources/virualReality/vehicle_simplified_ego.osg")
    , m_color(osg::Vec4(0.5f, 0.5f, 0.5f, 1.0f))
  {
  }

  SERIALIZABLE(VirtualEgoVehicleSettings)
  {
    ADD_STRING_MEMBER(vehicleModelFilename);
    ADD_MEMBER(osg::Vec4f, color);
  }

  std::string m_vehicleModelFilename;
  osg::Vec4 m_color;
};

//!
//! VirtualEgoVehicle
//!
class VirtualEgoVehicle : public VirtualRealityObject
{
public:
  VirtualEgoVehicle();

  VirtualEgoVehicle(const VirtualEgoVehicle& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::virtualreality, VirtualEgoVehicle);

  void addObjectNode() override;

protected:
  ~VirtualEgoVehicle();

};



} // namespace virtualreality
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_VIRTUALREALITY_EGOVEHICLE_H
