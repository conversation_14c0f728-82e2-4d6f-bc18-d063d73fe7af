//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  WarnSymbols.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/WarnSymbols.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class WarnSymbolIconType : vfc::uint32_t
{
  WARNSYMBOL_USS_FAILURE_REAR_HORI,
  WARNSYMBOL_USS_FAILURE_FRONT_HORI,
  WARNSYMBOL_USS_FAILURE_WHOLE_HORI,
  WARNSYMBOL_USS_FAILURE_VERT
};

//!
//! @brief Construct a new WarnSymbols Manager:: WarnSymbols Manager object
//!
//! @param f_config
//!
WarnSymbolManager::WarnSymbolManager()
  : m_lastConfigUpdate{~0u}
  , m_ussWarnIcons{}
  , m_mat_b{false}
{
}



WarnSymbolManager::~WarnSymbolManager() = default;

void WarnSymbolManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init WarnSymbols icons
  m_ussWarnIcons.clear(f_imageOverlays);

  m_ussWarnIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathWarnSymbolUssRear, g_uiSettings->m_warnSymbolUssWhole.m_iconCenter, g_uiSettings->m_warnSymbolUssWhole.m_iconSize));
  m_ussWarnIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathWarnSymbolUssFront, g_uiSettings->m_warnSymbolUssWhole.m_iconCenter, g_uiSettings->m_warnSymbolUssWhole.m_iconSize));
  m_ussWarnIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathWarnSymbolUssWhole, g_uiSettings->m_warnSymbolUssWhole.m_iconCenter, g_uiSettings->m_warnSymbolUssWhole.m_iconSize));

  m_ussWarnIcons.addIcon(f_imageOverlays, createIcon(g_uiSettings->m_texturePathWarnSymbolUssVert, g_uiSettings->m_warnSymbolUssVert.m_iconCenter, g_uiSettings->m_warnSymbolUssVert.m_iconSize));

}


void WarnSymbolManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)
{
  if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
  {
      return;
  }
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }

  // ! check the uss warning status
  m_ussWarnIcons.setAllEnabled(false);

  cc::target::common::EThemeTypeHU l_theme    = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
  bool         l_iconShow = false;

  if (f_framework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = f_framework->m_SVSRotateStatusDaddy_Receiver.getData();
    l_theme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899
  }

  if (f_framework->m_pasStatus_ReceiverPort.hasData())
  {
    const cc::daddy::PasStatusDaddy_t* const l_pasStatus = f_framework->m_pasStatus_ReceiverPort.getData();
    const cc::target::common::EPasStatus l_pasSt = l_pasStatus->m_Data;

    if ( (cc::target::common::EPasStatus::PAS_FActiveRFailure == l_pasSt)
      || (cc::target::common::EPasStatus::PAS_RActiveFFailure == l_pasSt)
      || (cc::target::common::EPasStatus::PAS_SystemFailure   == l_pasSt))
    {
      l_iconShow = true;
    }
  }

  if (f_framework->m_sdwStatus_ReceiverPort.hasData())
  {
    const cc::daddy::SdwStatusDaddy_t* const l_sdwStatus = f_framework->m_sdwStatus_ReceiverPort.getData();
    const cc::target::common::ESdwStatus l_sdwSt = l_sdwStatus->m_Data;

    if (cc::target::common::ESdwStatus::SDWSTS_Failure == l_sdwSt)
    {
      l_iconShow = true;
    }
  }

  if (l_iconShow)
  {
    const cc::daddy::PasStatusDaddy_t* const l_pasStatus = f_framework->m_pasStatus_ReceiverPort.getData();
    const cc::target::common::EPasStatus l_pasSt = l_pasStatus->m_Data;
    if (cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT == l_theme)
    {
      m_ussWarnIcons.getIcon(static_cast<unsigned>(WarnSymbolIconType::WARNSYMBOL_USS_FAILURE_VERT))->setEnabled(true);
    }
    else
    {
      switch (l_pasSt)
      {
      case cc::target::common::EPasStatus::PAS_FActiveRFailure:
      {
        m_ussWarnIcons.getIcon(static_cast<unsigned>(WarnSymbolIconType::WARNSYMBOL_USS_FAILURE_REAR_HORI))->setEnabled(true);
        break;
      }
      case cc::target::common::EPasStatus::PAS_RActiveFFailure:
      {
        m_ussWarnIcons.getIcon(static_cast<unsigned>(WarnSymbolIconType::WARNSYMBOL_USS_FAILURE_FRONT_HORI))->setEnabled(true);
        break;
      }
      case cc::target::common::EPasStatus::PAS_SystemFailure:
      {
        m_ussWarnIcons.getIcon(static_cast<unsigned>(WarnSymbolIconType::WARNSYMBOL_USS_FAILURE_WHOLE_HORI))->setEnabled(true);
        break;
      }
      default:
      {
        break;
      }
      }
    }
  }

}


//!
//! @brief Construct a new WarnSymbols:: WarnSymbols object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
WarnSymbols::WarnSymbols(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_manager{}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

  //! render order
  //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
  constexpr vfc::uint32_t l_renderOrder = 11u;
  cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder);

}


WarnSymbols::~WarnSymbols() = default;


void WarnSymbols::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc

