//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Vehicle2D.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_COMMON_VEHICLE2D_H
#define CC_ASSETS_COMMON_VEHICLE2D_H

#include "pc/svs/core/inc/Asset.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/common/inc/Vehicle.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace common
{

class CustomVehicleModel2DSettings;
extern pc::util::coding::Item<CustomVehicleModel2DSettings> g_model2d;

//======================================================
// CustomVehicleModel2D
//------------------------------------------------------
/// Customer-specific 2D (plan/top-view) vehicle model
/// Encompasses the custom 2D model nodes (components) and manages all behaviour,
/// logic and changes related to them. It is a descendant of the osg::MatrixTransform class.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup vehicle
//======================================================
class CustomVehicleModel2D : public pc::vehiclemodel::VehicleModel
{
  public:

    enum CustomComponent2D : unsigned int
    {
      //! Night Parts Only
      //! body
      BODY_IMPOSTOR_DARK              , //! body without wheels,doors and trunk. Dark version

      //! door
      FRONT_LEFT_DOOR_IMPOSTOR_DARK   , //! left door. Dark version
      FRONT_RIGHT_DOOR_IMPOSTOR_DARK  , //! right door. Dark version
      REAR_LEFT_DOOR_IMPOSTOR_DARK    , //! rear left door. Dark version
      REAR_RIGHT_DOOR_IMPOSTOR_DARK   , //! rear right door. Dark version

      //! roof, trunk, mirror, full
      ROOF_IMPOSTOR_DARK              , //! roof. Dark version
      FRONT_LEFT_DOOR_MIRROR_IMPOSTOR_DARK, //! left side mirror. Dark version
      FRONT_RIGHT_DOOR_MIRROR_IMPOSTOR_DARK, //! right side mirror. Dark version
      FULL_CAR_IMPOSTOR_DARK          , //! Full car without wheels. Dark version

      //! wheels
      FRONT_LEFT_WHEEL_IMPOSTOR_DARK  , //! front left wheel. Dark version
      FRONT_RIGHT_WHEEL_IMPOSTOR_DARK , //! front right wheel. Dark version
      REAR_LEFT_WHEEL_IMPOSTOR_DARK   , //! rear left wheel. Dark version
      REAR_RIGHT_WHEEL_IMPOSTOR_DARK  , //! rear right wheel. Dark version

      //! trunk
      TRUNK_IMPOSTOR_DARK                   , //! trunk. Dark version

      NUM_CUST_COMPONENTS_2D            //! Keep this the last entry!
    };

    enum Component2DNodeMask : unsigned int
    {
      //! Night Parts
      NIGHT_BODY_IMPOSTOR_MASK             = 1u << 0u,
      NIGHT_DOORS_IMPOSTOR_MASK            = 1u << 1u,
      NIGHT_WHEELS_IMPOSTOR_MASK           = 1u << 2u,
      NIGHT_ROOF_IMPOSTOR_MASK             = 1u << 3u,
      NIGHT_TRUNK_IMPOSTOR_MASK            = 1u << 4u,
      NIGHT_FULL_CAR_IMPOSTOR_MASK         = 1u << 5u
    };

    enum CullSetting2D : unsigned int
    {
      //! All nodes that belong to the dynamic night impostor
      NIGHT_DYNAMIC_PARTS_IMPOSTOR   =  NIGHT_BODY_IMPOSTOR_MASK   |
                                        NIGHT_DOORS_IMPOSTOR_MASK  |
                                        NIGHT_ROOF_IMPOSTOR_MASK   |
                                        NIGHT_TRUNK_IMPOSTOR_MASK  ,

      //! All nodes that belong to the night impostor (also static one)
      NIGHT_ALL_NODES = NIGHT_DYNAMIC_PARTS_IMPOSTOR | NIGHT_FULL_CAR_IMPOSTOR_MASK | NIGHT_WHEELS_IMPOSTOR_MASK
    };

    CustomVehicleModel2D(const std::string& f_fileName, pc::core::Framework* f_framework = nullptr);

    CustomVehicleModel2D(const CustomVehicleModel2D& f_other, const osg::CopyOp& f_copyOp);

    // META_Node(cc::assets::common, CustomVehicleModel2D);  // PRQA S 2504

    virtual void reset() override;

    osg::Node* getComponent(CustomComponent2D f_id, bool f_suppressErrorMessage = false);

private:

    std::array<osg::ref_ptr<osg::Node>, NUM_CUST_COMPONENTS_2D> m_customComponents2D;

};


//======================================================
// Vehicle2D
//------------------------------------------------------
/// 2D Vehicle asset
/// This class is responsible for comprising all 2D vehicle model features
/// into a single vehicle asset which can be attached to the scene and
/// rendered as a part thereof.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup vehicle
//======================================================
class Vehicle2D : public pc::core::Asset
{
public:

    Vehicle2D(cc::core::AssetId f_assetId, pc::core::Framework* f_framework);

};

} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_VEHICLE2D_H

