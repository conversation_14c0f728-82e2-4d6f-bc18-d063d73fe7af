//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileOverlay.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_TILEOVERLAY_TILEOVERLAY_H
#define CC_ASSETS_TILEOVERLAY_TILEOVERLAY_H

#include "pc/svs/core/inc/AsyncNode.h"

#include "pc/svs/util/math/inc/Box2D.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/Polygon2D.h"

#include <osg/MatrixTransform>
#include <osg/observer_ptr>

#include <vector>

#define DISABLE_ROTATABLE_TRANSPARENT_MODEL 0

//! forward declarations
namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace tileoverlay
{

typedef pc::util::math::LinearInterpolator<float> HeightInterpolator;
extern HeightInterpolator g_heightInterpolator;

enum EObjMovingSts : unsigned int
{
  OBJ_MOVING_UNKNOW               = 0u,
  OBJ_MOVING_NORMAL               = 1u,
  OBJ_MOVING_HYSTERESIS_LV1       = 2u,
  OBJ_MOVING_HYSTERESIS_LV2       = 3u,
  OBJ_MOVING_HYSTERESIS_LV3       = 4u
};

//======================================================
// TileSectorData
//------------------------------------------------------
/// Get the USS related data
/// <AUTHOR>
//======================================================
class TileSectorData // The distances are measured around the car along the sectors.
{
public:
  TileSectorData();
  // ~TileSectorData();

  osg::Vec2f m_leftBorderRefPoint;
  osg::Vec2f m_leftBorderRefPointEnd;
  osg::Vec2f m_leftBorderDir;
  float      m_currentDistance;
  float      m_currentDistanceForPosDisp;
  float      m_previousDistance;
  EObjMovingSts m_ObjMovingSts;

};


//======================================================
// TileOverlayComposite
//------------------------------------------------------
/// Composite all the overlay
/// <AUTHOR>
//======================================================
class TileOverlayComposite : public osg::MatrixTransform
{
public:

  TileOverlayComposite(pc::core::Framework* f_framework = nullptr, bool f_isPerspective = false);
  TileOverlayComposite(const TileOverlayComposite& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::tileoverlay, TileOverlayComposite);  // PRQA S 2504

  void setFamework(pc::core::Framework* f_framework)
  {
    m_framework = f_framework;
  }

  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

  virtual ~TileOverlayComposite();

private:

  //! Copy constructor is not permitted.
  TileOverlayComposite (const TileOverlayComposite& other); // = delete
  //! Copy assignment operator is not permitted.
  TileOverlayComposite& operator=(const TileOverlayComposite& other); // = delete
  
  pc::core::Framework* m_framework;
  unsigned int m_idxShadow;
  unsigned int m_idxSpline2D;
  unsigned int m_idxShield3D;
  int m_renderBinOrder;

};


//======================================================
// TileOverlay
//------------------------------------------------------
/// Displays the USS object distance in each sector
/// <AUTHOR>
//======================================================
class TileOverlay : public pc::core::AsyncNode
{
public:

  TileOverlay(pc::core::Framework* f_framework, bool f_isVehOffset, bool f_isPerspective);

protected:

  virtual ~TileOverlay();

private:
  //! Copy constructor is not permitted.
  TileOverlay (const TileOverlay& other); // = delete
  //! Copy assignment operator is not permitted.
  TileOverlay& operator=(const TileOverlay& other); // = delete

};

void updateInterpolator();
bool convergeDistance(float& f_currentDistance, float f_targetDistance);

} // namespace tileoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TILEOVERLAY_TILEOVERLAY_H