//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  FloorPlateSm.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_COMMON_FLOORPLATESM_H
#define CC_ASSETS_COMMON_FLOORPLATESM_H

#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/texfloor/odometry/inc/Pose.h"
#include <iomanip>
#include <sstream>

namespace cc
{
namespace assets
{
namespace common
{

//!
//! getCurrentRelativeToPrevious
//! Computes and returns the relative pose of the current position with respect to the last position
//!
pc::texfloor::odometry::Pose getCurrentRelativeToPrevious(const pc::texfloor::odometry::Pose& f_currentPose, const pc::texfloor::odometry::Pose& f_lastPose);

//!
//! getPreviousRelativeToCurrent
//! Computes and returns the relative pose of the last position with respect to the current position
//!
pc::texfloor::odometry::Pose getPreviousRelativeToCurrent(const pc::texfloor::odometry::Pose& f_currentPose, const pc::texfloor::odometry::Pose& f_lastPose);

//!
//! StbSmData
//!
class StbSmData : public pc::util::coding::ISerializable
{
public:

  StbSmData()
    : m_minDrivenDistX(6.0f)
    , m_minDrivenDistY(1.5f)
    , m_speedTrigVehTrans_lower_kph(0.0f)
    , m_speedTrigVehTrans_upper_kph(15.0f)
  {
  }

  SERIALIZABLE(StbSmData)
  {
    ADD_FLOAT_MEMBER(minDrivenDistX);
    ADD_FLOAT_MEMBER(minDrivenDistY);
    ADD_FLOAT_MEMBER(speedTrigVehTrans_lower_kph);
    ADD_FLOAT_MEMBER(speedTrigVehTrans_upper_kph);
  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "StbSmData:"
                           << "\n    m_minDrivenDistX: "              << m_minDrivenDistX
                           << "\n    m_minDrivenDistY: "              << m_minDrivenDistY
                           << "\n    m_speedTrigVehTrans_lower_kph: " << m_speedTrigVehTrans_lower_kph
                           << "\n    m_speedTrigVehTrans_upper_kph: " << m_speedTrigVehTrans_upper_kph
                           << std::endl;  // PRQA S 3803
    return l_stream.str();
  }

  float    m_minDrivenDistX;
  float    m_minDrivenDistY;
  float    m_speedTrigVehTrans_lower_kph;
  float    m_speedTrigVehTrans_upper_kph;

};

extern pc::util::coding::Item<StbSmData> g_stbSmData;

//!
//! Logging parameters for debug
//!
class StbSmLogSettings : public pc::util::coding::ISerializable
{
public:
  StbSmLogSettings()
  : m_logGbcCcf(false)
  , m_logStbOdoCam(false)
  , m_logBlurOdoCam(false)
  , m_logTranspOdocam(false)
  , m_logGbcPivi(false)
  , m_logGbcPdm(false)
  {
  }

  SERIALIZABLE(StbSmLogSettings)
  {
    ADD_BOOL_MEMBER(logGbcCcf);
    ADD_BOOL_MEMBER(logStbOdoCam);
    ADD_BOOL_MEMBER(logBlurOdoCam);
    ADD_BOOL_MEMBER(logTranspOdocam);
    ADD_BOOL_MEMBER(logGbcPivi);
    ADD_BOOL_MEMBER(logGbcPdm);
  }

  bool m_logGbcCcf;
  bool m_logStbOdoCam;
  bool m_logBlurOdoCam;
  bool m_logTranspOdocam;
  bool m_logGbcPivi;
  bool m_logGbcPdm;
};

extern pc::util::coding::Item<StbSmLogSettings> g_stbSmLogSettings;

//!
//! BlurSmData
//!
class BlurSmData : public pc::util::coding::ISerializable
{
public:

  BlurSmData()
    : m_blurDurnThreshold_ms(660u)   // 20 ticks * 33ms cycle time
    , m_deblurDurnThreshold_ms(2640u) // 20 ticks * 33ms cycle time
    , m_anim2blurDuration(3000.0f)     // 3000ms -> 3sec
    , m_anim2normalDuration(3000.0f)   // 3000ms -> 3sec
    , m_blurredStateAlpha(0.0f)
    , m_blurLevelDuringDriving(1.0f)
    , m_movingTooFastThreshold_Kmh(30.0f)
  {
  }

  SERIALIZABLE(BlurSmData)
  {
    ADD_UINT32_MEMBER(blurDurnThreshold_ms);
    ADD_UINT32_MEMBER(deblurDurnThreshold_ms);
    ADD_FLOAT_MEMBER(anim2blurDuration);
    ADD_FLOAT_MEMBER(anim2normalDuration);
    ADD_FLOAT_MEMBER(blurredStateAlpha);
    ADD_FLOAT_MEMBER(blurLevelDuringDriving);
    ADD_FLOAT_MEMBER(movingTooFastThreshold_Kmh);
  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "BlurSmData:"
                           << "\n    m_blurDurnThreshold_ms:   " << m_blurDurnThreshold_ms
                           << "\n    m_deblurDurnThreshold_ms: " << m_deblurDurnThreshold_ms
                           << "\n    m_anim2blurDuration:      " << m_anim2blurDuration
                           << "\n    m_anim2normalDuration:    " << m_anim2normalDuration
                           << "\n    m_blurredStateAlpha:      " << m_blurredStateAlpha
                           << "\n    m_blurLevelDuringDriving: " << m_blurLevelDuringDriving
                           << "\n    m_movingTooFastThreshold_Kmh: " << m_movingTooFastThreshold_Kmh << std::endl;  // PRQA S 3803
    return l_stream.str();
  }

  unsigned int m_blurDurnThreshold_ms;
  unsigned int m_deblurDurnThreshold_ms;
  float m_anim2blurDuration;
  float m_anim2normalDuration;
  float m_blurredStateAlpha;
  float m_blurLevelDuringDriving;
  float m_movingTooFastThreshold_Kmh;
};

extern pc::util::coding::Item<BlurSmData> g_blurSmData;

//!
//! TranspSmData
//!
class TranspSmData : public pc::util::coding::ISerializable
{
public:

  TranspSmData()
    : m_transpDurnThreshold_ms(660u)               // 20 ticks * 33ms cycle time
    , m_opaqueDurnThreshold_ms(2640u)               // 20 ticks * 33ms cycle time
    , m_transition2opaqueDuration(3000.0f)         // 3000ms -> 3sec
    , m_transition2transpDuration(3000.0f)         // 3000ms -> 3sec
    , m_transpDurnThreshold_Parking_ms(660u)       // 20 ticks * 33ms cycle time
    , m_opaqueDurnThreshold_Parking_ms(19800u)     // 600 ticks * 33ms cycle time
    , m_transition2opaqueDuration_Parking(3000.0f) // 3000ms -> 3sec
    , m_transition2transpDuration_Parking(3000.0f) // 3000ms -> 3sec
    , m_minDrivenDistX(6.0f)
    , m_minDrivenDistY(1.5f)
    , m_vehicleOpacity(0.7f) //0.0 is transparency for vehicle 2D
    , m_wheelOpacity(0.2f)
  {
  }

  SERIALIZABLE(TranspSmData)
  {
    ADD_UINT32_MEMBER(transpDurnThreshold_ms);
    ADD_UINT32_MEMBER(opaqueDurnThreshold_ms);
    ADD_FLOAT_MEMBER(transition2opaqueDuration);
    ADD_FLOAT_MEMBER(transition2transpDuration);
    ADD_UINT32_MEMBER(transpDurnThreshold_Parking_ms);
    ADD_UINT32_MEMBER(opaqueDurnThreshold_Parking_ms);
    ADD_FLOAT_MEMBER(transition2opaqueDuration_Parking);
    ADD_FLOAT_MEMBER(transition2transpDuration_Parking);
    ADD_FLOAT_MEMBER(minDrivenDistX);
    ADD_FLOAT_MEMBER(minDrivenDistY);
    ADD_FLOAT_MEMBER(vehicleOpacity);
    ADD_FLOAT_MEMBER(wheelOpacity);
  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "TranspSmData:"
                           << "\n    m_transpDurnThreshold_ms:            " << m_transpDurnThreshold_ms
                           << "\n    m_opaqueDurnThreshold_ms:            " << m_opaqueDurnThreshold_ms
                           << "\n    m_transition2opaqueDuration:         " << m_transition2opaqueDuration
                           << "\n    m_transition2transpDuration:         " << m_transition2transpDuration
                           << "\n    m_transpDurnThreshold_Parking_ms:    " << m_transpDurnThreshold_Parking_ms
                           << "\n    m_opaqueDurnThreshold_Parking_ms:    " << m_opaqueDurnThreshold_Parking_ms
                           << "\n    m_transition2opaqueDuration_Parking: " << m_transition2opaqueDuration_Parking
                           << "\n    m_transition2transpDuration_Parking: " << m_transition2transpDuration_Parking
                           << "\n    m_minDrivenDistX:                    " << m_minDrivenDistX
                           << "\n    m_minDrivenDistY:                    " << m_minDrivenDistY
                           << "\n    m_vehicleOpacity:                    " << m_vehicleOpacity
                           << "\n    m_wheelOpacity:                      " << m_wheelOpacity << std::endl;  // PRQA S 3803
    return l_stream.str();
  }



  unsigned int m_transpDurnThreshold_ms;
  unsigned int m_opaqueDurnThreshold_ms;
  float m_transition2opaqueDuration;
  float m_transition2transpDuration;
  unsigned int m_transpDurnThreshold_Parking_ms;
  unsigned int m_opaqueDurnThreshold_Parking_ms;
  float m_transition2opaqueDuration_Parking;
  float m_transition2transpDuration_Parking;

  float m_minDrivenDistX;
  float m_minDrivenDistY;
  float m_vehicleOpacity; // range [0..1]; 0: vehicle transparent; 1: vehicle opaque
  float m_wheelOpacity;   // range [0..1]; 0: wheel transparent; 1: wheel opaque
};

extern pc::util::coding::Item<TranspSmData> g_transpSmData;

//======================================================
// StbMainCallback
//------------------------------------------------------
/// This is the main callback class for the See Through Bonnet (STB)
/// State Machine which controls the textured/ default grey behavior of the FloorPlate in STB view.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup FloorPlate
//======================================================
class StbMainCallback
{

public:

enum EStbState {
  STB_INIT = 0,
  STB_OFF  = 1,
  STB_ON   = 2
};

  StbMainCallback( cc::core::CustomFramework* f_pFramework );

  void step(bool f_doorsClosed, bool f_camWorking, bool f_odoWorking);
  bool isPlateOn();

protected:

  virtual ~StbMainCallback();

private:
  //! Copy constructor is not permitted.
  StbMainCallback (const StbMainCallback& other);
  //! Copy assignment operator is not permitted.
  StbMainCallback& operator=(const StbMainCallback& other);

private:
  EStbState m_state;
  cc::core::CustomFramework* m_pFramework;

  pc::texfloor::odometry::Pose m_initialPose;

//! Members for degradation
  bool m_doorsClosed;
  bool m_camWorking;
  bool m_odoWorking;

  bool m_plateOn;

  void goToOff();
  void goToOn();
  bool inDegradation();
  bool inReverseGear();
  bool inRollingBack();
  bool minDistanceReached();

};


//======================================================
// BlurrinessMainCallback
//------------------------------------------------------
/// This is the main callback class for the FloorPlate Blurriness State Machine
/// which controls the different blurriness levels and corresponding blurriness animation
/// for the FloorPlate of the Glass Bottom Car (GBC) feature.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup FloorPlate
//======================================================
class BlurrinessMainCallback
{

public:

enum EBlurState {
  INIT_BLUR    = 0,
  NORMAL       = 1,
  ANIM2BLURRED = 2,
  BLURRED      = 3,
  ANIM2NORMAL  = 4
};


  BlurrinessMainCallback( cc::core::CustomFramework* f_pFramework );

  void step(bool f_doorsClosed, bool f_camWorking, bool f_odoWorking);
  bool isPlateOn();
  float getBlurLevel();
  void setCcfEnabled(bool f_ccfEnabled){m_ccfEnabled = f_ccfEnabled;}

protected:

  virtual ~BlurrinessMainCallback();

private:
  //! Copy constructor is not permitted.
  BlurrinessMainCallback (const BlurrinessMainCallback& other);
  //! Copy assignment operator is not permitted.
  BlurrinessMainCallback& operator=(const BlurrinessMainCallback& other);

private:
  cc::core::CustomFramework* m_pFramework;
  EBlurState m_state;
  pc::texfloor::odometry::Pose m_initialPose;
  unsigned int m_cycleTime_ms;
  unsigned int m_blurCounter;
  unsigned int m_deblurCounter;
  unsigned int m_anim2blurCounter;
  unsigned int m_anim2normalCounter;
  bool m_plateOn;
  float m_blurLevel;
  float m_blurLevelDuringDriving;
  float m_anim2blurDelta;
  float m_anim2normalDelta;
  bool m_blurStatus;
  bool m_basePlatePdmWasRead;
  cc::target::common::EPdmSvsSetting m_basePlateStatusInPdm;

//! Members for degradation
  bool m_doorsClosed;
  bool m_camWorking;
  bool m_odoWorking;

//! CCF Enable flag
  bool m_ccfEnabled;

//! The minimum of moving distance is reached
  bool m_minDistanceReached;

  bool inDegradation();
  bool isMovementDirectionStatic();
  bool isMovementTooFast();
  void startAnimation2Blur();
  void startAnimation2Normal();
  void goToBlurred();
  void goToNormal();
  bool isBlurStatusOn();
  void updateBlurStatus();
  bool minDistanceReached();
};




//======================================================
// TransparencyMainCallback
//------------------------------------------------------
/// This is the main callback class for the VehicleModel Transpareny State Machine
/// which controls the different transparency levels and corresponding transparency animation
/// for the VehicleModel of the Glass Bottom Car (GBC) feature.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup FloorPlate
//======================================================
class TransparencyMainCallback
{

public:

  enum ETranspState {
    INIT_TRANSP     = 0,
    OPAQUE          = 1,
    OPAQUE2TRANSP   = 2,
    TRANSPARENT     = 3,
    TRANSP2OPAQUE   = 4
  };

  TransparencyMainCallback( cc::core::CustomFramework* f_pFramework );

  void step(bool f_doorsClosed, bool f_camWorking, bool f_odoWorking);
  float getVehOpacityLevel();
  float getWhlOpacityLevel();

  void setCcfEnabled(bool f_ccfEnabled){m_ccfEnabled = f_ccfEnabled;}

protected:

  virtual ~TransparencyMainCallback();

private:
  //! Copy constructor is not permitted.
  TransparencyMainCallback (const TransparencyMainCallback& other);
  //! Copy assignment operator is not permitted.
  TransparencyMainCallback& operator=(const TransparencyMainCallback& other);

private:
  cc::core::CustomFramework* m_pFramework;
  ETranspState m_state;
  pc::texfloor::odometry::Pose m_initialPose;
  unsigned int m_opaqueCounter;
  unsigned int m_transpCounter;
  float m_vehtransp2opaqueDelta;
  float m_vehopaque2transpDelta;
  float m_whltransp2opaqueDelta;
  float m_whlopaque2transpDelta;
  float m_vehtransp2opaqueDelta_Parking;
  float m_vehopaque2transpDelta_Parking;
  float m_whltransp2opaqueDelta_Parking;
  float m_whlopaque2transpDelta_Parking;
  float m_vehOpacityLevel;
  float m_whlOpacityLevel;
  unsigned int m_cycleTime_ms;
  bool m_gbcStatus;
  bool m_pdmWasRead;
  cc::target::common::EPdmSvsSetting m_GbcStatusInPdm;

//! Members for degradation
  bool m_doorsClosed;
  bool m_camWorking;
  bool m_odoWorking;

//! CCF Enable flag
  bool m_ccfEnabled;

//! The minimum of moving distance is reached
  bool m_minDistanceReached;

  bool inDegradation();
  bool updateInitialPosition();
  bool minDistanceReached();
  bool isMovementDirectionStatic();
  void startTransition2Transparent();
  void startTransition2Opaque();
  void goToOpaque(bool updatePos = false);  // By default do not update Odometry initial position
  void goToTransp();
  bool isOverlayStatusOn();
  bool isGBCStatusOn();
  bool trailerBasedDegradation();
  void updateGbcStatus();

};


//======================================================
// FloorPlateMainCallback
//------------------------------------------------------
/// This is the main callback class that controls all the related State Machines
/// concerned to transparency changes and blurriness changes for FloorPlates
/// and the VehicleModel in the Plan view.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup FloorPlate
//======================================================
class FloorPlateMainCallback : public osg::NodeCallback
{

public:

  FloorPlateMainCallback( osg::Uniform* f_pOnOffToggleUniform,
                          osg::Uniform* f_pBlurringMixUniform,
                          cc::core::CustomFramework* f_pFramework );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:

  virtual ~FloorPlateMainCallback();

  bool allDoorsClosed();

private:
  //! Copy constructor is not permitted.
  FloorPlateMainCallback (const FloorPlateMainCallback& other);
  //! Copy assignment operator is not permitted.
  FloorPlateMainCallback& operator=(const FloorPlateMainCallback& other);

private:

  bool m_gbcViewEnabled;

//! Individual callbacks for the different View/Assets related to FloorPlate
  StbMainCallback             *m_pStb;
  BlurrinessMainCallback      *m_pBlur;
  TransparencyMainCallback    *m_pTransp;

//! Floor Plate Uniforms
  osg::Uniform* m_pOnOffToggleUniform;
  osg::Uniform* m_pBlurringMixUniform;

//! ViewBufferStatus: STB Signal needed by ViewModeStateMachine for communication with PIVI
  bool m_viewBufferStatusForPivi;
  cc::core::CustomFramework* m_pFramework;
};


} // namespace common
} // namespace assets
} // namespace cc





#endif // CC_ASSETS_COMMON_FLOORPLATESM_H
