//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SettingBar.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_SETTINGBAR_H
#define CC_ASSETS_UIELEMENTS_SETTINGBAR_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{


//!
//! SettingBarManager
//!
class SettingBarManager
{
public:

  SettingBarManager(cc::core::CustomFramework* f_customFramework);

  virtual ~SettingBarManager();

  void init(pc::assets::ImageOverlays* f_imageOverlays);

  void update(pc::assets::ImageOverlays* f_imageOverlays);

  void deliver();

private:

  //! Copy constructor is not permitted.
  SettingBarManager (const SettingBarManager& other); // = delete
  //! Copy assignment operator is not permitted.
  SettingBarManager& operator=(const SettingBarManager& other); // = delete

  unsigned int m_lastConfigUpdate;

  pc::assets::IconGroup m_settingBarIcons;
  cc::core::CustomFramework* m_customFramework;

  cc::target::common::EPARKStatusR2L m_apatatus;
  ESVSViewMode m_requestViewMode;
  EScreenID m_requestViewId;
  pc::daddy::EGear m_gear;
  cc::target::common::EParkActiveStatus m_parkActiveStatus;
  bool m_transparentStatus;

};


//!
//! SettingBar
//!
class SettingBar: public cc::assets::uielements::CustomImageOverlays
{
public:

  SettingBar(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);

  virtual ~SettingBar();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  //! Copy constructor is not permitted.
  SettingBar (const SettingBar& other); // = delete
  //! Copy assignment operator is not permitted.
  SettingBar& operator=(const SettingBar& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  SettingBarManager m_manager;


};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_SETTINGBAR_H
