//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PARKVIEWRENDERMANAGER_H
#define CC_ASSETS_PARKVIEWRENDERMANAGER_H

#include "pc/svs/factory/inc/RenderManager.h"

namespace cc
{
namespace assets
{

class ParkViewRenderManager : public pc::factory::RenderManager
{
public:

  ParkViewRenderManager(
    pc::factory::RenderManagerRegistry* f_registry,
    cc::virtcam::VirtualCamEnum f_virtCam = cc::virtcam::NUMBER_OF_VIRT_CAMS);

  void showEnvironment();

  void hideEnvironment();

protected:

  virtual ~ParkViewRenderManager();

  virtual void updateInputData(const osg::FrameStamp* f_frameStamp);

private:

  //! Copy constructor is not permitted.
  ParkViewRenderManager (const ParkViewRenderManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ParkViewRenderManager& operator=(const ParkViewRenderManager& other); // = delete

  bool m_hideEnvironment;

};

} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PARKVIEWRENDERMANAGER_H