//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.

#ifndef CC_ASSETS_UIELEMENTS_VIEWINFOOVERLAY_H
#define CC_ASSETS_UIELEMENTS_VIEWINFOOVERLAY_H

#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"

namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

//!
//! ViewInfoOverlayManager
//!
class ViewInfoOverlayManager
{
public:
    ViewInfoOverlayManager();
    virtual ~ViewInfoOverlayManager();

    void init(pc::assets::ImageOverlays* f_imageOverlays);
    void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
    void update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);

private:
    //! Copy constructor is not permitted.
    ViewInfoOverlayManager(const ViewInfoOverlayManager& other); // = delete
    //! Copy assignment operator is not permitted.
    ViewInfoOverlayManager& operator=(const ViewInfoOverlayManager& other); // = delete
    bool      m_hasAndroidError ;
    bool      m_hasCalibError ;
    bool      m_hasExtrinsicFileError ;
    bool      m_vhmDegrated;
    bool      m_hasCameraError;
    unsigned int          m_lastConfigUpdate;
    pc::assets::IconGroup m_viewInfoIcons;
    pc::assets::IconGroup m_backBackgroundLines;
    pc::assets::IconGroup m_floatScreenCornerTextures;
    pc::assets::IconGroup m_fullScreenCornerTextures;
};

//!
//! ViewInfoOverlays
//!
class ViewInfoOverlays : public cc::assets::uielements::CustomImageOverlays
{
public:
    ViewInfoOverlays(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
    virtual ~ViewInfoOverlays();

    virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
    //! Copy constructor is not permitted.
    ViewInfoOverlays(const ViewInfoOverlays& other); // = delete
    //! Copy assignment operator is not permitted.
    ViewInfoOverlays& operator=(const ViewInfoOverlays& other); // = delete

    cc::core::CustomFramework* m_customFramework;
    ViewInfoOverlayManager     m_manager;
};

} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_VIEWINFOOVERLAY_H
