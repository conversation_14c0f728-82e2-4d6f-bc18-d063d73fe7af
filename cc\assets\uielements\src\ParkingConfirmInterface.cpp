//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ParkingConfirmInterface.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/ParkingConfirmInterface.h"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/assets/uielements/inc/Utils.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace parkconfirminterface
{

pc::util::coding::Item<ParkingConfirmInterfaceSettings> g_parkingConfirmInterfaceSettings("ParkingConfirmInterface");

// ! enum values for parking modes
enum ParkingConfirmInterfaceType : vfc::uint32_t
{
  PARKING_SEARCHING_POP_OUT_WINDOW,
  PARKING_SEARCHING_PRESS_PARKING_SWITCH,
  PARKING_SEARCHING_RPA_PRESS_PARKING_SWITCH,
  PARKING_SEARCHING_BUCKLE_SEAT_BELT,
  PARKING_SEARCHING_PRESS_BRAKE_PEDAL,
  PARKING_SEARCHING_CLOSE_DOOR,
  PARKING_SEARCHING_CLOSE_TRUNK,
  PARKING_SEARCHING_APA_PARK_OUT,
  PARKING_SEARCHING_APA_PARK_OUT_PRESS_PARKING_SWITCH,
  PARKING_SEARCHING_APA_PARK_OUT_BUCKLE_SEAT_BELT,
  PARKING_SEARCHING_APA_PARK_OUT_PRESS_BRAKE_PEDAL,
  PARKING_SEARCHING_APA_PARK_OUT_CLOSE_DOOR,
  PARKING_SEARCHING_APA_PARK_OUT_CLOSE_TRUNK,
  PARKING_SEARCHING_RPA_Sketch,
  PARKING_SEARCHING_RPA_Checklist,
  PARKING_SEARCHING_APA_UNSELECTED,
  PARKING_SEARCHING_RPA_UNSELECTED,
  PARKING_SEARCHING_RPA_Check,
  PARKING_SEARCHING_RPA_Closetrunk,
  PARKING_SEARCHING_RPA_ConnectBT,
  PARKING_SEARCHING_RPA_CloseDoor,
  PARKING_SEARCHING_RPA_GeartoP,
  PARKING_SEARCHING_RPA_EbrakeOn,
  PARKING_SEARCHING_RPA_LeaveCar,
  PARKING_CONFIRM_APA_ACTIVE,
  PARKING_CONFIRM_APA_INACTIVE,
  PARKING_CONFIRM_APA_PARK_OUT_ACTIVE,
  PARKING_CONFIRM_APA_PARK_OUT_INACTIVE,
  PARKING_CONFIRM_RPA_ACTIVE,
  PARKING_CONFIRM_RPA_INACTIVE,
  PARKING_CONFIRM_RPA_TITLE,
  PARKING_CONFIRM_PARK_TYPE,
  PARKING_CONFIRM_EXIT,
  PARKING_CONFIRM_SEPERATOR,
  PARKING_CONFIRM_RPA_SEPERATOR,
};

//!
//! @brief Construct a new ParkingConfirmInterface Manager:: ParkingConfirmInterface Manager object
//!
//! @param f_config
//!
ParkingConfirmInterfaceManager::ParkingConfirmInterfaceManager()
  : m_lastConfigUpdate{~0u}
  , m_settingParkConfirmInterface{}
{
}

// ! transfer to start from bottom left
osg::Vec2f ParkingConfirmInterfaceManager::transferToBottomLeft(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(f_iconPos.x() - static_cast<vfc::float32_t>(cc::core::g_views->m_usableCanvasViewport.m_origin.x()), static_cast<vfc::float32_t>(cc::core::g_views->m_usableCanvasViewport.m_size.y()) - f_iconPos.y());
}

ParkingConfirmInterfaceManager::~ParkingConfirmInterfaceManager() = default;

void ParkingConfirmInterfaceManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init icons
  m_settingParkConfirmInterface.clear(f_imageOverlays);
  //  variant RPA/APA-----------------------------------------------------------------
  // | ----------------------------------|        | ----------------------------------|
  // | --------|             |-----------|        | --------|     RPA     |-----------|
  // | --------|   P     P   |-----------| choose | --------|  1 2 3 4    |-----------|
  // | --------|  RPA   APA  |-----------|  RPA   | --------|     exit    |-----------|
  // | ----------------------------------|        | ----------------------------------|
  // |-----------------------------------|        |-----------------------------------|
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkPopOutBox,             transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKPopOutBox.m_iconCenter),       uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkPopOutBox)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxPressParkingSwitch, transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKAPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxPressParkingSwitch)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPApressParkSwitch,    transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPApressParkSwitch)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxBuckleSeatBelt,     transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKAPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxBuckleSeatBelt)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkBoxPressBrakePedal,    transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKAPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkBoxPressBrakePedal)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseDoor,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKAPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseDoor)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseTrunk,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKAPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseTrunk)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkAPAUnselected,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKConfirmTitle.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkAPAUnselected)));

  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxPressParkingSwitch, transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirmText.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxPressParkingSwitch)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxBuckleSeatBelt,     transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirmText.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxBuckleSeatBelt)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkBoxPressBrakePedal,    transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirmText.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkBoxPressBrakePedal)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseDoor,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirmText.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseDoor)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseTrunk,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirmText.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathTextBoxCloseTrunk)));

  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPASketch,             transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPASketch.m_iconCenter),       uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPASketch)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAChecklist,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPAChecklist.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAChecklist)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkAPAUnselected,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAConfirmText.m_iconCenter),  uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkAPAUnselected)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAUnselected,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkRPAConfirmText.m_iconCenter),  uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAUnselected)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPACheck,              transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKConfirmTitle.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPACheck)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAClosetrunk,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAClosetrunk)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAConnectBT,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAConnectBT)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPACloseDoor,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPACloseDoor)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAGeartoP,            transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAGeartoP)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAEbrakeOn,           transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAEbrakeOn)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPALeaveCar,           transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPADrvReqInd.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPALeaveCar)));

  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIcon,               transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAConfirm.m_iconCenter),             uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIcon)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIconOFF,            transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAConfirm.m_iconCenter),             uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIconOFF)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIcon,               transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirm.m_iconCenter),      uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIcon)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIconOFF,            transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkAPAParkOutConfirm.m_iconCenter),      uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathAPAParkIconOFF)));

  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathRPAParkIcon,               transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkRPAConfirm.m_iconCenter),             uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathRPAParkIcon)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathRPAParkIconOFF,            transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingParkRPAConfirm.m_iconCenter),             uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathRPAParkIconOFF)));

  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAUnselected,         transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKConfirmTitle.m_iconCenter),           uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathParkRPAUnselected)));

  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathConfirmParkType,           transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKConfirmTitle.m_iconCenter),           uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathConfirmParkType)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathConfirmExit,               transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKConfirmExit.m_iconCenter),            uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathConfirmExit)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathConfirmSeparator,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKConfirmSeparator.m_iconCenter),       uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathConfirmSeparator)));
  m_settingParkConfirmInterface.addIcon(f_imageOverlays, uielements::createIcon(g_parkingConfirmInterfaceSettings->m_texturePathConfirmSeparator,          transferToBottomLeft(g_parkingConfirmInterfaceSettings->m_settingPARKRPAConfirmSeparator.m_iconCenter),    uielements::getImageSize(g_parkingConfirmInterfaceSettings->m_texturePathConfirmSeparator)));
}


void ParkingConfirmInterfaceManager::batchSetInActiveIcons()
{
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_POP_OUT_WINDOW))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_PRESS_PARKING_SWITCH))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_RPA_PRESS_PARKING_SWITCH))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_BUCKLE_SEAT_BELT))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_PRESS_BRAKE_PEDAL))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_CLOSE_DOOR))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_CLOSE_TRUNK))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_APA_PARK_OUT_PRESS_PARKING_SWITCH))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_APA_PARK_OUT_BUCKLE_SEAT_BELT))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_APA_PARK_OUT_PRESS_BRAKE_PEDAL))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_APA_PARK_OUT_CLOSE_DOOR))->setEnabled(false);
  m_settingParkConfirmInterface.getIcon((PARKING_SEARCHING_APA_PARK_OUT_CLOSE_TRUNK))->setEnabled(false);

  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Sketch)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Checklist)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_UNSELECTED)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_UNSELECTED)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_ACTIVE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_INACTIVE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_ACTIVE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_ACTIVE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_TITLE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Check)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Closetrunk)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_ConnectBT)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_CloseDoor)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_GeartoP)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_EbrakeOn)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_LeaveCar)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_PARK_TYPE)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_EXIT)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_SEPERATOR)->setEnabled(false);
  m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(false);
}

void ParkingConfirmInterfaceManager::update(cc::assets::uielements::CustomImageOverlays* f_imageOverlays, core::CustomFramework* f_framework) // PRQA S 6040  // PRQA S 6041 // PRQA S 6043  // PRQA S 6044
{
  if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
  {
      return;
  }
  // ! check if config has changed
  if (g_parkingConfirmInterfaceSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_parkingConfirmInterfaceSettings->getModifiedCount();
  }

  batchSetInActiveIcons();

    // deliver through daddy port
  cc::daddy::ParkConfirmInterfaceExist_t& l_rParkConfirmInterfaceExistDataContainer =
          cc::daddy::CustomDaddyPorts::sm_SVSParkConfirmInterfaceDataDaddy_SenderPort.reserve() ;

  if(f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    const cc::target::common::EPARKDriverIndR2L l_curparkDriverInd = l_parkDriverInd->m_Data;

    switch (l_curparkDriverInd) // PRQA S 3139  #code looks fine
    {
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest:
    {
      l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_UNSELECTED)->setEnabled(true);
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_UNSELECTED)->setEnabled(true);
      m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(true);
      m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(true);
      m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_PARK_TYPE)->setEnabled(true);
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);

      break;
    }
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk:
    {
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
      if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      {
        const cc::daddy::ParkModeDaddy_t* const l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
        const cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;

        if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
        {
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT_CLOSE_TRUNK)->setEnabled(true);
          m_confirmBtnSts = PARKCONFIRM_INACTIVE;
        }
        else
        {
          l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
          if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
          {
            const cc::daddy::ParkTypeSelectedStDaddy_t* const l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
            const cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;
            if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_CLOSE_TRUNK)->setEnabled(true);
              m_confirmBtnSts = PARKCONFIRM_INACTIVE;
            }
            else
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_CLOSE_TRUNK)->setEnabled(false);
            }
          }
        }
      }
      break;
    }
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:
    {
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
      if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      {
        const cc::daddy::ParkModeDaddy_t* const l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
        const cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;

        if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
        {
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT_CLOSE_DOOR)->setEnabled(true);
          m_confirmBtnSts = PARKCONFIRM_INACTIVE;
        }
        else
        {
          l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
          if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
          {
            const cc::daddy::ParkTypeSelectedStDaddy_t* const l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
            const cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;

            if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_CLOSE_DOOR)->setEnabled(true);
              m_confirmBtnSts = PARKCONFIRM_INACTIVE;
            }
            else
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_CLOSE_DOOR)->setEnabled(false);
            }
          }
        }
      }
      break;
    }
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt:
    {
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);

      if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      {
        const cc::daddy::ParkModeDaddy_t* const l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
        const cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;

        if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
        {
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT_BUCKLE_SEAT_BELT)->setEnabled(true);
          m_confirmBtnSts = PARKCONFIRM_INACTIVE;
        }
        else
        {
          l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
          if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
          {
            const cc::daddy::ParkTypeSelectedStDaddy_t* const l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
            const cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;
            if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_BUCKLE_SEAT_BELT)->setEnabled(true);
              m_confirmBtnSts = PARKCONFIRM_INACTIVE;
            }
            else
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_BUCKLE_SEAT_BELT)->setEnabled(false);
            }
          }
        }
      }
      break;
    }
    case cc::target::common::EPARKDriverIndR2L::PARKDRV_PressBrakePedal:
    {
      m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
      if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      {
        const cc::daddy::ParkModeDaddy_t* const l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
        const cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;

        if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
        {
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT_PRESS_BRAKE_PEDAL)->setEnabled(true);
          m_confirmBtnSts = PARKCONFIRM_INACTIVE;
        }
        else
        {
          l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
          if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
          {
            const cc::daddy::ParkTypeSelectedStDaddy_t* const l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
            const cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;
            if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_PRESS_BRAKE_PEDAL)->setEnabled(true);
              m_confirmBtnSts = PARKCONFIRM_INACTIVE;
            }
            else
            {
              m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_PRESS_BRAKE_PEDAL)->setEnabled(false);
            }
          }
        }
      }
      break;
    }
      // case PARKDRV_PressParkingSwitch:
      // case PARKDRV_RepressParkSwitch:
      //   m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
      //   if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      //   {
      //     const cc::daddy::ParkModeDaddy_t* l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
      //     cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;

      //     if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
      //     {
      //       m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
      //       m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
      //       m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT_PRESS_PARKING_SWITCH)->setEnabled(true);
      //       m_confirmBtnSts = PARKCONFIRM_INACTIVE;
      //     }
      //     else
      //     {
      //       l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
      //       if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
      //       {
      //         const cc::daddy::ParkTypeSelectedStDaddy_t* l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
      //         cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;
      //         if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
      //         {
      //           m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_PRESS_PARKING_SWITCH)->setEnabled(true);
      //           m_confirmBtnSts = PARKCONFIRM_INACTIVE;
      //         }
      //         else
      //         {
      //           m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_PRESS_PARKING_SWITCH)->setEnabled(false);
      //         }
      //       }
      //     }
      //   }
      //   break;
      // case PARKDRV_ReleaseParkSwitch:
      //   m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
      //   if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      //   {
      //     const cc::daddy::ParkModeDaddy_t* l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
      //     cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;

      //     if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
      //     {
      //       m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
      //       m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
      //       m_confirmBtnSts = PARKCONFIRM_ACTIVE;
      //     }
      //     else
      //     {
      //       l_rParkConfirmInterfaceExistDataContainer.m_Data = true;
      //       if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
      //       {
      //         const cc::daddy::ParkTypeSelectedStDaddy_t* l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
      //         cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;
      //         if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
      //         {
      //           m_confirmBtnSts = PARKCONFIRM_ACTIVE;
      //         }
      //       }
      //     }
      //   }
      //   break;
    default:
    {
      m_confirmBtnSts = PARKCONFIRM_NOTSHOW;
      l_rParkConfirmInterfaceExistDataContainer.m_Data = false;
      break;
    }
    }
  }

  if(f_framework->m_parkHmiParkingReqReleaseBtnReceiver.hasData())
  {
    const cc::daddy::ParkReqReleaseBtnDaddy_t* const l_parkReqReleaseBtn = f_framework->m_parkHmiParkingReqReleaseBtnReceiver.getData();
    const bool l_curParkReqReleaseBtn = l_parkReqReleaseBtn->m_Data;
    if(l_curParkReqReleaseBtn)
    {
      m_confirmBtnSts = PARKCONFIRM_ACTIVE;
    }
  }

  if(f_framework->m_parkHmiParkingTypeSelectedReceiver.hasData())
  {
    const cc::daddy::ParkTypeSelectedStDaddy_t* const l_parkTypeSelected = f_framework->m_parkHmiParkingTypeSelectedReceiver.getData();
    const cc::target::common::EPARkType l_curparTypeSelected = l_parkTypeSelected->m_Data;
    if(cc::target::common::EPARkType::EPARKTYPE_APA == l_curparTypeSelected)
    {
      if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      {
        const cc::daddy::ParkModeDaddy_t* const l_parkMode = f_framework->m_parkHmiParkingModeReceiver.getData();
        const cc::target::common::EPARKModeR2L l_curparkMode = l_parkMode->m_Data;
        if (l_curparkMode == cc::target::common::EPARKModeR2L::PARKMODE_Park_Out)
        {
          switch (m_confirmBtnSts) // PRQA S 3139  #code looks fine
          {
          case PARKCONFIRM_INACTIVE:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_INACTIVE)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_ACTIVE)->setEnabled(false);

            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            break;
          }
          case PARKCONFIRM_ACTIVE:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_INACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_ACTIVE)->setEnabled(true);

            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_PARK_OUT)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            break;
          }
          default:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_INACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_PARK_OUT_ACTIVE)->setEnabled(false);
            break;
          }
          }
        }
        else
        {
          switch (m_confirmBtnSts) // PRQA S 3139  #code looks fine
          {
          case PARKCONFIRM_INACTIVE:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_ACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_ACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(true);

            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_UNSELECTED)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_UNSELECTED)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_PARK_TYPE)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            break;
          }
          case PARKCONFIRM_ACTIVE:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_ACTIVE)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_ACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(true);

            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_UNSELECTED)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_UNSELECTED)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_PARK_TYPE)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            break;
          }
          default:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(false);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_ACTIVE)->setEnabled(false);
            break;
          }
          }
        }
      }

      m_count = 0u;
    }
    else
    {
      if (m_count < 20u)
      {
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_ACTIVE)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_ACTIVE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_UNSELECTED)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_UNSELECTED)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_PARK_TYPE)->setEnabled(true);
      }
      else
      {
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_INACTIVE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_APA_ACTIVE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_APA_UNSELECTED)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_UNSELECTED)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_INACTIVE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_PARK_TYPE)->setEnabled(false);
        m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Sketch)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Checklist)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_TITLE)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_EXIT)->setEnabled(true);
        m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_SEPERATOR)->setEnabled(true);

        if (l_rParkConfirmInterfaceExistDataContainer.m_Data == false)
        {
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Sketch)->setEnabled(false);
          m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Checklist)->setEnabled(false);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_TITLE)->setEnabled(false);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_EXIT)->setEnabled(false);
          m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_SEPERATOR)->setEnabled(false);
        }
      }
      m_count++;
    }
  }

  if(f_framework->m_parkHmiParkingTypeReceiver.hasData())
  {
  const cc::daddy::ParkTypeDaddy_t* const l_parkTypeInd = f_framework->m_parkHmiParkingTypeReceiver.getData();
  const cc::target::common::EPARKTypeR2L l_curparkTypeInd = l_parkTypeInd->m_Data;
  if( cc::target::common::EPARKTypeR2L::PARKTYPE_RPA == l_curparkTypeInd)
  {
    if(f_framework->m_parkHmiParkDriverIndReceiver.hasData())
    {
      const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
      const cc::target::common::EPARKDriverIndR2L l_curparkDriverInd = l_parkDriverInd->m_Data;

      batchSetInActiveIcons();

      l_rParkConfirmInterfaceExistDataContainer.m_Data = false;
      if (f_framework->m_parkHmiParkingModeReceiver.hasData())
      {
        const cc::daddy::ParkModeDaddy_t* const l_parkModeInd = f_framework->m_parkHmiParkingModeReceiver.getData();
        const cc::target::common::EPARKModeR2L l_curparkModeInd = l_parkModeInd->m_Data;
        if ( l_curparkModeInd == cc::target::common::EPARKModeR2L::PARKMODE_PPSC_Park_In || l_curparkModeInd == cc::target::common::EPARKModeR2L::PARKMODE_CPSC_Park_In)
        {
          switch (l_curparkDriverInd) // PRQA S 3139  #code looks fine
          {
          case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Closetrunk)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Check)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
            break;
          }
          case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:
          { 
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_CloseDoor)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Check)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
            break;
          }
          case cc::target::common::EPARKDriverIndR2L::PARKDRV_ConnectPhone:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_ConnectBT)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Check)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
            break;
          }
          case cc::target::common::EPARKDriverIndR2L::PARKDRV_LeaveCar:
          {
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_LeaveCar)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Check)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
            m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
            break;
          }
          // case PARKDRV_PressParkingSwitch:
          // case PARKDRV_RepressParkSwitch:
          //   m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_PRESS_PARKING_SWITCH)->setEnabled(true);
          //   m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_RPA_Check)->setEnabled(true);
          //   m_settingParkConfirmInterface.getIcon(PARKING_SEARCHING_POP_OUT_WINDOW)->setEnabled(true);
          //   m_settingParkConfirmInterface.getIcon(PARKING_CONFIRM_RPA_SEPERATOR)->setEnabled(true);
          //   break;
          default:
          {
            break;
          }
          }
        }
      }
    }
  }

  }
  cc::daddy::CustomDaddyPorts::sm_SVSParkConfirmInterfaceDataDaddy_SenderPort.deliver() ;
}


//!
//! @brief Construct a new ParkingConfirmInterface:: ParkingConfirmInterface object
//!
//! @param f_customFramework
//! @param f_assetId
//!
ParkingConfirmInterface::ParkingConfirmInterface(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId/*, pc::core::View* f_view*/)
  :  cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
  , m_customFramework{f_customFramework}
  , m_manager{}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


ParkingConfirmInterface::~ParkingConfirmInterface() = default;


void ParkingConfirmInterface::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace parkconfirminterface
} // namespace assets
} // namespace cc

