//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  VehicleTransIcon.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/VehicleTransIcon.h"
#include "CustomSystemConf.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/util/pdmwriter/inc/PdmWriter.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h" // PRQA S 1060
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/osgx/inc/Utils.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/assets/uielements/inc/HmiElementsSettings.h"
#include "cc/assets/uielements/inc/Vehicle2DOverlay.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class VehicleTransIconType : vfc::uint32_t
{
    VEHICLE_TRANS_ICON_FULLSCREEN,
    VEHICLE_TRANS_ICON_FLOATSCREEN
};

pc::util::coding::Item<VehicleTransIconSettings> g_vehicleTransIconSettings("VehicleTransIconSettings");

//!
//! @brief Construct a new VehicleTransIcon Manager:: VehicleTransIcon Manager object
//!
//! @param f_config
//!
VehicleTransIconManager::VehicleTransIconManager()
    : m_lastConfigUpdate{~0u}
    , m_vehicleTransIcons{}
    , m_mat_b{false}
    , m_parkActive{false}
{
}

VehicleTransIconManager::~VehicleTransIconManager() = default;

static osg::Vec2f calculateIconSizeTransIcon(bool f_parkActive, bool f_horizontal)
{
    const osg::Vec2 l_vehicleTransIconData = (f_parkActive) ? g_uiSettings->m_vehicleTransIconParkActive.m_iconSize
                                                      : g_uiSettings->m_vehicleTransIcon.m_iconSize;
    osg::Vec2 l_iconSize;
    if (f_horizontal)
    {
        osg::Vec2f l_iconSize_hori =
            l_vehicleTransIconData /
            (cc::core::g_planView->m_widthMeters - cc::core::g_planView->m_widthMetersVeh2dDiff) *
            7.5f; // defaul value is tuning based on 7.5m
        l_iconSize.x() = std::floor(l_iconSize_hori.x());
        l_iconSize.y() = std::floor(l_iconSize_hori.y());
    }
    else
    {
        osg::Vec2f l_iconSize_vert =
            l_vehicleTransIconData /
            (cc::core::g_planView->m_widthMeters - cc::core::g_planView->m_widthMetersVeh2dDiff) *
            7.5f; // defaul value is tuning based on 7.5m
        l_iconSize.x() = std::floor(l_iconSize_vert.x());
        l_iconSize.y() = std::floor(l_iconSize_vert.y());
    }

    return l_iconSize;
}

// bool checkTouchInsideVehicleTransIcon(EScreenID f_currentScreen )
// {
//     vfc::float32_t huX = 0.0f;
//     vfc::float32_t huY = 0.0f;
//     cc::daddy::CustomDaddyPorts::touch
//         huX = static_cast<vfc::float32_t>(m_huX) - static_cast<vfc::float32_t>(viewport->x());
//         huY = (static_cast<vfc::float32_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) -
//         static_cast<vfc::float32_t>(viewport->y())) - static_cast<vfc::float32_t>(m_huY);
//     vfc::float32_t lowerX = (m_iconCenter.x() - m_responseArea.x() * 0.5f);
//     vfc::float32_t upperX = (m_iconCenter.x() + m_responseArea.x() * 0.5f);
//     vfc::float32_t lowerY = (m_iconCenter.y() - m_responseArea.y() * 0.5f);
//     vfc::float32_t upperY = (m_iconCenter.y() + m_responseArea.y() * 0.5f);
//     if ((lowerX <= huX) && (huX <= upperX) && (lowerY <= huY) && (huY <= upperY))
//     {
//         return true;
//     }
//     return false;
// }

void VehicleTransIconManager::setVehicleIconSize(core::CustomFramework* f_framework)
{
    if (f_framework == nullptr)
    {
        return;
    }
    if (f_framework->m_parkHmiParkingStatusReceiver.isConnected())
    {
        if (f_framework->m_parkHmiParkingStatusReceiver.hasData())
        {
            cc::target::common::EPARKStatusR2L                      l_curparkStatus = cc::target::common::EPARKStatusR2L::PARK_Off;
            const cc::daddy::ParkStatusDaddy_t* const l_parkStatus    = f_framework->m_parkHmiParkingStatusReceiver.getData();
            if (l_parkStatus != nullptr)
            {
                l_curparkStatus = l_parkStatus->m_Data;
            }
            bool l_curParkActive = false;
            switch (l_curparkStatus)
            {
            case cc::target::common::EPARKStatusR2L::PARK_Searching:
            case cc::target::common::EPARKStatusR2L::PARK_Guidance_active:
            case cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend:
            case cc::target::common::EPARKStatusR2L::PARK_Terminated:
            case cc::target::common::EPARKStatusR2L::PARK_Completed:
            case cc::target::common::EPARKStatusR2L::PARK_Failure:
            case cc::target::common::EPARKStatusR2L::PARK_AssistStandby:
            {
                l_curParkActive = true;
                break;
            }
            case cc::target::common::EPARKStatusR2L::PARK_Off:
            case cc::target::common::EPARKStatusR2L::PARK_Standby:
            default:
            {
                l_curParkActive = false;
                break;
            }
            }
            if (m_parkActive != l_curParkActive)
            {
                m_parkActive               = l_curParkActive;
                const osg::Vec2f l_iconSize_hori = calculateIconSizeTransIcon(m_parkActive, true);
                const osg::Vec2f l_iconSize_vert = calculateIconSizeTransIcon(m_parkActive, false); // PRQA S 3803
                m_vehicleTransIcons.getIcon(static_cast<unsigned>(VehicleTransIconType::VEHICLE_TRANS_ICON_FULLSCREEN))
                    ->setSize(l_iconSize_hori, pc::assets::Icon::UnitType::Pixel);
                // m_vehicleTransIcons.getIcon(VEHICLE_TRANS_ICON_VERT)->setSize(l_iconSize_vert,
                // pc::assets::Icon::UnitType::Pixel);
            }
        }
    }
}

void VehicleTransIconManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
    // ! init VehicleTransIcon icons
    m_vehicleTransIcons.clear(f_imageOverlays);

    // For hori screen mode
    osg::Vec2f l_iconSize_planView = osg::Vec2f(0.f, 0.f);
    calculateHoriVehicleIconSize(l_iconSize_planView);

    // For vertical screen mode
    osg::Vec2f l_iconSize_planView_vert = osg::Vec2f(0.f, 0.f);
    calculateFloatVehicleIconSize(l_iconSize_planView_vert);

    m_vehicleTransIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathVehicleTransIcon,
            g_uiSettings->m_vehicleTransIcon.m_iconCenter,
            l_iconSize_planView));
    m_vehicleTransIcons.addIcon(
        f_imageOverlays,
        createIcon(
            g_uiSettings->m_texturePathVehicleTransIconVert,
            g_uiSettings->m_vehicleTransIconVert.m_iconCenter,
            l_iconSize_planView_vert));
}

bool VehicleTransIconManager::m_iconShow=false;

void VehicleTransIconManager::update(
    pc::assets::ImageOverlays* f_imageOverlays,
    core::CustomFramework*     f_framework,
    bool                       f_blockTouch) // PRQA S 6041
{
    if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
    {
        return;
    }
    // ! check if config has changed
    if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
    {
        init(f_imageOverlays);
        m_lastConfigUpdate = g_uiSettings->getModifiedCount();
    }
    setVehicleIconSize(f_framework);
    // ! check the uss warning status
    m_vehicleTransIcons.getIcon( static_cast<unsigned>(VehicleTransIconType::VEHICLE_TRANS_ICON_FULLSCREEN))->setEnabled(false);
    m_vehicleTransIcons.getIcon( static_cast<unsigned>(VehicleTransIconType::VEHICLE_TRANS_ICON_FLOATSCREEN))->setEnabled(false);

    bool                                      l_allDoorsAndMirrorsClosed  = true;
    bool                                      l_isFloatScreen             = false;
    bool                                      l_isSoftwareSwitchAvailable = false;

    //! DOORS
    // check actual door status signal
    if (f_framework->m_doorStateReceiver.isConnected())
    {
        const pc::daddy::DoorStateDaddy* const l_pDoorStateDaddy = f_framework->m_doorStateReceiver.getData();
        if (nullptr != l_pDoorStateDaddy)
        {
            l_allDoorsAndMirrorsClosed =
                (pc::daddy::CARDOORSTATE_CLOSED == l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) &&
                (pc::daddy::CARDOORSTATE_CLOSED == l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) &&
                (pc::daddy::CARDOORSTATE_CLOSED == l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK]) &&
                (pc::daddy::CARDOORSTATE_CLOSED == l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_HOOD]) &&
                (pc::daddy::CARDOORSTATE_CLOSED == l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) &&
                (pc::daddy::CARDOORSTATE_CLOSED == l_pDoorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT]);
        }
    }
    if (f_framework->m_mirrorStateReceiver.isConnected())
    {
        if (f_framework->m_mirrorStateReceiver.hasData())
        {
            const pc::daddy::MirrorStateDaddy* const l_pMirrorState = f_framework->m_mirrorStateReceiver.getData();
            if (nullptr != l_pMirrorState)
            {
                l_allDoorsAndMirrorsClosed =
                    l_allDoorsAndMirrorsClosed &&
                    (pc::daddy::MIRRORSTATE_NOT_FLAPPED ==
                     l_pMirrorState->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_LEFT)]) &&
                    (pc::daddy::MIRRORSTATE_NOT_FLAPPED ==
                     l_pMirrorState->m_Data[static_cast<vfc::uint8_t>(pc::daddy::SIDEMIRROR_RIGHT)]);
            }
        }
    }



    // cc::util::pdmwriter::PdmSettings* l_pdmSetting =
    //     dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));
    // m_iconShow = static_cast<bool>( l_pdmSetting->getPdmVehTransStatus());

    if (f_framework->m_displayedView_ReceiverPort.hasData())
    {
        const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayid = f_framework->m_displayedView_ReceiverPort.getData();
        if (nullptr != l_displayid)
        {
            if (l_displayid->m_Data == EScreenID_FLOAT_SINGLE_FRONT || l_displayid->m_Data == EScreenID_FLOAT_SINGLE_REAR ||
                l_displayid->m_Data == EScreenID_FLOAT_WHEEL_FRONT_DUAL || l_displayid->m_Data == EScreenID_FLOAT_WHEEL_REAR_DUAL ||
                l_displayid->m_Data == EScreenID_FLOAT_FRONT_PLAN_VIEW || l_displayid->m_Data == EScreenID_FLOAT_REAR_PLAN_VIEW ||
                l_displayid->m_Data == EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW || l_displayid->m_Data == EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW)
            {
                l_isFloatScreen             = true;
                l_isSoftwareSwitchAvailable = true;
            }
            else if (
                EScreenID_SINGLE_FRONT_NORMAL == l_displayid->m_Data ||
                EScreenID_SINGLE_REAR_NORMAL_ON_ROAD == l_displayid->m_Data ||
                EScreenID_WHEEL_REAR_DUAL == l_displayid->m_Data || EScreenID_WHEEL_FRONT_DUAL == l_displayid->m_Data ||
                EScreenID_PERSPECTIVE_FL == l_displayid->m_Data || EScreenID_PERSPECTIVE_FR == l_displayid->m_Data ||
                EScreenID_PERSPECTIVE_RL == l_displayid->m_Data || EScreenID_PERSPECTIVE_RR == l_displayid->m_Data ||
                EScreenID_PERSPECTIVE_PFR == l_displayid->m_Data || EScreenID_PERSPECTIVE_PRI == l_displayid->m_Data ||
                EScreenID_PERSPECTIVE_PLE == l_displayid->m_Data || EScreenID_PERSPECTIVE_PRE == l_displayid->m_Data ||
                EScreenID_SINGLE_STB == l_displayid->m_Data)
            {
                l_isFloatScreen             = false;
                l_isSoftwareSwitchAvailable = true;
            }
            else
            {
                l_isSoftwareSwitchAvailable = false;
            }
        }
    }

    if (transIconChangedBySoftSwitch(f_framework, f_blockTouch, l_isFloatScreen, l_isSoftwareSwitchAvailable))
    {
        m_iconShow = !m_iconShow;
        if (cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.isConnected())
        {
            auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.reserve();
            l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(m_iconShow);
            cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.deliver();
        }
    }
        // send updated status to pdm
        // if (cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.isConnected())
        // {
        // auto& l_container  = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.reserve();
        // l_container.m_Data = static_cast<cc::target::common::EPdmSvsSetting>(m_iconShow);
        // cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.deliver();
        // }


    // if (m_iconShow && l_isFloatScreen)
    // {
    //     m_vehicleTransIcons.getIcon(VEHICLE_TRANS_ICON_FLOATSCREEN)->setEnabled(true);
    // }
    // else if (m_iconShow && !l_isFloatScreen)
    // {
    //     m_vehicleTransIcons.getIcon(VEHICLE_TRANS_ICON_FULLSCREEN)->setEnabled(true);
    // }
    // setVehicleTransIcon(m_iconShow);
}
void VehicleTransIconManager::setVehicleTransIcon(bool f_show)
{
    static std::mutex l_mtx;
    std::lock_guard<std::mutex> l_lock(l_mtx);
    m_iconShow=f_show;
    cc::daddy::SVSVehTransStsInternalDaddy_t& l_transparenceInternal =
        cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsInternalDaddy_SenderPort.reserve();
    l_transparenceInternal.m_Data = m_iconShow;
    cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsInternalDaddy_SenderPort.deliver();

    if (cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.isConnected())
    {
        cc::daddy::SVSVehTransStsDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.reserve();
        l_container.m_Data = m_iconShow? 1u : 0u;
        cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.deliver();
    }
}
void VehicleTransIconManager::calculateHoriVehicleIconSize(osg::Vec2f& f_iconSize) // PRQA S 4287
{
    vfc::float32_t      l_pixelPerMeter           = 0.f;
    vfc::float32_t      l_viewPortWidthSizePixel  = 0.f;
    vfc::float32_t      l_viewPortWidthSizeMeters = 0.f;
    osg::Vec2f l_iconSize                = osg::Vec2f(0.f, 0.f);

    l_viewPortWidthSizePixel  = static_cast<vfc::float32_t>(cc::core::g_views->m_planViewport.m_size.x());
    l_viewPortWidthSizeMeters = cc::core::g_planView->m_widthMeters - cc::core::g_planView->m_widthMetersVeh2dDiff;

    l_pixelPerMeter = l_viewPortWidthSizePixel / l_viewPortWidthSizeMeters;
    l_iconSize.y()  = l_pixelPerMeter * pc::vehicle::g_mechanicalData->getLength();
    l_iconSize.y()  = std::floor(
        l_iconSize.y() / g_vehicleTransIconSettings->m_proportion.x() * g_vehicleTransIconSettings->m_proportion.y());
    l_iconSize.x() = l_iconSize.y() * g_vehicleTransIconSettings->m_aspectRatioOfVehicle;

    f_iconSize = l_iconSize;
}

void VehicleTransIconManager::calculateFloatVehicleIconSize(osg::Vec2f& f_iconSize) // PRQA S 4287
{
    vfc::float32_t      l_pixelPerMeter           = 0.f;
    vfc::float32_t      l_viewPortWidthSizePixel  = 0.f;
    vfc::float32_t      l_viewPortWidthSizeMeters = 0.f;
    osg::Vec2f l_iconSize                = osg::Vec2f(0.f, 0.f);

    l_viewPortWidthSizePixel  = static_cast<vfc::float32_t>(cc::core::g_views->m_floatPlanViewport.m_size.x());
    l_viewPortWidthSizeMeters = cc::core::g_planView->m_widthMeters - cc::core::g_planView->m_widthMetersVeh2dDiff;

    l_pixelPerMeter = l_viewPortWidthSizePixel / l_viewPortWidthSizeMeters;
    l_iconSize.y()  = l_pixelPerMeter * pc::vehicle::g_mechanicalData->getLength();
    l_iconSize.y()  = std::floor(
        l_iconSize.y() / g_vehicleTransIconSettings->m_proportion.x() * g_vehicleTransIconSettings->m_proportion.y());
    l_iconSize.x() = l_iconSize.y() * g_vehicleTransIconSettings->m_aspectRatioOfVehicle;

    f_iconSize = l_iconSize;
}

bool VehicleTransIconManager::transIconChangedBySoftSwitch(
    core::CustomFramework* f_framework,
    bool                   f_blockTouch,
    bool                   is_floatScreen,
    bool                   f_softSwitchAvailable)
{
    if (f_framework == nullptr)
    {
        return false;
    }
    //! check soft switch
    vfc::uint8_t       l_touchEvent        = 0u;
    cc::daddy::HmiData l_hmiData           = {0, 0u, 0u, 0u, 0u, 0};
    static bool        l_isUpdated         = false;
    bool               isSoftButtonClicked = false;
    if (f_framework->m_HUTouchTypeReceiver.hasData())
    {
        const cc::daddy::HUtouchEvenTypeDaddy_t* const l_pData = f_framework->m_HUTouchTypeReceiver.getData();
        l_touchEvent                                     = l_pData->m_Data;
    }

    if (f_framework->m_hmiDataReceiver.hasData())
    {
        const cc::daddy::HmiData_Daddy* const l_pData = f_framework->m_hmiDataReceiver.getData();
        l_hmiData.m_huX                         = l_pData->m_Data.m_huX;
        l_hmiData.m_huY                         = static_cast<vfc::uint16_t>(static_cast<vfc::uint16_t>(pc::core::g_systemConf->m_mainViewport.m_size.y()) - static_cast<vfc::uint16_t>(l_pData->m_Data.m_huY));
    }

    if (!f_blockTouch && f_softSwitchAvailable && (1u == l_touchEvent || 3u == l_touchEvent)) // down or move
    {
        cc::assets::uielements::UIData vehicleTransIconUIData = g_uiSettings->m_vehicleTransIcon;
        ;
        if (is_floatScreen)
        {
            vehicleTransIconUIData = g_uiSettings->m_vehicleTransIconVert;
        }

        if (!l_isUpdated &&
            l_hmiData.m_huX > // PRQA S 3011
                vehicleTransIconUIData.m_iconCenter.x() - vehicleTransIconUIData.m_responseArea.x() * 0.5f &&
            l_hmiData.m_huX < // PRQA S 3011
                vehicleTransIconUIData.m_iconCenter.x() + vehicleTransIconUIData.m_responseArea.x() * 0.5f &&
            l_hmiData.m_huY > // PRQA S 3011
                vehicleTransIconUIData.m_iconCenter.y() - vehicleTransIconUIData.m_responseArea.y() * 0.5f &&
            l_hmiData.m_huY < // PRQA S 3011
                vehicleTransIconUIData.m_iconCenter.y() + vehicleTransIconUIData.m_responseArea.y() * 0.5f)
        {
            l_isUpdated         = true;
            isSoftButtonClicked = true;
        }
    }
    else
    {
        l_isUpdated = false;
    }
    return isSoftButtonClicked;
}

//!
//! @brief Construct a new VehicleTransIcon:: VehicleTransIcon object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
VehicleTransIcon::VehicleTransIcon(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
    : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}
    , m_customFramework{f_customFramework}
    , m_manager{}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

    //! render order
    //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
    constexpr vfc::uint32_t l_renderOrder = 500u;
    cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder, true);
}

VehicleTransIcon::~VehicleTransIcon() = default;

void VehicleTransIcon::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        invokeUpdate(f_nv);
    }
    pc::assets::ImageOverlays::traverse(f_nv);
}

pc::util::coding::Item<FreeParkingVehicleTransIconOverlaySettings> g_freeParkingVehicleTransIconOverlaySettings("FreeParkingVehicleTransIconOverlaySettings");

pc::assets::Icon* createIcon(const std::string& f_filepath)
{
    pc::assets::Icon* const l_icon = new pc::assets::Icon{f_filepath, true};
    // l_icon->setSize(osg::Vec2(l_image->s(), l_image->t()), pc::assets::Icon::UnitType::Pixel);
    l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
    // l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Percentage);
    l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    // l_icon->setEnabled(false);
    return l_icon;
}


cc::assets::uielements::FreeParkingVehicleTransIconOverlay::FreeParkingVehicleTransIconOverlay(
    pc::core::Framework* f_framework,
    cc::core::AssetId                 f_assetId,
    osg::Camera*                      f_referenceView)
    : pc::assets::ImageOverlays(f_assetId, f_referenceView) // PRQA S 2323
    , m_framework(f_framework)
{
    init();
    m_modifiedCount = cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->getModifiedCount();
    const auto camera = static_cast<osg::Camera*>(getAsset());
    camera->setRenderOrder(osg::Camera::RenderOrder::NESTED_RENDER);
}

void FreeParkingVehicleTransIconOverlay::init()
{
    m_icons.clear(this);
    m_icons.addIcon(this, createIcon(cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->m_freeParkingVehicleTransIconPath));
    m_icons.getIcon(0u)->setSize(osg::Vec2f{100.0f, 100.0f}, pc::assets::Icon::UnitType::Percentage);

}

void FreeParkingVehicleTransIconOverlay::update(vfc::float64_t /*f_time*/)
{
    m_icons.getIcon(0u)->setEnabled(true);
    osg::Vec2f position = {};
    if (m_referenceView != nullptr && m_referenceView->getViewport() != nullptr)
    {
        osg::Viewport* const l_viewport = m_referenceView->getViewport();
        position.x() = static_cast<vfc::float32_t>(cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->m_freeParkingVehicleTransIconPos.x());
        position.y() = static_cast<vfc::float32_t>(cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->m_freeParkingVehicleTransIconPos.y());
        m_icons.getIcon(0u)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
    }
    else
    {
        m_icons.getIcon(0u)->setPosition(position, pc::assets::Icon::UnitType::Pixel);
    }
}

void FreeParkingVehicleTransIconOverlay::traverse(osg::NodeVisitor& f_nv)
{
    if (f_nv.getVisitorType() == osg::NodeVisitor::UPDATE_VISITOR)
    {
        if (m_modifiedCount != cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->getModifiedCount())
        {
            m_modifiedCount = cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->getModifiedCount();
            init();
        }
        update(f_nv.getFrameStamp()->getSimulationTime());
    }
    ImageOverlays::traverse(f_nv);
}

//!
//! MeterPerPixelProjectionUpdateVisitor
//!
MeterPerPixelProjectionUpdateVisitor::MeterPerPixelProjectionUpdateVisitor(vfc::float64_t f_meterPerPixel)
    : osg::NodeVisitor(osg::NodeVisitor::TraversalMode::TRAVERSE_NONE) // PRQA S 2323 // PRQA S 4052
    , m_meterPerPixel(f_meterPerPixel)
{
}

void MeterPerPixelProjectionUpdateVisitor::apply(osg::Node& f_node)
{
    osg::Camera* const camera = f_node.asCamera();
    if (camera == nullptr)
    {
        XLOG_ERROR(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: invalid node");
        traverse(f_node);
        return;
    }
    osg::Viewport* const viewport = camera->getViewport();
    if (viewport == nullptr)
    {
        XLOG_ERROR(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: invalid viewport");
        traverse(f_node);
        return;
    }


    XLOG_INFO(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: "
        << "Viewport Size: " << viewport->width() << ", " << viewport->height());

    const vfc::float64_t viewportWidth = m_meterPerPixel * static_cast<vfc::float64_t>(viewport->width());
    const vfc::float64_t viewportHeight = viewportWidth * static_cast<vfc::float64_t>(viewport->height()) / static_cast<vfc::float64_t>(viewport->width());
    m_top = viewportHeight * 0.5f  + cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->m_viewportOffset;
    m_bottom = -viewportHeight * 0.5f + cc::assets::uielements::g_freeParkingVehicleTransIconOverlaySettings->m_viewportOffset;
    m_left = -viewportWidth * 0.5f ;
    m_right = viewportWidth * 0.5f ;

    camera->setProjectionMatrixAsOrtho2D(m_left, m_right, m_bottom, m_top);

    XLOG_INFO(g_AppContext, "MeterPerPixelProjectionUpdateVisitor: set view projection [" << camera->getName()
                               << "]: "
                               << "left: " << m_left << "right: " << m_right << "bottom: " << m_bottom
                               << "top: " << m_top);
}

} // namespace uielements
} // namespace assets
} // namespace cc
