//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_MAINLOGICCFG_H
#define CC_ASSETS_TRAJECTORY_MAINLOGICCFG_H


namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{


//const unsigned int c_defaultNumOfSubAssets = 20;


} // namespace mainlogic
} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_MAINLOGICCFG_H
