//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LUM7LR Maximilian Luzius (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  FloorSingleCam.h
/// @brief
//=============================================================================

#ifndef PC_FACTORY_FLOORSINGLECAM_H
#define PC_FACTORY_FLOORSINGLECAM_H

#include "pc/svs/factory/inc/Floor.h"

namespace cc
{
namespace assets
{
namespace common
{

class FrontFloorData;
extern pc::util::coding::Item<FrontFloorData> g_frontFloorData;


class RearFloorData;
extern pc::util::coding::Item<RearFloorData> g_rearFloorData;

// floors of the floorSingleCam
enum FloorType
{
  FLOOR_FRONT,
  FLOOR_REAR
};

//======================================================
// FloorSingleCam
//------------------------------------------------------
/// Provides the possibility to build a mesh for the floor with an area
/// inside of it with an higher resolution for a single cam. So far just
/// used for the front rear view and adapted that way to it. Could be
/// changed to a more common class if necessary.
/// <AUTHOR> Luzius (CC-DA/EAV3)
/// @ingroup Floor
//======================================================
class FloorSingleCam : public pc::factory::Floor
{
public:

  typedef osg::DrawElementsUShort::value_type UShort;

  /**
   * Initialize the floor for the singleCam
   */
  FloorSingleCam(const osg::Vec2f& f_size, const osg::Vec2f& f_resolution, const osg::Vec2f& f_offset, FloorType f_floorType);

  FloorSingleCam(const FloorSingleCam& f_floor, const osg::CopyOp& f_copyOp = osg::CopyOp::SHALLOW_COPY);    // PRQA S 3143

  /**
   * Filling the vertexArray with the vertices of the floor mesh.
   * @param f_vertexCounter Increasing index counter for the vertexArrayPosition.
   * @param f_numVerticesX Number of vertices in X-direction to limit the for loop.
   * @param f_numVerticesY Number of vertices in Y-direction to limit the for loop.
   * @param f_offsetX Offset that will be added in X-direction to each vertex.
   * @param f_offsetY Offset that will be added in Y-direction to each vertex.
   * @param f_stepX StepSize in X-direction.
   * @param f_stepY StepSize in Y-direction.
   */
  void fillVertexArray(unsigned int& f_vertexCounter, unsigned int f_numVerticesX, unsigned int f_numVerticesY, float f_offsetX, float f_offsetY, float f_stepX, float f_stepY);

  /**
   * Filling the indexArray with the indices of the vertices in the correct triangulated order.
   * @param f_indexArray Passed indexArray to push the index order on.
   * @param f_numVerticesX Number of vertices in X-direction to limit the for loop.
   * @param f_numVerticesY Number of vertices in Y-direction to limit the for loop.
   * @param f_offsetX Offset that will be added in X-direction to each vertexIndex.
   * @param f_factorY Factor that will be multiplied in Y-direction to each vertexIndex.
   */
  void fillIndexArray(osg::DrawElementsUShort* f_indexArray, unsigned int f_numVerticesX, unsigned int f_numVerticesY, float f_offsetX, float f_factorY);

  /** region B has a higher resolution, the regions A,C and D are with the normal resolution
  * <pre>
  *  _____________
  * |    .   .    |
  * |    . C .    |
  * | D  .___.  A |
  * |    | B |    |
  * |____|___|____|
  * </pre>
  * also remember that the outer regions A,C,D are using the border vertices of B which are connecting the regions
  */
  virtual void build();

protected:

  virtual ~FloorSingleCam();

private:

  FloorSingleCam& operator = (const FloorSingleCam&); // delete

  osg::Vec2f m_size;
  osg::Vec2f m_standardResolution;
  osg::Vec4f m_areaHighResolution;
  osg::Vec2f m_highResolution;

  float m_dir;

};

} // namespace common
} // namespace factory
} // namespace pc

#endif // PC_FACTORY_FLOORSINGLECAM_H
