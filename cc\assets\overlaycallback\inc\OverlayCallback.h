//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: VUJ1LR Vujicic Milica (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  OverlayCallback.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_OVERLAYCALLBACK_H
#define CC_ASSETS_OVERLAYCALLBACK_H

#include "pc/svs/core/inc/View.h"
#include "cc/core/inc/CustomFramework.h"

#include <osg/NodeCallback>
#include <osg/NodeVisitor>
#include <iomanip>
#include <sstream>

namespace cc
{
namespace assets
{
namespace overlaycallback
{

enum EOverlayType
{
  TRAILER_CIRCLE,
  TRAILER_TRAJ,
  TRAJECTORY
};

enum ERearCamTailgatePosition : unsigned int
{
    REARCAMPOS_ON_TAILGATE = 0u,
    REARCAMPOS_NOT_ON_TAILGATE = 1u
};

//!
//! LCF Coding for overlays
//!
class OverlayData : public pc::util::coding::ISerializable
{
public:

  OverlayData()
    : m_ICE_stallTimeout(10.0f) //in seconds
    , m_SATCAM_rear_tailgatePosition(REARCAMPOS_NOT_ON_TAILGATE) //0-fitted on the tailgate //1-not fitted on the tailgate
  {
  }

  SERIALIZABLE(OverlayData)
  {
    ADD_FLOAT_MEMBER(ICE_stallTimeout);
    ADD_UINT32_MEMBER(SATCAM_rear_tailgatePosition);
  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "OverlayData:"
                           << "\n    m_ICE_stallTimeout                 " << static_cast<unsigned int>( m_ICE_stallTimeout              )
                           << "\n    m_SATCAM_rear_tailgatePosition     " << static_cast<unsigned int>( m_SATCAM_rear_tailgatePosition  );  // PRQA S 3803
    return l_stream.str();
  }

  float m_ICE_stallTimeout;
  unsigned int m_SATCAM_rear_tailgatePosition;

};

extern pc::util::coding::Item<OverlayData> g_overlayData;


//!
//! Logging parameters for debug
//!
class OverlayLogSettings : public pc::util::coding::ISerializable
{
public:
  OverlayLogSettings()
  : m_logOverlayType                (false)
  , m_logOverlayStatus              (false)
  , m_logMovementDirection          (false)
  , m_logPowerModeOverlayStatus     (false)
  , m_logViewModeCamDegraded        (false)
  , m_logViewModeDoorsDegraded      (false)
  , m_logTrailerDegraded            (false)
  {

  }

  SERIALIZABLE(OverlayLogSettings)
  {
    ADD_BOOL_MEMBER(logOverlayType);
    ADD_BOOL_MEMBER(logOverlayStatus);
    ADD_BOOL_MEMBER(logMovementDirection);
    ADD_BOOL_MEMBER(logPowerModeOverlayStatus);
    ADD_BOOL_MEMBER(logViewModeCamDegraded);
    ADD_BOOL_MEMBER(logViewModeDoorsDegraded);
    ADD_BOOL_MEMBER(logTrailerDegraded);
  }

  bool m_logOverlayType;
  bool m_logOverlayStatus;
  bool m_logMovementDirection;
  bool m_logPowerModeOverlayStatus;
  bool m_logViewModeCamDegraded;
  bool m_logViewModeDoorsDegraded;
  bool m_logTrailerDegraded;
};

enum OverlayType
{
  OVERLAY_NONE          = 0,
  VEHICLE_WHEEL_TRACK   = 1,
  VEHICLE_DRIVING_TUBE  = 2,
  OBSTACLE_DISTANCE     = 3,
  HITCH                 = 4,
  TRAILER_DRIVING_TUBE  = 5,
  DISTANCE_TO_STOP      = 6,
  DEGRADATION_OVERLAY   = 7
};

extern pc::util::coding::Item<OverlayLogSettings> g_overlayLogSettings;

//======================================================
// OverlayCallback
//------------------------------------------------------
/// Evaluates common requirements for visibility of overlays
/// Evaluates the minimum conditions for an overlays to be displayed.
/// <AUTHOR> Milica
//======================================================
class OverlayCallback : public osg::NodeCallback
{
public:
	OverlayCallback(pc::core::Framework* f_framework, OverlayType f_overlayType);

  //virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
  virtual ~OverlayCallback();
  virtual void cull(osg::Node* f_node, osg::NodeVisitor* f_nv);

private:

  //! Copy constructor is not permitted.
  OverlayCallback (const OverlayCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  OverlayCallback& operator=(const OverlayCallback& other); // = deleteở n

  void checkOverlayStatus();
  void checkMovementDirection();
  void checkPowerMode();
  void checkViewModeCamDegradation();
  void checkViewModeDoorDegradation();
  void checkTrailerStatus();

  pc::core::Framework* m_framework;
  int m_overlayStatus;
  bool m_movementDirection;
  int m_powerModePrevious;
  int m_powerModeOverlayStatus;
  int m_viewModeCamDegraded;
  int m_viewModeDoorsDegraded;
  OverlayType m_overlayType;
  bool m_rearCamPosTailgate;
  bool m_trailerDegraded;
  osg::Timer m_timer;
  osg::Timer_t m_tick;

};

//!
//! Keep the overlays on in exception views (currently this is the top view)
//!

class TopViewOverlayCullCallback : public OverlayCallback
{
public:
  TopViewOverlayCullCallback(pc::core::Framework* f_framework, OverlayType f_overlayType);

  //virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv);

protected:
  virtual ~TopViewOverlayCullCallback();
  virtual void cull(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

private:
  //! Copy constructor is not permitted.
  TopViewOverlayCullCallback (const TopViewOverlayCullCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  TopViewOverlayCullCallback& operator=(const TopViewOverlayCullCallback& other); // = delete

};

} // namespace overlaycallback
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_OVERLAYCALLBACK_H