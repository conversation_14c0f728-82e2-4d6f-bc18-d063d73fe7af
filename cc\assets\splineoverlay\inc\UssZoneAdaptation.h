//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: KVT2BP Kovacs Tibor (CC-DA/EAV1-Bp)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  UssZoneAdaptation.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_SPLINEOVERLAY_USS_ZONE_ADAPTATION_H
#define CC_ASSETS_SPLINEOVERLAY_USS_ZONE_ADAPTATION_H

#include <vector>
#include <osg/Vec2f>
#include <osg/Vec4f>

namespace cc
{
namespace assets
{
namespace splineoverlay
{

struct Rect
{
  float left, right, front, rear;

  // True if all of the f_InnerRect's values ar inside the rectangle
  bool rectInside(const Rect& f_InnerRect) const
  {
    return ((f_InnerRect.left  < left)
         && (f_InnerRect.right > right)
         && (f_InnerRect.rear  < rear)
         && (f_InnerRect.front > front));
  }
};

//======================================================
// UssZoneAdaptation
//------------------------------------------------------
/// Manage the computation for the USS Zone adaptation.
/// Gets the existing USS Zones and the vehicle contour
/// and find the best position to attach the zone lines
/// to the car contour.
/// <AUTHOR>
//======================================================
class UssZoneAdaptation
{
public:
  UssZoneAdaptation();
  
  ~UssZoneAdaptation() {}

  // Bounding rectangle computations
  void carBoundingRect(const osg::Vec2f& f_Point);
  void insertToUssBoundingRect(const osg::Vec2f& f_Point);
  void closeUssBoundingRect(); // Call it after the USS bounding rectangle is computed

  osg::Vec4f scaleUssZone(const osg::Vec4f& f_ZoneLine);
  void moveUssZoneToCarContour(osg::Vec4f& f_ZoneLine, const std::vector<float>& f_ContourChunkX, const std::vector<float>& f_ContourChunkY);

  bool shiftUssLineToTheCarContour(const osg::Vec4f& f_CarContourLine, osg::Vec4f& f_UssZoneLine);

  bool isInTheSameDomain(const osg::Vec2f& inPoint1, const osg::Vec2f& inPoint2); // True if the two points are in the same domain according to the geometry center

  void setIsScaleNeeded(const bool f_isScaleNeeded) { m_isScaleNeeded = f_isScaleNeeded; }

  const Rect& getCarBoundingRect() const { return m_carBoundingRectangle; }
  const Rect& getUssBoundingRect() const { return m_ussBoundingRectangle; }

private:

  //! Copy constructor is not permitted.
  UssZoneAdaptation (const UssZoneAdaptation& other); // = delete
  //! Copy assignment operator is not permitted.
  UssZoneAdaptation& operator=(const UssZoneAdaptation& other); // = delete

  osg::Vec2f intersection(const osg::Vec4f& f_Line, const osg::Vec4f& f_Segment, bool* f_IntersectedOut);

  bool       m_isScaleNeeded;        // True if the bounding rectangle of the inner USS zone points is not inside of the car's bounding rectangle
  Rect       m_carBoundingRectangle; // Bounding rectangle of the car contour
  Rect       m_ussBoundingRectangle; // Bounding rectangle of the inner points of the USS zone's
  osg::Vec2f m_carGeometryCenter;    // Middle point of the car's bounding rectangle
};

} // namespace splineoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_SPLINEOVERLAY_USS_ZONE_ADAPTATION_H