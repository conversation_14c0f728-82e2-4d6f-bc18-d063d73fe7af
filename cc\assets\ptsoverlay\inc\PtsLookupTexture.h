//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PTSOVERLAY_PTSLOOKUPTEXTURE_H
#define CC_ASSETS_PTSOVERLAY_PTSLOOKUPTEXTURE_H

#include <osg/Vec2ub>
#include <osg/Array>

/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace osg
{
class StateSet;
} // namespace osg


namespace cc
{
namespace assets
{
namespace ptsoverlay
{

//!
//! @class LookupTexture
//! @brief Helper class for sampling a Look-up texture for color and transparency values
//!
//!
class LookupTexture
{
public:

  typedef osg::Vec2ub TexCoord; //!< 8-bit should be more than enough to address a 32x32 texture
  typedef osg::Vec2ubArray TexCoordArray;

  static const unsigned int s_size;
  static const TexCoord::value_type s_maxTexCoord;

  //!
  //! @brief Helper class to acces different color mapping ranges within the look-up texture
  //!
  //!
  struct ValueRange
  {
    //!
    //! @brief Construct a new Value Range object
    //!
    //! @param f_begin normalized begin of range
    //! @param f_end normalized end of range
    //!
    ValueRange(float f_begin, float f_end);

    bool contains(float f_value) const
    {
      return (m_begin <= f_value) && (f_value <= m_end);
    }

    inline float length() const
    {
      return m_end - m_begin;
    }

    inline float normalize(float f_value) const
    {
      return std::max(m_begin, std::min(m_end, (f_value - m_begin) / length()));
    }

    TexCoord::value_type get(float f_v) const;

    TexCoord get(float f_u, float f_v) const;

    const float m_begin;
    const float m_end;
  };

  static const ValueRange s_distanceColor;
  static const ValueRange s_onColor;
  static const ValueRange s_outlineColor;
  static const ValueRange s_shadowColor;
};


osg::StateSet* getOrCreateTexturingStateSet();


} // namespace ptsoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTSOVERLAY_PTSLOOKUPTEXTURE_H