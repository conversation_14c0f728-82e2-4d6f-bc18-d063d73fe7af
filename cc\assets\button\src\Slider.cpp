//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/core/inc/SystemConf.h"

#include "cc/assets/button/inc/Slider.h"
#include "cc/core/inc/CustomFramework.h"
// #include "cc/imgui/inc/imgui_manager.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "vfc/core/vfc_types.hpp"
#include <fstream>
#include <iostream>
#include <osg/Geometry>
#include <osgText/Text>

using pc::util::logging::g_AppContext;

#define GET_PORT_DATA(dataDaddy, port, PortHaveDataFlag)                                                               \
    auto const dataDaddy    = port.getData();                                                                             \
    PortHaveDataFlag = PortHaveDataFlag && (dataDaddy != nullptr);
    // if (dataDaddy == nullptr)
// {                                                                                                                  \ // PRQA S 1054
    //     XLOG_ERROR(g_AppContext, #port << " doesn't have data!\n");                                                    \ // PRQA S 1054
    // }

namespace cc
{
namespace assets
{
namespace button
{

pc::util::coding::Item<SliderSettings> g_defaultSliderSettings("SliderSettingsDefault");

namespace
{

bool isEqual(const pc::core::Viewport& f_a, const pc::core::Viewport& f_b)
{
    return (f_a.m_origin == f_b.m_origin) && (f_a.m_size == f_b.m_size);
}

} // namespace

class CameraCallback : public osg::NodeCallback // PRQA S 2113 // PRQA S 2119
{
public:
    explicit CameraCallback(Slider* f_slider, pc::core::Framework* f_framework)
        : m_slider{f_slider}
        , m_framework{f_framework}
    {
    }

    void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override
    {
        if ((f_node == nullptr) || (f_nv == nullptr))
        {
            return;
        }
        switch (f_nv->getVisitorType())
        {
            case osg::NodeVisitor::CULL_VISITOR:
            {
                cull(f_node->asCamera(), f_nv);
                break;
            }
            case osg::NodeVisitor::UPDATE_VISITOR:
            {
                update(f_node->asCamera(), f_nv);
                break;
            }
            default:
            {
                traverse(f_node, f_nv);
                break;
            }
        }
    }

    static void cull(osg::Camera* f_cam, osg::NodeVisitor* f_nv)
    {
        if ((f_cam == nullptr) || (f_nv == nullptr))
        {
            return;
        }
        const vfc::uint32_t l_numChildren = f_cam->getNumChildren();
        for (vfc::uint32_t i = 0; i < l_numChildren; ++i)
        {
            osg::Node*  const l_node = f_cam->getChild(i);
            SliderIcon* const l_icon = dynamic_cast<SliderIcon*>(l_node);  // PRQA S 3400
            if (l_icon != nullptr)
            {
                //! only draw icon if it is enabled and in a valid state
                if (l_icon->getEnabled())
                {
                    l_icon->accept(*f_nv);
                }
            }
            else
            {
                //! not an icon, handle like a group node would do
                l_node->accept(*f_nv);
            }
        }
    }

    void update(osg::Camera* f_cam, osg::NodeVisitor* f_nv) // PRQA S 4211
    {
        if ((f_cam == nullptr) || (f_nv == nullptr))
        {
            return;
        }
        pc::core::Viewport l_viewport(osg::Vec2i(0, 0), pc::core::g_systemConf->m_mainViewport.m_size);
        osg::Camera*       const l_referenceView = m_slider->getReferenceView();
        if (l_referenceView != nullptr)
        {
            osg::Viewport* const l_osgViewport = l_referenceView->getViewport();
            if (l_osgViewport != nullptr)
            {
                l_viewport.m_origin =
                    osg::Vec2i(static_cast<vfc::int32_t>(l_osgViewport->x()), static_cast<vfc::int32_t>(l_osgViewport->y())); // PRQA S 3016
                l_viewport.m_size =
                    osg::Vec2i(static_cast<vfc::int32_t>(l_osgViewport->width()), static_cast<vfc::int32_t>(l_osgViewport->height())); // PRQA S 3016
            }
        }
        f_cam->setViewport(
            l_viewport.m_origin.x(), l_viewport.m_origin.y(), l_viewport.m_size.x(), l_viewport.m_size.y());
        f_cam->setProjectionMatrixAsOrtho2D(0.0, l_viewport.m_size.x(), 0.0, l_viewport.m_size.y()); // PRQA S 3011

        const vfc::uint32_t l_numChildren = f_cam->getNumChildren();
        for (vfc::uint32_t i = 0; i < l_numChildren; ++i)
        {
            osg::Node*  const l_node = f_cam->getChild(i);
            SliderIcon* const l_icon = dynamic_cast<SliderIcon*>(l_node);  // PRQA S 3400
            if (l_icon != nullptr)
            {
                l_icon->update(l_viewport);
            }
            //! normal updating
            if (0 < l_node->getNumChildrenRequiringUpdateTraversal())
            {
                l_node->accept(*f_nv);
            }
        }
    }

private:
    Slider*              m_slider;
    pc::core::Framework* m_framework;
};

SliderIcon::SliderIcon(const SliderSettings* f_settings, pc::core::Framework* f_framework, bool f_enabled)
    : m_positionType{UnitType::Pixel}
    , m_viewport{0, 0, 0, 0}
    , m_alignmentHorizontal{Alignment::Center}
    , m_alignmentVertical{Alignment::Center}
    , m_position{f_settings->m_position}
    , m_size{f_settings->m_size}
    , m_origin{Origin::BottomLeft}
    , m_dirty{true}
    , m_enabled{f_enabled}
    , m_lastTouchX{0}
    , m_framework{f_framework}
    , m_settings{f_settings}
    , m_isDragging{false}
{
    if ((f_settings != nullptr) && (f_framework != nullptr))
    {
        updateRealPercentage();
    }
}

SliderIcon::SliderIcon(const SliderIcon& f_other, const osg::CopyOp& f_copyOp)
    : osg::Geode{f_other, f_copyOp}
    , m_positionType{f_other.m_positionType}
    , m_viewport{f_other.m_viewport}
    , m_alignmentHorizontal{f_other.m_alignmentHorizontal}
    , m_alignmentVertical{f_other.m_alignmentVertical}
    , m_position{f_other.m_position}
    , m_size{f_other.m_size}
    , m_origin{f_other.m_origin}
    , m_dirty{f_other.m_dirty}
    , m_enabled{f_other.m_enabled}
    , m_framework{f_other.m_framework}
    , m_settings{f_other.m_settings}
{
}

bool SliderIcon::getEnabled() const
{
    return m_enabled;
}

void SliderIcon::setEnabled(bool f_state)
{
    m_enabled = f_state;
}

SliderIcon::Origin SliderIcon::getOrigin() const
{
    return m_origin;
}

void SliderIcon::setOrigin(Origin f_origin)
{
    if (m_origin != f_origin)
    {
        m_origin = f_origin;
        m_dirty  = true;
    }
}

SliderIcon::Alignment SliderIcon::getAlignmentHorizontal() const
{
    return m_alignmentHorizontal;
}

void SliderIcon::setAlignmentHorizontal(Alignment f_alignment)
{
    if (m_alignmentHorizontal != f_alignment)
    {
        m_alignmentHorizontal = f_alignment;
        m_dirty               = true;
    }
}

SliderIcon::Alignment SliderIcon::getAlignmentVertical() const
{
    return m_alignmentVertical;
}

void SliderIcon::setAlignmentVertical(Alignment f_alignment)
{
    if (m_alignmentVertical != f_alignment)
    {
        m_alignmentVertical = f_alignment;
        m_dirty             = true;
    }
}

void SliderIcon::getPosition(osg::Vec2f& f_position, UnitType& f_unitType) const // PRQA S 4678 // PRQA S 4287
{
    f_position = m_position;
    f_unitType = m_positionType;
}

void SliderIcon::setPosition(const osg::Vec2f& f_position, UnitType f_unitType)
{
    if ((m_position != f_position) || (m_positionType != f_unitType))
    {
        m_position     = f_position;
        m_positionType = f_unitType;
        m_dirty        = true;
    }
}

void SliderIcon::getSize(osg::Vec2f& f_size) const // PRQA S 4287
{
    f_size = m_size;
}

void SliderIcon::setSize(const osg::Vec2f& f_size)
{
    if (m_size != f_size)
    {
        m_size  = f_size;
        m_dirty = true;
    }
}

osg::Geometry* cc::assets::button::SliderIcon::getBackgroundGeometry()
{
    if (getDrawable(0u)->asGeometry() != nullptr)
    {
        return (getDrawable(0u)->asGeometry());
    }
    return nullptr;
}

osg::Geometry* SliderIcon::getForegroundGeometry()
{
    if (getDrawable(1u)->asGeometry() != nullptr)
    {
        return (getDrawable(1u)->asGeometry());
    }
    return nullptr;
}

osg::Geometry* cc::assets::button::SliderIcon::getMoveLeftLine()
{
    if (getDrawable(3u)->asGeometry() != nullptr)
    {
        return (getDrawable(3u)->asGeometry());
    }
    return nullptr;
}

osg::Geometry* SliderIcon::getMoveRightLine()
{
    if (getDrawable(4u)->asGeometry() != nullptr)
    {
        return (getDrawable(4u)->asGeometry());
    }
    return nullptr;
}

void SliderIcon::updatePercentage()
{
    bool allPortHaveData = true;
    if (m_framework == nullptr)
    {
        return;
    }
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, allPortHaveData);
    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, allPortHaveData);
    if (!allPortHaveData)
    {
        return;
    }

    const auto hmiData     = hmiDataContainer->m_Data;
    const auto touchStatus = static_cast<TouchStatus>(touchStatusContainer->m_Data);
    bool dragging    = false;

    // Check if the touch is within the response area and update status accordingly
    if (touchInsideResponseArea(hmiData.m_huX, hmiData.m_huY))
    {
        if ((getTouchStatus() == TouchStatus::TOUCH_INVALID) &&
            (touchStatus == TouchStatus::TOUCH_DOWN || touchStatus == TouchStatus::TOUCH_MOVE))
        {
            setTouchStatus(touchStatus);
            setTouchPosition({hmiData.m_huX, hmiData.m_huY});
        }
        else if ((touchStatus == TouchStatus::TOUCH_DOWN || touchStatus == TouchStatus::TOUCH_MOVE))
        {
            setTouchStatus(touchStatus);
            dragging = true;
        }
        else if (touchStatus == TouchStatus::TOUCH_UP)
        {
            setTouchStatus(TouchStatus::TOUCH_INVALID);
            dragging = false;
            // updateRealPercentage();
        }
        else
        {
            setTouchStatus(TouchStatus::TOUCH_INVALID);
        }
    }
    else if ((m_touchStatus == TouchStatus::TOUCH_DOWN || m_touchStatus == TouchStatus::TOUCH_MOVE))
    {
        if (touchStatus == TouchStatus::TOUCH_UP || touchStatus == TouchStatus::TOUCH_INVALID)
        {
            setTouchStatus(TouchStatus::TOUCH_INVALID);
            dragging = false;
            // updateRealPercentage();
            // sendPercentageSignal(m_percentage);
        }
    }
    else
    {
        setTouchStatus(TouchStatus::TOUCH_INVALID);
    }

    if (dragging)
    {
        m_isDragging               = true; // Indicate that the slider is being dragged
        osg::Vec2i currentTouchPos = osg::Vec2i{hmiData.m_huX, hmiData.m_huY};
        osg::Vec2f sliderSize;
        getSize(sliderSize);
//        const vfc::float32_t activationRadius = m_settings->m_baseActivationRadius;

        // Calculate touch speed and adjust activation radius
        const vfc::int32_t touchX = hmiData.m_huX - m_viewport.m_origin.x();
//        const vfc::int32_t          speed  = abs(touchX - m_lastTouchX); // PRQA S 3803
        m_lastTouchX        = touchX;

        // activationRadius = m_settings->m_baseActivationRadius + static_cast<float>(speed *
        // m_settings->m_speedFactor);

        // Calculate the slider position and update if within the activation radius
        const vfc::int32_t   lowerX         = static_cast<vfc::int32_t>(m_topLeftCorner.x()); // PRQA S 3016
        const vfc::float32_t diffPercentage = (static_cast<vfc::float32_t>(currentTouchPos.x()) - static_cast<vfc::float32_t>(lowerX)) / static_cast<vfc::float32_t>(sliderSize.x());

        sendBrightnessValue(diffPercentage);

        setTouchPosition(currentTouchPos);
    }
}

void SliderIcon::update(const pc::core::Viewport& f_viewport)
{
    if (!isEqual(m_viewport, f_viewport))
    {
        m_viewport = f_viewport;
        m_dirty    = true;
    }

    if (m_modifiedCount != m_settings->getModifiedCount())
    {
        m_modifiedCount = m_settings->getModifiedCount();
        setPosition(m_settings->m_position, m_positionType);
        setSize(m_settings->m_size);
    }
    updateRealPercentage();
    updatePercentage();
    bool               dayNightThemePortHaveData = true;
    cc::target::common::EThemeTypeDayNight l_themeTypeDayNight       = cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT;
    GET_PORT_DATA(
        dayNightThemeContainer,
        m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver,
        dayNightThemePortHaveData)

    if (dayNightThemePortHaveData)
    {
        l_themeTypeDayNight = dayNightThemeContainer->m_Data;
        if (l_themeTypeDayNight != m_dayNightTheme)
        {
            updateColor(l_themeTypeDayNight);
        }
    }

    // if (4u < getNumDrawables())
    // {
    //     if ((m_size.x() * m_percentage) < m_settings->m_leftMoveLineOffset)
    //     {
    //         const osg::Vec4f l_completelyTransparentColor = osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f);
    //         setMoveLinesColor(l_completelyTransparentColor);
    //     }
    //     else
    //     {
    //         setMoveLinesColor(m_settings->m_night.m_moveLineColor);
    //     }
    // }

    if (m_dirty)
    {
        osg::Vec2f l_position = m_position;
        if (UnitType::Percentage == m_positionType)
        {
            l_position.x() = l_position.x() / 100.0f;
            l_position.y() = l_position.y() / 100.0f;
            l_position.x() = std::floor(l_position.x() * f_viewport.m_size.x()); // PRQA S 3011
            l_position.y() = std::floor(l_position.y() * f_viewport.m_size.y()); // PRQA S 3011
        }
        osg::Vec2 l_size = m_size;

        //! handle horizontal alignment
        switch (m_alignmentHorizontal)
        {
            case Alignment::Center:
            {
                l_position.x() -= std::floor(0.5f * l_size.x());
                break;
            }
            case Alignment::Right:
            {
                l_position.x() -= l_size.x();
                break;
            }
            default:
            {
                break;
            }
        }
        //! handle vertical alignment
        switch (m_alignmentVertical)
        {
            case Alignment::Center:
            {
                l_position.y() -= std::floor(0.5f * l_size.y());
                break;
            }
            case Alignment::Bottom:
            {
                l_position.y() -= l_size.y();
                break;
            }
            default:
            {
                break;
            }
        }
        //! handle alignment relative to origin
        if (Origin::TopLeft == m_origin)
        {
            l_position.y() = f_viewport.m_size.y() - l_position.y() - l_size.y(); // PRQA S 3011
        }

        if (0u == getNumDrawables())
        {
            osg::Vec4Array* l_colorArray = nullptr;

            osg::Geometry* const foregroundGeometry = createGeometry();
            l_colorArray                      = static_cast<osg::Vec4Array*>(foregroundGeometry->getColorArray()); // PRQA S 3076
            (*l_colorArray)[0] =
                (l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY
                     ? m_settings->m_day.m_foregroundColor
                     : m_settings->m_night.m_foregroundColor);

            osg::Geometry* const backgroundGeometry = createGeometry();
            l_colorArray                      = static_cast<osg::Vec4Array*>(backgroundGeometry->getColorArray()); // PRQA S 3076
            (*l_colorArray)[0] =
                (l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY
                     ? m_settings->m_day.m_backgroundColor
                     : m_settings->m_night.m_backgroundColor);
            addDrawable(backgroundGeometry); // PRQA S 3803
            addDrawable(foregroundGeometry); // PRQA S 3803

            // Value Display
            // const osg::ref_ptr<osgText::Text> l_BrightnessValue = new osgText::Text;
            // l_BrightnessValue->setPosition(m_settings->m_brightnessValuePos);
            // l_BrightnessValue->setFont(m_settings->m_fontType);
            // l_BrightnessValue->setColor(m_settings->m_night.m_digitalColor);
            // l_BrightnessValue->setDrawMode(osgText::TextBase::TEXT); // PRQA S 3143
            // l_BrightnessValue->setCharacterSize(m_settings->m_brightnessValueSize);
            // l_BrightnessValue->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
            // l_BrightnessValue->setAxisAlignment(osgText::Text::XY_PLANE);
            // l_BrightnessValue->setRotation(osg::Quat(osg::DegreesToRadians(0.0f), osg::Z_AXIS));
            // l_BrightnessValue->setAlignment(osgText::Text::CENTER_CENTER);
            // addDrawable(l_BrightnessValue); // PRQA S 3803

            // Move Lines
            // osg::Geometry* const l_leftMoveLineGeometry = createGeometry();
            // l_colorArray = static_cast<osg::Vec4Array*>(l_leftMoveLineGeometry->getColorArray()); // PRQA S 3076
            // (*l_colorArray)[0] =
            //     (l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY
            //          ? m_settings->m_day.m_moveLineColor
            //          : m_settings->m_night.m_moveLineColor);
            // osg::Geometry* const l_rightMoveLineGeometry = createGeometry();
            // l_colorArray = static_cast<osg::Vec4Array*>(l_rightMoveLineGeometry->getColorArray()); // PRQA S 3076
            // (*l_colorArray)[0] =
            //     (l_themeTypeDayNight == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY
            //          ? m_settings->m_day.m_moveLineColor
            //          : m_settings->m_night.m_moveLineColor);
            // addDrawable(l_leftMoveLineGeometry); // PRQA S 3803
            // addDrawable(l_rightMoveLineGeometry); // PRQA S 3803
        }
        updateGeometry(getBackgroundGeometry(), l_position, l_size);
        updateGeometry(getForegroundGeometry(), l_position, {l_size.x() * m_percentage, l_size.y()});
        // const osg::Vec2f l_leftMoveLinePos = {
        //     l_position.x() + (l_size.x() * m_percentage) - m_settings->m_leftMoveLineOffset,
        //     l_position.y() + l_size.y() * 0.5f - m_settings->m_moveLineSize.y() * 0.5f};
        // const osg::Vec2f l_rightMoveLinePos = {
        //     l_position.x() + (l_size.x() * m_percentage) - m_settings->m_rightMoveLineOffset,
        //     l_position.y() + l_size.y() * 0.5f - m_settings->m_moveLineSize.y() * 0.5f};

        // updateGeometry(getMoveLeftLine(), l_leftMoveLinePos,  m_settings->m_moveLineSize);
        // updateGeometry(getMoveRightLine(), l_rightMoveLinePos,  m_settings->m_moveLineSize);

        m_topLeftCorner = l_position;
        m_dirty         = false;
    }
}

void SliderIcon::setForegroundColor(osg::Vec4f f_color)
{
    osg::Geometry* const foregroundGeometry = getForegroundGeometry();
    if (foregroundGeometry == nullptr)
    {
        return;
    }
    osg::Vec4Array* const l_colorArray = static_cast<osg::Vec4Array*>(foregroundGeometry->getColorArray()); // PRQA S 3076
    (*l_colorArray)[0u]          = f_color;
    l_colorArray->dirty();
}

void SliderIcon::setBackgroundColor(osg::Vec4f f_color)
{
    osg::Geometry* const backgroundGeometry = getBackgroundGeometry();
    if (backgroundGeometry == nullptr)
    {
        return;
    }
    osg::Vec4Array* const l_colorArray = static_cast<osg::Vec4Array*>(backgroundGeometry->getColorArray()); // PRQA S 3076
    (*l_colorArray)[0u]          = f_color;
    l_colorArray->dirty();
}

void SliderIcon::setMoveLinesColor(osg::Vec4f f_color)
{
    osg::Geometry* const l_moveLeftLineGeometry  = getMoveLeftLine();
    osg::Geometry* const l_moveRightLineGeometry = getMoveRightLine();
    if ((l_moveLeftLineGeometry == nullptr) || (l_moveRightLineGeometry == nullptr))
    {
        return;
    }
    osg::Vec4Array* const l_moveLeftLineColorArray  = static_cast<osg::Vec4Array*>(l_moveLeftLineGeometry->getColorArray()); // PRQA S 3076
    osg::Vec4Array* const l_moveRightLineColorArray = static_cast<osg::Vec4Array*>(l_moveRightLineGeometry->getColorArray()); // PRQA S 3076
    (*l_moveLeftLineColorArray)[0u]           = f_color;
    (*l_moveRightLineColorArray)[0u]          = f_color;
    l_moveLeftLineColorArray->dirty();
    l_moveRightLineColorArray->dirty();
}

void SliderIcon::updateColor(cc::target::common::EThemeTypeDayNight f_dayNightTheme)
{
    if (2u <= getNumDrawables()) // >=2u
    {
        if (m_dayNightTheme != f_dayNightTheme)
        {
            m_dayNightTheme = f_dayNightTheme;
        }
        if (f_dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEME_TYPE_DAYNIGHT_NIGHT)
        {
            setForegroundColor(m_settings->m_night.m_foregroundColor);
            setBackgroundColor(m_settings->m_night.m_backgroundColor);
            // osgText::Text* const l_brightnessValueStatus = static_cast<osgText::Text*>(this->getDrawable(2u)); // PRQA S 3076
            // l_brightnessValueStatus->setColor(m_settings->m_night.m_digitalColor);
        }
        else if (f_dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        {
            setForegroundColor(m_settings->m_day.m_foregroundColor);
            setBackgroundColor(m_settings->m_day.m_backgroundColor);
            // osgText::Text* const l_brightnessValueStatus = static_cast<osgText::Text*>(this->getDrawable(2u)); // PRQA S 3076
            // l_brightnessValueStatus->setColor(m_settings->m_day.m_digitalColor);
        }
        else{}
    }
    }

void SliderIcon::sendBrightnessValue(vfc::float32_t f_percentage)
{
    if (f_percentage < 0.01f)
    {
        f_percentage = 0.01f;
    }
    if (f_percentage > 1.0f)
    {
        f_percentage = 1.0f;
    }

    vfc::uint8_t birghtnessValueOutput = 0;
    birghtnessValueOutput =
        static_cast<vfc::uint8_t>(std::round(f_percentage * static_cast<vfc::float32_t>(m_settings->m_sliderTopValue))); // PRQA S 3016
    if (birghtnessValueOutput > m_settings->m_sliderTopValue)
    {
        birghtnessValueOutput = static_cast<vfc::uint8_t>(m_settings->m_sliderTopValue);
    }
    // updateValue(static_cast<int>(birghtnessValueOutput));
    // m_percentage = static_cast<vfc::float32_t>(birghtnessValueOutput) /
    // static_cast<vfc::float32_t>(m_settings->m_sliderTopValue);
    // updateValue(static_cast<vfc::int32_t>(birghtnessValueOutput));
    if(birghtnessValueOutput<1)
    {
        birghtnessValueOutput=1;
    }
    if (cc::daddy::CustomDaddyPorts::sm_displayBrightnessRequest_SenderPort.isConnected())
    {
        cc::daddy::DisplayBrightnessRequestDaddy_t& l_displayBrightnessRequest =
            cc::daddy::CustomDaddyPorts::sm_displayBrightnessRequest_SenderPort.reserve();
        l_displayBrightnessRequest.m_Data = birghtnessValueOutput;
        cc::daddy::CustomDaddyPorts::sm_displayBrightnessRequest_SenderPort.deliver();
    }
    else
    {
        XLOG_ERROR(g_AppContext, "displayBrightnessRequest_SenderPort unconnected");
    }
    XLOG_INFO(g_AppContext, "sendPercentageSignal: " << static_cast<vfc::int32_t>(birghtnessValueOutput));
}

void SliderIcon::updateRealPercentage()
{
    bool displayBrightnessPortHaveData = true;
    m_dirty                            = true;
    GET_PORT_DATA(
        displayBrightnessStatusContainer,
        m_framework->asCustomFramework()->m_displayBrightnessStatus_ReceiverPort,
        displayBrightnessPortHaveData)

    if (displayBrightnessPortHaveData)
    {
        const uint8_t l_displayBrightnessStatusReceived = displayBrightnessStatusContainer->m_Data;
        if (l_displayBrightnessStatusReceived > m_settings->m_sliderTopValue)
        {
            XLOG_ERROR(
                g_AppContext,
                "Invalid displayBrightnessStatus  data into slider: "
                    << static_cast<int>(l_displayBrightnessStatusReceived));
        }
        else
        {
            m_percentage = static_cast<vfc::float32_t>(l_displayBrightnessStatusReceived) /
                           static_cast<vfc::float32_t>(m_settings->m_sliderTopValue);
            updateValue(static_cast<vfc::int32_t>(l_displayBrightnessStatusReceived));
        }
    }
    else
    {
        // XLOG_ERROR(g_AppContext, "No displayBrightnessStatus into slider");
        m_percentage = 1.0f / static_cast<vfc::float32_t>(m_settings->m_sliderTopValue);
    }
}

osg::Geometry* SliderIcon::createGeometry() const
{
    osg::Geometry* const l_geometry = new osg::Geometry;
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);
    l_geometry->setVertexArray(new osg::Vec3Array(4));

    osg::Vec4Array* const l_colors = new osg::Vec4Array(1);
    (*l_colors)[0]           = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colors, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(osg::PrimitiveSet::TRIANGLES, 6u);
    (*l_indices)[0]                   = 0u;
    (*l_indices)[1]                   = 1u;
    (*l_indices)[2]                   = 2u;
    (*l_indices)[3]                   = 2u;
    (*l_indices)[4]                   = 3u;
    (*l_indices)[5]                   = 0u;
    l_geometry->addPrimitiveSet(l_indices); // PRQA S 3803

    return l_geometry;
}

void SliderIcon::updateGeometry(osg::Geometry* f_geometry, const osg::Vec2f& f_origin, const osg::Vec2f& f_size) const
{
    if (f_geometry == nullptr)
    {
        return;
    }
    osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*>(f_geometry->getVertexArray()); // PRQA S 3076
    (*l_vertices)[0]           = osg::Vec3(f_origin, 0.0f);
    (*l_vertices)[1]           = osg::Vec3(f_origin.x() + f_size.x(), f_origin.y(), 0.0f);
    (*l_vertices)[2]           = osg::Vec3(f_origin + f_size, 0.0f);
    (*l_vertices)[3]           = osg::Vec3(f_origin.x(), f_origin.y() + f_size.y(), 0.0f);

    l_vertices->dirty();
    f_geometry->dirtyBound();
}

bool SliderIcon::touchInsideResponseArea(vfc::int32_t f_x, vfc::int32_t f_y) const
{
    static constexpr vfc::float32_t TOUCH_THRESHOLD = 10.0f; // pixel
    const vfc::int32_t touchX = f_x - m_viewport.m_origin.x();
    const vfc::int32_t touchY = pc::core::g_systemConf->m_mainViewport.m_size.y() - f_y - m_viewport.m_origin.y();
    const vfc::float32_t lowerX = m_topLeftCorner.x() - TOUCH_THRESHOLD;
    const vfc::float32_t upperX = m_topLeftCorner.x() + m_size.x() + TOUCH_THRESHOLD;
    const vfc::float32_t lowerY = m_topLeftCorner.y() - TOUCH_THRESHOLD;
    const vfc::float32_t upperY = m_topLeftCorner.y() + m_size.y() + TOUCH_THRESHOLD;
    const bool insideX = (vfc::float32_t(touchX) >= lowerX) && (vfc::float32_t(touchX) <= upperX);
    const bool insideY = (vfc::float32_t(touchY) >= lowerY) && (vfc::float32_t(touchY) <= upperY);
    return insideX && insideY;
}

void SliderIcon::updateValue(vfc::int32_t f_sliderValue)
{
    if (4u < getNumDrawables()) // >=5u
    {
        osgText::Text* const l_brightnessValueStatus = static_cast<osgText::Text*>(this->getDrawable(2u)); // PRQA S 3076
        l_brightnessValueStatus->setText(std::to_string(f_sliderValue));
    }
}

Slider::Slider(pc::core::Framework* f_framework, osg::Camera* f_referenceView)
    : osg::Group{}
    , m_referenceView{f_referenceView}
    , m_hudCamera{nullptr}
    , m_framework{f_framework}
{
    setNumChildrenRequiringUpdateTraversal(1);
    setCullingActive(false);

    m_hudCamera = new osg::Camera;
    m_hudCamera->setReferenceFrame(osg::Transform::ABSOLUTE_RF);
    m_hudCamera->setClearMask(0u);
    m_hudCamera->setRenderOrder(osg::Camera::POST_RENDER);
    m_hudCamera->setAllowEventFocus(false);
    CameraCallback* const l_cameraCallback = new CameraCallback(this, m_framework); // PRQA S 4262
    m_hudCamera->setUpdateCallback(l_cameraCallback);
    m_hudCamera->setCullCallback(l_cameraCallback);

    osg::StateSet* const l_stateSet = m_hudCamera->getOrCreateStateSet();
    l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_hudElements, "RenderBin");
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicColor");
    l_basicTexShader.apply(l_stateSet); // PRQA S 3803
    osg::Group::addChild(m_hudCamera); // PRQA S 3803
}

void Slider::addSlider(SliderIcon* f_sliderIcon) // PRQA S 4211
{
    m_hudCamera->addChild(f_sliderIcon); // PRQA S 3803
}

void Slider::removeSlider(SliderIcon* f_sliderIcon) // PRQA S 4211
{
    m_hudCamera->removeChild(f_sliderIcon); // PRQA S 3803
}

} // namespace button
} // namespace assets
} // namespace cc
