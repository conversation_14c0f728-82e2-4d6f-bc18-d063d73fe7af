//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: EXTERNAL Castellane Florian (Altran, CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Vehicle.cpp
/// @brief
//=============================================================================

#include "cc/assets/common/inc/Vehicle.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/assets/vehiclemodel/inc/DoorAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/RenderBinSetter.h"
#include "pc/svs/assets/vehiclemodel/inc/WheelAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/assets/vehiclemodel/inc/BodyAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/Utils.h"
#include "cc/core/inc/CustomMechanicalData.h"

#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/generic/util/cli/inc/CommandLineInterface.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/common/inc/LightStateClasses.h"
#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"

#include "osg/Uniform"
#include "osg/StateSet"

#include <memory>
#include <unordered_map>
#include <array>

using pc::util::logging::g_EngineContext;
using pc::util::logging::g_AppContext;

#define ST_HC_VEHICLES

namespace cc
{
namespace assets
{
namespace common
{

pc::util::coding::Item<CustomVehicleModelSettings> g_modelSettings("CustomVehicleModel");
pc::util::coding::Item<CustomVehicleColorSettings> g_modelColorSettings("CustomVehicleColorSettings");
constexpr char* const g_uniformNameDoorOpen = "u_openDoorColor";

//!
//! \brief Custom implementation of OSG's StateSet getOrCreateUniform method
//! which allows setting the override value
//!
osg::Uniform* getOrCreateUniform(
  osg::StateSet* f_stateSet,
  const std::string& f_uniformName,
  osg::Uniform::Type f_type,
  osg::StateAttribute::OverrideValue f_value)
{
  if (f_stateSet != nullptr)
  {
    osg::Uniform* l_uniform = f_stateSet->getUniform(f_uniformName);
    if (((l_uniform != nullptr) && (l_uniform->getType() == f_type)))
    {
      return l_uniform;
    }
    l_uniform = new osg::Uniform(f_type, f_uniformName);
    f_stateSet->addUniform(l_uniform, f_value);
    return l_uniform;
  }
  return nullptr;
}


//!
//! class VehicleModel
//!
CustomVehicleModel::CustomVehicleModel(pc::core::Framework* f_framework)
  : pc::vehiclemodel::VehicleModel(f_framework)
  , m_customComponents{}
  , m_prevBaseplateState(cc::daddy::SolidBasePlateState::deactivated)
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


CustomVehicleModel::CustomVehicleModel(const CustomVehicleModel& f_other, const osg::CopyOp& f_copyOp)
  : pc::vehiclemodel::VehicleModel(f_other, f_copyOp)
  , m_customComponents(f_other.m_customComponents)
  , m_prevBaseplateState(cc::daddy::SolidBasePlateState::deactivated)
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


void CustomVehicleModel::reset()
{
  m_customComponents.fill(nullptr);
  pc::vehiclemodel::VehicleModel::reset();
}

osg::Camera* findParentCameraWithAbsoluteReferenceFrame(const osg::NodePath& f_nodePath)
{
    osg::NodePath::const_reverse_iterator l_itr = f_nodePath.rbegin();
    while (l_itr != f_nodePath.rend())
    {
        osg::Camera* const l_cam = (*l_itr)->asCamera();
        if ((l_cam != nullptr) && (osg::Transform::ABSOLUTE_RF == l_cam->getReferenceFrame()))
        {
            return l_cam;
        }
        ++l_itr;  // PRQA S 3803
    }
    return nullptr;
}


void updateCameraStateSet(osg::StateSet* f_stateSet, const osg::Matrix& f_inverseViewMatrix)
{
    if (f_stateSet == nullptr)
    {
        return;
    }
    osg::Uniform* const l_viewMatrixInverseUniform = f_stateSet->getOrCreateUniform("osg_ViewMatrixInverse",osg::Uniform::FLOAT_MAT4);
    l_viewMatrixInverseUniform->set(f_inverseViewMatrix);  // PRQA S 3803
}


void CustomVehicleModel::traverse(osg::NodeVisitor& f_nv)
{
    switch (f_nv.getVisitorType())
    {
        case osg::NodeVisitor::UPDATE_VISITOR:
        {
            const cc::daddy::SolidBasePlateStateDaddy* const l_solidBasePlateStateDaddy = getFramework()->asCustomFramework()->m_solidBaseplateState_ReceiverPort.getData();
            if(nullptr !=l_solidBasePlateStateDaddy)
            {
              if( m_prevBaseplateState != l_solidBasePlateStateDaddy->m_Data )
              {
                m_prevBaseplateState = l_solidBasePlateStateDaddy->m_Data;

                osg::Node* const l_node = pc::vehiclemodel::VehicleModel::getComponent(pc::vehiclemodel::VehicleModel::FLOOR_PLANE);

                if(l_node->getNodeMask() != 0u)
                {
                  m_floorplateNodeMask = l_node->getNodeMask();
                }

                switch(l_solidBasePlateStateDaddy->m_Data)
                {
                  case cc::daddy::SolidBasePlateState::activated:
                  {//disable vehicle shadow
                    l_node->setNodeMask (0u);
                    break;
                  }
                  case cc::daddy::SolidBasePlateState::deactivated:
                  {//reenable vehicle shadow
                    l_node->setNodeMask (m_floorplateNodeMask);
                    break;
                  }
                  default:
                  {// do nothing
                    break;
                  }
                }
              }
            }
            osg::StateSet* const l_stateSet = getOrCreateStateSet();
            osg::Uniform* const l_leftHandDriveUniform = getOrCreateUniform(l_stateSet, "u_leftHandDrive", osg::Uniform::BOOL, osg::StateAttribute::OVERRIDE);    // PRQA S 3143
            l_leftHandDriveUniform->set(pc::vehicle::g_mechanicalData->m_leftHandDrive);  // PRQA S 3803
            pc::vehiclemodel::VehicleModel::traverse(f_nv);
        }
        break;

        case osg::NodeVisitor::CULL_VISITOR:
        {
            osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (&f_nv);
            osg::Camera* const l_currentCamera = l_cv->getCurrentCamera();
            osg::StateSet* const l_stateSet = l_currentCamera->getOrCreateStateSet();
            if (osg::Transform::RELATIVE_RF == l_currentCamera->getReferenceFrame())
            {
                osg::Camera* const l_absCam = findParentCameraWithAbsoluteReferenceFrame(f_nv.getNodePath());
                if (l_absCam != nullptr)
                {
                    updateCameraStateSet(l_stateSet, l_absCam->getInverseViewMatrix());
                }
            }
            else
            {
                updateCameraStateSet(l_stateSet, l_currentCamera->getInverseViewMatrix());
            }
            pc::vehiclemodel::VehicleModel::traverse(f_nv);
        }
        break;
        default:
        {
          pc::vehiclemodel::VehicleModel::traverse(f_nv);
          break;
        }
    }
}


//!
//! FrontWheelsGetter
//!
class FrontWheelsGetter : public pc::vehiclemodel::IFinalizer
{
public:

    explicit FrontWheelsGetter(osg::Group* f_targetGroup)
      : m_targetGroup(f_targetGroup)
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        if (f_vehicleModel == nullptr)
        {
            return;
        }
        if (!m_targetGroup.valid())
        {
            return;
        }

        osg::Node* const l_frontLeftWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL);
        osg::Node* const l_frontRightWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL);

        if ((l_frontLeftWheel != nullptr) && (l_frontRightWheel != nullptr))
        {
            osg::Matrix l_translation;
            l_translation.makeTranslate(0.0f, 0.0f, 0.0f); //magic number. Positions car on ground level
            osg::MatrixTransform* const l_matrixTransform = new osg::MatrixTransform(l_translation);

            l_matrixTransform->addChild(l_frontLeftWheel);  // PRQA S 3803
            l_matrixTransform->addChild(l_frontRightWheel);  // PRQA S 3803

            const osg::ref_ptr<osg::StateSet> l_stateSet = l_matrixTransform->getOrCreateStateSet();
            const osg::ref_ptr<osg::Uniform> l_brightnessUniform = getOrCreateUniform(l_stateSet, "brightness", osg::Uniform::FLOAT, osg::StateAttribute::OVERRIDE);    // PRQA S 3143
            l_brightnessUniform->set(1.0f);

            m_targetGroup->addChild(l_matrixTransform);  // PRQA S 3803
        }
    }

protected:

    virtual ~FrontWheelsGetter() = default;

private:

    osg::ref_ptr<osg::Group> m_targetGroup;

    // Private copy constructor and assignment operator
    FrontWheelsGetter(const FrontWheelsGetter&) = delete;
    FrontWheelsGetter& operator=(const FrontWheelsGetter&) = delete;
};


//!
//! AllWheelsGetter
//!
class AllWheelsGetter : public pc::vehiclemodel::IFinalizer
{
public:

    explicit AllWheelsGetter(osg::Group* f_targetGroup)
      : m_targetGroup(f_targetGroup)
    {
    }

    virtual void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)
    {
        if (f_vehicleModel == nullptr)
        {
            return;
        }
        if (!m_targetGroup.valid())
        {
            return;
        }

        osg::Node* const l_frontLeftWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL);
        osg::Node* const l_frontRightWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL);
        osg::Node* const l_rearLeftWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL);
        osg::Node* const l_rearRightWheel = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL);

        if ((l_frontLeftWheel != nullptr) && (l_frontRightWheel != nullptr) && (l_rearLeftWheel != nullptr) && (l_rearRightWheel != nullptr))
        {
            osg::Matrix l_translation;
            l_translation.makeTranslate(0.0f, 0.0f, pc::vehicle::g_mechanicalData->m_wheelRadius); // radius used for the STB overlays
            osg::MatrixTransform* const l_matrixTransform = new osg::MatrixTransform(l_translation);

            l_matrixTransform->addChild(l_frontLeftWheel);  // PRQA S 3803
            l_matrixTransform->addChild(l_frontRightWheel);  // PRQA S 3803
            l_matrixTransform->addChild(l_rearLeftWheel);  // PRQA S 3803
            l_matrixTransform->addChild(l_rearRightWheel);  // PRQA S 3803

            const osg::ref_ptr<osg::StateSet> l_stateSet = l_matrixTransform->getOrCreateStateSet();
            const osg::ref_ptr<osg::Uniform> l_brightnessUniform = getOrCreateUniform(l_stateSet, "brightness", osg::Uniform::FLOAT, osg::StateAttribute::OVERRIDE);    // PRQA S 3143
            l_brightnessUniform->set(1.0f);

            m_targetGroup->addChild(l_matrixTransform);  // PRQA S 3803
            // osg::StateSet* l_stateSet = m_targetGroup->getOrCreateStateSet();
            //! set this wheel node explicitly opaque
            // l_stateSet->addUniform(new osg::Uniform("VehicleTransparency", 0.3f));
            // l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);    // PRQA S 3143
        }
    }

protected:

    virtual ~AllWheelsGetter() = default;

private:

    //! Copy constructor is not permitted.
    AllWheelsGetter (const AllWheelsGetter& other); // = delete
    //! Copy assignment operator is not permitted.
    AllWheelsGetter& operator=(const AllWheelsGetter& other); // = delete

    osg::ref_ptr<osg::Group> m_targetGroup;
};

class WipingIndicatorSignal : public pc::util::coding::ISerializable
{
public:

  WipingIndicatorSignal()
    : m_period(0.3f)
    , m_sustain(0.2f)
    , m_delay(0.3f)
    , m_offset(0.0f)
  {
  }

  SERIALIZABLE(WipingIndicatorSignal)
  {
    if (f_descriptor == nullptr)
    {
      return;
    }
    ADD_FLOAT_MEMBER(period);
    ADD_FLOAT_MEMBER(sustain);
    ADD_FLOAT_MEMBER(delay);
    ADD_FLOAT_MEMBER(offset);
  }


  float getDuration() const
  {
    return m_period + m_sustain + m_delay;
  }

  float getValue(float f_t) const
  {
    f_t += m_offset;
    f_t = std::fmod(f_t, getDuration());
    if (f_t <= m_period)
    {
      // add epsilon to ensure begin of period is always larger than exact 0.0
      return (f_t / m_period) + std::numeric_limits<float>::epsilon();
    }
    else if (f_t <= (m_period + m_sustain))
    {
      return 1.0f;
    }
    else
    {
      //do nothing
    }
    return 0.0f;
  }

  float m_period;
  float m_sustain;
  float m_delay;
  float m_offset;
};

pc::util::coding::Item<WipingIndicatorSignal> g_wipingIndicatorSettings("WipingIndicator");

///
/// WipingIndicatorAnimation
///
class WipingIndicatorAnimation : public osg::NodeCallback
{
public:

  typedef pc::daddy::IndicatorStateDaddy::value_type IndicatorState;

  WipingIndicatorAnimation(pc::core::Framework* f_framework)
    : m_framework(f_framework)
    , m_lastUpdate(0u)
    , m_previousState(pc::daddy::INDICATOR_OFF)
    , m_startTime(-1.0f)
    , m_progress(0.0f)

  {
  }

  void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override
  {
    if ((f_node == nullptr) || (f_nv == nullptr))
    {
        return;
    }
    const osg::FrameStamp* const l_frameStamp = f_nv->getFrameStamp();
    if (m_lastUpdate != l_frameStamp->getFrameNumber())
    {
      update(static_cast<float> (l_frameStamp->getReferenceTime()));
      m_lastUpdate = l_frameStamp->getFrameNumber();
    }
    else
    {
      // Do nothing
    }
    traverse(f_node, f_nv);
  }

  float getProgress() const
  {
    return m_progress;
  }

private:

  void update(float f_referenceTime)
  {
    const IndicatorState l_indicatorState = getIndicatorState();
    if (hasIndicatorStateChanged(l_indicatorState))
    {
      m_startTime = -1.0f;
    }
    if (isWipingEnabled() && isIndicatorActive(l_indicatorState))
    {
      if (0.0f > m_startTime)
      {
        m_startTime = f_referenceTime; // begin new animation cycle
      }
      m_progress = 1.0f - g_wipingIndicatorSettings->getValue(f_referenceTime - m_startTime);
    }
    else
    {
      m_progress = 0.0f;
    }
  }

  IndicatorState getIndicatorState() const
  {
    cc::core::CustomFramework* const l_pCustomFramework = static_cast<cc::core::CustomFramework*>(m_framework);
    if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
    {
        const cc::daddy::CustomVehicleLightsDaddy* const l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();

        if (nullptr != l_pCustomData)
        {
            #ifdef ST_HC_VEHICLES
            if (1u == l_pCustomData->m_Data.m_hazardLightState)
            {
              return pc::daddy::INDICATOR_WARN;
            }
            #else
            if (Turn_Signal_DangerWarningSignal == l_pCustomData->m_Data.m_turnSignalStatus)
            {
              return pc::daddy::INDICATOR_WARN;
            }
            else if(Turn_Signal_LeftTurnSignalLightNormalFlashing == l_pCustomData->m_Data.m_turnSignalStatus)
            {
              return pc::daddy::INDICATOR_LEFT;
            }
            else if(Turn_Signal_RightTurnSignalLightFlashingNormally == l_pCustomData->m_Data.m_turnSignalStatus)
            {
              return pc::daddy::INDICATOR_RIGHT;
            }
            else if(Turn_Signal_NOTWORKING == l_pCustomData->m_Data.m_turnSignalStatus)
            {
              return pc::daddy::INDICATOR_OFF;
            }
            else
            {
              // do nothing
            }
            #endif
        }
    }
    const pc::daddy::IndicatorStateDaddy* const l_state = m_framework->m_indicatorStateReceiver.getData();
    if (l_state != nullptr)
    {
      return l_state->m_Data;
    }
    return pc::daddy::INDICATOR_OFF;
  }

  bool hasIndicatorStateChanged(const IndicatorState& f_state)
  {
    if (f_state != m_previousState)
    {
      m_previousState = f_state;
      return true;
    }
    return false;
  }

  bool isIndicatorActive(const IndicatorState& f_state) const
  {
    return pc::daddy::INDICATOR_OFF != f_state;
  }

  bool isWipingEnabled() const
  {
#if 0 //m_dynIndicatorEnable signal is not mapped in ValIn
    cc::core::CustomFramework* l_pCustomFramework = static_cast<cc::core::CustomFramework*>(m_framework);
    if (l_pCustomFramework->m_VehicleLightsReceiver.isConnected())
    {
        const cc::daddy::CustomVehicleLightsDaddy* l_pCustomData = l_pCustomFramework->m_VehicleLightsReceiver.getData();

        if (0 != l_pCustomData)
        {
            if (1u == l_pCustomData->m_Data.m_dynIndicatorEnable)
            {
              return true;
            }
            else
            {
              // Do nothing
            }
        }
        else
        {
          // Do nothing
        }
    }
    else
    {
      //Do nothing
    }
    return false;
#else
  return true;
#endif
  }

  pc::core::Framework* m_framework;
  unsigned int m_lastUpdate;
  IndicatorState m_previousState;
  float m_startTime;
  float m_progress;


};


static constexpr const char* ANIMATION_UNIFORM_NAME = "alpha";
///
/// WipingIndicatorUpdateCallback
///
class WipingIndicatorCallback : public osg::NodeCallback
{
public:


  WipingIndicatorCallback(WipingIndicatorAnimation* f_wipingIndicatorAnimation)
    : m_wipingIndicatorAnimation(f_wipingIndicatorAnimation)
  {
  }

  static osg::Uniform* getOrCreateAnimationUnifrom(osg::StateSet* f_stateSet)
  {
    if (f_stateSet != nullptr)
    {
      osg::Uniform* l_animationUniform = f_stateSet->getUniform(ANIMATION_UNIFORM_NAME);
      if (nullptr == l_animationUniform)
      {
        l_animationUniform = new osg::Uniform(osg::Uniform::FLOAT, ANIMATION_UNIFORM_NAME);
        f_stateSet->addUniform(l_animationUniform, osg::StateAttribute::OVERRIDE);    // PRQA S 3143
      }
      return l_animationUniform;
    }
    return nullptr;
  }

  void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override
  {
    if ((f_node == nullptr) || (f_nv == nullptr))
    {
        return;
    }
    osg::StateSet* const l_stateSet = f_node->getOrCreateStateSet();
    osg::Uniform* const l_fadeUniform = getOrCreateAnimationUnifrom(l_stateSet);
    const float l_progress = m_wipingIndicatorAnimation->getProgress();
    l_fadeUniform->set(l_progress);  // PRQA S 3803
    traverse(f_node, f_nv);
  }

private:

  osg::ref_ptr<WipingIndicatorAnimation> m_wipingIndicatorAnimation;

};

class LightStateParser : public lightstate::LightStateJsonParser
{
public:

  LightStateParser(
    const std::shared_ptr<lightstate::SignalWrapperRegistry>& f_registry,
    WipingIndicatorAnimation* f_wipingIndicatorAnimation)
    : lightstate::LightStateJsonParser(f_registry)
    , m_wipingIndicatorAnimation(f_wipingIndicatorAnimation)
  {
  }

  void onHint(const std::string& f_hint, osg::Node* f_node) override
  {
    if (f_node == nullptr)
    {
        return;
    }
    if (f_hint == "wiping_indicator")
    {
      f_node->addUpdateCallback(new WipingIndicatorCallback(m_wipingIndicatorAnimation.get()));
    }
  }

private:

  osg::ref_ptr<WipingIndicatorAnimation> m_wipingIndicatorAnimation;

};

///
/// LightFinalizer
///
class LightFinalizer : public pc::vehiclemodel::IFinalizer
{
public:

  explicit LightFinalizer(pc::core::Framework* f_framework)
    : m_framework(f_framework)
    , m_registry(std::make_shared<lightstate::SignalWrapperRegistry>(f_framework))
  {
    m_registry->registerSignal("Light", std::make_shared<lightstate::HeadlightWrapper>());
    m_registry->registerSignal("IndicatorLeft", std::make_shared<lightstate::IndicatorLeftWrapper>());
    m_registry->registerSignal("IndicatorRight", std::make_shared<lightstate::IndicatorRightWrapper>());
    m_registry->registerSignal("Brake", std::make_shared<lightstate::BrakeLightWrapper>());
    m_registry->registerSignal("Reverse", std::make_shared<lightstate::ReverseGearWrapper>());
  }

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
  {
    // install global vehicle model updated callbacks to nested root-node (actual root node of loaded OSG file),
    // so they will be properly removed in case of a model reset
    osg::Group* l_nestedRoot = nullptr;
    if (0u < f_vehicleModel->getNumChildren())
    {
      l_nestedRoot = dynamic_cast<osg::Group*> (f_vehicleModel->getChild(0u)); // PRQA S 3077
    }
    // parse light node json file
    const osg::ref_ptr<WipingIndicatorAnimation> l_wipingIndicatorAnimation = new WipingIndicatorAnimation(m_framework);
    LightStateParser l_parser(m_registry, l_wipingIndicatorAnimation.get());
    lightstate::LightNodeUpdateCallback* const l_lightNodeUpdateCallback = l_parser.parse(g_modelSettings->m_lightStateFilePath, f_vehicleModel);
    if ((l_lightNodeUpdateCallback != nullptr) && (l_nestedRoot != nullptr))
    {
      l_nestedRoot->addUpdateCallback(l_lightNodeUpdateCallback);
      l_nestedRoot->addUpdateCallback(l_wipingIndicatorAnimation.get());
    }
  }

protected:

  virtual ~LightFinalizer() = default;

private:

  LightFinalizer(const LightFinalizer&) = delete;
  LightFinalizer& operator=(const LightFinalizer&) = delete;

  pc::core::Framework* m_framework;
  std::shared_ptr<lightstate::SignalWrapperRegistry> m_registry;

};


//!
//! VehicleNodeMaskFinalizer
//!
class VehicleNodeMaskFinalizer : public pc::vehiclemodel::IFinalizer
{
public:

  explicit VehicleNodeMaskFinalizer(pc::core::Framework* f_pFramework)
      : m_pFramework(f_pFramework)
    {

    }

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)  override // PRQA S 6041
  {
    if (f_vehicleModel == nullptr)
    {
        return;
    }
    osg::Node* l_node = nullptr;

    // set all node cull setting to default
    constexpr vfc::uint32_t l_otherComponentsNodeMask = 0xffffffu & ~( static_cast<vfc::uint32_t> (
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK)            |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK)           |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK)             |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK)            |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_INTERIOR_MASK)   |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MASK)            |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_INTERIOR_MASK)    |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MASK)             |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR_MASK)              |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR_MASK)             |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::TRUNK_DOOR_MASK)                  |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::HOOD_DOOR_MASK)                   |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::INTERIOR_MASK)                    |
                                                                static_cast<vfc::uint32_t>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK)                 ));
    pc::util::osgx::NodeMaskSetter l_nodeMaskParser(l_otherComponentsNodeMask);
    f_vehicleModel->accept(l_nodeMaskParser);

    // const bool l_isOptional = ((cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_4DOOR) && (cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_5DOOR));
    // const bool l_isOptional = false;

    // Name does not exactly match "Scene Root", since vehicle variant is included -> use node name finder
    pc::util::osgx::NodeNameFinder l_nodeFinder("Scene Root");
    f_vehicleModel->accept(l_nodeFinder);
    l_node = l_nodeFinder.getFoundNode();
    if (l_node != nullptr)
    {
      l_node->setNodeMask(0xffffffu);

      // Publish actual root name
      cc::core::CustomScene* const l_scene = static_cast<cc::core::CustomScene*> (m_pFramework->getScene());
      if (l_scene != nullptr)
      {
          l_scene->setVehicleModelName( l_nodeFinder.getNodeName() );
      }

    }
    f_vehicleModel->setNodeMask(0xffffffu);

    // Modify the cull mask for the important parts
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FLOOR_PLANE);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FLOOR_PLANE_MASK));
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::INTERIOR);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::INTERIOR_MASK));
    }

    // Wheels masks
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK));
      //set the children with the right nodemask
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser1(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK));
      l_node->accept(l_nodeMaskParser1);
    }
    // brake caliper
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_BRAKE);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL_MASK));
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK));
      //set the children with the right nodemask
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser2(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK));
      l_node->accept(l_nodeMaskParser2);
    }
    // brake caliper
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_BRAKE);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_LEFT_WHEEL_MASK));
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK));
      //set the children with the right nodemask
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser3(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK));
      l_node->accept(l_nodeMaskParser3);
    }
    // brake caliper
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_BRAKE);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_WHEEL_MASK));
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK));
      //set the children with the right nodemask
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser4(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK));
      l_node->accept(l_nodeMaskParser4);
    }
    // brake caliper
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_BRAKE);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL_MASK));
    }


    //doors masks
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MASK));
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser5(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MASK));
      l_node->accept(l_nodeMaskParser5);
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MASK));
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser6(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MASK));
      l_node->accept(l_nodeMaskParser6);
    }

    // Load the rear doors if the car has 4 or 5 doors
    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR_MASK));
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser7(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR_MASK));
      l_node->accept(l_nodeMaskParser7);
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR_MASK));
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser8(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR_MASK));
      l_node->accept(l_nodeMaskParser8);
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::TRUNK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::TRUNK_DOOR_MASK));
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser9(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::TRUNK_DOOR_MASK));
      l_node->accept(l_nodeMaskParser9);
    }

    l_node = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::HOOD);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::HOOD_DOOR_MASK));
      pc::util::osgx::NodeMaskSetter l_nodeMaskParser10(static_cast<unsigned int>(pc::vehiclemodel::VehicleModel::HOOD_DOOR_MASK));
      l_node->accept(l_nodeMaskParser10);
    }

    //osgDB::writeNodeFile(*f_vehicleModel, "D:/temp/VehcileCulling.osg");
  }

private:
  pc::core::Framework* m_pFramework;

};


//!
//! VehicleDoorsGetteretter
//!
class VehicleDoorsGetter : public pc::vehiclemodel::IFinalizer
{
public:

    explicit VehicleDoorsGetter(osg::Group* f_targetGroup)
      : m_targetGroup(f_targetGroup)
    {
    }

    virtual void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)
    {
        if (f_vehicleModel == nullptr)
        {
            return;
        }
        if (!m_targetGroup.valid())
        {
            return;
        }

        osg::Node* const l_frontLeftDoor  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR);
        osg::Node* const l_frontRightDoor = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR);
        osg::Node* const l_rearLeftDoor   = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR);
        osg::Node* const l_rearRightDoor  = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR);
        osg::Node* const l_trunkDoor      = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::TRUNK);
        osg::Node* const l_hoodDoor       = f_vehicleModel->getComponent(pc::vehiclemodel::VehicleModel::HOOD);

        if (l_frontLeftDoor != nullptr)
        {
            m_targetGroup->addChild(l_frontLeftDoor);  // PRQA S 3803
        }

        if (l_frontRightDoor != nullptr)
        {
            m_targetGroup->addChild(l_frontRightDoor);  // PRQA S 3803
        }

        if (l_rearLeftDoor != nullptr)
        {
            m_targetGroup->addChild(l_rearLeftDoor);  // PRQA S 3803
        }

        if (l_rearRightDoor != nullptr)
        {
            m_targetGroup->addChild(l_rearRightDoor);  // PRQA S 3803
        }

        if (l_trunkDoor != nullptr)
        {
            m_targetGroup->addChild(l_trunkDoor);  // PRQA S 3803
        }

        if (l_hoodDoor != nullptr)
        {
            m_targetGroup->addChild(l_hoodDoor);  // PRQA S 3803
        }
        if ((l_frontLeftDoor != nullptr) || (l_frontRightDoor != nullptr) ||
            (l_rearLeftDoor != nullptr)  || (l_rearRightDoor != nullptr)  ||
            (l_trunkDoor != nullptr)     || (l_hoodDoor != nullptr))
        {
            osg::StateSet* const l_stateSet = m_targetGroup->getOrCreateStateSet();
            //! set this door node explicitly opaque
            l_stateSet->addUniform(new osg::Uniform("VehicleTransparency", 1.0f));
            l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);    // PRQA S 3143
        }
    }

protected:

    virtual ~VehicleDoorsGetter() = default;

private:

    //! Copy constructor is not permitted.
    VehicleDoorsGetter (const VehicleDoorsGetter& other); // = delete
    //! Copy assignment operator is not permitted.
    VehicleDoorsGetter& operator=(const VehicleDoorsGetter& other); // = delete

    osg::ref_ptr<osg::Group> m_targetGroup;
};



ColorSettings getColorSettingFromIndex(vfc::uint8_t /*f_index*/)
{
  ColorSettings l_colorSetting = g_modelColorSettings->m_TimeGray;
  return l_colorSetting;
}

//!
//! Vehicle Diffuse Color
//!
void CarPaintFinalizer::VehicleDiffuseColorUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* /* f_nv */) // PRQA S 6043
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_indexReceiver.isConnected() )
    {
        if(true == m_indexReceiver.hasNewData())
        {
            const cc::daddy::ColorIndexDaddy* const l_pIndexDaddy = m_indexReceiver.getData();
            if (nullptr != l_pIndexDaddy)
            {
                ColorSettings l_colorSetting = getColorSettingFromIndex(l_pIndexDaddy->m_Data);
                uniform->set( osg::Vec4f( static_cast<float>(l_colorSetting.m_diffuseColor.x()) / 255.f,  // PRQA S 3803
                                          static_cast<float>(l_colorSetting.m_diffuseColor.y()) / 255.f,
                                          static_cast<float>(l_colorSetting.m_diffuseColor.z()) / 255.f,
                                          static_cast<float>(l_colorSetting.m_diffuseColor.a()) / 255.f) );
            }
        }
    }
}

//!
//! specColor1 Update
//!
void CarPaintFinalizer::VehicleSpecColor1UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set( osg::Vec4f( static_cast<float>(l_colorSetting.m_specColor1.x()) / 255.f,  // PRQA S 3803
                                  static_cast<float>(l_colorSetting.m_specColor1.y()) / 255.f,
                                  static_cast<float>(l_colorSetting.m_specColor1.z()) / 255.f,
                                  static_cast<float>(l_colorSetting.m_specColor1.a()) / 255.f) );
    }
}

//!
//! VehicleSpecShininess1UpdateCallback
//!
void CarPaintFinalizer::VehicleSpecShininess1UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_specShininess1);  // PRQA S 3803
    }
}

//!
//! SpecColor2 Update
//!
void CarPaintFinalizer::VehicleSpecColor2UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set( osg::Vec4f( static_cast<float>(l_colorSetting.m_specColor2.x()) / 255.f,  // PRQA S 3803
                                  static_cast<float>(l_colorSetting.m_specColor2.y()) / 255.f,
                                  static_cast<float>(l_colorSetting.m_specColor2.z()) / 255.f,
                                  static_cast<float>(l_colorSetting.m_specColor2.a()) / 255.f) );
    }
}

//!
//! VehicleSpecShininess2UpdateCallback
//!
void CarPaintFinalizer::VehicleSpecShininess2UpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_specShininess2);  // PRQA S 3803
    }
}


//!
//! ReflectionUpdateCallback
//!
void CarPaintFinalizer::ReflectionUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();

    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*       const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_reflectionPower);  // PRQA S 3803
    }
}

//!
//! VehicleFresnelUpdateCallback
//!
void CarPaintFinalizer::VehicleFresnelUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_fresnel);  // PRQA S 3803
    }
}


//!
//! LightPositionUpdateCallback
//!
void CarPaintFinalizer::LightPositionUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();

    if ( l_pCustomFramework->m_SVSRotateStatusDaddy_Receiver.isConnected() &&
    l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected() )
    {
        const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType  = l_pCustomFramework->m_SVSRotateStatusDaddy_Receiver.getData();
        const cc::daddy::ColorIndexDaddy*       const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        osg::Vec3f l_lightPos = g_modelColorSettings->m_TimeGray.m_lightPos; // default color is Time gray
        if (nullptr != l_themeType)
        {
            if (cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI == static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data))
            {
              l_lightPos = l_colorSetting.m_lightPos;
            }
            else  //cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT
            {
              l_lightPos = osg::Vec3f(-l_colorSetting.m_lightPos.y(), l_colorSetting.m_lightPos.x(), l_colorSetting.m_lightPos.z());
            }
        }
        uniform->set(l_lightPos);    // PRQA S 3803
    }
}


//!
//! BrightnessUpdateCallback
//!
void CarPaintFinalizer::BrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    bool l_nightMode = false;
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    if ( l_pCustomFramework->m_VehicleLightsReceiver.isConnected() )
    {
      const cc::daddy::CustomVehicleLightsDaddy* const l_lightState = l_pCustomFramework->m_VehicleLightsReceiver.getData();
      if (l_lightState != nullptr)
      {
        if ( true == l_lightState->m_Data.m_mainBeamIndication )
        {
          l_nightMode = true;
        }
        if ( true == l_lightState->m_Data.m_lowBeamIndication )
        {
          l_nightMode = true;
        }
      }
    }

    if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_nightMode ? (l_colorSetting.m_brightness - l_colorSetting.m_nightBrightnessOffset) : (l_colorSetting.m_brightness));  // PRQA S 3803
    }
}

//!
//! VehicleplasticDiffuseColorUpdateCallback
//!
void CarPaintFinalizer::VehicleplasticDiffuseColorUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* /* f_nv */) // PRQA S 6043
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy* const l_pIndexDaddy = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        if (nullptr != l_pIndexDaddy)
        {
            ColorSettings l_colorSetting = getColorSettingFromIndex(l_pIndexDaddy->m_Data);
            uniform->set( osg::Vec4f( (l_colorSetting.m_plasticDiffuseColor.x()) / 255.f,  // PRQA S 3803
                                      (l_colorSetting.m_plasticDiffuseColor.y()) / 255.f,
                                      (l_colorSetting.m_plasticDiffuseColor.z()) / 255.f,
                                      (l_colorSetting.m_plasticDiffuseColor.a()) / 255.f) );
        }
    }
}

//!
//! VehicleplasticSpecColorUpdateCallback
//!
void CarPaintFinalizer::VehicleplasticSpecColorUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set( osg::Vec4f( (l_colorSetting.m_plasticSpecColor.x()) / 255.f,  // PRQA S 3803
                                  (l_colorSetting.m_plasticSpecColor.y()) / 255.f,
                                  (l_colorSetting.m_plasticSpecColor.z()) / 255.f,
                                  (l_colorSetting.m_plasticSpecColor.a()) / 255.f) );
    }
}

//!
//! VehicleplasticSpecShininessUpdateCallback
//!
void CarPaintFinalizer::VehicleplasticSpecShininessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_plasticSpecShininess);  // PRQA S 3803
    }
}

//!
//! VehicleplasticBrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleplasticBrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_plasticBrightness);  // PRQA S 3803
    }
}

//!
//! VehicleChrome2BrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleChrome2BrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_chrome2Brightness);  // PRQA S 3803
    }
}

//!
//! VehicleChrome3BrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleChrome3BrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_chrome3Brightness);  // PRQA S 3803
    }
}

//!
//! VehicleVeh2dBrightnessUpdateCallback
//!
void CarPaintFinalizer::VehicleVeh2dBrightnessUpdateCallback::update(osg::Uniform* uniform, osg::NodeVisitor* )
{
    if (uniform == nullptr)
    {
        return;
    }
    if ( m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
    {
        const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
        const ColorSettings l_colorSetting = getColorSettingFromIndex(l_pColorStateDaddy->m_Data);
        uniform->set(l_colorSetting.m_veh2dBrightness);  // PRQA S 3803
    }
}

class DoorWarningUpdateCallback : public osg::NodeCallback
{
public:

  explicit DoorWarningUpdateCallback(pc::core::Framework* pFramework, std::string f_uniform, pc::vehiclemodel::VehicleModel::Component f_componentId)
    : m_componentId(f_componentId),
      m_pFramework(pFramework),
      m_uniformName(f_uniform)
  {
  }

  virtual ~DoorWarningUpdateCallback() = default;

  const std::string getUniformName() const { return m_uniformName;}

  void operator () (osg::Node* f_node, osg::NodeVisitor* f_nv) override
  {
      update(f_node, f_nv);
      traverse(f_node, f_nv);
  }

  void update(osg::Node* f_node,  osg::NodeVisitor* f_nv);

protected:

  bool getSignalState() const;
  pc::vehiclemodel::VehicleModel::Component m_componentId;

private:

  //! Copy constructor is not permitted.
  DoorWarningUpdateCallback (const DoorWarningUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  DoorWarningUpdateCallback& operator=(const DoorWarningUpdateCallback& other); // = delete

  pc::core::Framework* m_pFramework;

  std::string m_uniformName;
};

void DoorWarningUpdateCallback::update(osg::Node* f_node,  osg::NodeVisitor* /*f_nv*/)
{
    if (f_node == nullptr)
    {
        return;
    }
    const bool l_open  = getSignalState();
    osg::StateSet* const l_stateSet = f_node->getOrCreateStateSet();
    osg::Uniform* const l_uniform = l_stateSet->getOrCreateUniform(m_uniformName, osg::Uniform::FLOAT);
    if (l_open)
    {
        l_uniform->set(g_modelSettings->m_doorWarningTrans);  // PRQA S 3803
    }
    else
    {
        l_uniform->set(0.0f);  // PRQA S 3803
    }
}

bool DoorWarningUpdateCallback::getSignalState() const
{
  const pc::daddy::DoorStateDaddy* const l_doorStateDaddy = m_pFramework->m_doorStateReceiver.getData();
  const cc::daddy::DoorLockStsDaddy_t* const l_doorLockStsDaddy = m_pFramework->asCustomFramework()->m_DoorLockSts_ReceiverPort.getData();
  if (nullptr != l_doorStateDaddy &&  nullptr != l_doorLockStsDaddy)
  {
    switch (m_componentId)
    {
      case pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR:
        {return (
            (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_LEFT]) &&
            (cc::target::common::EStateDoorLock::DoorLock_OPEN       == l_doorLockStsDaddy->m_Data.m_FLdoorLockStatus));}
      case pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR:
        {return (
            (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_FRONT_RIGHT]) &&
            (cc::target::common::EStateDoorLock::DoorLock_OPEN       == l_doorLockStsDaddy->m_Data.m_FRdoorLockStatus));}
      case pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR:
        {return (
            (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_LEFT]) &&
            (cc::target::common::EStateDoorLock::DoorLock_OPEN       == l_doorLockStsDaddy->m_Data.m_RLdoorLockStatus));}
      case pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR:
        {return (
            (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_REAR_RIGHT]) &&
            (cc::target::common::EStateDoorLock::DoorLock_OPEN       == l_doorLockStsDaddy->m_Data.m_RRdoorLockStatus));}
      case pc::vehiclemodel::VehicleModel::TRUNK:
        {return (
            (pc::daddy::CARDOORSTATE_CLOSED == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_TRUNK]) &&
            (cc::target::common::EStateDoorLock::DoorLock_OPEN       == l_doorLockStsDaddy->m_Data.m_TrunkLockStatus));}
      case pc::vehiclemodel::VehicleModel::HOOD:
        {return (pc::daddy::CARDOORSTATE_OPEN == l_doorStateDaddy->m_Data[pc::daddy::CARDOOR_HOOD]);}
      default:
        {break;}
    }
  }
  return false;
}

//!
//! DoorsWarningFinalizer
//!
class DoorsWarningFinalizer : public pc::vehiclemodel::IFinalizer
{
public:

  DoorsWarningFinalizer(pc::core::Framework* f_framework)
    : m_framework(f_framework)
  {
  }

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
  {
    if (f_vehicleModel == nullptr)
    {
        return;
    }
    osg::Uniform* const l_uniform = f_vehicleModel->getOrCreateStateSet()->getOrCreateUniform(g_uniformNameDoorOpen, osg::Uniform::FLOAT);
    l_uniform->set(0.0f);  // PRQA S 3803
    typedef std::pair<pc::vehiclemodel::VehicleModel::Component, osg::StateAttribute::OverrideValue> ComponentOverrideValuePair;
    typedef std::array<ComponentOverrideValuePair, 7> ComponentArray;
    constexpr ComponentArray l_doors = {
      {ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR, osg::StateAttribute::OVERRIDE),
      ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR, osg::StateAttribute::OVERRIDE),
      ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::REAR_LEFT_DOOR,osg::StateAttribute::OVERRIDE),
      ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::REAR_RIGHT_DOOR, osg::StateAttribute::OVERRIDE),
      ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::TRUNK, osg::StateAttribute::OVERRIDE),
      ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::FRONT_LEFT_DOOR_MIRROR, static_cast<vfc::uint32_t>(osg::StateAttribute::OVERRIDE) | static_cast<vfc::uint32_t>(osg::StateAttribute::PROTECTED)),
      ComponentOverrideValuePair(pc::vehiclemodel::VehicleModel::FRONT_RIGHT_DOOR_MIRROR, static_cast<vfc::uint32_t>(osg::StateAttribute::OVERRIDE) | static_cast<vfc::uint32_t>(osg::StateAttribute::PROTECTED))}
    };
    for (ComponentArray::const_iterator l_itr = l_doors.begin(); l_itr != l_doors.end(); ++l_itr)
    {
      osg::Node* const l_node = f_vehicleModel->getComponent(l_itr->first);
      if (l_node != nullptr)
      {
        l_node->addUpdateCallback(new DoorWarningUpdateCallback(m_framework, g_uniformNameDoorOpen, l_itr->first));
      }
    }
  }

protected:

  virtual ~DoorsWarningFinalizer() = default;

  DoorsWarningFinalizer(const DoorsWarningFinalizer&) = delete;
  DoorsWarningFinalizer& operator = (const DoorsWarningFinalizer&) = delete;

private:
  pc::core::Framework* m_framework;

};


//!
//! CarpaintTextureFinalizer
//!
class CarpaintTextureFinalizer : public pc::vehiclemodel::IFinalizer
{
public:

  // internal class of CarpaintTextureFinalizer
  class CarpaintTextureCallback : public osg::Image::UpdateCallback
  {
    public:
        explicit CarpaintTextureCallback(pc::core::Framework* pFramework)
            : m_pFramework(pFramework)
        {
        }

        virtual ~CarpaintTextureCallback() = default;
        
        

        void operator()(osg::StateAttribute* attr, osg::NodeVisitor* /*nv*/)
        {
          if (attr == nullptr)
          {
              return;
          }
          if(attr->asTexture() != nullptr)
          {
              osg::Texture2D* const l_tex = (osg::Texture2D*)attr->asTexture();
              cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
              if ( l_pCustomFramework->m_vehColorSts_ReceiverPort.isConnected())
              {
                  const cc::daddy::ColorIndexDaddy*  const l_pColorStateDaddy  = l_pCustomFramework->m_vehColorSts_ReceiverPort.getData();
                  if(m_currentColorIndex != l_pColorStateDaddy->m_Data)
                  {
                    //XLOG_INFO_OS(g_AppContext) << "CarpaintTextureCallback old and new Color Index:" <<m_currentColorIndex <<" "<<l_pColorStateDaddy->m_Data << m_carpaintTexutreKTXFile << XLOG_ENDL;

                    m_currentColorIndex = l_pColorStateDaddy->m_Data;
                    m_needUpdataCarpaintTexture = true;
                    switch (m_currentColorIndex)
                    {
                    case cc::daddy::NISSAN_WHITE:
                    {
                      m_carpaintTexutrePNGFile = g_modelSettings->m_whitePng;
                      m_carpaintTexutreKTXFile = g_modelSettings->m_whiteKtx;
                      break;
                    }
                    case cc::daddy::NISSAN_SLIVER:
                    {
                      m_carpaintTexutrePNGFile = g_modelSettings->m_sliverPng;
                      m_carpaintTexutreKTXFile = g_modelSettings->m_sliverKtx;
                      break;
                    }
                    case cc::daddy::NISSAN_BLACK:
                    {
                      m_carpaintTexutrePNGFile = g_modelSettings->m_blackPng;
                      m_carpaintTexutreKTXFile = g_modelSettings->m_blackKtx;
                      break;
                    }
                    case cc::daddy::NISSAN_PINK:
                    {
                      m_carpaintTexutrePNGFile = g_modelSettings->m_pinkPng;
                      m_carpaintTexutreKTXFile = g_modelSettings->m_pinkKtx;
                      break;
                    }
                    case cc::daddy::NISSAN_BLUE:
                    {
                      m_carpaintTexutrePNGFile = g_modelSettings->m_bluePng;
                      m_carpaintTexutreKTXFile = g_modelSettings->m_blueKtx;
                      break;
                    }
                    case cc::daddy::NISSAN_PURPLE:
                    {
                      m_carpaintTexutrePNGFile = g_modelSettings->m_purplePng;
                      m_carpaintTexutreKTXFile = g_modelSettings->m_purpleKtx;
                      break;
                    }
                    default:
                    {
                      m_needUpdataCarpaintTexture = false;
                      m_carpaintTexutrePNGFile = "";
                      m_carpaintTexutreKTXFile = "";
                      break;
                    }
                    }
                  }
                  else
                  {
                    if(m_needUpdataCarpaintTexture != false)
                    {
                      m_needUpdataCarpaintTexture = false;
                      m_carpaintTexutrePNGFile = "";
                      m_carpaintTexutreKTXFile = "";
                    }
                  }

                  if(m_needUpdataCarpaintTexture)
                  {
                    //osg::ref_ptr<osg::Image> l_newimg = nullptr;
                    osg::ref_ptr<osg::Image> l_newimg = nullptr;
                    l_newimg = osgDB::readImageFile(m_carpaintTexutreKTXFile);
                    if(l_newimg == nullptr)
                    {
                      l_newimg = osgDB::readImageFile(m_carpaintTexutrePNGFile);
                    }
                    else
                    {
                      XLOG_INFO(g_AppContext, "CarpaintTextureCallback succeed to load ktx carpaint image " << m_carpaintTexutreKTXFile);
                    }

                    if(l_newimg != nullptr)
                    {
                      XLOG_INFO(g_AppContext, "CarpaintTextureCallback succeed to load png carpaint image(ktx failed) " << m_carpaintTexutrePNGFile);
                      l_carpaintTextureimg = nullptr; //release the old image
                      l_carpaintTextureimg = l_newimg;
                      l_tex->setImage(l_carpaintTextureimg);
                    }
                    else
                    {
                      XLOG_ERROR(g_AppContext, "CarpaintTextureCallback Fail to carpaint ktx tex: " << m_carpaintTexutreKTXFile);
                      XLOG_ERROR(g_AppContext, "CarpaintTextureCallback Fail to carpaint png tex too: " << m_carpaintTexutrePNGFile);
                    }
                  }
                  else
                  {
                    //printf("need not update the texture this time !\n");
                  }
              }
          }
        }

      private:
        pc::core::Framework* m_pFramework;
        int m_currentColorIndex{-1};
        bool m_needUpdataCarpaintTexture{false};
        std::string m_carpaintTexutreKTXFile{};
        std::string m_carpaintTexutrePNGFile{};
        osg::ref_ptr<osg::Image> l_carpaintTextureimg{nullptr};
  };//end of internal class CarpaintTextureCallback


  // internal class of CarpaintTextureFinalizer
  class CarpaintTextureSettingVisitor : public osg::NodeVisitor
  {
    public:
      CarpaintTextureSettingVisitor(pc::core::Framework* f_framework):
              osg::NodeVisitor(osg::NodeVisitor::TraversalMode::TRAVERSE_ALL_CHILDREN), m_framework(f_framework){}

      void apply(osg::Node & node)
      {
          osg::Geode *const l_tmpGeode = node.asGeode();
          if(l_tmpGeode != nullptr)
          {
              //printf("Geode Name: %s, num_drawable=%u\n", l_tmpGeode->getName().c_str(), l_tmpGeode->getNumDrawables());
              for(unsigned int idx = 0; idx < l_tmpGeode->getNumDrawables(); idx++)
              {
                  osg::Drawable *const l_tmpDrawable = l_tmpGeode->getDrawable(idx);
                  osg::Geometry *const l_tmpGeometry = l_tmpDrawable->asGeometry();

                  if(l_tmpGeometry != nullptr)
                  {
                      osg::StateSet *const l_pStateSet =  l_tmpGeometry->getStateSet();
                      if(l_pStateSet != nullptr)
                      {
                          //go through the nodes to find the texture
                          osg::StateAttribute* const l_textureCarpaint = l_pStateSet->getTextureAttribute(0, osg::StateAttribute::TEXTURE);

                          if((l_textureCarpaint != nullptr) && (l_textureCarpaint->asTexture() != nullptr))
                          {
                            osg::Texture2D *const l_tex = (osg::Texture2D*)l_textureCarpaint->asTexture();
                            if(l_tex->getName() == m_carpaintTextureName)
                            {
                              l_tex->setUpdateCallback(new CarpaintTextureCallback(m_framework));
                              m_hasUpdatedCarpaintTexture = true;
                            }
                          }
                      }
                  }
              }
          }

          if(m_hasUpdatedCarpaintTexture == false)
          {
              traverse(node);
          }
      }

    private:
      pc::core::Framework* m_framework;

      bool m_hasUpdatedCarpaintTexture{false};

      //need be same name in the osg file
      const std::string m_carpaintTextureName{"texture_chesheng_carpaint"};

  }; //end of internal class CarpaintTextureSettingVisitor


  CarpaintTextureFinalizer(pc::core::Framework* f_framework)
    : m_framework(f_framework)
  {
  }

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
  {
    if (f_vehicleModel == nullptr)
    {
        return;
    }
    CarpaintTextureSettingVisitor l_CarpaintTextureSettingVisitor(m_framework);
    f_vehicleModel->accept(l_CarpaintTextureSettingVisitor);
  }

protected:

  virtual ~CarpaintTextureFinalizer() = default;

  CarpaintTextureFinalizer(const CarpaintTextureFinalizer&) = delete;
  CarpaintTextureFinalizer& operator = (const CarpaintTextureFinalizer&) = delete;

private:
  pc::core::Framework* m_framework;

}; //end of class CarpaintTextureFinalizer



class TransparencySettingVisitor : public osg::NodeVisitor
{
  public:
    TransparencySettingVisitor(float f_modelalpha, float f_modelalphaInterior):
          osg::NodeVisitor(osg::NodeVisitor::TraversalMode::TRAVERSE_ALL_CHILDREN),
          m_modelalpha(f_modelalpha),
          m_modelalphaInterior(f_modelalphaInterior){}

    void apply(osg::Node & node)
    {
        osg::Geode *const l_tmpGeode = node.asGeode();
        if(l_tmpGeode != nullptr)
        {
            //printf("Geode Name: %s, num_drawable=%u\n", l_tmpGeode->getName().c_str(), l_tmpGeode->getNumDrawables());
            for(unsigned int idx = 0; idx < l_tmpGeode->getNumDrawables(); idx++)
            {
                osg::Drawable *const l_tmpDrawable = l_tmpGeode->getDrawable(idx);
                osg::Geometry *const l_tmpGeometry = l_tmpDrawable->asGeometry();

                //glsl
                if(l_tmpGeometry != nullptr)
                {
                    osg::StateSet *const l_pStateSet =  l_tmpGeometry->getStateSet();
                    if(l_pStateSet != nullptr)
                    {
                        //go through the nodes to find the texture
//                        osg::StateAttribute* const l_textureCheSheng = l_pStateSet->getTextureAttribute(0, osg::StateAttribute::TEXTURE);

                        const osg::ref_ptr<osg::Uniform> l_uniform = l_pStateSet->getUniform("modelAlpha");
                        if(l_uniform != nullptr)
                        {
                          l_uniform->set(m_modelalpha);
                        }
                        else
                        {
                          //printf("stateset:%s can not find uniform modelAlpha \n", l_pStateSet->getName().c_str());
                        }

                        const osg::ref_ptr<osg::Uniform> l_uniformInterior = l_pStateSet->getUniform("modelAlphaInterior");
                        if(l_uniformInterior != nullptr)
                        {
                          l_uniformInterior->set(m_modelalphaInterior);
                        }
                        else
                        {
                          //printf("stateset:%s can not find uniform l_uniformInterior \n", l_pStateSet->getName().c_str());
                        }
                    }
                }
            }
        }

        traverse(node);
    }
  private:
    float m_modelalpha{0.0f};
    float m_modelalphaInterior{0.0f};

};

///!
//! VehicleTransparencyUpdateCallback
//!
void VehicleTransparencyUpdateCallback::update( osg::Node* f_node, osg::NodeVisitor* /* f_nv */)
{
    if (f_node == nullptr)
    {
        return;
    }
    cc::core::CustomFramework* const l_pCustomFramework = m_pFramework->asCustomFramework();
    osg::StateSet* const l_stateSet = f_node->getStateSet();
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF);  // PRQA S 3143
    const osg::ref_ptr<osg::Uniform> l_uniform = l_stateSet->getOrCreateUniform("modelAlpha",osg::Uniform::FLOAT);
    const osg::ref_ptr<osg::Uniform> l_uniformInterior = l_stateSet->getOrCreateUniform("modelAlphaInterior",osg::Uniform::FLOAT);

    if ((nullptr != l_uniform) && (nullptr != l_uniformInterior))
    {
      if ( l_pCustomFramework->m_VehTransparenceStsFromSM_Receiver.isConnected())
      {
        // still need this m_VehTransparenceStsFromSM_Receiver to be compatible with the RTT feature when DISABLE_ROTATABLE_TRANSPARENT_MODEL is true
        const cc::daddy::SVSVehTransStsDaddy_t* const l_pTransData = l_pCustomFramework->m_VehTransparenceStsFromSM_Receiver.getData();
        if(l_pTransData != nullptr)
        {
            if (1u == l_pTransData->m_Data)
            {
#if DISABLE_ROTATABLE_TRANSPARENT_MODEL
                l_uniform->set(0.0f);  // PRQA S 3803
                l_uniformInterior->set(0.0f);  // PRQA S 3803
#else
                const cc::daddy::SVSVehTransStsInternalDaddy_t* const lpTransInternalData = l_pCustomFramework->m_VehTransparenceStsInternalReceiver.getData();
                if ((true == lpTransInternalData->m_Data))
                {
                    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);  // PRQA S 3143
                    l_uniform->set(g_modelSettings->m_transparency);  // PRQA S 3803
                    l_uniformInterior->set(g_modelSettings->m_transparencyInterior);  // PRQA S 3803
                }
                else
                {
                    l_uniform->set(0.0f);  // PRQA S 3803
                    l_uniformInterior->set(0.0f);  // PRQA S 3803
                }
            #endif
            }
            else
            {
                l_uniform->set(0.0f);  // PRQA S 3803
                l_uniformInterior->set(0.0f);  // PRQA S 3803
            }

            //if(m_CurrentTransparentStatus != l_pTransData->m_Data)
            // must do the check each frame
            // since some nodes(like brake light) may be open/close after the transparency was set
            {
              m_CurrentTransparentStatus = l_pTransData->m_Data;
              if(1u == m_CurrentTransparentStatus)
              {
                TransparencySettingVisitor l_TransparencySettingVisitor(g_modelSettings->m_transparency, g_modelSettings->m_transparencyInterior);
                //TransparencySettingVisitor l_TransparencySettingVisitor(0.9f, 0.98f);
                f_node->accept(l_TransparencySettingVisitor);

              }
              else
              {
                TransparencySettingVisitor l_TransparencySettingVisitor(0.0f, 0.0f);
                f_node->accept(l_TransparencySettingVisitor);
              }
            }
        }
      }
      else
      {
        l_uniform->set(0.0f);  // PRQA S 3803
        l_uniformInterior->set(0.0f);  // PRQA S 3803
      }
    }
}

void VehicleTransparencyFinalizer::finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel)
{
  if (f_vehicleModel == nullptr)
  {
      return;
  }
  for (unsigned int componentID = pc::vehiclemodel::VehicleModel::FRONT_LEFT_WHEEL; componentID <= pc::vehiclemodel::VehicleModel::REAR_RIGHT_WHEEL; componentID++)
  {
    const unsigned int numOfChildren = f_vehicleModel->getComponent(static_cast<pc::vehiclemodel::VehicleModel::Component>(componentID))->asTransform()->asMatrixTransform()->getNumChildren();
    for (unsigned int childID = 0u; childID < numOfChildren; childID++)
    {
      osg::StateSet* const l_stateSet = f_vehicleModel->getComponent(static_cast<pc::vehiclemodel::VehicleModel::Component>(componentID))->asTransform()->asMatrixTransform()->getChild(childID)->getOrCreateStateSet();
      if (nullptr != l_stateSet)
      {
        l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carOpaque,"RenderBin");
      }
      else
      {
        // pf code. #code looks fine
        XLOG_ERROR(g_AppContext, "VehicleTransparencyFinalizer::finalize()::l_stateSet is nullptr\""); 
      }
    }
  }
  for (unsigned int i = 0; i < f_vehicleModel->getNumChildren(); i++)
  {
    osg::Node* const l_node =f_vehicleModel->getChild(i);
    l_node->addUpdateCallback(new VehicleTransparencyUpdateCallback(m_framework));
  }
}

//!
//! Vehicle
//!
Vehicle::Vehicle(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Group* f_frontWheelsGroup, osg::Group* f_allWheelsGroup, osg::Group* f_vehicleDoorsGroup)
  : Asset(f_assetId)
{
    // pc::vehiclemodel::VehicleModel* l_vehicleModel =  new pc::vehiclemodel::VehicleModel(f_framework);
    CustomVehicleModel *const l_vehicleModel = new CustomVehicleModel(f_framework);
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::RenderBinFinalizer(core::RENDERBIN_ORDER_CAR_OPAQUE, core::RENDERBIN_ORDER_CAR_GLASS));
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::WheelAnimationFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::BrakeAnimationFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::DoorAnimationFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::InstallPostProcessingCallbackFinalizer());
    // l_vehicleModel->addFinalizer(new DoorsWarningFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new FrontWheelsGetter(f_frontWheelsGroup));
    l_vehicleModel->addFinalizer(new AllWheelsGetter(f_allWheelsGroup));
    l_vehicleModel->addFinalizer(new VehicleDoorsGetter(f_vehicleDoorsGroup));
    l_vehicleModel->addFinalizer(new LightFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new VehicleNodeMaskFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new CarPaintFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new CarpaintTextureFinalizer(f_framework));
    l_vehicleModel->addFinalizer(new VehicleTransparencyFinalizer(f_framework));
    // l_vehicleModel->addFinalizer(new VehicleBodyFinalizer());
    l_vehicleModel->addFinalizer(new pc::vehiclemodel::CreateSteadyStateProxyFinalizer());

    // l_vehicleModel->setMatrix( osg::Matrixf::translate(0.0f, 0.0f, pc::vehicle::g_mechanicalData->m_wheelRadius) );//magic number. Official model positions car on ground level
    m_asset = l_vehicleModel;
    osg::Group::addChild(m_asset);  // PRQA S 3803
}


//!
//! VehicleReadCommand
//!
class VehicleReadCommand : public pc::util::cli::CommandCallback
{
public:

  bool invoke(std::istream&, std::ostream& f_output, const pc::util::cli::CommandLineInterface& f_cli) override
  {
    pc::core::Framework* l_framework = nullptr;
    const bool l_res = f_cli.getUserData().getValue<pc::core::Framework*>(l_framework);
    assert(l_res && (nullptr != l_framework));
    cc::core::CustomScene* const l_scene = static_cast<cc::core::CustomScene*> (l_framework->getScene());

    // Get vehicle model
    if (nullptr != l_scene)
    {
        f_output << l_scene->getVehicleModelName() << newline;  // PRQA S 3803
    }
    return true;
  }

  void getDescription(std::ostream& f_output) const override
  {
    f_output << "Get a name of vehicle model root node" << newline;  // PRQA S 3803
  }

  void getHelp(std::ostream& f_output) const override
  {
    f_output <<  newline;  // PRQA S 3803
  }

};

pc::util::cli::Command<VehicleReadCommand> g_vehicleReadCommand("vehicleversion");


} // namespace common
} // namespace assets
} // namespace cc

