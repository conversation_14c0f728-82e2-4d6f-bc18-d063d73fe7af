//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_TRAILERHITCHTRAJECTORY
#define CC_ASSETS_TRAJECTORY_SUBASSETS_TRAILERHITCHTRAJECTORY

#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
  class MainLogic;
  struct ModelData_st;
  struct Inputs_st;
} // namespace mainlogic


//!
//! @brief TrailerHitchTrajectory
//!
//!
class TrailerHitchTrajectory : public cc::assets::trajectory::GeneralTrajectoryLine
{
public:

  TrailerHitchTrajectory(
    const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
    unsigned int f_numOfVerts,
    pc::core::Framework* f_framework);

  virtual void generateVertexData();
  virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
  virtual ~TrailerHitchTrajectory();
  const unsigned int mc_numOfVerts;
  pc::core::Framework* m_framework;
  unsigned int m_lastCalibUpdate;

};


//!
//! @brief TrailerHitchCircle
//!
//!
class TrailerHitchCircle : public osg::Group
{
public:

  TrailerHitchCircle(pc::core::Framework* f_framework);

protected:

  virtual ~TrailerHitchCircle();

  osg::Image* create1DTexture();
  void createCircleGeometry();
  void loadTexture(osg::Geometry* f_geometry);
  void applyTexShaderToGeometry(osg::Geometry* f_geometry, unsigned int f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend);
  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  osg::ref_ptr<osg::Geode> m_circleGeode;
  pc::core::Framework* m_framework;
  unsigned int m_lastCalibUpdate;
};


} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_TRAILERHITCHTRAJECTORY
