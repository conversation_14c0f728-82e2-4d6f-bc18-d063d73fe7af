//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Jose Esparza (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomFloorPlateGenerator.cpp
/// @brief
//=============================================================================

#include "cc/assets/customfloorplategenerator/inc/CustomFloorPlateGenerator.h"
#include "vfc/core/vfc_types.hpp"
#include "osg/Geometry"
#include "osg/Geode"

using pc::util::logging::g_EngineContext;

namespace cc
{
namespace assets
{
namespace floorplate
{

pc::util::coding::Item<CustomFloorPlateData> g_customFloorPlateData("CustomFloorPlate");

CustomFloorPlateGenerator::CustomFloorPlateGenerator(pc::core::Framework* f_pFramework)
 : cc::assets::floorplate::FloorPlateBaseType{f_pFramework}
{
  // Access snapshot camera from base class pc::texfloor::core::FloorPlateGenerator, to change some settings
  osg::Camera* const l_cam = pc::texfloor::core::MovingFloorPlateGenerator::getSnapshotCam(); // PRQA S 0150

  if(nullptr != l_cam)
  {
    // Do not change view matrix in the customer implementation unless you really understand what you are doing
    // the base pc/texfloor classes will take care of this for you, so depth tests are legal between floor and camera itself

    // Add a transparent geometry to the snapshot camera, which defines an area to be discarded for the floor plate
    const vfc::float32_t l_length = g_customFloorPlateData->m_frontLeftCorner.x() - g_customFloorPlateData->m_rearRightCorner.x();
    const vfc::float32_t l_width = g_customFloorPlateData->m_frontLeftCorner.y() - g_customFloorPlateData->m_rearRightCorner.y();
    osg::Geometry* const l_geom =  osg::createTexturedQuadGeometry(
      osg::Vec3f(g_customFloorPlateData->m_rearRightCorner.x(), g_customFloorPlateData->m_rearRightCorner.y(), 0.5f),    //  bottom corner
      osg::Vec3f(l_length,                                      0.0f,                                           0.0f),    //  length
      osg::Vec3f(0.0f,                                           l_width,                                       0.0f));   //  width

    // set a transparent color for the geometry
    osg::Vec4Array* const color = new osg::Vec4Array(1u);
    (*color)[0u] = osg::Vec4f(0,0,0,0);
    color->dirty();
    l_geom->setColorArray(color, osg::Array::BIND_OVERALL);

    osg::Geode* const l_geode = new osg::Geode();
    osg::StateSet* const l_stateSet = l_geode->getOrCreateStateSet();
    pc::core::BasicShaderProgramDescriptor l_basicTexShaderLine("basicColor");
    l_basicTexShaderLine.apply(l_stateSet);  // PRQA S 3803
    l_stateSet->setRenderBinDetails(-10, "RenderBin");

    l_geode->addDrawable(l_geom);  // PRQA S 3803
    l_cam->addChild(l_geode);  // PRQA S 3803
  }
}

CustomFloorPlateGenerator::~CustomFloorPlateGenerator() = default;

} // namespace customfloorplategenerator
} // namespace assets
} // namespace cc
