//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ECALprogressoverlay.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_ECALPROGRESSOVERLAY_H
#define CC_ASSETS_ECALPROGRESSOVERLAY_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace ECALprogressoverlay
{

bool isCalibrationError(vfc::int16_t f_camPos, vfc::uint32_t errCode);

// !
// ! ECALprogressoverlayManager
// !
class ECALprogressoverlayManager
{
public:

  ECALprogressoverlayManager();

  virtual ~ECALprogressoverlayManager();

  void init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays);

  void update(const pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);

  virtual pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize) const;

private:

  //! Copy constructor is not permitted.
  ECALprogressoverlayManager (const ECALprogressoverlayManager& other); // = delete
  //! Copy assignment operator is not permitted.
  ECALprogressoverlayManager& operator=(const ECALprogressoverlayManager& other); // = delete

  unsigned int m_lastConfigUpdate;
  

  pc::assets::IconGroup m_ECALprogressoverlayIcons;

  std::string m_image_Calibrating_path;
  std::string m_image_CalibratedFailed_path;
  std::string m_image_CalibratedSuccessful_path;

  std::string m_image_mask_red_horizontal_path;
  std::string m_image_mask_green_horizontal_path;

};


//!
//! ECALprogressoverlay
//!
class ECALprogressoverlay: public cc::assets::uielements::CustomImageOverlays    // PRQA S 2504
{
public:

  ECALprogressoverlay(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);

  virtual ~ECALprogressoverlay();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:

  //! Copy constructor is not permitted.
  ECALprogressoverlay (const ECALprogressoverlay& other); // = delete
  //! Copy assignment operator is not permitted.
  ECALprogressoverlay& operator=(const ECALprogressoverlay& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  ECALprogressoverlayManager m_manager;

};



} // namespace ECALprogressoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_ECALPROGRESSOVERLAY_H
