#include "cc/assets/button/inc/ViewModeButton.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/imgui/inc/imgui_manager.h"

namespace cc
{
namespace assets
{
namespace button
{
namespace viewmodebutton
{

static pc::util::coding::Item<ViewModeButtonSettings> g_singleViewModeButtonSettings("SingleViewModeButton");
static pc::util::coding::Item<ViewModeButtonSettings> g_perspectiveViewModeButtonSettings("PerspectiveViewModeButton");
static pc::util::coding::Item<ViewModeButtonSettings> g_wheelViewModeButtonSettings("WheelViewModeButton");
static pc::util::coding::Item<ViewModeButtonSettings> g_wideViewModeButtonSettings("WideViewModeButton");
static pc::util::coding::Item<ViewModeButtonSettings> g_skeletonViewModeButtonSettings("SkeletonViewModeButton");
static pc::util::coding::Item<ViewModeButtonSettings> g_viewModeButtonBarSettings("ViewModeButtonBar");

ViewModeButton::ViewModeButton( // PRQA S 4207
    cc::core::AssetId       f_assetId,
    pc::core::Framework*    f_framework,
    ViewModeButtonSettings* f_settings,
    ESVSViewMode            f_viewMode,
    osg::Camera*            f_referenceView)
    : Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_viewMode{f_viewMode}
    , m_settings{f_settings}
    , m_preGear{pc::daddy::EGear::GEAR_INIT}
    , m_hasSentViewMode{false}
{
    setState(AVAILABLE);
    Object::setName(getViewModeButtonName());
    setPositionHori(m_settings->m_horiPos);
}

void ViewModeButton::onInvalid()
{
    setIconEnable(false);
}

void ViewModeButton::onUnavailable()
{
    setIconEnable(true);
    setTexturePath(m_settings->m_buttonTexture.m_UnavailableTexturePath);
}

void ViewModeButton::onAvailable()
{
    setIconEnable(true);
    setTexturePath(m_settings->m_buttonTexture.m_AvailableTexturePath);
    XLOG_INFO(g_AppContext, "onAvailable : " << getViewModeButtonName());

}

void ViewModeButton::onPressed()
{
    if (checkTouchInsideResponseArea() && (m_viewMode != ESVSViewMode::ESVSViewMode_VM_Default))
    {
        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort, getViewMode());
        XLOG_INFO(g_AppContext, "Setting HU SVSViewMode to " << getViewModeButtonName());
    }
}

void ViewModeButton::onSelected()
{
    setIconEnable(true);
    setTexturePath(m_settings->m_buttonTexture.m_SelectedTexturePath);
    XLOG_INFO(g_AppContext, "onSelected : " << getViewModeButtonName());

}

void ViewModeButton::onReleased()
{
    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort, ESVSViewMode::ESVSViewMode_VM_Default);
}

void ViewModeButton::update()
{
    setSettingModifiedCount(m_settings->getModifiedCount());
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)
    
    
    
    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)
    
    GET_PORT_DATA( viewModeContainer, m_framework->asCustomFramework()->m_viewModeStatus_ReceiverPort, viewModePortHaveData)
    
    GET_PORT_DATA(displayedViewContainer, m_framework->asCustomFramework()->m_displayedView_ReceiverPort, displayedViewPortHaveData);
    
    
    
    bool      touchStatusChanged       = false;
    EScreenID l_displayedView          = EScreenID_NO_CHANGE;
    bool      l_viewmodeFitCurrentView = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }
    if (displayedViewPortHaveData)
    {
        l_displayedView = displayedViewContainer->m_Data;
    }
    if (m_settings->getModifiedCount() != getSettingModifiedCount())
    {
        setSettingModifiedCount(m_settings->getModifiedCount());
    }
    switch (m_viewMode)
    {
    case ESVSViewMode::ESVSViewMode_VM_Standard:
    {
        l_viewmodeFitCurrentView = (l_displayedView == EScreenID::EScreenID_SINGLE_FRONT_NORMAL) ||
                                   (l_displayedView == EScreenID::EScreenID_SINGLE_REAR_NORMAL_ON_ROAD);
        break;
    }
    case ESVSViewMode::ESVSViewMode_VM_Wheel:
    {
        l_viewmodeFitCurrentView = (l_displayedView == EScreenID::EScreenID_WHEEL_REAR_DUAL) ||
                                   (l_displayedView == EScreenID::EScreenID_WHEEL_FRONT_DUAL);
        break;
    }
    case ESVSViewMode::ESVSViewMode_VM_Wide:
    {
        l_viewmodeFitCurrentView = (l_displayedView == EScreenID::EScreenID_SINGLE_FRONT_JUNCTION) ||
                                   (l_displayedView == EScreenID::EScreenID_SINGLE_REAR_JUNCTION);
        break;
    }
    case ESVSViewMode::ESVSViewMode_VM_Perspective:
    {
        l_viewmodeFitCurrentView = (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_FL) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_FR) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_RL) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_RR) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_PFR) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_PRI) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_PLE) ||
                                   (l_displayedView == EScreenID::EScreenID_PERSPECTIVE_PRE);
        break;
    }
    case ESVSViewMode::ESVSViewMode_VM_STB:
    {
        l_viewmodeFitCurrentView = (l_displayedView == EScreenID::EScreenID_SINGLE_STB);
        break;
    }
    default:
    {
        break;
    }
    }
//    const auto currentViewMode         = viewModeContainer->m_Data;
//    const auto touchInsideViewport     = checkTouchInsideViewport(); // PRQA S 3803
    const bool touchInsideResponseArea = checkTouchInsideResponseArea();

    EGear l_curGear = EGear_Init;
    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = static_cast<vfc::uint8_t>(l_pData->m_Data);
        }
    }
    const bool gearChanged = (m_preGear != l_curGear);
    m_preGear        = l_curGear;

    ButtonState currentState = getState();
    if (l_viewmodeFitCurrentView)
    {
        currentState = SELECTED;
    }
    switch (currentState)
    {
    case AVAILABLE:
    {
        if (l_curGear == EGear_R && getViewMode() == ESVSViewMode_VM_STB)
        {
            currentState = UNAVAILABLE;
        }
        else
        {
            if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
            {
                currentState = PRESSED;
            }
        }
        break;
    }

    case PRESSED:
    {
        if (touchStatusChanged && touchStatus() == TOUCH_UP)
        {
            currentState = RELEASED;
        }
        break;
    }
    case RELEASED:
    {
        currentState = AVAILABLE;
        break;
    }
    case SELECTED:
    {
        if (!l_viewmodeFitCurrentView)
        {
            currentState = AVAILABLE;
        }
        break;
    }
    case INVALID:
    case UNAVAILABLE:
    default:
    {
        if ((gearChanged) || (touchStatusChanged && touchStatus() == TOUCH_UP))
        {
            if (l_curGear == EGear_R && getViewMode() == ESVSViewMode_VM_STB)
            {
                currentState = UNAVAILABLE;
            }
            // else if ( l_curGear != EGear_R && getViewMode() == ESVSViewMode_VM_STB )
            // {
            //             currentState = AVAILABLE;
            // }
            else
            {
                currentState = AVAILABLE;
            }
        }
        break;
    }
    }

    setState(currentState);
    switch (getState())
    {
    case INVALID:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    case UNAVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
        break;
    }
    case AVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
        break;
    }
    case PRESSED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
        break;
    }
    case RELEASED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "RELEASED");
        break;
    }
    case SELECTED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "SELECTED");
        break;
    }
    default:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    }
}

ViewModelButtonBackground::ViewModelButtonBackground(
    cc::core::AssetId       f_assetId,
    pc::core::Framework*    f_framework,
    ViewModeButtonSettings* f_settings,
    osg::Camera*            f_referenceView)
    : cc::assets::button::Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_settings{f_settings}
{
    setState(AVAILABLE);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(m_settings->m_horiPos);
    setTexturePath(m_settings->m_buttonTexture.m_AvailableTexturePath);
}

ViewModeButtonGroup::ViewModeButtonGroup(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : ButtonGroup{f_assetId}
{
    setName("ViewModeButtonGroup");
    addButton(new ViewModelButtonBackground(
        f_assetId,
        f_framework,
        const_cast<ViewModeButtonSettings*>(g_viewModeButtonBarSettings.get()), // PRQA S 3066
        f_referenceView)); // just background
    addButton(new ViewModeButton(
        cc::core::AssetId::EASSETS_SINGLE_VIEW_MODE_BUTTON,
        f_framework,
        const_cast<ViewModeButtonSettings*>(g_singleViewModeButtonSettings.get()), // PRQA S 3066
        ESVSViewMode::ESVSViewMode_VM_Standard,
        f_referenceView));
    addButton(new ViewModeButton(
        cc::core::AssetId::EASSETS_PERSPECTIVE_VIEW_MODE_BUTTON,
        f_framework,
        const_cast<ViewModeButtonSettings*>(g_perspectiveViewModeButtonSettings.get()), // PRQA S 3066
        ESVSViewMode::ESVSViewMode_VM_Perspective,
        f_referenceView));
    addButton(new ViewModeButton(
        cc::core::AssetId::EASSETS_WHEEL_VIEW_MODE_BUTTON,
        f_framework,
        const_cast<ViewModeButtonSettings*>(g_wheelViewModeButtonSettings.get()), // PRQA S 3066
        ESVSViewMode::ESVSViewMode_VM_Wheel,
        f_referenceView));
    addButton(new ViewModeButton(
        cc::core::AssetId::EASSETS_WIDE_VIEW_MODE_BUTTON,
        f_framework,
        const_cast<ViewModeButtonSettings*>(g_wideViewModeButtonSettings.get()), // PRQA S 3066
        ESVSViewMode::ESVSViewMode_VM_Wide,
        f_referenceView));
    addButton(new ViewModeButton(
        cc::core::AssetId::EASSETS_SKELETON_VIEW_MODE_BUTTON,
        f_framework,
        const_cast<ViewModeButtonSettings*>(g_skeletonViewModeButtonSettings.get()), // PRQA S 3066
        ESVSViewMode::ESVSViewMode_VM_STB,
        f_referenceView));

    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    else
    {
        XLOG_ERROR(g_AppContext, "ViewModeButtonBar setReferenceView Failed");
    }
}

void ViewModeButtonGroup::update()
{
    m_enabled = true;
}

} // namespace viewmodebutton
} // namespace button
} // namespace assets
} // namespace cc
