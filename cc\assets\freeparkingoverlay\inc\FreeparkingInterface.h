//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#ifndef CC_ASSETS_FREEPARKINGOVERLAY_INTERFACE_H
#define CC_ASSETS_FREEPARKINGOVERLAY_INTERFACE_H

#include "vfc/core/vfc_siunits_convenienttypes.hpp"
#include "vfc/core/vfc_types.hpp"
#include "cc/daddy/inc/CustomDaddyTypes.h"

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

struct APSlotOrientation
{
    osg::Vec2f     m_CenterPos;
    vfc::float32_t m_yawAngleRaw;
};

// freeparking
struct FreeParkingSlot // output
{
    vfc::float32_t m_x{0.0f};   // cm, position of freeparking slot picture center, origin is ego car rear axel middle
                                // point (front:+x, left:+y)
    vfc::float32_t m_y{0.0f};   // cm
    vfc::float32_t m_yaw{0.0f}; // degree
};

struct SlotCorner
{
    osg::Vec2f m_point1{}; // cm
    osg::Vec2f m_point2{}; // cm
    osg::Vec2f m_point3{}; // cm
    osg::Vec2f m_point4{}; // cm
};

enum class EFreeParkingStage : vfc::uint8_t
{
    None = 0u,      // not show any slot
    InFpStandstill, // show freeparking slot
    GuidanceStart,  // show target slot during guidance
    GuidanceFinish, // not show any slot
    InFpMoving      // not show any slot
};

enum class EFreeParkingSlotType : vfc::uint8_t
{
    HorizontalSlot = 0u,
    VerticalSlot,
    DiagonalSlot
};

enum class EFreeParkingSlotState : vfc::uint8_t
{
    UNAVAILABLE = 0u,
    AVAILABLE,
    UNAVAILABLE_BARRIER
};

enum class ButtonState : vfc::uint8_t
{
    SELECTABLE = 0u,
    SELECTED   = 1u
};

enum class ButtonDirection : vfc::uint8_t
{
    TOP    = 0u,
    BOTTOM = 1u
};

struct CFreeParkingCornerPos
{
    vfc::CSI::si_metre_f32_t m_fpPosX; // the X coordinate (unit:meter) of the corner position (DIN70K)
    vfc::CSI::si_metre_f32_t m_fpPosY; // the Y coordinate (unit:meter) of the corner position (DIN70K)
};

enum EFreeParkingSpaceType : vfc::uint8_t
{
    IPS_PS_TYPE_UNKNOWN  = 0,
    IPS_PS_TYPE_CROSS    = 1,
    IPS_PS_TYPE_PARALLEL = 2,
    IPS_PS_TYPE_DIAGONAL = 3
};

enum EFreeParkingEntryType : vfc::uint8_t
{
    IPS_ENTRY_TYPE_UNKNOWN  = 0,
    IPS_ENTRY_TYPE_FORWARD  = 1,
    IPS_ENTRY_TYPE_BACKWARD = 2
};

struct CFreeParkingRectStripped
{
    CFreeParkingCornerPos m_fpCornerFrontLeft;
    CFreeParkingCornerPos m_fpCornerFrontRight;
    CFreeParkingCornerPos m_fpCornerRearLeft;
    CFreeParkingCornerPos m_fpCornerRearRight;

    EFreeParkingSpaceType m_fpPSType;
    EFreeParkingEntryType m_fpPSEntryType;
};

enum class SpotType : vfc::uint32_t
{
    PARKINGTYPE_PARALLEL      = 0,
    PARKINGTYPE_PERPENDICULAR = 1,
    PARKINGTYPE_DIAGONAL      = 2,
    PARKINGTYPE_EXPMODE       = 3,
    PARKINGTYPE_INVALID       = 4
};

// side of the parking slot, used in the parking out use case
enum class SpotSide : vfc::uint32_t
{
    PARKINGSIDE_LEFT_SIDE_PSX  = 0,
    PARKINGSIDE_RIGHT_SIDE_PSX = 1,
    PARKINGSIDE_LEFT_BOTH_PSX  = 2,
    PARKINGSIDE_INVALID_PSX    = 3
};

enum APSlitherActionType : vfc::uint32_t
{
    UNKOWN              = 0,
    ROTATION            = 1,
    TRANSLATION         = 2,
    TELEPORTATION       = 3,
};

enum ParkingPlaneState : vfc::uint32_t
{
    DEFAULT     = 0u,
    AVAILABLE   = 1u,
    UNAVAILABLE = 2u,
};

struct APSlitherStartEndPos
{
    osg::Vec2f StartPos;
    osg::Vec2f EndPos;
};

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_FREEPARKINGOVERLAY_INTERFACE_H
