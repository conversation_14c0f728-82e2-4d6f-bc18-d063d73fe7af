//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  CustomRenderManager.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_COMMON_CUSTOMRENDERMANAGER_H
#define CC_ASSETS_COMMON_CUSTOMRENDERMANAGER_H

#include "pc/svs/factory/inc/RenderManager.h"

namespace cc
{
namespace assets
{
namespace common
{

//======================================================
// CustomRenderManager
//------------------------------------------------------
/// Manage the surround view rendering.
/// It handles the surround image behind the 3D vehicle.
/// <AUTHOR>
//======================================================
class CustomRenderManager : public pc::factory::RenderManager
{
public:

  CustomRenderManager(
    pc::factory::RenderManagerRegistry* f_registry,
    cc::virtcam::VirtualCamEnum f_virtCam,
    rbp::vis::imp::chamaeleon::EChamaeleonView f_settingChamaeleon = rbp::vis::imp::chamaeleon::EChamaeleonView::NO_CHAMAELEON,
    rbp::vis::imp::sh::ESharpnessView f_settingSharpnessHarmonization = rbp::vis::imp::sh::ESharpnessView::NO_SHARPNESS_HARMONIZATION,
    rbp::vis::imp::tnf::ETnfView f_settingTemporalNoiseFilter = rbp::vis::imp::tnf::ETnfView::NO_TNF);

protected:

  virtual ~CustomRenderManager() = default;
  //! Copy constructor is not permitted.
  CustomRenderManager (const CustomRenderManager&) = delete;
  //! Copy assignment operator is not permitted.
  CustomRenderManager& operator=(const CustomRenderManager& other) = delete;


};

} // namespace common
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_COMMON_CUSTOMRENDERMANAGER_H
