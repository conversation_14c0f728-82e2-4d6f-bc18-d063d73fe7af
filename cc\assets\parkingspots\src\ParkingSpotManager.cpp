//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ParkingSpotManager.cpp
/// @brief
//=============================================================================

#include "cc/assets/parkingspots/inc/ParkingSpotManager.h"
#include "vfc/core/vfc_types.hpp"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060

#include "pc/svs/animation/inc/AnimationManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/assets/parkingspots/inc/ParkingSpotUtil.h"
#include "cc/assets/parkingspots/inc/ParkingSpot.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace parkingspots
{

pc::util::coding::Item<ParkingSpotManagerSettings> g_managerSettings("ParkingSpotManager");

//!
//! ParkingSpotManager
//!
ParkingSpotManager::ParkingSpotManager(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_hasNewParkingSlots{cc::daddy::ParkingSpot::PARKINGSLOT_CHANGES}
  , m_impAlphaStartVal{0.f}
  , m_lastSelectedParkingSpot{nullptr}
  , m_parkDirChanged{false}
  , m_lastUpdate{0u}
  , m_parkingSpotManagerState{cc::target::common::EParkngTypeSeld::PARKING_NONE}
  , m_slotSelectOverlayState{TRAJECTORY_IDLE}
  , m_animState{TRAJ_1_ANIM_IN_PROGRESS}
  , m_time{0.0f}
  , mc_time_inc{0.2f}
  , m_MVPmatrixValid{false}
  , m_ViewButtonParkInSlotsSelectingDispSts{cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE}
{
  setName("ParkingSpotManager");
  setNumChildrenRequiringUpdateTraversal(1u);
  setCullingActive(false);

  //! setup parking spots
  m_parkingSpotAssets = new osg::Switch;
  m_parkingSpotAssets->setName("parkingSpotAssets");

  for (vfc::uint32_t i=0u; i < static_cast<vfc::uint32_t>(cc::target::common::l_L_ParkSpace_side) * static_cast<vfc::uint32_t>(cc::target::common::l_L_ParkSpace_NumberPerside); ++i)
  {
    m_parkingSpotAssets->addChild(new ParkingSpot, false);    // PRQA S 3803
  }
  osg::Group::addChild(m_parkingSpotAssets.get());    // PRQA S 3803

  //! For HMI display
  m_spotSize.x() = g_managerSettings->m_parkingSpotHmiLength;
  m_spotSize.y() = g_managerSettings->m_parkingSpotHmiWidth;

  invalidateParkingSlots();

}


ParkingSpotManager::~ParkingSpotManager() = default;

void ParkingSpotManager::initManager()
{

  m_lastSelectedParkingSpot = nullptr;
  m_impAlphaStartVal = 0.f;
  m_slotSelectOverlayState  = TRAJECTORY_IDLE;
  m_animState               = TRAJ_1_ANIM_IN_PROGRESS;
  m_time                    = 0.0f;
}

void ParkingSpotManager::invalidateParkingSlots()
{
  //invalidate all the parking lots previously saved
  for (vfc::uint32_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; ++l_side)
  {
    for (vfc::uint32_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; ++l_numberPerside)
    {
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PrkgSlot               = cc::target::common::EPARKSlotStsR2L::PARKSLOT_NONE;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PrkgSlotSta_f32        = 0.f;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PSType                 = cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_ParkManeuverType       = cc::target::common::rbp_Type_ParkManeuverType_en::rbp_ParkBwdIn_enm;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PSCorner2X_i16         = 32767;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PSCorner2Y_i16         = 32767;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PSCorner1X_i16         = 32767;
      m_parkingSpotData[l_side][l_numberPerside].m_APA_PSCorner1Y_i16         = 32767;
    }
  }
}

vfc::float32_t ParkingSpotManager::getParkOutCrossFrontSpotXPos()
{
  return (g_managerSettings->m_parkOutCrossFrontSpotXPos);
}

vfc::float32_t ParkingSpotManager::getParkOutCrossRearSpotXPos()
{
  return (g_managerSettings->m_parkOutCrossRearSpotXPos);
}

vfc::float32_t ParkingSpotManager::getParkOutParaSpotXPos()
{
  return (g_managerSettings->m_parkOutParaSpotXPos);
}

vfc::float32_t ParkingSpotManager::getParkOutParaSpotYPos()
{
  return (g_managerSettings->m_parkOutParaSpotYPos);
}

osg::Vec2f ParkingSpotManager::getSpotSize() // PRQA S 4211
{
  return (m_spotSize);
}


ParkingSpot* ParkingSpotManager::getParkingSpot(vfc::uint8_t f_index) // PRQA S 4211
{
  return static_cast<ParkingSpot*> (m_parkingSpotAssets->getChild(f_index)); // PRQA S 3076
}


bool ParkingSpotManager::isParkingSpotActive(vfc::uint32_t f_index) const
{
  return m_parkingSpotAssets->getValue(f_index);
}

bool ParkingSpotManager::isParkingSpotShown(const cc::core::CustomFramework* f_framework)
{
  bool l_ret = false;
  cc::target::common::EAPAPARKMODE                 l_curAPAPARKMODE          = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::EPARKStatusR2L               l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld              l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L            l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndSearchR2L       l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  bool                         l_curFreeParkingActive    = false;

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curAPAPARKMODE = l_parkAPAPARKMODE->m_Data;
  }

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (f_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = f_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
    l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curAPAPARKMODE && cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
      {if (false == l_curFreeParkingActive)
        {
          l_ret = true;
          m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
        }
        break;}
      default:
        {break;}
    }
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curAPAPARKMODE && cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror: // req_496
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk: // req_386
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood: // req_387
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot: // req_371
        {break;}
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:
        {l_ret = true;
        m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
        break;}
      default:
        {break;}
    }
    if (cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear == l_curAPADriverReq_Search)
    {
        l_ret = true;
        m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_AVAILABLE;
    }
  }

  return l_ret;
}

void ParkingSpotManager::setParkingSpotVisiblity(ParkingSpot* f_parkingSpot, const cc::core::CustomFramework* f_customFramework)
{
  //mark it as visible or not
  if (isParkingSpotShown(f_customFramework))
  {
    f_parkingSpot->setVisibility(true);
  }
  else
  {
    f_parkingSpot->setVisibility(false);
  }
  /*
  bool l_spotShowState = isParkingSpotShown(f_customFramework);
  f_parkingSpot->setVisibility(l_spotShowState);
  */
}

void ParkingSpotManager::setIconCenterAndResponseArea(vfc::uint8_t f_index, cc::daddy::ParkUISpotData_t& f_rParkSpotUIDataContainer, UISpotData f_spotUIData)
{
  switch (static_cast<vfc::int32_t>(f_index))
  {
  case 0:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 1:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 2:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 3:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 4:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos5L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos5L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos5L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 5:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos6L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos6L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos6L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 6:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos7L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos7L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos7L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 7:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos8L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos8L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos8L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 8:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos9L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos9L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos9L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 9:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos10L.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos10L.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos10L.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 10:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos1R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 11:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos2R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 12:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos3R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 13:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos4R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 14:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos5R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos5R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos5R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 15:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos6R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos6R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos6R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 16:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos7R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos7R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos7R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 17:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos8R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos8R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos8R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 18:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos9R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos9R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos9R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  case 19:
  {
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos10R.m_spotType = f_spotUIData.m_spotType;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos10R.m_iconCenter = f_spotUIData.m_spotCenter;
    f_rParkSpotUIDataContainer.m_Data.m_SlotPos10R.m_responseArea = f_spotUIData.m_spotresponseArea;
  }
    break;
  default:
    {break;}
  }
}

void ParkingSpotManager::CleanButtonDispSts()
{
  m_ViewButtonParkInSlotsSelectingDispSts = cc::daddy::PARK_DISP2TOUCH_NOT_AVAILABLE;
}

bool ParkingSpotManager::DeliverButtonDispSts(const core::CustomFramework* f_framework) // PRQA S 4211
{
  cc::daddy::ParkDisp2TouchStsDaddy_t& l_ParkDisp2TouchSts =
              cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.reserve() ;

  l_ParkDisp2TouchSts.m_Data.m_ButtonParkSlotsSelectingDispSts = m_ViewButtonParkInSlotsSelectingDispSts;

  cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.deliver() ;

  return false;
}

void ParkingSpotManager::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (m_lastUpdate != f_nv.getFrameStamp()->getFrameNumber())
    {
      m_lastUpdate = f_nv.getFrameStamp()->getFrameNumber();
      update();
    }
  }
  else if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (&f_nv); // PRQA S 3076
    m_MVPmatrix = (*(l_cv->getModelViewMatrix())) * (*(l_cv->getProjectionMatrix()));
    m_Viewmatrix = (*(l_cv->getModelViewMatrix()));
    m_MVPmatrixValid = true;
  }
  else
  {
    //Do nothing
  }

  osg::Group::traverse(f_nv);
}

//! this function is called by the traverse method and handles the whole logic
//! for the parkin and parkout functionality
void ParkingSpotManager::update()    // PRQA S 6040  // PRQA S 6041 // PRQA S 6043 // PRQA S 2755
{

  this->CleanButtonDispSts();
  cc::core::CustomFramework* const l_customFramework = m_framework->asCustomFramework();

  switch (m_parkingSpotManagerState)
  {
    case cc::target::common::EParkngTypeSeld::PARKING_NONE:
    {
      invalidateParkingSlots();
      initManager();
    }
    break;
    case cc::target::common::EParkngTypeSeld::PARKING_OUT:
    case cc::target::common::EParkngTypeSeld::PARKING_IN:
    default:
    {break;}
  }

  if(l_customFramework->m_parkAPASlotsReceiver.hasData())
  {
    const cc::daddy::ParkAPA_ParkSpace_t *const l_parkSpace = l_customFramework->m_parkAPASlotsReceiver.getData();
    for(vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
    {
      for(vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
      {
        m_parkingSpotData[l_side][l_numberPerside] = l_parkSpace->m_Data[l_side][l_numberPerside];
        //XLOG_INFO_OS(g_AppContext) << " !!!!! Received new park spot " << XLOG_ENDL;
      }
    }
  }

  cc::daddy::ParkUISpotData_t& l_rParkSpotUIDataContainer =
            cc::daddy::CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort.reserve() ;
  // ParkingSpot* l_parkingSpotPrevious;
  // Update the parking spots under the osg::Switch "m_parkingSpotAssets" based on the updated and sorted input from Daddy ("m_parkingSpotData")
  //only iterate over the real parking spots / not the extra functions
  for (vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; ++l_side)
  {
    ParkingSpot* l_parkingSpotPrevious = nullptr;
    for (vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; ++l_numberPerside)
    {
      const vfc::uint8_t l_index = static_cast<vfc::uint8_t>(l_side * static_cast<vfc::uint8_t>(cc::target::common::l_L_ParkSpace_NumberPerside) + l_numberPerside);
      if (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED   == m_parkingSpotData[l_side][l_numberPerside].m_APA_PrkgSlot ||
          cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE == m_parkingSpotData[l_side][l_numberPerside].m_APA_PrkgSlot)
      {
        ParkingSpot* const l_parkingSpot = getParkingSpot(l_index);

        setParkingSpotVisiblity(l_parkingSpot, l_customFramework);
        //set each type of parking spot's size and curPosition point
        updateParkingSpot(l_parkingSpot, m_parkingSpotData[l_side][l_numberPerside], m_spotSize, l_side);
        //compare to the previous spot and adjust the position if needed
        if (nullptr != l_parkingSpotPrevious)
        {
          avoidOverlappedParkingSpots(l_parkingSpot, l_parkingSpotPrevious);
        }
        l_parkingSpotPrevious = l_parkingSpot;

        //For set parking spot hot zone
        const UISpotData l_spotUIData =  getParkingSpotVertexandSize(l_parkingSpot, m_MVPmatrix, l_index);
        setIconCenterAndResponseArea(l_index, l_rParkSpotUIDataContainer, l_spotUIData);
        m_parkingSpotAssets->setValue(l_index, true);
      }
      else
      {
        m_parkingSpotAssets->setValue(l_index, false);
      }
    }
  }
  cc::daddy::CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort.deliver() ;

  this->DeliverButtonDispSts(l_customFramework);    // PRQA S 3804
}


} // namespace parkingspots
} // namespace assets
} // namespace cc

