//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DynamicGearOverlays.cpp
/// @brief
//=============================================================================

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/SystemConf.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include "cc/assets/dynamicgearoverlay/inc/dynamicgearoverlay.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "osg/LineWidth"
#include "osg/BlendFunc"
#include "vfc/core/vfc_types.hpp"
#include <iomanip>
#include <iostream>
#include <sstream>

namespace cc
{
namespace assets
{
namespace dynamicgearoverlay
{
// SIL test example:
// park movenum 0
// park traveldist 0
// park traveldist 10
// park movenum 1
// park traveldist 5

pc::util::coding::Item<DynamicGearSettings> g_dynamicgearSettings("DynamicGear");

//!
//! DynamicGearOverlays
//!
DynamicGearOverlays::DynamicGearOverlays(cc::core::CustomFramework* f_customFramework, vfc::uint8_t f_parkingViewID)
  : m_cull{false}
  , m_hide{false}
  , m_sequenceNumber{0u}
  //, m_parkingViewID(f_parkingViewID)
  , m_customFramework{f_customFramework}

{
    //osg::StateSet* l_stateSet = getOrCreateStateSet();
    //l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    //pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
    //l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

    setName("DynamicGearOverlays");

    DynamicGear* const l_DynamicGear = new DynamicGear(f_customFramework,f_parkingViewID);
    osg::MatrixTransform::addChild(l_DynamicGear); // PRQA S 3803


    // set to none-render by default
    //setHide(false);

    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);

}

DynamicGearOverlays::~DynamicGearOverlays() = default;

bool DynamicGearOverlays::update(pc::core::Framework* /*f_framework*/)
{
    // todo
    return false;
}


class DynamicGearCullCallback : public osg::Drawable::CullCallback // PRQA S 2113
{
public:

  explicit DynamicGearCullCallback(DynamicGear* f_DynamicGear)
    : m_DynamicGear{f_DynamicGear}
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override    // PRQA S 2120
  {
    return m_DynamicGear->isCulled();
  }

protected:

  DynamicGear* m_DynamicGear;

};

//!
//! DynamicGearHideCallback
//!
class DynamicGearHideCallback : public DynamicGearCullCallback
{
public:

  explicit DynamicGearHideCallback(DynamicGear* f_DynamicGear)
    : DynamicGearCullCallback{f_DynamicGear}
  {
  }

  bool cull(osg::NodeVisitor* /* f_nv */, osg::Drawable* /* f_drawable */, osg::RenderInfo* /* f_renderInfo */) const override
  {
    if(m_DynamicGear->isCulled() || m_DynamicGear->isHidden())
    {
      return true;
    }
    else
    {
      return false;
    }
  }

};


DynamicGear::DynamicGear(cc::core::CustomFramework* f_customFramework, vfc::uint8_t f_parkingViewID)
  : m_cull{false}
  , m_hide{false}
  , m_DynamicGearVertexArray{new osg::Vec3Array(static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u)}
  , m_position{(f_parkingViewID == UI_PANEL) ? g_dynamicgearSettings->m_position_UIPanel : g_dynamicgearSettings->m_position_MainView}
  , m_PtrColor{new osg::Vec4Array(1u)}
  , m_color{g_dynamicgearSettings->m_color}
  , m_customFramework{f_customFramework}
  , m_progress_percentage{0.0f}
  , m_isNewStep{false}
  , m_parkingViewID{f_parkingViewID}
  , m_APG_Output_MoveNumber{0u}
  , m_APG_Output_TravelDistDesired_max{5000u}
{
    osg::Vec4Array* const l_Color = new osg::Vec4Array(static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u);
    for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u; i=i+4)
    {
      (*l_Color)[i] = m_color;
    }
    //draw circle
    //define vertex array
    osg::Vec3Array* const l_cicleVertexArray = drawCircle(m_position);

    //! create fill geometry
    osg::Geometry* const l_Circle = pc::util::osgx::createGeometry("DynamicGearOverlay");
    l_cicleVertexArray->dirty();
    l_Circle->setVertexArray(l_cicleVertexArray);
    l_Circle->addPrimitiveSet(new osg::DrawArrays(static_cast<vfc::uint32_t>(osg::PrimitiveSet::QUAD_STRIP), 0, static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4)); // PRQA S 3803  // PRQA S 3143
    //l_Circle->setCullCallback(new DynamicGearHideCallback(this));
    l_Circle->setColorArray(l_Color, osg::Array::BIND_PER_VERTEX);

    osg::StateSet* const l_commonStateSet = getOrCreateStateSet();
    l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    //l_commonStateSet->setAttribute(new osg::BlendFunc(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA));
    l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_commonStateSet->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");

    l_commonStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143

    //! disable depth buffer write
    // osg::Depth* l_depth = new osg::Depth;
    // l_depth->setWriteMask(false);
    // l_commonStateSet->setAttributeAndModes(l_depth, osg::StateAttribute::OFF); // PRQA S 3143
    // l_commonStateSet->setAttribute(new osg::LineWidth(20));

    // pc::core::BasicShaderProgramDescriptor l_dynamichGearShader("dynamichGear",
    // "dynamichGear");
    // l_dynamichGearShader.apply(l_commonStateSet);    // PRQA S 3803

    l_Circle->setStateSet(l_commonStateSet);

    osg::Geode::addDrawable(l_Circle); // PRQA S 3803

}


osg::ref_ptr<osg::Vec3Array> DynamicGear::drawCircle(const osg::Vec3f& f_pos) // PRQA S 4211
{
  const vfc::float32_t l_delta = 2.0f * static_cast<vfc::float32_t>(osg::PI) / static_cast<vfc::float32_t> (CIRCLE_NUM_SEGMENTS);
  const vfc::float32_t l_outerRadius = g_dynamicgearSettings->m_outerRadius;
  const vfc::float32_t l_innerRadius = g_dynamicgearSettings->m_innerRadius;
  const vfc::float32_t l_innerPerentage = l_innerRadius/l_outerRadius;

  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u; i=i+4)
  {
    const vfc::float32_t l_angle1 = static_cast<vfc::float32_t>(i*0.25f)      * l_delta;
    const vfc::float32_t l_angle2 = static_cast<vfc::float32_t>(i*0.25f+1.0f) * l_delta;
    const vfc::float32_t l_x1 = l_outerRadius * std::cos(l_angle1);
    const vfc::float32_t l_y1 = l_outerRadius * std::sin(l_angle1);
    const vfc::float32_t l_x2 = l_outerRadius * std::cos(l_angle2);
    const vfc::float32_t l_y2 = l_outerRadius * std::sin(l_angle2);

    (*m_DynamicGearVertexArray)[i]   = osg::Vec3f(f_pos.x() + l_x1*l_innerPerentage, f_pos.y() + l_y1*l_innerPerentage, 0.0f);
    (*m_DynamicGearVertexArray)[i+1] = osg::Vec3f(f_pos.x() + l_x1,                  f_pos.y() + l_y1,                  0.0f); // PRQA S 3000
    (*m_DynamicGearVertexArray)[i+2] = osg::Vec3f(f_pos.x() + l_x2*l_innerPerentage, f_pos.y() + l_y2*l_innerPerentage, 0.0f); // PRQA S 3000
    (*m_DynamicGearVertexArray)[i+3] = osg::Vec3f(f_pos.x() + l_x2,                  f_pos.y() + l_y2,                  0.0f); // PRQA S 3000

  }
  return m_DynamicGearVertexArray;

}


DynamicGear::~DynamicGear() = default;


void DynamicGear::update()
{
  vfc::uint8_t   l_CarmoveNumber           = 0u;
  vfc::int16_t   l_CartravelDistDesire     = 0;
  cc::target::common::EPARKStatusR2L l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  bool l_dynamicGearStatus = false;
  bool l_dynamicGearMainViewStatus = false;

  if(m_customFramework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = m_customFramework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (m_customFramework->m_dynamicGearStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearStatusPtr = m_customFramework->m_dynamicGearStatus_ReceiverPort.getData();
    l_dynamicGearStatus = l_dynamicGearStatusPtr->m_Data;
  }

  if (m_customFramework->m_dynamicGearMainViewStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearMainViewStatusPtr = m_customFramework->m_dynamicGearMainViewStatus_ReceiverPort.getData();
    l_dynamicGearMainViewStatus = l_dynamicGearMainViewStatusPtr->m_Data;
  }

  if ( ((UI_PANEL == m_parkingViewID) && l_dynamicGearStatus)
    || ((MAINVIEW == m_parkingViewID) && l_dynamicGearMainViewStatus) )
  {
    setNodeMask(~0u);
  }
  else
  {
    setNodeMask(0u);
    m_progress_percentage = 0;
  }

  if(m_customFramework->m_parkCarmoveNumberReceiver.hasData())
  {
    const cc::daddy::ParkCarmoveNumberDaddy_t* const l_parkCarmoveNumber = m_customFramework->m_parkCarmoveNumberReceiver.getData();
    l_CarmoveNumber = l_parkCarmoveNumber->m_Data;
  }

  if (m_customFramework->m_parkCartravelDistDesiredReceiver.hasData())
  {
    const cc::daddy::ParkCartravelDistDesiredDaddy_t* const l_parkCartravelDistDesire = m_customFramework->m_parkCartravelDistDesiredReceiver.getData();
    l_CartravelDistDesire = l_parkCartravelDistDesire->m_Data;
  }

  // refresh condition
  if (l_CarmoveNumber == 0u)
  {
    m_APG_Output_MoveNumber = l_CarmoveNumber;
    m_progress_percentage = 0.0f;
  }

  // Adding fexible condition to prevent issue from APA.
  // Actually we expect that l_CarmoveNumber = m_APG_Output_MoveNumber + 1
  if (l_CarmoveNumber > m_APG_Output_MoveNumber)
  {
    m_APG_Output_TravelDistDesired_max = l_CartravelDistDesire;
    m_APG_Output_MoveNumber = static_cast<vfc::uint8_t>(m_APG_Output_MoveNumber + 1);
    m_isNewStep = true;
    m_progress_percentage = 0.0f;
  }

  if ( m_isNewStep == true )
  {
      if(std::abs(l_CartravelDistDesire) > std::abs(m_APG_Output_TravelDistDesired_max) )
      {
        //m_isNewStep = false;
        // during vehicle test, we found that the current distance could be greater than the max distance for the short time
        // so, SVS shall skip the bigger value for work-around solution

        // do nothing
      }
      else if (l_CarmoveNumber == 0u) // refresh condition
      {
        m_isNewStep = false;
        m_APG_Output_MoveNumber = l_CarmoveNumber;
      }
      else
      {
        if (m_APG_Output_TravelDistDesired_max != 0u)
        {
           m_progress_percentage = 1.0f - (static_cast<vfc::float32_t>(l_CartravelDistDesire) / static_cast<vfc::float32_t>(m_APG_Output_TravelDistDesired_max));
        }
        else
        {
          m_progress_percentage = 0.0f;
          // wating for receiving the first frame have maximum travel distance
          m_APG_Output_TravelDistDesired_max = l_CartravelDistDesire;
        }
      }
  }

  osg::Geometry*  const l_CircleGeometry  = getDrawable(0u)->asGeometry();
  osg::Vec4Array* const l_colors   = static_cast<osg::Vec4Array*> (l_CircleGeometry->getColorArray()); // PRQA S 3076

  // clear color
  for (vfc::uint32_t i = 0u; i < static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS)*4u; i=i+4)
  {

    (*l_colors)[i]   = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f);
    (*l_colors)[i+1] = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f); // PRQA S 3000
    (*l_colors)[i+2] = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f); // PRQA S 3000
    (*l_colors)[i+3] = osg::Vec4f(0.0f, 1.0f, 0.0f, 0.0f); // PRQA S 3000

  }

  // update color

  vfc::uint32_t l_numOfShowingSegment = 0u;

  if (isGreater(m_progress_percentage,0.99f))
  {
    l_numOfShowingSegment = CIRCLE_NUM_SEGMENTS;
  }
  else
  {
    l_numOfShowingSegment = static_cast<vfc::uint32_t>(m_progress_percentage)*static_cast<vfc::uint32_t>(CIRCLE_NUM_SEGMENTS);
  }


  for (vfc::uint32_t i = 0u; i < l_numOfShowingSegment*4u; i=i+4)
  {
    (*l_colors)[i]   = osg::Vec4f(g_dynamicgearSettings->m_color);
    (*l_colors)[i+1] = osg::Vec4f(g_dynamicgearSettings->m_color); // PRQA S 3000
    (*l_colors)[i+2] = osg::Vec4f(g_dynamicgearSettings->m_color); // PRQA S 3000
    (*l_colors)[i+3] = osg::Vec4f(g_dynamicgearSettings->m_color); // PRQA S 3000

  }

  l_colors->dirty();
  //l_CircleGeometry->dirtyBound();

}


void DynamicGearOverlays::traverse(osg::NodeVisitor& f_nv) // PRQA S 6043
{
    //if (m_framework && (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType()))
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {

      DynamicGear* const l_DynamicGear = dynamic_cast<DynamicGear*> (getChild(0)); // PRQA S 3077  // PRQA S 3400
      if (l_DynamicGear != nullptr)
      {
        l_DynamicGear->update();

      }
    }

    osg::MatrixTransform::traverse(f_nv);
}


} // namespace dynamicgearoverlay
} // namespace assets
} // namespace cc

