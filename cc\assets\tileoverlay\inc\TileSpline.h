//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileSpline.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_TILEOVERLAY_TILESPLINE_H
#define CC_ASSETS_TILEOVERLAY_TILESPLINE_H

#include "pc/svs/util/math/inc/Polygon2D.h"

#include <osg/Geode>
#include <osg/Vec2f>
#include <osg/Geometry>
#include <osg/Math>
#include <osg/Image>
#include <osg/Texture2D>

#include <array>

#include "cc/assets/tileoverlay/inc/TileOverlay.h"
#include "cc/core/inc/CustomUltrasonic.h"
#define USE_SWD

namespace cc
{
namespace assets
{
namespace tileoverlay
{

typedef pc::util::math::LinearInterpolator<osg::Vec4f> ColorInterpolator;
extern ColorInterpolator g_colorInterpolator;

osg::Vec4f fromRGBA(int f_r, int f_g, int f_b, int f_a);
osg::Vec4f fromRGBA(const osg::Vec4i& f_rgba);

osg::Vec4f brighten(const osg::Vec4f& f_color, float f_add);

enum EPasZoneLocatedArea  : unsigned int
{
  EPasZoneFront,
  EPasZoneCorner,
  EPasZoneSide,
  EPasZoneRear
};
        
const std::size_t NUM_CONTROL_POINTS = 4u;
const std::size_t NUM_INTERPOLATED_POINTS = 8u;
const float Sub_division_offset_percentage = 0.1f;
const float Sub_division_offset = Sub_division_offset_percentage*0.25f;

class TileLayout // The distances are measured around the car along the sectors.
{
public:
  TileLayout();
  // ~TileLayout();

  osg::Vec2f m_shieldStartInner;
  // osg::Vec2f m_shieldStartMiddle;
  osg::Vec2f m_shieldStartOuter;
  osg::Vec2f m_shieldStartSolid;
  osg::Vec2f m_shieldStart3DOuter;
  osg::Vec2f m_shieldEndInner;
  // osg::Vec2f m_shieldEndMiddle;
  osg::Vec2f m_shieldEndOuter;
  osg::Vec2f m_shieldEndSolid;
  osg::Vec2f m_shieldEnd3DOuter;
  osg::Vec2f m_verticalRatioDivideOffset;
  osg::Vec2f m_verticalOffset;
  bool       m_isStartEdgeSegment;
  bool       m_isEndEdgeSegment;
  float      m_currentDistance;
  float      m_currentDistanceForPosDisp;
  EObjMovingSts m_ObjMovingSts;
  EPasZoneLocatedArea m_pasZoneLocatedArea;

};

  //!
  //! UpdateVisitor
  //!
  class TileUpdateVisitor : public osg::NodeVisitor
  {
  public:
    typedef std::array<osg::Vec2f, NUM_CONTROL_POINTS> ControlPointArray;
    typedef std::array<osg::Vec2f, NUM_INTERPOLATED_POINTS> PointArray;
    typedef std::array<PointArray, cc::target::sysconf::E_ULTRASONIC_NUM_ZONES> PointArrayZone;
  
    TileUpdateVisitor();

    virtual ~TileUpdateVisitor();

    virtual void apply(osg::Node& f_node) override;    // PRQA S 2120
    //void setLayoutData(const std::vector<TileSectorData>& f_tileSectors);
    void setSplineTileLayoutData(const std::vector<TileSectorData>& f_tileSectors);
    const std::vector<TileLayout>& getLayout() const
    {
      return m_layout;
    }

  void copyInterpolateArray(PointArrayZone* f_array);

  private:

    //! Copy constructor is not permitted.
    TileUpdateVisitor (const TileUpdateVisitor& other); // = delete
    //! Copy assignment operator is not permitted.
    TileUpdateVisitor& operator=(const TileUpdateVisitor& other); // = delete

    std::vector<TileLayout> m_layout;
    std::vector<TileLayout> m_layoutShadow;

    ControlPointArray m_InnerControlPoints;
    ControlPointArray m_OuterControlPoints;

    PointArray m_InnerPoints;
    PointArray m_OuterPoints;
    PointArrayZone m_InnerPointsArray;
  };


//!
//! TileSpline
//!
class TileSpline : public osg::Geode
{
public:

  TileSpline(const TileSpline& f_other, const osg::CopyOp& f_copyOp);

  virtual void update(const TileUpdateVisitor& f_visitor) = 0;

protected:

  TileSpline();

  virtual ~TileSpline();

  unsigned int m_numLayoutPoints;

private:

  //! Copy constructor is not permitted.
  TileSpline (const TileSpline& other); // = delete
  //! Copy assignment operator is not permitted.
  TileSpline& operator=(const TileSpline& other); // = delete

};


//!
//! TileShield3DInner
//!
class TileShield3DInner : public TileSpline
{
public:

  enum  : unsigned int
  {
    NUM_VERTICES_PER_SHIELD_SEGMENT = 3u,
    NUM_VERTICES_PER_HAIRLINE_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_SHIELD_SEGMENT + NUM_VERTICES_PER_HAIRLINE_SEGMENT
  };

  TileShield3DInner();
  TileShield3DInner(const TileShield3DInner& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::tileoverlay, TileShield3DInner);  // PRQA S 2504

  virtual void update(const TileUpdateVisitor& f_visitor) override;

protected:

  virtual ~TileShield3DInner();

private:

  //! Copy constructor is not permitted.
  TileShield3DInner (const TileShield3DInner& other); // = delete
  //! Copy assignment operator is not permitted.
  TileShield3DInner& operator=(const TileShield3DInner& other); // = delete

};


class TileShield3DOuter : public TileSpline
{
public:

  enum  : unsigned int
  {
    NUM_VERTICES_PER_SHIELD_SEGMENT = 3u,
    NUM_VERTICES_PER_HAIRLINE_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_SHIELD_SEGMENT + NUM_VERTICES_PER_HAIRLINE_SEGMENT
  };

  TileShield3DOuter();
  TileShield3DOuter(const TileShield3DOuter& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::tileoverlay, TileShield3DOuter);  // PRQA S 2504

  virtual void update(const TileUpdateVisitor& f_visitor) override;

protected:

  virtual ~TileShield3DOuter();

private:

  //! Copy constructor is not permitted.
  TileShield3DOuter (const TileShield3DOuter& other); // = delete
  //! Copy assignment operator is not permitted.
  TileShield3DOuter& operator=(const TileShield3DOuter& other); // = delete

};


//!
//! TileCoverTopBottom2D
//!
class TileCoverTopBottom2D : public TileSpline
{
public:

  enum  : unsigned int
  {
    NUM_VERTICES_PER_COVER_SEGMENT = 2u,
    NUM_VERTICES_PER_HAIRLINE_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_COVER_SEGMENT + NUM_VERTICES_PER_HAIRLINE_SEGMENT
  };

  TileCoverTopBottom2D();
  TileCoverTopBottom2D(const TileCoverTopBottom2D& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::tileoverlay, TileCoverTopBottom2D);  // PRQA S 2504

  virtual void update(const TileUpdateVisitor& f_visitor) override;

  void setOffset(const float f_offet);

protected:

  virtual ~TileCoverTopBottom2D();

private:

  //! Copy constructor is not permitted.
  TileCoverTopBottom2D (const TileCoverTopBottom2D& other); // = delete
  //! Copy assignment operator is not permitted.
  TileCoverTopBottom2D& operator=(const TileCoverTopBottom2D& other); // = delete

  float m_offset;
};


class Tile2DTopview : public TileSpline
{
public:

  enum  : unsigned int
  {
    NUM_VERTICES_PER_COVER_SEGMENT = 2u,
    NUM_VERTICES_PER_HAIRLINE_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_COVER_SEGMENT + NUM_VERTICES_PER_HAIRLINE_SEGMENT
  };

  Tile2DTopview();
  Tile2DTopview(const Tile2DTopview& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::tileoverlay, Tile2DTopview);  // PRQA S 2504

  virtual void update(const TileUpdateVisitor& f_visitor) override;

protected:

  virtual ~Tile2DTopview();

private:

  //! Copy constructor is not permitted.
  Tile2DTopview (const Tile2DTopview& other); // = delete
  //! Copy assignment operator is not permitted.
  Tile2DTopview& operator=(const Tile2DTopview& other); // = delete
};


//!
//! TileSideSquare3D
//!
class TileSideSquare3D : public TileSpline
{
public:

  enum : unsigned int
  {
    NUM_VERTICES_PER_SHIELD_SEGMENT = 3u,
    NUM_VERTICES_PER_HAIRLINE_SEGMENT = 2u,
    NUM_VERTICES_PER_SEGMENT = NUM_VERTICES_PER_SHIELD_SEGMENT + NUM_VERTICES_PER_HAIRLINE_SEGMENT
  };

  TileSideSquare3D();
  TileSideSquare3D(const TileSideSquare3D& f_other, const osg::CopyOp& f_copyOp);

  META_Node(cc::assets::tileoverlay, TileSideSquare3D);  // PRQA S 2504

  virtual void update(const TileUpdateVisitor& f_visitor) override;

protected:

  virtual ~TileSideSquare3D();

private:

  //! Copy constructor is not permitted.
  TileSideSquare3D (const TileSideSquare3D& other); // = delete
  //! Copy assignment operator is not permitted.
  TileSideSquare3D& operator=(const TileSideSquare3D& other); // = delete

};

osg::Vec4f getColorShieldWithThreshold(const float f_currentDistance, const EObjMovingSts f_ObjMovingSts);
void updateInterpolators();
osg::Geometry* createGeometry(const std::string& f_name, bool f_withTexCoors);
osg::DrawElements* createSurface(unsigned int f_numLayoutPoints, unsigned int f_numSurfacePoints, unsigned int f_vertexOffset);
osg::Image* createSmoothingTexture();
osg::StateSet* getOrCreateTexturingStateSet();
void updatePasZoneAlphaByThreshold(const EPasZoneLocatedArea f_zoneArea, const float f_currentDistance, const EObjMovingSts f_ObjMovingSts, float& f_currentalpha);

} // namespace tileoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TILEOVERLAY_TILESPLINE_H