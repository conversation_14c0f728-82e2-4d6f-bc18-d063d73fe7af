#!/bin/bash
#==============================================================================
# Copyright (c) 2019 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#==============================================================================
# SVS DAI continuous integration

#-------------------------------------------------------------------------------
# Help and sanity checks

set -e

prqa_install="/opt/Perforce/Helix-QAC-2023.4"

printHelp()
{
  echo -e "\nUsage: $0 [option]\n" >&2
  echo -e "--set-license:\tset license server" >&2
  echo -e "--source-config:\tsource config json file" >&2
  echo -e "--sync-build-log-file:\tcompile_commands.json" >&2
  echo -e "--export-format:\tDefines the report export formats. Available formats: 'xlsx', 'csv', 'csv_zip', 'html' or 'all', where 'all' is a list of commonly used report formats: xlsx, csv and html. Extra formats can be specified explicitly, e.g. -ef all csv_zip." >&2
  echo -e "--codeowners-file:\tAbsolute path or relative to PROJECT_ROOT to the codeowners file. Accepts single path or list of paths\n" >&2
  echo -e "--opt:\tscan operation: create, analyze, qaview" >&2
  echo -e "--datastore-target:\tTarget project to pull configurations from\n" >&2
  echo -e "--project-root:\tproject root dir absolute path\n" >&2
  echo -e "--qap:\tPath relative to project root or absolute, without leading slash, this will also be the project name\n" >&2
  exit 1
}

trap 'echo -e "${LINENO}: \"$BASH_COMMAND\" command filed with exit code $?. $(printHelp)"' ERR


QAC_COMMON_ARGS=""
PRJ_ROOT_DIR=$(pwd)

for pass in 1 2; do
    while [ -n "$1" ]; do
        case $1 in
            --) shift; break;;
            -*) case $1 in
                --set-license)
                    $prqa_install/common/bin/qacli admin --set-license-server <EMAIL>
                    $prqa_install/common/bin/qacli admin --set-license-server <EMAIL>
                    exit 0
                    ;;
                --source-config)
                    SOURCE_CONFIG=$2
                    QAC_COMMON_ARGS+=" -dp $2"
                    shift
                    ;;
                --sync-build-log-file)
                    export SYNC_BUILD_LOG_FILE=$2
                    shift
                    ;;
                --export-format)
                    QAC_COMMON_ARGS+=" -ef $2"
                    shift
                    ;;
                --codeowners-file)
                    QAC_COMMON_ARGS+=" -cf $2"
                    shift
                    ;;
                --opt)
                    case $2 in
                        create)
                            opt_func=create
                            ;;
                        analyze)
                            opt_func=analyze
                            ;;
                        qaview)
                            opt_func=qaview
                            ;;
                        pr)
                            opt_func=pr
                            ;;
                        *)
                            echo 
                            printHelp
                            exit 1
                            ;;
                    esac
                    ;;
                --qap)
                    QA_PROJECT=$2

                    QAC_COMMON_ARGS+=" -qap $2"
                    shift
                    ;;
                --cpp-changes)
                    CPP_PR_CHANGES_LIST=$2
                    shift
                    ;;
                --c-changes)
                    C_PR_CHANGES_LIST=$2
                    shift
                    ;;
                --datastore-target)
                    QAC_COMMON_ARGS+=" -dt $2"
                    shift
                    ;;
                --project-root)
                    QAC_COMMON_ARGS+=" -pr $2"
                    shift
                    ;;
                -h|--help)
                    printHelp
                    exit 1
                    ;;
                --baseline)
                    USE_BASELINE="true"
                    ;;
                --*)           error $1;;
                -*)            if [ $pass -eq 1 ]; then ARGS="$ARGS $1";
                               else error $1; fi;;
                esac;;
            *)  if [ $pass -eq 1 ]; then ARGS="$ARGS $1";
                else error $1; fi;;
        esac
        shift
    done
done


if [[ -f "cc/tools/sca_tools_package/sca_tools/sca_tools.py" ]]; then
    QAC_COMMAND="python cc/tools/sca_tools_package/sca_tools/sca_tools.py qac"
else
    echo "missing opt tools check your sca_tools location"
    exit 1
fi

customer_config() {
    echo "run project config"
    $prqa_install/common/bin/qacli pprops --qaf-project $QA_PROJECT -c qacpp-6.4.0 -o i --set $PRJ_ROOT_DIR/tmp/Deps_SA8295P_QNX71_osg323_adit/install/usr/include
    $prqa_install/common/bin/qacli pprops --qaf-project $QA_PROJECT -c qacpp-6.4.0 -o i --set $PRJ_ROOT_DIR/tmp/Deps_SA8295P_QNX71_osg323_adit/install/usr/include/deps
    $prqa_install/common/bin/qacli pprops --qaf-project $QA_PROJECT -c qacpp-6.4.0 -o i --set $PRJ_ROOT_DIR/hw/mom/daddy/inc
    $prqa_install/common/bin/qacli pprops --qaf-project $QA_PROJECT -c qacpp-6.4.0 -o i --set $PRJ_ROOT_DIR/hw/mom/vfc/include


    $prqa_install/common/bin/qacli pprops --qaf-project $QA_PROJECT -c qacpp-6.4.0 --view-values
}

helix_pr() {
    echo "run pr"
    echo "run analyze"

    cd cc/tools/sca_tools_package/
    git apply  ../../../cc/build/tools/qa/patch || true
    cd -

    echo "$QAC_COMMAND analyze ${QAC_COMMON_ARGS} --analyse_list ${CPP_PR_CHANGES_LIST}"
    $QAC_COMMAND analyze ${QAC_COMMON_ARGS} --analyse_list ${CPP_PR_CHANGES_LIST}
}

helix_create() {
    echo "run create"
    if [[ "${USE_BASELINE}" == "true" ]]; then
        QAC_COMMON_ARGS+=" -hsb bs_$QA_PROJECT/prqa/configs/Initial/output"
    fi

    #make sv3d soc=qc sup=mega -j6 --dry-run | grep -wE  'gcc|g\+\+' | grep -w '\-c' | jq -nR '[inputs|{directory:".", command:., file: match(" [^ ]+$").string[1:]}]' > compile_commands.json

    echo "$QAC_COMMAND create ${QAC_COMMON_ARGS}"
    $QAC_COMMAND create ${QAC_COMMON_ARGS}

    customer_config
}

helix_analyze() {
    if [[ "${USE_BASELINE}" == "true" ]]; then
        QAC_COMMON_ARGS+=" -hcb true"
    fi

    echo "run analyze"
    echo "$QAC_COMMAND analyze ${QAC_COMMON_ARGS}"
    $QAC_COMMAND analyze ${QAC_COMMON_ARGS}
}

helix_qaview() {
    echo "run view"
    echo "$QAC_COMMAND qaview ${QAC_COMMON_ARGS}"
    $QAC_COMMAND qaview ${QAC_COMMON_ARGS}
}


helix_$opt_func
