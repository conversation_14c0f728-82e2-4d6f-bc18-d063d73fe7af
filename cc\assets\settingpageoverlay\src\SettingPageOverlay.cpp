#include "cc/assets/settingpageoverlay/inc/SettingPageOverlay.h"

#include "cc/assets/button/inc/Slider.h"
#include "cc/assets/button/inc/TestButton.h" // PRQA S 1060
#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/util/pdmwriter/inc/PdmWriter.h"
namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

pc::util::coding::Item<SettingPageOverlaySettings> g_settingPageOverlaySettings("SettingPageOverlaySetting");
SettingPageBackground::SettingPageBackground(
    cc::core::AssetId    f_assetId,
    SettingPageGroupID   f_settingPageGroupID,
    pc::core::Framework* f_framework,
    SettingPageSwitch*   f_settingPageSwtich,
    osg::Camera*         f_referenceView)
    : cc::assets::button::Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_settingPageGroupID{f_settingPageGroupID}
    , m_settingPageSwitch{f_settingPageSwtich}
{
    setState(AVAILABLE);
    setName("SettingPageBackground");
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(g_settingPageOverlaySettings->m_settingPageBackgroundPos);
}

SettingPageBackground::~SettingPageBackground() = default;

void SettingPageBackground::onAvailable()
{
    setIconEnable(true);
}

void SettingPageBackground::update()
{
    // GET_PORT_DATA(
    //     touchStatusContainer,
    //     m_framework->asCustomFramework()->m_HUTouchTypeReceiver,
    //     touchStatusPortHaveData);
    // GET_PORT_DATA(
    //     hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData);
    // if (hmiDataPortHaveData)
    // {
    //     setHuX(hmiDataContainer->m_Data.m_huX);
    //     setHuY(hmiDataContainer->m_Data.m_huY);
    // }

    // bool touchStatusChanged = false;
    // if (touchStatusPortHaveData)
    // {
    //     touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
    //     setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    // }

    // bool touchInsideResponseArea = checkTouchInsideResponseArea();
    // if( touchStatusChanged && !touchInsideResponseArea)
    // {
    //     m_settingPageSwitch->closeSettingPage();
    // }

    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    if(m_settingPageGroupID == SettingPageGroupID::PAGE_ONE)
    {
        setTexturePath(
        (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            ? g_settingPageOverlaySettings->m_settingPageBackgroundTextureGroupOneDayPath
            : g_settingPageOverlaySettings->m_settingPageBackgroundTextureGroupOneNightPath);
    }
    else
    {
        setTexturePath(
        (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            ? g_settingPageOverlaySettings->m_settingPageBackgroundTextureGroupTwoDayPath
            : g_settingPageOverlaySettings->m_settingPageBackgroundTextureGroupTwoNightPath);
    }
    setState(AVAILABLE);
}

SettingPageSonarDeactiveFrontground::SettingPageSonarDeactiveFrontground(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView)
    : cc::assets::button::Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
{
    setState(AVAILABLE);
    setName("SettingPageSonarDeactiveFrontground");
    osg::Camera* l_hudCamera = new osg::Camera(); // PRQA S 3802  #code looks fine
    l_hudCamera              = static_cast<osg::Camera*>(this->getAsset()); // PRQA S 3076
    l_hudCamera->setRenderOrder(osg::Camera::POST_RENDER, 501);
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(g_settingPageOverlaySettings->m_settingPageSonarDeactiveFrontgroundPos);
}

SettingPageSonarDeactiveFrontground::~SettingPageSonarDeactiveFrontground() = default;

void SettingPageSonarDeactiveFrontground::update()
{
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    setTexturePath(
    (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
        ? g_settingPageOverlaySettings->m_settingPageSonarDeactiveFrontgroundDayTexturePath
        : g_settingPageOverlaySettings->m_settingPageSonarDeactiveFrontgroundNightTexturePath);

    bool isPasActStatusButtonDeactived = false;
    if (m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.isConnected())
    {
        const cc::daddy::SonarAPPDataDaddy_t* const l_pData =
            m_framework->asCustomFramework()->m_SonarAPPData_ReceiverPort.getData();
        if (nullptr != l_pData)
        {
            isPasActStatusButtonDeactived = (l_pData->m_Data.m_sonarStatusDisplayRequest == cc::target::common::ESonarStatusDisplayRequest::SONARSTATUS_DEACTIVE);
        }
    }
    if( isPasActStatusButtonDeactived )
    {
        setState(AVAILABLE);
    }
    else{
        setState(UNAVAILABLE);
    }
}


SettingPageGroupID SettingPageGroupHandler::s_curPage = SettingPageGroupID::PAGE_ONE;

SettingPageOverlay::SettingPageOverlay(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : cc::assets::button::ButtonGroup{f_assetId}
    , m_framework{f_framework}
    , m_isSettingPageEnabled{false}
{
    const auto settingInfoDialog =
        new cc::assets::button::Dialog(cc::core::AssetId::EASSETS_TEST_BUTTON, f_framework, f_referenceView);
    // Function Switches
    const auto settingPageModStatusButton = new SettingPageMODStatusButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_MOD_SUBBUTTON,
        g_settingPageOverlaySettings->m_MODSwichButtonPos,
        f_framework,
        f_referenceView);
    const auto settingPageDGearActStatusButton = new SettingPageDGearActStatusButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_DGEARACT_SUBBUTTON,
        g_settingPageOverlaySettings->m_DGearActSwichButtonPos,
        f_framework,
        f_referenceView);
    const auto settingPageSteerActStatusButton = new SettingPageSteerActStatusButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_STEERINGACT_SUBBUTTON,
        g_settingPageOverlaySettings->m_SteerActSwichButtonPos,
        f_framework,
        f_referenceView);

    // SettingPageSonarTrigLevelButtonGroup* const l_settingPageSonarTrigLevelButtonGroup =
    //     new SettingPageSonarTrigLevelButtonGroup(
    //         cc::core::AssetId::EASSETS_SETTINGPAGE_SONAR_TRIG_LEVEL_SUBBUTTONS,
    //         g_settingPageOverlaySettings->m_sonarLevelSettings,
    //         f_framework,
    //         f_referenceView);
#ifdef ENABLE_APAUI
    const auto settingPagePasActStatusButton = new SettingPagePasActStatusButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_PASACT_SUBBUTTON,
        g_settingPageOverlaySettings->m_PasActSwichButtonPos,
        f_framework,
        nullptr,
        f_referenceView);
    const auto settingPageNarrowLaneActButton = new SettingPageNarrowLaneActButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_NARROWLANEACT_SUBBUTTON,
        g_settingPageOverlaySettings->m_NarrowLaneActSwichButtonPos,
        f_framework,
        f_referenceView
    );
#endif
    const auto settingPageVehTransStatusButton = new SettingPageVehTransStatusButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEHTRANS_SUBBUTTON,
        g_settingPageOverlaySettings->m_VehTransSwichButtonPos,
        f_framework,
        f_referenceView);
    const auto settingPageNightModeStatusButton = new SettingPageNightModeStatusButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_NIGHTMODE_SUBBUTTON,
        g_settingPageOverlaySettings->m_NightModeSwichButtonPos,
        f_framework,
        f_referenceView);
    const auto settingPageMODInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_MOD_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_MODInfoButtonPos,
        cc::assets::button::DIALOG_MOD,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
    const auto settingPageDGearActInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_DGEARACT_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_DGearActInfoButtonPos,
        cc::assets::button::DIALOG_DGEARACT,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
#ifdef ENABLE_APAUI
    const auto settingPagePasActInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_PASACT_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_PasActInfoButtonPos,
        cc::assets::button::DIALOG_SONARACT,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
    const auto settingPageNarrowLaneActInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_NARROWLANEACT_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_NarrowLaneActInfoButtonPos,
        cc::assets::button::DIALOG_NARROWLANEACT,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
#endif
    const auto settingPageVehTransInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_VEHTRANS_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_VehTransInfoButtonPos,
        cc::assets::button::DIALOG_VEHTRANS,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
    const auto settingPageSteerActInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_STEERINGACT_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_SteerActInfoButtonPos,
        cc::assets::button::DIALOG_STEERACT,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
    const auto settingPageNightModeInfoButton = new SettingPageInfoButton(
        cc::core::AssetId::EASSETS_SETTINGPAGE_NIGHTMODE_INFOBUTTON,
        f_framework,
        g_settingPageOverlaySettings->m_NightModeInfoButtonPos,
        cc::assets::button::DIALOG_NIGHTMODE,
        static_cast<cc::assets::button::IDialogTrigger*>(settingInfoDialog),
        f_referenceView);
    SettingPageVehColorButtonGroup* const settingPageVehColorButtonGroup =
        new SettingPageVehColorButtonGroup(
            cc::core::AssetId::EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
            g_settingPageOverlaySettings->m_vehicleColorSettings,
            f_framework,
            f_referenceView);
    SettingPageSonarDeactiveFrontground* const l_settingPageSonarDeactiveFrontground =
    new SettingPageSonarDeactiveFrontground(cc::core::AssetId::EASSETS_SETTINGPAGE_PASACT_FRONT_GROUND, f_framework, f_referenceView);
    // auto settingPageGroupOne = new SettingPageGroup(
    //     cc::core::AssetId::EASSETS_SETTINGPAGE_OVERLAY, f_framework, SettingPageGroupID::PAGE_ONE, f_referenceView);
    addButton(new SettingPageBackground(f_assetId, SettingPageGroupID::PAGE_ONE, f_framework, static_cast<SettingPageSwitch*>(this), f_referenceView));
    addButton(settingPageModStatusButton);
    addButton(settingPageDGearActStatusButton);
#ifdef ENABLE_APAUI
    addButton(settingPagePasActStatusButton);
    addButton(settingPageNarrowLaneActButton);
#endif
    // addButtonGroup(l_settingPageSonarTrigLevelButtonGroup);
    addButton(settingPageMODInfoButton);
    addButton(settingPageDGearActInfoButton);
#ifdef ENABLE_APAUI
    addButton(settingPagePasActInfoButton);
    addButton(settingPageNarrowLaneActInfoButton);
#endif
    addButton(l_settingPageSonarDeactiveFrontground);
    // auto settingPageGroupTwo = new SettingPageGroup(
    //     cc::core::AssetId::EASSETS_SETTINGPAGE_OVERLAY, f_framework, SettingPageGroupID::PAGE_TWO, f_referenceView);
    // settingPageGroupTwo
    // ->addButton(new SettingPageBackground(f_assetId, SettingPageGroupID::PAGE_TWO
    // , f_framework, static_cast<SettingPageSwitch*>(this), f_referenceView));
    addButton(settingPageVehTransStatusButton);
    addButton(settingPageSteerActStatusButton);
    addButton(settingPageNightModeStatusButton);
    addButton(settingPageVehTransInfoButton);
    addButton(settingPageSteerActInfoButton);
    addButton(settingPageNightModeInfoButton);
    addButtonGroup(settingPageVehColorButtonGroup);
    // auto settingPageFlippingButton = new SettingPageFlippingButton(
    //     cc::core::AssetId::EASSETS_SETTINGPAGE_OVERLAY,
    //     g_settingPageOverlaySettings->m_SettingPageFlippingButtonPos,
    //     g_settingPageOverlaySettings->m_SettingPageLastPageFlipArea,
    //     g_settingPageOverlaySettings->m_SettingPageNextPageFlipArea,
    //     f_framework,
        // f_referenceView);
    // addButtonGroup(settingPageGroupOne);
    // addButtonGroup(settingPageGroupTwo);
    // addButton(settingPageFlippingButton);
    addButtonGroup(settingInfoDialog);
}

SettingPageOverlay::~SettingPageOverlay() = default;

void SettingPageOverlay::update()
{
    m_enabled = m_isSettingPageEnabled;
}

SettingPageGroup::SettingPageGroup(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    SettingPageGroupID   f_groupID,
    osg::Camera*         /*f_referenceView*/)
    : ButtonGroup{f_assetId}
    , m_framework{f_framework}
    , m_groupID{f_groupID}
{
}
SettingPageGroup::~SettingPageGroup() = default;
} // namespace settingpageoverlay
} // namespace assets
} // namespace cc
