//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin(CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileCallback.h
/// @brief 
//=============================================================================

#ifndef CC_ASSETS_TILEOVERLAY_TILECALLBACK_H
#define CC_ASSETS_TILEOVERLAY_TILECALLBACK_H

#include "cc/core/inc/CustomFramework.h"

#include <osg/NodeCallback>
#include <osg/NodeVisitor>


namespace cc
{
namespace assets
{
namespace tileoverlay
{

//======================================================
// TileCallback
//------------------------------------------------------
/// Evaluates common requirements for visibility of tile overlays
/// Evaluates the minimum conditions for an tile overlays to be displayed.
/// <AUTHOR> Xiangqin
//======================================================
class TileCallback : public osg::NodeCallback
{
public:
  TileCallback(pc::core::Framework* f_framework, bool f_isSettingBased);

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
  virtual ~TileCallback();

private:

  //! Copy constructor is not permitted.
  TileCallback (const TileCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  TileCallback& operator=(const TileCallback& other); // = delete

  void checkTileStatus();

  pc::core::Framework* m_framework;
  bool m_tileStatus;
  bool m_isSettingBased;

};



} // namespace tilecallback
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TILEOVERLAY_TILECALLBACK_H