#===============================================================================
# Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
# This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

include hw/build/module_head.mk

SOURCEFILES = \
CustomIcon \
CustomImageOverlays.cpp \
DynamicDistance.cpp \
HmiElementsSettings.cpp \
ParkingConfirmInterface.cpp \
ParkingSearching.cpp \
ParkingSlot.cpp \
ParkingView.cpp \
RemainingMoveNumber.cpp \
SpeedOverlay.cpp \
TextSymbols.cpp \
Utils.cpp \
Vehicle2dIcon.cpp \
Vehicle2DOverlay.cpp \
VehicleTransIcon.cpp \
WarnSymbols.cpp \
WheelSeparator.cpp \
SettingBar.cpp \
ViewInfoOverlay.cpp \
RotateIcon.cpp
# ParkingSlotFixed.cpp \

BINARY = object

include hw/build/$(COMPILER_NAME)/module_tail.mk