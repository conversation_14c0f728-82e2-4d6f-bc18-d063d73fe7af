//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Vehicle2dIcon.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_VEHICLE2D_ICON_H
#define CC_ASSETS_UIELEMENTS_VEHICLE2D_ICON_H

#include "pc/svs/assets/imageoverlays/inc/ImageOverlays.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/views/planview/inc/PlanView.h"

#include <osg/Matrixf>


namespace cc
{
namespace core
{
class CustomFramework;
} // namespace core

namespace assets
{
namespace uielements
{

//======================================================
// VehicleVehicle2dIconSettings
//------------------------------------------------------
/// Setting class for VehicleVehicle2D
/// <AUTHOR>
//======================================================

//!
//! Vehicle2dIconSettings
//!
class Vehicle2dIconSettings : public pc::util::coding::ISerializable
{
public:
  Vehicle2dIconSettings()
    : m_size(146.0f, 400.0f)
    , m_planViewPos(215.0f, 355.0f)
    , m_parkingPlanViewPos(854.0f, 355.0f)
    , m_planViewPosVert(180.0f, 180.0f)
    , m_parkingPlanViewPosVert(280.0f, 180.0f)
    , m_texturePathDoorOpen("cc/resources/vehicle_2d_doors_open.png")
    , m_texturePathDoorOpenVert("cc/resources/vehicle_2d_doors_open_vert.png")
  {

  }

  SERIALIZABLE(Vehicle2dIconSettings)
  {
    ADD_MEMBER(osg::Vec2f, size);
    ADD_MEMBER(osg::Vec2f, planViewPos);
    ADD_MEMBER(osg::Vec2f, parkingPlanViewPos);
    ADD_MEMBER(osg::Vec2f, planViewPosVert);;
    ADD_MEMBER(osg::Vec2f, parkingPlanViewPosVert);
    ADD_STRING_MEMBER(texturePathDoorOpen);
    ADD_STRING_MEMBER(texturePathDoorOpenVert);
  }

  osg::Vec2 m_size;
  osg::Vec2 m_planViewPos;
  osg::Vec2 m_parkingPlanViewPos;
  osg::Vec2 m_planViewPosVert;
  osg::Vec2 m_parkingPlanViewPosVert;
  std::string m_texturePathDoorOpen;
  std::string m_texturePathDoorOpenVert;
};
extern pc::util::coding::Item<Vehicle2dIconSettings> g_Vehicle2dIconSettings;

//!
//! Vehicle2dIconManager
//!
class Vehicle2dIconManager
{
public:
  typedef std::vector< osg::ref_ptr<pc::assets::Icon> > IconList;

  Vehicle2dIconManager();
  virtual ~Vehicle2dIconManager();

  void init(pc::assets::ImageOverlays* f_imageOverlays, cc::target::common::EThemeTypeHU f_theme);
  void addInitIcons(pc::assets::ImageOverlays* f_imageOverlays, const osg::Matrixf f_mat);
  void update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework);
  void setVehicleIconPosition(core::CustomFramework* f_framework);
  void setVehicleIconSize(core::CustomFramework* f_framework);

private:
  //! Copy constructor is not permitted.
  Vehicle2dIconManager (const Vehicle2dIconManager& other); // = delete
  //! Copy assignment operator is not permitted.
  Vehicle2dIconManager& operator=(const Vehicle2dIconManager& other); // = delete

  unsigned int m_lastConfigUpdate;
  pc::assets::IconGroup m_Vehicle2dIcons;
  cc::views::planview::PlanViewCullCallback* m_planViewCullCall;
  bool m_mat_b;
  cc::target::common::EThemeTypeHU m_theme;
  bool m_parkActive;
};


//!
//! Vehicle2dIcon
//!
class Vehicle2dIcon: public cc::assets::uielements::CustomImageOverlays
{
public:
  Vehicle2dIcon(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId);
  virtual ~Vehicle2dIcon();

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  //! Copy constructor is not permitted.
  Vehicle2dIcon (const Vehicle2dIcon& other); // = delete
  //! Copy assignment operator is not permitted.
  Vehicle2dIcon& operator=(const Vehicle2dIcon& other); // = delete

  cc::core::CustomFramework* m_customFramework;
  Vehicle2dIconManager m_manager;
};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_VEHICLE2D_ICON_H
