//-------------------------------------------------------------------------------
// Copyright (c) 2021 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TILEOVERLAY_SPLINEHELPER_H
#define CC_ASSETS_TILEOVERLAY_SPLINEHELPER_H

#include <osg/Vec2>
#include <osg/Vec4ub>
#include <cstdint>
#include <limits>
#include <vector>


namespace cc
{
namespace assets
{
namespace tileoverlay
{
  
//!
//! @brief linear interpolation
//!
//! @tparam T type
//! @param f_a point or scalar a
//! @param f_b point or scalar b
//! @param f_t amount of inter interpolation assumed to be in range 0 - 1
//!
template <typename T>
T lerp(const T& f_a, const T& f_b, float f_t)
{
  return f_a + ((f_b - f_a) * f_t);
}


typedef std::array<osg::Vec2f, 4> ControlPoints;
typedef std::array<float, 4> KnotSequence;

template <class ControlPointIter>
KnotSequence computeKnotSequence(ControlPointIter f_cp, float f_alpha = 0.5f)
{
  auto l_getT = [] (const osg::Vec2f& f_p0, const osg::Vec2f& f_p1, float f_t, float f_alphaT)
  {
    const osg::Vec2f l_diff = f_p1 - f_p0;
    const float l_dot = l_diff * l_diff; // dot prodoct
    const float l_b = std::pow(l_dot, f_alphaT * 0.5f);
    return l_b + f_t;
  };
  const float l_t0 = 0.0f;
  const float l_t1 = l_getT(f_cp[0u], f_cp[1u], l_t0, f_alpha);
  const float l_t2 = l_getT(f_cp[1u], f_cp[2u], l_t1, f_alpha);
  const float l_t3 = l_getT(f_cp[2u], f_cp[3u], l_t2, f_alpha);
  return {{ l_t0, l_t1, l_t2, l_t3 }};
}


///
/// @brief Catmull-Rom spline interpolation
///
/// Computes an interpolated spline point based on a curve defined by four control points p0, p1, p2, p3
/// lying on the curve which is going through the points p1 and p2 (f_ts = 0.0 -> f_ts = 1.0)
///
/// @param f_cp Array of four control points
/// @param f_t Corresponding array of knot sequence
/// @param f_ts Current knot paramter (0.0 - 1.0)
///
template <class ControlPointIterator>
osg::Vec2f interpolateCatmullRom(
  ControlPointIterator f_cp,
  const KnotSequence& f_ks,
  float f_t)
{
  f_t = lerp(f_ks[1u], f_ks[2u], f_t);
  const osg::Vec2f l_a1 = f_cp[0u] * (f_ks[1u] - f_t) / (f_ks[1u] - f_ks[0u]) + f_cp[1u] * (f_t - f_ks[0u]) / (f_ks[1u] - f_ks[0u]);
  const osg::Vec2f l_a2 = f_cp[1u] * (f_ks[2u] - f_t) / (f_ks[2u] - f_ks[1u]) + f_cp[2u] * (f_t - f_ks[1u]) / (f_ks[2u] - f_ks[1u]);
  const osg::Vec2f l_a3 = f_cp[2u] * (f_ks[3u] - f_t) / (f_ks[3u] - f_ks[2u]) + f_cp[3u] * (f_t - f_ks[2u]) / (f_ks[3u] - f_ks[2u]);
  const osg::Vec2f l_b1 = l_a1 * (f_ks[2u] - f_t) / (f_ks[2u] - f_ks[0u]) + l_a2 * (f_t - f_ks[0u]) / (f_ks[2u] - f_ks[0u]);
  const osg::Vec2f l_b2 = l_a2 * (f_ks[3u] - f_t) / (f_ks[3u] - f_ks[1u]) + l_a3 * (f_t - f_ks[1u]) / (f_ks[3u] - f_ks[1u]);
  return l_b1 * (f_ks[2u] - f_t) / (f_ks[2u] - f_ks[1u]) + l_b2 * (f_t - f_ks[1u]) / (f_ks[2u] - f_ks[1u]);
}


template <class ControlPointArray, class OutputIterator>
OutputIterator computeSpline(
  const ControlPointArray& f_controlPoints,
  OutputIterator f_destination,
  std::size_t f_numPoints,
  float f_alpha = 0.5f)
{
  const std::size_t l_numControlPoints = f_controlPoints.size();
  if ((l_numControlPoints < 4u) || (f_numPoints < 2u))
  {
    return f_destination; // we need at least four control points and a minimum of two points to generated
  }
  const std::size_t l_numIterations = l_numControlPoints - 3u;
  const float l_tDiff = static_cast<float>(l_numIterations) / (static_cast<float> (f_numPoints) - 1.0f);
  std::size_t l_segment = std::numeric_limits<std::size_t>::max();
  KnotSequence l_knots = {0.0f};
  for (std::size_t i = 0u; i < f_numPoints - 1u; ++i)
  {
    const float l_t = static_cast<float>(i) * l_tDiff; // global parameter t among all curve segments 
    const std::size_t l_s = static_cast<std::size_t> (l_t); // truncate to get int part only which indicates on which segment we are
    if (l_s != l_segment)
    {
      // update knot sequence if a new segment has been reached
      l_segment = l_s;
      l_knots = computeKnotSequence(std::next(f_controlPoints.begin(), l_segment), f_alpha); // PRQA S 3000
    }
    (*f_destination) = interpolateCatmullRom(
      std::next(f_controlPoints.begin(), l_segment), // PRQA S 3000
      l_knots,
      l_t - static_cast<float> (l_s)); // localized parameter t
    std::advance(f_destination, 1);
  }
  // add last point which is equal to p2 of last segment (interpolation is going through p1 and p2, with t=1)
  (*f_destination) = f_controlPoints[l_segment+2u];
  return std::next(f_destination);
}


template <class VecType>
VecType computeOrthogonal(const VecType& f_p0, const VecType& f_p1)
{
  VecType l_s = f_p1 - f_p0;
  l_s = VecType(-l_s.y(), l_s.x()); // rotate by 90 degree
  l_s.normalize(); // PRQA S 3803
  return l_s;
}


#ifdef SVG_WRITER_DEBUG

///
/// SvgWriter
///
class SvgWriter
{
public:

  SvgWriter(std::ostream& f_os, float f_viewBoxMinX, float f_viewBoxMinY, float f_viewBoxMaxX, float f_viewBoxMaxY);
  SvgWriter(std::ostream& f_os, const osg::Vec2f& f_viewBoxMin, const osg::Vec2f& f_viewBoxMax);

  ~SvgWriter();

  template <class Point>
  void addCircle(const Point& f_point, float f_radius);

  template <class PointArray>
  void addCircles(const PointArray& f_points, float f_radius);

  template <class PointArray>
  void addPolygon(const PointArray& f_points);

  template <class PointIterator>
  void addPolygon(PointIterator f_begin, PointIterator f_end);

  template <class PointArray>
  void addPolyline(const PointArray& f_points);

  template <class PointIterator>
  void addPolyline(PointIterator f_begin, PointIterator f_end);

private:

  std::ostream& m_os;
};


template <class Point>
void SvgWriter::addCircle(const Point& f_point, float f_radius)
{
  m_os << "  <circle cx=\"" << f_point.x() << "\" cy=\"" << f_point.y() << "\" r=\"" << f_radius << "\" fill=\"red\" />" << std::endl;
}


template <class PointArray>
void SvgWriter::addCircles(const PointArray& f_points, float f_radius)
{
  for (const auto& l_p : f_points)
  {
    addCircle(l_p, f_radius);
  }
}


template <class PointArray>
void SvgWriter::addPolygon(const PointArray& f_points)
{
  addPolygon(f_points.begin(), f_points.end());
}


template <class PointIterator>
void SvgWriter::addPolygon(PointIterator f_begin, PointIterator f_end)
{
  m_os << "  <polygon stroke=\"black\" fill=\"none\" points=\"";
  for (PointIterator l_itr = f_begin; l_itr != f_end; ++l_itr)
  {
    m_os << l_itr->x() << "," << l_itr->y() << " ";
  }
  m_os << "\"/>" << std::endl;
}


template <class PointArray>
void SvgWriter::addPolyline(const PointArray& f_points)
{
  addPolyline(f_points.begin(), f_points.end());
}


template <class PointIterator>
void SvgWriter::addPolyline(PointIterator f_begin, PointIterator f_end)
{
  m_os << "  <polyline stroke=\"orange\" fill=\"none\" points=\"";
  for (auto l_itr = f_begin; l_itr != f_end; ++l_itr)
  {
    m_os << l_itr->x() << "," << l_itr->y() << " ";
  }
  m_os << "\"/>" << std::endl;
}



///
 /// SvgWriter
 ///
 SvgWriter::SvgWriter(std::ostream& f_os, float f_viewBoxMinX, float f_viewBoxMinY, float f_viewBoxMaxX, float f_viewBoxMaxY)
   : SvgWriter(f_os, osg::Vec2f(f_viewBoxMinX, f_viewBoxMinY), osg::Vec2f(f_viewBoxMaxX, f_viewBoxMaxY))
 {
 }
 SvgWriter::SvgWriter(std::ostream& f_os, const osg::Vec2f& f_viewBoxMin, const osg::Vec2f& f_viewBoxMax)
   : m_os(f_os)
 {
   m_os << "<svg" <<
     " viewBox=\"" << f_viewBoxMin.x() << " " << f_viewBoxMin.y() << " " << f_viewBoxMax.x() << " " << f_viewBoxMax.y() << "\"" <<
     //width=\"" << f_size.x() << "\" height=\"" << f_size.y() << "\"" <<
     " xmlns=\"http://www.w3.org/2000/svg\">" << std::endl;
 }
 SvgWriter::~SvgWriter()
 {
   // close SVG root node
   m_os << "</svg>" << std::endl;
 }
 
#endif

} // namespace tileoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TILEOVERLAY_SPLINEHELPER_H