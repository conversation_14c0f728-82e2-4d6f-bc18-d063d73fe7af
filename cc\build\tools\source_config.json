{"SYNC_TYPE": "MONITOR", "QAC_BIN_PATH": "/opt/Perforce/Helix-QAC-2023.4/common/bin", "QAC_CONFIG_PATH": "cc/tools/sca_tools_package/sca_tools/cfg", "SYNC_BUILD_COMMAND": "'./svs_setup.sh megak1a'", "VCF_FILE": "prqa_ccda_config_git_1.0.xml", "HELPER_LOGS_PATH": "saap/build_out/sca_tools/logs", "HELP_PAGES_ROOT_DIR": "cc/tools/sca_tools_package/res/qac/custom_help", "SKIP_EXIT_ON_ANALYSIS_RETURN_CODES": [2, 3, 9, 0], "QAC_SYNC_PATH_BLACKLIST": ["hw", "pc/generic", "pc/svs", "cc/cpc", "cc/pmasip", "cc/vhm", "cc/viper", "tmp", "cc/sm/viewmode/src/ViewModexViewStateMachine_R2015b.cpp", "cc/assets/streetoverlay", "cc/assets/virtualreality", "cc/assets/parkingspace", "cc/assets/parkingspots"], "QAC_ANALYSIS_PATH_BLACKLIST": ["hw", "pc/generic", "pc/svs", "cc/cpc", "cc/pmasip", "cc/vhm", "cc/viper", "tmp", "cc/sm/viewmode/src/ViewModexViewStateMachine_R2015b.cpp", "cc/assets/streetoverlay", "cc/assets/virtualreality", "cc/assets/parkingspace", "cc/assets/parkingspots"], "ANALYZE_PARAMS": "--file-based-analysis --force-complete -I --assemble-support-analytics --generate-preprocessed-source", "QACPP": {"SYNC_TYPE_JSON_PATH_PATTERN_WHITELIST": "\\.cpp$", "HELPER_SUPPRESS_C_HEADER": "yes", "COMPILER_LIST": ["QNX_ARM_83_c++14.cct"], "QAC_MODULES": ["qacpp-6.4.0"], "ACF_FILE": "helix2023.2_CPP_2.6.1_parking.acf", "RCF_FILE": ["helix2023.2_CPP_2.6.1_parking.rcf"], "USER_MESSAGES": ["helix2023.2_CPP_2.6.1_parking_messages.xml"]}}