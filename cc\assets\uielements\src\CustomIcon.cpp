//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomIcon.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/CustomIcon.h"
#include "cc/assets/uielements/inc/Utils.h"
#include "vfc/core/vfc_types.hpp"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "cc/util/logging/inc/LoggingContexts.h"
#include "osg/ColorMask"
#include "osg/BlendFunc" // PRQA S 1060
using pc::util::logging::g_OSGContext;
using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

//!
//! CustomIcon
//!
CustomIcon::CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen)
: CustomIcon{f_filename, f_isLeft, f_isParkSpace, f_isParkUI, f_isHoriScreen, AnimationStyle::NONE_EFFECT, AnimationDir::START_FROM_TOP}
{
}

CustomIcon::CustomIcon(const std::string& f_filename, bool f_isLeft, bool f_isParkSpace, bool f_isParkUI, bool f_isHoriScreen,
                       const AnimationStyle f_animationStyle, const AnimationDir f_animationDir)
: Icon::Icon{f_filename, false}
, m_rotAngle{0.f}
, m_isLeft{f_isLeft}
, m_isParkSpace{f_isParkSpace}
, m_isParkUI{f_isParkUI}
, m_isHoriScreen{f_isHoriScreen}
, m_animationStyle{f_animationStyle}
, m_animationDir{f_animationDir}
, m_iconSize{}
{
  const auto texture = Icon::getTexture();
  if (texture == nullptr)
  {
    XLOG_WARN(g_AppContext, "CustomIcon: " << f_filename << " not found!");
    return;
  }
  osg::Vec2i l_textureSize(texture->getTextureWidth(), texture->getTextureHeight());
  if ((0 == l_textureSize.x()) || (0 == l_textureSize.y()))
  {
    //! Texture size is only available after the texture has been applied for the first time,
    //! so check underlying image size in case of invalid texture size
    const osg::Image* const l_image = texture->getImage();
    // assert(l_image);
    l_textureSize.x() = l_image->s();
    l_textureSize.y() = l_image->t();
  }
  m_iconSize = osg::Vec2f{static_cast<vfc::float32_t>(l_textureSize.x()), static_cast<vfc::float32_t>(l_textureSize.y())};
  setSize(m_iconSize, pc::assets::Icon::UnitType::Pixel);
  if (m_animationStyle == AnimationStyle::AUGMENTED_WAVE_EFFECT)
  {
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_iconAnimationShader("iconAnimationAugmentedWave");
    osg::Uniform* const l_augmentedWaveUniform = l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL);
    osg::Uniform* const l_alphaUniform = l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
    l_augmentedWaveUniform->set(false); // PRQA S 3803
    l_alphaUniform->set(1.0f);  // PRQA S 3803
    l_iconAnimationShader.apply(l_stateSet);  // PRQA S 3803
  }
  else if (m_animationStyle == AnimationStyle::FADEIN_FADEOUT_EFFECT)
  {
    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    pc::core::TextureShaderProgramDescriptor l_iconAnimationShader("iconAnimationFadeInFadeOut");
    osg::Uniform* const l_fadingUniform = l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL);
    osg::Uniform* const l_alphaUniform = l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT);
    l_fadingUniform->set(false); // PRQA S 3803
    l_alphaUniform->set(1.0f);  // PRQA S 3803
    l_iconAnimationShader.apply(l_stateSet);  // PRQA S 3803
  }
  else if (m_animationStyle == AnimationStyle::ALPHA_MASK)
  {
    constexpr char * const l_vert =
      "attribute vec4 osg_Vertex; \n"
      "attribute vec2 osg_MultiTexCoord0; \n"
      "uniform mat4 osg_ModelViewProjectionMatrix; \n"
      "varying vec2 v_texCoord; \n"
      "\n"
      "void main()\n"
      "{\n"
      "  v_texCoord = osg_MultiTexCoord0;\n"
      "  gl_Position = osg_ModelViewProjectionMatrix * osg_Vertex;\n"
      "}\n";

    constexpr char * const l_frag =
      "precision highp float; \n"
      "varying vec2 v_texCoord; \n"
      "uniform sampler2D u_tex0; \n"
      " \n"
      "void main() \n"
      "{ \n"
      "  //gl_FragColor = texture2D(u_tex0, v_texCoord); \n"
      "  vec4 basecolor = texture2D(u_tex0, v_texCoord); \n"
      "  if(basecolor.a > 0.01){discard;}else{gl_FragColor = basecolor;} \n"
      "} \n";


    const osg::ref_ptr<osg::Program> l_roundCornerProgram = new osg::Program;
    const osg::ref_ptr<osg::Shader>  l_roundCornerVertexShader = new osg::Shader(osg::Shader::Type::VERTEX, l_vert);
    const osg::ref_ptr<osg::Shader>  l_roundCornerFragmentShader = new osg::Shader(osg::Shader::Type::FRAGMENT, l_frag);
    l_roundCornerProgram->addShader(l_roundCornerVertexShader); // PRQA S 3803
    l_roundCornerProgram->addShader(l_roundCornerFragmentShader); // PRQA S 3803

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    // osg::ref_ptr<osg::ColorMask> m_RounderCornerTexColorMask = new osg::ColorMask(false, false, false, true);
    // l_stateSet->setAttribute(m_RounderCornerTexColorMask);
    // l_stateSet->setBinNumber(1000);
    // l_stateSet->setRenderingHint(osg::StateSet::RenderingHint::TRANSPARENT_BIN);
    // //pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
    // //l_basicTexShader.apply(l_stateSet);  // PRQA S 3803

    // osg::ref_ptr<osg::BlendFunc> l_BlendFunc = new osg::BlendFunc;
    // l_BlendFunc->setFunction(GL_SRC_ALPHA, GL_ONE_MINUS_SRC_ALPHA);
    // l_stateSet->setAttribute(l_BlendFunc.get());
    // l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::OFF);
    l_stateSet->setAttribute(l_roundCornerProgram.get(), osg::StateAttribute::ON);
  }
  else{}

}

void CustomIcon::setTexture(osg::Texture2D* f_texture)
{
  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setTextureAttribute(0u, f_texture);
}

void CustomIcon::setRotateAngle(const vfc::float32_t& f_rotAngle)
{
  m_rotAngle = f_rotAngle;
  // magic to set dirty
  osg::Vec2f originalPos;
  pc::assets::Icon::UnitType originalUnitType{pc::assets::Icon::UnitType::Pixel};
  pc::assets::Icon::getPosition(originalPos, originalUnitType);
  pc::assets::Icon::setPosition(originalPos * 0.5f + osg::Vec2f{1.0f, 1.0f}, originalUnitType);
  pc::assets::Icon::setPosition(originalPos, originalUnitType);
}

void CustomIcon::setAnimation(const AnimationStyle f_animationStyle)
{
  m_animationStyle = f_animationStyle;
}

CustomIcon::AnimationStyle CustomIcon::getAnimation() // PRQA S 4211
{
  return m_animationStyle;
}

void CustomIcon::updateGeometry(
    osg::Geometry* f_geometry,
    const osg::Vec2f& f_origin,
    const osg::Vec2f& f_size,
    vfc::float32_t f_left,
    vfc::float32_t f_bottom,
    vfc::float32_t f_right,
    vfc::float32_t f_top) const
{
  if (f_geometry == nullptr)
  {
      return;
  }
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (f_geometry->getVertexArray()); // PRQA S 3076
  if (m_isParkSpace)  // for parking space
  {
    if (m_isLeft)
    {
      if (false == m_isHoriScreen)  // left && vertical screen
      {
        (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle), 0.0f);
        (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle) - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle) - f_size.y() * std::cos(m_rotAngle), 0.0f);
        (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
        (*l_vertices)[3u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle), 0.0f);
      }
      else  // left && horizontal screen
      {
        (*l_vertices)[0u] = osg::Vec3f(f_origin, 0.0f);
        (*l_vertices)[1u] = osg::Vec3f(f_origin.x() - f_size.x() * std::cos(m_rotAngle), f_origin.y() + f_size.x() * std::sin(m_rotAngle), 0.0f);
        (*l_vertices)[2u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle), 0.0f);
        (*l_vertices)[3u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle) - f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle) + f_size.x() * std::sin(m_rotAngle), 0.0f);
      }
    }
    else if (false == m_isHoriScreen)  // right && vertical screen
    {
      (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle),                                     f_origin.y() - f_size.x() * std::sin(m_rotAngle), 0.0f);
      (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle) + f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle) + f_size.y() * std::cos(m_rotAngle), 0.0f);
      (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
      (*l_vertices)[3u] = osg::Vec3f(f_origin.x() + f_size.y() * std::sin(m_rotAngle),                                     f_origin.y() + f_size.y() * std::cos(m_rotAngle), 0.0f);
    }
    else  // right && horizontal screen
    {
      (*l_vertices)[0u] = osg::Vec3f(f_origin, 0.0f);
      (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.x() * std::sin(m_rotAngle), 0.0f);
      (*l_vertices)[2u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle), 0.0f);
      (*l_vertices)[3u] = osg::Vec3f(f_origin.x() - f_size.y() * std::sin(m_rotAngle) + f_size.x() * std::cos(m_rotAngle), f_origin.y() - f_size.y() * std::cos(m_rotAngle) - f_size.x() * std::sin(m_rotAngle), 0.0f);
    }
  }
  else if (m_isParkUI && (false == m_isHoriScreen))  // vertical layout , and rotate 90degree
  {
    //        3 (d)    1 (b)
    //        -------------
    //        |   size.y  |
    //        |           |
    //        |           |    size.x
    //        |           |
    //        |           |
    //        |           |
    //        -------------
    //        2 (c)     0 (a)

    // if pic size is already rotated, take below calculation
    (*l_vertices)[0u] = osg::Vec3f(f_origin.x() + f_size.x(), f_origin.y(), 0.0f);
    (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x(), f_origin.y() + f_size.y(), 0.0f);
    (*l_vertices)[2u] = osg::Vec3f(f_origin, 0.0f);
    (*l_vertices)[3u] = osg::Vec3f(f_origin.x(), f_origin.y() + f_size.y(), 0.0f);
  }
  else  // default, no rotate
  {
    //        2 (c)            3 (d)
    //        ----------------------
    //        |       size.x       |
    //        |                    |   size.y
    //        |                    |
    //        ----------------------
    //        0 (a)            1 (b)
    (*l_vertices)[0u] = osg::Vec3f(f_origin, 0.0f);
    (*l_vertices)[1u] = osg::Vec3f(f_origin.x() + f_size.x(), f_origin.y(), 0.0f);
    (*l_vertices)[2u] = osg::Vec3f(f_origin.x(), f_origin.y() + f_size.y(), 0.0f);
    (*l_vertices)[3u] = osg::Vec3f(f_origin + f_size, 0.0f);
  }
  l_vertices->dirty();

  osg::Vec2Array* const l_texCoords = static_cast<osg::Vec2Array*> (f_geometry->getTexCoordArray(0u)); // PRQA S 3076
  (*l_texCoords)[0u] = osg::Vec2f(f_left, f_bottom);
  (*l_texCoords)[1u] = osg::Vec2f(f_right, f_bottom);
  (*l_texCoords)[2u] = osg::Vec2f(f_left, f_top);
  (*l_texCoords)[3u] = osg::Vec2f(f_right, f_top);
  l_texCoords->dirty();

  f_geometry->dirtyBound();
}

void CustomIcon::updateShaderUniform()
{

  if (this->getAnimation() == cc::assets::uielements::CustomIcon::AnimationStyle::AUGMENTED_WAVE_EFFECT)
  {

    // we need to use the static member because the icon is created every frame in parking space
    static vfc::float32_t g_alpha = 1.f;
    static AnimationDir g_animationDir = AnimationDir::START_FROM_TOP;

    if (g_animationDir != m_animationDir)
    {
      if (m_animationDir == AnimationDir::START_FROM_TOP)
      {
          g_alpha = 1.f;
      }
      else if (m_animationDir == AnimationDir::START_FROM_BOTTOM)
      {
         g_alpha = 0.f;
      }
      else
      {
        // do nothing, unreachable case
      }

      g_animationDir = m_animationDir;
    }

    if (m_animationDir == AnimationDir::START_FROM_TOP)
    {
      if( g_alpha < 0.f )
      {
        g_alpha = 1.f;
      }
      else
      {
        g_alpha = g_alpha - 0.03f;
      }
    }
    else if (m_animationDir == AnimationDir::START_FROM_BOTTOM)
    {
      if( g_alpha > 1.f )
      {
        g_alpha = 0.f;
      }
      else
      {
        g_alpha = g_alpha + 0.03f;
      }
    }
    else
    {
      // do nothing, unreachable case
    }

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL)->set(true); // PRQA S 3803
    l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL)->set(false); // PRQA S 3803
    l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT)->set(g_alpha); // PRQA S 3803
  }
  else if (this->getAnimation() == cc::assets::uielements::CustomIcon::AnimationStyle::FADEIN_FADEOUT_EFFECT)
  {
    // we need to use the static member because the icon is created every frame in parking space
    static vfc::uint32_t g_animationCounter = 0u;

    const vfc::float32_t l_alpha = std::abs(std::cos(osg::DegreesToRadians(static_cast<vfc::float32_t>(g_animationCounter))));

    g_animationCounter = g_animationCounter + 3u;

    osg::StateSet* const l_stateSet = getOrCreateStateSet();
    l_stateSet->getOrCreateUniform("isAugmentedWave", osg::Uniform::BOOL)->set(false); // PRQA S 3803
    l_stateSet->getOrCreateUniform("isFadeInOut", osg::Uniform::BOOL)->set(true); // PRQA S 3803
    l_stateSet->getOrCreateUniform("alpha", osg::Uniform::FLOAT)->set(l_alpha); // PRQA S 3803
  }
  else
  {

  }
}


} // namespace uielements
} // namespace assets
} // namespace cc // PRQA S 1041
