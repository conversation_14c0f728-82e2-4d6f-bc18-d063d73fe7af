//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/ParkingCallback.h"
#include "cc/assets/trajectory/inc/ActionPoint.h"
#include "cc/assets/trajectory/inc/WheelTrack.h" // PRQA S 1060
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"


namespace cc
{
namespace assets
{
namespace trajectory
{


ParkingCallback::ParkingCallback(AssetType f_assetType, pc::core::Framework* f_framework)
  : m_assetType{f_assetType}
  , m_framework{f_framework}
{
}


ParkingCallback::~ParkingCallback() = default;


void ParkingCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if ((f_node == nullptr) || (f_nv == nullptr))
  {
      return;
  }
  if (osg::NodeVisitor::CULL_VISITOR == f_nv->getVisitorType())
  {
    cc::core::CustomFramework* const l_framework = m_framework->asCustomFramework();
    // PMA operating mode
    bool l_automaticParking = false;
    if (l_framework->m_parkHmiParkingStatusReceiver.isConnected())
    {
      const cc::daddy::ParkStatusDaddy_t* const l_pData = l_framework->m_parkHmiParkingStatusReceiver.getData();
      if (nullptr != l_pData)
      {
        if (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_pData->m_Data || cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_pData->m_Data)  // MANOEUVERING
        {
          l_automaticParking = true;
        }
        else
        {
          l_automaticParking = false;
        }
      }
    }

    if (l_automaticParking) // If it is automatic parking
    {
      switch (m_assetType) // PRQA S 4018
      {
      case ACTIONPOINT:
      case WHEELTRACK_AUTO:
      case WHEELTRACK_ALWAYS_VISIBLE:
      {
        traverse(f_node, f_nv);
        break;
      }
      default:
      {// Do not traverse
        break;
      }
      }
    }
    else // If it is manual parking
    {
      switch (m_assetType) // PRQA S 4018
      {
        case WHEELTRACK_MANUAL:
        case WHEELTRACK_ALWAYS_VISIBLE:
        {
          traverse(f_node, f_nv);
          break;
          }
        default:
        {// Do not traverse
          break;
        }
      }
    }
  }
  else
  {
    traverse(f_node, f_nv);
  }
}


} // namespace trajectory
} // namespace assets
} // namespace cc
