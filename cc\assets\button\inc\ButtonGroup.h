//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_BUTTON_UTIL_ASSET_MANAGER_H
#define CC_ASSETS_BUTTON_UTIL_ASSET_MANAGER_H

#include "pc/svs/core/inc/Asset.h"
#include "cc/assets/button/inc/Button.h"

namespace pc
{
namespace core
{
class View;
class Framework;
} // namespace core

} // namespace pc

namespace cc
{
namespace assets
{
namespace button
{

class Button;
class ButtonSettings;

class ButtonGroup : public pc::core::Asset
{
public:
    ButtonGroup(cc::core::AssetId f_assetId);

    void addButton(cc::assets::button::Button* f_asset);

    void addButtonGroup(cc::assets::button::ButtonGroup* f_asset);

    void traverse(osg::NodeVisitor& f_nv) override;

    void setHoriReferenceView(pc::core::View* f_view);

    void setVertReferenceView(pc::core::View* f_view);

    virtual bool isTouchInsideButtonGroup(osg::Vec2f f_touch);

protected:
    virtual void update() = 0;

    bool m_enabled = true;
};

} // namespace button
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BUTTON_UTIL_ASSET_MANAGER_H
