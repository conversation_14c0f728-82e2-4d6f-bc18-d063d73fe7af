//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CXH5SZH Chen Xiangqin (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  TextSymbols.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/TextSymbols.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class TextSymbolIconType : vfc::uint32_t
{
  WARNSYMBOL_TEXT_HORI,
  WARNSYMBOL_TEXT_VERT,
  PARKING_GUIDANCE_CONTINUE_DRIVE_DISTANCE,
  PARKING_GUIDANCE_MOVES_LEFT_NUMBER,
  PARKING_GUIDANCE_GEAR_D,
  PARKING_GUIDANCE_GEAR_N,
  PARKING_GUIDANCE_GEAR_R,
  PARKING_GUIDANCE_GEAR_P
};

//!
//! @brief Construct a new WarnSymbols Manager:: WarnSymbols Manager object
//!
//! @param f_config
//!
TextSymbolManager::TextSymbolManager()
  : m_lastConfigUpdate{~0u}
  , m_textIcons{}
  , m_mat_b{false}
{
}



TextSymbolManager::~TextSymbolManager() = default;

void TextSymbolManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init WarnSymbols icons
  m_textIcons.clear(f_imageOverlays);

  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathTextBoxSafeNotification,      getSurroundViewPosition(g_uiSettings->m_settingTextBoxSafeNotification.m_iconCenter),              getSurroundViewIconSize(g_uiSettings->m_settingTextBoxSafeNotification.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathTextBoxSafeNotification_vert, getSurroundViewPosition(g_uiSettings->m_settingTextBoxSafeNotification_vert.m_iconCenter),         getSurroundViewIconSize(g_uiSettings->m_settingTextBoxSafeNotification_vert.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathContinueDrivingText,          getSurroundViewPosition(g_uiSettings->m_settingParkingUIContinueDrivingTextMainView.m_iconCenter), getSurroundViewIconSize(g_uiSettings->m_settingParkingUIContinueDrivingTextMainView.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathMovesLeftNumberText,          getSurroundViewPosition(g_uiSettings->m_settingParkingUIMovesLeftNumberTextMainView.m_iconCenter), getSurroundViewIconSize(g_uiSettings->m_settingParkingUIMovesLeftNumberTextMainView.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathGuidanceGearD,                getSurroundViewPosition(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconCenter),      getSurroundViewIconSize(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathGuidanceGearN,                getSurroundViewPosition(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconCenter),      getSurroundViewIconSize(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathGuidanceGearR,                getSurroundViewPosition(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconCenter),      getSurroundViewIconSize(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconSize)));
  m_textIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathGuidanceGearP,                getSurroundViewPosition(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconCenter),      getSurroundViewIconSize(g_uiSettings->m_settingParkingUIGearIcon_MainView_Hori.m_iconSize)));
}


void TextSymbolManager::update(pc::assets::ImageOverlays* f_imageOverlays, core::CustomFramework* f_framework)
{
  if ((f_imageOverlays == nullptr) || (f_framework == nullptr))
  {
      return;
  }
  // ! check if config has changed
  if (g_uiSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_uiSettings->getModifiedCount();
  }

  m_textIcons.setAllEnabled(false);

  cc::target::common::EThemeTypeHU    l_theme             = cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI;
  bool            l_dynamicGearStatus = false;
  cc::target::common::EParkngTypeSeld l_curParkngTypeSeld = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKStatusR2L  l_curparkStatus     = cc::target::common::EPARKStatusR2L::PARK_Off;
  bool            l_dynamicGearMainViewStatus = false;

  if (f_framework->m_SVSRotateStatusDaddy_Receiver.hasData())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType = f_framework->m_SVSRotateStatusDaddy_Receiver.getData();
    l_theme = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899
  }

  if (f_framework->m_dynamicGearStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearStatusPtr = f_framework->m_dynamicGearStatus_ReceiverPort.getData();
    l_dynamicGearStatus = l_dynamicGearStatusPtr->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI == l_theme)
  {
    m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_HORI))->setEnabled(true);
    m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT))->setEnabled(false);
    // always show remaining distance, gear, remaining move number at guidance
    if ((cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curparkStatus) || (cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_curparkStatus))
    {
      m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::PARKING_GUIDANCE_CONTINUE_DRIVE_DISTANCE))->setEnabled(true);
      this->displayGear(f_framework);
      if (cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
      {
        m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::PARKING_GUIDANCE_MOVES_LEFT_NUMBER))->setEnabled(true);
      }
      l_dynamicGearMainViewStatus = true;
    }

    if (cc::daddy::CustomDaddyPorts::sm_dynamicGearMainViewStatus_SenderPort.isConnected())
    {
      cc::daddy::DynamicGearActive_t& l_container = cc::daddy::CustomDaddyPorts::sm_dynamicGearMainViewStatus_SenderPort.reserve();
      l_container.m_Data = l_dynamicGearMainViewStatus;
      cc::daddy::CustomDaddyPorts::sm_dynamicGearMainViewStatus_SenderPort.deliver();
    }
  }
  else
  {
    m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_HORI))->setEnabled(false);
    m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::WARNSYMBOL_TEXT_VERT))->setEnabled(true);
  }

}


void TextSymbolManager::displayGear(const core::CustomFramework* f_framework)
{
  if (f_framework == nullptr)
  {
      return;
  }
  if (f_framework->m_gearReceiver.hasData())
  {
    const pc::daddy::GearDaddy* const l_parkGear = f_framework->m_gearReceiver.getData();
    const EGear l_curparkGear = static_cast<EGear>(l_parkGear->m_Data);
    switch (static_cast<vfc::int32_t>(l_curparkGear))
    {
    case pc::daddy::GEAR_D:
    {
      m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::PARKING_GUIDANCE_GEAR_D))->setEnabled(true);
      break;
    }
    case pc::daddy::GEAR_N:
    {
      m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::PARKING_GUIDANCE_GEAR_N))->setEnabled(true);
      break;
    }
    case pc::daddy::GEAR_R:
    {
      m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::PARKING_GUIDANCE_GEAR_R))->setEnabled(true);
      break;
    }
    case pc::daddy::GEAR_P:
    {
      m_textIcons.getIcon(static_cast<unsigned>(TextSymbolIconType::PARKING_GUIDANCE_GEAR_P))->setEnabled(true);
      break;
    }
    default:
    {
      break;
    }
    }
  }
}


//!
//! @brief Construct a new TextSymbols:: TextSymbols object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
TextSymbols::TextSymbols(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, nullptr}  // PRQA S 2966 
  , m_customFramework{f_customFramework}
  , m_manager{}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);

  //! render order
  //! Vehicle imposter is 0. Warning symbols are over vehicle, so this render order shall be > 0.
  constexpr vfc::uint32_t l_renderOrder = 10u;
  cc::assets::uielements::CustomImageOverlays::CustomSetRenderOrder(l_renderOrder, true);


}


TextSymbols::~TextSymbols() = default;


void TextSymbols::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc

