
//-------------------------------------------------------------------------------
// Copyright (c) 2018 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_DEBUGOVERLAY
#define CC_ASSETS_DEBUGOVERLAY

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"  //callback sensors distances
#include "pc/svs/vehicle/inc/Ultrasonic.h"

#include <osg/ref_ptr>
//#include <osg/Texture2D>
#include <osg/Geometry>


#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "cc/core/inc/CustomFramework.h"      //callback sensors distances
#include "cc/assets/trajectory/inc/MainLogic.h"

namespace cc
{
namespace assets
{
namespace debugoverlay
{

// ----------------------------------------------- ULTRASONIC

class UssDebugOverlay : public osg::Group
{
public:

    UssDebugOverlay(
        cc::core::CustomFramework* f_pCustomFramework,
        pc::vehicle::AbstractZoneLayout* f_zoneLayout,
        bool f_showDistanceToOutline = false);

  virtual void traverse(osg::NodeVisitor& f_nv) override;

private:
  bool m_showDistanceToOutline;
  osg::Geode* getOrCreateDistancesGeode();
  osg::Geode* getOrCreateDistancesToOutlineGeode();
  void setDistance(osg::Vec3Array* f_vertices, unsigned int f_zoneIdx, float f_distance, float f_zCoord = 0.05f) const;
  osg::Geometry* createZone(unsigned int f_zoneIdx, bool f_outline, const osg::Vec4f& f_color, float f_posZ);

  cc::core::CustomFramework*                      m_pCustomFramework;
  osg::ref_ptr<osg::Geode>                        m_ultrasonicGeode;
  osg::ref_ptr<pc::vehicle::AbstractZoneLayout>   m_ussLayout;
  osg::ref_ptr<osg::Geode>                        m_distancesGeode;
  osg::ref_ptr<osg::Geode>                        m_distancesToOutlineGeode;
  unsigned int                                    m_lastUpdateUss;

};



// ------------------------------------------- VEHICLE CONTOUR
class VehicleContourDebugOverlay : public osg::Group
{
public:

  VehicleContourDebugOverlay(const cc::assets::trajectory::mainlogic::Inputs_st& fc_input);
  osg::Vec2f getWidestPointAtVehicleContour(bool f_withMirrors);
private:
  void createVehicleContourLayer();
  //osg::Geometry* createOverlayFromPoints(const osg::Vec2Array* cf_points, EDebugLayer f_layer, bool f_mirrorPoints, bool f_isLoop = false);

  const cc::assets::trajectory::mainlogic::Inputs_st&   m_inputData;
  osg::ref_ptr<osg::Geode>                              m_vehicleContourGeode;
  osg::ref_ptr<osg::Geometry>                           m_vehicleContourGeometry;
};


// ------------------------------------------- VEHICLE SIZE
class VehicleSizeDebugOverlay : public osg::Group
{
public:

  VehicleSizeDebugOverlay(float f_initialDistance);
  void rebuildPlanes();
  void deletePlanes();

  float    m_parallelPlanesCurrentDist;   // meters

private:
  osg::Geometry* createParallelPlane(float f_distance, float f_height = 2.0f, float f_length = 5.0f);
  osg::Geometry* createParallelPlaneTopView(float f_distance, float f_width = 0.02f, float f_length = 5.1f);

  osg::ref_ptr<osg::Geode>     m_parallelPlanesGeode;
  osg::ref_ptr<osg::Geometry>  m_parallelPlanesGeometryLeft;
  osg::ref_ptr<osg::Geometry>  m_parallelPlanesGeometryRight;
  osg::ref_ptr<osg::Geometry>  m_parallelPlanesGeometryLeft_topView;
  osg::ref_ptr<osg::Geometry>  m_parallelPlanesGeometryRight_topView;
};


// ------------------------------------------- DEBUG
class DebugOverlay : public osg::Switch
{
public:

  DebugOverlay(
    cc::core::CustomFramework* f_pCustomFramework,
    pc::vehicle::AbstractZoneLayout* f_zoneLayout,
    const cc::assets::trajectory::mainlogic::Inputs_st& fc_input,
    const osg::Vec2f& f_viewSize,
    const osg::Vec3f& f_cameraPosition,
    uint16_t f_renderBinOrder);

  virtual void traverse(osg::NodeVisitor& f_nv) override;

  enum EDebugLayer
  {
      DEBUG_LAYER_ULTRASONIC,
      DEBUG_LAYER_ULTRASONIC_DIST_TO_OUTLINE,
      DEBUG_LAYER_VECHICLE_CONTOUR,
      DEBUG_LAYER_PARALLEL_PLANES,
      DEBUG_LAYER_NUM
  };

private:

  void checkCommands();
  void debugRunCommands(const EDebugOverlayAction f_action);
  void debugOverlayActionResetAndShowAll();
  void debugOverlayActionGetCurrentDistance() const;
  void debugOverlayActionChangePlaneDistance(const float f_distance);
  void debugOverlayActionDistanceAtWidestPointMirrors(const bool f_includeMirrors);
  void showLayer(EDebugLayer f_layer);
  void hideLayer(EDebugLayer f_layer);
  void toggleLayer(EDebugLayer f_layer);

  cc::core::CustomFramework*                m_pCustomFramework;
  osg::ref_ptr<UssDebugOverlay>             m_ussLayer;
  osg::ref_ptr<UssDebugOverlay>             m_ussToOtlineLayer;
  osg::ref_ptr<VehicleContourDebugOverlay>  m_vehicleContourLayer;
  osg::ref_ptr<VehicleSizeDebugOverlay>     m_vehicleSizeLayer;
  bool                                      m_activeLayers[DEBUG_LAYER_NUM];
};


class UpdateCallback : public osg::NodeCallback
{
public:
  UpdateCallback() {};
  virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv);
};

} // namespace DebugOverlay
} // namespace assets
} // namespace cc


#endif // CC_ASSETS_DEBUGOVERLAY
