//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: JLR NFS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: VUJ1LR Vujicic Milica (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS JLR
/// @file  SeeThroughBonnet.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_STB_SEETHROUGHBONNET_H
#define CC_ASSETS_STB_SEETHROUGHBONNET_H

#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "daddy_receiverport.hpp"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/assets/trajectory/inc/MainLogic.h"


#include <osg/Geode>
#include <osg/Geometry>
#include <osg/PositionAttitudeTransform>
#include <osg/Depth>

#include <iomanip>

namespace cc
{
namespace assets
{
namespace stb
{

//!
//! Settings
//!
class Settings : public pc::util::coding::ISerializable
{
public:

  Settings()
  : m_height(0.05f)
  , m_shadowHeight(0.01f)
  , m_color(0.96f, 0.44f, 0.008f, 0.9f)
  , m_shadowColor(0.0f, 0.0f, 0.0f, 0.9f)
  , m_wheelRadius(0.4f)
  , m_wheelAlpha(0.7f)
  , m_lightPosition(3.0f, 0.0f, 1.0f)
  , m_lineWidth(0.001f)
  , m_lineGradientWidth(0.005f)
  , m_shadowWidth(0.04f)
  , m_shadowGradientWidth(0.03f)
  , m_wheelinePositionX(0.01f)
  , m_wheelinePositionY(0.01f)
  , m_wheelinePositionZ(0.01f)
  , m_wheelineWidth(0.01f)
  , m_wheelineLength(0.01f)
  , m_wheelModelFile("cc/vehicle_model/LeftWheel.osgb")
  {
  }

  SERIALIZABLE(Settings)
  {
    ADD_FLOAT_MEMBER(height);
    ADD_FLOAT_MEMBER(shadowHeight);
    ADD_MEMBER(osg::Vec4f, color);
    ADD_MEMBER(osg::Vec4f, shadowColor);
    ADD_FLOAT_MEMBER(wheelRadius);
    ADD_FLOAT_MEMBER(wheelAlpha);
    ADD_MEMBER(osg::Vec3f, lightPosition);
    ADD_FLOAT_MEMBER(lineWidth);
    ADD_FLOAT_MEMBER(lineGradientWidth);
    ADD_FLOAT_MEMBER(shadowWidth);
    ADD_FLOAT_MEMBER(shadowGradientWidth);
    ADD_FLOAT_MEMBER(wheelinePositionX);
    ADD_FLOAT_MEMBER(wheelinePositionY);
    ADD_FLOAT_MEMBER(wheelinePositionZ);
    ADD_FLOAT_MEMBER(wheelineWidth);
    ADD_FLOAT_MEMBER(wheelineLength);
    ADD_STRING_MEMBER(wheelModelFile);

  }

  const std::string asString() const
  {
    std::ostringstream l_stream;
    (void) (l_stream << std::fixed << std::setprecision(3) << "Stb Settings:"

                           << "\n    m_height        "  << m_height
                           << "\n    m_shadowHeight  "  << m_shadowHeight
                           << "\n    m_color         "  << m_color.x() << ", " << m_color.y() << ", " << m_color.z() << ", " << m_color.w()
                           << "\n    m_shadowColor   "  << m_shadowColor.x() << ", " << m_shadowColor.y() << ", " << m_shadowColor.z() << ", " << m_shadowColor.w()
                           << "\n    m_wheelRadius   "  << m_wheelRadius
                           << "\n    m_wheelAlpha    "  << m_wheelAlpha
                           << "\n    m_lightPosition "  << m_lightPosition.x() << ", " << m_lightPosition.y() << ", " << m_lightPosition.z());
    return l_stream.str();
  }

  float m_height;
  float m_shadowHeight;
  osg::Vec4f m_color;
  osg::Vec4f m_shadowColor;
  float m_wheelRadius;
  float m_wheelAlpha;
  osg::Vec3f m_lightPosition;
  float m_lineWidth;
  float m_lineGradientWidth;
  float m_shadowWidth;
  float m_shadowGradientWidth;
  float m_wheelinePositionX;
  float m_wheelinePositionY;
  float m_wheelinePositionZ;
  float m_wheelineWidth;
  float m_wheelineLength;
  std::string m_wheelModelFile;
};

enum GeometryData
{
    BUMPER_POINTS = 7, //bumper points on one side
    OUTLINE_POINTS = 2, //outline points on the side
    WHEEL_POINTS = 20 //points for the rectangle
};

enum
{
  WHEEL_SUBMODEL_COUNT = 1
  //WHEEL_SUBMODEL_COUNT = 12 // Use when generating the model from the submodels
};


//!
//! SeeThroughBonnet
//!
//======================================================
// SeeThroughBonnet
//------------------------------------------------------
/// Displays the vehicle contour and front wheels contour.
/// Creates Splines for front bumper, front wheels, and side contours of the car
/// Creates shadow-like Splines for the same elements
/// Updates the rotation of the wheel elements
/// <AUTHOR> Milica
//======================================================
class SeeThroughBonnet : public osg::Group
{
public:

  SeeThroughBonnet(cc::core::CustomFramework* f_pCustomFramework,
                   const cc::assets::trajectory::mainlogic::Inputs_st& fc_input);
  void update(const osg::NodeVisitor* f_nv);

protected:

 virtual ~SeeThroughBonnet();
  //! Copy constructor is not permitted.
  SeeThroughBonnet (const SeeThroughBonnet& other) /* = delete */;
  //! Copy assignment operator is not permitted.
  SeeThroughBonnet& operator=(const SeeThroughBonnet& other) /* = delete */;

private:

  void setStateSetToGeometry(osg::StateSet* f_stateSet, osg::Geometry* f_geometry, osg::Geode* f_geode, bool f_addAsChild = true);
  osg::Geometry* createWheelOverlays(bool f_shadow, bool f_left);
  osg::Geometry* createWheelTestOverlays();
  osg::Geometry* createOverlayFromPoints(const osg::Vec2Array* cf_points, bool f_shadow, bool f_mirrored);
  void createFrontAndSidesOverlays();
  void updateWheelSteering();
  void updateWheelRotation();
  void applyTexShaderToStateSet(osg::StateSet* f_stateSet, int f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend);
  void create1DTexture(osg::Vec4f f_color, const std::string& f_texturePath, float f_lineWidth, float f_lineGradientWidth, bool f_shadow);
  void loadTexture(const unsigned int f_texSlot, const std::string& f_texturePath, osg::StateSet* f_stateSet);

  // bool isMovementDirectionForward();


  osg::Vec2Array* smoothSpline(const osg::Vec2Array* cf_points, uint16_t f_numOfSegments= 4u);
  osg::Vec2f      getTangentDirFromSplinePoint(unsigned int f_pointIndex, const osg::Vec2Array* fc_points);
  osg::Vec2f      findIntersection(osg::Vec2Array* f_VehicleContour_Side, unsigned int f_index1, unsigned int f_index2,  float f_edgeX) const;
  unsigned int    indexBehindPosX(osg::Vec2Array* f_VehicleContour_Side, unsigned int f_from, unsigned int f_to, float f_posToCompare) const;


  cc::core::CustomFramework* m_pCustomFramework;
  const cc::assets::trajectory::mainlogic::Inputs_st    & m_inputData;

  bool m_drivingForward;
  double m_lastFrameTime;
  float m_wheelRotationAngle_rad;

  float m_steeringAngle_rad;
  float m_rotationAngle_rad;
  osg::PositionAttitudeTransform * m_leftTransform;
  osg::PositionAttitudeTransform * m_rightTransform;
  osg::PositionAttitudeTransform * m_leftWheelTransform;
  osg::PositionAttitudeTransform * m_rightWheelTransform;
  osg::PositionAttitudeTransform * m_rightWheelTreadTransform;
  osg::ref_ptr<osg::Geometry> m_lineGeom_front;
  osg::ref_ptr<osg::Geometry> m_lineGeom_leftWheel;
  osg::ref_ptr<osg::Geometry> m_lineGeom_rightWheel;
  osg::ref_ptr<osg::Geometry> m_lineGeom_leftOutline;
  osg::ref_ptr<osg::Geometry> m_lineGeom_rightOutline;

  osg::ref_ptr<osg::Geometry> m_shadowGeom_front;
  osg::ref_ptr<osg::Geometry> m_shadowGeom_leftWheel;
  osg::ref_ptr<osg::Geometry> m_shadowGeom_rightWheel;
  osg::ref_ptr<osg::Geometry> m_shadowGeom_leftOutline;
  osg::ref_ptr<osg::Geometry> m_shadowGeom_rightOutline;

  osg::ref_ptr<osg::Geode> m_lineGeode_front;
  osg::ref_ptr<osg::Geode> m_lineGeode_leftOutline;
  osg::ref_ptr<osg::Geode> m_lineGeode_rightOutline;
  osg::ref_ptr<osg::Geode> m_lineGeode_leftWheel;
  osg::ref_ptr<osg::Geode> m_lineGeode_rightWheel;
  osg::ref_ptr<osg::Geode> m_shadowGeode_front;
  osg::ref_ptr<osg::Geode> m_shadowGeode_leftWheel;
  osg::ref_ptr<osg::Geode> m_shadowGeode_rightWheel;
  osg::ref_ptr<osg::Geode> m_shadowGeode_leftOutline;
  osg::ref_ptr<osg::Geode> m_shadowGeode_rightOutline;
  osg::ref_ptr<osg::Node> m_leftWheel[WHEEL_SUBMODEL_COUNT];
  osg::ref_ptr<osg::Node> m_rightWheel[WHEEL_SUBMODEL_COUNT];
  cc::assets::trajectory::commontypes::VertexData_st m_vertexData;
  float m_lineGeometryWidth;
  float m_shadowGeometryWidth;
  const std::string m_texturePath_1;
  const std::string m_texturePath_2;

  osg::Uniform *m_pWheelAlphaUniform;
  osg::Uniform *m_pLightPositionUniform;

};

class SeeThroughBonnetCallback : public osg::NodeCallback
{
public:
  SeeThroughBonnetCallback();

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;

protected:
  virtual ~SeeThroughBonnetCallback();
  //! Copy constructor is not permitted.
  SeeThroughBonnetCallback (const SeeThroughBonnetCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  SeeThroughBonnetCallback& operator=(const SeeThroughBonnetCallback& other); // = delete
};

} // namespace stb
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_STB_SEETHROUGHBONNET_H
