//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ViewBowl.cpp
/// @brief 
//=============================================================================

#include "osg/Geode"
#include "osg/Geometry"

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/worker/bowlshaping/inc/Bowl.h"
#include "pc/svs/worker/bowlshaping/inc/BowlShaperTask.h"
#include "pc/svs/worker/bowlshaping/inc/BowlUpdateVisitor.h"
#include "pc/svs/worker/bowlshaping/inc/PolarBowlLayoutGenerator.h"
#include "pc/svs/factory/inc/SV3DCullCallback.h"
#include "pc/svs/factory/inc/SV3DUpdateCallback.h"
#include "pc/svs/factory/inc/FloorUpdateVisitor.h"

#include "pc/svs/factory/inc/Floor.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/assets/viewbowl/inc/ViewBowl.h"


namespace cc
{
namespace assets
{

pc::factory::SV3DNode * createBowl(const std::string& f_name, pc::core::Framework* f_pFramework, const pc::worker::bowlshaping::BowlShaperData &f_bowlShaper )
{
  if (f_pFramework == nullptr)
  {
    return nullptr;
  }
  //! Set layout & wall generators
  const auto l_pLayoutGenerator = new pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator(
  f_bowlShaper.m_numRadialSections,
  pc::vehicle::g_mechanicalData->getCenter(),
  f_bowlShaper.m_bowlDefault.m_semiaxis.x(),
  f_bowlShaper.m_bowlDefault.m_semiaxis.y(),
  static_cast<vfc::int32_t>(f_bowlShaper.m_bowlDefault.m_n));

  const auto l_pWallGenerator = new pc::worker::bowlshaping::ParabolicSectionBowlWallGenerator(
  f_bowlShaper.m_numHeightSections,
  f_bowlShaper.m_bowlDefault.m_height,
  f_bowlShaper.m_bowlDefault.m_depth,
  f_bowlShaper.m_bowlDefault.m_slope,
  f_bowlShaper.m_bowlDefault.m_densityShift,
  false);

  //! Update visitor
  const auto l_pBowlUpdateVisitor = new pc::worker::bowlshaping::BowlUpdateVisitor(
  f_pFramework->m_cameraCalibrationReceiver, // PRQA S 0251
  f_pFramework->m_cameraMasksReceiver,
  l_pLayoutGenerator,
  l_pWallGenerator,
  false
  );

  //! Actual Bowl SV3D Node
  const auto l_pBowl = new pc::worker::bowlshaping::Bowl(1u);
  l_pBowl->setName(f_name);
  l_pBowl->accept(*l_pBowlUpdateVisitor);

  l_pBowl->addUpdateCallback(new pc::factory::SV3DUpdateCallback(l_pBowlUpdateVisitor));
  l_pBowl->addCullCallback(new pc::factory::SV3DCullCallback);

  return l_pBowl;  
}

pc::factory::SV3DNode * createFloor(const std::string& f_name, pc::core::Framework* f_pFramework, const pc::worker::bowlshaping::BowlShaperData &f_bowlShaper, const cc::assets::FloorData &f_floor )
{
  if (f_pFramework == nullptr)
  {
    return nullptr;
  }
  const auto  l_pLayoutGenerator = new pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator(
                                        f_bowlShaper.m_numRadialSections,
                                        pc::vehicle::g_mechanicalData->getCenter(),
                                        f_bowlShaper.m_bowlDefault.m_semiaxis.x(),
                                        f_bowlShaper.m_bowlDefault.m_semiaxis.y(),
                                        static_cast<vfc::int32_t>(f_bowlShaper.m_bowlDefault.m_n));

  const auto l_pFloorUpdateVisitor = new pc::factory::FloorUpdateVisitor(
  f_pFramework->m_cameraCalibrationReceiver, // PRQA S 0251
  f_pFramework->m_cameraMasksReceiver,
  l_pLayoutGenerator->getPolarLayout()
  );

  const auto l_pFloor = new pc::factory::Floor(f_floor.m_size, f_floor.m_resolution, l_pLayoutGenerator->getPolarLayout().getOffset());
  l_pFloor->setName(f_name);
  l_pFloor->accept(*l_pFloorUpdateVisitor);

  l_pFloor->addUpdateCallback(new pc::factory::SV3DUpdateCallback(l_pFloorUpdateVisitor));
  l_pFloor->addCullCallback(new pc::factory::SV3DCullCallback);

  return l_pFloor;
}

} // namespace assets
} // namespace cc 

