//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PARKINGSPOTS_EXPMODETRAJECTORY_H
#define CC_ASSETS_PARKINGSPOTS_EXPMODETRAJECTORY_H


#include <osg/ref_ptr>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Depth>
#include <osg/NodeCallback>

#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
// #include "cc/assets/parkingspots/inc/ParkingSpot.h"


//! forward declaration
namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc


namespace cc
{
namespace assets
{
namespace trajectory
{


class ExpModeTrajectorySettings;
extern pc::util::coding::Item<ExpModeTrajectorySettings> g_trajectorySettings;

//!
//! TrajectoryEndPoint
//! DIN70k
struct ControlPoint
{
  ControlPoint()
    : pos(-2.0f, 0.0f) // Valid default value
    , startDir(1.0f, 0.0f) // Valid default value
    , endDir(-1.0f, 0.0f) // Valid default value
  {}

  osg::Vec2f pos;      // Position of the parking trajectory segment start or end point
  osg::Vec2f startDir; // Direction vector in the point towards the end of the trajectory (the dir vector for the start point of the segment)
  osg::Vec2f endDir;   // Direction vector in the point towards the start of the trajectory (the dir vector for the end point of the segment)
};

struct Segment
{
  Segment()
    : length(0.0f)
    , segmentCount(0u)
  {}

  float length;
  unsigned int segmentCount;
};

//!
//! ParkingSpotTrajectory
//!
class ExpModeTrajectory : public osg::Geode
{
public:

  ExpModeTrajectory(
    unsigned int f_renderBinOrder,
    bool f_depthTest,
    bool f_depthBufferWrite,
    const cc::assets::trajectory::TrajectoryParams_st & f_trajParams);

  virtual ~ExpModeTrajectory();

  bool isCulled() const
  {
    return m_cull;
  }

  void setCull(bool f_isCulled)
  {
    m_cull = f_isCulled;
  }

  void constructTrajectories();

  float getAlpha() const;

  void setAlpha(float f_alpha);

private:

  //! Copy constructor is not permitted.
  ExpModeTrajectory (const ExpModeTrajectory& other); // = delete
  //! Copy assignment operator is not permitted.
  ExpModeTrajectory& operator=(const ExpModeTrajectory& other); // = delete

  osg::Image* create1DTexture();

  void resizeArrays(unsigned int f_newSplinePointCount);

  void makeDirty();

  void loadTexture();

  void generateWaveShape();

  void applyFading(
    unsigned int f_splinePointIndex_Opaque,
    unsigned int f_splinePointIndex_Transparent,
    float        f_startValue,
    float        f_endValue);

  bool                 m_cull;

  cc::assets::trajectory::TrajectoryParams_st m_trajParams;
  float                m_lineGeometryWidth; // The width of the quad stripe [m].

  unsigned int         m_splinePointCount;    // This tells the actual point count. Must be <= m_splineMaxPointCount.
  unsigned int         m_splineMaxPointCount; // This defines the capacity of the arrays.

  osg::Uniform*        m_alphaUniform;

  osg::ref_ptr<osg::Vec2Array> m_splineVertexArray;
  cc::assets::trajectory::commontypes::VertexData_st m_vertexData;
  osg::ref_ptr<osg::Geometry>                        m_geometry;


};

} // namespace trajectory
} // namespace assets
} // namespace cc


#endif // CC_ASSETS_PARKINGSPOTS_EXPMODETRAJECTORY_H
