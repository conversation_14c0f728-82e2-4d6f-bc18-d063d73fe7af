//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  HmiElementsSettings.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_UIELEMENTS_HMIELEMENTSSETTINGS_H
#define CC_ASSETS_UIELEMENTS_HMIELEMENTSSETTINGS_H

#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/assets/uielements/inc/Utils.h"

#include <osgDB/ReadFile>


namespace cc
{

namespace assets
{
namespace uielements
{

class UIData : public pc::util::coding::ISerializable
{
public:

  UIData()
    : m_iconSize(48.0f, 48.0f)
    , m_iconCenter(442.0f, 64.0f)
    , m_responseArea(76.0f, 76.0f)
    , m_isHoriScreen(true)
  {
  }

  SERIALIZABLE(UIData)
  {
    ADD_MEMBER(osg::Vec2f, iconSize);
    ADD_MEMBER(osg::Vec2f, iconCenter);
    ADD_MEMBER(osg::Vec2f, responseArea);
    ADD_BOOL_MEMBER(isHoriScreen);
  }

  osg::Vec2f m_iconSize;
  osg::Vec2f m_iconCenter;
  osg::Vec2f m_responseArea;
  bool       m_isHoriScreen;

};

//!
//! CameraIconSettings
//!
class UISettings : public pc::util::coding::ISerializable
{
public:

  UISettings()
    : m_texturePathCamV("cc/vehicle_model/ui/33_cam_icon_vertical.png")
    , m_texturePathCamH("cc/vehicle_model/ui/33_cam_icon_horizontal.png")
    , m_texturePathCamO("cc/vehicle_model/ui/33_cam_icon_oblique.png")
    , m_texturePathCamVActive("cc/vehicle_model/ui/33_cam_icon_vertical_active.png")
    , m_texturePathCamHActive("cc/vehicle_model/ui/33_cam_icon_horizontal_active.png")
    , m_texturePathCamOActive("cc/vehicle_model/ui/33_cam_icon_oblique_active.png")
    , m_texturePathQuit("cc/vehicle_model/ui/49_quit.png")
    , m_texturePathPPDIS("cc/vehicle_model/ui/28_pp_active.png")
    , m_texturePathPPON("cc/vehicle_model/ui/28_pp_active.png")
    , m_texturePathPPOFF("cc/vehicle_model/ui/29_pp_inactive.png")
    , m_texturePathAutoCamActivON("cc/vehicle_model/ui/29_auto_open.png")
    , m_texturePathAutoCamActivOFF("cc/vehicle_model/ui/29_auto_close.png")
    , m_texturePathVM2D("cc/resources/icons/poc_2d_button.png")
    , m_texturePathVM2DOFF("cc/vehicle_model/ui/30_2D_button_disable.png")
    , m_texturePathVM3D("cc/resources/icons/poc_3d_button.png")
    , m_texturePathVM3DOFF("cc/vehicle_model/ui/31_3D_button_disable.png")
    , m_texturePathBottom("cc/resources/icons/poc_bottom_bar.png")
    , m_texturePathApaActive("cc/resources/icons/poc_apa_active.png")
    , m_texturePathApaInactive("cc/resources/icons/poc_apa_inactive.png")
    , m_texturePathOffRoad("cc/resources/icons/poc_offroad_active.png")
    , m_texturePathOffRoadOFF("cc/resources/icons/poc_offroad_inactive.png")
    , m_texturePathWheel("cc/resources/icons/poc_wheel_active.png")
    , m_texturePathWheelOFF("cc/resources/icons/poc_wheel_inactive.png")
    , m_texturePathWheelLine("cc/resources/icons/poc_wheel_line.png")
    , m_texturePathWarnSymbolUssWhole("cc/resources/icons/uss_fault_warning_whole.png")
    , m_texturePathWarnSymbolUssFront("cc/resources/icons/uss_fault_warning_front.png")
    , m_texturePathWarnSymbolUssRear("cc/resources/icons/uss_fault_warning_rear.png")
    , m_texturePathWarnSymbolUssVert("cc/resources/icons/uss_fault_warning_vert.png")
    , m_texturePathViewInfoSingleFront("cc/resources/viewInfo/viewinfosinglefront.png")
    , m_texturePathViewInfoSingleRear("cc/resources/viewInfo/viewinfosinglerear.png")
    , m_texturePathViewInfoPerspective("cc/resources/viewInfo/viewinfo3d.png")
    , m_texturePathViewInfoFrontWheel("cc/resources/viewInfo/viewinfofrontwheel.png")
    , m_texturePathViewInfoRearWheel("cc/resources/viewInfo/viewinforearwheel.png")
    , m_texturePathViewInfoFrontJunction("cc/resources/viewInfo/viewinfofrontJunction.png")
    , m_texturePathViewInfoRearJunction("cc/resources/viewInfo/viewinforearjunction.png")
    , m_texturePathViewInfoSTB("cc/resources/viewInfo/viewinfostb.png")
    , m_texturePathPleaseCareSurrounding("cc/resources/viewInfo/pleasecaresurrounding.png")
    , m_texturePathFloatPleaseCareSurrounding("cc/resources/viewInfo/floatpleasecaresurrounding.png")
    , m_texturePathFrontWheelLimitLine("cc/resources/viewInfo/frontwheel_limitline.png")
    , m_texturePathRearWheelLimitLine("cc/resources/viewInfo/rearwheel_limitline.png")
    , m_texturePathFullScreenBlackLineIcon("cc/resources/viewInfo/blackline_10x1320pix.png")
    , m_texturePathFloatScreenBlackLineIcon("cc/resources/viewInfo/blackline_7x858pix.png")
    , m_texturePathfloatTopLeftCorner("cc/resources/viewInfo/float_corner_top_left.png")
    , m_texturePathfloatTopRightCorner("cc/resources/viewInfo/float_corner_top_right.png")
    , m_texturePathfloatButtomLeftCorner("cc/resources/viewInfo/float_corner_buttom_left.png")
    , m_texturePathfloatButtomRightCorner("cc/resources/viewInfo/float_corner_buttom_right.png")
    , m_texturePathFullTopLeftCorner("cc/resources/viewInfo/full_corner_top_left.png")
    , m_texturePathFullTopRightCorner("cc/resources/viewInfo/full_corner_top_right.png")
    , m_texturePathFullButtomLeftCorner("cc/resources/viewInfo/full_corner_bottom_left.png")
    , m_texturePathFullButtomRightCorner("cc/resources/viewInfo/full_corner_bottom_right.png")
    , m_texturePathDegrationNotCalirated("cc/resources/icons/avm_not_calibrated.png")
    , m_texturePathDegrationAndroidError("cc/resources/icons/avm_error_please_go_to_4s.png")
    , m_texturePathDegrationPlanviewIcon("cc/resources/icons/warningicon.png")
    , m_texturePathDegrationPlanviewRedIcon("cc/resources/icons/redwarningicon.png")
    , m_texturePathVehicleTransIcon("cc/resources/icons/vehicle_trans.png")
    , m_texturePathVehicleTransIconVert("cc/resources/icons/vehicle_trans_float.png")
    , m_texturePathTextBoxSafeNotification("cc/vehicle_model/ui/117_Text_Pay_attention_to_the_environment.png")
    , m_texturePathTextBoxSafeNotification_vert("cc/vehicle_model/ui/117_Text_Pay_attention_to_the_environment_vert.png")
    , m_texturePathParkingPlanIcon("cc/resources/icons/parkicon.png")
    , m_texturePathParkingPlanIcon_vert("cc/resources/icons/parkicon_vert.png")
    , m_texturePathParkIcon("cc/vehicle_model/ui/21_park_confirmed.png")
    , m_texturePathParkIconOFF("cc/vehicle_model/ui/22_park_not_confirmed.png")
    , m_texturePathParkModePPSCIn("cc/vehicle_model/ui/03_parallel_parkin_mode.png")
    , m_texturePathParkModeCPSCIn("cc/vehicle_model/ui/04_cross_parkin_mode.png")
    , m_texturePathParkModeOut("cc/vehicle_model/ui/05_parallel_parkout_mode.png")
    , m_texturePathParkModeSelected("cc/vehicle_model/ui/06_park_mode_selected.png")
    , m_texturePathParkSearchingRoad("cc/vehicle_model/ui/09_searching_road.png")
    , m_texturePathParkSearchingWave1("cc/vehicle_model/ui/11_searching_wave1.png")
    , m_texturePathParkSearchingWave2("cc/vehicle_model/ui/12_searching_wave2.png")
    , m_texturePathParkParaSlotValid("cc/vehicle_model/ui/14_parallel_slot_valid.png")
    , m_texturePathParkVertSlotValid("cc/vehicle_model/ui/13_vertical_slot_valid.png")
    , m_texturePathParkBackground("cc/vehicle_model/ui/01_background.png")
    , m_texturePathParkRPAGuidance("cc/vehicle_model/ui/45_rpa_background.jpg")
    , m_texturePathParkBoxGearD("cc/vehicle_model/ui/98_searching_slot_searching.png")
    , m_texturePathTextBoxTurnLever("cc/vehicle_model/ui/98_searching_turn_lever.png")
    , m_texturePathTextBoxExitGearR("cc/vehicle_model/ui/98_searching_exit_gear_R.png")
    , m_texturePathTextBoxSlowDown("cc/vehicle_model/ui/98_searching_slow_down.png")
    , m_texturePathTextBoxStop("cc/vehicle_model/ui/98_searching_stop.png")
    , m_texturePathTextBoxPsIDSelection("cc/vehicle_model/ui/98_searching_PS_ID_selection.png")
    , m_texturePathTextBoxDoorToCloseinSearching("cc/vehicle_model/ui/98_searching_door_to_close.png")
    , m_texturePathTextBoxFunctionOff("cc/vehicle_model/ui/98_searching_function_off.png")
    , m_texturePathTextBoxCloseTrunk("cc/vehicle_model/ui/98_searching_close_trunk.png")
    , m_texturePathTextBoxSlotSearchingUnder10("cc/vehicle_model/ui/98_searching_slot_searching_under_10.png")
    , m_texturePathTextBoxOnGoing("cc/vehicle_model/ui/99_manuaring_ongoing.png")
    , m_texturePathTextBoxAccAeb("cc/vehicle_model/ui/99_manuaring_acc_aeb.png")
    , m_texturePathTextBoxCompleted("cc/vehicle_model/ui/99_manuaring_completed.png")
    , m_texturePathTextBoxDriverDoorOpen("cc/vehicle_model/ui/99_manuaring_driverdoor_open.png")
    , m_texturePathTextBoxEPBApply("cc/vehicle_model/ui/99_manuaring_epb_apply.png")
    , m_texturePathTextBoxExternalEcuActive("cc/vehicle_model/ui/99_manuaring_external_ecu_active.png")
    , m_texturePathTextBoxExternalEcuFailure("cc/vehicle_model/ui/99_manuaring_external_ecu_failure.png")
    , m_texturePathTextBoxGasPedal("cc/vehicle_model/ui/99_manuaring_gas_pedal.png")
    , m_texturePathTextBoxGearInterrupt("cc/vehicle_model/ui/99_manuaring_gear_interrupt.png")
    , m_texturePathTextBoxGearIntervention("cc/vehicle_model/ui/99_manuaring_gear_intervention.png")
    , m_texturePathTextBoxMovetimesOverflow("cc/vehicle_model/ui/99_manuaring_movetimes_overflow.png")
    , m_texturePathTextBoxObjInTraj("cc/vehicle_model/ui/99_manuaring_obj_in_trajectory.png")
    , m_texturePathTextBoxPasFailure("cc/vehicle_model/ui/99_manuaring_pas_failure.png")
    , m_texturePathTextBoxRecovertimesOverflow("cc/vehicle_model/ui/99_manuaring_recovertimes_overflow.png")
    , m_texturePathTextBoxSeatbeltUnbuckle("cc/vehicle_model/ui/99_manuaring_seatbelt_unbuckle.png")
    , m_texturePathTextBoxSpaceLimit("cc/vehicle_model/ui/99_manuaring_space_limit.png")
    , m_texturePathTextBoxSpeedHigh("cc/vehicle_model/ui/99_manuaring_speed_high.png")
    , m_texturePathTextBoxSteeringwheelHandon("cc/vehicle_model/ui/99_manuaring_steeringwheel_handson.png")
    , m_texturePathTextBoxTimingOverflow("cc/vehicle_model/ui/99_manuaring_timing_overflow.png")
    , m_texturePathTextBoxTrajOutRange("cc/vehicle_model/ui/99_manuaring_trajectory_out_range.png")
    , m_texturePathTextBoxTrunkdoorOpen("cc/vehicle_model/ui/99_manuaring_trunkdoor_open.png")
    , m_texturePathTextBoxVehicleBlock("cc/vehicle_model/ui/99_manuaring_vehicle_block.png")
    , m_texturePathTextBoxOtherReason("cc/vehicle_model/ui/99_manuaring_other_reason.png")
    , m_texturePathTextBoxPressDeadmanSwitch("cc/vehicle_model/ui/99_manuaring_press_deadman_switch.png")
    , m_texturePathTextBoxReleaseBrake("cc/vehicle_model/ui/99_manuaring_release_brake.png")
    , m_texturePathTextBoxSearchingProcess("cc/vehicle_model/ui/99_manuaring_searching_process.png")
    , m_texturePathTextBoxSurroundView("cc/vehicle_model/ui/99_manuaring_surround_view.png")
    , m_texturePathTextBoxUnsafeBehavior("cc/vehicle_model/ui/99_manuaring_unsafe_behavior.png")
    , m_texturePathTextBoxDoorToClose("cc/vehicle_model/ui/99_manuaring_door_to_close.png")
    , m_texturePathTextBoxQuitTJAHWA("cc/vehicle_model/ui/99_manuaring_quit_TJA_HWA.png")
    , m_texturePathTextBoxQuitObjnotExist("cc/vehicle_model/ui/98_searching_front_obj_POC.png")
    , m_texturePathTextBoxFailure("cc/vehicle_model/ui/99_failure.png")
    , m_texturePathTextModeSelect("cc/vehicle_model/ui/02_text_please_select_mode.png")
    , m_texturePathParkFinished("cc/vehicle_model/ui/38_park_finished.png")
    , m_texturePathWheelSeparatorHorizontal("cc/vehicle_model/vehicle2d/WheelSeparator_Horizontal.png")
    , m_texturePathWheelSeparatorHorizontalFront("cc/vehicle_model/vehicle2d/WheelSeparator_Horizontal_Front.png")
    , m_texturePathWheelSeparatorHorizontalRear("cc/vehicle_model/vehicle2d/WheelSeparator_Horizontal_Rear.png")
    , m_texturePathWheelSeparatorVertical("cc/vehicle_model/vehicle2d/WheelSeparator_Vertical.png")

    // parking searching
    , m_texturePathParkAutoPic("cc/vehicle_model/vehicleicon/86_Autopark_pic.png")
    , m_texturePathParkHalo("cc/vehicle_model/vehicleicon/86_Autopark_halo_pic.png")
    , m_texturePathTextBoxSlotSearching("cc/vehicle_model/ui/109_Text_Search_parking_spaces.png")
    , m_texturePathTextBoxSearchingInLowSpeed("cc/vehicle_model/ui/101_Text_Low_speed_straight_line_driving.png")
    , m_texturePathParkingUIBackground("cc/vehicle_model/ui/133_Background_Horizontal.png")
    , m_texturePathSearchingTextBoxTimeOut("cc/vehicle_model/ui/77_Text_Search_timed_out.png")
    , m_texturePathSearchingTimeOut("cc/vehicle_model/ui/12_Unrecoverable_interrupt_Search_timed_out.png")
    , m_texturePathSearchingTextBoxSlowDown("cc/vehicle_model/ui/108_Text_Slow_down.png")
    , m_texturePathSearchingSlowDown("cc/vehicle_model/ui/30_Unrecoverable_interrupt_Limited_speed.png")
    , m_texturePathSearchingTextBoxSelectType("cc/vehicle_model/ui/103_Text_Select_the_parking_mode.png")
    , m_texturePathSearchingFrontInButton("cc/vehicle_model/ui/62_Button_Head_in_parking_unselected.png")
    , m_texturePathSearchingRearInButton("cc/vehicle_model/ui/64_Button_Back_in_parking_selected.png")
    , m_texturePathSearchingTextBoxFindSlotStop("cc/vehicle_model/ui/75_Text_Located_parking_space_please_park.png")
    , m_texturePathSearchingPOCDirecSelectVehicle("cc/vehicle_model/vehicleicon/AutoParkOutSide.png")
    , m_texturePathSearchingPOCDirecSelectVehicleTrans("cc/vehicle_model/vehicleicon/AutoParkOutSideTrans.png")
    , m_texturePathSearchingPOCDirecSelectSteeringWheel("cc/vehicle_model/ui/31_Steering_wheel.png")
    , m_texturePathSearchingPOCDirecSelectSteeringWheelTrans("cc/vehicle_model/ui/31_Steering_wheel_trans.png")
    , m_texturePathTextBoxSearchingPOCDirecSelectTop("cc/vehicle_model/ui/Text_Select_the_direction_Top.png")
    , m_texturePathTextBoxSearchingPOCDirecSelectBottom("cc/vehicle_model/ui/113_Text_Select_the_direction.png")
    , m_texturePathTextBoxSearchingPOCDirecSelectBottomTrans("cc/vehicle_model/ui/113_Text_Select_the_direction_trans.png")
    , m_texturePathSearchingVehicleWithDoors("cc/vehicle_model/vehicle2d/gray/vehicle_body/normal_body.png")
    , m_texturePathContinueDrivingText("cc/resources/icons/36_text_continue_driving_for_distance.png")
    , m_texturePathMovesLeftNumberText("cc/resources/icons/DFC_text_ParkGuideMovesLeft.png")
    , m_texturePathSearchingDoorFrontRight("cc/vehicle_model/vehicle2d/gray/front_right_door/open_warning.png")
    , m_texturePathSearchingDoorFrontLeft("cc/vehicle_model/vehicle2d/gray/front_left_door/open_warning.png")
    , m_texturePathSearchingTrunkOpen("cc/vehicle_model/vehicle2d/gray/trunk/open_warning.png")
    , m_texturePathSearchingDoorRearRight("cc/vehicle_model/vehicle2d/gray/rear_right_door/open_warning.png")
    , m_texturePathSearchingDoorRearLeft("cc/vehicle_model/vehicle2d/gray/rear_left_door/open_warning.png")
    , m_texturePathSearchingHoodOpen("cc/vehicle_model/vehicle2d/gray/hood/open_warning.png")
    , m_texturePathSearchingDoorsOpen("cc/vehicle_model/ui/107_doors_open_warning.png")
    , m_texturePathSearchingTextBoxSelectParkoutDirectionWhenStop("cc/vehicle_model/ui/Text_Select_The_Parkout_Direction_When_Stop.png")
    // RPA
    , m_texturePathRPAOthersBluetoothmobilephone("cc/vehicle_model/ui/RPA_37_Others_Bluetooth_mobile_phone.png")
    , m_texturePathRPAOthersBluetoothDisconnect("cc/vehicle_model/ui/RPA_38_Others_BlueToothDisconnect.png")
    , m_texturePathRPATextBluetoothConnectedPrompt("cc/vehicle_model/ui/RPA_99_Text_Bluetooth_connected_text_prompt.png")
    , m_texturePathRPATextPleaseLeaveTheCar("cc/vehicle_model/ui/RPA_Text_Please_Leave_The_Car.png")
    , m_texturePathRPATextBluetoothConnected("cc/vehicle_model/ui/RPA_100_Text_Bluetooth_connected.png")
    , m_texturePathRPATextPlease_parking("cc/vehicle_model/ui/RPA_104_Text_Please_parking.png")
    , m_texturePathRPATextClickOnThePhoneToStartParking("cc/vehicle_model/ui/RPA_110_Text_Click_on_the_phone_to start_parking.png")
    , m_texturePathRPATextOpenTheAPPAndConnectToBluetooth("cc/vehicle_model/ui/RPA_112_Text_Open_the_APP_and_connect_to_Bluetooth.png")
    , m_texturePathRPATextRemoteParkingIsNotAvailable("cc/vehicle_model/ui/RPA_127_Text_Remote_parking_is_not_available.png")
    , m_texturePathRPATextRemoteParking("cc/vehicle_model/ui/RPA_128_Text_Remote_parking.png")
    , m_texturePathRPATextDriverReponseTimeout("cc/vehicle_model/ui/RPA_129_Text_Driver_reponse_timeout.png")
    , m_texturePathRPATextSuspend("cc/vehicle_model/ui/RPA_130_Text_Suspend.png")
    , m_texturePathRPATextBlueToothDisconnect("cc/vehicle_model/ui/RPA_132_Text_BlueTooth_Disconnect.png")
    , m_texturePathRPATextTerminated("cc/vehicle_model/ui/RPA_131_Text_Terminate.png")
    , m_texturePathRPATextUsePhone("cc/vehicle_model/ui/RPA_Text_Please_Use_Phone.png")

    // start parking
    , m_texturePathCrossVehicleNoSlotPic("cc/vehicle_model/vehicleicon/247_Cross_Vehicle.png")
    , m_texturePathCrossVehiclePic("cc/vehicle_model/vehicleicon/245_Cross_Vehicle_With_Slot.png")
    , m_texturePathParallelVehiclePic("cc/vehicle_model/vehicleicon/244_Parallel_Vehicle_With_Slot.png")
    , m_texturePathDiagonalVehiclePic("cc/vehicle_model/vehicleicon/246_Diagonal_Slot_With_Slot.png")
    , m_texturePathStarkParkOutButton("cc/vehicle_model/ui/50_Button_Start_parking_out.png")
    , m_texturePathStarkParkInButton("cc/vehicle_model/ui/50_Button_Start_parking_in.png")
    , m_texturePathStarkParkOutDeactivateButton("cc/vehicle_model/ui/49_Button_Start_parking_out_gray.png")
    , m_texturePathStarkParkInDeactivateButton("cc/vehicle_model/ui/49_Button_Start_parking_in_gray.png")
    , m_texturePathTextParkingIn("cc/vehicle_model/ui/Text_Parking_in.png")
    // guidance activate
    , m_texturePathGuidanceGearD("cc/vehicle_model/ui/32_Others_Gear_D.png")
    , m_texturePathGuidanceGearN("cc/vehicle_model/ui/33_Others_Gear_N.png")
    , m_texturePathGuidanceGearR("cc/vehicle_model/ui/35_Others_Gear_R.png")
    , m_texturePathGuidanceGearP("cc/vehicle_model/ui/34_Others_Gear_P.png")
    , m_texturePathTextBoxAPAParkingIn("cc/vehicle_model/ui/94_Text_Automatic_parking.png")
    , m_texturePathTextBoxAPAParkingOut("cc/vehicle_model/ui/Text_Automatic_parkingOut.png")
    , m_texturePathTextBoxParkingNoticeSur("cc/vehicle_model/ui/83_Text_Pay_attention_to_the_environment.png")
    , m_texturePathTextGuidanceReleaseBrakeAndSteering("cc/vehicle_model/ui/Text_Guidance_Release_Brake_And_Steering.png")
    , m_texturePathSuspendButton("cc/vehicle_model/ui/53_Button_Pause.png")
    , m_texturePathTextParkingOutPayAttentionToEnvironment("cc/vehicle_model/ui/Text_parkingout_payattention_to_environment.png")

    // park finished
    , m_texturePathTextBoxParkFinished("cc/vehicle_model/ui/86_Text_Park_finished.png")
    , m_texturePathTextBoxFinishedTakeOver("cc/vehicle_model/ui/121_Text_Take_over_the_car.png")

    // guidance suspend
    , m_texturePathHoriSuspendParkingPause("cc/vehicle_model/ui/Text_Parking_Pause.png")
    , m_texturePathTextBoxQuitIn30s("cc/vehicle_model/ui/132_Text_Automatic_withdrawal.png")
    , m_texturePathSuspendContinueButton("cc/vehicle_model/ui/55_Button_Continue.png")
    , m_texturePathSuspendContinueButtonGray("cc/vehicle_model/ui/53_Button_Continue_gray.png")
    , m_texturePathSuspendTextBoxObjectOnPath("cc/vehicle_model/ui/82_Text_Obstacle_removal.png")
    , m_texturePathSuspendObjectOnPath("cc/vehicle_model/vehicleicon/47_Recoverable_interrupt_Obstacle_removal.png")
    , m_texturePathSuspendObjectOnPathIcon("cc/vehicle_model/ui/47_Recoverable_interrupt_Obstacle_removal.png")
    , m_texturePathSuspendTextBoxDoorOpen("cc/vehicle_model/ui/81_Text_Door_opening.png")
    , m_texturePathSuspendTextBrakePadal("cc/vehicle_model/ui/84_Text_Brake_Pedal_Pressed.png")
    , m_texturePathSuspendBrakePadal("cc/vehicle_model/ui/84_Brake_Pedal_Pressed.png")
    , m_texturePathSuspendTextMirrorFold("cc/vehicle_model/ui/80_Text_Rearview_mirror_folding.png")
    , m_texturePathSuspendMirrorFold("cc/vehicle_model/ui/44_Recoverable_interrupt_Rearview_mirror_open.png")
    , m_texturePathSuspendTrunkOpen("cc/vehicle_model/ui/44_Recoverable_interrupt_trunk_open.png")
    , m_texturePathSuspendHoodOpen("cc/vehicle_model/ui/44_Recoverable_interrupt_hood_open.png")
    // , m_texturePathSuspendTextSeatBelt("cc/vehicle_model/ui/")
    // , m_texturePathSuspendSeatBelt("cc/vehicle_model/ui/")
    // , m_texturePathSuspendTextBlueTooth("cc/vehicle_model/ui/")
    // , m_texturePathSuspendBlueTooth("cc/vehicle_model/ui/")
    , m_texturePathSuspendTextHoodOpen("cc/vehicle_model/ui/78_Text_Front_hatch_open.png")
    , m_texturePathSuspendTextTrunkOpen("cc/vehicle_model/ui/79_Text_Trunk_open.png")
    , m_texturePathSuspendTextUserTriggerPause("cc/vehicle_model/ui/80_Text_User_Trigger_Pause.png")
    //park assist standby
    , m_texturePathAssistStandbyResponseTimeout("cc/vehicle_model/ui/Response_timeout.png")

    //park quit
    , m_texturePathQuitTextBoxApaQuitTakeOver("cc/vehicle_model/ui/95_Text_vehicle_take_over.png")
    , m_texturePathQuitTextBoxSeatBeltUnbuckle("cc/vehicle_model/ui/130_Text_Seat_belt_is_not_wore.png")
    , m_texturePathSeatBeltUnbuckle("cc/vehicle_model/ui/10_Unrecoverable_interrupt_Unfastened_seat_belt.png")
    , m_texturePathQuitTextBoxExcessiveSlope("cc/vehicle_model/ui/74_Text_Too_steep_slope.png")
    , m_texturePathExcessiveSlope("cc/vehicle_model/ui/09_Unrecoverable_interrupt_Too_steep_slope.png")
    , m_texturePathQuitTextDriverOverride("cc/vehicle_model/ui/126_Text_Interfered_by_the_driver.png")
    , m_texturePathDriverOverride("cc/vehicle_model/ui/28_Unrecoverable_interrupt_Vehicle_be_intervened.png")
    , m_texturePathQuitTextRoutePlanningFailure("cc/vehicle_model/ui/122_Text_Path_planning_failed.png")
    , m_texturePathRoutePlanningFailure("cc/vehicle_model/ui/25_Unrecoverable_interrupt_Path_plan_failed.png")
    , m_texturePathQuitTextVehicleSpeedOverthreshold("cc/vehicle_model/ui/91_Text_Parking_overspeed.png")
    , m_texturePathVehicleSpeedOverthreshold("cc/vehicle_model/ui/19_Unrecoverable_interrupt_Parking_overspeed.png")
    , m_texturePathQuitTextAPSTimeout("cc/vehicle_model/ui/90_Text_Parking_APS_timeout.png")
    , m_texturePathAPSTimeout("cc/vehicle_model/ui/12_Unrecoverable_interrupt_Search_timed_out.png")
    , m_texturePathQuitTextCurrentStepNumberOverThreshold("cc/vehicle_model/ui/88_Text_Too_much_parking.png")
    , m_texturePathCurrentStepNumberOverThreshold("cc/vehicle_model/ui/16_Unrecoverable_interrupt_Too_much_parking.png")
    , m_texturePathQuitTextSpaceIsLimitedInParkOutMode("cc/vehicle_model/ui/123_Text_Limited_parking_space.png")
    , m_texturePathSpaceIsLimitedInParkOutMode("cc/vehicle_model/vehicleicon/26_Unrecoverable_interrupt_Limited_parking_space.png")
    , m_texturePathQuitTextEPBFailure("cc/vehicle_model/ui/92_Text_Electronic_hanbrake_pulled_up.png")
    , m_texturePathEPBFailure("cc/vehicle_model/ui/20_Unrecoverable_interrupt_Electronic_hanbrake_system_fault.png")
    , m_texturePathQuitTextSCUFailure("cc/vehicle_model/ui/76_Text_Gear_controller_fault.png")
    , m_texturePathSCUFailure("cc/vehicle_model/ui/13_Unrecoverable_interrupt_Gearcontroller_fault.png")
    , m_texturePathQuitTextBoxDriveModeUnsuitable("cc/vehicle_model/ui/114_text_drive_mode_unsuitable.png")
    , m_texturePathDriveModeUnsuitable("cc/vehicle_model/ui/115_unrecoverable_interrupt_drive_mode_unsuitable.png")
    , m_texturePathQuitTextBoxTrailerHitchConnected("cc/vehicle_model/ui/116_text_trailer_hitch_connected.png")
    , m_texturePathTrailerHitchConnected("cc/vehicle_model/ui/116_unrecoverable_interrupt_trailer_hitch_connected.png")

    //park comfirming
    , m_texturePathParkConfirmingTextSeatBelt("cc/vehicle_model/ui/118_Text_Fasten_seat_belt.png")
    , m_texturePathParkConfirmingTextPressBrakePedal("cc/vehicle_model/ui/119_Text_Brakes.png")
    , m_texturePathParkConfirmingPressBrakePedal("cc/vehicle_model/ui/24_Unrecoverable_interrupt_Brakes.png")
    , m_texturePathParkConfirmingTextCloseDoor("cc/vehicle_model/ui/107_Text_Close_the_door.png")
    , m_texturePathParkConfirmingTextExpandedMirror("cc/vehicle_model/ui/111_Text_Open_the_rearview_mirror.png")
    , m_texturePathParkConfirmingExpandedMirror("cc/vehicle_model/ui/08_Unrecoverable_interrupt_Rearview_mirror_folding.png")
    , m_texturePathParkConfirmingTextCloseTrunk("cc/vehicle_model/ui/106_Text_Turn_off_the_trunk.png")
    , m_texturePathParkConfirmingTextCloseHood("cc/vehicle_model/ui/105_Text_Turn_off_the_front_cabin.png")
    , m_texturePathParkConfirmingTextStop("cc/vehicle_model/ui/Text_Unrecoverable_interrupt_Stop.png")
    , m_texturePathParkFrontIsClearParkingPath("cc/vehicle_model/ui/Text_The_Parking_Path_Available.png")
    , m_texturePathParkFrontIsClearPic("cc/vehicle_model/ui/front_is_clear_parking_path.png")
    , m_texturePathParkFrontIsClearHandOver("cc/vehicle_model/ui/Text_Please_Hand_Over_Vehicle.png")
    , m_texturePathParkFrontIsClear("cc/vehicle_model/ui/11_Unrecoverable_interrupt_Take_over_vehicle.png")
    , m_texturePathConfirmingTextAPAquitTakeOver("cc/vehicle_model/ui/Text_APA_quit_take_over.png")
    , m_texturePathPathParkConfirmingTextSmallParkSlot("cc/vehicle_model/ui/124_Text_Recommend_to_use_remote_parking.png")
    , m_texturePathPathParkConfirmingSmallParkSlot("cc/vehicle_model/ui/124_small_park_slot.png")
    , m_texturePathParkConfirmingTextHoldBrakeAndStart("cc/vehicle_model/ui/102_Text_Click_to_start_parking.png")
    , m_texturePathQuitTextAPAFailure("cc/vehicle_model/ui/89_Text_Parking_system_fault.png")
    , m_texturePathAPAFailure("cc/vehicle_model/ui/17_Unrecoverable_interrupt_Parking_system_fault.png")
    , m_texturePathQuitTextExternalECUFailure("cc/vehicle_model/ui/72_Text_Associated_system_fault.png")
    , m_texturePathExternalECUFailure("cc/vehicle_model/ui/04_Unrecoverable_interrupt_Associated_system_response_timeout.png")
    , m_texturePathQuitTextABSTCSESPACCAEBActive("cc/vehicle_model/ui/73_Text_Other_system_activation.png")
    , m_texturePathABSTCSESPACCAEBActive("cc/vehicle_model/ui/05_Unrecoverable_interrupt_Other_system_activation.png")
    , m_texturePathQuitTextESCFailure("cc/vehicle_model/ui/69_Text_ESP_fault.png")
    , m_texturePathESCFailure("cc/vehicle_model/ui/01_Unrecoverable_interrupt_ESP_fault.png")
    , m_texturePathQuitTextEPSFailure("cc/vehicle_model/ui/68_Text_EPS_fault.png")
    , m_texturePathEPSFailure("cc/vehicle_model/ui/00_Unrecoverable_interrupt_EPS_fault.png")
    , m_texturePathQuitTextVehicleBlock("cc/vehicle_model/ui/125_Text_VehicleBlock.png")
    , m_texturePathVehicleBlock("cc/vehicle_model/ui/27_Unrecoverable_interrupt_VehicleBlock.png")
    , m_texturePathQuitTextInterruptNumberOverThreshold("cc/vehicle_model/ui/84_Text_interrupt_NumberOverThreshold.png")
    , m_texturePathInterruptNumberOverThreshold("cc/vehicle_model/ui/14_Unrecoverable_interrupt_NumberOverThreshold.png")
    , m_texturePathQuitTextRadarDirty("cc/vehicle_model/ui/Text_Radar_Dirty.png")
    , m_texturePathRadarDirty("cc/vehicle_model/ui/Radar_Dirty.png")
    , m_texturePathQuitTextEPBActive("cc/vehicle_model/ui/Text_EPBActive.png")
    , m_texturePathEPBActive("cc/vehicle_model/ui/21_Unrecoverable_interrupt_EPBActive.png")
    , m_texturePathParkConfirmingTextKeepBrakePedal("cc/vehicle_model/ui/Text_Driver_keep_brake_pedal.png")

    // Free Parking
    , m_texturePathFreeParkingTextStop("cc/vehicle_model/ui/Text_Choose_Free_Parking_Space_At_Secure.png")
    , m_texturePathFreeParkingTextChooseParkingSpaceType("cc/vehicle_model/ui/Text_Choose_Parking_Space_Type.png")
    , m_texturePathFreeParkingTextDescriptionTop("cc/vehicle_model/ui/97_Text_Self_selected_parking_text_description.png")
    , m_texturePathFreeParkingTextDescriptionBottom("cc/vehicle_model/ui/96_Text_Self_selected_parking_operation_guide.png")
    , m_texturePathFreeParkingSelectedParkingInstructions("cc/vehicle_model/ui/98_Text_Self_selected_parking_instructions.png")
    , m_texturePathFreeParkingSelectedParkingInstructionsGrey("cc/vehicle_model/ui/98_Text_Self_selected_parking_instructions_grey.png")
    , m_texturePathFreeParkingSelectedCrossParkingInstructions("cc/vehicle_model/ui/98_Text_Self_selected_parking_instructions_cross.png")
    , m_texturePathFreeParkingSelectedCrossParkingInstructionsGrey("cc/vehicle_model/ui/98_Text_Self_selected_parking_instructions_cross_grey.png")
    , m_texturePathFreeParkingSelectedDiagonalParkingInstructions("cc/vehicle_model/ui/98_Text_Self_selected_parking_instructions_diagonal.png")
    , m_texturePathFreeParkingSelectedDiagonalParkingInstructionsGrey("cc/vehicle_model/ui/98_Text_Self_selected_parking_instructions_diagonal_grey.png")
    , m_texturePathFreeParkingTextDescriptionBottomTrans("cc/vehicle_model/ui/96_Text_Self_selected_parking_operation_guide_trans.png")


    //Hori parking slots & guideline
    , m_texturePathHoriUnselectedSlotVertRight("cc/vehicle_model/ui/178_Horizontal_screen_preview_parking_space_Unselected_vertical_right.png")
    , m_texturePathHoriUnselectedSlotVertLeft("cc/vehicle_model/ui/179_Horizontal_screen_preview_parking_space_Unselected_vertical_left.png")
    , m_texturePathHoriSelectedSlotVertRight("cc/vehicle_model/ui/180_Horizontal_screen_preview_parking_space_Selected_vertical_right.png")
    , m_texturePathHoriUnselectedSlotParaRight("cc/vehicle_model/ui/186_Horizontal_screen_preview_parking_space_Unselected_horizental_right.png")
    , m_texturePathHoriUnselectedSlotParaLeft("cc/vehicle_model/ui/187_Horizontal_screen_preview_parking_space_Unselected_horizental_left.png")
    , m_texturePathHoriSelectedSlotParaRight("cc/vehicle_model/ui/188_Horizontal_screen_preview_parking_space_selected_horizontal_right.png")
    , m_texturePathHoriSelectedSlotParaLeft("cc/vehicle_model/ui/189_Horizontal_screen_preview_parking_space_selected_horizental_left.png")
    , m_texturePathHoriUnselectedSlotDiagRight("cc/vehicle_model/ui/182_Horizontal_screen_preview_parking_space_Unselected_inclined_right.png")
    , m_texturePathHoriUnselectedSlotDiagLeft("cc/vehicle_model/ui/183_Horizontal_screen_preview_parking_space_Unselected_inclined_left.png")
    , m_texturePathHoriSelectedSlotDiagRight("cc/vehicle_model/ui/184_Horizontal_screen_preview_parking_space_Selected_inclined_right.png")
    , m_texturePathHoriSelectedSlotDiagLeft("cc/vehicle_model/ui/185_Horizontal_screen_preview_parking_space_Selected_inclined_left.png")
    , m_texturePathParkCrossSlotLeft("cc/vehicle_model/vehicleicon/248_RearLeftCross.png")
    , m_texturePathParkCrossSlotRight("cc/vehicle_model/vehicleicon/249_RearRightCross.png")
    , m_texturePathParkParaSlotLeft("cc/vehicle_model/vehicleicon/252_RearLeftPara.png")
    , m_texturePathParkParaSlotRight("cc/vehicle_model/vehicleicon/253_RearRightPara.png")
    , m_texturePathParkDiagSlotLeft("cc/vehicle_model/vehicleicon/250_RearLeftDig.png")
    , m_texturePathParkDiagSlotRight("cc/vehicle_model/vehicleicon/251_RearRightDig.png")
    , m_texturePathHoriCombinationDiagRightFrontIn("cc/vehicle_model/ui/218_Horizontal_view_combination_of_diagonal_slot_frontin_right.png")
    , m_texturePathHoriCombinationDiagLeftFrontIn("cc/vehicle_model/ui/219_Horizontal_view_combination_of_diagonal_slot_frontin_left.png")
    , m_texturePathHoriCombinationDiagRightRearIn("cc/vehicle_model/ui/220_Horizontal_view_combination_of_diagonal_slot_rearin_right.png")
    , m_texturePathHoriCombinationDiagLeftRearIn("cc/vehicle_model/ui/221_Horizontal_view_combination_of_diagonal_slot_rearin_left.png")
    , m_texturePathHoriCompleteCombinationDiagRightFrontIn("cc/vehicle_model/ui/222_Horizontal_view_complete_combination_of_diagonal_slot_frontin_right.png")
    , m_texturePathHoriCompleteCombinationDiagLeftFrontIn("cc/vehicle_model/ui/223_Horizontal_view_complete_combination_of_diagonal_slot_frontin_left.png")
    , m_texturePathHoriCompleteCombinationDiagRightRearIn("cc/vehicle_model/ui/224_Horizontal_view_complete_combination_of_diagonal_slot_rearin_right.png")
    , m_texturePathHoriCompleteCombinationDiagLeftRearIn("cc/vehicle_model/ui/225_Horizontal_view_complete_combination_of_diagonal_slot_rearin_left.png")
    , m_texturePathHoriParkOutGuidanceCombinationParaRight("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_parallel_slot_right.png")
    , m_texturePathParkOutGuidanceCombinationParaLeft("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_parallel_slot_left.png")
    , m_texturePathParkOutGuidanceCombinationParaRightGuidanceActive("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_parallel_slot_right.png")
    , m_texturePathParkOutGuidanceCombinationParaLeftGuidanceActive("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_parallel_slot_left.png")
    , m_texturePathParkOutCompleteCombinationParaRight("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_complete_of_parallel_slot_right.png")
    , m_texturePathParkOutCompleteCombinationParaLeft("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_complete_of_parallel_slot_left.png")
    , m_texturePathHoriParkOutGuidanceCombinationCrossRight("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_cross_slot_right.png")
    , m_texturePathParkOutGuidanceCombinationCrossLeft("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_cross_slot_left.png")
    , m_texturePathParkOutGuidanceCombinationCrossRightGuidanceActive("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_cross_slot_right.png")
    , m_texturePathParkOutGuidanceCombinationCrossLeftGuidanceActive("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_combination_of_cross_slot_left.png")
    , m_texturePathParkOutCompleteCombinationCrossRight("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_complete_of_cross_slot_right.png")
    , m_texturePathParkOutCompleteCombinationCrossLeft("cc/vehicle_model/vehicleicon/226_Horizontal_view_park_out_complete_of_cross_slot_left.png")
    , m_texturePathHoriParkOutSlotParaRight("cc/vehicle_model/ui/208_Horizontal_screen_park_out_Selected_horizental_right.png")
    , m_texturePathParkOutSlotParaLeft("cc/vehicle_model/ui/209_Horizontal_screen_Park_out_Selected_horizental_left.png")
    , m_texturePathParkOutSlotCrossLeft("cc/vehicle_model/ui/209_Horizontal_screen_Park_out_Selected_horizental_left.png")
    , m_texturePathHoriPauseCombinationDiagRightFrontIn("cc/vehicle_model/ui/227_Horizontal_view_combination_of_diagonal_slot_frontin_right.png")
    , m_texturePathHoriPauseCombinationDiagLeftFrontIn("cc/vehicle_model/ui/228_Horizontal_view_combination_of_diagonal_slot_frontin_left_inPause.png")
    , m_texturePathHoriPauseCombinationDiagRightRearIn("cc/vehicle_model/ui/229_Horizontal_view_combination_of_diagonal_slot_rearin_right_inPause.png")
    , m_texturePathHoriPauseCombinationDiagLeftRearIn("cc/vehicle_model/ui/230_Horizontal_view_combination_of_diagonal_slot_rearin_left_inPause.png")

    //button
    , m_texturePathAPAFunctionSelectionButtonParkin("cc/vehicle_model/ui/57_Button_Function_Selection_Parkin.png")
    , m_texturePathAPAFunctionSelectionButtonParkout("cc/vehicle_model/ui/58_Button_Function_Selection_Parkout.png")
    , m_texturePathAPAFunctionSelectionButtonFreeParking("cc/vehicle_model/ui/60_Button_Function_Selection_FreeParking.png")
    , m_texturePathAPAFunctionSelectionButtonParkinAPA("cc/vehicle_model/ui/56_Button_Button_Function_Selection_Parkin_APA.png")

    , m_texturePathAPAFunctionSelectionButtonParkinRPA("cc/vehicle_model/ui/67_Button_Button_Function_Selection_Parkin_RPA.png")
    , m_texturePathParkOutSideParallelLeftButtonUnSelect("cc/vehicle_model/ui/39_Parkout_Button_Unselected_turn_signal_left_parallel.png")
    , m_texturePathParkOutSideParallelLeftButtonSelected("cc/vehicle_model/ui/41_Parkout_Button_selected_turn_signal_left_parallel.png")
    , m_texturePathParkOutSideParallelRightButtonUnSelect("cc/vehicle_model/ui/38_Parkout_Button_Unselected_turn_signal_right_parallel.png")
    , m_texturePathParkOutSideParallelRightButtonSelected("cc/vehicle_model/ui/40_Parkout_Button_selected_turn_signal_right_parallel.png")
    , m_texturePathParkOutSideParallelLeftButtonUnSelectTrans("cc/vehicle_model/ui/39_Parkout_Button_Unselected_turn_signal_left_parallel_trans.png")
    , m_texturePathParkOutSideParallelLeftButtonSelectedTrans("cc/vehicle_model/ui/41_Parkout_Button_selected_turn_signal_left_parallel_trans.png")
    , m_texturePathParkOutSideParallelRightButtonUnSelectTrans("cc/vehicle_model/ui/38_Parkout_Button_Unselected_turn_signal_right_parallel_trans.png")
    , m_texturePathParkOutSideParallelRightButtonSelectedTrans("cc/vehicle_model/ui/40_Parkout_Button_selected_turn_signal_right_parallel_trans.png")
    , m_texturePathParkOutSideCrossLeftButtonUnSelect("cc/vehicle_model/ui/39_Parkout_Button_Unselected_turn_signal_left_cross.png")
    , m_texturePathParkOutSideCrossLeftButtonSelected("cc/vehicle_model/ui/41_Parkout_Button_selected_turn_signal_left_cross.png")
    , m_texturePathParkOutSideCrossRightButtonUnSelect("cc/vehicle_model/ui/38_Parkout_Button_Unselected_turn_signal_right_Cross.png")
    , m_texturePathParkOutSideCrossRightButtonSelected("cc/vehicle_model/ui/40_Parkout_Button_selected_turn_signal_right_Cross.png")
    , m_texturePathParkOutSideCrossLeftButtonUnSelectTrans("cc/vehicle_model/ui/39_Parkout_Button_Unselected_turn_signal_left_cross_trans.png")
    , m_texturePathParkOutSideCrossLeftButtonSelectedTrans("cc/vehicle_model/ui/41_Parkout_Button_selected_turn_signal_left_cross_trans.png")
    , m_texturePathParkOutSideCrossRightButtonUnSelectTrans("cc/vehicle_model/ui/38_Parkout_Button_Unselected_turn_signal_right_cross_trans.png")
    , m_texturePathParkOutSideCrossRightButtonSelectedTrans("cc/vehicle_model/ui/40_Parkout_Button_selected_turn_signal_right_cross_trans.png")
    , m_texturePathParkQuitButton("cc/vehicle_model/ui/66_Button_Quit.png")
    , m_texturePathFreeParkingConfirmButton("cc/vehicle_model/ui/52_Button_OK.png")
    , m_texturePathFreeParkingConfirmButtonGrey("cc/vehicle_model/ui/52_Button_OK_grey.png")
    , m_floatCornerTextureSize(12.0f, 12.0f)
    , m_fullCornerTextureSize(30.0f, 30.0f)

    //themeHu
    , m_themeHu(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)
  {
    // updateTheme(cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI);
  }

  SERIALIZABLE(UISettings)  // PRQA S 6044
  {
    ADD_MEMBER(UIData, camSvFr);
    ADD_MEMBER(UIData, camSvRe);
    ADD_MEMBER(UIData, camFwLe);
    ADD_MEMBER(UIData, camFwRi);
    ADD_MEMBER(UIData, camRwLe);
    ADD_MEMBER(UIData, camRwRi);
    ADD_MEMBER(UIData, camPvFL);
    ADD_MEMBER(UIData, camPvFR);
    ADD_MEMBER(UIData, camPvRL);
    ADD_MEMBER(UIData, camPvRR);
    ADD_MEMBER(UIData, cpcOverlaySwitchPressMR);
    ADD_MEMBER(UIData, cpcOverlaySwitchPressST);
    ADD_MEMBER(UIData, swVersionShowSwitchBMR);
    ADD_MEMBER(UIData, swVersionShowSwitchCMR);
    ADD_MEMBER(UIData, swVersionShowSwitchBST);
    ADD_MEMBER(UIData, swVersionShowSwitchCST);
    ADD_MEMBER(UIData, settingQuit);
    ADD_MEMBER(UIData, settingPPDIS);
    ADD_MEMBER(UIData, settingPPON);
    ADD_MEMBER(UIData, settingAutoCamActiv);
    ADD_MEMBER(UIData, settingVM2D);
    ADD_MEMBER(UIData, settingVM3D);
    ADD_MEMBER(UIData, settingVM);
    ADD_MEMBER(UIData, settingBottomBar);
    ADD_MEMBER(UIData, settingApaInactive);
    ADD_MEMBER(UIData, settingApaActive);
    ADD_MEMBER(UIData, settingApa);
    ADD_MEMBER(UIData, settingOffRoad);
    ADD_MEMBER(UIData, settingWheel);
    ADD_MEMBER(UIData, settingWheelLine);
    ADD_MEMBER(UIData, warnSymbolUssWhole);
    ADD_MEMBER(UIData, warnSymbolUssVert);
    ADD_MEMBER(UIData, vehicleTransIcon);
    ADD_MEMBER(UIData, vehicleTransIconParkActive);
    ADD_MEMBER(UIData, vehicleTransIconVert);

    ADD_MEMBER(UIData, settingTextBoxSafeNotification);
    ADD_MEMBER(UIData, settingTextBoxSafeNotification_vert);

    ADD_MEMBER(UIData, settingParkingPlanIconLeft);
    ADD_MEMBER(UIData, settingParkingPlanIconRight);
    ADD_MEMBER(UIData, settingParkingPlanIconLeft_vert);
    ADD_MEMBER(UIData, settingParkingPlanIconRight_vert);

    ADD_MEMBER(UIData, settingPARKIcon);
    ADD_MEMBER(UIData, settingPARKModeSelected);
    ADD_MEMBER(UIData, settingTextModeSelect);


    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot1L);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot2L);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot3L);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot4L);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot1R);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot2R);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot3R);
    ADD_MEMBER(UIData, settingHoriSearchingVerticalSlot4R);
    ADD_MEMBER(UIData, settingHoriSearchingParallelSlot1L);
    ADD_MEMBER(UIData, settingHoriSearchingParallelSlot2L);
    ADD_MEMBER(UIData, settingHoriSearchingParallelSlot3L);
    ADD_MEMBER(UIData, settingHoriSearchingParallelSlot1R);
    ADD_MEMBER(UIData, settingHoriSearchingParallelSlot2R);
    ADD_MEMBER(UIData, settingHoriSearchingParallelSlot3R);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertRightRearIn_Hori);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertRightFrontIn_Hori);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertLeftRearIn_Hori);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertLeftFrontIn_Hori);
    ADD_MEMBER(UIData, settingHoriSelectedSlotParaRight_Hori);
    ADD_MEMBER(UIData, settingHoriSelectedSlotParaLeft_Hori);
    ADD_MEMBER(UIData, settingHoriGuidelineVertRightRearIn_Hori);
    ADD_MEMBER(UIData, settingHoriGuidelineVertRightFrontIn_Hori);
    ADD_MEMBER(UIData, settingHoriGuidelineVertLeftRearIn_Hori);
    ADD_MEMBER(UIData, settingHoriGuidelineVertLeftFrontIn_Hori);
    ADD_MEMBER(UIData, settingHoriGuidelineParaRight_Hori);
    ADD_MEMBER(UIData, settingHoriGuidelineParaLeft_Hori);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertRightRearIn_Vert);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertRightFrontIn_Vert);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertLeftRearIn_Vert);
    ADD_MEMBER(UIData, settingHoriSelectedSlotVertLeftFrontIn_Vert);
    ADD_MEMBER(UIData, settingHoriSelectedSlotParaRight_Vert);
    ADD_MEMBER(UIData, settingHoriSelectedSlotParaLeft_Vert);
    ADD_MEMBER(UIData, settingHoriGuidelineVertRightRearIn_Vert);
    ADD_MEMBER(UIData, settingHoriGuidelineVertRightFrontIn_Vert);
    ADD_MEMBER(UIData, settingHoriGuidelineVertLeftRearIn_Vert);
    ADD_MEMBER(UIData, settingHoriGuidelineVertLeftFrontIn_Vert);
    ADD_MEMBER(UIData, settingHoriGuidelineParaRight_Vert);
    ADD_MEMBER(UIData, settingHoriGuidelineParaLeft_Vert);
    ADD_MEMBER(UIData, settingHoriCombinationDiagRight);
    ADD_MEMBER(UIData, settingHoriCombinationDiagLeft);
    ADD_MEMBER(UIData, settingHoriCombinationDiagRight_Hori);
    ADD_MEMBER(UIData, settingHoriCombinationDiagLeft_Hori);
    ADD_MEMBER(UIData, settingHoriCombinationDiagRight_Vert);
    ADD_MEMBER(UIData, settingHoriCombinationDiagLeft_Vert);
    ADD_MEMBER(UIData, settingHoriCompleteCombinationDiagRight);
    ADD_MEMBER(UIData, settingHoriCompleteCombinationDiagLeft);
    ADD_MEMBER(UIData, settingHoriCompleteCombinationDiagRight_Hori);
    ADD_MEMBER(UIData, settingHoriCompleteCombinationDiagLeft_Hori);
    ADD_MEMBER(UIData, settingHoriCompleteCombinationDiagRight_Vert);
    ADD_MEMBER(UIData, settingHoriCompleteCombinationDiagLeft_Vert);
    ADD_MEMBER(UIData, settingParkOutSmallAuto);
    ADD_MEMBER(UIData, settingParkOutSmallAuto_Hori);
    ADD_MEMBER(UIData, settingParkOutSmallAuto_Vert);
    ADD_MEMBER(UIData, settingParkOutGuidanceCombinationParaRight_Hori);
    ADD_MEMBER(UIData, settingParkOutGuidanceCombinationParaRight_Vert);
    ADD_MEMBER(UIData, settingParkOutGuidanceCombinationParaLeft_Hori);
    ADD_MEMBER(UIData, settingParkOutGuidanceCombinationParaLeft_Vert);
    ADD_MEMBER(UIData, settingHoriParkOutSlotParaRight);
    ADD_MEMBER(UIData, settingHoriParkOutSlotParaRight_Hori);
    ADD_MEMBER(UIData, settingHoriParkOutSlotParaRight_Vert);
    ADD_MEMBER(UIData, settingParkOutSlotParaLeft_Hori);
    ADD_MEMBER(UIData, settingParkOutSlotParaLeft_Vert);
    ADD_MEMBER(UIData, settingSmallAutoPicPara);
    ADD_MEMBER(UIData, settingSmallAutoPicPara_Hori);
    ADD_MEMBER(UIData, settingSmallAutoPicPara_Vert);
    ADD_MEMBER(UIData, settingSmallAutoPicVertFrontInLeft);
    ADD_MEMBER(UIData, settingSmallAutoPicVertFrontInLeft_Hori);
    ADD_MEMBER(UIData, settingSmallAutoPicVertFrontInLeft_Vert);
    ADD_MEMBER(UIData, settingSmallAutoPicVertFrontInRight);
    ADD_MEMBER(UIData, settingSmallAutoPicVertFrontInRight_Hori);
    ADD_MEMBER(UIData, settingSmallAutoPicVertFrontInRight_Vert);
    ADD_MEMBER(UIData, settingSmallAutoPicVertRearInLeft);
    ADD_MEMBER(UIData, settingSmallAutoPicVertRearInLeft_Hori);
    ADD_MEMBER(UIData, settingSmallAutoPicVertRearInLeft_Vert);
    ADD_MEMBER(UIData, settingSmallAutoPicVertRearInRight);
    ADD_MEMBER(UIData, settingSmallAutoPicVertRearInRight_Hori);
    ADD_MEMBER(UIData, settingSmallAutoPicVertRearInRight_Vert);

    ADD_MEMBER(UIData, settingHoriAPASelected);
    ADD_MEMBER(UIData, settingPARKAutoPic);
    ADD_MEMBER(UIData, settingTextBoxSlotSearching_Hori);
    ADD_MEMBER(UIData, settingParkingUIBackground);
    ADD_MEMBER(UIData, settingParkingUIBackground_Hori);
    ADD_MEMBER(UIData, settingParkingUIBackground_Vert);

    ADD_MEMBER(UIData, settingCrossVehiclePic);
    ADD_MEMBER(UIData, settingSmallParkAutoPic);
    ADD_MEMBER(UIData, settingSmallParkAutoPicHoriCompletedParkOut);
    ADD_MEMBER(UIData, settingStarkParkButton_Hori);
    ADD_MEMBER(UIData, settingGuidanceGearD);
    ADD_MEMBER(UIData, settingSuspendContinueButton_Hori);
    ADD_MEMBER(UIData, settingSuspendQuitButton_Hori);
    ADD_MEMBER(UIData, settingSuspendContinueButton_Vert);
    ADD_MEMBER(UIData, settingSuspendQuitButton_Vert);
    ADD_MEMBER(UIData, settingSuspendContinueButton);
    ADD_MEMBER(UIData, settingSuspendQuitButton);
    ADD_MEMBER(UIData, settingGuideHorParallel);

    ADD_MEMBER(UIData, settingPARKSearchingRoad);
    ADD_MEMBER(UIData, settingPARKSearchingWave1);
    ADD_MEMBER(UIData, settingPARKSearchingWave2);
    ADD_MEMBER(UIData, settingPARKParaSlotValid_Hori);
    ADD_MEMBER(UIData, settingPARKVertSlotValid_Hori);
    ADD_MEMBER(UIData, settingPARKDiagSlotValid_Hori);
    ADD_MEMBER(UIData, settingPARKParaSlotValid_Vert);
    ADD_MEMBER(UIData, settingPARKVertSlotValid_Vert);
    ADD_MEMBER(UIData, settingPARKDiagSlotValid_Vert);
    ADD_MEMBER(UIData, settingPARKParaSlotPos1L);
    ADD_MEMBER(UIData, settingPARKParaSlotPos2L);
    ADD_MEMBER(UIData, settingPARKParaSlotPos3L);
    ADD_MEMBER(UIData, settingPARKParaSlotPos1R);
    ADD_MEMBER(UIData, settingPARKParaSlotPos2R);
    ADD_MEMBER(UIData, settingPARKParaSlotPos3R);
    ADD_MEMBER(UIData, settingPARKSlotPos1L);
    ADD_MEMBER(UIData, settingPARKSlotPos1R);
    ADD_MEMBER(UIData, settingPARKSlotPos2L);
    ADD_MEMBER(UIData, settingPARKSlotPos2R);
    ADD_MEMBER(UIData, settingPARKSlotPos3L);
    ADD_MEMBER(UIData, settingPARKSlotPos3R);
    ADD_MEMBER(UIData, settingPARKSlotPos4L);
    ADD_MEMBER(UIData, settingPARKSlotPos4R);
    ADD_MEMBER(UIData, settingPARKBackground);
    ADD_MEMBER(UIData, settingPARKRPAGuidance);
    ADD_MEMBER(UIData, settingParkFrontIsClearSteeringWheel);
    ADD_MEMBER(UIData, settingParkFrontIsClearVehicleAvailable);
    ADD_MEMBER(UIData, settingPARKFinished);
    ADD_MEMBER(UIData, settingTextBox);

    ADD_MEMBER(UIData, settingSmallSlotAutoPic_Hori);
    ADD_MEMBER(UIData, settingSmallSlotAutoPic_Vert);
    ADD_MEMBER(UIData, settingSmallParkAutoPic_Hori);
    ADD_MEMBER(UIData, settingSmallParkAutoPic_Vert);

    ADD_MEMBER(UIData, settingWheelSeparatorHorizontal);
    ADD_MEMBER(UIData, settingWheelSeparatorVertical);
    //For vehicle Model Switch
    ADD_MEMBER(UIData, settingSuspendContinueButtonPress_DENZA);
    ADD_MEMBER(UIData, settingSuspendContinueButtonPress_MR);
    ADD_MEMBER(UIData, settingParkinQuitButtonPress_DENZA);
    ADD_MEMBER(UIData, settingParkinQuitButtonPress_MR);
    ADD_MEMBER(UIData, settingParkingStartButtonPress_DENZA);
    ADD_MEMBER(UIData, settingParkingStartButtonPress_MR);
    ADD_MEMBER(UIData, settingParkTypeButtonAPAPress_DENZA);
    ADD_MEMBER(UIData, settingParkTypeButtonAPAPress_MR);
    ADD_MEMBER(UIData, settingParkTypeButtonRPAPress_DENZA);
    ADD_MEMBER(UIData, settingParkTypeButtonRPAPress_MR);

    ADD_MEMBER(UIData, settingParkingUICenterIcon);
    ADD_MEMBER(UIData, settingParkingUIGearIcon);
    ADD_MEMBER(UIData, settingParkingUISeachingAutoIcon);
    ADD_MEMBER(UIData, settingParkingUITopTextWhite);
    ADD_MEMBER(UIData, settingParkingUITopTextBlue);
    ADD_MEMBER(UIData, settingQuitIn30SecondText);
    ADD_MEMBER(UIData, settingParkingUIFunctionButton);
    ADD_MEMBER(UIData, settingParkingUIAPARPAButton);
    ADD_MEMBER(UIData, settingParkingStartPauseConfirmButton);
    ADD_MEMBER(UIData, settingParkinContinueButton);
    ADD_MEMBER(UIData, settingParkinContinueButtonPress);
    ADD_MEMBER(UIData, settingParkinQuitButton);
    ADD_MEMBER(UIData, settingParkinQuitButtonPress);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideParallelLeftButton);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideParallelRightButton);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideCrossLeftButton);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideCrossRightButton);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideVehicle);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideSteeringWheel);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideTextBottom);
    ADD_MEMBER(UIData, settingParkingUIRPAMobilePhoneIcon);
    ADD_MEMBER(UIData, settingParkingUIRPATextPrompt);
    ADD_MEMBER(UIData, settingParkingUIRPAPleaseLeaveTheCar);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingTextDescription);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingInstructions);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingOperationGuide);

    ADD_MEMBER(UIData, settingParkingUICenterIcon_Hori);
    ADD_MEMBER(UIData, settingParkingUICenterIcon_Vert);
    ADD_MEMBER(UIData, settingParkingUIGearIcon_Hori);
    ADD_MEMBER(UIData, settingParkingUIGearIcon_MainView_Hori);

    ADD_MEMBER(UIData, settingParkingUIGearIcon_Vert);

    ADD_MEMBER(UIData, settingParkingUISeachingVehicleWithDoors);
    ADD_MEMBER(UIData, settingParkingUIContinueDrivingTextUIPanel);
    ADD_MEMBER(UIData, settingParkingUIContinueDrivingTextMainView);
    ADD_MEMBER(UIData, settingParkingUIContinueDrivingDistance);
    ADD_MEMBER(UIData, settingParkingUIMovesLeftNumberTextUIPanel);
    ADD_MEMBER(UIData, settingParkingUIMovesLeftNumberTextMainView);
    ADD_MEMBER(UIData, settingParkingUISuspendIcon);
    ADD_MEMBER(UIData, settingParkingUISeachingAutoIcon_Hori);
    ADD_MEMBER(UIData, settingParkingUISeachingAutoIcon_Vert);
    ADD_MEMBER(UIData, settingParkingUITopTextWhite_Hori);
    ADD_MEMBER(UIData, settingParkingUITopTextWhite_Vert);
    ADD_MEMBER(UIData, settingParkingUITopTextBlue_Hori);
    ADD_MEMBER(UIData, settingParkingUITopTextBlue_Vert);
    ADD_MEMBER(UIData, settingQuitIn30SecondText_Hori);
    ADD_MEMBER(UIData, settingQuitIn30SecondText_Vert);
    ADD_MEMBER(UIData, settingParkingUIFunctionButton_Hori);
    ADD_MEMBER(UIData, settingParkingUIFunctionButton_Vert);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkinPress_Hori);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkinPress_Vert);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkoutPress_Hori);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkoutPress_Vert);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonFreeParkingPress_Hori);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonFreeParkingPress_Vert);
    ADD_MEMBER(UIData, settingParkTypeButtonAPAPress_Hori);
    ADD_MEMBER(UIData, settingParkTypeButtonAPAPress_Vert);
    ADD_MEMBER(UIData, settingParkTypeButtonRPAPress_Hori);
    ADD_MEMBER(UIData, settingParkTypeButtonRPAPress_Vert);
    ADD_MEMBER(UIData, settingParkingStartPauseConfirmButton_Hori);
    ADD_MEMBER(UIData, settingParkingStartPauseConfirmButton_Vert);
    ADD_MEMBER(UIData, settingParkingStartPauseConfirmButtonPress_Hori);
    ADD_MEMBER(UIData, settingParkingStartPauseConfirmButtonPress_Vert);
    ADD_MEMBER(UIData, settingParkinContinueButton_Hori);
    ADD_MEMBER(UIData, settingParkinContinueButton_Vert);
    ADD_MEMBER(UIData, settingParkinContinueButtonPress_Hori);
    ADD_MEMBER(UIData, settingParkinContinueButtonPress_Vert);
    ADD_MEMBER(UIData, settingParkinQuitButton_Hori);
    ADD_MEMBER(UIData, settingParkinQuitButton_Vert);
    ADD_MEMBER(UIData, settingParkinQuitButtonPress_Hori);
    ADD_MEMBER(UIData, settingParkinQuitButtonPress_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideParallelLeftButton_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideParallelLeftButton_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideParallelRightButton_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideParallelRightButton_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideCrossLeftButton_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideCrossLeftButton_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideCrossRightButton_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideCrossRightButton_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideVehicle_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideVehicle_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideSteeringWheel_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideSteeringWheel_Vert);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideTextBottom_Hori);
    ADD_MEMBER(UIData, settingParkingUIParkOutSideTextBottom_Vert);
    ADD_MEMBER(UIData, settingParkingUIRPAMobilePhoneIcon_Hori);
    ADD_MEMBER(UIData, settingParkingUIRPAMobilePhoneIcon_Vert);
    ADD_MEMBER(UIData, settingParkingUIRPATextPrompt_Hori);
    ADD_MEMBER(UIData, settingParkingUIRPATextPrompt_Vert);
    ADD_MEMBER(UIData, settingParkingUIRPAPleaseLeaveTheCar_Hori);
    ADD_MEMBER(UIData, settingParkingUIRPAPleaseLeaveTheCar_Vert);
    ADD_MEMBER(UIData, settingParkOutSideLeftButtonSelectedPress_Hori);
    ADD_MEMBER(UIData, settingParkOutSideLeftButtonSelectedPress_Vert);
    ADD_MEMBER(UIData, settingParkOutSideRightButtonSelectedPress_Hori);
    ADD_MEMBER(UIData, settingParkOutSideRightButtonSelectedPress_Vert);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeCrossButtonPress_Hori);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeCrossButtonPress_Vert);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeParallelButtonPress_Hori);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeParallelButtonPress_Vert);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeDiagonalButtonPress_Hori);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeDiagonalButtonPress_Vert);
    ADD_MEMBER(UIData, settingSearchingFrontInButtonPress_Hori);
    ADD_MEMBER(UIData, settingSearchingFrontInButtonPress_Vert);
    ADD_MEMBER(UIData, settingSearchingRearInButtonPress_Hori);
    ADD_MEMBER(UIData, settingSearchingRearInButtonPress_Vert);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingTextDescription_Hori);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingTextDescription_Vert);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingInstructions_Hori);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingInstructions_Vert);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingOperationGuide_Hori);
    ADD_MEMBER(UIData, settingParkingUIFreeParkingOperationGuide_Vert);

    //freepark ps type
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeCrossButtonPress_DENZA);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeParallelButtonPress_DENZA);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeDiagonalButtonPress_DENZA);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeCrossButtonPress_MR);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeParallelButtonPress_MR);
    ADD_MEMBER(UIData, settingFreeParkingSpaceTypeDiagonalButtonPress_MR);

    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkinPress_DENZA);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkoutPress_DENZA);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonFreeParkingPress_DENZA);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkinPress_MR);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonParkoutPress_MR);
    ADD_MEMBER(UIData, settingFunctionSelectionButtonFreeParkingPress_MR);

    ADD_MEMBER(UIData, settingParkOutSideLeftButtonSelectedPress_DENZA);
    ADD_MEMBER(UIData, settingParkOutSideRightButtonSelectedPress_DENZA);
    ADD_MEMBER(UIData, settingParkOutSideLeftButtonSelectedPress_MR);
    ADD_MEMBER(UIData, settingParkOutSideRightButtonSelectedPress_MR);
    ADD_MEMBER(UIData, settingParkOutSideParallelLeftButtonSelectedPress_DFC);
    ADD_MEMBER(UIData, settingParkOutSideParallelRightButtonSelectedPress_DFC);
    ADD_MEMBER(UIData, settingParkOutSideCrossLeftButtonSelectedPress_DFC);
    ADD_MEMBER(UIData, settingParkOutSideCrossRightButtonSelectedPress_DFC);
    ADD_MEMBER(UIData, viewInfoSingleFront);
    ADD_MEMBER(UIData, viewInfoSingleRear);
    ADD_MEMBER(UIData, viewInfoFrontWheel);
    ADD_MEMBER(UIData, viewInfoRearWheel);
    ADD_MEMBER(UIData, viewInfoFrontJunction);
    ADD_MEMBER(UIData, viewInfoRearJunction);
    ADD_MEMBER(UIData, viewInfoSTB);
    ADD_MEMBER(UIData, pleaseCareSurrounding);
    ADD_MEMBER(UIData, frontWheelLimitLine);
    ADD_MEMBER(UIData, rearWheelLimitLine);
    ADD_MEMBER(UIData, degrationNotCalirated);
    ADD_MEMBER(UIData, degrationNotCaliratedJunctionView);
    ADD_MEMBER(UIData, degrationAndroidError);
    ADD_MEMBER(UIData, degrationAndroidErrorJunctionView);
    ADD_MEMBER(UIData, degrationPlanviewIcon);
    ADD_MEMBER(UIData, viewInfoSingleFrontFloat);
    ADD_MEMBER(UIData, viewInfoSingleRearFloat);
    ADD_MEMBER(UIData, viewInfoFrontWheelFloat);
    ADD_MEMBER(UIData, viewInfoRearWheelFloat);
    ADD_MEMBER(UIData, pleaseCareSurroundingFloatPlan);
    ADD_MEMBER(UIData, pleaseCareSurroundingFloatFR);
    ADD_MEMBER(UIData, FullScreenBlackLineIcon);
    ADD_MEMBER(UIData, FloatScreenBlackLineIcon);
    ADD_MEMBER(UIData, degrationNotCaliratedFloat);
    ADD_MEMBER(UIData, degrationAndroidErrorFloat);
    ADD_MEMBER(UIData, degrationPlanviewIconFloat);
    ADD_MEMBER(UIData, frontWheelLimitLineFloat);
    ADD_MEMBER(UIData, rearWheelLimitLineFloat);

    ADD_STRING_MEMBER(texturePathCamV);
    ADD_STRING_MEMBER(texturePathCamH);
    ADD_STRING_MEMBER(texturePathCamO);
    ADD_STRING_MEMBER(texturePathCamVActive);
    ADD_STRING_MEMBER(texturePathCamHActive);
    ADD_STRING_MEMBER(texturePathCamOActive);
    ADD_STRING_MEMBER(texturePathQuit);
    ADD_STRING_MEMBER(texturePathPPDIS);
    ADD_STRING_MEMBER(texturePathPPON);
    ADD_STRING_MEMBER(texturePathPPOFF);
    ADD_STRING_MEMBER(texturePathAutoCamActivON);
    ADD_STRING_MEMBER(texturePathAutoCamActivOFF);
    ADD_STRING_MEMBER(texturePathVM2D);
    ADD_STRING_MEMBER(texturePathVM2DOFF);
    ADD_STRING_MEMBER(texturePathVM3D);
    ADD_STRING_MEMBER(texturePathVM3DOFF);
    ADD_STRING_MEMBER(texturePathOffRoad);
    ADD_STRING_MEMBER(texturePathOffRoadOFF);
    ADD_STRING_MEMBER(texturePathWheel);
    ADD_STRING_MEMBER(texturePathWheelOFF);
    ADD_STRING_MEMBER(texturePathWheelLine);
    ADD_STRING_MEMBER(texturePathWarnSymbolUssWhole);
    ADD_STRING_MEMBER(texturePathWarnSymbolUssVert);
    ADD_STRING_MEMBER(texturePathVehicleTransIcon);
    ADD_STRING_MEMBER(texturePathVehicleTransIconVert);
    ADD_STRING_MEMBER(texturePathParkIcon);
    ADD_STRING_MEMBER(texturePathParkIconOFF);
    ADD_STRING_MEMBER(texturePathParkModePPSCIn);
    ADD_STRING_MEMBER(texturePathParkModeCPSCIn);
    ADD_STRING_MEMBER(texturePathParkModeOut);
    ADD_STRING_MEMBER(texturePathParkModeSelected);
    ADD_STRING_MEMBER(texturePathParkSearchingRoad);
    ADD_STRING_MEMBER(texturePathParkSearchingWave1);
    ADD_STRING_MEMBER(texturePathParkSearchingWave2);
    ADD_STRING_MEMBER(texturePathParkParaSlotValid);
    ADD_STRING_MEMBER(texturePathParkVertSlotValid);
    ADD_STRING_MEMBER(texturePathParkBackground);
    ADD_STRING_MEMBER(texturePathParkRPAGuidance);
    ADD_STRING_MEMBER(texturePathParkAutoPic);
    ADD_STRING_MEMBER(texturePathParkBoxGearD);
    ADD_STRING_MEMBER(texturePathTextBoxTurnLever);
    ADD_STRING_MEMBER(texturePathTextBoxExitGearR);
    ADD_STRING_MEMBER(texturePathTextBoxSlowDown);
    ADD_STRING_MEMBER(texturePathTextBoxStop);
    ADD_STRING_MEMBER(texturePathTextBoxPsIDSelection);
    ADD_STRING_MEMBER(texturePathTextBoxDoorToCloseinSearching);
    ADD_STRING_MEMBER(texturePathTextBoxFunctionOff);
    ADD_STRING_MEMBER(texturePathTextBoxCloseTrunk);
    ADD_STRING_MEMBER(texturePathTextBoxSlotSearching);
    ADD_STRING_MEMBER(texturePathTextBoxSlotSearchingUnder10);
    ADD_STRING_MEMBER(texturePathTextBoxOnGoing);
    ADD_STRING_MEMBER(texturePathTextBoxAccAeb);
    ADD_STRING_MEMBER(texturePathTextBoxCompleted);
    ADD_STRING_MEMBER(texturePathTextBoxDriverDoorOpen);
    ADD_STRING_MEMBER(texturePathTextBoxEPBApply);
    ADD_STRING_MEMBER(texturePathTextBoxExternalEcuActive);
    ADD_STRING_MEMBER(texturePathTextBoxExternalEcuFailure);
    ADD_STRING_MEMBER(texturePathTextBoxGasPedal);
    ADD_STRING_MEMBER(texturePathTextBoxGearInterrupt);
    ADD_STRING_MEMBER(texturePathTextBoxGearIntervention);
    ADD_STRING_MEMBER(texturePathTextBoxMovetimesOverflow);
    ADD_STRING_MEMBER(texturePathTextBoxObjInTraj);
    ADD_STRING_MEMBER(texturePathTextBoxPasFailure);
    ADD_STRING_MEMBER(texturePathTextBoxRecovertimesOverflow);
    ADD_STRING_MEMBER(texturePathTextBoxSeatbeltUnbuckle);
    ADD_STRING_MEMBER(texturePathTextBoxSpaceLimit);
    ADD_STRING_MEMBER(texturePathTextBoxSpeedHigh);
    ADD_STRING_MEMBER(texturePathTextBoxSteeringwheelHandon);
    ADD_STRING_MEMBER(texturePathTextBoxTimingOverflow);
    ADD_STRING_MEMBER(texturePathTextBoxTrajOutRange);
    ADD_STRING_MEMBER(texturePathTextBoxTrunkdoorOpen);
    ADD_STRING_MEMBER(texturePathTextBoxVehicleBlock);
    ADD_STRING_MEMBER(texturePathTextBoxOtherReason);
    ADD_STRING_MEMBER(texturePathTextBoxPressDeadmanSwitch);
    ADD_STRING_MEMBER(texturePathTextBoxReleaseBrake);
    ADD_STRING_MEMBER(texturePathTextBoxSearchingProcess);
    ADD_STRING_MEMBER(texturePathTextBoxSurroundView);
    ADD_STRING_MEMBER(texturePathTextBoxUnsafeBehavior);
    ADD_STRING_MEMBER(texturePathTextBoxDoorToClose);
    ADD_STRING_MEMBER(texturePathTextBoxQuitTJAHWA);
    ADD_STRING_MEMBER(texturePathTextBoxQuitObjnotExist);
    ADD_STRING_MEMBER(texturePathTextBoxFailure);
    ADD_STRING_MEMBER(texturePathTextModeSelect);
    ADD_STRING_MEMBER(texturePathParkFinished);
    ADD_STRING_MEMBER(texturePathWheelSeparatorHorizontal);
    ADD_STRING_MEMBER(texturePathWheelSeparatorVertical);
    ADD_STRING_MEMBER(texturePathViewInfoSingleFront);
    ADD_STRING_MEMBER(texturePathViewInfoSingleRear);
    ADD_STRING_MEMBER(texturePathViewInfoFrontWheel);
    ADD_STRING_MEMBER(texturePathViewInfoRearWheel);
    ADD_STRING_MEMBER(texturePathViewInfoFrontJunction);
    ADD_STRING_MEMBER(texturePathViewInfoRearJunction);
    ADD_STRING_MEMBER(texturePathViewInfoSTB);
    ADD_STRING_MEMBER(texturePathPleaseCareSurrounding);
    ADD_STRING_MEMBER(texturePathFrontWheelLimitLine);
    ADD_MEMBER(osg::Vec2f, floatCornerTextureSize);
    ADD_MEMBER(osg::Vec2f, fullCornerTextureSize);
  }

  void updateTheme(cc::target::common::EThemeTypeHU f_theme)  // PRQA S 6044
  {
    // m_themeHu = f_theme;
    if (f_theme == cc::target::common::EThemeTypeHU::ETHEME_TYPE_HORI)  // horizontal layout
    {
      m_texturePathParkingUIBackground = "cc/vehicle_model/ui/133_Background_Horizontal.png";
      m_settingParkingUIBackground.m_iconSize = getImageSizeHori(m_texturePathParkingUIBackground);
      m_settingParkingUIBackground.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIBackground_Hori.m_iconCenter);
      m_settingParkingUIBackground.m_isHoriScreen = true;

      m_settingParkingUIFunctionButton.m_iconSize = getImageSizeHori(m_texturePathAPAFunctionSelectionButtonParkin);
      m_settingParkingUIFunctionButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIFunctionButton_Hori.m_iconCenter);
      m_settingParkingUIFunctionButton.m_isHoriScreen = true;

      m_settingParkingUIAPARPAButton.m_iconSize = getImageSizeHori(m_texturePathAPAFunctionSelectionButtonParkinAPA);
      m_settingParkingUIAPARPAButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIFunctionButton_Hori.m_iconCenter);
      m_settingParkingUIAPARPAButton.m_isHoriScreen = true;

      m_settingParkingUIGearIcon.m_iconSize = getImageSizeHori(m_texturePathGuidanceGearD);
      m_settingParkingUIGearIcon.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIGearIcon_Hori.m_iconCenter);
      m_settingParkingUIGearIcon.m_isHoriScreen = true;

      // ! parking slots
      m_settingParallelSlot.m_iconSize = getImageSizeHori(m_texturePathHoriUnselectedSlotParaRight);
      m_settingVerticalSlot.m_iconSize = getImageSizeHori(m_texturePathHoriUnselectedSlotVertRight);
      m_settingDiagonalSlot.m_iconSize = getImageSizeHori(m_texturePathHoriUnselectedSlotDiagRight);
      m_settingHoriSearchingVerticalSlot1L.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot2L.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot3L.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot4L.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot1R.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot2R.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot3R.m_isHoriScreen = true;
      m_settingHoriSearchingVerticalSlot4R.m_isHoriScreen = true;
      m_settingSmallAutoPicPara             = m_settingSmallAutoPicPara_Hori;
      m_settingSmallAutoPicVertFrontInLeft  = m_settingSmallAutoPicVertFrontInLeft_Hori;
      m_settingSmallAutoPicVertFrontInRight = m_settingSmallAutoPicVertFrontInRight_Hori;
      m_settingSmallAutoPicVertRearInLeft   = m_settingSmallAutoPicVertRearInLeft_Hori;
      m_settingSmallAutoPicVertRearInRight  = m_settingSmallAutoPicVertRearInRight_Hori;
      m_settingHoriSelectedSlotVertRightRearIn.m_iconSize  = getImageSizeHori(m_texturePathHoriSelectedSlotVertRight);
      m_settingHoriSelectedSlotVertLeftRearIn.m_iconSize   = getImageSizeHori(m_texturePathHoriSelectedSlotVertRight);
      m_settingHoriSelectedSlotParaRight.m_iconSize        = getImageSizeHori(m_texturePathHoriSelectedSlotParaRight);
      m_settingHoriSelectedSlotParaLeft.m_iconSize         = getImageSizeHori(m_texturePathHoriSelectedSlotParaLeft);
      m_settingHoriSelectedSlotVertRightRearIn.m_iconCenter  = transferToBottomLeftHori(m_settingHoriSelectedSlotVertRightRearIn_Hori.m_iconCenter);
      m_settingHoriSelectedSlotVertRightFrontIn.m_iconCenter = transferToBottomLeftHori(m_settingHoriSelectedSlotVertRightFrontIn_Hori.m_iconCenter);
      m_settingHoriSelectedSlotVertLeftRearIn.m_iconCenter   = transferToBottomLeftHori(m_settingHoriSelectedSlotVertLeftRearIn_Hori.m_iconCenter);
      m_settingHoriSelectedSlotVertLeftFrontIn.m_iconCenter  = transferToBottomLeftHori(m_settingHoriSelectedSlotVertLeftFrontIn_Hori.m_iconCenter);
      m_settingHoriSelectedSlotParaRight.m_iconCenter        = transferToBottomLeftHori(m_settingHoriSelectedSlotParaRight_Hori.m_iconCenter);
      m_settingHoriSelectedSlotParaLeft.m_iconCenter         = transferToBottomLeftHori(m_settingHoriSelectedSlotParaLeft_Hori.m_iconCenter);
      m_settingHoriGuidelineVertRightRearIn.m_iconCenter     = transferToBottomLeftHori(m_settingHoriGuidelineVertRightRearIn_Hori.m_iconCenter );
      m_settingHoriGuidelineVertRightFrontIn.m_iconCenter    = transferToBottomLeftHori(m_settingHoriGuidelineVertRightFrontIn_Hori.m_iconCenter);
      m_settingHoriGuidelineVertLeftRearIn.m_iconCenter      = transferToBottomLeftHori(m_settingHoriGuidelineVertLeftRearIn_Hori.m_iconCenter);
      m_settingHoriGuidelineVertLeftFrontIn.m_iconCenter     = transferToBottomLeftHori(m_settingHoriGuidelineVertLeftFrontIn_Hori.m_iconCenter);
      m_settingHoriGuidelineParaRight.m_iconCenter           = transferToBottomLeftHori(m_settingHoriGuidelineParaRight_Hori.m_iconCenter);
      m_settingHoriGuidelineParaLeft.m_iconCenter            = transferToBottomLeftHori(m_settingHoriGuidelineParaLeft_Hori.m_iconCenter);
      m_settingHoriGuidelineVertRightRearIn.m_isHoriScreen     = true;
      m_settingHoriGuidelineVertLeftRearIn.m_isHoriScreen      = true;
      m_settingHoriGuidelineVertRightFrontIn.m_isHoriScreen    = true;
      m_settingHoriGuidelineVertLeftFrontIn.m_isHoriScreen     = true;
      m_settingHoriGuidelineParaRight.m_isHoriScreen           = true;
      m_settingHoriGuidelineParaLeft.m_isHoriScreen            = true;
      m_settingHoriSelectedSlotVertRightRearIn.m_isHoriScreen  = true;
      m_settingHoriSelectedSlotVertLeftRearIn.m_isHoriScreen   = true;
      m_settingHoriSelectedSlotVertRightFrontIn.m_isHoriScreen = true;
      m_settingHoriSelectedSlotVertLeftFrontIn.m_isHoriScreen  = true;
      m_settingHoriSelectedSlotParaRight.m_isHoriScreen        = true;
      m_settingHoriSelectedSlotParaLeft.m_isHoriScreen         = true;
      m_settingHoriCombinationDiagRight.m_iconSize     = getImageSizeHori(m_texturePathHoriCombinationDiagRightFrontIn);
      m_settingHoriCombinationDiagLeft.m_iconSize      = getImageSizeHori(m_texturePathHoriCombinationDiagLeftFrontIn);
      m_settingHoriCombinationDiagRight.m_iconCenter   = transferToBottomLeftHori(m_settingHoriCombinationDiagRight_Hori.m_iconCenter);
      m_settingHoriCombinationDiagLeft.m_iconCenter    = transferToBottomLeftHori(m_settingHoriCombinationDiagLeft_Hori.m_iconCenter);
      m_settingHoriCombinationDiagRight.m_isHoriScreen = true;
      m_settingHoriCombinationDiagLeft.m_isHoriScreen  = true;
      m_settingHoriCompleteCombinationDiagRight.m_iconSize     = getImageSizeHori(m_texturePathHoriCompleteCombinationDiagRightFrontIn);
      m_settingHoriCompleteCombinationDiagLeft.m_iconSize      = getImageSizeHori(m_texturePathHoriCompleteCombinationDiagLeftFrontIn);
      m_settingHoriCompleteCombinationDiagRight.m_iconCenter   = transferToBottomLeftHori(m_settingHoriCompleteCombinationDiagRight_Hori.m_iconCenter);
      m_settingHoriCompleteCombinationDiagLeft.m_iconCenter    = transferToBottomLeftHori(m_settingHoriCompleteCombinationDiagLeft_Hori.m_iconCenter);
      m_settingHoriCompleteCombinationDiagRight.m_isHoriScreen = true;
      m_settingHoriCompleteCombinationDiagLeft.m_isHoriScreen  = true;
      m_settingParkOutGuidanceCombinationParaRight.m_iconSize = getImageSizeHori(m_texturePathHoriParkOutGuidanceCombinationParaRight);
      m_settingParkOutGuidanceCombinationParaLeft.m_iconSize  = getImageSizeHori(m_texturePathParkOutGuidanceCombinationParaLeft);
      m_settingParkOutGuidanceCombinationParaRight.m_iconCenter = transferToBottomLeftHori(m_settingParkOutGuidanceCombinationParaRight_Hori.m_iconCenter);
      m_settingParkOutGuidanceCombinationParaLeft.m_iconCenter = transferToBottomLeftHori(m_settingParkOutGuidanceCombinationParaLeft_Hori.m_iconCenter);
      m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconSize = getImageSizeHori(m_texturePathParkOutGuidanceCombinationParaRightGuidanceActive);
      m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconSize  = getImageSizeHori(m_texturePathParkOutGuidanceCombinationParaLeftGuidanceActive);
      m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconCenter = transferToBottomLeftHori(m_settingParkOutGuidanceCombinationParaRight_Hori.m_iconCenter);
      m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconCenter = transferToBottomLeftHori(m_settingParkOutGuidanceCombinationParaLeft_Hori.m_iconCenter);
      m_settingParkOutSmallAuto.m_iconCenter                  = transferToBottomLeftHori(m_settingParkOutSmallAuto_Hori.m_iconCenter);
      m_settingHoriParkOutSlotParaRight.m_iconSize            = getImageSizeHori(m_texturePathHoriParkOutSlotParaRight);
      m_settingParkOutSlotParaLeft.m_iconSize                 = getImageSizeHori(m_texturePathParkOutSlotParaLeft);
      m_settingHoriParkOutSlotParaRight.m_iconCenter          = transferToBottomLeftHori(m_settingHoriParkOutSlotParaRight_Hori.m_iconCenter);
      m_settingParkOutSlotParaLeft.m_iconCenter               = transferToBottomLeftHori(m_settingParkOutSlotParaLeft_Hori.m_iconCenter);
      m_settingHoriParkOutSlotParaRight.m_isHoriScreen        = true;
      m_settingParkOutSlotParaLeft.m_isHoriScreen             = true;
      m_settingSuspendContinueButton.m_iconSize     = getImageSizeHori(m_texturePathSearchingRearInButton);
      m_settingSuspendQuitButton.m_iconSize         = getImageSizeHori(m_texturePathSearchingFrontInButton);
      m_settingSuspendContinueButton.m_iconCenter   = transferToBottomLeftHori(m_settingSuspendContinueButton_Hori.m_iconCenter);
      m_settingSuspendQuitButton.m_iconCenter       = transferToBottomLeftHori(m_settingSuspendQuitButton_Hori.m_iconCenter);
      m_settingSuspendContinueButton.m_isHoriScreen = true;
      m_settingSuspendQuitButton.m_isHoriScreen     = true;
      m_settingSmallSlotAutoPic.m_iconCenter = transferToBottomLeftHori(m_settingSmallSlotAutoPic_Hori.m_iconCenter);
      m_settingPARKParaSlotValid = m_settingPARKParaSlotValid_Hori;
      m_settingPARKVertSlotValid = m_settingPARKVertSlotValid_Hori;
      m_settingPARKDiagSlotValid = m_settingPARKDiagSlotValid_Hori;
  //  ! parkout side select
      m_settingParkingUICenterIcon.m_iconSize = getImageSizeHori(m_texturePathParkFrontIsClear);
      m_settingParkingUICenterIcon.m_iconCenter = transferToBottomLeftHori(m_settingParkingUICenterIcon_Hori.m_iconCenter);
      m_settingParkingUICenterIcon.m_isHoriScreen = true;

      m_settingParkingUITopTextBlue.m_iconSize = getImageSizeHori(m_texturePathTextBoxSearchingInLowSpeed);
      m_settingParkingUITopTextBlue.m_iconCenter = transferToBottomLeftHori(m_settingParkingUITopTextBlue_Hori.m_iconCenter);
      m_settingParkingUITopTextBlue.m_isHoriScreen = true;

      m_settingParkingUITopTextWhite.m_iconSize = getImageSizeHori(m_texturePathTextBoxSlotSearching);
      m_settingParkingUITopTextWhite.m_iconCenter = transferToBottomLeftHori(m_settingParkingUITopTextWhite_Hori.m_iconCenter);
      m_settingParkingUITopTextWhite.m_isHoriScreen = true;

      m_settingParkingUIRPAMobilePhoneIcon.m_iconSize = getImageSizeHori(m_texturePathRPAOthersBluetoothmobilephone);
      m_settingParkingUIRPAMobilePhoneIcon.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIRPAMobilePhoneIcon_Hori.m_iconCenter);
      m_settingParkingUIRPAMobilePhoneIcon.m_isHoriScreen = true;

      m_settingParkingUIRPATextPrompt.m_iconSize = getImageSizeHori(m_texturePathRPATextBluetoothConnectedPrompt);
      m_settingParkingUIRPATextPrompt.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIRPATextPrompt_Hori.m_iconCenter);
      m_settingParkingUIRPATextPrompt.m_isHoriScreen = true;

      m_settingParkingUIRPAPleaseLeaveTheCar.m_iconSize = getImageSizeHori(m_texturePathRPATextPleaseLeaveTheCar);
      m_settingParkingUIRPAPleaseLeaveTheCar.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIRPAPleaseLeaveTheCar_Hori.m_iconCenter);
      m_settingParkingUIRPAPleaseLeaveTheCar.m_isHoriScreen = true;

      m_settingParkingStartPauseConfirmButton.m_iconSize = getImageSizeHori(m_texturePathStarkParkOutButton);
      m_settingParkingStartPauseConfirmButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingStartPauseConfirmButton_Hori.m_iconCenter);
      m_settingParkingStartPauseConfirmButton.m_isHoriScreen = true;

      m_settingParkinContinueButton.m_iconSize = getImageSizeHori(m_texturePathSuspendContinueButton);
      m_settingParkinContinueButton.m_iconCenter = transferToBottomLeftHori(m_settingParkinContinueButton_Hori.m_iconCenter);
      m_settingParkinContinueButton.m_isHoriScreen = true;

      m_settingParkinQuitButton.m_iconSize = getImageSizeHori(m_texturePathParkQuitButton);
      m_settingParkinQuitButton.m_iconCenter = transferToBottomLeftHori(m_settingParkinQuitButton_Hori.m_iconCenter);
      m_settingParkinQuitButton.m_isHoriScreen = true;

      m_settingQuitIn30SecondText.m_iconSize = getImageSizeHori(m_texturePathTextBoxQuitIn30s);
      m_settingQuitIn30SecondText.m_iconCenter = transferToBottomLeftHori(m_settingQuitIn30SecondText_Hori.m_iconCenter);
      m_settingQuitIn30SecondText.m_isHoriScreen = true;

      m_settingParkingUISeachingAutoIcon.m_iconSize = getImageSizeHori(m_texturePathParkAutoPic);
      m_settingParkingUISeachingAutoIcon.m_iconCenter = transferToBottomLeftHori(m_settingParkingUISeachingAutoIcon_Hori.m_iconCenter);
      m_settingParkingUISeachingAutoIcon.m_isHoriScreen = true;


      m_settingParkingUISeachingVehicleWithDoors.m_iconSize = getImageSizeHori(m_texturePathSearchingVehicleWithDoors);
      m_settingParkingUISeachingVehicleWithDoors.m_iconCenter = transferToBottomLeftHori(m_settingParkingUISeachingVehicleWithDoors.m_iconCenter);
      m_settingParkingUISeachingVehicleWithDoors.m_isHoriScreen = true;

      m_settingParkingUIContinueDrivingTextUIPanel.m_iconSize = getImageSizeHori(m_texturePathContinueDrivingText);
      m_settingParkingUIContinueDrivingTextUIPanel.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIContinueDrivingTextUIPanel.m_iconCenter);
      m_settingParkingUIContinueDrivingTextUIPanel.m_isHoriScreen = true;

      // m_settingParkingUIContinueDrivingDistance.m_iconSize = getImageSizeHori(m_settingParkingUIContinueDrivingDistance);
      //m_settingParkingUIContinueDrivingDistance.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIContinueDrivingDistance.m_iconCenter);
      //m_settingParkingUIContinueDrivingDistance.m_isHoriScreen = true;

      m_settingParkingUIMovesLeftNumberTextUIPanel.m_iconSize = getImageSizeHori(m_texturePathMovesLeftNumberText);
      m_settingParkingUIMovesLeftNumberTextUIPanel.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIMovesLeftNumberTextUIPanel.m_iconCenter);
      m_settingParkingUIMovesLeftNumberTextUIPanel.m_isHoriScreen = true;

      m_settingParkingUISuspendIcon.m_iconSize = getImageSizeHori(m_texturePathHoriSuspendParkingPause);
      m_settingParkingUISuspendIcon.m_iconCenter = transferToBottomLeftHori(m_settingParkingUISuspendIcon.m_iconCenter);
      m_settingParkingUISuspendIcon.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideParallelLeftButton.m_iconSize = getImageSizeHori(m_texturePathParkOutSideParallelLeftButtonSelected);
      m_settingParkingUIParkOutSideParallelLeftButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideParallelLeftButton_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideParallelLeftButton.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideParallelRightButton.m_iconSize = getImageSizeHori(m_texturePathParkOutSideParallelRightButtonSelected);
      m_settingParkingUIParkOutSideParallelRightButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideParallelRightButton_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideParallelRightButton.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideCrossLeftButton.m_iconSize = getImageSizeHori(m_texturePathParkOutSideCrossLeftButtonSelected);
      m_settingParkingUIParkOutSideCrossLeftButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideCrossLeftButton_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideCrossLeftButton.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideCrossRightButton.m_iconSize = getImageSizeHori(m_texturePathParkOutSideCrossRightButtonSelected);
      m_settingParkingUIParkOutSideCrossRightButton.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideCrossRightButton_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideCrossRightButton.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideVehicle.m_iconSize = getImageSizeHori(m_texturePathSearchingPOCDirecSelectVehicle);
      m_settingParkingUIParkOutSideVehicle.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideVehicle_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideVehicle.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideSteeringWheel.m_iconSize = getImageSizeHori(m_texturePathSearchingPOCDirecSelectSteeringWheel);
      m_settingParkingUIParkOutSideSteeringWheel.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideSteeringWheel_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideSteeringWheel.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideTextBottom.m_iconSize = getImageSizeHori(m_texturePathTextBoxSearchingPOCDirecSelectBottom);
      m_settingParkingUIParkOutSideTextBottom.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideTextBottom_Hori.m_iconCenter);
      m_settingParkingUIParkOutSideTextBottom.m_isHoriScreen = true;

      m_settingParkingUIParkOutSideTextBottomTrans.m_iconSize = getImageSizeHori(m_texturePathTextBoxSearchingPOCDirecSelectBottomTrans);
      m_settingParkingUIParkOutSideTextBottomTrans.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIParkOutSideTextBottom_HoriTrans.m_iconCenter);
      m_settingParkingUIParkOutSideTextBottomTrans.m_isHoriScreen = true;

      m_settingParkingUIFreeParkingTextDescription.m_iconSize = getImageSizeHori(m_texturePathFreeParkingTextDescriptionTop);
      m_settingParkingUIFreeParkingTextDescription.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIFreeParkingTextDescription_Hori.m_iconCenter);
      m_settingParkingUIFreeParkingTextDescription.m_isHoriScreen = true;

      m_settingParkingUIFreeParkingInstructions.m_iconSize = getImageSizeHori(m_texturePathFreeParkingSelectedParkingInstructions);
      m_settingParkingUIFreeParkingInstructions.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIFreeParkingInstructions_Hori.m_iconCenter);
      m_settingParkingUIFreeParkingInstructions.m_isHoriScreen = true;

      m_settingParkingUIFreeParkingOperationGuide.m_iconSize = getImageSizeHori(m_texturePathFreeParkingTextDescriptionBottom);
      m_settingParkingUIFreeParkingOperationGuide.m_iconCenter = transferToBottomLeftHori(m_settingParkingUIFreeParkingOperationGuide_Hori.m_iconCenter);
      m_settingParkingUIFreeParkingOperationGuide.m_isHoriScreen = true;


      m_settingParkFrontIsClearSteeringWheel.m_iconCenter = transferToBottomLeftHori(m_settingParkFrontIsClearSteeringWheel.m_iconCenter);
      m_settingParkFrontIsClearVehicleAvailable.m_iconCenter = transferToBottomLeftHori(m_settingParkFrontIsClearVehicleAvailable.m_iconCenter);

      m_settingCrossVehicleNoSlotPic.m_iconSize     = getImageSizeHori(m_texturePathCrossVehicleNoSlotPic);
      m_settingCrossVehiclePic.m_iconSize           = getImageSizeHori(m_texturePathCrossVehiclePic);
      m_settingParallelVehiclePic.m_iconSize        = getImageSizeHori(m_texturePathParallelVehiclePic);
      m_settingDiagonalVehiclePic.m_iconSize        = getImageSizeHori(m_texturePathDiagonalVehiclePic);
      m_settingCrossVehiclePic.m_iconCenter         = transferToBottomLeftHori(m_settingCrossVehiclePic.m_iconCenter);
      m_settingCrossVehiclePic.m_isHoriScreen       = true;

    }
    else  // vertical layout
    {
      m_texturePathParkingUIBackground = "cc/vehicle_model/ui/134_Background_Vertical.png";
      m_settingParkingUIBackground.m_iconSize = getImageSizeHori(m_texturePathParkingUIBackground); // Don't need to rescale
      m_settingParkingUIBackground.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIBackground_Vert.m_iconCenter);
      m_settingParkingUIBackground.m_isHoriScreen = false;

      m_settingParkingUIFunctionButton.m_iconSize = getImageSizeVert(m_texturePathAPAFunctionSelectionButtonParkin);
      m_settingParkingUIFunctionButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIFunctionButton_Vert.m_iconCenter);
      m_settingParkingUIFunctionButton.m_isHoriScreen = false;

      m_settingParkingUIAPARPAButton.m_iconSize = getImageSizeVert(m_texturePathAPAFunctionSelectionButtonParkinAPA);
      m_settingParkingUIAPARPAButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIFunctionButton_Vert.m_iconCenter);
      m_settingParkingUIAPARPAButton.m_isHoriScreen = false;

      m_settingParkingUIGearIcon.m_iconSize = getImageSizeVert(m_texturePathGuidanceGearD);
      m_settingParkingUIGearIcon.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIGearIcon_Vert.m_iconCenter);
      m_settingParkingUIGearIcon.m_isHoriScreen = false;

      // ! parking slots
      m_settingParallelSlot.m_iconSize = getSlotImageSizeVert(m_texturePathHoriUnselectedSlotParaRight);
      m_settingVerticalSlot.m_iconSize = getSlotImageSizeVert(m_texturePathHoriUnselectedSlotVertRight);
      m_settingDiagonalSlot.m_iconSize = getSlotImageSizeVert(m_texturePathHoriUnselectedSlotDiagRight);
      m_settingHoriSearchingVerticalSlot1L.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot2L.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot3L.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot4L.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot1R.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot2R.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot3R.m_isHoriScreen = false;
      m_settingHoriSearchingVerticalSlot4R.m_isHoriScreen = false;
      m_settingSmallAutoPicPara             = m_settingSmallAutoPicPara_Vert;
      m_settingSmallAutoPicVertFrontInLeft  = m_settingSmallAutoPicVertFrontInLeft_Vert;
      m_settingSmallAutoPicVertFrontInRight = m_settingSmallAutoPicVertFrontInRight_Vert;
      m_settingSmallAutoPicVertRearInLeft   = m_settingSmallAutoPicVertRearInLeft_Vert;
      m_settingSmallAutoPicVertRearInRight  = m_settingSmallAutoPicVertRearInRight_Vert;
      m_settingHoriSelectedSlotVertRightRearIn.m_iconSize  = getImageSizeVert(m_texturePathHoriSelectedSlotVertRight);
      m_settingHoriSelectedSlotVertLeftRearIn.m_iconSize   = getImageSizeVert(m_texturePathHoriSelectedSlotVertRight);
      m_settingHoriSelectedSlotParaRight.m_iconSize        = getImageSizeVert(m_texturePathHoriSelectedSlotParaRight);
      m_settingHoriSelectedSlotParaLeft.m_iconSize         = getImageSizeVert(m_texturePathHoriSelectedSlotParaLeft);
      m_settingHoriSelectedSlotVertRightFrontIn.m_iconCenter = transferToBottomLeftVert(m_settingHoriSelectedSlotVertRightFrontIn_Vert.m_iconCenter);
      m_settingHoriSelectedSlotVertLeftRearIn.m_iconCenter   = transferToBottomLeftVert(m_settingHoriSelectedSlotVertLeftRearIn_Vert.m_iconCenter);
      m_settingHoriSelectedSlotVertLeftFrontIn.m_iconCenter  = transferToBottomLeftVert(m_settingHoriSelectedSlotVertLeftFrontIn_Vert.m_iconCenter);
      m_settingHoriSelectedSlotParaRight.m_iconCenter        = transferToBottomLeftVert(m_settingHoriSelectedSlotParaRight_Vert.m_iconCenter);
      m_settingHoriSelectedSlotParaLeft.m_iconCenter         = transferToBottomLeftVert(m_settingHoriSelectedSlotParaLeft_Vert.m_iconCenter);
      m_settingHoriGuidelineVertRightRearIn.m_iconCenter     = transferToBottomLeftVert(m_settingHoriGuidelineVertRightRearIn_Vert.m_iconCenter );
      m_settingHoriGuidelineVertRightFrontIn.m_iconCenter    = transferToBottomLeftVert(m_settingHoriGuidelineVertRightFrontIn_Vert.m_iconCenter);
      m_settingHoriGuidelineVertLeftRearIn.m_iconCenter      = transferToBottomLeftVert(m_settingHoriGuidelineVertLeftRearIn_Vert.m_iconCenter);
      m_settingHoriGuidelineVertLeftFrontIn.m_iconCenter     = transferToBottomLeftVert(m_settingHoriGuidelineVertLeftFrontIn_Vert.m_iconCenter);
      m_settingHoriGuidelineParaRight.m_iconCenter           = transferToBottomLeftVert(m_settingHoriGuidelineParaRight_Vert.m_iconCenter);
      m_settingHoriGuidelineParaLeft.m_iconCenter            = transferToBottomLeftVert(m_settingHoriGuidelineParaLeft_Vert.m_iconCenter);
      m_settingHoriGuidelineVertRightRearIn.m_isHoriScreen     = false;
      m_settingHoriGuidelineVertLeftRearIn.m_isHoriScreen      = false;
      m_settingHoriGuidelineVertRightFrontIn.m_isHoriScreen    = false;
      m_settingHoriGuidelineVertLeftFrontIn.m_isHoriScreen     = false;
      m_settingHoriGuidelineParaRight.m_isHoriScreen           = false;
      m_settingHoriGuidelineParaLeft.m_isHoriScreen            = false;
      m_settingHoriSelectedSlotVertRightRearIn.m_isHoriScreen  = false;
      m_settingHoriSelectedSlotVertLeftRearIn.m_isHoriScreen   = false;
      m_settingHoriSelectedSlotVertRightFrontIn.m_isHoriScreen = false;
      m_settingHoriSelectedSlotVertLeftFrontIn.m_isHoriScreen  = false;
      m_settingHoriSelectedSlotParaRight.m_isHoriScreen        = false;
      m_settingHoriSelectedSlotParaLeft.m_isHoriScreen         = false;
      m_settingHoriCombinationDiagRight.m_iconSize     = getImageSizeVert(m_texturePathHoriCombinationDiagRightFrontIn);
      m_settingHoriCombinationDiagLeft.m_iconSize      = getImageSizeVert(m_texturePathHoriCombinationDiagLeftFrontIn);
      m_settingHoriCombinationDiagRight.m_iconCenter   = transferToBottomLeftVert(m_settingHoriCombinationDiagRight_Vert.m_iconCenter);
      m_settingHoriCombinationDiagLeft.m_iconCenter    = transferToBottomLeftVert(m_settingHoriCombinationDiagLeft_Vert.m_iconCenter);
      m_settingHoriCombinationDiagRight.m_isHoriScreen = false;
      m_settingHoriCombinationDiagLeft.m_isHoriScreen  = false;
      m_settingHoriCompleteCombinationDiagRight.m_iconSize     = getImageSizeVert(m_texturePathHoriCompleteCombinationDiagRightFrontIn);
      m_settingHoriCompleteCombinationDiagLeft.m_iconSize      = getImageSizeVert(m_texturePathHoriCompleteCombinationDiagLeftFrontIn);
      m_settingHoriCompleteCombinationDiagRight.m_iconCenter   = transferToBottomLeftVert(m_settingHoriCompleteCombinationDiagRight_Vert.m_iconCenter);
      m_settingHoriCompleteCombinationDiagLeft.m_iconCenter    = transferToBottomLeftVert(m_settingHoriCompleteCombinationDiagLeft_Vert.m_iconCenter);
      m_settingHoriCompleteCombinationDiagRight.m_isHoriScreen = false;
      m_settingHoriCompleteCombinationDiagLeft.m_isHoriScreen  = false;
      m_settingParkOutGuidanceCombinationParaRight.m_iconSize = getImageSizeVert(m_texturePathHoriParkOutGuidanceCombinationParaRight);
      m_settingParkOutGuidanceCombinationParaLeft.m_iconSize  = getImageSizeVert(m_texturePathParkOutGuidanceCombinationParaLeft);
      m_settingParkOutGuidanceCombinationParaRight.m_iconCenter = transferToBottomLeftVert(m_settingParkOutGuidanceCombinationParaRight_Vert.m_iconCenter);
      m_settingParkOutGuidanceCombinationParaLeft.m_iconCenter = transferToBottomLeftVert(m_settingParkOutGuidanceCombinationParaLeft_Vert.m_iconCenter);
      m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconSize = getImageSizeHori(m_texturePathParkOutGuidanceCombinationParaRightGuidanceActive);
      m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconSize  = getImageSizeHori(m_texturePathParkOutGuidanceCombinationParaLeftGuidanceActive);
      m_settingParkOutGuidanceCombinationParaRightGuidanceActive.m_iconCenter = transferToBottomLeftHori(m_settingParkOutGuidanceCombinationParaRight_Vert.m_iconCenter);
      m_settingParkOutGuidanceCombinationParaLeftGuidanceActive.m_iconCenter = transferToBottomLeftHori(m_settingParkOutGuidanceCombinationParaLeft_Vert.m_iconCenter);
      m_settingParkOutSmallAuto.m_iconCenter                  = transferToBottomLeftVert(m_settingParkOutSmallAuto_Vert.m_iconCenter);
      m_settingHoriParkOutSlotParaRight.m_iconSize            = getImageSizeVert(m_texturePathHoriParkOutSlotParaRight);
      m_settingParkOutSlotParaLeft.m_iconSize                 = getImageSizeVert(m_texturePathParkOutSlotParaLeft);
      m_settingHoriParkOutSlotParaRight.m_iconCenter          = transferToBottomLeftVert(m_settingHoriParkOutSlotParaRight_Vert.m_iconCenter);
      m_settingParkOutSlotParaLeft.m_iconCenter               = transferToBottomLeftVert(m_settingParkOutSlotParaLeft_Vert.m_iconCenter);
      m_settingHoriParkOutSlotParaRight.m_isHoriScreen        = false;
      m_settingParkOutSlotParaLeft.m_isHoriScreen             = false;
      m_settingSuspendContinueButton.m_iconSize     = getImageSizeVert(m_texturePathSearchingRearInButton);
      m_settingSuspendQuitButton.m_iconSize         = getImageSizeVert(m_texturePathSearchingFrontInButton);
      m_settingSuspendContinueButton.m_iconCenter   = transferToBottomLeftVert(m_settingSuspendContinueButton_Vert.m_iconCenter);
      m_settingSuspendQuitButton.m_iconCenter       = transferToBottomLeftVert(m_settingSuspendQuitButton_Vert.m_iconCenter);
      m_settingSuspendContinueButton.m_isHoriScreen = false;
      m_settingSuspendQuitButton.m_isHoriScreen     = false;
      m_settingSmallSlotAutoPic.m_iconCenter = transferToBottomLeftVert(m_settingSmallSlotAutoPic_Vert.m_iconCenter);
      m_settingPARKParaSlotValid = m_settingPARKParaSlotValid_Vert;
      m_settingPARKVertSlotValid = m_settingPARKVertSlotValid_Vert;
      m_settingPARKDiagSlotValid = m_settingPARKDiagSlotValid_Vert;
      m_settingParkingUICenterIcon.m_iconSize = getImageSizeVert(m_texturePathParkFrontIsClear);
      m_settingParkingUICenterIcon.m_iconCenter = transferToBottomLeftVert(m_settingParkingUICenterIcon_Vert.m_iconCenter);
      m_settingParkingUICenterIcon.m_isHoriScreen = false;

      m_settingParkingUITopTextBlue.m_iconSize = getImageSizeVert(m_texturePathTextBoxSearchingInLowSpeed);
      m_settingParkingUITopTextBlue.m_iconCenter = transferToBottomLeftVert(m_settingParkingUITopTextBlue_Vert.m_iconCenter);
      m_settingParkingUITopTextBlue.m_isHoriScreen = false;

      m_settingParkingUITopTextWhite.m_iconSize = getImageSizeVert(m_texturePathTextBoxSlotSearching);
      m_settingParkingUITopTextWhite.m_iconCenter = transferToBottomLeftVert(m_settingParkingUITopTextWhite_Vert.m_iconCenter);
      m_settingParkingUITopTextWhite.m_isHoriScreen = false;

      m_settingParkingUIRPAMobilePhoneIcon.m_iconSize = getImageSizeVert(m_texturePathRPAOthersBluetoothmobilephone);
      m_settingParkingUIRPAMobilePhoneIcon.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIRPAMobilePhoneIcon_Vert.m_iconCenter);
      m_settingParkingUIRPAMobilePhoneIcon.m_isHoriScreen = false;

      m_settingParkingUIRPATextPrompt.m_iconSize = getImageSizeVert(m_texturePathRPATextBluetoothConnectedPrompt);
      m_settingParkingUIRPATextPrompt.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIRPATextPrompt_Vert.m_iconCenter);
      m_settingParkingUIRPATextPrompt.m_isHoriScreen = false;

      m_settingParkingUIRPAPleaseLeaveTheCar.m_iconSize = getImageSizeVert(m_texturePathRPATextPleaseLeaveTheCar);
      m_settingParkingUIRPAPleaseLeaveTheCar.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIRPAPleaseLeaveTheCar_Vert.m_iconCenter);
      m_settingParkingUIRPAPleaseLeaveTheCar.m_isHoriScreen = false;

      m_settingParkingStartPauseConfirmButton.m_iconSize = getImageSizeVert(m_texturePathStarkParkOutButton);
      m_settingParkingStartPauseConfirmButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingStartPauseConfirmButton_Vert.m_iconCenter);
      m_settingParkingStartPauseConfirmButton.m_isHoriScreen = false;

      m_settingParkinContinueButton.m_iconSize = getImageSizeVert(m_texturePathSuspendContinueButton);
      m_settingParkinContinueButton.m_iconCenter = transferToBottomLeftVert(m_settingParkinContinueButton_Vert.m_iconCenter);
      m_settingParkinContinueButton.m_isHoriScreen = false;

      m_settingParkinQuitButton.m_iconSize = getImageSizeVert(m_texturePathParkQuitButton);
      m_settingParkinQuitButton.m_iconCenter = transferToBottomLeftVert(m_settingParkinQuitButton_Vert.m_iconCenter);
      m_settingParkinQuitButton.m_isHoriScreen = false;

      m_settingQuitIn30SecondText.m_iconSize = getImageSizeVert(m_texturePathTextBoxQuitIn30s);
      m_settingQuitIn30SecondText.m_iconCenter = transferToBottomLeftVert(m_settingQuitIn30SecondText_Vert.m_iconCenter);
      m_settingQuitIn30SecondText.m_isHoriScreen = false;

      m_settingParkingUISeachingAutoIcon.m_iconSize = getImageSizeVert(m_texturePathParkAutoPic);
      m_settingParkingUISeachingAutoIcon.m_iconCenter = transferToBottomLeftVert(m_settingParkingUISeachingAutoIcon_Vert.m_iconCenter);
      m_settingParkingUISeachingAutoIcon.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideParallelLeftButton.m_iconSize = getImageSizeVert(m_texturePathParkOutSideParallelLeftButtonSelected);
      m_settingParkingUIParkOutSideParallelLeftButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideParallelLeftButton_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideParallelLeftButton.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideParallelRightButton.m_iconSize = getImageSizeVert(m_texturePathParkOutSideParallelRightButtonSelected);
      m_settingParkingUIParkOutSideParallelRightButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideParallelRightButton_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideParallelRightButton.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideCrossLeftButton.m_iconSize = getImageSizeVert(m_texturePathParkOutSideCrossLeftButtonSelected);
      m_settingParkingUIParkOutSideCrossLeftButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideCrossLeftButton_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideCrossLeftButton.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideCrossRightButton.m_iconSize = getImageSizeVert(m_texturePathParkOutSideCrossRightButtonSelected);
      m_settingParkingUIParkOutSideCrossRightButton.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideCrossRightButton_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideCrossRightButton.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideVehicle.m_iconSize = getImageSizeVert(m_texturePathSearchingPOCDirecSelectVehicle);
      m_settingParkingUIParkOutSideVehicle.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideVehicle_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideVehicle.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideSteeringWheel.m_iconSize = getImageSizeVert(m_texturePathSearchingPOCDirecSelectSteeringWheel);
      m_settingParkingUIParkOutSideSteeringWheel.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideSteeringWheel_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideSteeringWheel.m_isHoriScreen = false;

      m_settingParkingUIParkOutSideTextBottom.m_iconSize = getImageSizeVert(m_texturePathTextBoxSearchingPOCDirecSelectBottom);
      m_settingParkingUIParkOutSideTextBottom.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIParkOutSideTextBottom_Vert.m_iconCenter);
      m_settingParkingUIParkOutSideTextBottom.m_isHoriScreen = false;


      m_settingParkingUIFreeParkingTextDescription.m_iconSize = getImageSizeVert(m_texturePathFreeParkingTextDescriptionTop);
      m_settingParkingUIFreeParkingTextDescription.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIFreeParkingTextDescription_Vert.m_iconCenter);
      m_settingParkingUIFreeParkingTextDescription.m_isHoriScreen = false;

      m_settingParkingUIFreeParkingInstructions.m_iconSize = getImageSizeVert(m_texturePathFreeParkingSelectedParkingInstructions);
      m_settingParkingUIFreeParkingInstructions.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIFreeParkingInstructions_Vert.m_iconCenter);
      m_settingParkingUIFreeParkingInstructions.m_isHoriScreen = false;

      m_settingParkingUIFreeParkingOperationGuide.m_iconSize = getImageSizeVert(m_texturePathFreeParkingTextDescriptionBottom);
      m_settingParkingUIFreeParkingOperationGuide.m_iconCenter = transferToBottomLeftVert(m_settingParkingUIFreeParkingOperationGuide_Vert.m_iconCenter);
      m_settingParkingUIFreeParkingOperationGuide.m_isHoriScreen = false;


    }
  }


    //////////////////////////////////////////////////////////
   ////                   Horizontal                     ////
  //////////////////////////////////////////////////////////

  // ! camera icons size and positions
  UIData      m_camSvFr;
  UIData      m_camSvRe;
  UIData      m_camFwLe;
  UIData      m_camFwRi;
  UIData      m_camRwLe;
  UIData      m_camRwRi;
  UIData      m_camPvFL;
  UIData      m_camPvFR;
  UIData      m_camPvRL;
  UIData      m_camPvRR;

  // ! cpc information overlay switch
  UIData      m_cpcOverlaySwitchPressMR;
  UIData      m_cpcOverlaySwitchPressST;

  // ! hw/sw version show switch
  UIData      m_swVersionShowSwitchBMR;
  UIData      m_swVersionShowSwitchCMR;
  UIData      m_swVersionShowSwitchBST;
  UIData      m_swVersionShowSwitchCST;

  // ! setting bar icons size and positions
  UIData      m_settingQuit;
  UIData      m_settingPPDIS;
  UIData      m_settingPPON;
  UIData      m_settingAutoCamActiv;
  UIData      m_settingVM2D;
  UIData      m_settingVM3D;
  UIData      m_settingVM;
  UIData      m_settingBottomBar;
  UIData      m_settingApaInactive;
  UIData      m_settingApaActive;
  UIData      m_settingApa;
  UIData      m_settingOffRoad;
  UIData      m_settingWheel;
  UIData      m_settingWheelLine;

  // ! warn symbol icons size and position
  UIData      m_warnSymbolUssWhole;
  UIData      m_warnSymbolUssVert;

  // ! view info icons size and position
  UIData m_viewInfoSingleFront;
  UIData m_viewInfoSingleRear;
  UIData m_viewInfoFrontWheel;
  UIData m_viewInfoRearWheel;
  UIData m_viewInfoFrontJunction;
  UIData m_viewInfoRearJunction;
  UIData m_viewInfoSTB;
  UIData m_pleaseCareSurrounding;
  UIData m_frontWheelLimitLine;
  UIData m_rearWheelLimitLine;
  UIData m_degrationNotCalirated;
  UIData m_degrationNotCaliratedJunctionView;
  UIData m_degrationAndroidError;
  UIData m_degrationAndroidErrorJunctionView;
  UIData m_degrationPlanviewIcon;
  UIData m_viewInfoSingleFrontFloat;
  UIData m_viewInfoSingleRearFloat;
  UIData m_viewInfoFrontWheelFloat;
  UIData m_viewInfoRearWheelFloat;
  UIData m_pleaseCareSurroundingFloatPlan;
  UIData m_pleaseCareSurroundingFloatFR;
  UIData m_frontWheelLimitLineFloat;
  UIData m_rearWheelLimitLineFloat;
  UIData m_FullScreenBlackLineIcon;
  UIData m_FloatScreenBlackLineIcon;
  UIData m_degrationNotCaliratedFloat;
  UIData m_degrationAndroidErrorFloat;
  UIData m_degrationPlanviewIconFloat;
  // ! vehicle trans icons size and position
  UIData      m_vehicleTransIcon;
  UIData      m_vehicleTransIconParkActive;
  UIData      m_vehicleTransIconVert;

  // ! text symbol
  UIData      m_settingTextBoxSafeNotification;
  UIData      m_settingTextBoxSafeNotification_vert;

  // ! parking icon symbol
  UIData      m_settingParkingPlanIconLeft;
  UIData      m_settingParkingPlanIconRight;
  UIData      m_settingParkingPlanIconLeft_vert;
  UIData      m_settingParkingPlanIconRight_vert;

  // ! park icon size and position
  UIData      m_settingPARKIcon;

  // ! park mode select
  UIData      m_settingPARKModeSelected;
  UIData      m_settingTextModeSelect;

  UIData      m_settingParallelSlot;
  UIData      m_settingVerticalSlot;
  UIData      m_settingDiagonalSlot;
  UIData      m_settingHoriSearchingVerticalSlot1L;
  UIData      m_settingHoriSearchingVerticalSlot2L;
  UIData      m_settingHoriSearchingVerticalSlot3L;
  UIData      m_settingHoriSearchingVerticalSlot4L;
  UIData      m_settingHoriSearchingVerticalSlot1R;
  UIData      m_settingHoriSearchingVerticalSlot2R;
  UIData      m_settingHoriSearchingVerticalSlot3R;
  UIData      m_settingHoriSearchingVerticalSlot4R;
  UIData      m_settingHoriSearchingParallelSlot1L;
  UIData      m_settingHoriSearchingParallelSlot2L;
  UIData      m_settingHoriSearchingParallelSlot3L;
  UIData      m_settingHoriSearchingParallelSlot1R;
  UIData      m_settingHoriSearchingParallelSlot2R;
  UIData      m_settingHoriSearchingParallelSlot3R;
  UIData      m_settingHoriSelectedSlotVertRightRearIn;
  UIData      m_settingHoriSelectedSlotVertRightFrontIn;
  UIData      m_settingHoriSelectedSlotVertLeftRearIn;
  UIData      m_settingHoriSelectedSlotVertLeftFrontIn;
  UIData      m_settingHoriSelectedSlotParaRight;
  UIData      m_settingHoriSelectedSlotParaLeft;
  UIData      m_settingHoriGuidelineVertRightRearIn;
  UIData      m_settingHoriGuidelineVertRightFrontIn;
  UIData      m_settingHoriGuidelineVertLeftRearIn;
  UIData      m_settingHoriGuidelineVertLeftFrontIn;
  UIData      m_settingHoriGuidelineParaRight;
  UIData      m_settingHoriGuidelineParaLeft;
  UIData      m_settingHoriSelectedSlotVertRightRearIn_Hori;
  UIData      m_settingHoriSelectedSlotVertRightFrontIn_Hori;
  UIData      m_settingHoriSelectedSlotVertLeftRearIn_Hori;
  UIData      m_settingHoriSelectedSlotVertLeftFrontIn_Hori;
  UIData      m_settingHoriSelectedSlotParaRight_Hori;
  UIData      m_settingHoriSelectedSlotParaLeft_Hori;
  UIData      m_settingHoriGuidelineVertRightRearIn_Hori;
  UIData      m_settingHoriGuidelineVertRightFrontIn_Hori;
  UIData      m_settingHoriGuidelineVertLeftRearIn_Hori;
  UIData      m_settingHoriGuidelineVertLeftFrontIn_Hori;
  UIData      m_settingHoriGuidelineParaRight_Hori;
  UIData      m_settingHoriGuidelineParaLeft_Hori;
  UIData      m_settingHoriSelectedSlotVertRightRearIn_Vert;
  UIData      m_settingHoriSelectedSlotVertRightFrontIn_Vert;
  UIData      m_settingHoriSelectedSlotVertLeftRearIn_Vert;
  UIData      m_settingHoriSelectedSlotVertLeftFrontIn_Vert;
  UIData      m_settingHoriSelectedSlotParaRight_Vert;
  UIData      m_settingHoriSelectedSlotParaLeft_Vert;
  UIData      m_settingHoriGuidelineVertRightRearIn_Vert;
  UIData      m_settingHoriGuidelineVertRightFrontIn_Vert;
  UIData      m_settingHoriGuidelineVertLeftRearIn_Vert;
  UIData      m_settingHoriGuidelineVertLeftFrontIn_Vert;
  UIData      m_settingHoriGuidelineParaRight_Vert;
  UIData      m_settingHoriGuidelineParaLeft_Vert;
  UIData      m_settingHoriCombinationDiagRight;
  UIData      m_settingHoriCombinationDiagLeft;
  UIData      m_settingHoriCombinationDiagRight_Hori;
  UIData      m_settingHoriCombinationDiagLeft_Hori;
  UIData      m_settingHoriCombinationDiagRight_Vert;
  UIData      m_settingHoriCombinationDiagLeft_Vert;
  UIData      m_settingHoriCompleteCombinationDiagRight;
  UIData      m_settingHoriCompleteCombinationDiagLeft;
  UIData      m_settingHoriCompleteCombinationDiagRight_Hori;
  UIData      m_settingHoriCompleteCombinationDiagLeft_Hori;
  UIData      m_settingHoriCompleteCombinationDiagRight_Vert;
  UIData      m_settingHoriCompleteCombinationDiagLeft_Vert;
  UIData      m_settingParkOutGuidanceCombinationParaRight;
  UIData      m_settingParkOutGuidanceCombinationParaRight_Hori;
  UIData      m_settingParkOutGuidanceCombinationParaRight_Vert;
  UIData      m_settingParkOutGuidanceCombinationParaLeft;
  UIData      m_settingParkOutGuidanceCombinationParaLeft_Hori;
  UIData      m_settingParkOutGuidanceCombinationParaLeft_Vert;
  UIData      m_settingParkOutGuidanceCombinationParaRightGuidanceActive;
  UIData      m_settingParkOutGuidanceCombinationParaLeftGuidanceActive;
  UIData      m_settingParkOutSmallAuto;
  UIData      m_settingParkOutSmallAuto_Hori;
  UIData      m_settingParkOutSmallAuto_Vert;
  UIData      m_settingHoriParkOutSlotParaRight;
  UIData      m_settingHoriParkOutSlotParaRight_Hori;
  UIData      m_settingHoriParkOutSlotParaRight_Vert;
  UIData      m_settingParkOutSlotParaLeft;
  UIData      m_settingParkOutSlotParaLeft_Hori;
  UIData      m_settingParkOutSlotParaLeft_Vert;
  UIData      m_settingSmallAutoPicPara;
  UIData      m_settingSmallAutoPicPara_Hori;
  UIData      m_settingSmallAutoPicPara_Vert;
  UIData      m_settingSmallAutoPicVertFrontInLeft;
  UIData      m_settingSmallAutoPicVertFrontInLeft_Hori;
  UIData      m_settingSmallAutoPicVertFrontInLeft_Vert;
  UIData      m_settingSmallAutoPicVertFrontInRight;
  UIData      m_settingSmallAutoPicVertFrontInRight_Hori;
  UIData      m_settingSmallAutoPicVertFrontInRight_Vert;
  UIData      m_settingSmallAutoPicVertRearInLeft;
  UIData      m_settingSmallAutoPicVertRearInLeft_Hori;
  UIData      m_settingSmallAutoPicVertRearInLeft_Vert;
  UIData      m_settingSmallAutoPicVertRearInRight;
  UIData      m_settingSmallAutoPicVertRearInRight_Hori;
  UIData      m_settingSmallAutoPicVertRearInRight_Vert;


  // ! park searching
  UIData      m_settingHoriAPASelected;
  UIData      m_settingPARKAutoPic;
  UIData      m_settingTextBoxSlotSearching_Hori;
  UIData      m_settingParkingUIBackground;
  UIData      m_settingParkingUIBackground_Hori;
  UIData      m_settingParkingUIBackground_Vert;

  // ! start parking/guidance/complete
  UIData      m_settingCrossVehicleNoSlotPic;
  UIData      m_settingCrossVehiclePic;
  UIData      m_settingParallelVehiclePic;
  UIData      m_settingDiagonalVehiclePic;
  UIData      m_settingSmallParkAutoPic;
  UIData      m_settingSmallSlotAutoPic;
  UIData      m_settingSmallParkAutoPicComplete;
  UIData      m_settingSmallParkAutoPicHoriCompletedParkOut;
  UIData      m_settingStarkParkButton_Hori;
  UIData      m_settingGuidanceGearD;
  UIData      m_settingSuspendContinueButton;
  UIData      m_settingSuspendQuitButton;
  UIData      m_settingGuideHorParallel;

  UIData      m_settingPARKSearchingRoad;
  UIData      m_settingPARKSearchingWave1;
  UIData      m_settingPARKSearchingWave2;
  UIData      m_settingPARKParaSlotValid;
  UIData      m_settingPARKVertSlotValid;
  UIData      m_settingPARKDiagSlotValid;
  UIData      m_settingPARKParaSlotValid_Hori;
  UIData      m_settingPARKVertSlotValid_Hori;
  UIData      m_settingPARKDiagSlotValid_Hori;
  UIData      m_settingPARKParaSlotValid_Vert;
  UIData      m_settingPARKVertSlotValid_Vert;
  UIData      m_settingPARKDiagSlotValid_Vert;
  UIData      m_settingPARKParaSlotPos1L;
  UIData      m_settingPARKParaSlotPos2L;
  UIData      m_settingPARKParaSlotPos3L;
  UIData      m_settingPARKParaSlotPos1R;
  UIData      m_settingPARKParaSlotPos2R;
  UIData      m_settingPARKParaSlotPos3R;
  UIData      m_settingPARKSlotPos1L;
  UIData      m_settingPARKSlotPos1R;
  UIData      m_settingPARKSlotPos2L;
  UIData      m_settingPARKSlotPos2R;
  UIData      m_settingPARKSlotPos3L;
  UIData      m_settingPARKSlotPos3R;
  UIData      m_settingPARKSlotPos4L;
  UIData      m_settingPARKSlotPos4R;

  // ! park confirming
  UIData      m_settingPARKBackground;
  UIData      m_settingPARKRPAGuidance;
  UIData      m_settingParkFrontIsClearSteeringWheel;
  UIData      m_settingParkFrontIsClearVehicleAvailable;

  // ! park finished
  UIData      m_settingPARKFinished;
  UIData      m_settingTextBox;

  // ! horizontal pos

  // ! vertical pos
  UIData      m_settingSmallParkAutoPic_Hori;
  UIData      m_settingSmallSlotAutoPic_Hori;
  UIData      m_settingSuspendContinueButton_Hori;
  UIData      m_settingSuspendQuitButton_Hori;

  // ! vertical pos
  UIData      m_settingSmallParkAutoPic_Vert;
  UIData      m_settingSmallSlotAutoPic_Vert;
  UIData      m_settingSuspendContinueButton_Vert;
  UIData      m_settingSuspendQuitButton_Vert;

  // wheel Separator
  UIData      m_settingWheelSeparatorHorizontal;
  UIData      m_settingWheelSeparatorVertical;


    //////////////////////////////////////////////////////////
   ////                   Vehicle Model                  ////
  //////////////////////////////////////////////////////////
  UIData     m_settingParkinQuitButtonPress_DENZA;
  UIData     m_settingParkinQuitButtonPress_MR;
  UIData     m_settingSuspendContinueButtonPress_DENZA;
  UIData     m_settingSuspendContinueButtonPress_MR;
  UIData     m_settingParkingStartButtonPress_DENZA;
  UIData     m_settingParkingStartButtonPress_MR;
  UIData     m_settingParkTypeButtonAPAPress_DENZA;
  UIData     m_settingParkTypeButtonAPAPress_MR;
  UIData     m_settingParkTypeButtonRPAPress_DENZA;
  UIData     m_settingParkTypeButtonRPAPress_MR;

    //////////////////////////////////////////////////////////
   ////                   Common                         ////
  //////////////////////////////////////////////////////////

  UIData      m_settingParkingUICenterIcon;
  UIData      m_settingParkingUIGearIcon;
  UIData      m_settingParkingUISeachingAutoIcon;
  UIData      m_settingParkingUITopTextWhite;
  UIData      m_settingParkingUITopTextBlue;
  UIData      m_settingQuitIn30SecondText;
  UIData      m_settingParkingUIFunctionButton;
  UIData      m_settingParkingUIAPARPAButton;
  UIData      m_settingParkingStartPauseConfirmButton;
  UIData      m_settingParkinContinueButton;
  UIData      m_settingParkinContinueButtonPress;
  UIData      m_settingParkinQuitButton;
  UIData      m_settingParkinQuitButtonPress;
  UIData      m_settingParkingUIParkOutSideParallelLeftButton;
  UIData      m_settingParkingUIParkOutSideParallelRightButton;
  UIData      m_settingParkingUIParkOutSideCrossLeftButton;
  UIData      m_settingParkingUIParkOutSideCrossRightButton;
  UIData      m_settingParkingUIParkOutSideVehicle;
  UIData      m_settingParkingUIParkOutSideSteeringWheel;
  UIData      m_settingParkingUIParkOutSideTextBottom;
  UIData      m_settingParkingUIParkOutSideTextBottomTrans;
  UIData      m_settingParkingUIRPAMobilePhoneIcon;
  UIData      m_settingParkingUIRPATextPrompt;
  UIData      m_settingParkingUIRPAPleaseLeaveTheCar;
  UIData      m_settingParkingUIFreeParkingTextDescription;
  UIData      m_settingParkingUIFreeParkingInstructions;
  UIData      m_settingParkingUIFreeParkingOperationGuide;

    //////////////////////////////////////////////////////////
   ////           Horizontal and Vertical                ////
  //////////////////////////////////////////////////////////

  UIData      m_settingParkingUICenterIcon_Hori;
  UIData      m_settingParkingUICenterIcon_Vert;
  UIData      m_settingParkingUIGearIcon_Hori;
  UIData      m_settingParkingUIGearIcon_MainView_Hori;
  UIData      m_settingParkingUIGearIcon_Vert;
  UIData      m_settingParkingUISeachingVehicleWithDoors;
  UIData      m_settingSearchingTextBoxSelectParkoutDirectionWhenStop;
  UIData      m_settingParkingUIContinueDrivingTextUIPanel;
  UIData      m_settingParkingUIContinueDrivingTextMainView;
  UIData      m_settingParkingUIContinueDrivingDistance;
  UIData      m_settingParkingUIMovesLeftNumberTextUIPanel;
  UIData      m_settingParkingUIMovesLeftNumberTextMainView;
  UIData      m_settingParkingUISuspendIcon;
  UIData      m_settingParkingUISeachingAutoIcon_Hori;
  UIData      m_settingParkingUISeachingAutoIcon_Vert;
  UIData      m_settingParkingUITopTextWhite_Hori;
  UIData      m_settingParkingUITopTextWhite_Vert;
  UIData      m_settingParkingUITopTextBlue_Hori;
  UIData      m_settingParkingUITopTextBlue_Vert;
  UIData      m_settingQuitIn30SecondText_Hori;
  UIData      m_settingQuitIn30SecondText_Vert;
  UIData      m_settingParkingUIFunctionButton_Hori;
  UIData      m_settingParkingUIFunctionButton_Vert;
  UIData      m_settingFunctionSelectionButtonParkinPress_Hori;
  UIData      m_settingFunctionSelectionButtonParkinPress_Vert;
  UIData      m_settingFunctionSelectionButtonParkoutPress_Hori;
  UIData      m_settingFunctionSelectionButtonParkoutPress_Vert;
  UIData      m_settingFunctionSelectionButtonFreeParkingPress_Hori;
  UIData      m_settingFunctionSelectionButtonFreeParkingPress_Vert;
  UIData      m_settingParkTypeButtonAPAPress_Hori;
  UIData      m_settingParkTypeButtonAPAPress_Vert;
  UIData      m_settingParkTypeButtonRPAPress_Hori;
  UIData      m_settingParkTypeButtonRPAPress_Vert;
  UIData      m_settingParkingStartPauseConfirmButton_Hori;
  UIData      m_settingParkingStartPauseConfirmButton_Vert;
  UIData      m_settingParkingStartPauseConfirmButtonPress_Hori;
  UIData      m_settingParkingStartPauseConfirmButtonPress_Vert;
  UIData      m_settingParkinContinueButton_Hori;
  UIData      m_settingParkinContinueButton_Vert;
  UIData      m_settingParkinContinueButtonPress_Hori;
  UIData      m_settingParkinContinueButtonPress_Vert;
  UIData      m_settingParkinQuitButton_Hori;
  UIData      m_settingParkinQuitButton_Vert;
  UIData      m_settingParkinQuitButtonPress_Hori;
  UIData      m_settingParkinQuitButtonPress_Vert;
  UIData      m_settingParkingUIParkOutSideParallelLeftButton_Hori;
  UIData      m_settingParkingUIParkOutSideParallelLeftButton_Vert;
  UIData      m_settingParkingUIParkOutSideParallelRightButton_Hori;
  UIData      m_settingParkingUIParkOutSideParallelRightButton_Vert;
  UIData      m_settingParkingUIParkOutSideCrossLeftButton_Hori;
  UIData      m_settingParkingUIParkOutSideCrossLeftButton_Vert;
  UIData      m_settingParkingUIParkOutSideCrossRightButton_Hori;
  UIData      m_settingParkingUIParkOutSideCrossRightButton_Vert;
  UIData      m_settingParkingUIParkOutSideVehicle_Hori;
  UIData      m_settingParkingUIParkOutSideVehicle_Vert;
  UIData      m_settingParkingUIParkOutSideSteeringWheel_Hori;
  UIData      m_settingParkingUIParkOutSideSteeringWheel_Vert;
  UIData      m_settingParkingUIParkOutSideTextBottom_Hori;
  UIData      m_settingParkingUIParkOutSideTextBottom_HoriTrans;
  UIData      m_settingParkingUIParkOutSideTextBottom_Vert;
  UIData      m_settingParkingUIRPAMobilePhoneIcon_Hori;
  UIData      m_settingParkingUIRPAMobilePhoneIcon_Vert;
  UIData      m_settingParkingUIRPATextPrompt_Hori;
  UIData      m_settingParkingUIRPATextPrompt_Vert;
  UIData      m_settingParkingUIRPAPleaseLeaveTheCar_Hori;
  UIData      m_settingParkingUIRPAPleaseLeaveTheCar_Vert;
  UIData      m_settingParkOutSideLeftButtonSelectedPress_Hori;
  UIData      m_settingParkOutSideLeftButtonSelectedPress_Vert;
  UIData      m_settingParkOutSideRightButtonSelectedPress_Hori;
  UIData      m_settingParkOutSideRightButtonSelectedPress_Vert;
  UIData      m_settingParkOutSideCrossLeftButtonSelectedPress_Hori;
  UIData      m_settingParkOutSideCrossLeftButtonSelectedPress_Vert;
  UIData      m_settingParkOutSideCrossRightButtonSelectedPress_Hori;
  UIData      m_settingParkOutSideCrossRightButtonSelectedPress_Vert;
  UIData      m_settingFreeParkingSpaceTypeCrossButtonPress_Hori;
  UIData      m_settingFreeParkingSpaceTypeCrossButtonPress_Vert;
  UIData      m_settingFreeParkingSpaceTypeParallelButtonPress_Hori;
  UIData      m_settingFreeParkingSpaceTypeParallelButtonPress_Vert;
  UIData      m_settingFreeParkingSpaceTypeDiagonalButtonPress_Hori;
  UIData      m_settingFreeParkingSpaceTypeDiagonalButtonPress_Vert;
  UIData      m_settingSearchingFrontInButtonPress_Hori;
  UIData      m_settingSearchingFrontInButtonPress_Vert;
  UIData      m_settingSearchingRearInButtonPress_Hori;
  UIData      m_settingSearchingRearInButtonPress_Vert;
  UIData      m_settingParkingUIFreeParkingTextDescription_Hori;
  UIData      m_settingParkingUIFreeParkingTextDescription_Vert;
  UIData      m_settingParkingUIFreeParkingInstructions_Hori;
  UIData      m_settingParkingUIFreeParkingInstructions_Vert;
  UIData      m_settingParkingUIFreeParkingOperationGuide_Hori;
  UIData      m_settingParkingUIFreeParkingOperationGuide_Vert;
  //freepark ps type
  UIData      m_settingFreeParkingSpaceTypeCrossButtonPress_DENZA;
  UIData      m_settingFreeParkingSpaceTypeParallelButtonPress_DENZA;
  UIData      m_settingFreeParkingSpaceTypeDiagonalButtonPress_DENZA;
  UIData      m_settingFreeParkingSpaceTypeCrossButtonPress_MR;
  UIData      m_settingFreeParkingSpaceTypeParallelButtonPress_MR;
  UIData      m_settingFreeParkingSpaceTypeDiagonalButtonPress_MR;

  UIData      m_settingFunctionSelectionButtonParkinPress_DENZA;
  UIData      m_settingFunctionSelectionButtonParkoutPress_DENZA;
  UIData      m_settingFunctionSelectionButtonFreeParkingPress_DENZA;
  UIData      m_settingFunctionSelectionButtonParkinPress_MR;
  UIData      m_settingFunctionSelectionButtonParkoutPress_MR;
  UIData      m_settingFunctionSelectionButtonFreeParkingPress_MR;

  UIData      m_settingParkOutSideLeftButtonSelectedPress_DENZA;
  UIData      m_settingParkOutSideRightButtonSelectedPress_DENZA;
  UIData      m_settingParkOutSideLeftButtonSelectedPress_MR;
  UIData      m_settingParkOutSideRightButtonSelectedPress_MR;

  UIData      m_settingParkOutSideParallelLeftButtonSelectedPress_DFC;
  UIData      m_settingParkOutSideParallelRightButtonSelectedPress_DFC;
  UIData      m_settingParkOutSideCrossLeftButtonSelectedPress_DFC;
  UIData      m_settingParkOutSideCrossRightButtonSelectedPress_DFC;

  //////////////////////////////////////////////////////////

  vfc::uint32_t  m_parkingParaSlotPoseX;
  vfc::uint32_t  m_parkingVertSlotPoseX;
  vfc::uint32_t  m_parkingCalculateLeftSlotPoseX;
  vfc::uint32_t  m_parkingSlotPositionY;
  vfc::uint32_t  m_parkingDistanceBetweenPP;
  vfc::uint32_t  m_parkingDistanceBetweenPC;
  vfc::uint32_t  m_parkingDistanceBetweenCC;
  vfc::uint32_t  m_parkingDistanceBetweenDD;
  vfc::uint32_t  m_parkingMaxHeight;

  // ! camera icons
  std::string m_texturePathCamV;
  std::string m_texturePathCamH;
  std::string m_texturePathCamO;
  std::string m_texturePathCamVActive;
  std::string m_texturePathCamHActive;
  std::string m_texturePathCamOActive;

  // ! setting bar
  std::string m_texturePathQuit;
  std::string m_texturePathPPDIS;
  std::string m_texturePathPPON;
  std::string m_texturePathPPOFF;
  std::string m_texturePathAutoCamActivON;
  std::string m_texturePathAutoCamActivOFF;
  std::string m_texturePathVM2D;
  std::string m_texturePathVM2DOFF;
  std::string m_texturePathVM3D;
  std::string m_texturePathVM3DOFF;
  std::string m_texturePathBottom;
  std::string m_texturePathApaActive;
  std::string m_texturePathApaInactive;
  std::string m_texturePathOffRoad;
  std::string m_texturePathOffRoadOFF;
  std::string m_texturePathWheel;
  std::string m_texturePathWheelOFF;
  std::string m_texturePathWheelLine;

  // ! warn symbol
  std::string m_texturePathWarnSymbolUssWhole;
  std::string m_texturePathWarnSymbolUssFront;
  std::string m_texturePathWarnSymbolUssRear;
  std::string m_texturePathWarnSymbolUssVert;

  // ! view info icon
  std::string m_texturePathViewInfoSingleFront;
  std::string m_texturePathViewInfoSingleRear;
  std::string m_texturePathViewInfoPerspective;
  std::string m_texturePathViewInfoFrontWheel;
  std::string m_texturePathViewInfoRearWheel;
  std::string m_texturePathViewInfoFrontJunction;
  std::string m_texturePathViewInfoRearJunction;
  std::string m_texturePathViewInfoSTB;
  std::string m_texturePathPleaseCareSurrounding;
  std::string m_texturePathFloatPleaseCareSurrounding;
  std::string m_texturePathFrontWheelLimitLine;
  std::string m_texturePathRearWheelLimitLine;
  std::string m_texturePathDegrationNotCalirated;
  std::string m_texturePathDegrationAndroidError;
  std::string m_texturePathDegrationPlanviewIcon;
  std::string m_texturePathDegrationPlanviewRedIcon;
  std::string m_texturePathFullScreenBlackLineIcon;
  std::string m_texturePathFloatScreenBlackLineIcon;
  std::string m_texturePathfloatTopLeftCorner;
  std::string m_texturePathfloatTopRightCorner;
  std::string m_texturePathfloatButtomLeftCorner;
  std::string m_texturePathfloatButtomRightCorner;

  std::string m_texturePathFullTopLeftCorner;
  std::string m_texturePathFullTopRightCorner;
  std::string m_texturePathFullButtomLeftCorner;
  std::string m_texturePathFullButtomRightCorner;

  // ! vehicle trans icon
  std::string m_texturePathVehicleTransIcon;
  std::string m_texturePathVehicleTransIconVert;

  // ! text symbol
  std::string m_texturePathTextBoxSafeNotification;
  std::string m_texturePathTextBoxSafeNotification_vert;

  // ! parking icon symbol
  std::string m_texturePathParkingPlanIcon;
  std::string m_texturePathParkingPlanIcon_vert;

  // ! parking confirm icon
  std::string m_texturePathParkIcon;
  std::string m_texturePathParkIconOFF;

  // ! parking mode select icon
  std::string m_texturePathParkModePPSCIn;
  std::string m_texturePathParkModeCPSCIn;
  std::string m_texturePathParkModeOut;
  std::string m_texturePathParkModeSelected;

  // ! parking searching
  std::string m_texturePathParkSearchingRoad;
  std::string m_texturePathParkSearchingWave1;
  std::string m_texturePathParkSearchingWave2;
  std::string m_texturePathParkParaSlotValid;
  std::string m_texturePathParkVertSlotValid;
  std::string m_texturePathParkBackground;
  std::string m_texturePathParkRPAGuidance;
  std::string m_texturePathParkBoxGearD;
  std::string m_texturePathTextBoxTurnLever;
  std::string m_texturePathTextBoxExitGearR;
  std::string m_texturePathTextBoxSlowDown;
  std::string m_texturePathTextBoxStop;
  std::string m_texturePathTextBoxPsIDSelection;
  std::string m_texturePathTextBoxDoorToCloseinSearching;
  std::string m_texturePathTextBoxFunctionOff;
  std::string m_texturePathTextBoxCloseTrunk;
  std::string m_texturePathTextBoxSlotSearchingUnder10;
  std::string m_texturePathTextBoxOnGoing;
  std::string m_texturePathTextBoxAccAeb;
  std::string m_texturePathTextBoxCompleted;
  std::string m_texturePathTextBoxDriverDoorOpen;
  std::string m_texturePathTextBoxEPBApply;
  std::string m_texturePathTextBoxExternalEcuActive;
  std::string m_texturePathTextBoxExternalEcuFailure;
  std::string m_texturePathTextBoxGasPedal;
  std::string m_texturePathTextBoxGearInterrupt;
  std::string m_texturePathTextBoxGearIntervention;
  std::string m_texturePathTextBoxMovetimesOverflow;
  std::string m_texturePathTextBoxObjInTraj;
  std::string m_texturePathTextBoxPasFailure;
  std::string m_texturePathTextBoxRecovertimesOverflow;
  std::string m_texturePathTextBoxSeatbeltUnbuckle;
  std::string m_texturePathTextBoxSpaceLimit;
  std::string m_texturePathTextBoxSpeedHigh;
  std::string m_texturePathTextBoxSteeringwheelHandon;
  std::string m_texturePathTextBoxTimingOverflow;
  std::string m_texturePathTextBoxTrajOutRange;
  std::string m_texturePathTextBoxTrunkdoorOpen;
  std::string m_texturePathTextBoxVehicleBlock;
  std::string m_texturePathTextBoxOtherReason;
  std::string m_texturePathTextBoxPressDeadmanSwitch;
  std::string m_texturePathTextBoxReleaseBrake;
  std::string m_texturePathTextBoxSearchingProcess;
  std::string m_texturePathTextBoxSurroundView;
  std::string m_texturePathTextBoxUnsafeBehavior;
  std::string m_texturePathTextBoxDoorToClose;
  std::string m_texturePathTextBoxQuitTJAHWA;
  std::string m_texturePathTextBoxQuitObjnotExist;
  std::string m_texturePathTextBoxFailure;
  std::string m_texturePathTextModeSelect;
  std::string m_texturePathParkFinished;
  std::string m_texturePathWheelSeparatorHorizontal;
  std::string m_texturePathWheelSeparatorHorizontalFront;
  std::string m_texturePathWheelSeparatorHorizontalRear;
  std::string m_texturePathWheelSeparatorVertical;


  // ! parking searching
  std::string m_texturePathParkAutoPic;
  std::string m_texturePathParkHalo;
  std::string m_texturePathTextBoxSlotSearching;
  std::string m_texturePathTextBoxSearchingInLowSpeed;
  std::string m_texturePathParkingUIBackground;
  std::string m_texturePathSearchingTextBoxTimeOut;
  std::string m_texturePathSearchingTimeOut;
  std::string m_texturePathSearchingTextBoxSlowDown;
  std::string m_texturePathSearchingSlowDown;
  std::string m_texturePathSearchingTextBoxSelectType;
  std::string m_texturePathSearchingFrontInButton;
  std::string m_texturePathSearchingRearInButton;
  std::string m_texturePathSearchingTextBoxFindSlotStop;
  std::string m_texturePathSearchingPOCDirecSelectVehicle;
  std::string m_texturePathSearchingPOCDirecSelectVehicleTrans;
  std::string m_texturePathSearchingPOCDirecSelectSteeringWheel;
  std::string m_texturePathSearchingPOCDirecSelectSteeringWheelTrans;
  std::string m_texturePathTextBoxSearchingPOCDirecSelectTop;
  std::string m_texturePathTextBoxSearchingPOCDirecSelectBottom;
  std::string m_texturePathTextBoxSearchingPOCDirecSelectBottomTrans;
  std::string m_texturePathSearchingVehicleWithDoors;
  std::string m_texturePathContinueDrivingText;
  std::string m_texturePathMovesLeftNumberText;
  std::string m_texturePathSearchingDoorFrontRight;
  std::string m_texturePathSearchingDoorFrontLeft;
  std::string m_texturePathSearchingTrunkOpen;
  std::string m_texturePathSearchingDoorRearRight;
  std::string m_texturePathSearchingDoorRearLeft;
  std::string m_texturePathSearchingHoodOpen;
  std::string m_texturePathSearchingDoorsOpen;
  std::string m_texturePathSearchingTextBoxSelectParkoutDirectionWhenStop;

  // ! RPA
  std::string m_texturePathRPAOthersBluetoothmobilephone;
  std::string m_texturePathRPAOthersBluetoothDisconnect;
  std::string m_texturePathRPATextBluetoothConnectedPrompt;
  std::string m_texturePathRPATextPleaseLeaveTheCar;
  std::string m_texturePathRPATextBluetoothConnected;
  std::string m_texturePathRPATextPlease_parking;
  std::string m_texturePathRPATextClickOnThePhoneToStartParking;
  std::string m_texturePathRPATextOpenTheAPPAndConnectToBluetooth;
  std::string m_texturePathRPATextRemoteParkingIsNotAvailable;
  std::string m_texturePathRPATextRemoteParking;
  std::string m_texturePathRPATextDriverReponseTimeout;
  std::string m_texturePathRPATextSuspend;
  std::string m_texturePathRPATextBlueToothDisconnect;
  std::string m_texturePathRPATextTerminated;
  std::string m_texturePathRPATextUsePhone;

  // ! start parking
  std::string m_texturePathCrossVehicleNoSlotPic;
  std::string m_texturePathCrossVehiclePic;
  std::string m_texturePathParallelVehiclePic;
  std::string m_texturePathDiagonalVehiclePic;
  std::string m_texturePathStarkParkOutButton;
  std::string m_texturePathStarkParkInButton;
  std::string m_texturePathStarkParkOutDeactivateButton;
  std::string m_texturePathStarkParkInDeactivateButton;
  std::string m_texturePathTextParkingIn;

  // ! guidance activate
  std::string m_texturePathGuidanceGearD;
  std::string m_texturePathGuidanceGearN;
  std::string m_texturePathGuidanceGearR;
  std::string m_texturePathGuidanceGearP;
  std::string m_texturePathTextBoxAPAParkingIn;
  std::string m_texturePathTextBoxAPAParkingOut;
  std::string m_texturePathTextBoxParkingNoticeSur;
  std::string m_texturePathTextGuidanceReleaseBrakeAndSteering;
  std::string m_texturePathSuspendButton;
  std::string m_texturePathTextParkingOutPayAttentionToEnvironment;

  // ! park finished
  std::string m_texturePathTextBoxParkFinished;
  std::string m_texturePathTextBoxFinishedTakeOver;

  // ! guidance suspend
  std::string m_texturePathHoriSuspendParkingPause;
  std::string m_texturePathTextBoxQuitIn30s;
  std::string m_texturePathSuspendContinueButton;
  std::string m_texturePathSuspendContinueButtonGray;
  std::string m_texturePathSuspendTextBoxObjectOnPath;
  std::string m_texturePathSuspendObjectOnPath;
  std::string m_texturePathSuspendObjectOnPathIcon;
  std::string m_texturePathSuspendTextBoxDoorOpen;
  std::string m_texturePathSuspendTextBrakePadal;
  std::string m_texturePathSuspendBrakePadal;
  std::string m_texturePathSuspendTextMirrorFold;
  std::string m_texturePathSuspendMirrorFold;
  std::string m_texturePathSuspendTrunkOpen;
  std::string m_texturePathSuspendHoodOpen;
  // std::string m_texturePathSuspendTextSeatBelt;
  // std::string m_texturePathSuspendSeatBelt;
  // std::string m_texturePathSuspendTextBlueTooth;
  // std::string m_texturePathSuspendBlueTooth;
  std::string m_texturePathSuspendTextHoodOpen;
  std::string m_texturePathSuspendTextTrunkOpen;
  std::string m_texturePathSuspendTextUserTriggerPause;

  // ! park assist standby
  std::string m_texturePathAssistStandbyResponseTimeout;

  // ! park quit
  std::string m_texturePathQuitTextBoxApaQuitTakeOver;
  std::string m_texturePathQuitTextBoxSeatBeltUnbuckle;
  std::string m_texturePathSeatBeltUnbuckle;
  std::string m_texturePathQuitTextBoxExcessiveSlope;
  std::string m_texturePathExcessiveSlope;
  std::string m_texturePathQuitTextDriverOverride;
  std::string m_texturePathDriverOverride;
  std::string m_texturePathQuitTextRoutePlanningFailure;
  std::string m_texturePathRoutePlanningFailure;
  std::string m_texturePathQuitTextVehicleSpeedOverthreshold;
  std::string m_texturePathVehicleSpeedOverthreshold;
  std::string m_texturePathQuitTextAPSTimeout;
  std::string m_texturePathAPSTimeout;
  std::string m_texturePathQuitTextCurrentStepNumberOverThreshold;
  std::string m_texturePathCurrentStepNumberOverThreshold;
  std::string m_texturePathQuitTextSpaceIsLimitedInParkOutMode;
  std::string m_texturePathSpaceIsLimitedInParkOutMode;
  std::string m_texturePathQuitTextEPBFailure;
  std::string m_texturePathEPBFailure;
  std::string m_texturePathQuitTextSCUFailure;
  std::string m_texturePathSCUFailure;
  std::string m_texturePathQuitTextBoxDriveModeUnsuitable;
  std::string m_texturePathDriveModeUnsuitable;
  std::string m_texturePathQuitTextBoxTrailerHitchConnected;
  std::string m_texturePathTrailerHitchConnected;

  // ! park comfirming
  std::string m_texturePathParkConfirmingTextSeatBelt;
  std::string m_texturePathParkConfirmingTextPressBrakePedal;
  std::string m_texturePathParkConfirmingPressBrakePedal;
  std::string m_texturePathParkConfirmingTextCloseDoor;
  std::string m_texturePathParkConfirmingTextExpandedMirror;
  std::string m_texturePathParkConfirmingExpandedMirror;
  std::string m_texturePathParkConfirmingTextCloseTrunk;
  std::string m_texturePathParkConfirmingTextCloseHood;
  std::string m_texturePathParkConfirmingTextStop;
  std::string m_texturePathParkFrontIsClearParkingPath;
  std::string m_texturePathParkFrontIsClearPic;
  std::string m_texturePathParkFrontIsClearHandOver;
  std::string m_texturePathParkFrontIsClear;
  std::string m_texturePathConfirmingTextAPAquitTakeOver;
  std::string m_texturePathPathParkConfirmingTextSmallParkSlot;
  std::string m_texturePathPathParkConfirmingSmallParkSlot;
  std::string m_texturePathParkConfirmingTextHoldBrakeAndStart;
  std::string m_texturePathQuitTextAPAFailure;
  std::string m_texturePathAPAFailure;
  std::string m_texturePathQuitTextExternalECUFailure;
  std::string m_texturePathExternalECUFailure;
  std::string m_texturePathQuitTextABSTCSESPACCAEBActive;
  std::string m_texturePathABSTCSESPACCAEBActive;
  std::string m_texturePathQuitTextESCFailure;
  std::string m_texturePathESCFailure;
  std::string m_texturePathQuitTextEPSFailure;
  std::string m_texturePathEPSFailure;
  std::string m_texturePathQuitTextVehicleBlock;
  std::string m_texturePathVehicleBlock;
  std::string m_texturePathQuitTextInterruptNumberOverThreshold;
  std::string m_texturePathInterruptNumberOverThreshold;
  std::string m_texturePathQuitTextRadarDirty;
  std::string m_texturePathRadarDirty;
  std::string m_texturePathQuitTextEPBActive;
  std::string m_texturePathEPBActive;
  std::string m_texturePathParkConfirmingTextKeepBrakePedal;

  // ! Free Parking
  std::string m_texturePathFreeParkingTextStop;
  std::string m_texturePathFreeParkingTextChooseParkingSpaceType;
  std::string m_texturePathFreeParkingTextDescriptionTop;
  std::string m_texturePathFreeParkingTextDescriptionBottom;
  std::string m_texturePathFreeParkingSelectedParkingInstructions;
  std::string m_texturePathFreeParkingSelectedParkingInstructionsGrey;
  std::string m_texturePathFreeParkingSelectedCrossParkingInstructions;
  std::string m_texturePathFreeParkingSelectedCrossParkingInstructionsGrey;
  std::string m_texturePathFreeParkingSelectedDiagonalParkingInstructions;
  std::string m_texturePathFreeParkingSelectedDiagonalParkingInstructionsGrey;
  std::string m_texturePathFreeParkingTextDescriptionBottomTrans;

  // ! Hori parking slots & guideline
  std::string m_texturePathHoriUnselectedSlotVertRight;
  std::string m_texturePathHoriUnselectedSlotVertLeft;
  std::string m_texturePathHoriSelectedSlotVertRight;
  std::string m_texturePathHoriUnselectedSlotParaRight;
  std::string m_texturePathHoriUnselectedSlotParaLeft;
  std::string m_texturePathHoriSelectedSlotParaRight;
  std::string m_texturePathHoriSelectedSlotParaLeft;
  std::string m_texturePathHoriUnselectedSlotDiagRight;
  std::string m_texturePathHoriUnselectedSlotDiagLeft;
  std::string m_texturePathHoriSelectedSlotDiagRight;
  std::string m_texturePathHoriSelectedSlotDiagLeft;
  std::string m_texturePathParkCrossSlotLeft;
  std::string m_texturePathParkCrossSlotRight;
  std::string m_texturePathParkParaSlotLeft;
  std::string m_texturePathParkParaSlotRight;
  std::string m_texturePathParkDiagSlotLeft;
  std::string m_texturePathParkDiagSlotRight;
  std::string m_texturePathHoriCombinationDiagRightFrontIn;
  std::string m_texturePathHoriCombinationDiagLeftFrontIn;
  std::string m_texturePathHoriCombinationDiagRightRearIn;
  std::string m_texturePathHoriCombinationDiagLeftRearIn;
  std::string m_texturePathHoriCompleteCombinationDiagRightFrontIn;
  std::string m_texturePathHoriCompleteCombinationDiagLeftFrontIn;
  std::string m_texturePathHoriCompleteCombinationDiagRightRearIn;
  std::string m_texturePathHoriCompleteCombinationDiagLeftRearIn;
  std::string m_texturePathHoriParkOutGuidanceCombinationParaRight;
  std::string m_texturePathParkOutGuidanceCombinationParaLeft;
  std::string m_texturePathParkOutGuidanceCombinationParaRightGuidanceActive;
  std::string m_texturePathParkOutGuidanceCombinationParaLeftGuidanceActive;
  std::string m_texturePathParkOutCompleteCombinationParaRight;
  std::string m_texturePathParkOutCompleteCombinationParaLeft;
  std::string m_texturePathHoriParkOutGuidanceCombinationCrossRight;
  std::string m_texturePathParkOutGuidanceCombinationCrossLeft;
  std::string m_texturePathParkOutGuidanceCombinationCrossRightGuidanceActive;
  std::string m_texturePathParkOutGuidanceCombinationCrossLeftGuidanceActive;
  std::string m_texturePathParkOutCompleteCombinationCrossRight;
  std::string m_texturePathParkOutCompleteCombinationCrossLeft;
  std::string m_texturePathHoriParkOutSlotParaRight;
  std::string m_texturePathParkOutSlotParaLeft;
  std::string m_texturePathParkOutSlotCrossLeft;
  std::string m_texturePathHoriPauseCombinationDiagRightFrontIn;
  std::string m_texturePathHoriPauseCombinationDiagLeftFrontIn;
  std::string m_texturePathHoriPauseCombinationDiagRightRearIn;
  std::string m_texturePathHoriPauseCombinationDiagLeftRearIn;

  // ! button
  std::string m_texturePathAPAFunctionSelectionButtonParkin;
  std::string m_texturePathAPAFunctionSelectionButtonParkout;
  std::string m_texturePathAPAFunctionSelectionButtonFreeParking;
  std::string m_texturePathAPAFunctionSelectionButtonParkinAPA;
  std::string m_texturePathAPAFunctionSelectionButtonParkinRPA;
  std::string m_texturePathParkOutSideParallelLeftButtonUnSelect;
  std::string m_texturePathParkOutSideParallelLeftButtonSelected;
  std::string m_texturePathParkOutSideParallelRightButtonUnSelect;
  std::string m_texturePathParkOutSideParallelRightButtonSelected;
  std::string m_texturePathParkOutSideParallelLeftButtonUnSelectTrans;
  std::string m_texturePathParkOutSideParallelLeftButtonSelectedTrans;
  std::string m_texturePathParkOutSideParallelRightButtonUnSelectTrans;
  std::string m_texturePathParkOutSideParallelRightButtonSelectedTrans;
  std::string m_texturePathParkOutSideCrossLeftButtonUnSelect;
  std::string m_texturePathParkOutSideCrossLeftButtonSelected;
  std::string m_texturePathParkOutSideCrossRightButtonUnSelect;
  std::string m_texturePathParkOutSideCrossRightButtonSelected;
  std::string m_texturePathParkOutSideCrossLeftButtonUnSelectTrans;
  std::string m_texturePathParkOutSideCrossLeftButtonSelectedTrans;
  std::string m_texturePathParkOutSideCrossRightButtonUnSelectTrans;
  std::string m_texturePathParkOutSideCrossRightButtonSelectedTrans;
  std::string m_texturePathParkQuitButton;
  std::string m_texturePathFreeParkingConfirmButton;
  std::string m_texturePathFreeParkingConfirmButtonGrey;

  osg::Vec2f m_floatCornerTextureSize;
  osg::Vec2f m_fullCornerTextureSize;
  // ! Theme
  cc::target::common::EThemeTypeHU m_themeHu;

};

extern pc::util::coding::Item<UISettings> g_uiSettings;


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_UIELEMENTS_HMIELEMENTSSETTINGS_H
