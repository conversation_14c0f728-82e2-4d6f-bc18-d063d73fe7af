//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: BOK1YH (XC-DA/EPF5) Bokelmann Karin
//  Department: CC-DA/EPF5
//=============================================================================
/// @swcomponent SV3D
/// @file  CustomFloorPlateRenderer.cpp
/// @brief
//=============================================================================
#include "vfc/core/vfc_types.hpp"
#include "cc/assets/common/inc/CustomFloorPlateRenderer.h"

namespace cc
{
namespace assets
{
namespace common
{
namespace
{

pc::util::coding::Item<CustomFloorPlateRendererSettings> g_customFloorPlateRendererSettings("CustomFloorPlateRenderer");

} // namespace

CustomFloorPlateRenderer::CustomFloorPlateRenderer(pc::factory::Floor* f_floor, const pc::worker::bowlshaping::PolarBowlLayout& f_polarBowlLayout, pc::texfloor::core::FloorGenerator* f_floorPlateGenerator, cc::core::AssetId f_floorPlateAssetId, pc::core::Framework* f_framework, const bool f_enableFOVBasedCulling)
: pc::assets::FloorPlateRenderer{f_floor, f_polarBowlLayout, f_floorPlateGenerator, f_floorPlateAssetId, f_framework, f_enableFOVBasedCulling}
, m_fadingStateBlurredBP{FadingState::IDLE}
, m_fadingStateHistoricBP{FadingState::IDLE}
, m_timer{}
, m_timeout{}
{

}

CustomFloorPlateRenderer::CustomFloorPlateRenderer(pc::factory::Floor* f_floor, const pc::worker::bowlshaping::PolarBowlLayout& f_polarBowlLayout, pc::texfloor::core::FloorGenerator* f_floorPlateGenerator, pc::assets::FloorPlateStateHandler* f_floorPlateStateHandler, cc::core::AssetId f_floorPlateAssetId, pc::core::Framework* f_framework, const bool f_enableFOVBasedCulling)
: pc::assets::FloorPlateRenderer::FloorPlateRenderer{f_floor, f_polarBowlLayout, f_floorPlateGenerator, f_floorPlateStateHandler, f_floorPlateAssetId, f_framework, f_enableFOVBasedCulling}
, m_fadingStateBlurredBP{FadingState::IDLE}
, m_fadingStateHistoricBP{FadingState::IDLE}
, m_timer{}
, m_timeout{}
{

}

void CustomFloorPlateRenderer::updateAlphaAnimationForBaseplate(const BaseplateType f_baseplateType)
{
  const bool l_historicBaseplate = (f_baseplateType == BaseplateType::History);

  FadingState& l_fadingState     = l_historicBaseplate ? m_fadingStateHistoricBP      : m_fadingStateBlurredBP;
  const vfc::float32_t l_layerAlpha       = l_historicBaseplate ? getHistoryLayerAlpha()       : getBasePlateLayerAlpha();
  const vfc::float32_t l_layerAlphaTarget = l_historicBaseplate ? getHistoryLayerAlphaTarget() : getBasePlateLayerAlphaTarget();

  m_timeout = m_timer.tick();

  switch( l_fadingState ) //check if animation is necessary
  {
    case FadingState::IDLE :
    {
      if ( l_layerAlpha > l_layerAlphaTarget + pc::assets::g_floorPlateRendererSettings->m_texturedBasePlateFadeoutAlphaThreshold )
      {
        //Trigger the Fade-Out
        l_fadingState = FadingState::FADEOUT_ONGOING;
        m_timeout = m_timer.tick();

      }
      else if(  l_layerAlpha < l_layerAlphaTarget - pc::assets::g_floorPlateRendererSettings->m_texturedBasePlateFadeoutAlphaThreshold )
      {
        //Trigger the Fade-In
        l_fadingState = FadingState::FADEIN_ONGOING;
        m_timeout = m_timer.tick();
      }
      else
      {
        l_historicBaseplate ? setHistoryLayerAlpha(l_layerAlphaTarget) : setBasePlateLayerAlpha(l_layerAlphaTarget);
      }
      break;
    }
    case FadingState::FADEOUT_ONGOING :
    {
      if( l_layerAlpha < l_layerAlphaTarget + pc::assets::g_floorPlateRendererSettings->m_texturedBasePlateFadeoutAlphaThreshold )
      {
        l_historicBaseplate ? setHistoryLayerAlpha(l_layerAlphaTarget) : setBasePlateLayerAlpha(l_layerAlphaTarget);
        l_fadingState = FadingState::IDLE;
        break;
      }

      const vfc::float32_t l_elapsedTimeMS = static_cast<vfc::float32_t>(m_timer.delta_m(m_timeout, m_timer.tick()));
      const vfc::float32_t l_remainingTimeMS = g_customFloorPlateRendererSettings->m_texturedBasePlateFadeoutSpeedMS - l_elapsedTimeMS;

      const vfc::float32_t l_deltaAlpha = l_layerAlpha - l_layerAlphaTarget;
      const vfc::float32_t l_alpha = l_layerAlpha - (l_deltaAlpha/l_remainingTimeMS)*g_customFloorPlateRendererSettings->m_frameDurationMS;
      l_historicBaseplate ? setHistoryLayerAlpha(l_alpha) : setBasePlateLayerAlpha(l_alpha);
      break;
    }
    case FadingState::FADEIN_ONGOING :
    {
      if( l_layerAlpha > l_layerAlphaTarget - pc::assets::g_floorPlateRendererSettings->m_texturedBasePlateFadeoutAlphaThreshold )
      {
        l_historicBaseplate ? setHistoryLayerAlpha(l_layerAlphaTarget) : setBasePlateLayerAlpha(l_layerAlphaTarget);
        l_fadingState = FadingState::IDLE;
        break;
      }

      const vfc::float32_t l_elapsedTimeMS = static_cast<vfc::float32_t>(m_timer.delta_m(m_timeout, m_timer.tick()));
      const vfc::float32_t l_remainingTimeMS = g_customFloorPlateRendererSettings->m_texturedBasePlateFadeoutSpeedMS - l_elapsedTimeMS;

      const vfc::float32_t l_deltaAlpha = l_layerAlphaTarget - l_layerAlpha;
      const vfc::float32_t l_alpha = l_layerAlpha + (l_deltaAlpha/l_remainingTimeMS)*g_customFloorPlateRendererSettings->m_frameDurationMS;
      l_historicBaseplate ? setHistoryLayerAlpha(l_alpha) : setBasePlateLayerAlpha(l_alpha);
      break;
    }
    default:
      {break;}
  }
}

void CustomFloorPlateRenderer::animateAlpha() //overrides parent class implementation so the duration is codable in ms
{
  updateAlphaAnimationForBaseplate(BaseplateType::Blurred);
  updateAlphaAnimationForBaseplate(BaseplateType::History);
}

} // namspace common
} // namespace assets
} // namespace cc
