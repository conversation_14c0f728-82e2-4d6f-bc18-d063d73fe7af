//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  ParkingSpace.cpp
/// @brief
//=============================================================================

#include "cc/assets/parkingspace/inc/ParkingSpace.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/math/inc/Math2D.h"
#include "CustomSystemConf.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "vfc/core/vfc_types.hpp"
#include "osgDB/FileUtils"

using pc::util::logging::g_AppContext;

// #define USE_DIFFERENT_SLOT_ICON_FOR_CORSS_PARALLEL_GUIDANCE

namespace cc
{
namespace assets
{
namespace parkingspace
{

// const unsigned int g_numSlots = 6;

pc::util::coding::Item<ParkingSpaceSettings> g_parkingSpaceSettings("ParkingSpace");

namespace
{

osg::ref_ptr<osg::Texture2D> createTexture(const std::string& f_filename)
{
  const osg::ref_ptr<osg::Image> l_image = osgDB::readImageFile(f_filename);
  if (nullptr != l_image)
  {
    const osg::ref_ptr<osg::Texture2D> l_texture = new osg::Texture2D(l_image);
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
  }
  else
  {
    XLOG_ERROR(g_AppContext, "Failed to load parking space texture: " << f_filename); 
  }
  return nullptr;
}

} // namespace


//!
//! @brief Construct a new Parking Space Manager:: Parking Space Manager object
//!
//! @param f_config
//!
ParkingSpaceManager::ParkingSpaceManager(cc::views::planview::PlanViewCullCallback* f_cullcallback, const pc::core::Viewport& f_viewport, const bool& f_isHoriScreen)
  : m_lastConfigUpdate{~0u}
  , m_planViewCullCall{f_cullcallback}
  , m_strTexturePath(g_parkingSpaceSettings->m_texturePathSelectableParaSlot)
  , m_manoeuveringIconSize{g_parkingSpaceSettings->m_iconSize}
  , m_flipH_b{false}
  , m_flipV_b{false}
  , m_parkingSpotOffset{0.0f, 0.0f}
  , m_radOffset{0.0f}
  , m_initTime{0.0f}
  , m_isInitTimeUpdated{false}
  , m_viewport{f_viewport}
  , m_isHoriScreen{f_isHoriScreen}
  , m_planViewWidth_hori{cc::core::g_planView->m_widthMetersParkingHori}
  , m_planViewWidth_vert{cc::core::g_planView->m_widthMetersParkingVert}
  , m_textureInterface{nullptr}
  , m_textureParaSelectable{createTexture(g_parkingSpaceSettings->m_texturePathSelectableParaSlot)}
  , m_textureParaSelected{createTexture(g_parkingSpaceSettings->m_texturePathSelectedParaSlot)}
  , m_textureCrossSelectable{createTexture(g_parkingSpaceSettings->m_texturePathSelectableCrossSlot)}
  , m_textureCrossSelected{createTexture(g_parkingSpaceSettings->m_texturePathSelectedCrossSlot)}
{
  //   --------------
  //   | .          |
  //   |  .         |     m_veh_alpha
  // x | a . l      |     m_veh_length
  //   |    .       |
  //   |..y...      |     vehicle rear axel center point
  //   |            |
  //   --------------
//   float l_veh_x = g_parkingSpaceSettings->m_slotParallelX
//     - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear
//     - (g_parkingSpaceSettings->m_slotParallelX
//         - pc::vehicle::g_mechanicalData->m_wheelbase
//         - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront
//         - pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear) / 2.0f;  // m
  const vfc::float32_t l_veh_x = g_parkingSpaceSettings->m_slotParallelX - g_parkingSpaceSettings->m_slotOffset.x();
  const vfc::float32_t l_veh_y = g_parkingSpaceSettings->m_slotCrossX / 2.0f - g_parkingSpaceSettings->m_slotOffset.y();  // m
  m_veh_alpha   = std::atan(l_veh_y / l_veh_x);  // rad
  m_veh_length  = std::sqrt(l_veh_x*l_veh_x + l_veh_y*l_veh_y);  // m
}


ParkingSpaceManager::~ParkingSpaceManager() = default;

void ParkingSpaceManager::init(cc::assets::uielements::CustomImageOverlays* f_imageOverlays)
{
  // ! init Parking Space icons
  m_iconParkingSpace.clear(f_imageOverlays);
  m_iconParkingSpaceGuidance.clear(f_imageOverlays);
  for (vfc::uint8_t i = 0u; i < (static_cast<vfc::uint8_t>(cc::target::common::l_L_ParkSpace_side) * static_cast<vfc::uint8_t>(cc::target::common::l_L_ParkSpace_NumberPerside)); i++)
  {
    if ( i < cc::target::common::l_L_ParkSpace_NumberPerside )
    {
      m_iconParkingSpace.addIcon(f_imageOverlays, createIcon(m_strTexturePath, true));
    }
    else
    {
      m_iconParkingSpace.addIcon(f_imageOverlays, createIcon(m_strTexturePath, false));
    }
  }
}


void ParkingSpaceManager::update( // PRQA S 6043  // PRQA S 6040
    cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
    const core::CustomFramework* f_framework,
    const osg::NodeVisitor* f_nv)
{
  // ! check if config has changed
  if (g_parkingSpaceSettings->getModifiedCount() != m_lastConfigUpdate)
  {
    init(f_imageOverlays);
    m_lastConfigUpdate = g_parkingSpaceSettings->getModifiedCount();
  }

  if(false == m_isHoriScreen)
  {
    // m_manoeuveringIconSize = g_parkingSpaceSettings->m_iconSizeVert;
    osg::Vec2f l_manoeuveringIconSize_vert = g_parkingSpaceSettings->m_iconSizeVert / m_planViewWidth_vert * 8.0f;  // defaul value is tuning based on 8.0m
    m_manoeuveringIconSize.x() = std::floor(l_manoeuveringIconSize_vert.x());
    m_manoeuveringIconSize.y() = std::floor(l_manoeuveringIconSize_vert.y());
  }
  else
  {
    // m_manoeuveringIconSize = g_parkingSpaceSettings->m_iconSize;
    osg::Vec2f l_manoeuveringIconSize_hori = g_parkingSpaceSettings->m_iconSize / m_planViewWidth_hori * 11.0f;  // defaul value is tuning based on 11.0m
    m_manoeuveringIconSize.x() = std::floor(l_manoeuveringIconSize_hori.x());
    m_manoeuveringIconSize.y() = std::floor(l_manoeuveringIconSize_hori.y());
  }

  // ! Check Parkhmi Signal and icons
  if (   f_framework->m_parkHmiParkingStatusReceiver.isConnected()       // searching, confirming, guidance...
      && f_framework->m_parkHmiParkParkngTypeSeldReceiver.isConnected()  // parkin, parkout
      && f_framework->m_parkHmiParkAPAPARKMODEReceiver.isConnected()     // apa, rpa,
      && f_framework->m_freeparkingActiveReceiver.isConnected()          // inactive, active
      && f_framework->m_parkAPASlotsReceiver.isConnected()
      && f_framework->m_speedReceiver.isConnected())
  {
    const cc::daddy::ParkStatusDaddy_t*        const l_pParkStatus      = f_framework->m_parkHmiParkingStatusReceiver.getData();
    const cc::daddy::ParkParkngTypeSeld_t*     const l_pParkType        = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    const cc::daddy::ParkAPAPARKMODE_t*        const l_pParkMode        = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    const cc::daddy::ParkFreeParkingActive_t*  const l_pFreeParkActive  = f_framework->m_freeparkingActiveReceiver.getData();
    const cc::daddy::ParkAPA_ParkSpace_t*      const l_pParkSpace       = f_framework->m_parkAPASlotsReceiver.getData();

    cc::daddy::ParkAPA_ParkSpaceMark_t& l_ParkSpaceMark = cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort.reserve();

    m_iconParkingSpace.setAllEnabled(false);
    m_iconParkingSpaceGuidance.clear(f_imageOverlays);

    if ((nullptr != l_pParkStatus) && (nullptr != l_pParkType)&& (nullptr != l_pParkMode)&& (nullptr != l_pFreeParkActive) && (nullptr != l_pParkSpace) )
    {
      std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> l_curparkSpace; // PRQA S 4102
      for(vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
      {
        for(vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
        {
          l_curparkSpace[l_side][l_numberPerside] = l_pParkSpace->m_Data[l_side][l_numberPerside];
        }
      }

      const vfc::uint32_t l_retPSIMode = checkAPAStatus(static_cast<vfc::uint8_t>(l_pParkStatus->m_Data), l_curparkSpace);

      bool l_isShowAtSearching = false;       // bit 0: scanning
      bool l_isShowAtSelected  = false;       // bit 1: selected
      bool l_isShowAtGuidanceParkin  = false; // bit 2: guidance at parkin
      bool l_isShowAtGuidanceParkout = false; // bit 3: guidance at parkout.

      checkShowSts(
        static_cast<vfc::uint8_t>(l_pParkType->m_Data),
        static_cast<vfc::uint8_t>(l_pParkMode->m_Data),
        l_pFreeParkActive->m_Data,
        l_isShowAtSearching,
        l_isShowAtSelected, // PRQA S 4126
        l_isShowAtGuidanceParkin,
        l_isShowAtGuidanceParkout);

      if (l_isShowAtSearching && (ENUM_PS_MODE_SCANING == l_retPSIMode || ENUM_PS_MODE_SELECTING == l_retPSIMode))
      {
        //! Parking spot display scaning mode
        for(vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
        {
          for(vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
          {
            const vfc::uint8_t l_index = static_cast<vfc::uint8_t>(l_side * static_cast<vfc::uint8_t>(cc::target::common::l_L_ParkSpace_NumberPerside) + l_numberPerside);
            bool l_isParallel = true;
            bool l_isLeft = false;
            m_textureInterface = m_textureParaSelectable;
            l_ParkSpaceMark.m_Data[l_side][l_numberPerside].m_x = 0;  // cm, front positive +
            l_ParkSpaceMark.m_Data[l_side][l_numberPerside].m_y = 0;  // cm, left positive +
            l_ParkSpaceMark.m_Data[l_side][l_numberPerside].m_phi = 0;  // 2^(-12) rad, clockwise positive +
            updatePositionSearching(
                l_isParallel,
                l_isLeft,
                l_ParkSpaceMark.m_Data[l_side][l_numberPerside],
                l_curparkSpace,
                l_side,
                l_numberPerside);

            if (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE == l_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot)
            {
              if (!l_isParallel) {m_textureInterface = m_textureCrossSelectable;}
              updateParkingSpotSearching(l_index, f_imageOverlays, l_ParkSpaceMark.m_Data[l_side][l_numberPerside], l_isParallel, l_isLeft, f_framework);
            }
            else if (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED == l_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot)
            {
              m_strTexturePath = (l_isParallel ?
                g_parkingSpaceSettings->m_texturePathSelectedParaSlot :
                g_parkingSpaceSettings->m_texturePathSelectedCrossSlot);
              updateParkingSpotSearching(
                l_index,
                f_imageOverlays,
                l_ParkSpaceMark.m_Data[l_side][l_numberPerside],
                l_isParallel,
                l_isLeft,
                f_framework);
#if PARKINGSPACE_SEARCHING_DEBUG
              cc::target::common::StrippedParkhmiPositionSearching l_corner1 = {
                (l_curparkSpace[l_side][l_numberPerside].m_APA_PSCorner1X_i16),
                (l_curparkSpace[l_side][l_numberPerside].m_APA_PSCorner1Y_i16), 0};
              cc::target::common::StrippedParkhmiPositionSearching l_corner2 = {
                (l_curparkSpace[l_side][l_numberPerside].m_APA_PSCorner2X_i16),
                (l_curparkSpace[l_side][l_numberPerside].m_APA_PSCorner2Y_i16), 0};
              updateParkingObjSearching(
                f_imageOverlays, l_corner1, l_isParallel,
                l_isLeft, g_parkingSpaceSettings->m_texturePathObjectCorner1);
              updateParkingObjSearching(
                f_imageOverlays, l_corner2, l_isParallel,
                l_isLeft, g_parkingSpaceSettings->m_texturePathObjectCorner2);
#endif //PARKINGSPACE_SEARCHING_DEBUG
              if (l_isParallel) {m_textureInterface = m_textureParaSelected;}
              else {m_textureInterface = m_textureCrossSelected;}
              updateParkingSpotSearching(l_index, f_imageOverlays, l_ParkSpaceMark.m_Data[l_side][l_numberPerside], l_isParallel, l_isLeft, f_framework);
            }
            else
            {
              //Do nothing
            }
          }
        }
        m_isInitTimeUpdated = false;
      }
      // else if ( !l_isShowAtSearching && l_isShowAtSelected && (ENUM_PS_MODE_SELECTING == l_retPSIMode))
      // {
      //   //! Parking spot display selecting mode
      //   bool l_isContinue = true;
      //   for(vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side && l_isContinue; l_side++ )
      //   {
      //     for(vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside && l_isContinue; l_numberPerside++)
      //     {
      //       if (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED == l_curparkSpace[l_side][l_numberPerside].m_APA_PrkgSlot)
      //       {
      //         vfc::uint8_t l_index = l_side * (cc::target::common::l_L_ParkSpace_NumberPerside) + l_numberPerside;
      //         bool l_isParallel = true;
      //         bool l_isLeft = false;
      //         l_ParkSpaceMark.m_Data[l_side][l_numberPerside].m_x = 0;  // cm, front positive +
      //         l_ParkSpaceMark.m_Data[l_side][l_numberPerside].m_y = 0;  // cm, left positive +
      //         l_ParkSpaceMark.m_Data[l_side][l_numberPerside].m_phi = 0;  // 2^(-12) rad, clockwise positive +
      //         updatePositionSearching(l_isParallel, l_isLeft, l_ParkSpaceMark.m_Data[l_side][l_numberPerside], l_curparkSpace, l_side, l_numberPerside);
      //         if (l_isParallel) m_strTexturePath = g_parkingSpaceSettings->m_texturePathNextParaSlot;
      //         else m_strTexturePath = g_parkingSpaceSettings->m_texturePathNextCrossSlot;
      //         updateParkingSpotSearching(l_index, f_imageOverlays, l_ParkSpaceMark.m_Data[l_side][l_numberPerside], l_isParallel, l_isLeft, f_framework);

      //         l_isContinue = false;
      //       }
      //     }
      //   }

      //   m_isInitTimeUpdated = false;
      // }
      else if ((l_isShowAtGuidanceParkin || l_isShowAtGuidanceParkout) && (ENUM_PS_MODE_MANOEUVER == l_retPSIMode))
      {
        //! Find selected slot
        cc::target::common::EFAPAParkSlotType l_selectedSpaceType       = cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT;
        ESpaceSide        l_selectedSpaceSide       = LEFTSIDE;
        for(vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
        {
          for(vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
          {
            if (cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED == l_pParkSpace->m_Data[l_side][l_numberPerside].m_APA_PrkgSlot)
            {
              l_selectedSpaceType = l_pParkSpace->m_Data[l_side][l_numberPerside].m_APA_PSType;
              l_selectedSpaceSide = static_cast<ESpaceSide>(l_side);
            }
          }
        }

        //! get time diff since init
        const vfc::float32_t l_currentTime = static_cast<vfc::float32_t> (f_nv->getFrameStamp()->getReferenceTime());
        if (false == m_isInitTimeUpdated)
        {
          m_initTime = l_currentTime;
          m_isInitTimeUpdated = true;
        }
        const vfc::float32_t l_timeDiff = l_currentTime - m_initTime;
        if ( isGreater(l_timeDiff, g_parkingSpaceSettings->m_manoeuverShowDelay) )
        {
          //! Parking spot final position during manoeuvering
          if(l_isShowAtGuidanceParkin && (cc::target::common::EParkngTypeSeld::PARKING_IN == l_pParkType->m_Data))  // there is final position spot only when parkIn
          {
            if ( f_framework->m_parkFinalEndPositionReceiver.isConnected() && f_framework->m_parkIsLastMoveReceiver.isConnected() )
            {
              const cc::daddy::ParkFinalEndPositionDaddy_t*  const l_pFinalEndPosition  = f_framework->m_parkFinalEndPositionReceiver.getData();
              const cc::daddy::ParkIsLastMoveDaddy_t*        const l_pIsLastMove        = f_framework->m_parkIsLastMoveReceiver.getData();
              bool  l_isLastMove = false;
              m_flipH_b = false;
              m_flipV_b = true;
              if (nullptr != l_pFinalEndPosition && l_pIsLastMove != 0)
              {
                l_isLastMove = l_pIsLastMove->m_Data;
                #ifdef USE_DIFFERENT_SLOT_ICON_FOR_CORSS_PARALLEL_GUIDANCE
                if(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS == l_selectedSpaceType)
                {
                  m_strTexturePath = (l_isLastMove ?
                    g_parkingSpaceSettings->m_texturePathTargetCrossSlotFinal :
                    g_parkingSpaceSettings->m_texturePathTargetCrossSlot);
                  if (LEFTSIDE == l_selectedSpaceSide)
                  {
                    m_flipH_b = true;
                    m_flipV_b = false;
                  }
                }
                else
                #endif
                {
                  m_strTexturePath = (l_isLastMove ?
                    g_parkingSpaceSettings->m_texturePathTargetParaSlotFinal :
                    g_parkingSpaceSettings->m_texturePathTargetParaSlot);
                }
                const cc::target::common::StrippedParkhmiTargetPosition l_finalEndPosition = l_pFinalEndPosition->m_Data;
                updateTargetPositionManoeuvering(f_imageOverlays, l_finalEndPosition,
                                                 l_isLastMove ? cc::assets::uielements::CustomIcon::AnimationStyle::AUGMENTED_WAVE_EFFECT
                                                              : cc::assets::uielements::CustomIcon::AnimationStyle::NONE_EFFECT);
              }
            }
          }

          //! Parking spot target position of current move during manoeuvering
          if((cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_pParkStatus->m_Data) || (cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_pParkStatus->m_Data) )
          {
            //! show the next move position slot during guidance active
            if ( f_framework->m_parkCurMoveTargetPositionReceiver.isConnected() && f_framework->m_parkIsLastMoveReceiver.isConnected() )
            {
              const cc::daddy::ParkCurMoveTargetPositionDaddy_t*  const l_pCurMoveTargetPosition   = f_framework->m_parkCurMoveTargetPositionReceiver.getData();
              const cc::daddy::ParkIsLastMoveDaddy_t*             const l_pIsLastMove              = f_framework->m_parkIsLastMoveReceiver.getData();
              if ((0 != l_pCurMoveTargetPosition) && (0 != l_pIsLastMove) && (0 != l_pParkType))
              {
                const cc::target::common::StrippedParkhmiTargetPosition l_curMoveTargetPosition = l_pCurMoveTargetPosition->m_Data;
                const bool                          l_isLastMove            = l_pIsLastMove->m_Data;
                m_flipH_b = false;
                m_flipV_b = true;
                #ifdef USE_DIFFERENT_SLOT_ICON_FOR_CORSS_PARALLEL_GUIDANCE
                if(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS == l_selectedSpaceType)
                {
                  m_strTexturePath = g_parkingSpaceSettings->m_texturePathNextCrossSlot;
                  if (LEFTSIDE == l_selectedSpaceSide)
                  {
                    m_flipH_b = true;
                    m_flipV_b = false;
                  }
                }
                else
                #endif
                {
                  m_strTexturePath = g_parkingSpaceSettings->m_texturePathNextParaSlot;
                }

                //! Display
                if ((false == l_isLastMove) && (
                    (l_isShowAtGuidanceParkin  && (cc::target::common::EParkngTypeSeld::PARKING_IN  == l_pParkType->m_Data)) ||
                    (l_isShowAtGuidanceParkout && (cc::target::common::EParkngTypeSeld::PARKING_OUT == l_pParkType->m_Data)) ) )
                {
                  updateTargetPositionManoeuvering(f_imageOverlays, l_curMoveTargetPosition,
                                                                    l_isLastMove ? cc::assets::uielements::CustomIcon::AnimationStyle::NONE_EFFECT
                                                                                 : cc::assets::uielements::CustomIcon::AnimationStyle::AUGMENTED_WAVE_EFFECT);
                }
              }
            }
          } // endif((cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_pParkStatus->m_Data) || (cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_pParkStatus->m_Data) )
        } // endif ( isGreater(l_timeDiff, g_parkingSpaceSettings->m_manoeuverShowDelay) )
      } // end else if ((l_isShowAtGuidanceParkin || l_isShowAtGuidanceParkout) && (ENUM_PS_MODE_MANOEUVER == l_retPSIMode))
      else
      {
        m_isInitTimeUpdated = false;
      }

      //! Sending data to park space mark
      cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceMarkDaddy_SenderPort.deliver();

    }

  }

}


void ParkingSpaceManager::updatePositionSearching( // PRQA S 6043  // PRQA S 6040 // PRQA S 4678
  bool& f_isParallel, // PRQA S 4287
                                                  bool& f_isLeft, // PRQA S 4287
                                                  cc::target::common::StrippedParkhmiPositionSearching& f_position,
                                                  const std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_parkSpace,
                                                  const vfc::uint8_t f_side,
                                                  const vfc::uint8_t f_number)
{
  f_position.m_x = f_parkSpace[f_side][f_number].m_APA_PSCorner2X_i16;
  f_position.m_y = f_parkSpace[f_side][f_number].m_APA_PSCorner2Y_i16;

  f_isParallel = true;
  f_isLeft = false;
  vfc::int16_t l_endPhi = 0;  // 2^(-12) rad, clockwise positive +

  switch (f_parkSpace[f_side][f_number].m_APA_PSType)
  {
    case cc::target::common::EFAPAParkSlotType::APASLOT_CROSS:
    case cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL: // APA will not send this value
      {
        f_isParallel = false;
        vfc::float32_t l_phi = f_parkSpace[f_side][f_number].m_APA_PrkgSlotSta_f32;  // in rad; 1 degree = 0.017 rad
        if(isLessEqual(l_phi, 0.0f))
        {
          l_phi = std::abs(l_phi);  // sin, cos, tan always >= 0 if 0 < phi < pi/2
        }
        if (isGreaterEqual(l_phi, g_parkingSpaceSettings->m_parkingDiagSlotAngleUpperLimit) ||
            isLessEqual(l_phi, g_parkingSpaceSettings->m_parkingDiagSlotAngleLowerLimit) )
        {
          // CROSS
          l_endPhi = 6435;  // 6435 = 90*71.5, pi/180*4096=71.5

          const vfc::int16_t l_corner1_x = f_parkSpace[f_side][f_number].m_APA_PSCorner1X_i16;
          const vfc::int16_t l_corner1_y = f_parkSpace[f_side][f_number].m_APA_PSCorner1Y_i16;
          const vfc::float32_t l_delta_x = static_cast<vfc::float32_t>(l_corner1_x) - static_cast<vfc::float32_t>(f_position.m_x);
          const vfc::float32_t l_delta_y = static_cast<vfc::float32_t>(l_corner1_y) - static_cast<vfc::float32_t>(f_position.m_y);
          vfc::float32_t l_delta_endPhi = 0.0f;

          if (0u == f_side) // left side
          {
            if (isEqual(l_delta_x, 0.0f) && isLess(l_delta_y, 0.0f))
            {
              //  obj2       obj1
              //    -----------
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    -----------
              l_endPhi = 0;
            }
            else if (isEqual(l_delta_x, 0.0f) && isGreater(l_delta_y, 0.0f))
            {
              //    -----------
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    -----------
              //  obj1       obj2
              l_endPhi = static_cast<vfc::uint8_t>(l_endPhi * 2);
            }
            else
            {
              //                      obj2
              //    --------------------
              //     |                  |
              //      |                  |
              //       |                  |
              //        --------------------
              //                          obj1
              l_delta_endPhi = std::atan(l_delta_y/l_delta_x)*4096.f;
              l_endPhi = static_cast<vfc::uint8_t>(l_endPhi - static_cast<vfc::int16_t>(l_delta_endPhi)); // PRQA S 3016
            }
          }
          else // right side
          {
            if (isEqual(l_delta_x, 0.0f) && isLess(l_delta_y, 0.0f))
            {
              //    -----------
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    -----------
              //  obj2       obj1
              l_endPhi = static_cast<vfc::uint8_t>(l_endPhi * 2);
            }
            else if (isEqual(l_delta_x, 0.0f) && isGreater(l_delta_y, 0.0f))
            {
              //  obj1       obj2
              //    -----------
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    |         |
              //    -----------
              l_endPhi = 0;
            }
            else
            {

              //                       obj2
              //    --------------------
              //     |                  |
              //      |                  |
              //       |                  |
              //        --------------------
              //                          obj1
              l_delta_endPhi = std::atan(l_delta_y/l_delta_x)*4096.f;
              l_endPhi = static_cast<vfc::uint8_t>(l_endPhi + static_cast<vfc::int16_t>(l_delta_endPhi)); // PRQA S 3016
            }
          }
        }
        else
        {
          // DIAGONAL
          vfc::int16_t l_abs_offset_cm_x = 0;
          vfc::int16_t l_abs_offset_cm_y = 0;
          vfc::uint16_t l_abs_diagonal_PSlenght_cm_x = 0u;
          const vfc::int16_t l_corner1_x = f_parkSpace[f_side][f_number].m_APA_PSCorner1X_i16;
          const vfc::int16_t l_corner1_y = f_parkSpace[f_side][f_number].m_APA_PSCorner1Y_i16;
          const vfc::int16_t l_corner2_x = f_parkSpace[f_side][f_number].m_APA_PSCorner2X_i16;
          const vfc::int16_t l_corner2_y = f_parkSpace[f_side][f_number].m_APA_PSCorner2Y_i16;

          const vfc::float32_t l_delta_x   = static_cast<vfc::float32_t>(l_corner1_x) - static_cast<vfc::float32_t>(l_corner2_x);
          const vfc::float32_t l_delta_y   = static_cast<vfc::float32_t>(l_corner1_y) - static_cast<vfc::float32_t>(l_corner2_y);
          const vfc::float32_t l_delta_phi = std::atan(std::abs(l_delta_y / l_delta_x));
          const vfc::float32_t l_old_phi   = l_phi;
          if (f_side == 0u) // left side
          {
            if (isGreaterEqual(l_delta_y, 0.0f)) // y1 >= y2
            {
              //                                ^+
              //                                |
              //                            +<---
              //                         obj2
              //       --------------------
              //      |                  |
              //     |                  |
              //    |                  |
              //   --------------------
              //                      obj1
              l_phi = l_phi + l_delta_phi;
            }
            else if (isLess(l_delta_y, 0.0f)) // y1 < y2
            {
              //                                ^+
              //                                |
              //                            +<---
              //                       obj2
              //   --------------------
              //    |                  |
              //     |                  |
              //      |                  |
              //       --------------------
              //                           obj1
              l_phi = l_phi - l_delta_phi;
            }
            else
            {

            }
          }
          else // right side
          {
            if (isGreaterEqual(l_delta_y, 0.0f)) // y1 >= y2
            {
              //       ^+
              //       |
              //   +<---
              //            obj2
              //           --------------------
              //          |                  |
              //         |                  |
              //        |                  |
              //       --------------------
              //      obj1
              l_phi = l_phi - l_delta_phi;
            }
            else if (isLess(l_delta_y, 0.0f)) // y1 < y2
            {
              //       ^+
              //       |
              //   +<---
              //      obj2
              //      --------------------
              //       |                  |
              //        |                  |
              //         |                  |
              //          --------------------
              //         obj1
              l_phi = l_phi + l_delta_phi;
            }
            else
            {
              //Do nothing
            }
          }


          vfc::float32_t l_temp = (g_parkingSpaceSettings->m_slotCrossX/std::sin(l_phi))*100.0f;
          l_abs_diagonal_PSlenght_cm_x = static_cast<vfc::uint16_t>(l_temp); // value range is within range of int16 // PRQA S 3016
          //range checking for m_APA_PSTypem_APA_PSLength_u16 in cm, : +- 1m
          if( (f_parkSpace[f_side][f_number].m_APA_PSLength_u16 <= l_abs_diagonal_PSlenght_cm_x + 100u)
            && (f_parkSpace[f_side][f_number].m_APA_PSLength_u16 >= l_abs_diagonal_PSlenght_cm_x - 100u))
          {
            l_abs_diagonal_PSlenght_cm_x = f_parkSpace[f_side][f_number].m_APA_PSLength_u16;
          }

          l_temp = static_cast<vfc::float32_t>(l_abs_diagonal_PSlenght_cm_x)*std::cos(l_old_phi)*std::cos(l_phi);
          l_abs_offset_cm_x = static_cast<vfc::int16_t>(l_temp);    // value range is within range of int16 // PRQA S 3016
          l_temp = static_cast<vfc::float32_t>(l_abs_diagonal_PSlenght_cm_x)*std::cos(l_old_phi)*std::sin(l_phi);
          l_abs_offset_cm_y = static_cast<vfc::int16_t>(l_temp);    // value range is within range of int16 // PRQA S 3016
          l_temp = (static_cast<vfc::float32_t>(l_abs_diagonal_PSlenght_cm_x) * std::sin(l_phi) - static_cast<vfc::float32_t>(g_parkingSpaceSettings->m_seachingSpacewidth_cm))*0.5f;
          const vfc::int16_t l_WidthCenterOffset_cm = static_cast<vfc::int16_t>(l_temp); // we could receive negative value // value range is within range of int16 // PRQA S 3016
          const vfc::int16_t l_abs_HeightCenterOffset_cm = static_cast<vfc::int16_t>(g_parkingSpaceSettings->m_seachingSpaceDepthOffset_cm);


          vfc::int16_t l_abs_CenterOffset_cm_x = 0;
          vfc::int16_t l_abs_CenterOffset_cm_y = 0;

          // pf code. #code looks fine
          // XLOG_ERROR_OS(g_AppContext) << "l_WidthCenterOffset_cm: " <<l_WidthCenterOffset_cm<< XLOG_ENDL;
          // Measuring base on Spacewidth= 240 cm:
          // phi: 30 ~90 deg -> l_WidthCenterOffset_cm: 10~54 cm
          if (l_WidthCenterOffset_cm >= 5) // dont consider negative and too small value
          {
            // slide on the height dimension
            l_temp = static_cast<vfc::float32_t>(l_WidthCenterOffset_cm ) * std::cos(l_phi);
            l_abs_CenterOffset_cm_y = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016
            l_temp = static_cast<vfc::float32_t>(l_abs_CenterOffset_cm_y) * std::tan(l_phi);
            l_abs_CenterOffset_cm_x = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016
          }

          l_temp = l_phi*4096.0f;
          l_endPhi = static_cast<vfc::int16_t>(l_temp);  // value range is within range of int16 // PRQA S 3016

          if (0u == f_side) // left side
          {
            f_position.m_x =  f_position.m_x - l_abs_offset_cm_x - l_abs_CenterOffset_cm_x;
            f_position.m_y =  f_position.m_y + l_abs_offset_cm_y - l_abs_CenterOffset_cm_y;

            if (l_abs_HeightCenterOffset_cm >= 5)  // dont consider too small value
            {
              // slide on the width dimension
              // re-calculate for the Center Offset
              // this formula also valid if l_WidthCenterOffset_cm ~ 0
              l_temp = static_cast<vfc::float32_t>(l_abs_HeightCenterOffset_cm) * std::sin(l_phi);
              l_abs_CenterOffset_cm_y = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016
              l_temp = static_cast<vfc::float32_t>(l_abs_HeightCenterOffset_cm) * std::cos(l_phi);
              l_abs_CenterOffset_cm_x = static_cast<vfc::int16_t>(l_temp); // value range is within range of int16 // PRQA S 3016

              f_position.m_x =  f_position.m_x - l_abs_CenterOffset_cm_x;
              f_position.m_y =  f_position.m_y + l_abs_CenterOffset_cm_y;
            }

          }
          else // right side
          {
            f_position.m_x =  f_position.m_x - l_abs_offset_cm_x - l_abs_CenterOffset_cm_x;
            f_position.m_y =  f_position.m_y - l_abs_offset_cm_y + l_abs_CenterOffset_cm_y;

            if (l_abs_HeightCenterOffset_cm >= 5)
            {
              // slide on the width dimension
              // re-calculate for the Center Offset
              // this formula also valid if l_WidthCenterOffset_cm ~ 0
              l_temp = static_cast<vfc::float32_t>(l_abs_HeightCenterOffset_cm) * std::sin(l_phi);
              l_abs_CenterOffset_cm_y = static_cast<vfc::int16_t>(l_temp);  // value range is within range of int16 // PRQA S 3016
              l_temp = static_cast<vfc::float32_t>(l_abs_HeightCenterOffset_cm) * std::cos(l_phi);
              l_abs_CenterOffset_cm_x = static_cast<vfc::int16_t>(l_temp);  // value range is within range of int16 // PRQA S 3016

              f_position.m_x =  f_position.m_x - l_abs_CenterOffset_cm_x;
              f_position.m_y =  f_position.m_y - l_abs_CenterOffset_cm_y;
            }
          }
        }
      }
      break;
    case cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL:
      {
        f_isParallel = true;
        l_endPhi = 0;
        const vfc::int16_t l_corner1_x = f_parkSpace[f_side][f_number].m_APA_PSCorner1X_i16;
        const vfc::int16_t l_corner1_y = f_parkSpace[f_side][f_number].m_APA_PSCorner1Y_i16;
        const vfc::float32_t l_delta_x = static_cast<vfc::float32_t>(l_corner1_x) - static_cast<vfc::float32_t>(f_position.m_x);
        const vfc::float32_t l_delta_y = static_cast<vfc::float32_t>(l_corner1_y) - static_cast<vfc::float32_t>(f_position.m_y);
        vfc::float32_t l_delta_endPhi = 0.0f;
        constexpr vfc::int16_t l_tmp_endPhi = 6435;  //  90*pi/180*4096 = 90*71.5 = 6435

        if (0u == f_side) // left side
        {
          if (isEqual(l_delta_x, 0.0f) && isLess(l_delta_y, 0.0f))
          {
            l_endPhi = -l_tmp_endPhi;
          }
          else if (isEqual(l_delta_x, 0.0f) && isGreater(l_delta_y, 0.0f))
          {
            l_endPhi = l_tmp_endPhi;
          }
          else
          {
            l_delta_endPhi = std::atan(l_delta_y/l_delta_x)*4096.f;
            l_endPhi = static_cast<vfc::uint8_t>(l_endPhi - static_cast<vfc::int16_t>(l_delta_endPhi)); // PRQA S 3016
          }
        }
        else // right side
        {
          if (isEqual(l_delta_x, 0.0f) && isLess(l_delta_y, 0.0f))
          {
            l_endPhi = l_tmp_endPhi;
          }
          else if (isEqual(l_delta_x, 0.0f) && isGreater(l_delta_y, 0.0f))
          {
            l_endPhi = -l_tmp_endPhi;
          }
          else
          {
            l_delta_endPhi = std::atan(l_delta_y/l_delta_x)*4096.f;
            l_endPhi = static_cast<vfc::uint8_t>(l_endPhi + static_cast<vfc::int16_t>(l_delta_endPhi)); // PRQA S 3016
          }
        }
      }
      break;
    default:
      {break;}
  }

  if (0u == f_side)
  {
    f_isLeft = true;
    f_position.m_phi = l_endPhi;
  }
  else
  {
    f_isLeft = false;
    f_position.m_phi = -l_endPhi;   // 2^(-12) rad, clockwise positive +
  }

  return;
}


void ParkingSpaceManager::updateParkingSpotSearching( vfc::uint8_t f_index,
                                                      cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
                                                      const cc::target::common::StrippedParkhmiPositionSearching& f_position,
                                                      const bool f_isParallel,
                                                      const bool f_isLeft,
                                                      const core::CustomFramework* f_framework)
{
  m_flipH_b              = false;
  m_flipV_b              = true;

  const vfc::float32_t l_phi = static_cast<vfc::float32_t>(f_position.m_phi)*(1.f/4096.f);  // in rad

  osg::Vec2f l_positionOrigin = osg::Vec2f(0.0f, 0.0f);  // in m
  if (f_isParallel)
  {
    l_positionOrigin.x() = static_cast<vfc::float32_t>(f_position.m_x)*(1.f/100.f);
    l_positionOrigin.y() = static_cast<vfc::float32_t>(f_position.m_y)*(1.f/100.f);
    if (f_isLeft)
    {
      m_flipH_b              = true;
      m_flipV_b              = true;
    }
  }
  else if (f_isLeft)
  {
    l_positionOrigin.x() = static_cast<vfc::float32_t>(f_position.m_x)*(1.f/100.f) - g_parkingSpaceSettings->m_slotCrossX *std::sin(l_phi);
    l_positionOrigin.y() = static_cast<vfc::float32_t>(f_position.m_y)*(1.f/100.f) - g_parkingSpaceSettings->m_slotCrossX *std::cos(l_phi);
    m_flipH_b              = false;
    m_flipV_b              = false;
  }
  else
  {
    l_positionOrigin.x() = static_cast<vfc::float32_t>(f_position.m_x)*(1.f/100.f) + g_parkingSpaceSettings->m_slotCrossX *std::sin(l_phi);
    l_positionOrigin.y() = static_cast<vfc::float32_t>(f_position.m_y)*(1.f/100.f) + g_parkingSpaceSettings->m_slotCrossX *std::cos(l_phi);
    m_flipH_b              = false;
    m_flipV_b              = true;
  }

  // parking slot data is lagging behind the video due to PLD and APA run time synchronization.
  // thus the parking space overlay will be nearer to the vehicle when moving forward.
  // simplified work around solution: only x direction is considered. the gap in y direction has little influence
  if(f_framework->m_speedReceiver.isConnected() && f_framework->m_drivingDirReceiver.isConnected())
  {
    const pc::daddy::SpeedDaddy* const l_speedDaddy = f_framework->m_speedReceiver.getData();
    const pc::daddy::DrivingDirDaddy* const l_drvDirDaddy = f_framework->m_drivingDirReceiver.getData();
    vfc::float32_t l_curSpeed = 0.0f;
    pc::daddy::DrivingDirection l_drvDir = pc::daddy::DRVDIR_NONE;
    if (nullptr != l_speedDaddy && nullptr != l_drvDirDaddy)
    {
      l_curSpeed = l_speedDaddy->m_Data;
      l_drvDir = static_cast<pc::daddy::DrivingDirection>(l_drvDirDaddy->m_Data); // PRQA S 4899
    }
    if( pc::daddy::DRVDIR_FORWARD == l_drvDir)
    {
      l_positionOrigin.x() = l_positionOrigin.x() - l_curSpeed * g_parkingSpaceSettings->m_pldDelayDuration / 3.6f;
    }
  }

  const osg::Vec3f l_temp = osg::Vec3f(l_positionOrigin, 0.0f);
  // transfer the vehicle coordinate to view port coordinate
  osg::Matrixf l_mat;
  const bool l_ret_b = m_planViewCullCall->getRefMatrix(l_mat);
  if (!l_ret_b)
  {
    // if not valid matrix input, stop drawing.
    return;
  }

  osg::Vec3f l_screenCoord3D = l_temp * l_mat;
  osg::Vec2f l_screenCoord2D = osg::Vec2f(0.f, 0.f);
  // l_screenCoord2D.x() = l_screenCoord3D.x() - cc::core::g_views->m_mainViewport.m_origin.x();
  // l_screenCoord2D.y() = l_screenCoord3D.y() - cc::core::g_views->m_mainViewport.m_origin.y();
  l_screenCoord2D.x() = l_screenCoord3D.x() - static_cast<vfc::float32_t>(m_viewport.m_origin.x());
  l_screenCoord2D.y() = l_screenCoord3D.y() - static_cast<vfc::float32_t>(m_viewport.m_origin.y());

  cc::assets::uielements::CustomIcon* const l_icon = dynamic_cast<cc::assets::uielements::CustomIcon* >(m_iconParkingSpace.getIcon(f_index)); // PRQA S 3077  // PRQA S 3400

  l_icon->setTexture(m_textureInterface);
  l_icon->setPosition(l_screenCoord2D, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(m_manoeuveringIconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setFlipHorizontal(m_flipH_b);
  l_icon->setFlipVertical(m_flipV_b);
  l_icon->setRotateAngle(l_phi);
  l_icon->setEnabled(true);

}


#if PARKINGSPACE_SEARCHING_DEBUG
void ParkingSpaceManager::updateParkingObjSearching(
  cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
  const cc::target::common::StrippedParkhmiPositionSearching& f_position,
  const bool f_isParallel,
  const bool f_isLeft,
  const std::string& f_strTexturePath)
{
  m_flipH_b              = false;
  m_flipV_b              = true;
  float l_phi = static_cast<float>(f_position.m_phi)*(1.f/4096.f);  // in rad
  osg::Vec2f l_positionOrigin = osg::Vec2f(0.0f, 0.0f);  // in m
  l_positionOrigin.x() = static_cast<float>(f_position.m_x)*(1.f/100.f);
  l_positionOrigin.y() = static_cast<float>(f_position.m_y)*(1.f/100.f);

  osg::Vec3f l_temp = osg::Vec3f(l_positionOrigin, 0.0f);
  // transfer the vehicle coordinate to view port coordinate
  osg::Matrixf l_mat;
  bool l_ret_b = m_planViewCullCall->getRefMatrix(l_mat);
  if (!l_ret_b)
  {
    return; // if not valid matrix input, stop drawing.
  }
  osg::Vec3f l_screenCoord3D = l_temp * l_mat;
  osg::Vec2f l_screenCoord2D = osg::Vec2f(0.f, 0.f);
  l_screenCoord2D.x() = l_screenCoord3D.x() - static_cast<float>(m_viewport.m_origin.x());
  l_screenCoord2D.y() = l_screenCoord3D.y() - static_cast<float>(m_viewport.m_origin.y());
  pc::assets::Icon* icon_pst = createIcon(f_strTexturePath, l_screenCoord2D, osg::Vec2f(18.0f, 18.0f),
                                          m_flipH_b, m_flipV_b, f_isLeft, cc::assets::uielements::CustomIcon::AnimationStyle::NONE_EFFECT, 0.0f);
  m_iconParkingSpace.addIcon(f_imageOverlays, icon_pst);

}
#endif //PARKINGSPACE_SEARCHING_DEBUG


void ParkingSpaceManager::updateTargetPositionManoeuvering(
  cc::assets::uielements::CustomImageOverlays* f_imageOverlays,
  const cc::target::common::StrippedParkhmiTargetPosition& f_targetPosition,
  const cc::assets::uielements::CustomIcon::AnimationStyle f_animationEffectOfIcon)
{
  constexpr bool l_isLeft  = false;

  const vfc::float32_t l_phi = static_cast<vfc::float32_t>(f_targetPosition.m_phi)*(1.f/4096.f);  // in rad
  const vfc::float32_t l_beta = (static_cast<vfc::float32_t> (osg::PI))/2.0f - l_phi - m_veh_alpha;  // rad

  osg::Vec2f l_positionOrigin = osg::Vec2f(0.0f, 0.0f);  // in m
  l_positionOrigin.x() = static_cast<vfc::float32_t>(f_targetPosition.m_x)*(1.f/1024.f) + m_veh_length * std::sin(l_beta);
  l_positionOrigin.y() = static_cast<vfc::float32_t>(f_targetPosition.m_y)*(1.f/1024.f) + m_veh_length * std::cos(l_beta);

  const osg::Vec3f l_temp = osg::Vec3f(l_positionOrigin, 0.0f);
  // transfer the vehicle coordinate to view port coordinate
  osg::Matrixf l_mat;
  const bool l_ret_b = m_planViewCullCall->getRefMatrix(l_mat);
  if (!l_ret_b)
  {
    // if not valid matrix input, stop drawing.
    return;
  }

  osg::Vec3f l_screenCoord3D = l_temp * l_mat;
  osg::Vec2f l_screenCoord2D = osg::Vec2f(0.f, 0.f);
  // l_screenCoord2D.x() = l_screenCoord3D.x() - cc::core::g_views->m_mainViewport.m_origin.x();
  // l_screenCoord2D.y() = l_screenCoord3D.y() - cc::core::g_views->m_mainViewport.m_origin.y();
  l_screenCoord2D.x() = l_screenCoord3D.x() - static_cast<vfc::float32_t>(m_viewport.m_origin.x());
  l_screenCoord2D.y() = l_screenCoord3D.y() - static_cast<vfc::float32_t>(m_viewport.m_origin.y());


  // parking direction to the destination
  cc::assets::uielements::CustomIcon::AnimationDir l_animationDir = cc::assets::uielements::CustomIcon::AnimationDir::START_FROM_TOP;
  if (f_targetPosition.m_x > 0)
  {
    l_animationDir = cc::assets::uielements::CustomIcon::AnimationDir::START_FROM_BOTTOM;
  }
  else
  {
    // start from top
  }

  pc::assets::Icon* const icon_pst = createIcon(m_strTexturePath, l_screenCoord2D, m_manoeuveringIconSize,
                                          m_flipH_b, m_flipV_b, l_isLeft, f_animationEffectOfIcon, l_animationDir, -l_phi);
  m_iconParkingSpaceGuidance.addIcon(f_imageOverlays, icon_pst);

}


vfc::uint32_t ParkingSpaceManager::checkAPAStatus(
  const vfc::uint8_t f_status,
  const std::array<std::array<cc::target::common::StrippedEAPAParkSpace, cc::target::common::l_L_ParkSpace_NumberPerside>, cc::target::common::l_L_ParkSpace_side> f_parkSpace)
{
  bool l_slotSelected = false;
  for(vfc::uint8_t l_side = 0u; l_side < cc::target::common::l_L_ParkSpace_side; l_side++ )
  {
    for(vfc::uint8_t l_numberPerside = 0u; l_numberPerside < cc::target::common::l_L_ParkSpace_NumberPerside; l_numberPerside++)
    {
      if (f_parkSpace[l_side][l_numberPerside].m_APA_PrkgSlot == cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED)
      {
        l_slotSelected = true;
      }
    }
  }

  if ( (static_cast<vfc::uint8_t>(cc::target::common::EPARKStatusR2L::PARK_Searching) == f_status) || (static_cast<vfc::uint8_t>(cc::target::common::EPARKStatusR2L::PARK_AssistStandby) == f_status) )
  {
    if (l_slotSelected)
    {
      return ENUM_PS_MODE_SELECTING;
    }
    else
    {
      return ENUM_PS_MODE_SCANING;
    }
  }
  else if ((static_cast<vfc::uint8_t>(cc::target::common::EPARKStatusR2L::PARK_Guidance_active) == f_status) || (static_cast<vfc::uint8_t>(cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend) == f_status) || (static_cast<vfc::uint8_t>(cc::target::common::EPARKStatusR2L::PARK_Completed) == f_status))
  {
    return ENUM_PS_MODE_MANOEUVER;
  }
  else
  {
    return ENUM_PS_MODE_NONE;
  }
}

void ParkingSpaceManager::checkShowSts( // PRQA S 4678
  const vfc::uint8_t f_parkType,         // parkin, parkout
  const vfc::uint8_t f_parkMode,         // apa, rpa
  const bool         f_freeParkActive,   // inactive, active
  bool&              f_isShowAtSearching, // PRQA S 4287
  bool&              f_isShowAtSelected, // PRQA S 4287
  bool&              f_isShowAtGuidanceParkin, // PRQA S 4287
  bool&              f_isShowAtGuidanceParkout) // PRQA S 4287
{
  //! Searched and selected parkslot only shown when parkin & freeparking inactive
  //! Next move and target parkslot shown during guidance

  f_isShowAtSearching       = false;
  f_isShowAtSelected        = false;
  f_isShowAtGuidanceParkin  = false;
  f_isShowAtGuidanceParkout = false;

  if ((static_cast<vfc::uint8_t>(cc::target::common::EParkngTypeSeld::PARKING_IN) == f_parkType) && (false == f_freeParkActive) && static_cast<bool>(1u & g_parkingSpaceSettings->m_enablePSMode))
  {
    f_isShowAtSearching = true;
  }

  if ((static_cast<vfc::uint8_t>(cc::target::common::EParkngTypeSeld::PARKING_IN) == f_parkType) && (false == f_freeParkActive) && static_cast<bool>(2u & g_parkingSpaceSettings->m_enablePSMode))
  {
    f_isShowAtSelected = true;
  }

  if ((static_cast<vfc::uint8_t>(cc::target::common::EParkngTypeSeld::PARKING_IN) == f_parkType) && static_cast<bool>(4u & g_parkingSpaceSettings->m_enablePSMode))
  {
    f_isShowAtGuidanceParkin = true;
  }

  if ((static_cast<vfc::uint8_t>(cc::target::common::EParkngTypeSeld::PARKING_OUT) == f_parkType) && static_cast<bool>(8u & g_parkingSpaceSettings->m_enablePSMode))
  {
    f_isShowAtGuidanceParkout = true;
  }
}

pc::assets::Icon* ParkingSpaceManager::createIcon(
  const std::string& f_iconPath,
  const bool& f_isLeft) const
{
  if (false == osgDB::fileExists(f_iconPath))
  {
    XLOG_ERROR(g_AppContext, "parking slot icon not found");
    return nullptr;
  }
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, f_isLeft, true, false, m_isHoriScreen); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Left);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Top);
  return l_icon;
}

pc::assets::Icon* ParkingSpaceManager::createIcon(
  const std::string& f_iconPath,
  const osg::Vec2f& f_iconPos,
  const osg::Vec2f& f_iconSize,
  const bool& f_flipHorinzontal,
  const bool& f_flipVertical,
  const bool& f_isLeft,
  const cc::assets::uielements::CustomIcon::AnimationStyle f_animationStyle,
  const cc::assets::uielements::CustomIcon::AnimationDir f_animationDir,
  const vfc::float32_t& f_angle) const
{
  if (false == osgDB::fileExists(f_iconPath))
  {
    // pf code. #code looks fine
    XLOG_ERROR(g_AppContext, "parking slot icon not found");
    return nullptr;
  }
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, f_isLeft, true, false, m_isHoriScreen, f_animationStyle, f_animationDir); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Left);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Top);
  l_icon->setFlipHorizontal(f_flipHorinzontal);
  l_icon->setFlipVertical(f_flipVertical);
  l_icon->setRotateAngle(f_angle);
  l_icon->setEnabled(true);
  return l_icon;
}


//!
//! @brief Construct a new Parking Space:: Parking Space object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
ParkingSpace::ParkingSpace(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId, osg::Camera* f_view , const pc::core::Viewport& f_viewport, const bool& f_isHoriScreen, cc::views::planview::PlanViewCullCallback* f_cullcallback)
  : cc::assets::uielements::CustomImageOverlays{f_assetId, f_view, cc::assets::uielements::CustomIcon::AnimationStyle::AUGMENTED_WAVE_EFFECT, g_parkingSpaceSettings->m_parkIconNumberOfDelayTraversalCycle}
  , m_customFramework{f_customFramework}
  , m_manager{f_cullcallback, f_viewport, f_isHoriScreen}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
  CustomSetRenderOrder(500u, true); // PRQA S 2759
}


ParkingSpace::~ParkingSpace() =  default;


void ParkingSpace::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    m_manager.update(this, m_customFramework, &f_nv);
  }

  if ( g_parkingSpaceSettings->m_isEnabled )
  {
    pc::assets::ImageOverlays::traverse(f_nv);
  }
  else
  {
    return;
  }

}


} // namespace warnsymbols
} // namespace assets
} // namespace cc

