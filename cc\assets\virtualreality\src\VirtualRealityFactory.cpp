//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityFactory.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/VirtualRealityFactory.h"


namespace cc
{
namespace assets
{
namespace virtualreality
{


VirtualRealityFactory::VirtualRealityFactory() = default;



VirtualRealityFactory::~VirtualRealityFactory() = default;



VirtualRealityObject* VirtualRealityFactory::createObject(EObjectType f_objectType)
{
    switch (f_objectType)
    {
        case EObjectType::SLOT :
            {return new VirtualParkSlot;
        break;} // PRQA S 2880
        case EObjectType::LOWPOLYVEHICLE :
            {return new LowpolyVehicle;
        break;} // PRQA S 2880
        case EObjectType::EGOVEHICLE :
            {return new VirtualEgoVehicle;
        break;} // PRQA S 2880
        case EObjectType::LOWPOLYPEDESTRAIN :
            {return new LowpolyPedestrian;
        break;} // PRQA S 2880
        default:
            {return nullptr;}
    }
}

} // namespace virtualreality
} // namespace assets
} // namespace cc
