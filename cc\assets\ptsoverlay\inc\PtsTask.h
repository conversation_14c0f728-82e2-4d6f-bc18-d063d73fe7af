//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_PTSOVERLAY_PTSTASK_H
#define CC_ASSETS_PTSOVERLAY_PTSTASK_H

#include "cc/assets/ptsoverlay/inc/PtsSpline.h"
#include "cc/assets/ptsoverlay/inc/PtsOverlay.h"
#include "pc/svs/worker/core/inc/Task.h"
#include "pc/svs/util/math/inc/FilterSpatial.h"

//! forward declarations
namespace pc
{
namespace worker
{
namespace fusion
{
class FusionTask;
} // namespace fusion
} // namespace worker
} // namespace pc


namespace cc
{
namespace assets
{
namespace ptsoverlay
{


//!
//! PtsExtractor
//!
class PtsExtractor
{
public:

  typedef std::vector<osg::Vec2> Vec2List;
  typedef std::vector<unsigned char> UByteList;

  PtsExtractor(pc::vehicle::AbstractZoneLayout* f_zoneLayout);

  float getDefaultDistance() const;

  const pc::util::FloatList getDistances() const;

  void setDistances(const pc::util::FloatList& f_distances);

  const pc::util::FloatList getCutoffDistances() const;

  const UByteList getPasZoneMappings() const;

  pc::util::Polygon2D toPolygon2D() const;

  void update(const pc::vehicle::UltrasonicData& f_ussData);

private:

  void init();

  osg::ref_ptr<pc::vehicle::AbstractZoneLayout> m_zoneLayout;
  float m_defaultDistance;
  Vec2List m_origins;
  Vec2List m_directions;
  UByteList m_pasZoneMappings;
  pc::util::FloatList m_distances;
  pc::util::FloatList m_cutoffDistances;

};


//!
//! ProcessingTask
//!
class ProcessingTask : public pc::worker::core::Task
{
public:

  typedef pc::util::SpatialFilter<pc::util::FloatList> SpatialFilter;

  ProcessingTask(PtsOverlay* f_ptsOverlay, pc::vehicle::AbstractZoneLayout* f_zoneLayout);

  virtual bool onRun(pc::worker::core::TaskManager* f_taskManager) override;

protected:

  virtual ~ProcessingTask() = default;

private:

  osg::observer_ptr<PtsOverlay> m_ptsOverlay;
  PtsExtractor m_extractor;
  osg::ref_ptr<SpatialFilter> m_distanceFilter;
  osg::ref_ptr<PtsUpdateVisitor> m_updateVisitor;
  pc::util::FloatList m_distancesCurrent;
  pc::util::FloatList m_distancesTarget;
  pc::util::FloatList m_distancesColoring;

};

} // namespace ptsoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_PTSOVERLAY_PTSTASK_H