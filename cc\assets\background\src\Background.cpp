//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/background/inc/Background.h"
#include "cc/core/inc/CustomScene.h"

#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include <osg/Geode>
#include <osg/Geometry>
#include <osg/Texture2D>
#include <osgDB/ReadFile>

namespace cc
{
namespace assets
{

class BackgroundData : public pc::util::coding::ISerializable
{
public:
  BackgroundData()
  : m_backgroundTextureFilename("cc/vehicle_model/ui/01_background.png")
  {
  }

  SERIALIZABLE(BackgroundData)
  {
    ADD_STRING_MEMBER(backgroundTextureFilename);
  }

  std::string m_backgroundTextureFilename;
};

pc::util::coding::Item<BackgroundData> g_backgroundSettings("BackgroundData");

Background::Background()
  : osg::Camera()
{
  setReferenceFrame(osg::Transform::ABSOLUTE_RF);
  setViewMatrix(osg::Matrix::identity());
  setRenderOrder(osg::Camera::PRE_RENDER);
  setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f/*, -1.f, 1.f*/);
  osg::StateSet* l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
  l_stateSet->setRenderBinDetails(pc::core::g_renderOrder->m_carOpaque - 1, "RenderBin");

  osg::Geometry* l_quad = osg::createTexturedQuadGeometry(
    osg::Vec3f(0.0f, 0.0f, 0.0f), // corner
    osg::Vec3f(1.0f, 0.0f, 0.0f), // width
    osg::Vec3f(0.0f, 1.0f, 0.0f)); // height

  osg::Image* l_image = osgDB::readImageFile(g_backgroundSettings->m_backgroundTextureFilename);
  osg::Texture2D* l_texture = new osg::Texture2D(l_image);
  l_texture->setDataVariance(osg::Object::STATIC);
  l_texture->setUnRefImageDataAfterApply(true);
  l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::REPEAT);
  l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::REPEAT);
  l_stateSet->setTextureAttribute(0, l_texture);

  osg::Geode* l_quadGeode = new osg::Geode();
  l_quadGeode->addDrawable(l_quad);  // PRQA S 3803
  osg::Group::addChild(l_quadGeode);  // PRQA S 3803
}


Background::~Background()
{
}

} // namespace assets
} // namespace cc