//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  InfoPanels.cpp
/// @brief 
//=============================================================================

#include "cc/assets/infopanels/inc/InfoPanels.h"
#include "pc/svs/core/inc/ShaderManager.h"

#include <osg/Geometry>
#include <osg/Texture2D>
#include <osgDB/ReadFile>


namespace cc
{
namespace assets
{
namespace infopanel
{

osg::observer_ptr<osg::StateSet> g_sharedStateSet;

//!
//! InfoPanel
//!
InfoPanel::InfoPanel(
  const std::string& f_name, const pc::core::Viewport& f_viewport, const std::string& f_bitmap, float f_alpha)
  : pc::core::View(f_name, f_viewport)
{
  //! Idle Icon as HUD Camera
  setRenderOrder(osg::Camera::POST_RENDER, std::numeric_limits<int>::max());
  setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f);

  if (!g_sharedStateSet.valid())
  {
    g_sharedStateSet = new osg::StateSet;
    pc::core::TextureShaderProgramDescriptor l_infoPanelShader("infoPanel");
    l_infoPanelShader.apply(g_sharedStateSet.get());
    //! set default value for the  alpha uniform to 1.0
    osg::Uniform* l_alphaUniform = g_sharedStateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT);
    l_alphaUniform->set(1.0f);
    g_sharedStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
  }
  setStateSet(g_sharedStateSet.get());

  //! Add group as child to the HUD camera
  addChild(createInfoPanelGroup( f_bitmap, f_alpha ));
}


InfoPanel::~InfoPanel()
{
}


osg::Geode* InfoPanel::createInfoPanelGroup(const std::string& f_bitmap, float f_alpha)
{
  //! Load icon
  osg::Image* testImage = osgDB::readImageFile( f_bitmap );

  //! Set up 2D Texture
  osg::Texture2D* l_pIconTexture = new osg::Texture2D;
  l_pIconTexture->setName("texture for InfoPanel");
  l_pIconTexture->setResizeNonPowerOfTwoHint(false);

  l_pIconTexture->setImage(testImage);
  l_pIconTexture->setWrap(osg::Texture::WRAP_R, osg::Texture::CLAMP_TO_EDGE);
  l_pIconTexture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_pIconTexture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  l_pIconTexture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
  l_pIconTexture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

  //! Quad geometry
  osg::Geometry* l_quad = osg::createTexturedQuadGeometry(osg::Vec3f(0.0f, 0.0f, 0.0f),
                                                          osg::Vec3f(1.0f, 0.0f, 0.0f),
                                                          osg::Vec3f(0.0f, 1.0f, 0.0f));
  l_quad->setUseDisplayList(false);
  l_quad->setUseVertexBufferObjects(true);

  //! Create geode and set StateSets
  osg::Geode* l_pInfoPanelGeode = new osg::Geode;
  l_pInfoPanelGeode->addDrawable(l_quad);

  osg::StateSet* l_pInfoPanelStateSet = l_pInfoPanelGeode->getOrCreateStateSet();
  l_pInfoPanelStateSet->setTextureAttribute(0, l_pIconTexture);
  l_pInfoPanelStateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); //always draw
  osg::Uniform *l_alphaUniform = l_pInfoPanelStateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT);
  l_alphaUniform->set(f_alpha);

  return l_pInfoPanelGeode;
}


} // namespace SidePanel
} // namespace assets
} // namespace cc