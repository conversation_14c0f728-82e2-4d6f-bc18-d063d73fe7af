//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BJEV AVAP
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BJEV
/// @file  TileSettings.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_TILEOVERLAY_TILESETTINGS_H
#define CC_ASSETS_TILEOVERLAY_TILESETTINGS_H

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/math/inc/FilterSpatial.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include <osg/Vec4i>
#include <cassert>

namespace cc
{
namespace assets
{
namespace tileoverlay
{

//!
//! ColorValues
//!
class ColorValues : public pc::util::coding::ISerializable
{
public:

  enum { NUM_COLORS = 6 };

  //! for BYD colors
  ColorValues()
    : m_r2(241,   29,   30, 255)
    , m_r1(241,   29,   30, 255)
    , m_y2(244,  179,    0, 255)
    , m_y1(244,  179,    0, 255)
    , m_g2(  0,  205,   69, 255)
    , m_g1(  0,  205,   69, 255)
  {
  }

  SERIALIZABLE(ColorValues)
  {
    ADD_MEMBER(osg::Vec4i, r2);
    ADD_MEMBER(osg::Vec4i, r1);
    ADD_MEMBER(osg::Vec4i, y2);
    ADD_MEMBER(osg::Vec4i, y1);
    ADD_MEMBER(osg::Vec4i, g2);
    ADD_MEMBER(osg::Vec4i, g1);
  }

  const osg::Vec4i& getColor(unsigned int f_idx) const
  {
    assert(static_cast<int>(f_idx) < NUM_COLORS);
    return *(&m_r2 + f_idx);
  }

  osg::Vec4i m_r2;
  osg::Vec4i m_r1;
  osg::Vec4i m_y2;
  osg::Vec4i m_y1;
  osg::Vec4i m_g2;
  osg::Vec4i m_g1;
};

//!
//! TileSettings
//!
class TileSettings : public pc::util::coding::ISerializable
{
public:

  TileSettings()
    : m_extractorOffset(-0.25f)
    , m_heightOverGround(0.01f)
    , m_projectionTransitionCamAngle(55.0f, 65.0f)
    , m_heightMinThreshold(0.1f)
    , m_contourAlpha(0.75f)
    , m_contourHeight(0.02f)
    , m_shadowRadius(0.3f)
    , m_spline2DWidthInnerNear(0.05f)
    , m_spline2DWidthInnerFar(0.02f)
    , m_spline2DWidthOuter(0.025f)
    , m_shield3DAlphaRatioTop(0.05f)
    , m_shield3DAlphaRatioDivide(0.3f)
    , m_shield3DAlphaRatioBottom(0.7f)
    , m_shield3DSideAlphaRatioTop(0.02f)
    , m_shield3DSideAlphaRatioDivide(0.7f)
    , m_shield3DSideAlphaRatioBottom(0.7f)
    , m_shield3DBottomCoverAlphaRatio(1.0f)
    , m_shieldAlpha(1.0f)
    , m_solidLine2DAlpha(1.0f)
    , m_shadow2DAlphaInner(0.9f)
    , m_shadow2DAlpha(0.35f)
    , m_shieldHeight(1.0f)
    , m_shieldThickness(0.2f)
    , m_solidLineThicknessRatio(0.3f)
    , m_shield3DThicknessRatio(0.1f)
    , m_shieldOffset(0.03f)
    , m_shieldTopOffset(0.0f)
    , m_shieldRatioDivide(0.3f)
    , m_shieldSideRatioDivide(0.5f)
    , m_hairlineWidth(1.0f)
    , m_hairlineAlpha(0.5f)
    , m_hairlineBlooming(0.6f)
    , m_hairlineAlphaRatioTop(0.1f)
    , m_hairlineAlphaRatioDivide(0.5f)
    , m_hairlineAlphaRatioBottom(0.9f)
    , m_distanceDefault(1.25f)
    , m_distanceThreshL1(0.3f)
    , m_distanceThreshL2(0.7f)
    , m_distanceThreshL3(1.2f)
    , m_HysteresisDistanceThresh(0.05f)
    , m_MinDistanceThresh(0.30f)
    , m_MinDistanceThreshForPosDisp(0.3f)
    , m_distanceThreshCorner(0.6f)
    , m_smoothingTextureSize(32u)
    , m_smoothingGradientSize(0.20f)
    , m_colorOff(108, 108, 108, 255)
    , m_colorOn(0, 254, 172, 255)
    , m_colorOutline(15, 47, 63, 255)
    , m_colorShadow(26, 26, 26, 89)
    , m_distanceFilter(2u, 5u, 0.4f)
    , m_SplineTypeForEachSegment(false)
    , m_smoothingFrontCornerSplineType(0.36f)
    , m_smoothingRearCornerSplineType(0.72f)
  {
  }

  SERIALIZABLE(TileSettings)
  {
    ADD_FLOAT_MEMBER(extractorOffset);
    ADD_FLOAT_MEMBER(heightOverGround);
    ADD_MEMBER(osg::Vec2f, projectionTransitionCamAngle);
    ADD_FLOAT_MEMBER(heightMinThreshold);
    ADD_FLOAT_MEMBER(contourAlpha);
    ADD_FLOAT_MEMBER(contourHeight);
    ADD_FLOAT_MEMBER(shadowRadius);
    ADD_FLOAT_MEMBER(spline2DWidthInnerNear);
    ADD_FLOAT_MEMBER(spline2DWidthInnerFar);
    ADD_FLOAT_MEMBER(spline2DWidthOuter);
    ADD_FLOAT_MEMBER(shield3DAlphaRatioTop);
    ADD_FLOAT_MEMBER(shield3DAlphaRatioDivide);
    ADD_FLOAT_MEMBER(shield3DAlphaRatioBottom);
    ADD_FLOAT_MEMBER(shield3DSideAlphaRatioTop);
    ADD_FLOAT_MEMBER(shield3DSideAlphaRatioDivide);
    ADD_FLOAT_MEMBER(shield3DSideAlphaRatioBottom);
    ADD_FLOAT_MEMBER(shield3DBottomCoverAlphaRatio);
    ADD_FLOAT_MEMBER(shieldAlpha);
    ADD_FLOAT_MEMBER(solidLine2DAlpha);
    ADD_FLOAT_MEMBER(shadow2DAlphaInner);
    ADD_FLOAT_MEMBER(shadow2DAlpha);
    ADD_FLOAT_MEMBER(shieldHeight);
    ADD_FLOAT_MEMBER(shieldThickness);
    ADD_FLOAT_MEMBER(solidLineThicknessRatio);
    ADD_FLOAT_MEMBER(shield3DThicknessRatio);
    ADD_FLOAT_MEMBER(shieldOffset);
    ADD_FLOAT_MEMBER(shieldTopOffset);
    ADD_FLOAT_MEMBER(shieldRatioDivide);
    ADD_FLOAT_MEMBER(shieldSideRatioDivide);
    ADD_FLOAT_MEMBER(hairlineWidth);
    ADD_FLOAT_MEMBER(hairlineAlpha);
    ADD_FLOAT_MEMBER(hairlineBlooming);
    ADD_FLOAT_MEMBER(hairlineAlphaRatioTop);
    ADD_FLOAT_MEMBER(hairlineAlphaRatioDivide);
    ADD_FLOAT_MEMBER(hairlineAlphaRatioBottom);
    ADD_FLOAT_MEMBER(distanceDefault);
    ADD_FLOAT_MEMBER(distanceThreshL1);
    ADD_FLOAT_MEMBER(distanceThreshL2);
    ADD_FLOAT_MEMBER(distanceThreshL3);
    ADD_FLOAT_MEMBER(HysteresisDistanceThresh);
    ADD_FLOAT_MEMBER(MinDistanceThresh);
    ADD_FLOAT_MEMBER(MinDistanceThreshForPosDisp);
    ADD_FLOAT_MEMBER(distanceThreshCorner);
    ADD_UINT32_MEMBER(smoothingTextureSize);
    ADD_FLOAT_MEMBER(smoothingGradientSize);
    ADD_MEMBER(osg::Vec4i, colorOff);
    ADD_MEMBER(osg::Vec4i, colorOn);
    ADD_MEMBER(osg::Vec4i, colorOutline);
    ADD_MEMBER(osg::Vec4i, colorShadow);
    ADD_MEMBER(ColorValues, colors);
    ADD_MEMBER(pc::util::SpatialFilterData, distanceFilter);
    ADD_BOOL_MEMBER(SplineTypeForEachSegment);
    ADD_FLOAT_MEMBER(smoothingFrontCornerSplineType);
    ADD_FLOAT_MEMBER(smoothingRearCornerSplineType);
  }

  float m_extractorOffset;
  float m_heightOverGround;
  osg::Vec2f m_projectionTransitionCamAngle;
  float m_heightMinThreshold;
  float m_contourAlpha;
  float m_contourHeight;
  float m_shadowRadius;

  float m_spline2DWidthInnerNear;
  float m_spline2DWidthInnerFar;
  float m_spline2DWidthOuter;

  float m_shield3DAlphaRatioTop;
  float m_shield3DAlphaRatioDivide;
  float m_shield3DAlphaRatioBottom;
  float m_shield3DSideAlphaRatioTop;
  float m_shield3DSideAlphaRatioDivide;
  float m_shield3DSideAlphaRatioBottom;
  float m_shield3DBottomCoverAlphaRatio;
  float m_shieldAlpha;
  float m_solidLine2DAlpha;
  float m_shadow2DAlphaInner;
  float m_shadow2DAlpha;
  float m_shieldHeight;
  float m_shieldThickness;
  float m_solidLineThicknessRatio;
  float m_shield3DThicknessRatio;
  float m_shieldOffset;
  float m_shieldTopOffset;
  float m_shieldRatioDivide;
  float m_shieldSideRatioDivide;
  float m_hairlineWidth;
  float m_hairlineAlpha;
  float m_hairlineBlooming;
  float m_hairlineAlphaRatioTop;
  float m_hairlineAlphaRatioDivide;
  float m_hairlineAlphaRatioBottom;
  float m_distanceDefault;

  float m_distanceThreshL1;
  float m_distanceThreshL2;
  float m_distanceThreshL3;
  float m_HysteresisDistanceThresh;
  float m_MinDistanceThresh;
  float m_MinDistanceThreshForPosDisp;
  float m_distanceThreshCorner;

  unsigned int m_smoothingTextureSize;
  float m_smoothingGradientSize;

  osg::Vec4i m_colorOff;
  osg::Vec4i m_colorOn;
  osg::Vec4i m_colorOutline;
  osg::Vec4i m_colorShadow;
  ColorValues m_colors;
  pc::util::SpatialFilterData m_distanceFilter;

  bool m_SplineTypeForEachSegment;
  float m_smoothingFrontCornerSplineType;
  float m_smoothingRearCornerSplineType;

};

extern pc::util::coding::Item<TileSettings> g_tileSettings;

} // namespace tileoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TILEOVERLAY_TILESETTINGS_H