//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS DFC
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CHEN Xiangqin (CC-DX/EPF2-CN)
//  Department: CC-DX/EPF2-CN
//=============================================================================
/// @swcomponent SVS Virtual Reality
/// @file  VirtualRealityUtil.cpp
/// @brief
//=============================================================================

#include "cc/assets/virtualreality/inc/VirtualRealityUtil.h"
#include "cc/assets/parkingspots/inc/ParkingSpotUtil.h"

#include <osg/BlendColor>
#include <osg/BlendFunc> // PRQA S 1060
#include <osg/Uniform>


namespace cc
{
namespace assets
{
namespace virtualreality
{

  void setColor(osg::Node* f_pNode,osg::Vec4 f_vecColor)
  {
    osg::StateSet* const l_stateSet = f_pNode->getOrCreateStateSet();
    const osg::ref_ptr<osg::BlendColor> l_blendColor =new osg::BlendColor();
    l_stateSet->setAttributeAndModes(l_blendColor.get());
    osg::Vec4 l_vecColorNew = osg::Vec4(1.0f, 1.0f, 1.0f, 1.0f) - f_vecColor;
    l_vecColorNew.a() = f_vecColor.a();
    l_blendColor->setConstantColor(l_vecColorNew);
    l_stateSet->getOrCreateUniform("diffusecolor", osg::Uniform::FLOAT_VEC4)->set(l_vecColorNew); // PRQA S 3803
  }

  vfc::uint8_t isPointOnLineWhere(osg::Vec2f& f_linePoint1, osg::Vec2f& f_linePoint2, osg::Vec2f& f_point)
  {
    /*
    This function is for judging a point is on a line left, right or on the line.
    return value will be 1u(left), 2u(right), 3u(on the line)
    */
    const vfc::float32_t l_tmpX = (f_linePoint1.x() - f_linePoint2.x())/(f_linePoint1.y() - f_linePoint2.y()) * (f_point.y() - f_linePoint2.y()) + f_linePoint2.x();
    if (l_tmpX > f_point.x())
    {
      return 1u; //Stands point to the left of the line
    }
    else if (l_tmpX < f_point.x())
    {
      return 2u; //Stands point to the right of the line
    }
    return 3u; //Stands point on the line
  }

  bool isPointInQuadrilateral(vfc::TFixedVector<osg::Vec2f, 4>& f_points2D, osg::Vec2f& f_pointClick) // PRQA S 4283
  {
    /*
    This function is for judging a point is in a quadrilateral or not.
    We will receive the 4 points of the quadrilateral and the click point position,
    and use The method of vector multiplication determines whether the point is inside the quadrilateral
    We have two steps:
      Step 1: we need to sort the four vertices clockwise
      Step 2: we need to use vector multiplication to determine whether a point is within a quadrilateral
    */

    //Step 1: we need to sort the four vertices clockwise
    vfc::TFixedVector<osg::Vec2f, 4> l_points2D;
    l_points2D.push_back(f_points2D[0]);
    vfc::int32_t l_leftMostPoint = 0;
    for (vfc::int32_t i = 1; i < 4; i++)
    {
      if (f_points2D[i].x() < l_points2D[0].x())
      {
        l_points2D[0] = f_points2D[i];
        l_leftMostPoint = i;
      }
    }

    vfc::TFixedVector<vfc::int32_t, 3> l_pointsNotLeftMost;
    for (vfc::int32_t i = 0; i < 4; i++)
    {
      if(i != l_leftMostPoint)
      {
        l_pointsNotLeftMost.push_back(i);
      }
    }

    osg::Vec2f l_linePoint = l_points2D[0];
    vfc::uint8_t l_isDiagonalPoint = 0;

    for (vfc::int32_t i = 0; i < 3; i++)
    {

      switch(i)
      {
          case 0:
            {l_isDiagonalPoint = isPointOnLineWhere(l_linePoint, f_points2D[l_pointsNotLeftMost[0]], f_points2D[l_pointsNotLeftMost[1]]) + isPointOnLineWhere(l_linePoint, f_points2D[l_pointsNotLeftMost[0]], f_points2D[l_pointsNotLeftMost[2]]);
            break;}
          case 1:
            {l_isDiagonalPoint = isPointOnLineWhere(l_linePoint, f_points2D[l_pointsNotLeftMost[1]], f_points2D[l_pointsNotLeftMost[0]]) + isPointOnLineWhere(l_linePoint, f_points2D[l_pointsNotLeftMost[1]], f_points2D[l_pointsNotLeftMost[2]]);
            break;}
          case 2: // PRQA S 4011
            {l_isDiagonalPoint = isPointOnLineWhere(l_linePoint, f_points2D[l_pointsNotLeftMost[2]], f_points2D[l_pointsNotLeftMost[0]]) + isPointOnLineWhere(l_linePoint, f_points2D[l_pointsNotLeftMost[2]], f_points2D[l_pointsNotLeftMost[1]]);}
          default:
            {break;}
      }

      if (3u == l_isDiagonalPoint)
      {
        vfc::TFixedVector<vfc::int32_t, 2> l_whichPointInLeft;
        for (vfc::int32_t j=0; j < 3; j++)
        {
          if (j != i)
          {
            l_whichPointInLeft.push_back(l_pointsNotLeftMost[j]);
          }
        }
        if (1u == isPointOnLineWhere(l_linePoint,f_points2D[l_pointsNotLeftMost[i]], f_points2D[l_whichPointInLeft[0]]))
        {
          l_points2D.push_back(f_points2D[l_whichPointInLeft[0]]);
          l_points2D.push_back(f_points2D[l_pointsNotLeftMost[i]]);
          l_points2D.push_back(f_points2D[l_whichPointInLeft[1]]);
        }
        else
        {
          l_points2D.push_back(f_points2D[l_whichPointInLeft[1]]);
          l_points2D.push_back(f_points2D[l_pointsNotLeftMost[i]]);
          l_points2D.push_back(f_points2D[l_whichPointInLeft[0]]);
        }
        break;
      }

    }

    //Step 2: we need to use vector multiplication to determine whether a point is within a quadrilateral
    for (vfc::int32_t i = 0; i < 4; i++)
    {
      osg::Vec2f l_vector1;
      if (i != 3)
      {
        l_vector1 = l_points2D[i+1] - l_points2D[i];
      }
      else
      {
        l_vector1 = l_points2D[0] - l_points2D[i];
      }

      osg::Vec2f l_vector2 = f_pointClick - l_points2D[i];

      if (1.0f * (l_vector1.x() * l_vector2.y() - l_vector2.x() * l_vector1.y()) <= 0.0f)
      {
        return false;
      }

    }
    return true;

  }

  vfc::TFixedVector<osg::Vec4f, 4> getParkingSpotFourCorners(const cc::target::common::ParkSlot_st& f_parkSlot)
  {
    /*
    This function is for getting four corners of spot by using the rear axel position (coordinate and angle), slotHalfWidth, slotRear2RearAxelDistance
    and slotFront2RearAxelDistance.
    */
    const vfc::float32_t l_slotHalfWidth = g_virtualRealityObjectSetting->m_slotHalfWidth;
    const vfc::float32_t l_slotRear2RearAxelDistance = g_virtualRealityObjectSetting->m_slotRear2RearAxelDistance;
    const vfc::float32_t l_slotFront2RearAxelDistance = g_virtualRealityObjectSetting->m_slotFront2RearAxelDistance;

    //!!! Might cause error due to the unit
    const vfc::float32_t  l_slotRearAxelCenter_x = static_cast<vfc::float32_t>(f_parkSlot.m_parkSlotPosition.m_slotRearAxelCenter.m_x) / 1024.0f;    //unit: 1 m
    const vfc::float32_t  l_slotRearAxelCenter_y = static_cast<vfc::float32_t>(f_parkSlot.m_parkSlotPosition.m_slotRearAxelCenter.m_y) / 1024.0f;    //unit: 1 m
    const vfc::float32_t  l_slotRearAxelCenter_phi = static_cast<vfc::float32_t>(f_parkSlot.m_parkSlotPosition.m_slotRearAxelCenter.m_phi) / 4096.0f;  //unit: 1 rad

    const vfc::float32_t l_cornerA_x = l_slotRearAxelCenter_x - l_slotFront2RearAxelDistance*std::sin(l_slotRearAxelCenter_phi);
    const vfc::float32_t l_cornerA_y = l_slotRearAxelCenter_y + l_slotFront2RearAxelDistance*std::cos(l_slotRearAxelCenter_phi);
    const vfc::float32_t l_cornerB_x = l_slotRearAxelCenter_x + l_slotRear2RearAxelDistance*std::sin(l_slotRearAxelCenter_phi);
    const vfc::float32_t l_cornerB_y = l_slotRearAxelCenter_y - l_slotRear2RearAxelDistance*std::cos(l_slotRearAxelCenter_phi);

    vfc::TFixedVector<osg::Vec4f, 4> l_points3D;
    l_points3D.push_back(osg::Vec4f(l_cornerA_x+std::cos(l_slotRearAxelCenter_phi)*l_slotHalfWidth, l_cornerA_y+std::sin(l_slotRearAxelCenter_phi)*l_slotHalfWidth, 0.0f, 1.0f));
    l_points3D.push_back(osg::Vec4f(l_cornerA_x-std::cos(l_slotRearAxelCenter_phi)*l_slotHalfWidth, l_cornerA_y-std::sin(l_slotRearAxelCenter_phi)*l_slotHalfWidth, 0.0f, 1.0f));
    l_points3D.push_back(osg::Vec4f(l_cornerB_x+std::cos(l_slotRearAxelCenter_phi)*l_slotHalfWidth, l_cornerB_y+std::sin(l_slotRearAxelCenter_phi)*l_slotHalfWidth, 0.0f, 1.0f));
    l_points3D.push_back(osg::Vec4f(l_cornerB_x-std::cos(l_slotRearAxelCenter_phi)*l_slotHalfWidth, l_cornerB_y-std::sin(l_slotRearAxelCenter_phi)*l_slotHalfWidth, 0.0f, 1.0f));

    return l_points3D;
  }

  bool parkingSpotIsSelected(const cc::target::common::ParkSlot_st& f_parkSlot, const osg::Matrixf& f_MVPmatrix, vfc::uint16_t f_click_x, vfc::uint16_t f_click_y)
  {
    //This part is calculating four spot corners
    vfc::TFixedVector<osg::Vec4f, 4> l_points3D = getParkingSpotFourCorners(f_parkSlot);

    //Turn 3D points into HU 2D points
    vfc::TFixedVector<osg::Vec2f, 4> l_points2D;
    for (vfc::int32_t i = 0; i < 4; i++)
    {
      osg::Vec4f l_point2D = cc::assets::parkingspots::adaptOsgCoordToViewCoord(l_points3D[i], f_MVPmatrix);
      l_points2D.push_back(osg::Vec2f(l_point2D.x(), l_point2D.y()));
    }
    osg::Vec2f l_pointClick = osg::Vec2f(f_click_x, f_click_y); // PRQA S 3011

    //Check the click point in spot area or not
    if (isPointInQuadrilateral(l_points2D, l_pointClick))
    {
      return true;
    }
    return false;
  }

bool isShow(const cc::core::CustomFramework* f_framework)
{
  bool l_ret = false;
  cc::target::common::EAPAPARKMODE                 l_curAPAPARKMODE          = cc::target::common::EAPAPARKMODE::APAPARKMODE_IDLE;
  cc::target::common::EPARKStatusR2L               l_curparkStatus           = cc::target::common::EPARKStatusR2L::PARK_Off;
  cc::target::common::EParkngTypeSeld              l_curParkngTypeSeld       = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  cc::target::common::EPARKDriverIndR2L            l_curparkDriverInd        = cc::target::common::EPARKDriverIndR2L::PARKDRV_NoRequest;
  cc::target::common::EPARKDriverIndSearchR2L      l_curAPADriverReq_Search  = cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_NoRequest;
  cc::target::common::EPARKRecoverIndR2L           l_curparkSuspend          = cc::target::common::EPARKRecoverIndR2L::PARKREC_NoPrompt;
  bool                         l_curFreeParkingActive    = false;

  if (f_framework->m_parkHmiParkAPAPARKMODEReceiver.hasData())
  {
    const cc::daddy::ParkAPAPARKMODE_t* const l_parkAPAPARKMODE = f_framework->m_parkHmiParkAPAPARKMODEReceiver.getData();
    l_curAPAPARKMODE = l_parkAPAPARKMODE->m_Data;
  }

  if(f_framework->m_parkHmiParkingStatusReceiver.hasData())
  {
    const cc::daddy::ParkStatusDaddy_t* const l_parkStatus = f_framework->m_parkHmiParkingStatusReceiver.getData();
    l_curparkStatus = l_parkStatus->m_Data;
  }

  if (f_framework->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = f_framework->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (f_framework->m_parkHmiParkDriverIndReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndDaddy_t* const l_parkDriverInd = f_framework->m_parkHmiParkDriverIndReceiver.getData();
    l_curparkDriverInd = l_parkDriverInd->m_Data;
  }

  if (f_framework->m_ParkDriverIndSearchReceiver.hasData())
  {
    const cc::daddy::ParkDriverIndSearchDaddy_t* const l_APADriverReq_Search = f_framework->m_ParkDriverIndSearchReceiver.getData();
    l_curAPADriverReq_Search = l_APADriverReq_Search->m_Data;
  }

  if (f_framework->m_parkHmiParkingRecoverIndReceiver.hasData())
  {
    const cc::daddy::ParkRecoverIndDaddy_t* const l_parkSuspend = f_framework->m_parkHmiParkingRecoverIndReceiver.getData();
    l_curparkSuspend = l_parkSuspend->m_Data;
  }

  if (f_framework->m_freeparkingActiveReceiver.hasData())
  {
    const cc::daddy::ParkFreeParkingActive_t* const l_pFreeparkingActiveButton = f_framework->m_freeparkingActiveReceiver.getData();
    l_curFreeParkingActive = l_pFreeparkingActiveButton->m_Data;
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curAPAPARKMODE && cc::target::common::EPARKStatusR2L::PARK_Searching == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SearchingProcess:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_PSSelection:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Stop:
      {if (false == l_curFreeParkingActive)
        {
          l_ret = true;
        }
        break;}
      default:
        {break;}
    }
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curAPAPARKMODE && cc::target::common::EPARKStatusR2L::PARK_AssistStandby == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    switch (l_curparkDriverInd)
    {
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseHood:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseTrunk:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_ExpandedMirror:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SeatBelt:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_CloseDoor:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_SmallParkSlot:
      case cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM:
        {l_ret = true;
        break;}
      default:
        {break;}
    }
    if (cc::target::common::EPARKDriverIndSearchR2L::PARKDRVSEARCH_WaitForDriverOperateGear == l_curAPADriverReq_Search)
    {
        l_ret = true;
    }
  }

  if (cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curAPAPARKMODE && cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)
  {
    l_ret = true;
  }

  if ( cc::target::common::EAPAPARKMODE::APAPARKMODE_APA == l_curAPAPARKMODE && cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_curparkStatus && cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld
    && cc::target::common::EPARKDriverIndR2L::PARKDRV_Confirm_Press_DM == l_curparkDriverInd && cc::target::common::EPARKRecoverIndR2L::PARKREC_PauseCommand == l_curparkSuspend)
  {
    l_ret = true;
  }

  return l_ret;
}

} // namespace virtualreality
} // namespace assets
} // namespace cc
