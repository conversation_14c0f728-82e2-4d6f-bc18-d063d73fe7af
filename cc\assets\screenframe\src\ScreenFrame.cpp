//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ScreenFrame.cpp
/// @brief 
//=============================================================================

#include "cc/assets/screenframe/inc/ScreenFrame.h"
#include <osg/Texture2D>
#include <osg/Geometry>
#include <osgDB/ReadFile>


static const char * const infoVertShader = {
    "varying vec2 v_texCoord;\n"
    "attribute vec2 osg_MultiTexCoord0;\n"
    "void main(void)\n"
    "{\n"
    "    v_texCoord     = osg_MultiTexCoord0;\n"
    "    gl_Position    = gl_ModelViewProjectionMatrix * gl_Vertex;\n"
    "}\n"
};


static const char * const infoFragShader = {
    "precision mediump float;\n"
    "varying vec2 v_texCoord;\n"
    "uniform sampler2D osg_Tex0;\n"
    "void main(void)\n"
    "{\n"
    "    vec4 color     = texture2D(osg_Tex0, v_texCoord);\n"
    "    gl_FragColor   = vec4(0.3, 0.3, 0.3, color.a);\n"
    "}\n"
};


namespace cc
{
namespace assets
{
namespace screenframe
{


ScreenFrame::ScreenFrame(
  const std::string& f_name, const pc::core::Viewport& f_viewport, const std::string& f_bitmap)
  : pc::core::View(f_name, f_viewport)
{
  //! Idle Icon as HUD Camera
  setRenderOrder(osg::Camera::POST_RENDER, 10);
  setProjectionMatrixAsOrtho2D(0.0f, 1.0f, 0.0f, 1.0f);

  //! Add group as child to the HUD camera
  addChild(createScreenFrameGroup( f_bitmap ));
}


ScreenFrame::~ScreenFrame()
{
}


osg::Geode* ScreenFrame::createScreenFrameGroup(const std::string &f_bitmap)
{
  //! Load icon
  osg::ref_ptr<osg::Image> testImage = osgDB::readImageFile( f_bitmap );

  //! Set up 2D Texture
  osg::ref_ptr<osg::Texture2D> l_pIconTexture = new osg::Texture2D;
  l_pIconTexture->setName("texture for ScreenFrame");
  l_pIconTexture->setResizeNonPowerOfTwoHint(false);

  l_pIconTexture->setImage(testImage);
  l_pIconTexture->setWrap(osg::Texture::WRAP_R, osg::Texture::CLAMP_TO_EDGE);
  l_pIconTexture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_pIconTexture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);
  l_pIconTexture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR);
  l_pIconTexture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);

  //! Quad geometry
  osg::ref_ptr<osg::Geometry> l_quad = osg::createTexturedQuadGeometry(osg::Vec3f(0.0f,0.0f,0.0f),
                                                                              osg::Vec3f(1.0f,0.0f,0.0f),
                                                                              osg::Vec3f(0.0f,1.0f,0.0f) );
//   l_quad->setUseDisplayList(false);
//   l_quad->setUseVertexBufferObjects(true);
  l_quad->getOrCreateStateSet()->setTextureAttribute(0, l_pIconTexture.get());
  l_quad->getOrCreateStateSet()->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF);



  //! Create geode and set StateSets
  osg::Geode* l_pScreenFrameGeode = new osg::Geode;
  l_pScreenFrameGeode->addDrawable(l_quad);
  osg::StateSet* l_pScreenFrameStateSet = l_pScreenFrameGeode->getOrCreateStateSet();
  // pc::factory::ShaderStateSet::setBasicTexShader(l_pScreenFrameStateSet);

  osg::Program* l_pProgram = new osg::Program();
  osg::Shader* l_pVertexShader = new osg::Shader(osg::Shader::VERTEX,infoVertShader);
  osg::Shader* l_pFragmentShader = new osg::Shader(osg::Shader::FRAGMENT, infoFragShader);

  l_pProgram->addShader(l_pVertexShader);
  l_pProgram->addShader(l_pFragmentShader);

  l_pScreenFrameStateSet->setAttribute(l_pProgram);
  l_pScreenFrameStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);

  return l_pScreenFrameGeode;
}


} // namespace SidePanel
} // namespace assets
} // namespace cc