//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by Robert <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================

#include "vis-dongfeng/assets/freeparkingoverlay/freeparking_rotate_button.hpp"
#include "vis-dongfeng/assets/freeparkingoverlay/freeparking_overlay.hpp"
#include "vis-dongfeng/core/custom_framework.hpp"
#include "vis-dongfeng/core/custom_scene.hpp"
#include "vis-dongfeng/daddy/custom_daddy_ports.hpp"
#include "vis-dongfeng/views/imguiview/imgui_manager.hpp"

#include "generic/coding/coding_manager.hpp"
#include "generic/logging/logging.hpp"
#include "generic/logging/logging_contexts.hpp"

#include "vis/core/shader_manager.hpp"

#include "osgDB/ReadFile"
#include "osg/Depth"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osg/Vec2ui"

using pc::util::logging::g_AppContext;

#define PARALLEL_SLOT_ON_TOP 0

namespace cc
{
namespace assets
{
namespace freeparkingoverlay
{

osg::ref_ptr<osg::Texture2D> RotateButton::sm_parkableTexture          = nullptr;
osg::ref_ptr<osg::Texture2D> RotateButton::sm_unparkableTexture        = nullptr;
osg::ref_ptr<osg::Texture2D> RotateButton::sm_parkablePressedTexture   = nullptr;
osg::ref_ptr<osg::Texture2D> RotateButton::sm_unparkablePressedTexture = nullptr;
osg::ref_ptr<osg::Texture2D> RotateButton::sm_arrowTexture             = nullptr;

pc::util::coding::Item<FreeparkingRotateButtonSettings> g_freeparkingRotateButtonSettings("FreeparkingRotateButton");

//!
//! RotateButton
//!
RotateButton::RotateButton()
    : osg::MatrixTransform()
    , FreeparkingNode()
    , m_geode(nullptr)
    , m_visible(false)
    , m_size(g_freeparkingRotateButtonSettings->m_size)
    , m_spotType(0)
    , m_state(ButtonState::SELECTABLE)
    , m_transparency(0.0f)
    , m_step(g_freeparkingRotateButtonSettings->m_step) // frame
    , m_animationType(FADE_IN)
    , m_animationState(OPAQUE)
{
    // build the plane for the parking slot itself
    osg::Geometry* const l_geometry = new osg::Geometry;
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);

    osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
    (*l_vertices)[0u]          = osg::Vec3f(
        0.0f,
        -0.5f,
        g_freeparkingSettings->m_groundLevel); // create a high planar to make it placed upper than vehicle model
    (*l_vertices)[1u] =
        osg::Vec3f(1.0f, -0.5f, g_freeparkingSettings->m_groundLevel); // just set the height as 0.1 also be fine
    (*l_vertices)[2u] =
        osg::Vec3f(1.0f, 0.5f, g_freeparkingSettings->m_groundLevel); // 14.5 ~ maximum value of height that we can we
                                                                      // see thought planview camera
    (*l_vertices)[3u] = osg::Vec3f(0.0f, 0.5f, g_freeparkingSettings->m_groundLevel);
    l_geometry->setVertexArray(l_vertices);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
    (*l_texCoords)[0u]          = osg::Vec2f(1.0f, 0.0f);
    (*l_texCoords)[1u]          = osg::Vec2f(1.0f, 1.0f);
    (*l_texCoords)[2u]          = osg::Vec2f(0.0f, 1.0f);
    (*l_texCoords)[3u]          = osg::Vec2f(0.0f, 0.0f);
    l_geometry->setTexCoordArray(0u, l_texCoords);

    osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
    (*l_colours)[0u]          = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

    osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
    (*l_normals)[0u]          = osg::Z_AXIS;
    l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices =
        new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
    (*l_indices)[0u] = 1u;
    (*l_indices)[1u] = 0u;
    (*l_indices)[2u] = 2u;
    (*l_indices)[3u] = 2u;
    (*l_indices)[4u] = 0u;
    (*l_indices)[5u] = 3u;
    l_geometry->addPrimitiveSet(l_indices); // PRQA S 3803

    const osg::ref_ptr<osg::StateSet> stateSet = new osg::StateSet;

    // set texture options
    if (sm_parkableTexture == nullptr)
    {
        sm_parkableTexture = createTexture(g_freeparkingRotateButtonSettings->m_parkablePressableTexture);
    }
    if (sm_unparkableTexture == nullptr)
    {
        sm_unparkableTexture = createTexture(g_freeparkingRotateButtonSettings->m_unparkablePressableTexture);
    }

    if (sm_parkableTexture == nullptr)
    {
        sm_parkablePressedTexture = createTexture(g_freeparkingRotateButtonSettings->m_parkablePressedTexture);
    }
    if (sm_unparkableTexture == nullptr)
    {
        sm_unparkablePressedTexture = createTexture(g_freeparkingRotateButtonSettings->m_unparkablePressedTexture);
    }

    if (sm_arrowTexture == nullptr)
    {
        sm_arrowTexture = createTexture(g_freeparkingRotateButtonSettings->m_arrowTexture);
    }
    stateSet->setTextureAttribute(0u, sm_parkableTexture);
    stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
    stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);       // PRQA S 3143
    stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    // stateSet->setRenderBinDetails(core::RENDERBIN_ORDER_FREEPARKING_OVERLAY, "RenderBin");

    pc::core::TextureShaderProgramDescriptor l_parkingSpotShader("advancedTex");
    stateSet->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)
        ->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));                           // PRQA S 3803
    stateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f); // PRQA S 3803
    stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);  // PRQA S 3803
    l_parkingSpotShader.apply(stateSet.get());                               // PRQA S 3803
    setStateSet(stateSet);
    m_geode = new osg::Geode;
    m_geode->addDrawable(l_geometry);
    osg::MatrixTransform::addChild(m_geode);
}

osg::Texture2D* RotateButton::createTexture(const std::string& f_filename)
{
    osg::Image* const l_image = osgDB::readImageFile(f_filename);
    // assert(l_image);
    osg::Texture2D* const l_texture = new osg::Texture2D(l_image);
    // set texture options
    l_texture->setDataVariance(osg::Object::STATIC);
    l_texture->setUnRefImageDataAfterApply(true);
    // l_texture->setUseHardwareMipMapGeneration(true);
    l_texture->setResizeNonPowerOfTwoHint(false);
    l_texture->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_texture->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_texture->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP);
    l_texture->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP);
    return l_texture;
}

void RotateButton::updateAnimation()
{
    if (m_animationState == TRANSITION)
    {
        if (m_animationType == FADE_IN)
        {
            fadeIn();
        }
        if (m_animationType == FADE_OUT)
        {
            fadeOut();
        }
        getStateSet()->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(m_transparency);
    }
}

void RotateButton::updateTexture(ParkingPlaneState f_parkingPlaneState)
{
    if (m_state == ButtonState::SELECTABLE && m_animationState == OPAQUE) // showing rotate button
    {
        // show rotate button
        m_size = g_freeparkingRotateButtonSettings->m_size;
        getOrCreateStateSet()->setTextureAttribute(0u, f_parkingPlaneState ==  ParkingPlaneState::AVAILABLE? sm_parkableTexture : sm_unparkableTexture);
    }
    else if (m_state == ButtonState::SELECTED && m_animationState == TRANSITION && m_animationType == FADE_IN)
    {
        m_size = g_freeparkingRotateButtonSettings->m_size;
        getOrCreateStateSet()->setTextureAttribute(0u, f_parkingPlaneState ==  ParkingPlaneState::AVAILABLE ? sm_parkablePressedTexture : sm_unparkablePressedTexture);
    }
    else
    {
        // show arrow
        m_size = g_freeparkingRotateButtonSettings->m_arrowSize;
        getOrCreateStateSet()->setTextureAttribute(0u, sm_arrowTexture);
    }
}

void RotateButton::updateState(FreeparkingOverlay* f_freeparkingOverlay)
{
    if (f_freeparkingOverlay->getMousePressed() && (f_freeparkingOverlay->getSlitherActionType() == ROTATION))
    {
        setState(ButtonState::SELECTED);
    }
    else
    {
        setState(ButtonState::SELECTABLE);
    }
    osg::StateSet* const l_stateSet          = getOrCreateStateSet();
    osg::Uniform*  const l_mipmapBiasUniform = l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT);
    l_mipmapBiasUniform->set(g_freeparkingRotateButtonSettings->m_mipmapBias); // PRQA S 3803
}

void RotateButton::update(FreeparkingOverlay* f_freeparkingOverlay, const bool f_isLeft)
{
    m_visible =
        f_freeparkingOverlay->getRotateButtonVisibility(); // TODO: do we show rotate button during assist standby?
    if (!m_visible)
    {
        return;
    }
    IMGUI_LOG("RotateButton", "m_state", m_state == ButtonState::SELECTABLE ? "SELECTABLE" : "SELECTED");
    IMGUI_LOG("RotateButton", "m_visible", m_visible ? "Visible" : "Invisible");
    IMGUI_LOG("RotateButton", "m_transparency", m_transparency);
    IMGUI_LOG("RotateButton", "m_animationType", m_animationType == FADE_IN ? "FADE_IN" : "FADE_OUT");
    IMGUI_LOG("RotateButton", "m_animationState",
        m_animationState == TRANSITION ? "TRANSITION" :
        m_animationState == OPAQUE ? "OPAQUE" : "TRANSPARENT"
    );

    updateState(f_freeparkingOverlay);
    updateAnimation();
    updateTexture(f_freeparkingOverlay->getParkingPlanState());

    vfc::float32_t centerOriginX     = 0.0f;
    constexpr vfc::float32_t centerOriginY     = 0.0f;
    osg::Vec2f     slotSize          = f_freeparkingOverlay->getSize();
    const osg::Vec2f     l_size            = m_size;
    constexpr vfc::float32_t l_typeAngleOffset = 0.0f;
    vfc::float32_t translateX        = 0.0f;
    constexpr vfc::float32_t translateY        = 0.0f;
    const vfc::uint32_t  l_girdSizeIdx     = f_freeparkingOverlay->getType(); // PRQA S 3803
    const vfc::float32_t percentageOffset  = g_freeparkingRotateButtonSettings->m_offsetPercentage;
    m_step                           = g_freeparkingRotateButtonSettings->m_step;
    m_offsetToResponseCenter = (m_size.x() > m_size.y()) ? m_size.x() * percentageOffset : m_size.y() * percentageOffset;
    centerOriginX                    = m_offsetToResponseCenter;
    // m_direction = IMGUI_GET_CHECKBOX_BOOL("Settings", "IsBottomDirection") ? ButtonDirection::BOTTOM : ButtonDirection::TOP;
    if (m_direction == ButtonDirection::TOP)
    {
        translateX = slotSize.x() * 0.5f;
    }
    else
    {
        translateX = -slotSize.x() * 0.5f;
    }

    const osg::Vec3f l_scale(l_size, 1.0f);
    setMatrix(
        osg::Matrix::rotate(
            osg::DegreesToRadians(
                (m_direction == ButtonDirection::TOP) ? l_typeAngleOffset : 180.0f + l_typeAngleOffset),
            osg::Z_AXIS) *
        osg::Matrix::scale(l_scale) * osg::Matrix::translate(translateX, translateY, 0.0f));

    m_rotateCenter = (osg::Matrix::translate(centerOriginX, centerOriginY, 0.0f) * this->getMatrix() *
                      f_freeparkingOverlay->getMatrix())
                         .getTrans();
}

void RotateButton::onStateChanged(ButtonState f_newState)
{
    if (f_newState == m_state)
    {
        return;
    }

    if (m_state == ButtonState::SELECTABLE && f_newState == ButtonState::SELECTED)
    {
        if (m_animationState == OPAQUE)
        {
            m_transparency = 1.0f;
        }
        m_animationState = TRANSITION;
        m_animationType  = FADE_IN;
    }

    if (m_state == ButtonState::SELECTED && f_newState == ButtonState::SELECTABLE)
    {
        m_animationState = TRANSITION;
        m_animationType  = FADE_OUT;
    }
}

void RotateButton::fadeIn()
{
    m_transparency -= 1.0f / (static_cast<vfc::float32_t>(m_step));
    if (m_transparency <= 0.0f)
    {
        m_transparency   = 1.0f;
        m_animationState = OPAQUE;
    }
}

void RotateButton::fadeOut()
{
    m_transparency -= 1.0f / (static_cast<vfc::float32_t>(m_step));
    if (m_transparency <= 0.0f)
    {
        m_animationState = (m_state == ButtonState::SELECTED) ? TRANSPARENT : OPAQUE;
        m_transparency   = (m_state == ButtonState::SELECTED) ? 0.0 : 1.0f;
    }
}

void RotateButton::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
    {
        if (m_visible)
        {
            osg::MatrixTransform::traverse(f_nv);
        }
    }
    else
    {
        osg::MatrixTransform::traverse(f_nv);
    }
}

} // namespace freeparkingoverlay
} // namespace assets
} // namespace cc
