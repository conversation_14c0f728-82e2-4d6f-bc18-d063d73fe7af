//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------
#ifndef ViewModeButtonBAR_H
#define ViewModeButtonBAR_H

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"

namespace cc
{
namespace assets
{
namespace button
{
namespace viewmodebutton
{

class ViewModeButtonSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(ViewModeButtonSettings) // PRQA S 2428
    {
        ADD_MEMBER(ButtonTexturePath, buttonTexture);
        ADD_MEMBER(osg::Vec2f, horiPos);
    }

    ButtonTexturePath m_buttonTexture;
    osg::Vec2f        m_horiPos = osg::Vec2f(0.0f, 100.0f);
};

class ViewModeButton : public Button
{
public:
    ViewModeButton(
        cc::core::AssetId       f_assetId,
        pc::core::Framework*    f_framework,
        ViewModeButtonSettings* f_settings,
        ESVSViewMode            f_viewMode,
        osg::Camera*            f_referenceView = nullptr);

protected:
    void update() override;

private:
    void onInvalid() override;
    void onUnavailable() override;
    void onAvailable() override;
    void onPressed() override;
    void onReleased() override;
    void onSelected() override;

    ESVSViewMode getViewMode() const
    {
        return m_viewMode;
    }

    std::string getViewModeButtonName() const
    {
        switch (m_viewMode)
        {
        case ESVSViewMode::ESVSViewMode_VM_Standard:
            return "Single View Mode Button";
            break;
        case ESVSViewMode::ESVSViewMode_VM_Perspective:
            return "Perspective View Mode Button";
            break;
        case ESVSViewMode::ESVSViewMode_VM_Wheel:
            return "Wheel View Mode Button";
            break;
        case ESVSViewMode::ESVSViewMode_VM_Wide:
            return "Wide View Mode Button";
            break;
        case ESVSViewMode::ESVSViewMode_VM_STB:
            return "STB View Mode Button";
            break;
        default:
            return "Invalid View Mode";
            break;
        }
    }

private:
    pc::core::Framework*    m_framework;
    ESVSViewMode            m_viewMode;
    EScreenID               m_curScreen;
    ViewModeButtonSettings* m_settings;
    EGear                   m_preGear;
    bool                    m_hasSentViewMode;
};

class ViewModelButtonBackground : public cc::assets::button::Button // use Button for background temperly
{
public:
    ViewModelButtonBackground(
        cc::core::AssetId       f_assetId,
        pc::core::Framework*    f_framework,
        ViewModeButtonSettings* f_settings,
        osg::Camera*            f_referenceView = nullptr);
    virtual ~ViewModelButtonBackground(){}

protected:
    void update() override
    {
        setState(AVAILABLE);
    }

private:
    void onInvalid() override
    {
        setIconEnable(false);
    }
    void onUnavailable() override
    {
        setIconEnable(true);
    }
    void onAvailable() override
    {
        setIconEnable(true);
    }
    void onPressed() override
    {
        setIconEnable(true);

    }
    void onReleased() override
    {
        setIconEnable(true);
    }

private:
    //! Copy constructor is not permitted.
    ViewModelButtonBackground(const ViewModelButtonBackground& other); // = delete
    //! Copy assignment operator is not permitted.
    ViewModelButtonBackground& operator=(const ViewModelButtonBackground& other); // = delete
    pc::core::Framework*       m_framework;
    ViewModeButtonSettings*     m_settings;
};

class ViewModeButtonGroup : public ButtonGroup
{
public:
    ViewModeButtonGroup(cc::core::AssetId f_assetId, pc::core::Framework* f_framework, osg::Camera* f_referenceView);

protected:
    void update() override;
};

} // namespace viewmodebutton
} // namespace button
} // namespace assets
} // namespace cc

#endif // ViewModeButtonBAR_H