#ifndef CC_ASSETS_BRIGHTNESSSLIDER_OVERLAY_H
#define CC_ASSETS_BRIGHTNESSSLIDER_OVERLAY_H
#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"
#include "cc/assets/button/inc/Slider.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"

namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

class BrightnessSliderBackgroundSettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(BrightnessSliderBackgroundSettings)
    {
        ADD_STRING_MEMBER(brightnessSliderNightBackgroundTexturePath);
        ADD_STRING_MEMBER(brightnessSliderDayBackgroundTexturePath);
        ADD_MEMBER(osg::Vec2f, brightnessSliderBackgroundPos);
    }

    std::string                        m_brightnessSliderNightBackgroundTexturePath;
    std::string                        m_brightnessSliderDayBackgroundTexturePath;
    osg::Vec2f                         m_brightnessSliderBackgroundPos;
};

class BrightnessSliderOverlaySettings : public pc::util::coding::ISerializable
{
public:
    SERIALIZABLE(BrightnessSliderOverlaySettings)
    {
        ADD_MEMBER(BrightnessSliderBackgroundSettings, brightnessSun);
        ADD_MEMBER(BrightnessSliderBackgroundSettings, brightnessSliderBackground);
        ADD_MEMBER(BrightnessSliderBackgroundSettings, brightnessSliderLeftTopCorner);
        ADD_MEMBER(BrightnessSliderBackgroundSettings, brightnessSliderLeftBottomCorner);
        ADD_MEMBER(BrightnessSliderBackgroundSettings, brightnessSliderRightTopCorner);
        ADD_MEMBER(BrightnessSliderBackgroundSettings, brightnessSliderRightBottomCorner);
        ADD_MEMBER(osg::Vec2f, brightnessSliderBackgroundSize);
        ADD_MEMBER(cc::assets::button::SliderSettings, brightnessSliderSetting);
    }

    BrightnessSliderBackgroundSettings m_brightnessSun;
    BrightnessSliderBackgroundSettings m_brightnessSliderBackground;
    BrightnessSliderBackgroundSettings m_brightnessSliderLeftTopCorner;
    BrightnessSliderBackgroundSettings m_brightnessSliderLeftBottomCorner;
    BrightnessSliderBackgroundSettings m_brightnessSliderRightTopCorner;
    BrightnessSliderBackgroundSettings m_brightnessSliderRightBottomCorner;
    osg::Vec2f                         m_brightnessSliderBackgroundSize;
    cc::assets::button::SliderSettings m_brightnessSliderSetting;
};

extern pc::util::coding::Item<BrightnessSliderOverlaySettings> g_brightnessSliderOverlaySettings;

class BrightnessSliderSwitch
{
public:
    virtual void openBrightnessSlider()      = 0;
    virtual void closeBrightnessSlider()     = 0;
    virtual bool getBrightnessSliderStatus() = 0;
};

class BrigtnessSliderBackground : public cc::assets::button::Button
{
public:
    BrigtnessSliderBackground(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        const BrightnessSliderBackgroundSettings* f_setting,
        osg::Camera*         f_referenceView = nullptr);
    virtual ~BrigtnessSliderBackground();

protected:
    void update() override;

private:
    void onInvalid() override
    {
    }
    void onUnavailable() override
    {
    }
    void onAvailable() override;
    void onPressed() override
    {
    }
    void onReleased() override
    {
    }

private:
    //! Copy constructor is not permitted.
    BrigtnessSliderBackground(const BrigtnessSliderBackground& other); // = delete
    //! Copy assignment operator is not permitted.
    BrigtnessSliderBackground& operator=(const BrigtnessSliderBackground& other); // = delete
    pc::core::Framework*       m_framework;
    const BrightnessSliderBackgroundSettings*      m_setting;
};

class BrightnessSliderOverlay : public cc::assets::button::ButtonGroup, public BrightnessSliderSwitch
{
public:
    BrightnessSliderOverlay(
        cc::core::AssetId    f_assetId,
        pc::core::Framework* f_framework,
        osg::Camera*         f_referenceView);
    virtual ~BrightnessSliderOverlay();

    void openBrightnessSlider() override
    {
        m_isBrightnessSliderEnabled = true;
    }

    void closeBrightnessSlider() override
    {
        m_isBrightnessSliderEnabled = false;
    }

    bool getBrightnessSliderStatus() override
    {
        return m_isBrightnessSliderEnabled;
    }

private:
    //! Copy constructor is not permitted.
    BrightnessSliderOverlay(const BrightnessSliderOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    BrightnessSliderOverlay& operator=(const BrightnessSliderOverlay& other); // = delete
    void                     update() override;
    pc::core::Framework*     m_framework;
    bool                     m_isBrightnessSliderEnabled;

};

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_BRIGHTNESSSLIDER_OVERLAY_H