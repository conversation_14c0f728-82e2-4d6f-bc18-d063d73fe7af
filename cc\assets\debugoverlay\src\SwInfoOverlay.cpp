//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  SwInfoOverlay.cpp
/// @brief
//=============================================================================

#include "cc/assets/debugoverlay/inc/SwInfoOverlay.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h" // PRQA S 1060
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h" // PRQA S 1060
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"

#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

#include <string>

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace swinfooverlay
{

pc::util::coding::Item<SWInfoOverlaySettings> g_displaySettings("SWInfoOverlay");

//!
//! RctaOverlay
//!
SwInfoOverlay::SwInfoOverlay(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_SwVersionDisGeode{}
  , m_HwVersionDisGeode{}
{
  setName("SwInfoOverlay");
  setNumChildrenRequiringUpdateTraversal(1u);
}


SwInfoOverlay::~SwInfoOverlay() = default;


void SwInfoOverlay::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (g_displaySettings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      addUpdateCallback(new SwInfoOverlayUpdateCallback(m_SwVersionDisGeode, m_HwVersionDisGeode, m_framework));
      m_settingsModifiedCount = g_displaySettings->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}


void SwInfoOverlay::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803

  m_SwVersionDisGeode = new osg::Geode;
  addChild(m_SwVersionDisGeode);    // PRQA S 3803
  {
  }

  m_HwVersionDisGeode = new osg::Geode;
  addChild(m_HwVersionDisGeode);    // PRQA S 3803
  {
  }

  const osg::ref_ptr<osgText::Text> l_swVersion = new osgText::Text;
  l_swVersion->setPosition(-g_displaySettings->m_offset_SwVersion);
  l_swVersion->setFont(g_displaySettings->m_fontType);
  l_swVersion->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
  l_swVersion->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
  l_swVersion->setCharacterSize(30.0f);
  l_swVersion->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
  l_swVersion->setAxisAlignment(osgText::Text::XY_PLANE);
  l_swVersion->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
  l_swVersion->setAlignment(osgText::Text::CENTER_TOP);
  m_SwVersionDisGeode->addDrawable(l_swVersion); // PRQA S 3803

  const osg::ref_ptr<osgText::Text> l_hwVersion = new osgText::Text;
  l_hwVersion->setFont(g_displaySettings->m_fontType);
  l_hwVersion->setPosition(-g_displaySettings->m_offset_HwVersion);
  l_hwVersion->setColor(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f));
  l_hwVersion->setDrawMode(osgText::TextBase::TEXT ); // PRQA S 3143
  l_hwVersion->setCharacterSize(30.0f);
  l_hwVersion->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
  l_hwVersion->setAxisAlignment(osgText::Text::XY_PLANE);
  l_hwVersion->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
  l_hwVersion->setAlignment(osgText::Text::CENTER_TOP);
  m_HwVersionDisGeode->addDrawable(l_hwVersion); // PRQA S 3803

  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
}

SwInfoOverlayUpdateCallback::SwInfoOverlayUpdateCallback(
                                                          osg::ref_ptr<osg::Geode> f_SwVersionDisGeode,
                                                          osg::ref_ptr<osg::Geode> f_HwVersionDisGeode,
                                                          pc::core::Framework* f_pFramework
                                                          )
  : m_SwVersionDisGeode{f_SwVersionDisGeode}
  , m_HwVersionDisGeode{f_HwVersionDisGeode}
  , m_pFramework{f_pFramework}
{
}

SwInfoOverlayUpdateCallback::~SwInfoOverlayUpdateCallback() = default;

void SwInfoOverlayUpdateCallback::updateSwVersion(osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{
  bool l_showSwitch = false;
  if (m_pFramework->asCustomFramework()->m_swVersionShowSwitch_ReceiverPort.hasData())
  {
    const cc::daddy::SwVersionShowSwitchDaddy_t* const l_pShowSwitch = m_pFramework->asCustomFramework()->m_swVersionShowSwitch_ReceiverPort.getData();
    l_showSwitch = l_pShowSwitch->m_Data;
  }

  if (l_showSwitch)
  {
    // "SW Ver: " 7u
    std::string l_swVersionString = "SW Ver: ";

    if (true == m_pFramework->asCustomFramework()->m_swInfo_ReceiverPort.isConnected())
    {
      const cc::daddy::SWInfoDaddy_t* const l_pDataDaddy = m_pFramework->asCustomFramework()->m_swInfo_ReceiverPort.getData();
      if (nullptr != l_pDataDaddy)
      {
        std::string l_internalSwId(&l_pDataDaddy->m_Data.m_InternalSoftwareID[0], &l_pDataDaddy->m_Data.m_InternalSoftwareID[0] + cc::target::common::F192_Data_Length); // PRQA S 3705
        if (0x52u == l_pDataDaddy->m_Data.m_InternalSoftwareID[0])  // if the first is "R", then it is release version, otherwise it is daily build
        {
          l_swVersionString += l_internalSwId;
        }
        else  // it is daily build
        {
          vfc::uint32_t l_dailybuild = 0u;
          l_dailybuild = (static_cast<vfc::uint32_t>(l_internalSwId[0]) * 256u + static_cast<vfc::uint32_t>(l_internalSwId[1])); // PRQA S 3165
          l_swVersionString += std::to_string(l_dailybuild);    // PRQA S 3803
          const std::string l_dailyBuildString(&l_pDataDaddy->m_Data.m_InternalSoftwareID[2], &l_pDataDaddy->m_Data.m_InternalSoftwareID[2] - 2u + cc::target::common::F192_Data_Length); // PRQA S 3705
          l_swVersionString += l_dailyBuildString;
        }
      }
    }

    // l_swVersionString << "SW Ver: " << "R8.0RC04.3" << std::endl;  // PRQA S 3803
    // l_swVersionString << "SW Ver: " << static_cast<char>(0x5f) << std::endl;  // PRQA S 3803

    f_Geode->setNodeMask(~0u);
    osgText::Text* const l_swVersion = static_cast<osgText::Text*>(f_Geode->getDrawable(0u)); // PRQA S 3076
    l_swVersion->setText(l_swVersionString.c_str());
  }
  else
  {
    f_Geode->setNodeMask(0u);
  }

}

void SwInfoOverlayUpdateCallback::updateHwVersion(osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{
  bool l_showSwitch = false;
  if (m_pFramework->asCustomFramework()->m_swVersionShowSwitch_ReceiverPort.hasData())
  {
    const cc::daddy::SwVersionShowSwitchDaddy_t* const l_pShowSwitch = m_pFramework->asCustomFramework()->m_swVersionShowSwitch_ReceiverPort.getData();
    l_showSwitch = l_pShowSwitch->m_Data;
  }

  if (l_showSwitch)
  {
    std::string l_hwVersionString = "HW Ver: ";
    if (true == m_pFramework->asCustomFramework()->m_swInfo_ReceiverPort.isConnected())
    {
      const cc::daddy::SWInfoDaddy_t* const l_pDataDaddy = m_pFramework->asCustomFramework()->m_swInfo_ReceiverPort.getData();
      if (nullptr != l_pDataDaddy)
      {
        std::array<vfc::uint8_t, cc::target::common::F193_Data_Length> l_HardwareVersion; // PRQA S 4102
        std::memcpy(static_cast<void*>(&l_HardwareVersion[0]), static_cast<const void*>(&(l_pDataDaddy->m_Data.m_HardwareVersion[0])), cc::target::common::F193_Data_Length); // PRQA S 5017 // PRQA S 4741 // PRQA S 3803
        l_hwVersionString += std::to_string(l_HardwareVersion[0u] / 100u % 10u) + "." // PRQA S 3803
          + std::to_string(l_HardwareVersion[0u] / 10u % 10u) + "."
          + std::to_string(l_HardwareVersion[0u] / 1u % 10u) + ",";
        l_hwVersionString += std::to_string(l_HardwareVersion[1u] + 2000u) + "." // PRQA S 3803
          + std::to_string(l_HardwareVersion[2u]+0u) + "."
          + std::to_string(l_HardwareVersion[3u]+0u);
      }
    }

    f_Geode->setNodeMask(~0u);
    osgText::Text* const l_hwVersion = static_cast<osgText::Text*>(f_Geode->getDrawable(0u)); // PRQA S 3076
    l_hwVersion->setText(l_hwVersionString.c_str());
  }
  else
  {
    f_Geode->setNodeMask(0u);
  }

}


void SwInfoOverlayUpdateCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if ((f_node == nullptr) || (f_nv == nullptr))
  {
      return;
  }
  updateSwVersion(*f_nv, m_SwVersionDisGeode);
  updateHwVersion(*f_nv, m_HwVersionDisGeode);
  traverse(f_node, f_nv);
}

} // namespace rctaoverlay
} // namespace assets
} // namespace cc
