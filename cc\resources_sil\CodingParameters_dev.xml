<?xml version="1.0" encoding="UTF-8"?>
<CodingParameters>
  <System>
    <mainViewport>
      <origin>0 0</origin>
      <size>2560 1320</size>
    </mainViewport>
    <samples>4</samples>
    <defaultModeScissorTest>1</defaultModeScissorTest>
    <isCameraCombine>1</isCameraCombine>  <!-- 0: 4 camera texture; 1: 1 combined camera texure-->
  </System>
  <FrontFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>25</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </FrontFisheye>
  <RearFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>38</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RearFisheye>
  <LeftFisheye>
    <virtualYaw>90</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </LeftFisheye>
  <RightFisheye>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RightFisheye>
  <FrontPanoFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>30</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </FrontPanoFisheye>
  <RearPanoFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>40</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </RearPanoFisheye>
  <frontViewForModFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>0</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </frontViewForModFisheye>
  <rearViewForModFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>180</virtualPitch>
    <virtualRoll>180</virtualRoll>
  </rearViewForModFisheye>
  <leftViewForModFisheye>
    <virtualYaw>90</virtualYaw>
    <virtualPitch>0</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </leftViewForModFisheye>
  <rightViewForModFisheye>
    <virtualYaw>-90</virtualYaw>
    <virtualPitch>0</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </rightViewForModFisheye>
  <!--
  <FrontFisheyeModel>
    <horizontalHalfFov>90</horizontalHalfFov>
  </FrontFisheyeModel>
  <RearFisheyeModel>
    <horizontalHalfFov>90</horizontalHalfFov>
  </RearFisheyeModel> -->
  <!-- Horizontal partial unfisheye model -->
  <PartialUnfishFront>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishFront>
  <PartialUnfishRear>
    <modelSettings>
      <horizontalHalfFov>67</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRear>
  <PartialUnfishLeft>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishLeft>
  <PartialUnfishRight>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRight>
  <!-- Vertical partial unfisheye model
  <PartialUnfishFrontVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishFrontVert>
  <PartialUnfishRearVert>
    <modelSettings>
      <horizontalHalfFov>68</horizontalHalfFov>
    </modelSettings>
    <delta>0.85</delta>
  </PartialUnfishRearVert>
  <PartialUnfishLeftVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishLeftVert>
  <PartialUnfishRightVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRightVert>
  -->
  <!-- Horizontal partial unfisheye model -->
  <PartialUnfishFrontPano>
    <modelSettings>
      <horizontalHalfFov>85</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishFrontPano>
  <PartialUnfishRearPano>
    <modelSettings>
      <horizontalHalfFov>85</horizontalHalfFov>
    </modelSettings>
    <delta>1.0</delta>
  </PartialUnfishRearPano>
  <!-- Vertical partial unfisheye model
  <PartialUnfishFrontPanoVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishFrontPanoVert>
  <PartialUnfishRearPanoVert>
    <modelSettings>
      <horizontalHalfFov>70</horizontalHalfFov>
    </modelSettings>
    <delta>0.7</delta>
  </PartialUnfishRearPanoVert>
  <VertFrontFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>20</virtualPitch>
    <virtualRoll>90</virtualRoll>
  </VertFrontFisheye>
  <VertRearFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </VertRearFisheye>
  <VertFrontPanoFisheye>
    <virtualYaw>0</virtualYaw>
    <virtualPitch>20</virtualPitch>
    <virtualRoll>90</virtualRoll>
  </VertFrontPanoFisheye>
  <VertRearPanoFisheye>
    <virtualYaw>180</virtualYaw>
    <virtualPitch>35</virtualPitch>
    <virtualRoll>270</virtualRoll>
  </VertRearPanoFisheye>
  <VertLeftFisheye>
    <virtualYaw>90</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>180</virtualRoll>
  </VertLeftFisheye>
  <VertRightFisheye>
    <virtualYaw>270</virtualYaw>
    <virtualPitch>45</virtualPitch>
    <virtualRoll>0</virtualRoll>
  </VertRightFisheye>
  -->
  <FisheyeCropSettingsFront>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsFront>
  <FisheyeCropSettingsRear>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsRear>
  <FisheyeCropSettingsFrontView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>  <!-- order: left, right, bottom, top -->
  </FisheyeCropSettingsFrontView>
  <FisheyeCropSettingsRearView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsRearView>
  <FisheyeCropSettingsFrontPano>
    <cropBounds>-0.9 0.9 -0.8 1.0</cropBounds>
  </FisheyeCropSettingsFrontPano>
  <FisheyeCropSettingsRearPano>
    <cropBounds>-0.9 0.9 -0.8 1.0</cropBounds>
  </FisheyeCropSettingsRearPano>
  <FisheyeCropSettingsLeftView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsLeftView>
  <FisheyeCropSettingRightView>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingRightView>
  <!-- Vertical partial unfisheye model
  <FisheyeCropSettingsFrontVert>
    <cropBounds>-0.5 0.8 -0.65 0.65</cropBounds>
  </FisheyeCropSettingsFrontVert>
  <FisheyeCropSettingsRearVert>
    <cropBounds>-0.8 0.5 -0.65 0.65</cropBounds>
  </FisheyeCropSettingsRearVert>
  <FisheyeCropSettingsFrontPanoVert>
    <cropBounds>-0.75 0.75 -0.75 0.75</cropBounds>
  </FisheyeCropSettingsFrontPanoVert>
  <FisheyeCropSettingsRearPanoVert>
    <cropBounds>-0.74 0.74 -0.75 0.73</cropBounds>
  </FisheyeCropSettingsRearPanoVert>
  <FisheyeCropSettingsLeftViewVert>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingsLeftViewVert>
  <FisheyeCropSettingRightViewVert>
    <cropBounds>-1.0 1.0 -1.0 1.0</cropBounds>
  </FisheyeCropSettingRightViewVert>
    -->
  <Floor>
    <size>20 16</size>
    <resolution>0.2 0.2</resolution>
  </Floor>
  <CustomFloorPlateRenderer>
    <texturedBasePlateFadeoutSpeedMS> 500 </texturedBasePlateFadeoutSpeedMS>
  </CustomFloorPlateRenderer>
  <AudiFloorPlateStateHandler>
    <maxHistoryStandstillDuration> 3.0 </maxHistoryStandstillDuration>
    <enableHistoricGroundplane> 1 </enableHistoricGroundplane>
  </AudiFloorPlateStateHandler>
  <BlurredFloorPlate>
    <resolution> 0.1 </resolution>
    <downsamplingLevels> 0 </downsamplingLevels>
    <nonPowerOfTwo> 1 </nonPowerOfTwo>
    <textureStretch> 0.2 </textureStretch>
  </BlurredFloorPlate>
  <FrontFloor>
    <size>7 12.4</size>
    <standardResolution>0.2 0.2</standardResolution>
    <areaHighResolution>3.0 4.5 -1.0 1.0</areaHighResolution>
    <highResolution>0.04 0.1</highResolution>
  </FrontFloor>
  <RearFloor>
    <size>6.5 12.4</size>
    <standardResolution>0.2 0.2</standardResolution>
    <areaHighResolution>-0.5 -2.5 -1.0 1.0</areaHighResolution>
    <highResolution>0.04 0.1</highResolution>
  </RearFloor>
  <CustomFloorPlate>
    <rearRightCorner>-1.40 -1.30</rearRightCorner>
    <frontLeftCorner> 4.20  1.30</frontLeftCorner>
    <odoTuningThreshold_Velocity>0.1</odoTuningThreshold_Velocity>
    <odoTuningThreshold_Yaw>0.1</odoTuningThreshold_Yaw>
    <odoTuningThreshold_Acceleration>15.0</odoTuningThreshold_Acceleration>
    <odoCompensate_Forward_Accelerate_k>0.06</odoCompensate_Forward_Accelerate_k>
    <odoCompensate_Forward_Accelerate_b>0.0</odoCompensate_Forward_Accelerate_b>
    <odoCompensate_Forward_Decelerate_k>0.04</odoCompensate_Forward_Decelerate_k>
    <odoCompensate_Forward_Decelerate_b>0.0</odoCompensate_Forward_Decelerate_b>
    <odoCompensate_Backward_Accelerate_k>-0.06</odoCompensate_Backward_Accelerate_k>
    <odoCompensate_Backward_Accelerate_b>0.0</odoCompensate_Backward_Accelerate_b>
    <odoCompensate_Backward_Decelerate_k>-0.04</odoCompensate_Backward_Decelerate_k>
    <odoCompensate_Backward_Decelerate_b>0.0</odoCompensate_Backward_Decelerate_b>
  </CustomFloorPlate>
  <MovingFloorPlate>
    <snapshotRearLeftCorner>-3.0 2.0 0.0</snapshotRearLeftCorner>
    <snapshotFrontRightCorner>6.0 -2.0 0.0</snapshotFrontRightCorner>
    <historytRearLeftCorner>-6.0 5.0 0.0</historytRearLeftCorner>
    <historytFrontRightCorner>9.0 -5.0 0.0</historytFrontRightCorner>
    <snapshotTextureWidth>512</snapshotTextureWidth>
    <snapshotTextureHeight>1024</snapshotTextureHeight>
    <historyTextureWidth>512</historyTextureWidth>
    <historyTextureHeight>1024</historyTextureHeight>
  </MovingFloorPlate>
  <Masking>
    <filename>cc/resources/masks.bin</filename>
  </Masking>
  <BaseDataSync>
    <updateMask>3</updateMask>
    <minDeltaExtCalibPos>0.02</minDeltaExtCalibPos>
    <minDeltaExtCalibAng>0.1</minDeltaExtCalibAng>
    <majorDeltaExtCalibAng>0.25</majorDeltaExtCalibAng>
    <timeDeltaMinorCalibUpdate_ms>2000</timeDeltaMinorCalibUpdate_ms>
    <timeDeltaMajorCalibUpdate_ms>500</timeDeltaMajorCalibUpdate_ms>
  </BaseDataSync>
  <Stitching>
    <frontRight>
      <min>280</min>
      <max>345</max>
    </frontRight>
    <rearRight>
      <min>195</min>
      <max>250</max>
    </rearRight>
    <rearLeft>
      <min>110</min>
      <max>165</max>
    </rearLeft>
    <frontLeft>
      <min>15</min>
      <max>80</max>
    </frontLeft>
  </Stitching>
  <DynamicStitching>
    <lineAnimation>0</lineAnimation>
    <bufferCapacity>5</bufferCapacity>
    <useNthFrame>1</useNthFrame>
    <nbrOfRay>15</nbrOfRay>
    <clusterWidth>1</clusterWidth>
    <thresholdValue>0.8</thresholdValue>
    <objectSize>0.3</objectSize>
    <minRadius>0.1</minRadius>
    <spatialFilterParams>
      <type>1</type>
      <kernelSize>5</kernelSize>
      <gaussianSigma>1.8</gaussianSigma>
    </spatialFilterParams>
    <temporalFilterParams>
      <type>1</type>
      <bufferCapacity>14</bufferCapacity>
      <useNthFrame>1</useNthFrame>
    </temporalFilterParams>
    <extractorFrontRight>
      <originOffset>-0.8 -0.8</originOffset>
      <angleStart>270.0</angleStart>
      <angleEnd>360.0</angleEnd>
      <limitTopMiddleZone>315.0</limitTopMiddleZone>
      <limitMiddleBottomZone>315.</limitMiddleBottomZone>
      <defaultValueTopZone>345.0</defaultValueTopZone>
      <defaultValueBottomZone>285.0</defaultValueBottomZone>
      <lengthOfRay>2.0</lengthOfRay>
    </extractorFrontRight>
    <extractorFrontLeft>
      <originOffset>-0.8 -0.8</originOffset>
      <angleStart>0.0</angleStart>
      <angleEnd>90.0</angleEnd>
      <limitTopMiddleZone>45.0</limitTopMiddleZone>
      <limitMiddleBottomZone>45.0</limitMiddleBottomZone>
      <defaultValueTopZone>85.0</defaultValueTopZone>
      <defaultValueBottomZone>5.0</defaultValueBottomZone>
      <lengthOfRay>2.0</lengthOfRay>
    </extractorFrontLeft>
    <extractorRearRight>
      <originOffset>-1.2 -0.8</originOffset>
      <angleStart>180.0</angleStart>
      <angleEnd>270.0</angleEnd>
      <limitTopMiddleZone>225.0</limitTopMiddleZone>
      <limitMiddleBottomZone>225.0</limitMiddleBottomZone>
      <defaultValueTopZone>265.0</defaultValueTopZone>
      <defaultValueBottomZone>185.0</defaultValueBottomZone>
      <lengthOfRay>2.0</lengthOfRay>
    </extractorRearRight>
    <extractorRearLeft>
      <originOffset>-1.2 -0.8</originOffset>
      <angleStart>90.0</angleStart>
      <angleEnd>180.0</angleEnd>
      <limitTopMiddleZone>135.0</limitTopMiddleZone>
      <limitMiddleBottomZone>135.0</limitMiddleBottomZone>
      <defaultValueTopZone>175.0</defaultValueTopZone>
      <defaultValueBottomZone>95.0</defaultValueBottomZone>
      <lengthOfRay>2.0</lengthOfRay>
    </extractorRearLeft>
  </DynamicStitching>
  <VehicleModel>
    <modelFilePath>cc/vehicle_model/vehicle.osgb</modelFilePath>
    <componentNames>
      <frontLeftDoorMirror>LeftSideMirror</frontLeftDoorMirror>
      <frontRightDoorMirror>RightSideMirror</frontRightDoorMirror>
      <floorPlane>floor</floorPlane>
      <interior>interior1</interior>
    </componentNames>
  </VehicleModel>
  <CustomVehicleModel2D>
    <modelFilePath>cc/vehicle_model/vehicle2D.osgb</modelFilePath>
  </CustomVehicleModel2D>
  <TransparentVehicleModel>  <!-- for vehicle 3D -->
    <enableTransparency>1</enableTransparency>
    <targetTransparency>0.3</targetTransparency> <!-- 0.0 is transparency -->
    <transparencyStep>0.01</transparencyStep>
    <waitCycleInit>1</waitCycleInit> <!-- minimum value is 0 -->
    <waitCycle>35</waitCycle> <!-- minimum value is 1, waitCycle=waitCycleInit is better -->
  </TransparentVehicleModel>
  <CustomVehicleModel>
    <lightStateFilePath>cc/vehicle_model/lightstate.json</lightStateFilePath>
    <doorWarningTrans>0.6</doorWarningTrans>
    <transparency>0.9</transparency>  <!-- 0: non transpareny, 1: transparency -->
    <transparencyInterior>1.0</transparencyInterior>
  </CustomVehicleModel>
  <WipingIndicator>
    <period>0.3</period>
    <sustain>0.3</sustain>
    <delay>0.3</delay>
    <offset>0.0</offset>
  </WipingIndicator>
  <BlinkingIndicator>
    <blinkPeriod>10</blinkPeriod>
  </BlinkingIndicator>
  <DoorAnimation>
    <animDurationSec>2.0</animDurationSec>
  </DoorAnimation>
  <CameraFlight>
    <timeMin_s>0.5</timeMin_s>
    <timeMax_s>5.0</timeMax_s>
  </CameraFlight>
  <CamerFlightSpeed>
    <coefficientCamerFlightSpeed>1.0</coefficientCamerFlightSpeed>  <!-- 1.0 is default setting as platform -->
    <deltaSample>0.05</deltaSample>
    <flightTypeCentered>1</flightTypeCentered>
    <centeredWarnDist>0.5</centeredWarnDist> <!-- meter, try to reduce it as much as posible -->
  </CamerFlightSpeed>
  <RenderManager>
    <depthWriteFloor>0</depthWriteFloor>
    <depthWriteWall>0</depthWriteWall>
    <animationDuration>0.5</animationDuration>
    <camDisabledColor>0.6 0.6 0.6 1.0</camDisabledColor>
    <camOffColor>0.05 0.05 0.05 1.0</camOffColor>
  </RenderManager>
  <RenderOrder>
    <floor>10</floor>
    <wall>20</wall>
  </RenderOrder>
  <VehicleMechanicalData><!-- GAC A20 -->
    <wheelbase>2.73</wheelbase> <!-- Wheelbase -->
    <wheelRadius>0.23</wheelRadius> <!-- RWheel -->
    <trackFront>1.77</trackFront> <!-- VEH_WheelTrack_Front -->
    <trackRear>1.84</trackRear> <!-- VEH_WheelTrack_Rear -->
    <wheelWidthFront>0.216</wheelWidthFront> <!-- WheelHalfWidth*2 -->
    <wheelWidthRear>0.216</wheelWidthRear> <!-- WheelHalfWidth*2 -->
    <axleToBumperDistanceFront>0.874</axleToBumperDistanceFront> <!-- LengthFront-Wheelbase 3.604-2.73 -->
    <axleToBumperDistanceRear>0.783</axleToBumperDistanceRear> <!-- LengthRear -->
    <width>1.874</width> <!-- HalfWidth*2 -->
    <widthWithMirrors>2.108</widthWithMirrors>
    <offsetWidthWithOpenDoors>0.3</offsetWidthWithOpenDoors>
    <height>1.478</height>
    <trailerBallPosition>-1.272 0.000 0.422</trailerBallPosition>
  </VehicleMechanicalData>
  <VehicleMechanicalData>  <!-- Vehicle MechanicalData for JTOUR -->
    <wheelbase>2.80</wheelbase>
    <wheelRadius>0.23</wheelRadius>
    <trackFront>1.685</trackFront>
    <trackRear>1.695</trackRear>
    <wheelWidthFront>0.22</wheelWidthFront>
    <wheelWidthRear>0.22</wheelWidthRear>
    <axleToBumperDistanceFront>1.1</axleToBumperDistanceFront>  <!-- actual data is 0.894. 1.1 is used for the transparent picture -->
    <axleToBumperDistanceRear>0.922</axleToBumperDistanceRear> <!-- without backup tyre -->
    <width>2.006</width>
    <widthWithMirrors>2.140</widthWithMirrors>
    <height>1.881</height>
    <trailerBallPosition>-1.28 0.0 0.43</trailerBallPosition>
    <leftHandDrive>1</leftHandDrive>
    <automaticGearBox>1</automaticGearBox>
    <trailerHitch>0</trailerHitch>
  </VehicleMechanicalData>
  <VehicleMechanicalData>  <!-- Vehicle MechanicalData for Nissan VX6 -->
    <wheelbase>2.85</wheelbase>
    <wheelRadius>0.23</wheelRadius>
    <trackFront>1.635</trackFront>
    <trackRear>1.640</trackRear>
    <wheelWidthFront>0.22</wheelWidthFront>
    <wheelWidthRear>0.22</wheelWidthRear>
    <axleToBumperDistanceFront>0.925</axleToBumperDistanceFront>  <!-- actual data is 0.894. 1.1 is used for the transparent picture -->
    <axleToBumperDistanceRear>0.883</axleToBumperDistanceRear>
    <width>1.890</width>
    <widthWithMirrors>2.157</widthWithMirrors>
    <height>1.655</height>
    <trailerBallPosition>-1.28 0.0 0.43</trailerBallPosition>
    <leftHandDrive>1</leftHandDrive>
    <automaticGearBox>1</automaticGearBox>
    <trailerHitch>0</trailerHitch>
  </VehicleMechanicalData>
  <UltrasonicZones><!-- GAC A20 -->
    <frontArea1> 2.730  0.860  2.730  2.990</frontArea1>  <!-- DispAreaFront :: A1X A1Y  A4X A4Y -->
    <frontArea2> 2.985  0.850  3.504  2.888</frontArea2>
    <frontArea3> 3.220  0.840  4.225  2.589</frontArea3>
    <frontArea4> 3.410  0.699  4.844  2.114</frontArea4>
    <frontArea5> 3.520  0.495  5.319  1.495</frontArea5>
    <frontArea6> 3.560  0.250  5.618  0.774</frontArea6>
    <leftArea1>  0.000  0.860  0.000  2.990</leftArea1>  <!-- DispAreaLeft :: A1X A1Y  A4X A4Y -->
    <leftArea2>  0.341  0.860  0.341  2.990</leftArea2>
    <leftArea3>  0.683  0.860  0.683  2.990</leftArea3>
    <leftArea4>  1.024  0.860  1.024  2.990</leftArea4>
    <leftArea5>  1.365  0.860  1.365  2.990</leftArea5>
    <leftArea6>  1.706  0.860  1.706  2.990</leftArea6>
    <leftArea7>  2.048  0.860  2.048  2.990</leftArea7>
    <leftArea8>  2.389  0.860  2.389  2.990</leftArea8>
    <rearArea1> -0.258 -0.860 -0.775 -2.893</rearArea1>  <!-- DispAreaRear :: A2X A2Y  A3X A3Y -->
    <rearArea2> -0.497 -0.830 -1.497 -2.594</rearArea2>
    <rearArea3> -0.660 -0.690 -2.118 -2.118</rearArea3>
    <rearArea4> -0.690 -0.450 -2.594 -1.497</rearArea4>
    <rearArea5> -0.710 -0.200 -2.893 -0.775</rearArea5>
    <rearArea6> -0.720  0.000 -2.995  0.000</rearArea6>
  </UltrasonicZones>
  <VehicleContour><!-- GAC A20 -->
    <front0> 3.055  0.893</front0> <!-- VehContourFront -->
    <front1> 3.218  0.877</front1>
    <front2> 3.358  0.821</front2>
    <front3> 3.537  0.576</front3>
    <front4> 3.577  0.398</front4>
    <front5> 3.604  0.000</front5>
    <side0>  2.820  0.934</side0>
    <side1>  2.530  0.937</side1>
    <side2>  1.810  0.910</side2>
    <side3>  0.450  0.920</side3>
    <side4> -0.180  0.930</side4>
    <rear0> -0.259  0.884</rear0>
    <rear1> -0.443  0.870</rear1>
    <rear2> -0.635  0.792</rear2>
    <rear3> -0.698  0.645</rear3>
    <rear4> -0.736  0.371</rear4>
    <rear5> -0.754  0.000</rear5>
    <unfoldedMirror0> 2.05 1.07</unfoldedMirror0>
    <unfoldedMirror1> 0.0 0.0</unfoldedMirror1>
    <unfoldedMirror2> 0.0 0.0</unfoldedMirror2>
    <unfoldedMirror3> 0.0 0.0</unfoldedMirror3>
    <foldedMirror0> 0.0 0.0</foldedMirror0>
    <foldedMirror1> 0.0 0.0</foldedMirror1>
    <foldedMirror2> 0.0 0.0</foldedMirror2>
    <foldedMirror3> 0.0 0.0</foldedMirror3>
  </VehicleContour>
  <Vehicle2DWheels>
    <wheelSize>0.8 0.3</wheelSize>
  </Vehicle2DWheels>
  <ViewModeStateMachine>
    <distTrigIn_m>0.5</distTrigIn_m>
    <distTrigOut_m>1.0</distTrigOut_m>
    <speedTrigIn_kph>15</speedTrigIn_kph>
    <speedTrigOut_kph>20</speedTrigOut_kph>
    <steeringAngleTrigIn>225</steeringAngleTrigIn>
    <steeringAngleTrigOut>90</steeringAngleTrigOut>
    <unavlMsgDisplay_Timeout>5000</unavlMsgDisplay_Timeout>
    <threatDuration>5000</threatDuration>
    <parkingScreen_Delay_ms>500</parkingScreen_Delay_ms>
    <parkingGuidanceScreen_Delay_ms>100</parkingGuidanceScreen_Delay_ms>
    <firstShowReq_Delay_ms>200</firstShowReq_Delay_ms>
    <sm_Delay_ms>1000</sm_Delay_ms> <!-- smaller than 100,000 msec -->
    <sm_IndicatorBackToFrontView_Delay_ms>300</sm_IndicatorBackToFrontView_Delay_ms>
    <sm_CpcShow_Delay_s>8</sm_CpcShow_Delay_s>
    <vehicleModels>2</vehicleModels> <!-- STEX: 6.0; STHX: 8.0; MREC: 2.0; MRHC: 3.0. -->
  </ViewModeStateMachine>
  <!-- UltrasonicZones for GAC A18Y from ECU coding -->
  <UltrasonicZones>
    <zone00middleLine> 3.706171 0.362568 6.259671 0.542568</zone00middleLine>
    <zone01middleLine> 3.412146 0.881115 4.735647 2.113615</zone01middleLine>
    <zone02middleLine> 2.631993 0.945 2.631993 3.049999</zone02middleLine>
    <zone03middleLine> 1.884274 0.945 1.884274 3.049999</zone03middleLine>
    <zone04middleLine> 1.136556 0.945 1.136556 3.049999</zone04middleLine>
    <zone05middleLine> 0.388837 0.945 0.388837 3.049999</zone05middleLine>
    <zone06middleLine> -0.451455 0.920818 -1.579955 2.102318</zone06middleLine>
    <zone07middleLine> -0.868942 0.368958 -3.087942 0.497958</zone07middleLine>
    <zone08middleLine> -0.868942 -0.368958 -3.087942 -0.497958</zone08middleLine>
    <zone09middleLine> -0.451455 -0.920818 -1.579955 -2.102318</zone09middleLine>
    <zone10middleLine> 0.388837 -0.945 0.388837 -3.049999</zone10middleLine>
    <zone11middleLine> 1.136556 -0.945 1.136556 -3.049999</zone11middleLine>
    <zone12middleLine> 1.884274 -0.945 1.884274 -3.049999</zone12middleLine>
    <zone13middleLine> 2.631993 -0.945 2.631993 -3.049999</zone13middleLine>
    <zone14middleLine> 3.412146 -0.881115 4.735647 -2.113615</zone14middleLine>
    <zone15middleLine> 3.706171 -0.362568 6.259671 -0.542568</zone15middleLine>
    <zone00leftBorderLine> 3.544565 0.717356 6.191565 1.077356</zone00leftBorderLine>
    <zone01leftBorderLine> 3.005586 0.945 3.005586 3.049999</zone01leftBorderLine>
    <zone02leftBorderLine> 2.258399 0.945 2.258399 3.049999</zone02leftBorderLine>
    <zone03leftBorderLine> 1.510149 0.945 1.510149 3.049999</zone03leftBorderLine>
    <zone04leftBorderLine> 0.762963 0.945 0.762963 3.049999</zone04leftBorderLine>
    <zone05leftBorderLine> 0.014712 0.945 0.014712 3.049999</zone05leftBorderLine>
    <zone06leftBorderLine> -0.776333 0.734606 -3.033334 0.992606</zone06leftBorderLine>
    <zone07leftBorderLine> -0.89 -0.0 -3.071 -0.0</zone07leftBorderLine>
    <zone08leftBorderLine> -0.776333 -0.734606 -3.033334 -0.992606</zone08leftBorderLine>
    <zone09leftBorderLine> 0.014712 -0.945 0.014712 -3.049999</zone09leftBorderLine>
    <zone10leftBorderLine> 0.762963 -0.945 0.762963 -3.049999</zone10leftBorderLine>
    <zone11leftBorderLine> 1.510149 -0.945 1.510149 -3.049999</zone11leftBorderLine>
    <zone12leftBorderLine> 2.258399 -0.945 2.258399 -3.049999</zone12leftBorderLine>
    <zone13leftBorderLine> 3.005586 -0.945 3.005586 -3.049999</zone13leftBorderLine>
    <zone14leftBorderLine> 3.544565 -0.717356 6.191565 -1.077356</zone14leftBorderLine>
    <zone15leftBorderLine> 3.74 -0.0 6.199999 -0.0</zone15leftBorderLine>
  </UltrasonicZones>
  <VehicleContour>
    <front0> 3.044 0.945</front0>
    <front1> 3.237 0.936</front1>
    <front2> 3.454  0.868</front2>
    <front3> 3.656 0.532</front3>
    <front4> 3.717  0.326</front4>
    <front5> 3.74  0.0</front5>

    <side0>  2.9 0.945</side0>
    <side1>  2.1 0.945</side1>
    <side2>  0.5 0.945</side2>
    <side3>  -0.3 0.945</side3>
    <side4>  0.0 0.0</side4>

    <rear0> -0.359 0.941</rear0>
    <rear1> -0.538 0.907</rear1>
    <rear2> -0.736  0.802</rear2>
    <rear3> -0.815 0.67</rear3>
    <rear4> -0.867 0.403</rear4>
    <rear5> -0.89 0.0</rear5>

    <unfoldedMirror0> 2.049 1.101999</unfoldedMirror0>
    <unfoldedMirror1> 0.0 0.0</unfoldedMirror1>
    <unfoldedMirror2> 0.0 0.0</unfoldedMirror2>
    <unfoldedMirror3> 0.0 0.0</unfoldedMirror3>

    <foldedMirror0> 0.0 0.0</foldedMirror0>
    <foldedMirror1> 0.0 0.0</foldedMirror1>
    <foldedMirror2> 0.0 0.0</foldedMirror2>
    <foldedMirror3> 0.0 0.0</foldedMirror3>
  </VehicleContour>
  <!-- Vehicle contour for DAI V223
  <VehicleContour>
    <front0> 3.540 0.960</front0>
    <front1> 3.722 0.920</front1>
    <front2> 3.922 0.802</front2>
    <front3> 4.018 0.584</front3>
    <front4> 4.082 0.340</front4>
    <front5> 4.110 0.096</front5>

    <side0>  3.540 0.960</side0>
    <side1>  2.904 0.977</side1>
    <side2>  2.269 0.977</side2>
    <side3> -0.073 0.977</side3>
    <side4> -0.378 0.960</side4>

    <rear0> -0.378 0.960</rear0>
    <rear1> -0.857 0.886</rear1>
    <rear2> -1.028 0.830</rear2>
    <rear3> -1.134 0.712</rear3>
    <rear4> -1.196 0.430</rear4>
    <rear5> -1.195 0.071</rear5>

    <unfoldedMirror0> 0.0 0.0</unfoldedMirror0>
    <unfoldedMirror1> 0.0 0.0</unfoldedMirror1>
    <unfoldedMirror2> 0.0 0.0</unfoldedMirror2>
    <unfoldedMirror3> 0.0 0.0</unfoldedMirror3>

    <foldedMirror0> 0.0 0.0</foldedMirror0>
    <foldedMirror1> 0.0 0.0</foldedMirror1>
    <foldedMirror2> 0.0 0.0</foldedMirror2>
    <foldedMirror3> 0.0 0.0</foldedMirror3>
  </VehicleContour> -->
  <!-- Outline for L663_90 is below: -->
  <VehicleOutline>
    <pointCount>19</pointCount>
    <frontBumperStartPointIndex>0</frontBumperStartPointIndex>
    <frontBumperEndPointIndex>5</frontBumperEndPointIndex>
    <mirrorPointIndex>9</mirrorPointIndex>
    <rearBumperStartPointIndex>13</rearBumperStartPointIndex>
    <rearBumperEndPointIndex>18</rearBumperEndPointIndex>
    <points>
      <p00> 3.43046 0.140141</p00>
      <p01> 3.41285 0.398509</p01>
      <p02> 3.27975 0.701896</p02>
      <p03> 3.09184 0.938733</p03>
      <p04> 2.92156 0.985709</p04>
      <p05> 2.75127 0.995496</p05>
      <p06> 2.38916 0.999410</p06>
      <p07> 1.90766 0.999410</p07>
      <p08> 1.56121 0.999410</p08>
      <p09> 1.41637 1.054220</p09>
      <p10> 1.39679 0.985709</p10>
      <p11> 0.84562 0.985709</p11>
      <p12> 0.26367 0.985709</p12>
      <p13>-0.29043 0.985709</p13>
      <p14>-0.43332 0.972008</p14>
      <p15>-0.71321 0.850653</p15>
      <p16>-0.94614 0.635346</p16>
      <p17>-1.14383 0.386765</p17>
      <p18>-1.15949 0.133176</p18>
    </points>
  </VehicleOutline>
  <!-- Outline for L663_110 is below: -->
  <!--
  <VehicleOutline>
    <pointCount>20</pointCount>
    <frontBumperStartPointIndex>0</frontBumperStartPointIndex>
    <frontBumperEndPointIndex>5</frontBumperEndPointIndex>
    <mirrorPointIndex>9</mirrorPointIndex>
    <rearBumperStartPointIndex>14</rearBumperStartPointIndex>
    <rearBumperEndPointIndex>19</rearBumperEndPointIndex>
    <points>
      <p00> 3.86833 0.140141</p00>
      <p01> 3.85071 0.398509</p01>
      <p02> 3.71761 0.701896</p02>
      <p03> 3.52971 0.938733</p03>
      <p04> 3.35942 0.985709</p04>
      <p05> 3.18913 0.995496</p05>
      <p06> 2.82702 0.999410</p06>
      <p07> 2.34552 0.999410</p07>
      <p08> 1.99907 0.999410</p08>
      <p09> 1.85423 1.054220</p09>
      <p10> 1.83466 0.985709</p10>
      <p11> 1.31886 0.985709</p11>
      <p12> 0.78830 0.985709</p12>
      <p13> 0.23182 0.985709</p13>
      <p14>-0.29043 0.985709</p14>
      <p15>-0.43332 0.972008</p15>
      <p16>-0.71321 0.850653</p16>
      <p17>-0.94614 0.635346</p17>
      <p18>-1.14383 0.386765</p18>
      <p19>-1.15949 0.133176</p19>
    </points>
  </VehicleOutline>
  -->
  <TileOverlay>
    <extractorOffset>-0.25</extractorOffset>
    <heightOverGround>0.01</heightOverGround>
    <shieldAlpha>1.0</shieldAlpha>
    <solidLine2DAlpha>1.0</solidLine2DAlpha>
    <shadow2DAlphaInner>0.9</shadow2DAlphaInner>
    <shadow2DAlpha>0.35</shadow2DAlpha>
    <shield3DAlphaRatioTop>0.05</shield3DAlphaRatioTop>
    <shield3DAlphaRatioDivide>0.3</shield3DAlphaRatioDivide>
    <shield3DAlphaRatioBottom>0.7</shield3DAlphaRatioBottom>
    <shield3DSideAlphaRatioTop>0.02</shield3DSideAlphaRatioTop>
    <shield3DSideAlphaRatioDivide>0.7</shield3DSideAlphaRatioDivide>
    <shield3DSideAlphaRatioBottom>0.7</shield3DSideAlphaRatioBottom>
    <shield3DBottomCoverAlphaRatio>1.0</shield3DBottomCoverAlphaRatio>
    <shieldHeight>1.0</shieldHeight>
    <shieldThickness>0.2</shieldThickness>
    <solidLineThicknessRatio>0.3</solidLineThicknessRatio>
    <shield3DThicknessRatio>0.1</shield3DThicknessRatio>
    <shieldOffset>0.03</shieldOffset>
    <shieldTopOffset>0.0</shieldTopOffset>
    <shieldRatioDivide>0.3</shieldRatioDivide>
    <shieldSideRatioDivide>0.5</shieldSideRatioDivide>
    <hairlineWidth>1.0</hairlineWidth>
    <hairlineAlpha>0.5</hairlineAlpha>
    <hairlineBlooming>0.6</hairlineBlooming>
    <hairlineAlphaRatioTop>0.1</hairlineAlphaRatioTop>
    <hairlineAlphaRatioDivide>0.5</hairlineAlphaRatioDivide>
    <hairlineAlphaRatioBottom>0.9</hairlineAlphaRatioBottom>
    <distanceDefault>1.25</distanceDefault>
    <distanceThreshL1>0.3</distanceThreshL1>
    <distanceThreshL2>0.7</distanceThreshL2>
    <distanceThreshL3>1.2</distanceThreshL3>
    <HysteresisDistanceThresh>0.05</HysteresisDistanceThresh>
    <MinDistanceThresh>0.30</MinDistanceThresh>
    <MinDistanceThreshForPosDisp>0.30</MinDistanceThreshForPosDisp>
    <distanceThreshCorner>0.60</distanceThreshCorner>
    <!--<SplineTypeForEachSegment>1</SplineTypeForEachSegment>-->
    <smoothingFrontCornerSplineType>0.36</smoothingFrontCornerSplineType>
    <smoothingRearCornerSplineType>0.72</smoothingRearCornerSplineType>
    <colors>
      <r2>241 29 30 255</r2>
      <r1>241 29 30 255</r1>
      <y2>244 179 0 255</y2>
      <y1>244 179 0 255</y1>
      <g2>0 205 69 255</g2>
      <g1>0 205 69 255</g1>
    </colors>
  </TileOverlay>
  <Trajectory>
    <length>3.0</length>
    <gradientWidth>0.007</gradientWidth>
    <refLineVisible>0</refLineVisible>
    <animDurRemainingDistance>1000.0</animDurRemainingDistance>
    <outermostLine_Width>0.04</outermostLine_Width>
    <outermostLine_Color_Manual>0.97 0.66 0.02 1.0</outermostLine_Color_Manual>
    <outermostLine_Color_Auto>0.0 0.9 0.0 0.6</outermostLine_Color_Auto>
    <outermostLine_Colorful_Color_1>1.0 0.0 0.0 0.9</outermostLine_Colorful_Color_1>
    <outermostLine_Colorful_Color_2>0.97 0.66 0.02 1.0</outermostLine_Colorful_Color_2>
    <outermostLine_Colorful_Color_3>0.0 0.745 0.27 1.0</outermostLine_Colorful_Color_3>
    <outermostLine_OL_WT_minGap>0.12</outermostLine_OL_WT_minGap>
    <outermostLineColoful_DI_MagicOffset>0.014</outermostLineColoful_DI_MagicOffset>
    <outermostLine_rear_point_offset>0.15</outermostLine_rear_point_offset>
    <outermostLine_front_point_offset>0.20</outermostLine_front_point_offset>
    <outermostLine_VehContour_Gap>0.0</outermostLine_VehContour_Gap>
    <wheelTrack_Width_Whole>0.26</wheelTrack_Width_Whole>
    <wheelTrack_Width_BorderLine>0.013</wheelTrack_Width_BorderLine>
    <wheelTrack_Color_Manual_Inside>0.97 0.66 0.02 0.23</wheelTrack_Color_Manual_Inside>
    <wheelTrack_Color_Manual_BorderLine>0.97 0.66 0.02 0.8</wheelTrack_Color_Manual_BorderLine>
    <wheelTrack_Color_Auto_Close_Inside>0.0 0.90 0.0 0.2</wheelTrack_Color_Auto_Close_Inside>
    <wheelTrack_Color_Auto_Close_BorderLine>0.0 0.90 0.0 0.6</wheelTrack_Color_Auto_Close_BorderLine>
    <wheelTrack_Color_Auto_Far_Inside>0.5 0.5 0.5 0.2</wheelTrack_Color_Auto_Far_Inside>
    <wheelTrack_Color_Auto_Far_BorderLine>0.5 0.5 0.5 0.6</wheelTrack_Color_Auto_Far_BorderLine>
    <actionPoint_Length>0.1</actionPoint_Length>
    <actionPoint_Color>1.0 0.0 0.0 0.9</actionPoint_Color>
    <renderOffset_Front>0.29</renderOffset_Front>  <!-- disable action point -->
    <renderOffset_Rear>0.29</renderOffset_Rear>    <!-- disable action point -->
    <DL1_Width>0.01</DL1_Width>
    <DL1_Offset_Front>0.30</DL1_Offset_Front>
    <DL1_Offset_Rear>0.30</DL1_Offset_Rear>
    <DL1_Color>1.0 0.0 0.0 0.9</DL1_Color>
    <DIs>
      <DIlength>0.08</DIlength>
      <DI1_Pos>0.5</DI1_Pos>
      <DI1_Thickness>0.062</DI1_Thickness>
      <DI2_Pos>1.3</DI2_Pos>
      <DI2_Thickness>0.076</DI2_Thickness>
      <DI3_Pos>2.5</DI3_Pos>
      <DI3_Thickness>0.07</DI3_Thickness>
      <DI4_Pos>2.0</DI4_Pos>
      <DI4_Thickness>0.07</DI4_Thickness>
      <DI5_Pos>4.0</DI5_Pos>
      <DI5_Thickness>0.07</DI5_Thickness>
    </DIs>
    <THTraj_StartPoint>-1.116 0.0 0.0</THTraj_StartPoint> <!-- Dyn70k, height over the ground, recomend to set Z to '0' -->
    <THTraj_Width>0.1</THTraj_Width>
    <THTraj_Length>1.2</THTraj_Length>
    <THTraj_Color>0.97 0.66 0.02 1.0</THTraj_Color>
    <THTrajBorder_Color>0.97 0.66 0.02 0.0</THTrajBorder_Color>
    <THTraj_ColorGradientPosRatio>0.6</THTraj_ColorGradientPosRatio>
  </Trajectory>
  <CalibOverlay>
    <calibSwitch>1</calibSwitch>
    <calibOverlayColor>1.0 0.0 0.0 1.0</calibOverlayColor>
  </CalibOverlay>
  <SplineOverlay>
    <colors>
      <nearColor>0.953 0.0 0.039 1.0</nearColor>
      <middleColor>1.0 0.494 0.0 1.0</middleColor>
      <farColor>0.0 0.761 0.216 1.0</farColor>
      <nearColor_OffCourse>0.68 0.22 0.2 1.0</nearColor_OffCourse>
      <middleColor_OffCourse>0.96 0.72 0.32 1.0</middleColor_OffCourse>
      <farColor_OffCourse>1.0 1.0 1.0 0.4</farColor_OffCourse>
      <nearColor_OffCourse_Shadow>0.8 0.0 0.039 0.2</nearColor_OffCourse_Shadow>
      <middleColor_OffCourse_Shadow>0.8 0.6 0.0 0.2</middleColor_OffCourse_Shadow>
      <farColor_OffCourse_Shadow>1.0 1.0 1.0 0.2</farColor_OffCourse_Shadow>
      <shadowColor>0.0 0.0 0.0 0.0</shadowColor>
    </colors>
    <zPos>0.01</zPos>
    <distanceNear>0.3</distanceNear>
    <distanceMiddle>0.6</distanceMiddle>
    <distanceFar>0.9</distanceFar>
    <shadowOuterContourDist>4.0</shadowOuterContourDist>
    <splineWidth>0.04</splineWidth>
    <splineWidthShadow>1.0</splineWidthShadow>
    <maxShowDistance>2.0</maxShowDistance>
    <endPointOffset>0.7</endPointOffset>
    <handlePointScale>0.35</handlePointScale>
    <handlePointScaleMid>0.55</handlePointScaleMid>
    <farHandlePointScale>0.8</farHandlePointScale>
    <fadingWidth>10</fadingWidth>
  </SplineOverlay>
  <PanoramaViewFront>
    <horizontalStart>0.0</horizontalStart>
    <horizontalEnd>9.0</horizontalEnd>
    <verticalStart>0.0</verticalStart>
    <verticalEnd>6.0</verticalEnd>
    <resolutionHorizontal>20</resolutionHorizontal>
    <resolutionVertical>40</resolutionVertical>
    <planeWidth>9.0</planeWidth>
    <planeHeight>6.0</planeHeight>
    <cylinder>
      <cylinderRad>4.5</cylinderRad>
      <cylinderHeight>6.0</cylinderHeight>
      <cylinderFovDeg>180.0</cylinderFovDeg>
      <offsetVertical>0.5</offsetVertical>
      <offsetHorizontal>0.0</offsetHorizontal>
      <x1Factor>23</x1Factor>
      <x2Factor>61</x2Factor>
      <x3Factor>193</x3Factor>
      <x4Factor>231</x4Factor>
      <c1>1.0</c1>
      <c2>1.0</c2>
      <point0>0.0</point0>
      <point1>0.0</point1>
      <point2>0.0</point2>
      <point3>0.0</point3>
      <point4>0.0</point4>
      <point5>0.0</point5>
      <smoothing>0</smoothing>
    </cylinder>
  </PanoramaViewFront>
  <PanoramaViewRear>
    <horizontalStart>0.0</horizontalStart>
    <horizontalEnd>9.0</horizontalEnd>
    <verticalStart>0.0</verticalStart>
    <verticalEnd>6.0</verticalEnd>
    <resolutionHorizontal>20</resolutionHorizontal>
    <resolutionVertical>40</resolutionVertical>
    <planeWidth>9.0</planeWidth>
    <planeHeight>6.0</planeHeight>
    <cylinder>
      <cylinderRad>4.5</cylinderRad>
      <cylinderHeight>6.0</cylinderHeight>
      <cylinderFovDeg>180.0</cylinderFovDeg>
      <offsetVertical>1.0</offsetVertical>
      <offsetHorizontal>0.0</offsetHorizontal>
      <x1Factor>23</x1Factor>
      <x2Factor>61</x2Factor>
      <x3Factor>190</x3Factor>
      <x4Factor>227</x4Factor>
      <c1>1.0</c1>
      <c2>1.0</c2>
      <point0>0.0</point0>
      <point1>0.0</point1>
      <point2>0.0</point2>
      <point3>0.0</point3>
      <point4>0.0</point4>
      <point5>0.0</point5>
      <smoothing>0</smoothing>
    </cylinder>
  </PanoramaViewRear>
  <VirtCam>
    <plan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
      <stitchingBands>0.4 0.4 0.4 0.4</stitchingBands>
      <stitchingBandsFolded>0.1 0.4 0.4 0.1</stitchingBandsFolded>
    </plan>
    <front>
      <eye>3.15 0 0.89</eye>
      <center>4.11 0 0.60</center>
      <up>0 0 1</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </front>
    <rear>
      <eye>-0.73 0 0.70</eye>
      <center>-1.64 0 0.29</center>
      <up>-0.41 0 0.91</up>
      <fovy>107</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rear>
    <frontJunction>
      <eye>3.95 0 0.89</eye>
      <center>4.856 0 0.467</center>
      <up>0 0 1</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontJunction>
    <rearJunction>
      <eye>3.95 0 0.89</eye>
      <center>4.856 0 0.467</center>
      <up>0 0 1</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearJunction>
    <leftView>
      <eye>1.47 2.38 1.84</eye>
      <center>1.70 2.38 0.87</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </leftView>
    <rightView>
      <eye>1.47 -2.38 1.84</eye>
      <center>1.70 -2.38 0.87</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rightView>
    <trailer>
      <eye>-0.78 0 0.92</eye>
      <center>-1.52 0 0.38</center>
      <up>0 0 1</up>
      <fovy>95</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </trailer>
    <trailerZoom>
      <eye>-0.78 0 0.92</eye>
      <center>-1.52 0 0.38</center>
      <up>0 0 1</up>
      <fovy>80</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </trailerZoom>
    <bonnet>
      <eye>-0.33 0 1.77</eye>
      <center>0.64 0 1.51</center>
      <up>0.26 0 0.97</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </bonnet>
    <bonnetZoom>
      <eye>-0.33 0 1.77</eye>
      <center>0.64 0 1.51</center>
      <up>0.26 0 0.97</up>
      <fovy>30</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </bonnetZoom>
    <driveAssistLeft>
      <eye>1.92655 1.02723 1.29545</eye>
      <center>2.71 1.24 0.71</center>
      <up>0 -0.15 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistLeft>
    <driveAssistMain>
      <eye>3.93 0 0.70506</eye>
      <center>4.84 0 0.29</center>
      <up>0 0 1</up>
      <fovy>100</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </driveAssistMain>
    <driveAssistRight>
      <eye>1.92655 -1.02723 1.29545</eye>
      <center>2.71 -1.24 0.71</center>
      <up>0 0.15 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistRight>
    <driveAssistDualFront>
      <eye>3.93 0 0.70506</eye>
      <center>4.84 0 0.29</center>
      <up>0 0 1</up>
      <fovy>100</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 135 90</stitchingLines>
    </driveAssistDualFront>
    <driveAssistDualRight>
      <eye>1.92655 -1.02723 1.29545</eye>
      <center>2.71 -1.24 0.71</center>
      <up>0 0.11 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistDualRight>
    <driveAssistDualLeft>
      <eye>1.92655 1.02723 1.29545</eye>
      <center>2.71 1.24 0.71</center>
      <up>0 -0.11 1</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </driveAssistDualLeft>
    <frontLeft>
      <eye>4.83 3.87 3.56</eye>
      <center>4.28 3.24 3.01</center>
      <up>-0.37 -0.42 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </frontLeft>
    <rearLeft>
      <eye>-2.41 3.45 3.56</eye>
      <center>-1.79 2.89 3.01</center>
      <up>-0.41 0.37 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </rearLeft>
    <frontRight>
      <eye>4.83 -3.87 3.56</eye>
      <center>4.28 -3.24 3.01</center>
      <up>0.37 -0.42 0.83</up>
      <fovy>55</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </frontRight>
    <rearRight>
      <eye>-2.41 -3.45 3.56</eye>
      <center>-1.79 -2.89 3.01</center>
      <up>0.41 0.37 0.83</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </rearRight>
    <persFront>
      <eye>6.72 0 3.44</eye>
      <center>5.9 0 2.86</center>
      <up>0 0 1</up>
      <fovy>48</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 225 120 45</stitchingLines>
    </persFront>
    <persRight>
      <eye>1.37 -4.47 3.84</eye>
      <center>1.37 -3.77 3.12</center>
      <up>0 0.73 0.70</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 225 120 45</stitchingLines>
    </persRight>
    <persRear>
      <eye>-4.38 0 2.68</eye>
      <center>-3.49 0 2.22</center>
      <up>0 0 1</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 120 45</stitchingLines>
    </persRear>
    <persLeft>
      <eye>1.37 4.47 3.84</eye>
      <center>1.37 3.77 3.12</center>
      <up>0 -0.73 0.70</up>
      <fovy>50</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 180 120 45</stitchingLines>
    </persLeft>
    <kerbLeft>
      <eye>-1.6 1.51 1.25</eye>
      <center>-0.47 1.22 0.71</center>
      <up>0.35 -0.14 0.93</up>
      <fovy>50</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>0 180 180 0</stitchingLines>
    </kerbLeft>
    <kerbRight>
      <eye>-1.6 -1.51 1.25</eye>
      <center>-0.47 -1.22 0.71</center>
      <up>0.35 0.14 0.93</up>
      <fovy>50</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>359 180 180 0</stitchingLines>
    </kerbRight>
    <frontThreat>
      <eye>4.4 0 2.6</eye>
      <center>4.4 0 1.6</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 270 90 90</stitchingLines>
    </frontThreat>
    <rearThreat>
      <eye>-1.6 0 2</eye>
      <center>-1.6 0 1</center>
      <up>1 0 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 270 90 90</stitchingLines>
    </rearThreat>
    <fullScreen>
      <eye>1.3 0 9</eye>
      <center>1.3 0 5.5</center>
      <up>0 -1 0</up>
      <fovy>35</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </fullScreen>
    <frontWheelLeft>
      <eye>1.47 1.11 1.62</eye>
      <center>2.15 1.08 0.88</center>
      <up>0.73 -0.03 0.68</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontWheelLeft>
    <frontWheelRight>
      <eye>1.47 -1.11 1.62</eye>
      <center>2.15 -1.10 0.89</center>
      <up>0.73 0.01 0.68</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </frontWheelRight>
    <rearWheelLeft>
      <eye>1.89 1.0 1.53</eye>
      <center>1.12 0.99 0.90</center>
      <up>-0.63 -0.01 0.77</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearWheelLeft>
    <rearWheelRight>
      <eye>1.89 -1.0 1.53</eye>
      <center>1.12 -1.01 0.89</center>
      <up>-0.64 -0.01 0.77</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </rearWheelRight>
    <overTheRoof>
      <eye>-7.5 0.0 8.25</eye>
      <center>1.4 0.0 0.0</center>
      <up>0.679819 0 0.73338</up>
      <fovy>45</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </overTheRoof>
    <parkView>
      <eye>-23.34 0 31.41</eye>
      <center>-22.75 0 30.61</center>
      <up>1.0 0 3.0</up>
      <fovy>37.45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingMaskFolded>6</stitchingMaskFolded>
      <stitchingLines>315 225 135 45</stitchingLines>
      <stitchingLinesFolded>275 225 135 78</stitchingLinesFolded>
      <cullingMask>0</cullingMask>
    </parkView>
    <!-- Vertical mode -->
    <vertPlan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertPlan>
    <vertLeftView>
      <eye>1.47 2.38 1.84</eye>
      <center>1.70 2.38 0.87</center>
      <up>0 -1 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertLeftView>
    <vertRightView>
      <eye>1.47 -2.38 1.84</eye>
      <center>1.70 -2.38 0.87</center>
      <up>0 -1 0</up>
      <fovy>90</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertRightView>
    <vertWheelLeftView>
      <eye>1.75 1.0 1.34</eye>
      <center>1.75 1.0 0.34</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertWheelLeftView>
    <vertWheelRightView>
      <eye>1.75 -1.0 1.34</eye>
      <center>1.75 -1.0 0.34</center>
      <up>0 -1 0</up>
      <fovy>60</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertWheelRightView>
    <vertPersFront>
      <eye>6.54 0 4.92</eye>
      <center>5.81 0 4.24</center>
      <up>0 1 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 225 120 45</stitchingLines>
    </vertPersFront>
    <vertPersRear>
      <eye>-3.75 0 4.92</eye>
      <center>-3.02 0 4.24</center>
      <up>0 -1 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>270 225 120 45</stitchingLines>
    </vertPersRear>
    <vertRearLeft>
      <eye>-1.41 4.29 4.94</eye>
      <center>-1.01 3.68 4.25</center>
      <up>-0.84 -0.55 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </vertRearLeft>
    <vertRearRight>
      <eye>-1.41 -4.29 4.94</eye>
      <center>-1.01 -3.68 4.25</center>
      <up>0.84 -0.55 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </vertRearRight>
    <vertFrontLeft>
      <eye>4.03 4.41 4.94</eye>
      <center>3.65 3.78 4.25</center>
      <up>-0.86 0.51 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>315 225 175 1</stitchingLines>
    </vertFrontLeft>
    <vertFrontRight>
      <eye>4.03 -4.41 4.94</eye>
      <center>3.65 -3.78 4.25</center>
      <up>0.86 0.51 0</up>
      <fovy>53</fovy>
      <stitchingMask>0</stitchingMask>
      <stitchingLines>359 190 135 45</stitchingLines>
    </vertFrontRight>
    <!-- parking plan view -->
    <horiParkingPlan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>1 0 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </horiParkingPlan>
    <vertParkingPlan>
      <eye>1.3 0 15</eye>
      <center>1.3 0 7.8</center>
      <up>0 -1 0</up>
      <fovy>45</fovy>
      <stitchingMask>15</stitchingMask>
      <stitchingLines>315 225 135 45</stitchingLines>
    </vertParkingPlan>
  </VirtCam>
  <Views>
    <usableCanvasViewport>
      <origin>0 0</origin>
      <size>2560 1320</size>
    </usableCanvasViewport>
    <planViewport>
      <origin>0 0</origin>
      <size>842 1320</size>
    </planViewport>
    <!-- need to consider plan view horizontal offset +4 -->
    <mainViewport>
      <origin>846 0</origin>
      <size>1714 1320</size>
    </mainViewport>
    <viewForMod>
      <origin>0 0</origin>
      <size>832 832</size>
    </viewForMod>
    <driveAssistMain>
      <origin>245 490</origin>
      <size>639 590</size>
    </driveAssistMain>
    <driveAssistLeft>
      <origin>640 0</origin>
      <size>540 720</size>
    </driveAssistLeft>
    <driveAssistRight>
      <origin>1180 0</origin>
      <size>540 720</size>
    </driveAssistRight>
    <dayNightDATripleLeft>
      <origin>884 490</origin>
      <size>4 590</size>
    </dayNightDATripleLeft>
    <dayNightDATripleRight>
      <origin>241 490</origin>
      <size>4 590</size>
    </dayNightDATripleRight>
    <driveAssistDualFrontViewportFL>
      <origin>385 490</origin>
      <size>744 590</size>
    </driveAssistDualFrontViewportFL>
    <driveAssistDualFrontViewportFR>
      <origin>0 490</origin>
      <size>744 590</size>
    </driveAssistDualFrontViewportFR>
    <driveAssistDualLeftViewport>
      <origin>0 490</origin>
      <size>381 590</size>
    </driveAssistDualLeftViewport>
    <driveAssistDualRightViewport>
      <origin>748 490</origin>
      <size>381 590</size>
    </driveAssistDualRightViewport>
    <wideViewport>
      <origin>190 0</origin>
      <size>1570 720</size>
    </wideViewport>
    <dayNightViewport>
      <origin>640 0</origin>
      <size>0 720</size>
    </dayNightViewport>
    <dayNightTADualViewport>
      <origin>1008 490</origin>
      <size>3 590</size>
    </dayNightTADualViewport>
    <dayNightTATripleLeftViewport>
      <origin>665 490</origin>
      <size>4 590</size>
    </dayNightTATripleLeftViewport>
    <dayNightTATripleRightViewport>
      <origin>1350 490</origin>
      <size>4 590</size>
    </dayNightTATripleRightViewport>
    <dayNightDADualLeftViewport>
      <origin>381 490</origin>
      <size>4 590</size>
    </dayNightDADualLeftViewport>
    <dayNightDADualRightViewport>
      <origin>744 490</origin>
      <size>4 590</size>
    </dayNightDADualRightViewport>
    <fusiView>
      <origin>0 0</origin>
      <size>40 10</size>
    </fusiView>
    <fisheyeViewport>
      <origin>0 0</origin>
      <size>1280 720</size>
    </fisheyeViewport>
    <fullScreenViewport>
      <origin>0 0</origin>
      <size>2560 1320</size>
    </fullScreenViewport>
    <frontWheelLeft>
      <origin>846 0</origin>
      <size>852 1320</size>
    </frontWheelLeft>
    <frontWheelRight>
      <origin>1708 0</origin>
      <size>852 1320</size>
    </frontWheelRight>
    <rearWheelLeft>
      <origin>846 0</origin>
      <size>852 1320</size>
    </rearWheelLeft>
    <rearWheelRight>
      <origin>1708 0</origin>
      <size>852 1320</size>
    </rearWheelRight>
    <settingBarViewport>
      <origin>0 0</origin>
      <size>1920 1080</size>
    </settingBarViewport>
    <parkView>
      <origin>0 0</origin>
      <size>1920 720</size>
    </parkView>
    <!-- Vertical mode -->
    <vertMainViewport>
      <origin>0 0</origin>
      <size>464 720</size>
    </vertMainViewport>
    <vertPlanViewport>
      <origin>464 0</origin>
      <size>568 360</size>
    </vertPlanViewport>
    <vertSideViewport>
      <origin>464 364</origin>
      <size>568 356</size>
    </vertSideViewport>
    <vertWheelLeftViewport>
      <origin>0 0</origin>
      <size>464 220</size>
    </vertWheelLeftViewport>
    <vertWheelRightViewport>
      <origin>0 500</origin>
      <size>464 220</size>
    </vertWheelRightViewport>
    <!-- Floating View -->
    <floatPlanViewport>
      <origin>876 0</origin>
      <size>547 858</size>
    </floatPlanViewport>
    <floatMainViewport>
      <origin>1426 0</origin>
      <size>1114 858</size>
    </floatMainViewport>
    <floatWheelLeftViewport>
      <origin>310 0</origin>
      <size>325 540</size>
    </floatWheelLeftViewport>
    <floatWheelRightViewport>
      <origin>635 0</origin>
      <size>325 540</size>
    </floatWheelRightViewport>
    <!-- <floatSettingBarViewport>
      <origin>0 0</origin>
      <size>960 540</size>
    </floatSettingBarViewport> -->
    <!-- DENZA real displayed viewport -->
    <realMainViewportK1A>
      <origin>846 0</origin>
      <size>1714 1320</size>
    </realMainViewportK1A>
    <realMainViewportST>
      <origin>1080 0</origin>
      <size>1480 1320</size>
    </realMainViewportST>
  </Views>
  <CustomRawFisheyeView>
    <viewport>
      <origin>0 490</origin>
      <size>747 590</size>
    </viewport>
  </CustomRawFisheyeView>
  <WheelAnimation>
    <rollSpeedLimit>16.0</rollSpeedLimit>
  </WheelAnimation>
  <OdometryConverter>
    <filter_x>0</filter_x>
    <filter_y>0</filter_y>
    <filter_yaw>0</filter_yaw>
    <yaw_in_degrees>1</yaw_in_degrees>
    <thresholdX>0.35</thresholdX>
    <thresholdY>0.13</thresholdY>
    <thresholdYaw>5.0</thresholdYaw>
  </OdometryConverter>
  <OverlayData>
    <ICE_stallTimeout>10</ICE_stallTimeout>
    <SATCAM_rear_tailgatePosition>0</SATCAM_rear_tailgatePosition>
  </OverlayData>
  <PlanView>
    <widthMeters>7.0</widthMeters>
    <widthMetersParkingVert>8.0</widthMetersParkingVert>
    <widthMetersVeh2dDiff>0.0</widthMetersVeh2dDiff>
  </PlanView>
  <!-- View-specific bowls -->
  <BonnetViewBowl>
    <numRadialSections>64</numRadialSections>
    <numHeightSections>16</numHeightSections>
    <bowlDefault>
        <semiaxis>7.3 4.8</semiaxis>
        <height>7</height>
    </bowlDefault>
  </BonnetViewBowl>
  <BonnetViewFloor>
    <size>10.0 10.0</size>
    <resolution>0.2 0.2</resolution>
  </BonnetViewFloor>
  <DayNightData>
    <dayColor>0.9333 0.9333 0.9333</dayColor>
    <nightColor>0.0824 0.0824 0.0824</nightColor>
  </DayNightData>
  <EngineeringView>
    <mode>2</mode>  <!-- Backchannel Screen ID 3-->
                    <!-- Tow assist eng screen 4-->
  </EngineeringView>
  <BowlShaperDefault> <!-- LARGE BOWL --> <!-- CVS_Bowl_Size_Tall in dcm -->
    <bowlDefault>
        <semiaxis>10.0 8.0</semiaxis>
        <height>7</height>
    </bowlDefault>
  </BowlShaperDefault>
  <BowlShaperMedium> <!-- MEDIUM BOWL, apply for horizontal view mode --> <!-- CVS_Bowl_Size_Mid in dcm -->
    <bowlDefault>
        <semiaxis>6.0 4.0</semiaxis>
        <height>4.0</height>
    </bowlDefault>
  </BowlShaperMedium>
  <BowlShaperSmall> <!-- SMALL BOWL, apply for verical view mode --> <!-- CVS_Bowl_Size_Small in dcm -->
    <bowlDefault>
        <semiaxis>6.0 4.0</semiaxis>
        <height>3.35</height>
    </bowlDefault>
  </BowlShaperSmall>
  <FloorPlateSm>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <speedTrigVehTrans_lower_kph>0</speedTrigVehTrans_lower_kph>
    <speedTrigVehTrans_upper_kph>20</speedTrigVehTrans_upper_kph>
  </FloorPlateSm>
  <TranspSm>
    <transpDurnThreshold_ms>660</transpDurnThreshold_ms>
    <opaqueDurnThreshold_ms>2640</opaqueDurnThreshold_ms>
    <transition2opaqueDuration>3000.0</transition2opaqueDuration>
    <transition2transpDuration>3000.0</transition2transpDuration>
    <transpDurnThreshold_Parking_ms>660</transpDurnThreshold_Parking_ms>
    <opaqueDurnThreshold_Parking_ms>19800</opaqueDurnThreshold_Parking_ms>
    <transition2opaqueDuration_Parking>3000.0</transition2opaqueDuration_Parking>
    <transition2transpDuration_Parking>3000.0</transition2transpDuration_Parking>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <vehicleOpacity>0.7</vehicleOpacity>  <!-- 0.0 is transparency -->  <!-- for vehicle 2D -->
    <wheelOpacity>1.0</wheelOpacity>  <!-- for vehicle 2D -->
  </TranspSm>
  <BlurSm>
    <deblurDurnThreshold_ms>660</deblurDurnThreshold_ms> <!-- duration from baseplate default texture to baseplate transparancy -->
    <blurDurnThreshold_ms>2640</blurDurnThreshold_ms>     <!-- duration from baseplate transparancy to baseplate default texture -->
    <anim2blurDuration>3000.0</anim2blurDuration>
    <anim2normalDuration>3000.0</anim2normalDuration>
    <blurredStateAlpha>0.0</blurredStateAlpha>
    <blurLevelDuringDriving>1.0</blurLevelDuringDriving>  <!-- 1.0 is transparency -->  <!-- for baseplate -->
    <movingTooFastThreshold_Kmh>60.0</movingTooFastThreshold_Kmh>
  </BlurSm>
  <GbcVehicle>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <noSpeedTimeout>5.0</noSpeedTimeout>
    <transpAlpha>0.4</transpAlpha>
    <OpaqueToTransparentAnimationTime>2.0</OpaqueToTransparentAnimationTime>
    <TransparentToOpaqueAnimationTime>2.0</TransparentToOpaqueAnimationTime>
  </GbcVehicle>
  <GbcFloor>
    <minDrivenDistX>6.0</minDrivenDistX>
    <minDrivenDistY>1.5</minDrivenDistY>
    <noSpeedTimeout>5.0</noSpeedTimeout>
    <blurLevel>0.4</blurLevel>
    <BlurredToTexturedAnimationTime>2.0</BlurredToTexturedAnimationTime>
    <TexturedToBlurredAnimationTime>2.0</TexturedToBlurredAnimationTime>
  </GbcFloor>
  <HemisphereCamera>
    <minDistance>2.0</minDistance>
    <maxDistance>8.0</maxDistance>
    <minElevation>0.3</minElevation>     <!-- modify the minElevationHu in huhemidata as well -->
    <maxElevation>1.57</maxElevation>     <!-- modify the maxElevationHu in huhemidata as well -->
    <zoomDistanceInterpolationSpeed>30.0</zoomDistanceInterpolationSpeed>
    <sphericalInterpolationSpeed>200.0</sphericalInterpolationSpeed>
    <centerInterpolationSpeed>10.0</centerInterpolationSpeed>
  </HemisphereCamera>
  <huhemisdata>
    <zoomScale>8</zoomScale>
    <minElevationHu>0.3</minElevationHu>     <!-- keep the same as minElevation in HemisphereCamera -->
    <maxElevationHu>1.57</maxElevationHu>     <!-- keep the same as maxElevation in HemisphereCamera -->
    <zoomfactorHori>0.3</zoomfactorHori>      <!--0: HemisphereCamera maxDistance, 1: HemisphereCamera minDistance-->
    <zoomfactorVert>0.2</zoomfactorVert>      <!--0: HemisphereCamera maxDistance, 1: HemisphereCamera minDistance-->
  </huhemisdata>
  <CarCenter>
    <carCenterHori>0.0 0.0 0.12</carCenterHori>    <!-- x, y is difference w/ vehicle center -->
    <carCenterVert>0.0 0.0 0.02</carCenterVert>    <!-- x, y is difference w/ vehicle center -->
  </CarCenter>
  <PanAndZoom2D>
    <minDistance>4.0</minDistance>
    <maxDistance>15.0</maxDistance>
    <zoomDistanceInterpolationSpeed>30.0</zoomDistanceInterpolationSpeed>
    <positionInterpolationSpeed>200.0</positionInterpolationSpeed>
    <panningAreaMin>-3.0 -2.5</panningAreaMin>
    <panningAreaMax> 3.0  2.5</panningAreaMax>
  </PanAndZoom2D>
  <ParkingConfirmInterface>
    <settingParkAPAConfirm>
      <iconCenter>1160 340</iconCenter>
      <responseArea>148 148</responseArea>
    </settingParkAPAConfirm>
    <settingParkAPAParkOutConfirm>
      <iconCenter>960 360</iconCenter>
      <responseArea>148 148</responseArea>
    </settingParkAPAParkOutConfirm>
    <settingParkAPAParkOutConfirmText>  <!--text indicator -->
      <iconCenter>960 510</iconCenter>
    </settingParkAPAParkOutConfirmText>
    <settingParkAPAConfirmText>  <!--text indicator -->
      <iconCenter>1160 450</iconCenter>
    </settingParkAPAConfirmText>
    <settingParkRPAConfirm>
      <iconCenter>760 340</iconCenter>
      <responseArea>148 148</responseArea>
    </settingParkRPAConfirm>
    <settingParkRPAConfirmText>  <!--text indicator -->
      <iconCenter>760 450</iconCenter>
    </settingParkRPAConfirmText>
    <settingPARKAPADrvReqInd>
      <iconCenter>1160 510</iconCenter>
    </settingPARKAPADrvReqInd>
    <settingPARKRPASketch>
      <iconSize>474 252</iconSize>
      <iconCenter>1160 360</iconCenter>
    </settingPARKRPASketch>
    <settingPARKRPAChecklist>
      <iconSize>331 158</iconSize>
      <iconCenter>750 360</iconCenter>
    </settingPARKRPAChecklist>
    <settingPARKRPADrvReqInd>
      <iconCenter>960 360</iconCenter>
    </settingPARKRPADrvReqInd>
    <settingPARKConfirmTitle>
      <iconSize>232 27</iconSize>
      <iconCenter>960 180</iconCenter>
    </settingPARKConfirmTitle>
    <settingPARKConfirmExit>
      <iconCenter>960 550</iconCenter>
      <responseArea>100 100</responseArea>
    </settingPARKConfirmExit>
    <settingPARKConfirmSeparator>
      <iconCenter>960 520</iconCenter>
    </settingPARKConfirmSeparator>
    <settingPARKRPAConfirmSeparator>
      <iconCenter>960 210</iconCenter>
    </settingPARKRPAConfirmSeparator>
    <settingPARKPopOutBox>
      <iconSize>830 550</iconSize>
      <iconCenter>960 360</iconCenter>
    </settingPARKPopOutBox>
  </ParkingConfirmInterface>
  <ParkingSpotManager>
    <speedLimit>0.001</speedLimit>
    <parkOutCrossSpotYPos>2.5</parkOutCrossSpotYPos>
    <parkOutCrossFrontSpotXPos>6.2</parkOutCrossFrontSpotXPos>
    <parkOutCrossRearSpotXPos>-6.0</parkOutCrossRearSpotXPos>
    <parkOutParaSpotYPos>0.9</parkOutParaSpotYPos>
    <parkOutParaSpotXPos>7.5</parkOutParaSpotXPos>
    <parkingSpotHmiLength>2.4</parkingSpotHmiLength>
    <parkingSpotHmiWidth>1.44</parkingSpotHmiWidth>
    <spotOffsetX>5.0</spotOffsetX>
    <leftfreeParkSpotID>113</leftfreeParkSpotID>
    <rightfreeParkSpotID>114</rightfreeParkSpotID>
    <diagonalSpotLength> 2.5</diagonalSpotLength>
    <diagonalSpotWidth>2.5</diagonalSpotWidth>
    <adjustmentValueOfOverlapDistance> 0.21 </adjustmentValueOfOverlapDistance>
  </ParkingSpotManager>
  <FreeparkingManager>
    <speedLimit>0.001</speedLimit>
    <defaultPosition>1.3 -3.5</defaultPosition>
    <defaultAngle>0.0</defaultAngle>
    <translation_step>0.012</translation_step>
    <rotation_step>2.0</rotation_step>
    <widthOffset>0.6</widthOffset> <!--default for parallel slot-->
    <lengthOffset>1.5</lengthOffset> <!--default for parallel slot-->
    <horizontalPad_default>1</horizontalPad_default>
    <fpParkRange_Parallel_X_low_Limit>-2.0</fpParkRange_Parallel_X_low_Limit>
    <fpParkRange_Parallel_X_upper_Limit>5.5</fpParkRange_Parallel_X_upper_Limit>
    <fpParkRange_Parallel_Y_low_Limit>0.6</fpParkRange_Parallel_Y_low_Limit>  <!-- because of symmtric, only take the left side-->
    <fpParkRange_Parallel_Y_upper_Limit>2.1</fpParkRange_Parallel_Y_upper_Limit>  <!-- because of symmtric, only take the left side-->
    <fpParkRange_Cross_X_low_Limit>-2.0</fpParkRange_Cross_X_low_Limit>
    <fpParkRange_Cross_X_upper_Limit>5.5</fpParkRange_Cross_X_upper_Limit>
    <fpParkRange_Cross_Y_low_Limit>0.6</fpParkRange_Cross_Y_low_Limit>
    <fpParkRange_Cross_Y_upper_Limit>2.1</fpParkRange_Cross_Y_upper_Limit>
    <fpParkRange_Diagonal_X_low_Limit>-2.0</fpParkRange_Diagonal_X_low_Limit>
    <fpParkRange_Diagonal_X_upper_Limit>5.5</fpParkRange_Diagonal_X_upper_Limit>
    <fpParkRange_Diagonal_Y_low_Limit>0.6</fpParkRange_Diagonal_Y_low_Limit>
    <fpParkRange_Diagonal_Y_upper_Limit>2.1</fpParkRange_Diagonal_Y_upper_Limit>
    <freeparkingSlotWidth>2.9</freeparkingSlotWidth> <!-- actual width of freeparking slot in meter -->
    <imageProperties>
      <slotWidthPixel>200.0</slotWidthPixel>
      <slotHeightPixel>420.0</slotHeightPixel>
      <!-- <rotateIconWidthPixel>37.0</rotateIconWidthPixel>
      <rotateIconHeightPixel>37.0</rotateIconHeightPixel> -->
      <rotateIconWidthExtendPixel>11.0</rotateIconWidthExtendPixel>
      <rotateIconHeightExtendPixel>11.0</rotateIconHeightExtendPixel>
      <!-- <imageWidthPixel>222.0</imageWidthPixel>
      <imageHeightPixel>444.0</imageHeightPixel> -->
    </imageProperties>
  </FreeparkingManager>
  <Freeparking>
    <filenameTexture>cc/resources/freeparking.png</filenameTexture>
  </Freeparking>
  <AugmentedViewTransition>
    <backgroundTextureFilename>cc/vehicle_model/ui/131_black_pixel.png</backgroundTextureFilename>
    <waveTextureFilename>cc/vehicle_model/ui/132_waveTexture.png</waveTextureFilename>
    <transitionWaveAnimationDuration>0.5</transitionWaveAnimationDuration>
    <transitionWaveMaxRadius>6.0</transitionWaveMaxRadius>
    <transitionWaveWidth>0.4</transitionWaveWidth>
    <scanWaveAnimationDuration>6.0</scanWaveAnimationDuration>
    <scanWaveMaxRadius>3.5</scanWaveMaxRadius>
    <scanWaveWidth>0.30</scanWaveWidth>  <!-- HC:0.1; MR:0.3; -->
    <scanWaveBurstSize>1.0</scanWaveBurstSize>
    <scanWaveInterBurstTime>0.75</scanWaveInterBurstTime>
    <scanWaveIntraBurstTime>0.5</scanWaveIntraBurstTime>
    <scanWaveTravelStartingPoint>0.5</scanWaveTravelStartingPoint>  <!-- percentage of travel distance-->
  </AugmentedViewTransition>
  <UIElements>
    <camSvFr>
      <iconSize>48 48</iconSize>
      <iconCenter>430 64</iconCenter>
      <responseArea>76 76</responseArea>
    </camSvFr>
    <camSvRe>
      <iconSize>48 48</iconSize>
      <iconCenter>430 644</iconCenter>
      <responseArea>76 76</responseArea>
    </camSvRe>
    <camFwLe>
      <iconSize>48 48</iconSize>
      <iconCenter>300 250</iconCenter>
      <responseArea>76 76</responseArea>
    </camFwLe>
    <camFwRi>
      <iconSize>48 48</iconSize>
      <iconCenter>560 250</iconCenter>
      <responseArea>76 76</responseArea>
    </camFwRi>
    <camRwLe>
      <iconSize>48 48</iconSize>
      <iconCenter>300 480</iconCenter>
      <responseArea>76 76</responseArea>
    </camRwLe>
    <camRwRi>
      <iconSize>48 48</iconSize>
      <iconCenter>560 480</iconCenter>
      <responseArea>76 76</responseArea>
    </camRwRi>
    <camPvFL>
      <iconSize>48 48</iconSize>
      <iconCenter>277 109</iconCenter>
      <responseArea>76 76</responseArea>
    </camPvFL>
    <camPvFR>
      <iconSize>48 48</iconSize>
      <iconCenter>583 109</iconCenter>
      <responseArea>76 76</responseArea>
    </camPvFR>
    <camPvRL>
      <iconSize>48 48</iconSize>
      <iconCenter>277 614</iconCenter>
      <responseArea>76 76</responseArea>
    </camPvRL>
    <camPvRR>
      <iconSize>48 48</iconSize>
      <iconCenter>583 614</iconCenter>
      <responseArea>76 76</responseArea>
    </camPvRR>
    <cpcOverlaySwitchPressMR>
      <iconCenter>1820 890</iconCenter>
      <responseArea>200 200</responseArea>
    </cpcOverlaySwitchPressMR>
    <cpcOverlaySwitchPressST>
      <iconCenter>2360 1120</iconCenter>
      <responseArea>400 400</responseArea>
    </cpcOverlaySwitchPressST>
    <swVersionShowSwitchBMR>
      <iconCenter>710 100</iconCenter>
      <responseArea>200 200</responseArea>
    </swVersionShowSwitchBMR>
    <swVersionShowSwitchCMR>
      <iconCenter>1820 100</iconCenter>
      <responseArea>200 200</responseArea>
    </swVersionShowSwitchCMR>
    <swVersionShowSwitchBST>
      <iconCenter>880 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchBST>
    <swVersionShowSwitchCST>
      <iconCenter>2360 200</iconCenter>
      <responseArea>400 400</responseArea>
    </swVersionShowSwitchCST>
    <settingQuit>
      <iconSize>53 27</iconSize>
      <iconCenter>79 68</iconCenter>
      <responseArea>80 80</responseArea>
    </settingQuit>
    <settingPPDIS>
      <iconSize>100 100</iconSize>
      <iconCenter>79 171</iconCenter>
      <responseArea>76 76</responseArea>
    </settingPPDIS>
    <settingPPON>
      <iconSize>100 100</iconSize>
      <iconCenter>79 171</iconCenter>
      <responseArea>76 76</responseArea>
    </settingPPON>
    <settingAutoCamActiv>
      <iconSize>76 76</iconSize>
      <iconCenter>79 288</iconCenter>
      <responseArea>76 76</responseArea>
    </settingAutoCamActiv>
    <settingVM2D>
      <iconCenter>910 50</iconCenter>
      <responseArea>150 100</responseArea>
    </settingVM2D>
    <settingVM3D>
      <iconCenter>1110 50</iconCenter>
      <responseArea>150 100</responseArea>
    </settingVM3D>
    <settingOffRoad>
      <iconSize>202 100</iconSize>
      <iconCenter>1410 50</iconCenter>
      <responseArea>200 100</responseArea>
    </settingOffRoad>
    <settingWheel>
      <iconSize>202 100</iconSize>
      <iconCenter>1710 50</iconCenter>
      <responseArea>200 100</responseArea>
    </settingWheel>
    <settingWheelLine>
      <iconSize>480 500</iconSize>
      <iconCenter>1350 370</iconCenter>
    </settingWheelLine>
    <settingVM>
      <iconSize>400 100</iconSize>
      <iconCenter>1010 50</iconCenter>
    </settingVM>
    <settingBottomBar>
      <iconSize>1920 90</iconSize>
      <iconCenter>960 1035</iconCenter>
    </settingBottomBar>
    <settingApaInactive>
      <iconCenter>111 98</iconCenter>
      <responseArea>110 180</responseArea>
    </settingApaInactive>
    <settingApaActive>
      <iconCenter>111 295</iconCenter>
      <responseArea>110 180</responseArea>
    </settingApaActive>
    <settingApa>
      <iconSize>222 161</iconSize>
      <iconCenter>111 80.5</iconCenter>
    </settingApa>
    <warnSymbolUssWhole>
      <iconSize>426 28</iconSize>
      <iconCenter>216 42</iconCenter>
    </warnSymbolUssWhole>
    <warnSymbolUssVert>
      <iconSize>41 41</iconSize>
      <iconCenter>760 540</iconCenter>
    </warnSymbolUssVert>
    <vehicleTransIcon>
      <iconSize>135 295</iconSize>
      <iconCenter>516 585</iconCenter>
    </vehicleTransIcon>
    <vehicleTransIconParkActive>
      <iconSize>144 310</iconSize>
      <iconCenter>214 350</iconCenter>
    </vehicleTransIconParkActive>
    <vehicleTransIconVert>
      <iconSize>235 110</iconSize>
      <iconCenter>741 540</iconCenter>
    </vehicleTransIconVert>
    <settingTextBoxSafeNotification>
      <iconSize>220 29</iconSize>
      <iconCenter>855 685</iconCenter>
    </settingTextBoxSafeNotification>
    <settingTextBoxSafeNotification_vert>
      <iconSize>27 190</iconSize>
      <iconCenter>20 360</iconCenter>
    </settingTextBoxSafeNotification_vert>
    <settingParkingPlanIconLeft>
      <iconSize>76 64</iconSize>
      <iconCenter>644 685</iconCenter>
    </settingParkingPlanIconLeft>
    <settingParkingPlanIconRight>
      <iconSize>76 64</iconSize>
      <iconCenter>1068 685</iconCenter>
    </settingParkingPlanIconRight>
    <settingParkingPlanIconLeft_vert>
      <iconSize>46 46</iconSize>
      <iconCenter>435 540</iconCenter>
    </settingParkingPlanIconLeft_vert>
    <settingParkingPlanIconRight_vert>
      <iconSize>46 46</iconSize>
      <iconCenter>435 180</iconCenter>
    </settingParkingPlanIconRight_vert>
    <settingPARKIcon>
      <iconSize>148 148</iconSize>
      <iconCenter>840 80</iconCenter>
      <responseArea>148 148</responseArea>
    </settingPARKIcon>
    <settingPARKModeSelected>
      <iconSize>420 540</iconSize>
    </settingPARKModeSelected>
    <settingTextModeSelect>
      <iconSize>318 32</iconSize>
      <iconCenter>960 68</iconCenter>
    </settingTextModeSelect>
    <settingPARKAutoPic>
      <iconSize>121 264</iconSize>
      <iconCenter>216 320</iconCenter>
    </settingPARKAutoPic>
    <settingTextBoxSlotSearching_Hori>
      <iconSize>432 38</iconSize>
      <iconCenter>216 125</iconCenter>
    </settingTextBoxSlotSearching_Hori>
    <settingTextBoxSlotSearching_Vert>
      <iconCenter>107 360</iconCenter>
    </settingTextBoxSlotSearching_Vert>
    <settingParkingUIBackground_Hori>
      <iconSize>440 740</iconSize>
      <iconCenter>220 370</iconCenter>  <!-- HC:216,360; MR:220,370; -->
    </settingParkingUIBackground_Hori>
    <settingParkingUIBackground_Vert>
      <iconSize>568 720</iconSize>
      <iconCenter>284 360</iconCenter>
    </settingParkingUIBackground_Vert>
    <settingSmallParkAutoPic>
      <iconSize>58 127</iconSize>
      <iconCenter>216 213</iconCenter>
    </settingSmallParkAutoPic>
    <settingCrossVehiclePic>
      <iconCenter>216 350</iconCenter>
    </settingCrossVehiclePic>
    <settingSmallSlotAutoPic_Hori>
      <iconSize>58 127</iconSize>
      <iconCenter>216 244</iconCenter>
    </settingSmallSlotAutoPic_Hori>
    <settingSmallSlotAutoPic_Vert>
      <iconSize>58 127</iconSize>
      <iconCenter>211 360</iconCenter>
    </settingSmallSlotAutoPic_Vert>
    <settingSmallParkAutoPic_Hori>
      <iconSize>58 127</iconSize>
      <iconCenter>216 240</iconCenter>
    </settingSmallParkAutoPic_Hori>
    <settingSmallParkAutoPic_Vert>
      <iconCenter>183 360</iconCenter>
    </settingSmallParkAutoPic_Vert>
    <settingSmallAutoPicPara_Hori>
      <iconCenter>216 244</iconCenter>
    </settingSmallAutoPicPara_Hori>
    <settingSmallAutoPicPara_Vert>
      <iconCenter>210 360</iconCenter>
    </settingSmallAutoPicPara_Vert>
    <settingSmallAutoPicVertFrontInLeft_Hori>
      <iconCenter>220 483</iconCenter>
    </settingSmallAutoPicVertFrontInLeft_Hori>
    <settingSmallAutoPicVertFrontInLeft_Vert>
      <iconCenter>392 360</iconCenter>
    </settingSmallAutoPicVertFrontInLeft_Vert>
    <settingSmallAutoPicVertFrontInRight_Hori>
      <iconCenter>212 483</iconCenter>
    </settingSmallAutoPicVertFrontInRight_Hori>
    <settingSmallAutoPicVertFrontInRight_Vert>
      <iconCenter>392 360</iconCenter>
    </settingSmallAutoPicVertFrontInRight_Vert>
    <settingSmallAutoPicVertRearInLeft_Hori>
      <iconCenter>220 286</iconCenter>
    </settingSmallAutoPicVertRearInLeft_Hori>
    <settingSmallAutoPicVertRearInLeft_Vert>
      <iconCenter>210 360</iconCenter>
    </settingSmallAutoPicVertRearInLeft_Vert>
    <settingSmallAutoPicVertRearInRight_Hori>
      <iconCenter>212 286</iconCenter>
    </settingSmallAutoPicVertRearInRight_Hori>
    <settingSmallAutoPicVertRearInRight_Vert>
      <iconCenter>210 360</iconCenter>
    </settingSmallAutoPicVertRearInRight_Vert>
    <settingSmallParkAutoPicHoriCompletedParkOut>
      <iconSize>58 127</iconSize>
      <iconCenter>218 322</iconCenter>
    </settingSmallParkAutoPicHoriCompletedParkOut>
    <settingStarkParkButton_Hori>
      <iconSize>134 49</iconSize>
      <iconCenter>216 656</iconCenter>
      <responseArea>134 49</responseArea>
    </settingStarkParkButton_Hori>
    <settingGuidanceGearD>
      <iconSize>78 78</iconSize>
      <iconCenter>216 53</iconCenter>
    </settingGuidanceGearD>
    <settingSuspendQuitButton_Hori>
      <iconSize>134 49</iconSize>
      <iconCenter>301 656</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendQuitButton_Hori>
    <settingSuspendQuitButton_Vert>
      <iconSize>134 49</iconSize>
      <iconCenter>516 445</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendQuitButton_Vert>
    <settingGuideHorParallel>
      <iconSize>113 114</iconSize>
      <iconCenter>263 329</iconCenter>
    </settingGuideHorParallel>
    <settingSuspendContinueButton_Hori>
      <iconSize>134 49</iconSize>
      <iconCenter>131 656</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendContinueButton_Hori>
    <settingSuspendContinueButton_Vert>
      <iconSize>134 49</iconSize>
      <iconCenter>516 275</iconCenter>
      <responseArea>134 49</responseArea>
    </settingSuspendContinueButton_Vert>
    <settingPARKSearchingRoad>
      <iconSize>1255 850</iconSize>
      <iconCenter>960 360</iconCenter>
    </settingPARKSearchingRoad>
    <settingPARKSearchingWave1>
      <iconSize>402 284</iconSize>
      <iconCenter>960 520</iconCenter>
    </settingPARKSearchingWave1>
    <settingPARKSearchingWave2>
      <iconSize>278 177</iconSize>
      <iconCenter>960 520</iconCenter>
    </settingPARKSearchingWave2>
    <settingPARKParaSlotValid_Hori>
      <iconSize>92 186</iconSize>
      <responseArea>92 186</responseArea>
    </settingPARKParaSlotValid_Hori>
    <settingPARKVertSlotValid_Hori>
      <iconSize>186 92</iconSize>
      <responseArea>186 92</responseArea>
    </settingPARKVertSlotValid_Hori>
    <settingPARKDiagSlotValid_Hori>
      <iconSize>190 95</iconSize>
      <responseArea>190 95</responseArea>
    </settingPARKDiagSlotValid_Hori>
    <settingPARKParaSlotValid_Vert>
      <iconSize>83 168</iconSize>
      <responseArea>83 168</responseArea>
    </settingPARKParaSlotValid_Vert>
    <settingPARKVertSlotValid_Vert>
      <iconSize>168 83</iconSize>
      <responseArea>168 83 </responseArea>
    </settingPARKVertSlotValid_Vert>
    <settingPARKDiagSlotValid_Vert>
      <iconSize>171 86</iconSize>
      <responseArea>171 86</responseArea>
    </settingPARKDiagSlotValid_Vert>
    <!-- Vertical Slots UI-->
    <settingHoriSearchingVerticalSlot1L>
      <iconCenter>91 300</iconCenter>
    </settingHoriSearchingVerticalSlot1L>
    <settingHoriSearchingVerticalSlot2L>
      <iconCenter>91 384</iconCenter>
    </settingHoriSearchingVerticalSlot2L>
    <settingHoriSearchingVerticalSlot3L>
      <iconCenter>91 468</iconCenter>
    </settingHoriSearchingVerticalSlot3L>
    <settingHoriSearchingVerticalSlot4L>
      <iconCenter>91 552</iconCenter>
    </settingHoriSearchingVerticalSlot4L>
    <settingHoriSearchingVerticalSlot1R>
      <iconCenter>341 300</iconCenter>
    </settingHoriSearchingVerticalSlot1R>
    <settingHoriSearchingVerticalSlot2R>
      <iconCenter>341 384</iconCenter>
    </settingHoriSearchingVerticalSlot2R>
    <settingHoriSearchingVerticalSlot3R>
      <iconCenter>341 468</iconCenter>
    </settingHoriSearchingVerticalSlot3R>
    <settingHoriSearchingVerticalSlot4R>
      <iconCenter>341 552</iconCenter>
    </settingHoriSearchingVerticalSlot4R>
    <!-- Parallel Slots UI-->
    <settingHoriSearchingParallelSlot1L>
      <iconCenter>64 253</iconCenter>
    </settingHoriSearchingParallelSlot1L>
    <settingHoriSearchingParallelSlot2L>
      <iconCenter>64 399</iconCenter>
    </settingHoriSearchingParallelSlot2L>
    <settingHoriSearchingParallelSlot3L>
      <iconCenter>64 545</iconCenter>
    </settingHoriSearchingParallelSlot3L>
    <settingHoriSearchingParallelSlot1R>
      <iconCenter>368 253</iconCenter>
    </settingHoriSearchingParallelSlot1R>
    <settingHoriSearchingParallelSlot2R>
      <iconCenter>368 399</iconCenter>
    </settingHoriSearchingParallelSlot2R>
    <settingHoriSearchingParallelSlot3R>
      <iconCenter>368 545</iconCenter>
    </settingHoriSearchingParallelSlot3R>
    <settingHoriSelectedSlotVertRightRearIn_Hori>
      <iconCenter>336 469</iconCenter>
    </settingHoriSelectedSlotVertRightRearIn_Hori>
    <settingHoriSelectedSlotVertRightFrontIn_Hori>
      <iconCenter>336 301</iconCenter>
    </settingHoriSelectedSlotVertRightFrontIn_Hori>
    <settingHoriSelectedSlotVertLeftRearIn_Hori>
      <iconCenter>96 469</iconCenter>
    </settingHoriSelectedSlotVertLeftRearIn_Hori>
    <settingHoriSelectedSlotVertLeftFrontIn_Hori>
      <iconCenter>96 301</iconCenter>
    </settingHoriSelectedSlotVertLeftFrontIn_Hori>
    <settingHoriSelectedSlotParaRight_Hori>
      <iconCenter>360 400</iconCenter>
    </settingHoriSelectedSlotParaRight_Hori>
    <settingHoriSelectedSlotParaLeft_Hori>
      <iconCenter>72 400</iconCenter>
    </settingHoriSelectedSlotParaLeft_Hori>
    <settingHoriSelectedSlotVertRightRearIn_Vert>
      <iconCenter>372 471</iconCenter>
    </settingHoriSelectedSlotVertRightRearIn_Vert>
    <settingHoriSelectedSlotVertRightFrontIn_Vert>
      <iconCenter>231 471</iconCenter>
    </settingHoriSelectedSlotVertRightFrontIn_Vert>
    <settingHoriSelectedSlotVertLeftRearIn_Vert>
      <iconCenter>372 248</iconCenter>
    </settingHoriSelectedSlotVertLeftRearIn_Vert>
    <settingHoriSelectedSlotVertLeftFrontIn_Vert>
      <iconCenter>231 248</iconCenter>
    </settingHoriSelectedSlotVertLeftFrontIn_Vert>
    <settingHoriSelectedSlotParaRight_Vert>
      <iconCenter>377 490</iconCenter>
    </settingHoriSelectedSlotParaRight_Vert>
    <settingHoriSelectedSlotParaLeft_Vert>
      <iconCenter>377 230</iconCenter>
    </settingHoriSelectedSlotParaLeft_Vert>
    <settingHoriCombinationDiagRight_Hori>
      <iconCenter>302 336</iconCenter>
    </settingHoriCombinationDiagRight_Hori>
    <settingHoriCombinationDiagLeft_Hori>
      <iconCenter>130 336</iconCenter>
    </settingHoriCombinationDiagLeft_Hori>
    <settingHoriCombinationDiagRight_Vert>
      <iconCenter>292 438</iconCenter>
    </settingHoriCombinationDiagRight_Vert>
    <settingHoriCombinationDiagLeft_Vert>
      <iconCenter>292 282</iconCenter>
    </settingHoriCombinationDiagLeft_Vert>
    <settingHoriCompleteCombinationDiagRight_Hori>
      <iconCenter>334 383</iconCenter>
    </settingHoriCompleteCombinationDiagRight_Hori>
    <settingHoriCompleteCombinationDiagLeft_Hori>
      <iconCenter>98 383</iconCenter>
    </settingHoriCompleteCombinationDiagLeft_Hori>
    <settingHoriCompleteCombinationDiagRight_Vert>
      <iconCenter>345 478</iconCenter>
    </settingHoriCompleteCombinationDiagRight_Vert>
    <settingHoriCompleteCombinationDiagLeft_Vert>
      <iconCenter>345 242</iconCenter>
    </settingHoriCompleteCombinationDiagLeft_Vert>
    <settingParkOutSmallAuto_Hori>
      <iconCenter>216 335</iconCenter>
    </settingParkOutSmallAuto_Hori>
    <settingParkOutSmallAuto_Vert>
      <iconCenter>302 360</iconCenter>
    </settingParkOutSmallAuto_Vert>
    <settingParkOutGuidanceCombinationParaRight_Hori>
      <iconCenter>220 400</iconCenter>
    </settingParkOutGuidanceCombinationParaRight_Hori>
    <settingParkOutGuidanceCombinationParaRight_Vert>
      <iconCenter>287 422</iconCenter>
    </settingParkOutGuidanceCombinationParaRight_Vert>
    <settingParkOutGuidanceCombinationParaLeft_Hori>
      <iconCenter>220 400</iconCenter>
    </settingParkOutGuidanceCombinationParaLeft_Hori>
    <settingParkOutGuidanceCombinationParaLeft_Vert>
      <iconCenter>287 298</iconCenter>
    </settingParkOutGuidanceCombinationParaLeft_Vert>
    <settingHoriParkOutSlotParaRight_Hori>
      <iconCenter>220 400</iconCenter>
    </settingHoriParkOutSlotParaRight_Hori>
    <settingHoriParkOutSlotParaRight_Vert>
      <iconCenter>364 500</iconCenter>
    </settingHoriParkOutSlotParaRight_Vert>
    <settingParkOutSlotParaLeft_Hori>
      <iconCenter>220 400</iconCenter>
    </settingParkOutSlotParaLeft_Hori>
    <settingParkOutSlotParaLeft_Vert>
      <iconCenter>364 220</iconCenter>
    </settingParkOutSlotParaLeft_Vert>
    <settingHoriGuidelineVertRightRearIn_Hori>
      <iconCenter>216 411</iconCenter>  <!--HC:236; MR:216 -->
    </settingHoriGuidelineVertRightRearIn_Hori>
    <settingHoriGuidelineVertRightFrontIn_Hori>
      <iconCenter>236 358</iconCenter>
    </settingHoriGuidelineVertRightFrontIn_Hori>
    <settingHoriGuidelineVertLeftRearIn_Hori>
      <iconCenter>196 411</iconCenter>
    </settingHoriGuidelineVertLeftRearIn_Hori>
    <settingHoriGuidelineVertLeftFrontIn_Hori>
      <iconCenter>196 358</iconCenter>
    </settingHoriGuidelineVertLeftFrontIn_Hori>
    <settingHoriGuidelineParaRight_Hori>
      <iconCenter>267 334</iconCenter>
    </settingHoriGuidelineParaRight_Hori>
    <settingHoriGuidelineParaLeft_Hori>
      <iconCenter>165 334</iconCenter>
    </settingHoriGuidelineParaLeft_Hori>
    <settingHoriGuidelineVertRightRearIn_Vert>
      <iconCenter>322 381</iconCenter>
    </settingHoriGuidelineVertRightRearIn_Vert>
    <settingHoriGuidelineVertRightFrontIn_Vert>
      <iconCenter>280 381</iconCenter>
    </settingHoriGuidelineVertRightFrontIn_Vert>
    <settingHoriGuidelineVertLeftRearIn_Vert>
      <iconCenter>322 338</iconCenter>
    </settingHoriGuidelineVertLeftRearIn_Vert>
    <settingHoriGuidelineVertLeftFrontIn_Vert>
      <iconCenter>280 338</iconCenter>
    </settingHoriGuidelineVertLeftFrontIn_Vert>
    <settingHoriGuidelineParaRight_Vert>
      <iconCenter>322 405</iconCenter>
    </settingHoriGuidelineParaRight_Vert>
    <settingHoriGuidelineParaLeft_Vert>
      <iconCenter>322 314</iconCenter>
    </settingHoriGuidelineParaLeft_Vert>
    <settingPARKParaSlotPos1L>
      <iconCenter>315 342</iconCenter>
    </settingPARKParaSlotPos1L>
    <settingPARKParaSlotPos2L>
      <iconCenter>315 539</iconCenter>
    </settingPARKParaSlotPos2L>
    <settingPARKParaSlotPos3L>
      <iconCenter>315 738</iconCenter>
    </settingPARKParaSlotPos3L>
    <settingPARKParaSlotPos1R>
      <iconCenter>724 342</iconCenter>
    </settingPARKParaSlotPos1R>
    <settingPARKParaSlotPos2R>
      <iconCenter>724 539</iconCenter>
    </settingPARKParaSlotPos2R>
    <settingPARKParaSlotPos3R>
      <iconCenter>724 738</iconCenter>
    </settingPARKParaSlotPos3R>
    <settingPARKSlotPos1L>
      <iconCenter>351 406</iconCenter>
    </settingPARKSlotPos1L>
    <settingPARKSlotPos1R>
      <iconCenter>686 406</iconCenter>
    </settingPARKSlotPos1R>
    <settingPARKSlotPos2L>
      <iconCenter>351 519</iconCenter>
    </settingPARKSlotPos2L>
    <settingPARKSlotPos2R>
      <iconCenter>686 519</iconCenter>
    </settingPARKSlotPos2R>
    <settingPARKSlotPos3L>
      <iconCenter>351 632</iconCenter>
    </settingPARKSlotPos3L>
    <settingPARKSlotPos3R>
      <iconCenter>686 632</iconCenter>
    </settingPARKSlotPos3R>
    <settingPARKSlotPos4L>
      <iconCenter>351 745</iconCenter>
    </settingPARKSlotPos4L>
    <settingPARKSlotPos4R>
      <iconCenter>686 745</iconCenter>
    </settingPARKSlotPos4R>
    <settingPARKBackground>
      <iconSize>1920 720</iconSize>
      <iconCenter>960 360</iconCenter>
    </settingPARKBackground>
    <!-- <settingPARKRPAGuidance>
      <iconCenter>960 360</iconCenter>
    </settingPARKRPAGuidance> -->
    <settingParkFrontIsClearSteeringWheel>
			<iconSize>274 218</iconSize>
			<iconCenter>215 303</iconCenter>
    </settingParkFrontIsClearSteeringWheel>
    <settingParkFrontIsClearVehicleAvailable>
			<iconCenter>213 526</iconCenter>
    </settingParkFrontIsClearVehicleAvailable>
    <settingPARKFinished>
      <iconSize>659 506</iconSize>
      <iconCenter>1220 500</iconCenter>
    </settingPARKFinished>
    <settingTextBox>
      <iconSize>680 115</iconSize>
      <iconCenter>1220 80</iconCenter>
    </settingTextBox>
    <settingParkingUICenterIcon_Hori>
      <iconCenter>220 400</iconCenter>
    </settingParkingUICenterIcon_Hori>
    <settingParkingUICenterIcon_Vert>
      <iconCenter>318 360</iconCenter>
    </settingParkingUICenterIcon_Vert>
    <settingParkingUIGearIcon_Hori>
      <iconCenter>129 113</iconCenter>
    </settingParkingUIGearIcon_Hori>
    <settingParkingUIGearIcon_MainView_Hori>
      <iconSize>39 39</iconSize>
      <iconCenter>853 30</iconCenter>
    </settingParkingUIGearIcon_MainView_Hori>
    <settingParkingUIGearIcon_Vert>
      <iconCenter>44 360</iconCenter>
    </settingParkingUIGearIcon_Vert>
    <settingParkingUISeachingArrowIcon_Hori>
      <iconCenter>216 358</iconCenter>
    </settingParkingUISeachingArrowIcon_Hori>
    <settingParkingUISeachingArrowIcon_Vert>
      <iconCenter>315 360</iconCenter>
    </settingParkingUISeachingArrowIcon_Vert>
    <settingParkingUISeachingWaveIcon_Hori>
      <iconCenter>216 360</iconCenter>
    </settingParkingUISeachingWaveIcon_Hori>
    <settingParkingUISeachingWaveIcon_Vert>
      <iconCenter>326 360</iconCenter>
    </settingParkingUISeachingWaveIcon_Vert>
    <settingParkingUIContinueDrivingTextUIPanel>
      <iconSize>160 44</iconSize>
      <iconCenter>242 113</iconCenter>
    </settingParkingUIContinueDrivingTextUIPanel>
    <settingParkingUIContinueDrivingTextMainView>
      <iconSize>160 44</iconSize>
      <iconCenter>1000 30</iconCenter>
    </settingParkingUIContinueDrivingTextMainView>
    <settingParkingUIContinueDrivingDistance>
      <iconCenter>273 113</iconCenter>
    </settingParkingUIContinueDrivingDistance>
    <settingParkingUIMovesLeftNumberTextUIPanel>
      <iconSize>160 44</iconSize>
      <iconCenter>242 69</iconCenter>
    </settingParkingUIMovesLeftNumberTextUIPanel>
    <settingParkingUIMovesLeftNumberTextMainView>
      <iconSize>160 44</iconSize>
      <iconCenter>709 30</iconCenter>
    </settingParkingUIMovesLeftNumberTextMainView>
    <settingParkingUISeachingVehicleWithDoors>
      <iconCenter>214 365</iconCenter>
    </settingParkingUISeachingVehicleWithDoors>
    <settingParkingUISuspendIcon>
      <iconCenter>214 114</iconCenter>
    </settingParkingUISuspendIcon>
    <settingParkingUISeachingAutoIcon_Hori>
      <iconCenter>216 445</iconCenter>
    </settingParkingUISeachingAutoIcon_Hori>
    <settingParkingUISeachingAutoIcon_Vert>
      <iconCenter>395 360</iconCenter>
    </settingParkingUISeachingAutoIcon_Vert>
    <settingParkingUITopTextWhite_Hori>
      <iconCenter>215 114</iconCenter>
    </settingParkingUITopTextWhite_Hori>
    <settingParkingUITopTextWhite_Vert>
      <iconCenter>107 360</iconCenter>
    </settingParkingUITopTextWhite_Vert>
    <settingParkingUITopTextBlue_Hori>
      <iconCenter>216 157</iconCenter>
    </settingParkingUITopTextBlue_Hori>
    <settingParkingUITopTextBlue_Vert>
      <iconCenter>138 360</iconCenter>
    </settingParkingUITopTextBlue_Vert>
    <settingQuitIn30SecondText_Hori>
      <iconCenter>214 596</iconCenter>
    </settingQuitIn30SecondText_Hori>
    <settingQuitIn30SecondText_Vert>
      <iconCenter>468 360</iconCenter>
    </settingQuitIn30SecondText_Vert>
    <settingParkingUIFunctionButton_Hori>
      <iconCenter>215 49</iconCenter>
    </settingParkingUIFunctionButton_Hori>
    <settingParkingUIFunctionButton_Vert>
      <iconCenter>42 360</iconCenter>
    </settingParkingUIFunctionButton_Vert>
    <settingParkingStartPauseConfirmButton_Hori>
      <iconCenter>214 664</iconCenter>
    </settingParkingStartPauseConfirmButton_Hori>
    <settingParkingStartPauseConfirmButton_Vert>
      <iconCenter>516 360</iconCenter>
    </settingParkingStartPauseConfirmButton_Vert>
    <settingParkinContinueButton_Hori>
      <iconCenter>114 665</iconCenter>
    </settingParkinContinueButton_Hori>
    <settingParkinContinueButton_Vert>
      <iconCenter>516 284</iconCenter>
    </settingParkinContinueButton_Vert>
    <settingParkinQuitButton_Hori>
      <iconCenter>315 665</iconCenter>
    </settingParkinQuitButton_Hori>
    <settingParkinQuitButton_Vert>
      <iconCenter>516 436</iconCenter>
    </settingParkinQuitButton_Vert>
    <settingParkingUIParkOutSideParallelLeftButton_Hori>  <!-- offset cross to up: -60, parallel to down: +80 -->
			<iconCenter>82 305</iconCenter>
    </settingParkingUIParkOutSideParallelLeftButton_Hori>
    <settingParkingUIParkOutSideParallelLeftButton_Vert>
      <iconCenter>160 225</iconCenter>
    </settingParkingUIParkOutSideParallelLeftButton_Vert>
    <settingParkingUIParkOutSideParallelRightButton_Hori>
			<iconCenter>349 305</iconCenter>
    </settingParkingUIParkOutSideParallelRightButton_Hori>
    <settingParkingUIParkOutSideParallelRightButton_Vert>
      <iconCenter>160 495</iconCenter>
    </settingParkingUIParkOutSideParallelRightButton_Vert>
    <settingParkingUIParkOutSideCrossLeftButton_Hori>   <!-- offset cross to up: -60, parallel to down: +80 -->
			<iconCenter>82 165</iconCenter>
    </settingParkingUIParkOutSideCrossLeftButton_Hori>
    <settingParkingUIParkOutSideCrossLeftButton_Vert>
      <iconCenter>160 225</iconCenter>
    </settingParkingUIParkOutSideCrossLeftButton_Vert>
    <settingParkingUIParkOutSideCrossRightButton_Hori>
			<iconCenter>349 165</iconCenter>
    </settingParkingUIParkOutSideCrossRightButton_Hori>
    <settingParkingUIParkOutSideCrossRightButton_Vert>
      <iconCenter>160 495</iconCenter>
    </settingParkingUIParkOutSideCrossRightButton_Vert>
    <settingParkingUIParkOutSideVehicle_Hori>
			<iconCenter>216 385</iconCenter>
    </settingParkingUIParkOutSideVehicle_Hori>
    <settingParkingUIParkOutSideVehicle_Vert>
      <iconCenter>298 360</iconCenter>
    </settingParkingUIParkOutSideVehicle_Vert>
    <settingParkingUIParkOutSideSteeringWheel_Hori>
			<iconCenter>209 581</iconCenter>
    </settingParkingUIParkOutSideSteeringWheel_Hori>
    <settingParkingUIParkOutSideSteeringWheel_Vert>
      <iconCenter>475 360</iconCenter>
    </settingParkingUIParkOutSideSteeringWheel_Vert>
    <settingParkingUIParkOutSideTextBottom_Hori>
			<iconCenter>220 657</iconCenter>
    </settingParkingUIParkOutSideTextBottom_Hori>
    <settingParkingUIParkOutSideTextBottom_Vert>
      <iconCenter>539 360</iconCenter>
    </settingParkingUIParkOutSideTextBottom_Vert>
    <settingParkingUIRPAMobilePhoneIcon_Hori>
      <iconCenter>216 393</iconCenter>
    </settingParkingUIRPAMobilePhoneIcon_Hori>
    <settingParkingUIRPAMobilePhoneIcon_Vert>
      <iconCenter>290 360</iconCenter>
    </settingParkingUIRPAMobilePhoneIcon_Vert>
    <settingParkingUIRPATextPrompt_Hori>
      <iconCenter>222 570</iconCenter>
    </settingParkingUIRPATextPrompt_Hori>
    <settingParkingUIRPATextPrompt_Vert>
      <iconCenter>470 395</iconCenter>
    </settingParkingUIRPATextPrompt_Vert>
    <settingParkingUIRPAPleaseLeaveTheCar_Hori>
      <iconCenter>215 157</iconCenter>
    </settingParkingUIRPAPleaseLeaveTheCar_Hori>
    <settingParkingUIRPAPleaseLeaveTheCar_Vert>
      <iconCenter>470 360</iconCenter>
    </settingParkingUIRPAPleaseLeaveTheCar_Vert>
    <settingParkingUIFreeParkingTextDescription_Hori>
      <iconCenter>216 180</iconCenter>
    </settingParkingUIFreeParkingTextDescription_Hori>
    <settingParkingUIFreeParkingTextDescription_Vert>
      <iconCenter>167 360</iconCenter>
    </settingParkingUIFreeParkingTextDescription_Vert>
    <settingParkingUIFreeParkingInstructions_Hori>
      <iconCenter>214 377</iconCenter>
    </settingParkingUIFreeParkingInstructions_Hori>
    <settingParkingUIFreeParkingInstructions_Vert>
      <iconCenter>293 360</iconCenter>
    </settingParkingUIFreeParkingInstructions_Vert>
    <settingParkingUIFreeParkingOperationGuide_Hori>
      <iconCenter>214 558</iconCenter>
    </settingParkingUIFreeParkingOperationGuide_Hori>
    <settingParkingUIFreeParkingOperationGuide_Vert>
      <iconCenter>419 368</iconCenter>
    </settingParkingUIFreeParkingOperationGuide_Vert>
    <settingFunctionSelectionButtonParkinPress_Hori>
      <iconSize>190 72</iconSize>
      <iconCenter>329 72</iconCenter>
      <responseArea>190 72</responseArea>
    </settingFunctionSelectionButtonParkinPress_Hori>
    <settingFunctionSelectionButtonParkoutPress_Hori>
      <iconSize>190 72</iconSize>
      <iconCenter>519 72</iconCenter>
      <responseArea>190 72</responseArea>
    </settingFunctionSelectionButtonParkoutPress_Hori>
    <settingFunctionSelectionButtonFreeParkingPress_Hori>
      <iconSize>190 72</iconSize>
      <iconCenter>709 72</iconCenter>
      <responseArea>190 72</responseArea>
    </settingFunctionSelectionButtonFreeParkingPress_Hori>
    <settingParkTypeButtonAPAPress_Hori>
      <iconSize>207 72</iconSize>
      <iconCenter>416 72</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonAPAPress_Hori>
    <settingParkTypeButtonRPAPress_Hori>
      <iconSize>207 72</iconSize>
      <iconCenter>622 72</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonRPAPress_Hori>
    <settingParkOutSideLeftButtonSelectedPress_Hori>
      <iconSize>130 130</iconSize>
      <iconCenter>318 279</iconCenter>
      <responseArea>130 130</responseArea>
    </settingParkOutSideLeftButtonSelectedPress_Hori>
    <settingParkOutSideRightButtonSelectedPress_Hori>
      <iconSize>130 130</iconSize>
      <iconCenter>724 279</iconCenter>
      <responseArea>130 130</responseArea>
    </settingParkOutSideRightButtonSelectedPress_Hori>
    <settingParkingStartPauseConfirmButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>388 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingParkingStartPauseConfirmButtonPress_Hori>
    <settingParkinContinueButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>405 885</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkinContinueButtonPress_Hori>
    <settingParkinQuitButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>633 885</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkinQuitButtonPress_Hori>
    <settingSearchingFrontInButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>632 885</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingFrontInButtonPress_Hori>
    <settingSearchingRearInButtonPress_Hori>
      <iconSize>180 66</iconSize>
      <iconCenter>405 885</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingRearInButtonPress_Hori>
    <settingFreeParkingSpaceTypeCrossButtonPress_Hori>
      <iconSize>146 146</iconSize>
      <iconCenter>392 622</iconCenter>
      <responseArea>160 160</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_Hori>
    <settingFreeParkingSpaceTypeParallelButtonPress_Hori>
      <iconSize>146 146</iconSize>
      <iconCenter>168 622</iconCenter>
      <responseArea>160 160</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_Hori>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_Hori>
      <iconSize>146 146</iconSize>
      <iconCenter>616 622</iconCenter>
      <responseArea>160 160</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_Hori>
    <settingFunctionSelectionButtonParkinPress_Vert>
      <iconSize>190 72</iconSize>
      <iconCenter>350 759</iconCenter>
      <responseArea>190 72</responseArea>
    </settingFunctionSelectionButtonParkinPress_Vert>
    <settingFunctionSelectionButtonParkoutPress_Vert>
      <iconSize>190 72</iconSize>
      <iconCenter>540 759</iconCenter>
      <responseArea>190 72</responseArea>
    </settingFunctionSelectionButtonParkoutPress_Vert>
    <settingFunctionSelectionButtonFreeParkingPress_Vert>
      <iconSize>190 72</iconSize>
      <iconCenter>730 759</iconCenter>
      <responseArea>190 72</responseArea>
    </settingFunctionSelectionButtonFreeParkingPress_Vert>
    <settingParkTypeButtonAPAPress_Vert>
      <iconSize>207 72</iconSize>
      <iconCenter>437 759</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonAPAPress_Vert>
    <settingParkTypeButtonRPAPress_Vert>
      <iconSize>207 72</iconSize>
      <iconCenter>643 759</iconCenter>
      <responseArea>207 72</responseArea>
    </settingParkTypeButtonRPAPress_Vert>
    <settingParkOutSideLeftButtonSelectedPress_Vert>
      <iconSize>130 130</iconSize>
      <iconCenter>338 937</iconCenter>
      <responseArea>130 130</responseArea>
    </settingParkOutSideLeftButtonSelectedPress_Vert>
    <settingParkOutSideRightButtonSelectedPress_Vert>
      <iconSize>130 130</iconSize>
      <iconCenter>744 937</iconCenter>
      <responseArea>130 130</responseArea>
    </settingParkOutSideRightButtonSelectedPress_Vert>
    <settingParkingStartPauseConfirmButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>540 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkingStartPauseConfirmButtonPress_Vert>
    <settingParkinContinueButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>426 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkinContinueButtonPress_Vert>
    <settingParkinQuitButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>654 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingParkinQuitButtonPress_Vert>
    <settingSearchingFrontInButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>654 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingFrontInButtonPress_Vert>
    <settingSearchingRearInButtonPress_Vert>
      <iconSize>180 66</iconSize>
      <iconCenter>426 1470</iconCenter>
      <responseArea>180 66</responseArea>
    </settingSearchingRearInButtonPress_Vert>
    <settingFreeParkingSpaceTypeCrossButtonPress_Vert>
      <iconSize>146 146</iconSize>
      <iconCenter>355 1116</iconCenter>
      <responseArea>146 146</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_Vert>
    <settingFreeParkingSpaceTypeParallelButtonPress_Vert>
      <iconSize>146 146</iconSize>
      <iconCenter>540 1116</iconCenter>
      <responseArea>146 146</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_Vert>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_Vert>
      <iconSize>146 146</iconSize>
      <iconCenter>725 1116</iconCenter>
      <responseArea>146 146</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_Vert>
    <settingWheelSeparatorHorizontal>
      <iconSize>310 720</iconSize>
      <iconCenter>854 360</iconCenter>
    </settingWheelSeparatorHorizontal>
    <settingWheelSeparatorVertical>
      <iconSize>464 280</iconSize>
      <iconCenter>0 360</iconCenter>
    </settingWheelSeparatorVertical>
  <!-- Ending with D: For DENZA project; Ending with M: For MR project; -->
    <settingParkinQuitButtonPress_DENZA>
      <iconCenter>872 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingParkinQuitButtonPress_DENZA>
    <settingParkinQuitButtonPress_MR>
      <iconCenter>654 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingParkinQuitButtonPress_MR>
    <settingSuspendContinueButtonPress_DENZA>
      <iconCenter>504 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingSuspendContinueButtonPress_DENZA>
    <settingSuspendContinueButtonPress_MR>
      <iconCenter>378 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingSuspendContinueButtonPress_MR>
    <settingParkingStartButtonPress_DENZA>
      <iconCenter>692 1216</iconCenter>
      <responseArea>288 80</responseArea>
    </settingParkingStartButtonPress_DENZA>
    <settingParkingStartButtonPress_MR>
      <iconCenter>519 912</iconCenter>
      <responseArea>216 60</responseArea>
    </settingParkingStartButtonPress_MR>
    <settingParkTypeButtonAPAPress_DENZA>
      <iconCenter>580 92</iconCenter>
      <responseArea>208 88</responseArea>
    </settingParkTypeButtonAPAPress_DENZA>
    <settingParkTypeButtonAPAPress_MR>
      <iconCenter>435 69</iconCenter>
      <responseArea>156 66</responseArea>
    </settingParkTypeButtonAPAPress_MR>
    <settingParkTypeButtonRPAPress_DENZA>
      <iconCenter>808 92</iconCenter>
      <responseArea>192 88</responseArea>
    </settingParkTypeButtonRPAPress_DENZA>
    <settingParkTypeButtonRPAPress_MR>
      <iconCenter>606 69</iconCenter>
      <responseArea>144 66</responseArea>
    </settingParkTypeButtonRPAPress_MR>

    <settingFreeParkingSpaceTypeCrossButtonPress_DENZA>
      <iconSize>146 146</iconSize>
      <iconCenter>688 690</iconCenter>
      <responseArea>160 296</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_DENZA>
    <settingFreeParkingSpaceTypeParallelButtonPress_DENZA>
      <iconSize>146 146</iconSize>
      <iconCenter>464 690</iconCenter>
      <responseArea>160 296</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_DENZA>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_DENZA>
      <iconSize>146 146</iconSize>
      <iconCenter>912 690</iconCenter>
      <responseArea>160 296</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_DENZA>
    <!-- DENZA -->
    <settingFreeParkingSpaceTypeCrossButtonPress_MR>
      <iconSize>146 146</iconSize>
      <iconCenter>516 518</iconCenter>
      <responseArea>120 222</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_MR>
    <settingFreeParkingSpaceTypeParallelButtonPress_MR>
      <iconSize>146 146</iconSize>
      <iconCenter>348 518</iconCenter>
      <responseArea>120 222</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_MR>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_MR>
      <iconSize>146 146</iconSize>
      <iconCenter>684 518</iconCenter>
      <responseArea>120 222</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_MR>
    <!-- HC
    <settingFreeParkingSpaceTypeCrossButtonPress_MR>
      <iconSize>146 146</iconSize>
      <iconCenter>334 434</iconCenter>
      <responseArea>146 146</responseArea>
    </settingFreeParkingSpaceTypeCrossButtonPress_MR>
    <settingFreeParkingSpaceTypeParallelButtonPress_MR>
      <iconSize>146 146</iconSize>
      <iconCenter>519 434</iconCenter>
      <responseArea>146 146</responseArea>
    </settingFreeParkingSpaceTypeParallelButtonPress_MR>
    <settingFreeParkingSpaceTypeDiagonalButtonPress_MR>
      <iconSize>146 146</iconSize>
      <iconCenter>704 434</iconCenter>
      <responseArea>146 146</responseArea>
    </settingFreeParkingSpaceTypeDiagonalButtonPress_MR>
    -->
    <settingFunctionSelectionButtonParkinPress_DENZA>
      <iconSize>192 88</iconSize>
      <iconCenter>462 92</iconCenter>
      <responseArea>192 88</responseArea>
    </settingFunctionSelectionButtonParkinPress_DENZA>
    <settingFunctionSelectionButtonParkoutPress_DENZA>
      <iconSize>192 88</iconSize>
      <iconCenter>682 92</iconCenter>
      <responseArea>192 88</responseArea>
    </settingFunctionSelectionButtonParkoutPress_DENZA>
    <settingFunctionSelectionButtonFreeParkingPress_DENZA>
      <iconSize>192 88</iconSize>
      <iconCenter>910 92</iconCenter>
      <responseArea>192 88</responseArea>
    </settingFunctionSelectionButtonFreeParkingPress_DENZA>
    <settingFunctionSelectionButtonParkinPress_MR>
      <iconSize>144 66</iconSize>
      <iconCenter>347 69</iconCenter>
      <responseArea>144 66</responseArea>
    </settingFunctionSelectionButtonParkinPress_MR>
    <settingFunctionSelectionButtonParkoutPress_MR>
      <iconSize>144 66</iconSize>
      <iconCenter>512 69</iconCenter>
      <responseArea>144 66</responseArea>
    </settingFunctionSelectionButtonParkoutPress_MR>
    <settingFunctionSelectionButtonFreeParkingPress_MR>
      <iconSize>144 66</iconSize>
      <iconCenter>677 69</iconCenter>
      <responseArea>144 66</responseArea>
    </settingFunctionSelectionButtonFreeParkingPress_MR>

    <settingParkOutSideLeftButtonSelectedPress_DENZA>
      <iconSize>118 118</iconSize>
      <iconCenter>446 412</iconCenter>
      <responseArea>216 216</responseArea>
    </settingParkOutSideLeftButtonSelectedPress_DENZA>
    <settingParkOutSideRightButtonSelectedPress_DENZA>
      <iconSize>118 118</iconSize>
      <iconCenter>934 412</iconCenter>
      <responseArea>216 216</responseArea>
    </settingParkOutSideRightButtonSelectedPress_DENZA>
    <settingParkOutSideLeftButtonSelectedPress_MR>
      <iconSize>118 118</iconSize>
      <iconCenter>334 309</iconCenter>
      <responseArea>162 162</responseArea>
    </settingParkOutSideLeftButtonSelectedPress_MR>
    <settingParkOutSideRightButtonSelectedPress_MR>
      <iconSize>118 118</iconSize>
      <iconCenter>700 309</iconCenter>
      <responseArea>162 162</responseArea>
    </settingParkOutSideRightButtonSelectedPress_MR>
    <settingParkOutSideParallelLeftButtonSelectedPress_DFC>   <!-- offset cross to up: -((60*1698)/1235 = 82), parallel to down: +((80*1698)/1235 = 110) -->
      <iconSize>118 118</iconSize>
      <iconCenter>334 419</iconCenter>
      <responseArea>162 162</responseArea>
    </settingParkOutSideParallelLeftButtonSelectedPress_DFC>
    <settingParkOutSideParallelRightButtonSelectedPress_DFC>
      <iconSize>118 118</iconSize>
      <iconCenter>700 419</iconCenter>
      <responseArea>162 162</responseArea>
    </settingParkOutSideParallelRightButtonSelectedPress_DFC>
    <settingParkOutSideCrossLeftButtonSelectedPress_DFC>   <!-- offset cross to up: -((60*1698)/1235 = 82), parallel to down: +((80*1698)/1235 = 110) -->
      <iconSize>118 118</iconSize>
      <iconCenter>334 227</iconCenter>
      <responseArea>162 162</responseArea>
    </settingParkOutSideCrossLeftButtonSelectedPress_DFC>
    <settingParkOutSideCrossRightButtonSelectedPress_DFC>
      <iconSize>118 118</iconSize>
      <iconCenter>700 227</iconCenter>
      <responseArea>162 162</responseArea>
    </settingParkOutSideCrossRightButtonSelectedPress_DFC>

  </UIElements>
  <ParkingSpace>
    <isEnabled>1</isEnabled>  <!-- 0: not enabled; 1: enabled; -->
    <enablePSMode>7</enablePSMode>  <!-- show parkspace or not.
                                        bit 0: scanning, bit 1: selected, bit 2: guidance at parkin, bit 3: guidance at parkout.
                                        Eg. 1111B=15D means show all the time.
                                        Eg. 0111B=7D means not show at guidance at parkout-->
    <iconSize>235 517</iconSize>  <!-- the size is tuning based on planwidth is 11.0m -->
    <iconSizeVert>305 140</iconSizeVert>  <!-- the size is tuning based on planwidth is 8.0m -->
    <texturePathSelectableCrossSlot>cc/resources/icons/parkslot_selectable_cross.png</texturePathSelectableCrossSlot>
    <texturePathSelectableParaSlot>cc/resources/icons/parkslot_selectable_para.png</texturePathSelectableParaSlot>
    <texturePathSelectedCrossSlot>cc/resources/icons/parkslot_selected_cross.png</texturePathSelectedCrossSlot>
    <texturePathSelectedParaSlot>cc/resources/icons/parkslot_selected_para.png</texturePathSelectedParaSlot>
    <texturePathNextCrossSlot>cc/resources/icons/parkslot_next_cross.png</texturePathNextCrossSlot>
    <texturePathNextParaSlot>cc/resources/icons/parkslot_next_para.png</texturePathNextParaSlot>
    <texturePathTargetCrossSlot>cc/resources/icons/parkslot_target_cross.png</texturePathTargetCrossSlot>
    <texturePathTargetParaSlot>cc/resources/icons/parkslot_target_para.png</texturePathTargetParaSlot>
    <texturePathSelectedOrTargetCrossSlotFinal>cc/resources/icons/parkslot_target_cross_final.png</texturePathSelectedOrTargetCrossSlotFinal>
    <texturePathSelectedOrTargetParaSlotFinal>cc/resources/icons/parkslot_target_para_final.png</texturePathSelectedOrTargetParaSlotFinal>
    <slotCrossX>2.6</slotCrossX>
    <slotParallelX>5.5</slotParallelX>
    <slotOffset>1.48 0.11</slotOffset>
    <seachingSpacewidth_cm>238</seachingSpacewidth_cm>  <!-- widthWithMirrors: 2.175,  base on displayed ratio: 15*168/1060 = 2,3773 m -->
    <seachingSpaceDepthOffset_cm>30</seachingSpaceDepthOffset_cm>  <!-- we dont consider value < 5 cm -->
    <manoeuverShowDelay>0.2</manoeuverShowDelay>  <!-- in second -->
    <parkingDiagSlotAngleLowerLimit>0.401</parkingDiagSlotAngleLowerLimit>  <!-- keep the same as in ParkingSlot -->
    <parkingDiagSlotAngleUpperLimit>1.309</parkingDiagSlotAngleUpperLimit>  <!-- keep the same as in ParkingSlot -->
    <parkIconNumberOfDelayTraversalCycle>50</parkIconNumberOfDelayTraversalCycle> <!-- target park slot in parking planview during guidance -->
    <pldDelayDuration>0.06</pldDelayDuration>
  </ParkingSpace>
  <ParkingSlot>
    <parkingDiagSlotAngleLowerLimit>0.401</parkingDiagSlotAngleLowerLimit>  <!-- lower and upper limits of diag slot,
                                                                              unit: radian--> <!-- keep the same as in ParkingSpace -->
    <parkingDiagSlotAngleUpperLimit>1.309</parkingDiagSlotAngleUpperLimit>  <!-- keep the same as in ParkingSpace -->
    <parkingSlotflashDeplay>5</parkingSlotflashDeplay>
    <parkingCrossSlotConversionCycles>60</parkingCrossSlotConversionCycles>
  </ParkingSlot>
  <ParkingSlotFixed>
    <parkingDiagSlotAngleLowerLimit>0.401</parkingDiagSlotAngleLowerLimit>  <!-- lower and upper limits of diag slot,
                                                                              unit: radian--> <!-- keep the same as in ParkingSpace -->
    <parkingDiagSlotAngleUpperLimit>1.309</parkingDiagSlotAngleUpperLimit>  <!-- keep the same as in ParkingSpace -->
    <parkingSlotflashDeplay>5</parkingSlotflashDeplay>
  </ParkingSlotFixed>
  <DistanceDigitalDisplay>
    <offset_front>1.5 0.0 0.0</offset_front>
    <offset_front_parking>1.0 0.0 0.0</offset_front_parking>
    <offset_rear>1.4 0.0 0.0</offset_rear>
    <offset_rear_parking>1.3 0.0 0.0</offset_rear_parking>
    <fixedFrontPosition>6.0</fixedFrontPosition> <!-- default front fixed position -->
    <fixedRearPosition>-2.8</fixedRearPosition> <!-- default rear fixed position -->
    <fixedFrontPosition_vert_parking>4.7</fixedFrontPosition_vert_parking> <!-- default front fixed position -->
    <fixedRearPosition_vert_parking>-1.4</fixedRearPosition_vert_parking> <!-- default rear fixed position -->
    <stopTextFrontPosition>5.506 -0.64</stopTextFrontPosition>
    <stopTextRearPosition>-3.1 -0.64</stopTextRearPosition>
    <stopTextFrontPosition_vert_parking>4.2 -0.64</stopTextFrontPosition_vert_parking>
    <stopTextRearPosition_vert_parking>-2.5 -0.64</stopTextRearPosition_vert_parking>
    <textOffsetPercentageFront>0.6</textOffsetPercentageFront>
    <textOffsetPercentageRear>0.4</textOffsetPercentageRear>
  </DistanceDigitalDisplay>
  <PtsDistanceDigitalDisplay>
    <offset_front>1.5 0.0 0.0</offset_front>
    <offset_front_parking>1.0 0.0 0.0</offset_front_parking>
    <offset_rear>-0.0 0.0 0.0</offset_rear>
    <offset_rear_parking>1.3 0.0 0.0</offset_rear_parking>
    <fixedFrontPosition>6.0</fixedFrontPosition> <!-- default front fixed position -->
    <fixedRearPosition>-2.8</fixedRearPosition> <!-- default rear fixed position -->
    <fixedFrontTopPosition>6.7</fixedFrontTopPosition>
    <fixedRearBottomPosition>-4</fixedRearBottomPosition>
    <fixedFrontPosition_vert_parking>4.7</fixedFrontPosition_vert_parking> <!-- default front fixed position -->
    <fixedRearPosition_vert_parking>-1.4</fixedRearPosition_vert_parking> <!-- default rear fixed position -->
    <stopTextFrontPosition>5.706 -0.64</stopTextFrontPosition>
    <stopTextRearPosition>-3.1 -0.64</stopTextRearPosition>
    <stopTextFrontPosition_vert_parking>4.2 -0.64</stopTextFrontPosition_vert_parking>
    <stopTextRearPosition_vert_parking>-2.5 -0.64</stopTextRearPosition_vert_parking>
    <textOffsetPercentageFront>0.6</textOffsetPercentageFront>
    <textOffsetPercentageRear>0.4</textOffsetPercentageRear>
    <splineColorB>0.0 0.14 0.8 1.0</splineColorB>
    <rangeFrontConf_AIHC>1.0</rangeFrontConf_AIHC>
    <rangeRearConf_AIHC>1.0</rangeRearConf_AIHC>
    <rangeSideConf_AIHC>1.0</rangeSideConf_AIHC>
  </PtsDistanceDigitalDisplay>
  <DynamicGear>
    <!--<position>608 -130.0 0.0</position>
    <color>1.0 0.89 0.26 0.55</color>
    <outerRadius>22.0</outerRadius>
    <innerRadius>19.0</innerRadius> -->
    <position_UIPanel>5.35 1.376 0.0</position_UIPanel> <!--Unit: in meter on UI planview-->
    <position_MainView>5.94 0.0 0.0</position_MainView> <!--Unit: in meter on parking planview-->
    <color>1.0 0.89 0.26 0.85</color><!--HC:0.25 0.61 1.0 0.85; MR:1.0 0.89 0.26 0.85; -->
    <outerRadius>0.25.0</outerRadius> <!--Unit: in meter on parking planview-->
    <innerRadius>0.30.0</innerRadius> <!--Unit: in meter on parking planview-->
  </DynamicGear>
  <CustomVehicleColorSettings>
    <TimeGray>
      <diffuseColor>     75  78  82 255   </diffuseColor>
      <specColor1>       170 170 170  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       170 170 170 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       1.0              </brightness>  <!-- brightness is the last way to modify. better set to 1.0 -->
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>  <!-- logo baseplate -->
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 100             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>  <!-- front logo -->
      <chrome3Brightness>   0.5             </chrome3Brightness>  <!-- rear logo -->
      <veh2dBrightness>     0.55            </veh2dBrightness>  <!-- vehicle2D -->
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </TimeGray>
    <MountainAsh>
      <diffuseColor>     30  39  55 255   </diffuseColor>
      <specColor1>       30 20 40  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       170 170 170 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       1.0              </brightness>  <!-- brightness is the last way to modify. better set to 1.0 -->
      <plasticDiffuseColor>  0  0  0 255 </plasticDiffuseColor>  <!-- logo baseplate -->
      <plasticSpecColor>     127 127 127 255 </plasticSpecColor>
      <plasticSpecShininess> 10             </plasticSpecShininess>
      <plasticBrightness>    1.0             </plasticBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>  <!-- front logo -->
      <chrome3Brightness>   0.5             </chrome3Brightness>  <!-- rear logo -->
      <veh2dBrightness>     0.55            </veh2dBrightness>  <!-- vehicle2D -->
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </MountainAsh>
    <RedEmperor> <!-- RED_EMPEROR -->
      <diffuseColor>     120  21  22 255  </diffuseColor>
      <specColor1>       100   0   5  0   </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       100   0   5 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.2              </reflectionPower>
      <fresnel>          1.5               </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <plasticDiffuseColor>  0  0  0 255 </plasticDiffuseColor>
      <plasticSpecColor>     127 127 127 255 </plasticSpecColor>
      <plasticSpecShininess> 10             </plasticSpecShininess>
      <plasticBrightness>    1.0             </plasticBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     1.1             </veh2dBrightness>
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </RedEmperor>
    <SnowyWhite>
      <diffuseColor>     210 210 205 255  </diffuseColor>
      <specColor1>       191 191 191  0   </specColor1>
      <specShininess1>   150              </specShininess1>
      <specColor2>       255 255 255 255  </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       1.0             </brightness>
      <chromeDiffuseColor>  127 127 127 255 </chromeDiffuseColor>
      <chromeSpecColor>     127 127 127 255 </chromeSpecColor>
      <chromeSpecShininess> 50             </chromeSpecShininess>
      <chromeBrightness>    1.0             </chromeBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   0.5             </chrome3Brightness>
      <veh2dBrightness>     0.6             </veh2dBrightness>
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </SnowyWhite>
    <SilverSandBlack>
      <diffuseColor>     30  30  30 255   </diffuseColor>
      <specColor1>       86  86  86  0    </specColor1>
      <specShininess1>   300              </specShininess1>
      <specColor2>       86  86  86 255   </specColor2>
      <specShininess2>   20               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  1.0  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <plasticDiffuseColor>  0 0 0 255 </plasticDiffuseColor>
      <plasticSpecColor>     100 100 100 255 </plasticSpecColor>
      <plasticSpecShininess> 10             </plasticSpecShininess>
      <plasticBrightness>    1.0             </plasticBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.7             </veh2dBrightness>
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </SilverSandBlack>
    <SeaBlue>
      <diffuseColor>     2  32  100 255    </diffuseColor>
      <specColor1>       0 106 160  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       0 106 160 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.5  1.0  1.3    </lightPos>
      <brightness>       1.0              </brightness>
      <plasticDiffuseColor>  90  92  95 255 </plasticDiffuseColor>
      <plasticSpecColor>     127 127 127 255 </plasticSpecColor>
      <plasticSpecShininess> 100             </plasticSpecShininess>
      <plasticBrightness>    1.0             </plasticBrightness>
      <chrome2Brightness>   0.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </SeaBlue>
    <QianShanCui>   <!-- QIANSHAN_CUI -->
      <diffuseColor>    20  40  25 255   </diffuseColor>
      <specColor1>       5  60  50  0     </specColor1>
      <specShininess1>   200              </specShininess1>
      <specColor2>       5  60  50 255    </specColor2>
      <specShininess2>   10               </specShininess2>
      <reflectionPower>  1.0              </reflectionPower>
      <fresnel>          1.5              </fresnel>
      <lightPos>         0.0  0.6  1.0    </lightPos>
      <brightness>       1.0              </brightness>
      <plasticDiffuseColor>    0   0   0 255 </plasticDiffuseColor>
      <plasticSpecColor>      51  51  51 255 </plasticSpecColor>
      <plasticSpecShininess>  20             </plasticSpecShininess>
      <plasticBrightness>    1.0             </plasticBrightness>
      <chrome2Brightness>   1.5             </chrome2Brightness>
      <chrome3Brightness>   1.5             </chrome3Brightness>
      <veh2dBrightness>     0.8             </veh2dBrightness>
      <nightBrightnessOffset>0.5</nightBrightnessOffset>
    </QianShanCui>
  </CustomVehicleColorSettings>
  <Vehicle2DSettings>
    <black>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/black/front_left_door/open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/black/front_right_door/open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/black/hood/open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/black/rear_left_door/open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/black/rear_right_door/open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/black/trunk/open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/black/vehicle_body/body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/black/front_left_door/close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/black/front_right_door/close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/black/hood/close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/black/rear_left_door/close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/black/rear_right_door/close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/black/trunk/close.png</trunkClose>
      <vehicleBodyClose>cc/vehicle_model/vehicle2d/black/vehicle_body/normal_body.png</vehicleBodyClose>
    </black>
    <blue>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/blue/front_left_door/open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/blue/front_right_door/open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/blue/hood/open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/blue/rear_left_door/open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/blue/rear_right_door/open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/blue/trunk/open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/blue/vehicle_body/body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/blue/front_left_door/close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/blue/front_right_door/close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/blue/hood/close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/blue/rear_left_door/close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/blue/rear_right_door/close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/blue/trunk/close.png</trunkClose>
      <vehicleBodyClose>cc/vehicle_model/vehicle2d/blue/vehicle_body/normal_body.png</vehicleBodyClose>
    </blue>
    <gray>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/gray/front_left_door/open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/gray/front_right_door/open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/gray/hood/open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/gray/rear_left_door/open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/gray/rear_right_door/open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/gray/trunk/open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/gray/vehicle_body/body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/gray/front_left_door/close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/gray/front_right_door/close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/gray/hood/close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/gray/rear_left_door/close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/gray/rear_right_door/close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/gray/trunk/close.png</trunkClose>
      <vehicleBodyClose>cc/vehicle_model/vehicle2d/gray/vehicle_body/normal_body.png</vehicleBodyClose>
    </gray>
    <white>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/white/front_left_door/open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/white/front_right_door/open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/white/hood/open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/white/rear_left_door/open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/white/rear_right_door/open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/white/trunk/open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/white/vehicle_body/body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/white/front_left_door/close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/white/front_right_door/close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/white/hood/close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/white/rear_left_door/close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/white/rear_right_door/close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/white/trunk/close.png</trunkClose>
      <vehicleBodyClose>cc/vehicle_model/vehicle2d/white/vehicle_body/normal_body.png</vehicleBodyClose>
    </white>
    <golden>
      <frontLeftDoorOpen>cc/vehicle_model/vehicle2d/golden/front_left_door/open_warning.png</frontLeftDoorOpen>
      <frontRightDoorOpen>cc/vehicle_model/vehicle2d/golden/front_right_door/open_warning.png</frontRightDoorOpen>
      <hoodOpen>cc/vehicle_model/vehicle2d/golden/hood/open_warning.png</hoodOpen>
      <rearLeftDoorOpen>cc/vehicle_model/vehicle2d/golden/rear_left_door/open_warning.png</rearLeftDoorOpen>
      <rearRightDoorOpen>cc/vehicle_model/vehicle2d/golden/rear_right_door/open_warning.png</rearRightDoorOpen>
      <trunkOpen>cc/vehicle_model/vehicle2d/golden/trunk/open_warning.png</trunkOpen>
      <vehicleBodyOpen>cc/vehicle_model/vehicle2d/golden/vehicle_body/body_without_doors.png</vehicleBodyOpen>
      <frontLeftDoorClose>cc/vehicle_model/vehicle2d/golden/front_left_door/close_normal.png</frontLeftDoorClose>
      <frontRightDoorClose>cc/vehicle_model/vehicle2d/golden/front_right_door/close_normal.png</frontRightDoorClose>
      <hoodClose>cc/vehicle_model/vehicle2d/golden/hood/close.png</hoodClose>
      <rearLeftDoorClose>cc/vehicle_model/vehicle2d/golden/rear_left_door/close_normal.png</rearLeftDoorClose>
      <rearRightDoorClose>cc/vehicle_model/vehicle2d/golden/rear_right_door/close_normal.png</rearRightDoorClose>
      <trunkClose>cc/vehicle_model/vehicle2d/golden/trunk/close.png</trunkClose>
      <vehicleBodyClose>cc/vehicle_model/vehicle2d/golden/vehicle_body/normal_body.png</vehicleBodyClose>
    </golden>
    <proportion>397.0 400.0</proportion> <!-- measured manually in vehicle 2d picture in pixel -->
    <aspectRatioOfVehicle>0.46</aspectRatioOfVehicle> <!-- measured manually in vehicle 2d picture in pixel. method: crop to content. check the width and height: 181/397 -->
    <planViewPos>213.0 350.0</planViewPos> <!-- aligned with param in VehicleTransIconSettings-->
    <parkingPlanViewPos>853.0 350.0</parkingPlanViewPos> <!-- aligned with param in VehicleTransIconSettings-->
  </Vehicle2DSettings>
  <Vehicle2dIconSettings>
    <size>146.0 400.0</size>
    <planViewPos>215 355</planViewPos>
    <parkingPlanViewPos>854 355</parkingPlanViewPos>
    <planViewPosVert>280 180</planViewPosVert>
    <parkingPlanViewPosVert>280 180</parkingPlanViewPosVert>
  </Vehicle2dIconSettings>
  <VehicleTransIconSettings>
    <isEnabled>1</isEnabled>
    <proportion>516.0 516.0</proportion> <!-- measured manually in vehicle 2d picture in pixel -->
    <aspectRatioOfVehicle>0.51</aspectRatioOfVehicle> <!-- For HCST23 only for vehicle TransIcon ratio-->
  </VehicleTransIconSettings>
  <StreetOverlay>
    <gridFadeOutBegin>9.0</gridFadeOutBegin>
    <gridFadeOutEnd>16.0</gridFadeOutEnd>
    <gridXOffset>5.0</gridXOffset>
    <gridYOffset>3.7</gridYOffset>
    <gridWidth>4.0</gridWidth>
    <gridHeight>-0.01</gridHeight>
    <gridTileSize>1.0 2.0</gridTileSize>
    <gridTileOffset>0.5 0.0</gridTileOffset>
    <gridTexMinFilterMode>2</gridTexMinFilterMode>
    <roadBegin>4.5</roadBegin>
    <roadEnd>-10.0</roadEnd>
    <roadHeight>-0.02</roadHeight>
    <roadWidth>-0.8</roadWidth>
    <roadFadeOutFront>20.0</roadFadeOutFront>
    <roadFadeOutRear>5.0</roadFadeOutRear>
    <roadTexture>cc/vehicle_model/ui/park_road.png</roadTexture>
    <roadTexMinFilterMode>0</roadTexMinFilterMode>
    <roadTexCoordExp>3.0</roadTexCoordExp>
  </StreetOverlay>
  <SWInfoOverlay>
    <offset_SwVersion>2.6 0.0 0.0</offset_SwVersion>
    <offset_HwVersion>3.1 0.0 0.0</offset_HwVersion>
  </SWInfoOverlay>
  <STBSettings>
    <height>0.05</height>
    <shadowHeight>0.01</shadowHeight>
    <color>1.0 1.0 1.0 0.9</color>
    <shadowColor>0.0 0.0 0.0 0.9</shadowColor>
    <wheelRadius>0.36425</wheelRadius>
    <wheelAlpha>0.3</wheelAlpha>     <!-- transparency of wheels. 0.0 is full transparent. 1.0 is not transparent-->
    <lightPosition>0.0 1.0 1.0</lightPosition>
    <lineWidth>0.008</lineWidth>
    <lineGradientWidth>0.010</lineGradientWidth>
    <shadowWidth>0.04</shadowWidth>
    <shadowGradientWidth>0.03</shadowGradientWidth>
    <wheelinePositionX>0.5</wheelinePositionX>
    <wheelinePositionY>0.05</wheelinePositionY>
    <wheelinePositionZ>0.5</wheelinePositionZ>
    <wheelineWidth>0.04</wheelineWidth>  <!-- plus to the distance between two sides of the wheel width-->
    <wheelineLength>0.05</wheelineLength>  <!-- plus to the wheel diameter-->
    <wheelModelFile>cc/vehicle_model/LeftWheel.osgb</wheelModelFile>
  </STBSettings>
  <SpeedOverlay>
    <offset_Speed>-4.4 0.0 0.0</offset_Speed>
  </SpeedOverlay>
  <DynamicDistance>
    <position_UIPanel>5.33 -0.95 0.0</position_UIPanel>
    <position_MainView>6.2 -2.5 0.0</position_MainView>
    <fontType>cc/resources/Roboto-Regular.ttf</fontType>
    <color>1.0 1.0 1.0 1.0</color>
    <charSize>42.0</charSize>
    <FontResolutionWidth>100.0</FontResolutionWidth>
    <FontResolutionHeight>115.0</FontResolutionHeight>
  </DynamicDistance>
  <VirtualRealityObject>
    <slotHalfWidth>1.25</slotHalfWidth>
    <slotRear2RearAxelDistance>1.4</slotRear2RearAxelDistance>
    <slotFront2RearAxelDistance>4.1</slotFront2RearAxelDistance>
    <vehicleModelScaleFactor>1.0 1.0 1.0</vehicleModelScaleFactor>
    <filenameSlotOccupied>cc/resources/virualReality/parkslot_occupied.png</filenameSlotOccupied>
    <filenameSlotNotSelectable>cc/resources/virualReality/parkslot_occupied.png</filenameSlotNotSelectable>
    <filenameSlotSelectable>cc/resources/virualReality/parkslot_selectable.png</filenameSlotSelectable>
    <filenameSlotSelected>cc/resources/virualReality/parkslot_selected.png</filenameSlotSelected>
  </VirtualRealityObject>
  <LowpolyVehicle>
    <vehicleModelFilename>cc/resources/virualReality/vehicle_simplified.osgb</vehicleModelFilename>
  </LowpolyVehicle>
  <VirtualEgoVehicle>
    <vehicleModelFilename>cc/resources/virualReality/vehicle_simplified_ego.osgb</vehicleModelFilename>
  </VirtualEgoVehicle>
  <LowpolyPedestrian>
    <pedestrianModelFilename>cc/resources/virualReality/pedestrian.osgb</pedestrianModelFilename>
    <colorNormal>0.50 0.71 0.89 1.0</colorNormal>
    <colorCritical>0.89 0.25 0.29 1.0</colorCritical>
    <heightOverGround>0.0</heightOverGround>
  </LowpolyPedestrian>
  <VirtualDataHandler>
    <objectConfidenceThreshold>60.0</objectConfidenceThreshold>
    <pedestrianConfidenceThreshold>80.0</pedestrianConfidenceThreshold>
    <objectPositionCovXThreshold>1.0</objectPositionCovXThreshold>
    <objectPositionCovYThreshold>1.0</objectPositionCovYThreshold>
    <objectVelocityCovXThreshold>1.0</objectVelocityCovXThreshold>
    <objectVelocityCovYThreshold>1.0</objectVelocityCovYThreshold>
  </VirtualDataHandler>
  <RemainingMoveNumber>
    <position_UIPanel>6.07 -0.68 0.0</position_UIPanel>
    <position_MainView>6.2 1.864 0.0</position_MainView>
    <fontType>cc/resources/Roboto-Regular.ttf</fontType>
    <color>1.0 1.0 1.0 1.0</color>
    <charSize>42.0</charSize>
  </RemainingMoveNumber>
  <ModSettings>
    <absDiffThreshold>45.0</absDiffThreshold>
    <sizeErodeKernel>3</sizeErodeKernel>
    <sizeDilateKernel>15</sizeDilateKernel>
    <degradationSpeed>8.0</degradationSpeed>
    <thresholdToUpdateBg_ms>330</thresholdToUpdateBg_ms>
    <filterSize>20</filterSize>
    <filterShapeSize>0.5</filterShapeSize>
    <mergeSize>25</mergeSize>
    <resizeFactorTopView>0.25</resizeFactorTopView>
    <resizeFactorSingleCam>0.25</resizeFactorSingleCam>
    <frameSkipInterval>2</frameSkipInterval>
    <warnIconDelay>5</warnIconDelay>
    <trackingCycles>3</trackingCycles>
    <iconPosition>
      <topViewFrontWarnIcon>287.0 155.0</topViewFrontWarnIcon>
      <topViewRearWarnIcon>287.0 880.0</topViewRearWarnIcon>
      <topViewLeftWarnIcon>50.0 500.0</topViewLeftWarnIcon>
      <topViewRightWarnIcon>525.0 500.0</topViewRightWarnIcon>
      <singleCamViewWarnIcon>680.0 155.0</singleCamViewWarnIcon>
      <topViewFeatureIcon>50.0 155.0</topViewFeatureIcon>
      <singleCamViewFeatureIcon>1800.0 155.0</singleCamViewFeatureIcon>
    </iconPosition>
  </ModSettings>
  <ViewModeButtonBar>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/view_mode_button_bar.png</AvailableTexturePath>
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/view_mode_button_bar.png</AvailableTexturePath>
              </night>
          </buttonTexture>
          <horiPos>1353.0 100.0</horiPos>  <!-- 1353 1220 -->
  </ViewModeButtonBar>
  <SingleViewModeButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/single_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/single_view_selected.png</SelectedTexturePath>
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/single_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/single_view_selected.png</SelectedTexturePath>
              </night>
          </buttonTexture>
          <horiPos>989.0 100.0</horiPos>  <!-- 989 1220 -->
  </SingleViewModeButton>
  <PerspectiveViewModeButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/perspective_view_selected.png</SelectedTexturePath>
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/perspective_view_selected.png</SelectedTexturePath>
              </night>
          </buttonTexture>
          <horiPos>1171.0 100.0</horiPos>  <!-- 1171 1220 -->
  </PerspectiveViewModeButton>
  <WheelViewModeButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/wheel_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/wheel_view_selected.png</SelectedTexturePath>
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/wheel_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/wheel_view_selected.png</SelectedTexturePath>
              </night>
          </buttonTexture>
          <horiPos>1353 100</horiPos>  <!-- 1353 1220 -->
  </WheelViewModeButton>
  <WideViewModeButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/wide_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/wide_view_selected.png</SelectedTexturePath>
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/wide_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/wide_view_selected.png</SelectedTexturePath>
              </night>
          </buttonTexture>
          <horiPos>1535 100</horiPos>  <!-- 1535 1220 -->
  </WideViewModeButton>
  <SkeletonViewModeButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/skeleton_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/skeleton_view_selected.png</SelectedTexturePath>
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/skeleton_view.png</AvailableTexturePath>
                  <SelectedTexturePath>cc/resources/icons/skeleton_view_selected.png</SelectedTexturePath>
              </night>
          </buttonTexture>
          <horiPos>1717 100</horiPos>  <!-- 1717 1220 -->
  </SkeletonViewModeButton>
  <FrontViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/view_reqeust_available.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/front_vew_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/view_reqeust_available.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/front_vew_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>421.0 1056.0</horiPos>  <!-- 421 264 -->
  </FrontViewButton>
  <RearViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/view_reqeust_available.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/rear_vew_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/view_reqeust_available.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/rear_vew_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>421.0 316.0</horiPos> <!-- 421 1004 -->
  </RearViewButton>
  <PerspectiveFrontViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_front_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_front_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>421.0 1056.0</horiPos> <!-- 421 264 -->
  </PerspectiveFrontViewButton>
  <PerspectiveFrontLeftViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_front_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_front_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>297.0 998.0</horiPos> <!-- 297 322 -->
  </PerspectiveFrontLeftViewButton>
  <PerspectiveFrontRightViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_front_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_front_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>545.0 998.0</horiPos> <!-- 545 322 -->
  </PerspectiveFrontRightViewButton>
  <PerspectiveRearViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_rear_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_rear_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>421.0 316.0</horiPos> <!-- 421 1004 -->
  </PerspectiveRearViewButton>
  <PerspectiveRearLeftViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_rear_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_rear_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>297.0 374.0</horiPos> <!-- 297 946 -->
  </PerspectiveRearLeftViewButton>
  <PerspectiveRearRightViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_rear_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_rear_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>546.0 374.0</horiPos> <!-- 546 374 -->
  </PerspectiveRearRightViewButton>
  <PerspectiveLeftViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_left_selected.pngg</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_left_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>189.0 686.0</horiPos> <!-- 189 634 -->
  </PerspectiveLeftViewButton>
  <PerspectiveRightViewButton>
          <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_right_selected.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/perspective_unselected.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/perspective_right_selected.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>653.0 686.0</horiPos> <!-- 653 634 -->
  </PerspectiveRightViewButton>
  <ParkButton>
            <buttonTexture>
              <day>
                  <AvailableTexturePath>cc/resources/icons/parkButton.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/parkButton.png</SelectedTexturePath>;
              </day>
              <night>
                  <AvailableTexturePath>cc/resources/icons/parkButton.png</AvailableTexturePath>;
                  <SelectedTexturePath>cc/resources/icons/parkButton.png</SelectedTexturePath>;
              </night>
          </buttonTexture>
          <horiPos>82.0 99.0</horiPos> <!-- 82 1221 -->
  </ParkButton>
</CodingParameters>
