#===============================================================================
# Copyright (c) 2017 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON>sch GmbH. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

include hw/build/module_head.mk

SOURCEFILES = \
Bowl.cpp      \
SingleCam.cpp \
LightStateClasses.cpp \
Floor.cpp     \
FloorSingleCam.cpp \
Vehicle.cpp \
Vehicle2D.cpp \
VehicleCommand.cpp \
CustomRenderManager.cpp \
CustomFloorPlateRenderer.cpp \
FloorPlateSm.cpp \
CameraImageCallback.cpp

BINARY = object

include hw/build/$(COMPILER_NAME)/module_tail.mk