//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: JLR NFS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: VUJ1LR Vujicic Milica (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS JLR
/// @file  SeeThroughBonnet.cpp
/// @brief
//=============================================================================

#include "cc/assets/stb/inc/SeeThroughBonnet.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"   //for isZero float compare
#include "pc/svs/util/osgx/inc/Utils.h"

#include "cc/assets/trajectory/inc/Helper.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/util/beziercurve/inc/BezierCurve.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "vfc/core/vfc_types.hpp"
#include "osg/LineWidth"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osgDB/WriteFile"
#include "osgDB/ReadFile"

namespace cc
{
namespace assets
{
namespace stb
{

static pc::util::coding::Item<Settings> g_settings("STBSettings");

SeeThroughBonnetCallback::SeeThroughBonnetCallback() = default;



SeeThroughBonnetCallback::~SeeThroughBonnetCallback() = default;

void SeeThroughBonnetCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  SeeThroughBonnet* const l_stb = dynamic_cast <SeeThroughBonnet*>(f_node); // PRQA S 3077  // PRQA S 3400
  if (l_stb != nullptr)
  {
    l_stb->update(f_nv);
  }

  traverse(f_node, f_nv);
}

//!
//! SeeThroughBonnet
//!
SeeThroughBonnet::SeeThroughBonnet(cc::core::CustomFramework* f_pCustomFramework, const cc::assets::trajectory::mainlogic::Inputs_st& fc_input )
  : m_pCustomFramework{f_pCustomFramework}
  , m_inputData{fc_input}
  , m_drivingForward{true}
  , m_lastFrameTime{0.0}
  , m_wheelRotationAngle_rad{0.0f}
  , m_steeringAngle_rad{0.0f}
  , m_lineGeom_front{}
  , m_lineGeom_leftWheel{}
  , m_lineGeom_rightWheel{}
  , m_lineGeom_leftOutline{}
  , m_lineGeom_rightOutline{}
  , m_shadowGeom_front{}
  , m_shadowGeom_leftWheel{}
  , m_shadowGeom_rightWheel{}
  , m_shadowGeom_leftOutline{}
  , m_shadowGeom_rightOutline{}
  , m_lineGeode_front{}
  , m_lineGeode_leftOutline{}
  , m_lineGeode_rightOutline{}
  , m_lineGeode_leftWheel{}
  , m_lineGeode_rightWheel{}
  , m_shadowGeode_front{}
  , m_shadowGeode_leftWheel{}
  , m_shadowGeode_rightWheel{}
  , m_shadowGeode_leftOutline{}
  , m_shadowGeode_rightOutline{}
  , m_leftWheel{}
  , m_rightWheel{}
  , m_vertexData{}
  , m_texturePath_1(std::string("cc/resources/STB_LineTex.png")) // PRQA S 2961
  , m_texturePath_2(std::string("cc/resources/STB_ShadowTex.png")) // PRQA S 2961
{
  m_lineGeode_front           = new osg::Geode();
  m_lineGeode_leftOutline     = new osg::Geode();
  m_lineGeode_rightOutline    = new osg::Geode();
  m_lineGeode_leftWheel       = new osg::Geode();
  m_lineGeode_rightWheel      = new osg::Geode();
  m_shadowGeode_front         = new osg::Geode();
  m_shadowGeode_leftWheel     = new osg::Geode();
  m_shadowGeode_rightWheel    = new osg::Geode();
  m_shadowGeode_leftOutline   = new osg::Geode();
  m_shadowGeode_rightOutline  = new osg::Geode();
  m_lineGeom_front            = new osg::Geometry;
  m_lineGeom_leftWheel        = new osg::Geometry;
  m_lineGeom_rightWheel       = new osg::Geometry;
  m_lineGeom_leftOutline      = new osg::Geometry;
  m_lineGeom_rightOutline     = new osg::Geometry;
  m_shadowGeom_front          = new osg::Geometry;
  m_shadowGeom_leftWheel      = new osg::Geometry;
  m_shadowGeom_rightWheel     = new osg::Geometry;
  m_shadowGeom_leftOutline    = new osg::Geometry;
  m_shadowGeom_rightOutline    = new osg::Geometry;

  create1DTexture(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f), m_texturePath_1, g_settings->m_lineWidth, g_settings->m_lineGradientWidth, false);
  create1DTexture(osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f), m_texturePath_2, g_settings->m_shadowWidth, g_settings->m_shadowGradientWidth, true);

  m_lineGeom_leftWheel = createWheelOverlays(false, true);
  m_lineGeom_rightWheel = createWheelOverlays(false, false);
  m_shadowGeom_leftWheel = createWheelOverlays(true, true);
  m_shadowGeom_rightWheel = createWheelOverlays(true, false);

  createFrontAndSidesOverlays();

  osg::StateSet* const l_stateSetLine = new osg::StateSet();
  osg::StateSet* const l_stateSetShadow = new osg::StateSet();
  applyTexShaderToStateSet(l_stateSetLine,    pc::core::g_renderOrder->m_trajectories,     false, false, true);
  applyTexShaderToStateSet(l_stateSetShadow,  pc::core::g_renderOrder->m_carShadow,   false, false, true);
  loadTexture(0u, m_texturePath_1, l_stateSetLine);
  loadTexture(0u, m_texturePath_2, l_stateSetShadow);

  setStateSetToGeometry(l_stateSetLine, m_lineGeom_front,        m_lineGeode_front);
  setStateSetToGeometry(l_stateSetLine, m_lineGeom_leftOutline,  m_lineGeode_leftOutline);
  setStateSetToGeometry(l_stateSetLine, m_lineGeom_rightOutline, m_lineGeode_rightOutline);
  setStateSetToGeometry(l_stateSetLine, m_lineGeom_leftWheel,    m_lineGeode_leftWheel,  false);        //  child of m_leftWheelTransform
  setStateSetToGeometry(l_stateSetLine, m_lineGeom_rightWheel,   m_lineGeode_rightWheel, false);        //  child of m_rightWheelTransform
  setStateSetToGeometry(l_stateSetShadow, m_shadowGeom_front,        m_shadowGeode_front);
  setStateSetToGeometry(l_stateSetShadow, m_shadowGeom_leftOutline,  m_shadowGeode_leftOutline);
  setStateSetToGeometry(l_stateSetShadow, m_shadowGeom_rightOutline, m_shadowGeode_rightOutline);
  setStateSetToGeometry(l_stateSetShadow, m_shadowGeom_leftWheel,    m_shadowGeode_leftWheel,  false);  //  child of m_leftWheelTransform
  setStateSetToGeometry(l_stateSetShadow, m_shadowGeom_rightWheel,   m_shadowGeode_rightWheel, false);  //  child of m_rightWheelTransform

  m_leftWheelTransform = new osg::PositionAttitudeTransform();
  m_rightWheelTransform = new osg::PositionAttitudeTransform();
  m_rightWheelTreadTransform = new osg::PositionAttitudeTransform();

#if 0 // To generate the Wheel model (Don't forget to set WHEEL_SUBMODEL_COUNT correctly)
  osg::Group* l_leftGroup = new osg::Group;
  //osg::Group* l_rightGroup = new osg::Group;

  for (unsigned int i = 0; i < WHEEL_SUBMODEL_COUNT; ++i)
  {
    std::stringstream l_index_ss;
    l_index_ss << ((i < 10) ? ("0") : ("")) << i;
    std::stringstream l_fullPath_ss;
    l_fullPath_ss << "cc/resources/STB/" << l_index_ss.str() << ".osg";
    const std::string& l_fullPath = l_fullPath_ss.str();

    m_leftWheel[i] = osgDB::readNodeFile(l_fullPath);
    osg::StateSet* l_stateSet = m_leftWheel[i]->getOrCreateStateSet();
    l_stateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
    l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON);
    l_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
    l_stateSet->setRenderBinDetails(cc::core::RENDERBIN_ORDER_WHEEL_00 + i, "RenderBin");

    m_rightWheel[i] = m_leftWheel[i];

    m_leftWheelTransform->addChild(m_leftWheel[i]);
    l_leftGroup->addChild(m_leftWheel[i]);

    /*if (0 == i)
    {
      // Tire tread needs mirroring
      m_rightWheelTreadTransform->addChild(m_rightWheel[i]);
    }
    else*/
    {
      m_rightWheelTransform->addChild(m_rightWheel[i]);
    }
    //l_rightGroup->addChild(m_rightWheel[i]);
  }

  osgDB::writeNodeFile(*l_leftGroup, "cc/resources/STB/LeftWheel.osg");
  //osgDB::writeNodeFile(*l_rightGroup, "cc/resources/STB/RightWheel.osg");
#else
  m_leftWheel[0] = new osg::Group;
  pc::core::Scene* const l_scene = m_pCustomFramework->getScene();
  l_scene->addCullCallback(new pc::util::osgx::NodePagingCallback(g_settings->m_wheelModelFile, m_leftWheel[0]->asGroup()));

  m_rightWheel[0] = m_leftWheel[0];

  vfc::nop(m_leftWheelTransform->addChild(m_leftWheel[0]));
  vfc::nop(m_rightWheelTransform->addChild(m_rightWheel[0]));
#endif

  m_leftTransform = new osg::PositionAttitudeTransform();
  vfc::nop(m_leftTransform->addChild(m_lineGeode_leftWheel));
  vfc::nop(m_leftTransform->addChild(m_shadowGeode_leftWheel));

  m_rightTransform = new osg::PositionAttitudeTransform();
  vfc::nop(m_rightTransform->addChild(m_lineGeode_rightWheel));
  vfc::nop(m_rightTransform->addChild(m_shadowGeode_rightWheel));

  vfc::nop(osg::Group::addChild(m_leftTransform));
  vfc::nop(osg::Group::addChild(m_rightTransform));
  vfc::nop(osg::Group::addChild(m_leftWheelTransform));
  vfc::nop(osg::Group::addChild(m_rightWheelTransform));
  vfc::nop(osg::Group::addChild(m_rightWheelTreadTransform)); // PRQA S 3803

  updateWheelSteering();
  setUpdateCallback(new SeeThroughBonnetCallback());

  m_pWheelAlphaUniform     = getOrCreateStateSet()->getOrCreateUniform("u_wheelAlpha", osg::Uniform::FLOAT);
  m_pLightPositionUniform  = getOrCreateStateSet()->getOrCreateUniform("u_LightPos",   osg::Uniform::FLOAT_VEC3);

  //osgDB::writeNodeFile(*this, "STB.osg");
}

  void SeeThroughBonnet::setStateSetToGeometry( osg::StateSet* f_stateSet, osg::Geometry* f_geometry, osg::Geode* f_geode, bool f_addAsChild)
  {
    if ((f_stateSet == nullptr) || (f_geometry == nullptr) || f_geode == nullptr)
    {
        return;
    }
    if(f_geometry != nullptr)
    {
      f_geometry->setStateSet(f_stateSet);
      vfc::nop(f_geode->addDrawable(f_geometry));

      if(f_addAsChild)
      {
        vfc::nop(osg::Group::addChild(f_geode));
      }
    }
  }

void SeeThroughBonnet::applyTexShaderToStateSet(osg::StateSet* f_stateSet, vfc::int32_t f_renderBinOrder, bool f_depthTest, bool f_depthBufferWrite, bool f_blend)
{
  if (f_stateSet == nullptr)
  {
      return;
  }
  osg::Depth * const l_depthStateAttrib = new osg::Depth(osg::Depth::LESS);
  l_depthStateAttrib->setWriteMask(f_depthBufferWrite);
  const osg::StateAttribute::GLModeValue l_depthTest = f_depthTest ? osg::StateAttribute::ON : osg::StateAttribute::OFF;
  const osg::StateAttribute::GLModeValue l_blend     = f_blend     ? osg::StateAttribute::ON : osg::StateAttribute::OFF;

  pc::core::TextureShaderProgramDescriptor l_basicTexShader("basicTex");
  vfc::nop(l_basicTexShader.apply(f_stateSet));
  f_stateSet->setMode(GL_BLEND, l_blend);          // PRQA S 3143
  f_stateSet->setMode(GL_DEPTH_TEST, l_depthTest); // PRQA S 3143
  f_stateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  f_stateSet->setRenderBinDetails(f_renderBinOrder, "RenderBin");
  f_stateSet->setAttributeAndModes(l_depthStateAttrib);
}

void SeeThroughBonnet::create1DTexture(osg::Vec4f f_color, const std::string& /*f_texturePath*/, vfc::float32_t f_lineWidth, vfc::float32_t f_lineGradientWidth, bool f_shadow) // PRQA S 6043
{
  constexpr vfc::uint32_t  lc_imageWidth  = 64u;  // Image width in pixels.
  constexpr vfc::uint32_t  lc_imageHeight = 1u;   // Image height in pixels.
  constexpr vfc::uint32_t  lc_imageDepth  = 1u;   // Image depth in pixels, in case of a 3D image.
  constexpr unsigned char lc_channelsPerPixel = 4u;
  constexpr unsigned char lc_bytesPerChannel  = 1u;
  constexpr vfc::float32_t         lc_imageGeometryWidth = static_cast<vfc::float32_t>(lc_imageWidth - 1u);

  // This multiplier is to widen the quad stripe to have enough room for the blur on the downsampled mipmaps.
  constexpr vfc::float32_t lc_blurMul = 1.0f; // (1 <= )


  const vfc::float32_t lc_halfGradientWidth = std::abs(f_lineGradientWidth) * 0.5f;
  const vfc::float32_t lc_halfWholeWidth = f_lineWidth * 0.5f;           // Half of the width of the line including the borders.
  vfc::float32_t l_absDistancesFromCenter[2] = { 0.0f, 0.0f };           // 0..1: From outermost to innermost

  l_absDistancesFromCenter[0] = lc_halfWholeWidth + lc_halfGradientWidth;
  l_absDistancesFromCenter[1] = lc_halfWholeWidth - lc_halfGradientWidth;

  const vfc::float32_t lc_halfGeometryWidth = l_absDistancesFromCenter[0] * lc_blurMul;

  const vfc::float32_t l_geometryWidth = lc_halfGeometryWidth * 2.0f;

  if (f_shadow)
  {
    m_shadowGeometryWidth = l_geometryWidth;
  }
  else
  {
    m_lineGeometryWidth = l_geometryWidth;
  }

  vfc::float32_t l_normalizedPositions[4] = { 0.0f, 0.0f, 0.0f, 0.0f };  // 0..3: From left to right
  l_normalizedPositions[0] = (lc_halfGeometryWidth - l_absDistancesFromCenter[0]) / l_geometryWidth;
  l_normalizedPositions[1] = (lc_halfGeometryWidth - l_absDistancesFromCenter[1]) / l_geometryWidth;
  l_normalizedPositions[2] = 1.0f - l_normalizedPositions[1];
  l_normalizedPositions[3] = 1.0f - l_normalizedPositions[0];
  std::array<std::array<std::array<std::array<std::array<unsigned char,lc_bytesPerChannel>,lc_channelsPerPixel >,lc_imageDepth >,lc_imageHeight>,lc_imageWidth>l_pixelArray; // PRQA S 4102
  //unsigned char l_pixelArray[lc_imageWidth][lc_imageHeight][lc_imageDepth][lc_channelsPerPixel][lc_bytesPerChannel];
  osg::Vec4ub l_lineColor_Inside;

  l_lineColor_Inside = pc::util::osgx::toVec4ub(f_color);

  osg::Vec4ub l_lineColor_Outside = l_lineColor_Inside;

  l_lineColor_Outside.a() = 0u;

  for (vfc::uint32_t x = 0u; x < lc_imageWidth; x++)
  {
    const vfc::float32_t l_x_normalized = static_cast<vfc::float32_t>(x) / lc_imageGeometryWidth;

    if (l_x_normalized < l_normalizedPositions[0])
    {
      // Left outside
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::R)][0u] = l_lineColor_Outside.r();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::G)][0u] = l_lineColor_Outside.g();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::B)][0u] = l_lineColor_Outside.b();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::A)][0u] = l_lineColor_Outside.a();
    }
    else if (l_x_normalized < l_normalizedPositions[1])
    {
      // Left gradient
      osg::Vec4ub l_interpolatedColor;
      if (f_shadow)
      {
        l_interpolatedColor = cc::assets::trajectory::helper::smoothstep_GGX_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, l_normalizedPositions[1], l_normalizedPositions[0], l_x_normalized);
      }
      else
      {
        l_interpolatedColor = cc::assets::trajectory::helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Outside, l_lineColor_Inside, l_normalizedPositions[0], l_normalizedPositions[1], l_x_normalized);
      }
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::R)][0u] = l_interpolatedColor.r();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::G)][0u] = l_interpolatedColor.g();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::B)][0u] = l_interpolatedColor.b();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::A)][0u] = l_interpolatedColor.a();
    }
    else if (l_x_normalized < l_normalizedPositions[2])
    {
      // Middle
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::R)][0u] = l_lineColor_Inside.r();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::G)][0u] = l_lineColor_Inside.g();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::B)][0u] = l_lineColor_Inside.b();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::A)][0u] = l_lineColor_Inside.a();
    }
    else if (l_x_normalized < l_normalizedPositions[3])
    {
      // Right gradient
      osg::Vec4ub l_interpolatedColor;
      if (f_shadow)
      {
        l_interpolatedColor = cc::assets::trajectory::helper::smoothstep_GGX_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, l_normalizedPositions[2], l_normalizedPositions[3], l_x_normalized);
      }
      else
      {
        l_interpolatedColor = cc::assets::trajectory::helper::smoothstep_Vec4ub( // PRQA S 2759
          l_lineColor_Inside, l_lineColor_Outside, l_normalizedPositions[2], l_normalizedPositions[3], l_x_normalized);
      }
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::R)][0u] = l_interpolatedColor.r();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::G)][0u] = l_interpolatedColor.g();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::B)][0u] = l_interpolatedColor.b();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::A)][0u] = l_interpolatedColor.a();
    }
    else
    {
      // Right outside
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::R)][0u] = l_lineColor_Outside.r();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::G)][0u] = l_lineColor_Outside.g();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::B)][0u] = l_lineColor_Outside.b();
      l_pixelArray[x][0u][0u][static_cast<vfc::uint32_t>(cc::assets::trajectory::helper::A)][0u] = l_lineColor_Outside.a();
    }
  }

  unsigned char* const imageData = &(l_pixelArray[0u][0u][0u][0u][0u]);
  const osg::ref_ptr<osg::Image> l_image = new osg::Image;
  l_image->setOrigin(osg::Image::TOP_LEFT);
  l_image->setImage(static_cast<vfc::int32_t>(lc_imageWidth),
                    static_cast<vfc::int32_t>(lc_imageHeight),
                    static_cast<vfc::int32_t>(lc_imageDepth),
                    GL_RGBA,
                    GL_RGBA,     // PRQA S 3143
                    GL_UNSIGNED_BYTE,  // PRQA S 3143
                    imageData,
                    osg::Image::NO_DELETE);

  // osgDB::writeImageFile(*l_image, f_texturePath);
}

void SeeThroughBonnet::loadTexture(const vfc::uint32_t f_texSlot, const std::string& f_texturePath, osg::StateSet* f_stateSet)
{
  if (f_stateSet == nullptr)
  {
      return;
  }
  const osg::ref_ptr<osg::Image> l_texImage = osgDB::readImageFile(f_texturePath);
  const osg::ref_ptr<osg::Texture2D> l_tex2D = new osg::Texture2D;
  l_tex2D->setDataVariance(osg::Object::DYNAMIC);
  l_tex2D->setImage(l_texImage);
  l_tex2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
  l_tex2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
  l_tex2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
  l_tex2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

  f_stateSet->setTextureAttribute(f_texSlot, l_tex2D);
  std::stringstream ss;
  vfc::nop((ss << "u_tex" << f_texSlot));
  const std::string& s = ss.str();
  const char* const cstr = s.c_str();

  /*osg::Uniform* l_unifom = l_stateSet->getOrCreateUniform(ss.str(), osg::Uniform::SAMPLER_2D);
  l_unifom->set(static_cast<int>(f_texSlot));*/

  f_stateSet->addUniform(new osg::Uniform(cstr, static_cast<vfc::int32_t>(f_texSlot)));
}

/// Creates bumper and lateral geometries (both: line & shadow)
void SeeThroughBonnet::createFrontAndSidesOverlays()
{
  const vfc::float32_t l_wheelbase           = pc::vehicle::g_mechanicalData->m_wheelbase;
  const vfc::float32_t l_frontWheelRadius    = pc::vehicle::g_mechanicalData->m_wheelRadius;
  constexpr vfc::float32_t margin          = 0.075f;
  constexpr bool includeMirrorInOverlay = false;

  const vfc::float32_t frontWheelFront   = l_wheelbase + l_frontWheelRadius + margin;
  const vfc::float32_t frontWheelEnd     = l_wheelbase - l_frontWheelRadius - margin;
  const vfc::float32_t rearWheelFront    = l_frontWheelRadius + margin;

  ///////////////////// generate BUMPER overlay

  //find position of wheels on the contour
  const vfc::uint32_t contourSize = static_cast<vfc::uint32_t> (m_inputData.External.Car.VehicleContour_Left->size());
  const vfc::uint32_t frontWheelBeginIndex = indexBehindPosX(m_inputData.External.Car.VehicleContour_Left, 0u, contourSize, frontWheelFront);
  // assert((static_cast<int>(frontWheelBeginIndex) - 1) > 0);
  const osg::Vec2f frontWheelBeginPos = findIntersection(m_inputData.External.Car.VehicleContour_Left, frontWheelBeginIndex, frontWheelBeginIndex - 1u, frontWheelFront); // PRQA S 2912


  osg::ref_ptr<osg::Vec2Array> l_contourPoints;
  if(frontWheelBeginIndex > 0u)
  {
    l_contourPoints = new osg::Vec2Array(frontWheelBeginIndex + 1u);
    for(vfc::uint32_t i = frontWheelBeginIndex - 1u; i > 0u; --i)
    {
      (*l_contourPoints)[frontWheelBeginIndex - i] = (*m_inputData.External.Car.VehicleContour_Left)[i];
    }
    (*l_contourPoints)[0u] = frontWheelBeginPos;
    (*l_contourPoints)[frontWheelBeginIndex] = (*m_inputData.External.Car.VehicleContour_Left)[0u];

    const osg::ref_ptr<osg::Vec2Array> curvedPoints = smoothSpline(l_contourPoints, 3u);
    m_lineGeom_front = createOverlayFromPoints(curvedPoints, false, true);
    m_shadowGeom_front = createOverlayFromPoints(curvedPoints, true, true);
  }
  else
  {
    m_lineGeom_front = nullptr;
    m_shadowGeom_front = nullptr;
  }

  ///////////////////// generate LATERAL overlays

  //find position of wheels on the contour
  vfc::uint32_t frontWheelEndIndex = indexBehindPosX(m_inputData.External.Car.VehicleContour_Left, frontWheelBeginIndex, contourSize, frontWheelEnd);
  const vfc::uint32_t rearWheelBeginIndex = indexBehindPosX(m_inputData.External.Car.VehicleContour_Left, frontWheelEndIndex, contourSize, rearWheelFront);
  vfc::uint32_t avoidMirrorIndex = frontWheelEndIndex - 1u;

  if(includeMirrorInOverlay == false && (   frontWheelEndIndex == m_inputData.External.Car.MirrorPointIndex
                                         || frontWheelEndIndex == m_inputData.External.Car.MirrorPointIndex+1u))
  {
    avoidMirrorIndex = m_inputData.External.Car.MirrorPointIndex - 1u;
    frontWheelEndIndex = m_inputData.External.Car.MirrorPointIndex + 1u;
  }

  const osg::Vec2f frontWheelEndPos  = findIntersection(m_inputData.External.Car.VehicleContour_Left, frontWheelEndIndex, avoidMirrorIndex, frontWheelEnd);
  const osg::Vec2f rearWheelBeginPos = findIntersection(m_inputData.External.Car.VehicleContour_Left, rearWheelBeginIndex, rearWheelBeginIndex - 1u, rearWheelFront);

  l_contourPoints = new osg::Vec2Array();
  l_contourPoints->push_back(frontWheelEndPos);
  for(vfc::uint32_t i = frontWheelEndIndex+1u; i < rearWheelBeginIndex; ++i)
  {
    if(includeMirrorInOverlay || i != m_inputData.External.Car.MirrorPointIndex)
    {
      l_contourPoints->push_back((*m_inputData.External.Car.VehicleContour_Left)[i]);
    }
  }
  l_contourPoints->push_back(rearWheelBeginPos);

  const osg::ref_ptr<osg::Vec2Array> curvedPoints = smoothSpline(l_contourPoints, 3u);
  m_lineGeom_leftOutline = createOverlayFromPoints(curvedPoints, false, false);
  m_shadowGeom_leftOutline = createOverlayFromPoints(curvedPoints, true, false);

  for(size_t i = 0u ; i < curvedPoints->size(); ++i)
  {
    (*curvedPoints)[i].y() = -(*curvedPoints)[i].y();
  }
  m_lineGeom_rightOutline = createOverlayFromPoints(curvedPoints, false, false);
  m_shadowGeom_rightOutline = createOverlayFromPoints(curvedPoints, true, false);
}

/// Must receive points from left to right in order to Mirror them
osg::Geometry* SeeThroughBonnet::createOverlayFromPoints(const osg::Vec2Array* cf_points, bool f_shadow, bool f_mirrored)
{
  if(cf_points->empty())
  {
    return nullptr;
  }
  const vfc::uint32_t pointsCount = static_cast<vfc::uint32_t> (cf_points->size());
  const vfc::uint32_t mirrorMultiplier = f_mirrored ? 2u : 1u;
  m_vertexData.Vertices  = new osg::Vec3Array(2u * pointsCount * mirrorMultiplier);
  m_vertexData.Normals   = new osg::Vec3Array(1u);
  m_vertexData.Colors    = new osg::Vec4Array(2u * pointsCount * mirrorMultiplier);
  m_vertexData.TexCoords = new osg::Vec2Array(2u * pointsCount * mirrorMultiplier);
  m_vertexData.Indices   = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), (mirrorMultiplier * pointsCount - 1u) * 6u);

  osg::Geometry* const l_shape = new osg::Geometry;
  l_shape->setUseDisplayList(false);
  l_shape->setVertexArray(m_vertexData.Vertices);
  l_shape->setNormalArray(m_vertexData.Normals, osg::Array::BIND_OVERALL);
  l_shape->setColorArray(m_vertexData.Colors, osg::Array::BIND_PER_VERTEX);
  l_shape->setTexCoordArray(0u, m_vertexData.TexCoords);
  vfc::nop(l_shape->addPrimitiveSet(m_vertexData.Indices));

  //points on the left side
  std::vector<osg::Vec2f> l_vertexArray(pointsCount * 2u * mirrorMultiplier);  // *2 = upper and lower, *2 = mirror it
  for(vfc::uint32_t i= 0u; i < pointsCount; ++i)
  {
    l_vertexArray[(2u*i)] = (*cf_points)[i];
    l_vertexArray[(2u*i)+1u] = (*cf_points)[i];
  }

  if (f_shadow)
  {
    for (size_t i = 0u; i < l_vertexArray.size(); ++i) // PRQA S 4297 // PRQA S 4687
    {
      l_vertexArray[i].x() -= 0.04f;
    }
  }

  //points to be mirrored. Last should be near the center
  if(f_mirrored)
  {
    for (vfc::uint32_t i = 0u; i < pointsCount*2u; ++i)
    {
      l_vertexArray[i + (pointsCount*2u)] = osg::Vec2f(
                                              l_vertexArray[(pointsCount*2u) - i - 1u].x(),
                                            - l_vertexArray[(pointsCount*2u) - i - 1u].y());
    }
  }

  // Right perpendicular vectors
  osg::Vec2f l_segment0_RightPerpVec;
  osg::Vec2f l_segment1_RightPerpVec;
  // Normals
  osg::Vec2f l_leftNormal;
  osg::Vec2f l_rightNormal;
  // Transform to the left or right edge of the line (from the wheel track center spline)
  osg::Vec2f l_toLineLeftEdge;
  osg::Vec2f l_toLineRightEdge;


  const vfc::uint32_t lastpoint = pointsCount * 2u * mirrorMultiplier;
  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < lastpoint; l_vertexIndex+=2u)
  {
    if (0u == l_vertexIndex)
    {
      // First spline vertex
      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 2u]
                              - l_vertexArray[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment1_RightPerpVec;
      vfc::nop(l_rightNormal.normalize());
    }
    else if (lastpoint -2u == l_vertexIndex)
    {
      // Last spline vertex
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0u]
                              - l_vertexArray[l_vertexIndex - 2u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec;
      vfc::nop(l_rightNormal.normalize());
    }
    else
    {
      //  Every other vertex
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0u]
                              - l_vertexArray[l_vertexIndex - 2u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 2u]
                              - l_vertexArray[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec + l_segment1_RightPerpVec;
      vfc::nop(l_rightNormal.normalize());
    }
    l_leftNormal = -l_rightNormal;

    const vfc::float32_t l_width = f_shadow ? m_shadowGeometryWidth : m_lineGeometryWidth;

    l_toLineLeftEdge  = l_leftNormal  * l_width * 0.5f;
    l_toLineRightEdge = l_rightNormal * l_width * 0.5f;

    vfc::float32_t colorR = 0.0f;
    vfc::float32_t colorG = 0.0f;
    vfc::float32_t colorB = 0.0f;
    vfc::float32_t colorA = 0.0f;
    vfc::float32_t height = 0.0f;

    if(true == f_shadow)
    {
      colorR = g_settings->m_shadowColor.r();
      colorG = g_settings->m_shadowColor.g();
      colorB = g_settings->m_shadowColor.b();
      colorA = g_settings->m_shadowColor.a();
      height = g_settings->m_shadowHeight;
    }
    else
    {
      colorR = g_settings->m_color.r();
      colorG = g_settings->m_color.g();
      colorB = g_settings->m_color.b();
      colorA = g_settings->m_color.a();
      height = g_settings->m_height;
    }

    (*m_vertexData.Colors)  [l_vertexIndex  + 0u].set( colorR, colorG, colorB, colorA );
    (*m_vertexData.Colors)  [l_vertexIndex  + 1u].set( colorR, colorG, colorB, colorA );
    (*m_vertexData.Vertices)[l_vertexIndex  + 0u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineLeftEdge,  height) );
    (*m_vertexData.Vertices)[l_vertexIndex  + 1u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineRightEdge, height) );

    (*m_vertexData.TexCoords) [l_vertexIndex  + 0u].set( 0.0f, 0.5f );
    (*m_vertexData.TexCoords) [l_vertexIndex  + 1u].set( 1.0f, 0.5f );
  }

  (*m_vertexData.Normals)[0u] = osg::Vec3f(0.0f, 0.0f, 1.0f);
  m_vertexData.Indices->clear();

  for (vfc::uint32_t l_quadIndex = 0u; l_quadIndex < (mirrorMultiplier *pointsCount - 1u); l_quadIndex++)
  {
    const vfc::uint32_t l_vertexIndex = l_quadIndex * 2u;
    // Top left triangle
    m_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndex + 1u));
    m_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndex + 0u));
    m_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndex + 2u));
    // Bottom right triangle
    m_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndex + 2u));
    m_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndex + 3u));
    m_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndex + 1u));
  }

  return l_shape;
}

// not used
osg::Geometry* SeeThroughBonnet::createWheelTestOverlays()
{
  osg::Geometry* const l_wheelGeometry = new osg::Geometry;

  const vfc::float32_t l_wheelbase           = pc::vehicle::g_mechanicalData->m_wheelbase;
  const vfc::float32_t l_halfFrontTrack      = pc::vehicle::g_mechanicalData->m_trackFront * 0.5f;
  const vfc::float32_t l_halfFrontWheelWidth = pc::vehicle::g_mechanicalData->m_wheelWidthFront * 0.5f;
  const vfc::float32_t l_frontWheelRadius    = pc::vehicle::g_mechanicalData->m_wheelRadius;

  osg::Vec3Array* const l_vertexArray = new osg::Vec3Array(4u * 2u);
  (*l_vertexArray)[0u] = osg::Vec3f(l_wheelbase - l_frontWheelRadius,   l_halfFrontTrack + l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[1u] = osg::Vec3f(l_wheelbase - l_frontWheelRadius,   l_halfFrontTrack - l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[2u] = osg::Vec3f(l_wheelbase + l_frontWheelRadius,   l_halfFrontTrack - l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[3u] = osg::Vec3f(l_wheelbase + l_frontWheelRadius,   l_halfFrontTrack + l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[4u] = osg::Vec3f(l_wheelbase - l_frontWheelRadius, - l_halfFrontTrack + l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[5u] = osg::Vec3f(l_wheelbase - l_frontWheelRadius, - l_halfFrontTrack - l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[6u] = osg::Vec3f(l_wheelbase + l_frontWheelRadius, - l_halfFrontTrack - l_halfFrontWheelWidth, g_settings->m_height);
  (*l_vertexArray)[7u] = osg::Vec3f(l_wheelbase + l_frontWheelRadius, - l_halfFrontTrack + l_halfFrontWheelWidth, g_settings->m_height);
  l_wheelGeometry->setVertexArray(l_vertexArray);

  //indices
  vfc::nop(l_wheelGeometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINE_LOOP, static_cast<GLint>(0), static_cast<GLint>(4)))); // PRQA S 3143
  vfc::nop(l_wheelGeometry->addPrimitiveSet(new osg::DrawArrays(osg::PrimitiveSet::LINE_LOOP, static_cast<GLint>(4), static_cast<GLint>(4)))); // PRQA S 3143

  //color array
  osg::Vec4Array* const l_colorArray = new osg::Vec4Array(1u);
  (*l_colorArray)[0u] = osg::Vec4f (0.0f, 1.0f, 0.0f, 1.0f);
  l_wheelGeometry->setColorArray(l_colorArray, osg::Array::BIND_OVERALL);

  return l_wheelGeometry;
}

//// Create both lines and shadows around the wheels
const static osg::Vec2f ls_prevToLineLeftEdge;
const static osg::Vec2f ls_prevToLineRightEdge;
osg::Geometry* SeeThroughBonnet::createWheelOverlays(bool f_shadow, bool f_left) // PRQA S 6041 // PRQA S 4211
{
  osg::Geometry* const l_wheelGeometry = new osg::Geometry;

  const vfc::float32_t l_wheelbase           = pc::vehicle::g_mechanicalData->m_wheelbase;
  const vfc::float32_t l_halfFrontTrack      = pc::vehicle::g_mechanicalData->m_trackFront * 0.5f;
  const vfc::float32_t l_halfFrontWheelWidth = pc::vehicle::g_mechanicalData->m_wheelWidthFront * 0.5f;
  const vfc::float32_t l_frontWheelRadius    = pc::vehicle::g_mechanicalData->m_wheelRadius;

  vfc::float32_t l_width = m_lineGeometryWidth;
  if (f_shadow)
  {
    l_width = m_shadowGeometryWidth;
  }

  cc::assets::trajectory::commontypes::VertexData_st l_vertexData; // PRQA S 4102

  l_vertexData.Vertices  = new osg::Vec3Array(26u * 2u);
  l_vertexData.Normals   = new osg::Vec3Array(1u);
  l_vertexData.Colors    = new osg::Vec4Array(26u * 2u);
  l_vertexData.TexCoords = new osg::Vec2Array(26u * 2u);
  l_vertexData.Indices   = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), (26u - 1u) * 6u);

  l_wheelGeometry->setUseDisplayList(false);
  l_wheelGeometry->setVertexArray(l_vertexData.Vertices);
  l_wheelGeometry->setNormalArray(l_vertexData.Normals, osg::Array::BIND_OVERALL);
  l_wheelGeometry->setColorArray(l_vertexData.Colors, osg::Array::BIND_PER_VERTEX);
  l_wheelGeometry->setTexCoordArray(0u, l_vertexData.TexCoords);
  vfc::nop(l_wheelGeometry->addPrimitiveSet(l_vertexData.Indices));



  constexpr vfc::float32_t roundingRadius = 0.065f;
  constexpr vfc::uint32_t numPoints = 23u;
  const vfc::float32_t delta = 2.0f * static_cast<vfc::float32_t> (osg::PI)/static_cast<vfc::float32_t>(numPoints);
  vfc::float32_t l_refPosStartY = 0.0f;
  vfc::float32_t l_refPosRightY = 0.0f;
  vfc::float32_t l_refPosLeftY = 0.0f;

  if (true == f_left)
  {
    l_refPosStartY = l_halfFrontWheelWidth + g_settings->m_wheelineWidth + l_halfFrontTrack;
  }
  else
  {
    l_refPosStartY = -(l_halfFrontWheelWidth + g_settings->m_wheelineWidth + l_halfFrontTrack);
  }

  std::vector<osg::Vec2f> l_vertexArray(26u);
  l_vertexArray[0u] = osg::Vec2f(l_wheelbase, l_refPosStartY);
  l_vertexArray[25u] = osg::Vec2f(l_wheelbase, l_refPosStartY);

  for (vfc::uint32_t i = 0u; i < 24u; i++)
  {
    const vfc::float32_t deltaX = roundingRadius*std::sin(static_cast<vfc::float32_t>(i)*delta);
    const vfc::float32_t deltaY = roundingRadius*std::cos(static_cast<vfc::float32_t>(i)*delta);

    if (true == f_left)
    {
      l_refPosRightY = l_halfFrontTrack + deltaY +(l_halfFrontWheelWidth + g_settings->m_wheelineWidth - roundingRadius);
      l_refPosLeftY  = l_halfFrontTrack + deltaY -(l_halfFrontWheelWidth + g_settings->m_wheelineWidth - roundingRadius);
    }
    else
    {
      l_refPosRightY = -(l_halfFrontTrack + deltaY +(l_halfFrontWheelWidth + g_settings->m_wheelineWidth - roundingRadius));
      l_refPosLeftY  = -(l_halfFrontTrack + deltaY -(l_halfFrontWheelWidth + g_settings->m_wheelineWidth - roundingRadius));
    }

    if (i < 12u)
    {
      if (i < 6u)
      {
        l_vertexArray[i + 1u] = osg::Vec2f(l_wheelbase + l_frontWheelRadius - roundingRadius + deltaX,
                                          l_refPosRightY);
      }
      else
      {
        l_vertexArray[i + 1u] = osg::Vec2f(l_wheelbase + l_frontWheelRadius - roundingRadius + deltaX,
                                          l_refPosLeftY);
      }
    }
    else
    {
      if (i < 18u)
      {
        l_vertexArray[i + 1u] = osg::Vec2f(l_wheelbase - l_frontWheelRadius + roundingRadius + deltaX,
                                          l_refPosLeftY);
      }
      else
      {
        l_vertexArray[i + 1u] = osg::Vec2f(l_wheelbase - l_frontWheelRadius + roundingRadius + deltaX,
                                          l_refPosRightY);
      }
    }
  }

  if (f_shadow)
  {
    for (vfc::uint32_t i = 0u; i < 24u; i++)
    {
      l_vertexArray[i].x() *= 1.02f;
      l_vertexArray[i].x() -= 0.08f;
    }
  }

  // Right perpendicular vectors
  osg::Vec2f l_segment0_RightPerpVec;
  osg::Vec2f l_segment1_RightPerpVec;
  // Normals
  osg::Vec2f l_leftNormal;
  osg::Vec2f l_rightNormal;
  // Transform to the left or right edge of the line (from the wheel track center spline)
  osg::Vec2f l_toLineLeftEdge;
  osg::Vec2f l_toLineRightEdge;


  for (vfc::uint32_t l_vertexIndex = 0u; l_vertexIndex < 26u; ++l_vertexIndex)
  {
    if (0u == l_vertexIndex)
    {
      // First spline vertex
      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 1u]
                              - l_vertexArray[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment1_RightPerpVec;
      vfc::nop(l_rightNormal.normalize());
    }
    else if ((26u - 1u) == l_vertexIndex)
    {
      // Last spline vertex
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0u]
                              - l_vertexArray[l_vertexIndex - 1u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec;
      vfc::nop(l_rightNormal.normalize());
    }
    else
    {
      l_segment0_RightPerpVec = l_vertexArray[l_vertexIndex + 0u]
                              - l_vertexArray[l_vertexIndex - 1u];
      l_segment0_RightPerpVec = osg::Vec2f( - l_segment0_RightPerpVec.y(), l_segment0_RightPerpVec.x() );

      l_segment1_RightPerpVec = l_vertexArray[l_vertexIndex + 1u]
                              - l_vertexArray[l_vertexIndex + 0u];
      l_segment1_RightPerpVec = osg::Vec2f( - l_segment1_RightPerpVec.y(), l_segment1_RightPerpVec.x() );

      l_rightNormal = l_segment0_RightPerpVec + l_segment1_RightPerpVec;
      vfc::nop(l_rightNormal.normalize());
    }

    l_leftNormal = -l_rightNormal;

    l_toLineLeftEdge  = l_leftNormal  * l_width * 0.5f;
    l_toLineRightEdge = l_rightNormal * l_width * 0.5f;


    const vfc::uint32_t l_vertexIndexTemp  = 24u * 0u + l_vertexIndex * 2u;

    if (true == f_shadow)
    {
      (*l_vertexData.Colors)   [l_vertexIndexTemp  + 0u].set( g_settings->m_shadowColor.r(), g_settings->m_shadowColor.g(), g_settings->m_shadowColor.b(), g_settings->m_shadowColor.a() );
      (*l_vertexData.Colors)   [l_vertexIndexTemp  + 1u].set( g_settings->m_shadowColor.r(), g_settings->m_shadowColor.g(), g_settings->m_shadowColor.b(), g_settings->m_shadowColor.a() );
      (*l_vertexData.Vertices) [l_vertexIndexTemp  + 0u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineLeftEdge,  g_settings->m_shadowHeight ) );
      (*l_vertexData.Vertices) [l_vertexIndexTemp  + 1u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineRightEdge, g_settings->m_shadowHeight ) );
    }
    else
    {
      (*l_vertexData.Colors)   [l_vertexIndexTemp  + 0u].set( g_settings->m_color.r(), g_settings->m_color.g(), g_settings->m_color.b(), g_settings->m_color.a() );
      (*l_vertexData.Colors)   [l_vertexIndexTemp  + 1u].set( g_settings->m_color.r(), g_settings->m_color.g(), g_settings->m_color.b(), g_settings->m_color.a() );
      (*l_vertexData.Vertices) [l_vertexIndexTemp  + 0u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineLeftEdge,  g_settings->m_height ) );
      (*l_vertexData.Vertices) [l_vertexIndexTemp  + 1u].set( osg::Vec3f( l_vertexArray[l_vertexIndex] + l_toLineRightEdge, g_settings->m_height ) );
    }
    (*l_vertexData.TexCoords)[l_vertexIndexTemp  + 0u].set( 0.0f, 0.5f );
    (*l_vertexData.TexCoords)[l_vertexIndexTemp  + 1u].set( 1.0f, 0.5f );
  }

  (*l_vertexData.Normals)[0u] = osg::Vec3f(0.0f, 0.0f, 1.0f);
  l_vertexData.Indices->clear();

  for (vfc::uint32_t l_quadIndex = 0u; l_quadIndex < (26u - 1u); l_quadIndex++)
  {
    const vfc::uint32_t l_vertexIndexTemp = l_quadIndex * 2u;
    // Top left triangle
    l_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndexTemp + 1u));
    l_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndexTemp + 0u));
    l_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndexTemp + 2u));
    // Bottom right triangle
    l_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndexTemp + 2u));
    l_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndexTemp + 3u));
    l_vertexData.Indices->push_back(static_cast<vfc::uint16_t>(l_vertexIndexTemp + 1u));
  }

  return l_wheelGeometry;
}

void SeeThroughBonnet::updateWheelSteering()
{
  const vfc::float32_t l_wheelAxisHeight = g_settings->m_wheelRadius;
  const vfc::float32_t l_wheelBase = pc::vehicle::g_mechanicalData->m_wheelbase;
  const vfc::float32_t l_halfFrontTrack = pc::vehicle::g_mechanicalData->m_trackFront * 0.5f;

  m_leftTransform->setPivotPoint(osg::Vec3f(l_wheelBase, l_halfFrontTrack, 0.01f));
  m_leftTransform->setAttitude(osg::Quat(m_steeringAngle_rad, osg::Vec3f(0.0f, 0.0f, 1.0f)));
  m_leftTransform->setPosition(osg::Vec3f(l_wheelBase, l_halfFrontTrack, 0.01f));

  m_leftWheelTransform->setPivotPoint(osg::Vec3f(0.0f, 0.0f, 0.0f));
  m_leftWheelTransform->setPosition(osg::Vec3f(l_wheelBase, l_halfFrontTrack-0.04f, l_wheelAxisHeight));
  m_leftWheelTransform->setAttitude(osg::Quat(0.0f, osg::Y_AXIS));
  m_leftWheelTransform->setAttitude(osg::Quat(m_steeringAngle_rad, osg::Z_AXIS));

  m_rightTransform->setPivotPoint(osg::Vec3f(l_wheelBase, -l_halfFrontTrack, 0.01f));
  m_rightTransform->setAttitude(osg::Quat(m_steeringAngle_rad, osg::Vec3f(0.0f, 0.0f, 1.0f)));
  m_rightTransform->setPosition(osg::Vec3f(l_wheelBase, -l_halfFrontTrack, 0.01f));

  m_rightWheelTransform->setPivotPoint(osg::Vec3f(0.0f, 0.0f, 0.0f));
  m_rightWheelTransform->setPosition(osg::Vec3f(l_wheelBase, -l_halfFrontTrack+0.04f, l_wheelAxisHeight));
  m_rightWheelTransform->setAttitude(osg::Quat(0.0f, osg::Y_AXIS));
  m_rightWheelTransform->setAttitude(osg::Quat(m_steeringAngle_rad + osg::PI, osg::Z_AXIS));
}


void SeeThroughBonnet::updateWheelRotation()
{
  m_leftWheelTransform ->setAttitude(osg::Quat( m_wheelRotationAngle_rad, osg::Y_AXIS) * m_leftWheelTransform ->getAttitude());
  m_rightWheelTransform->setAttitude(osg::Quat(-m_wheelRotationAngle_rad, osg::Y_AXIS) * m_rightWheelTransform->getAttitude());
}

// bool SeeThroughBonnet::isMovementDirectionForward()
// {
//   const pc::daddy::DrivingDirDaddy* l_pData = m_pCustomFramework->m_drivingDirReceiver.getData();
//   if (pc::daddy::DRVDIR_NONE != l_pData)
//   {
//     vfc::int16_t l_movementDirection = l_pData->m_Data.m_MovementDirection_u8;

//     if (pc::daddy::DRVDIR_BACKWARD == l_movementDirection)
//     {
//       return false;
//     }
//     else
//     {
//       return true;
//     }
//   }
//   else
//   {
//   // if movement direction not available, assume false
//   return false;
//   }
// }


void SeeThroughBonnet::update(const osg::NodeVisitor* f_nv)
{
  if (f_nv == nullptr)
  {
      return;
  }
  const pc::daddy::SteeringAngleDaddy* const l_pData = m_pCustomFramework->m_steeringAngleFrontReceiver.getData();
  if (nullptr != l_pData)
  {
    vfc::CSI::si_radian_f32_t l_value_rad = l_pData->m_Data;
    const vfc::float32_t l_value = l_value_rad.value();
    if( false == isZero(m_steeringAngle_rad - l_value) )
    {
      m_steeringAngle_rad = l_value;
      (m_vertexData.Vertices)->dirty();
      dirtyBound();
    }
  }

  updateWheelSteering();

  const pc::daddy::DrivingDirDaddy* const l_drvDirDaddy = m_pCustomFramework->m_drivingDirReceiver.getData();
  if (nullptr != l_drvDirDaddy)
  {
    switch (l_drvDirDaddy->m_Data)
    {
    case pc::daddy::DRVDIR_FORWARD:
    {
      m_drivingForward = true;
      break;
    }
    case pc::daddy::DRVDIR_BACKWARD:
    {
      m_drivingForward = false;
      break;
    }
    default:
    {
      //! don't change anything
      break;
    }
    }
  }

  vfc::float32_t l_speed_kmPerHour = 0.0f;

  const pc::daddy::SpeedDaddy* const l_speedDaddy = m_pCustomFramework->m_speedReceiver.getData();
  if (nullptr != l_speedDaddy)
  {
    l_speed_kmPerHour = l_speedDaddy->m_Data;

    if ( (l_speed_kmPerHour >= 1.0f)/* && isMovementDirectionForward()*/)
    {
      const vfc::float32_t l_wheelPerimeter = 2.0f * std::abs(g_settings->m_wheelRadius) * static_cast<vfc::float32_t> (osg::PI);
      assert(0.0f < l_wheelPerimeter);
      const vfc::float32_t l_speed_metersPerSecond = l_speedDaddy->m_Data / 3.6f;
      const vfc::float32_t l_wheelRotationsPerSecond = l_speed_metersPerSecond / l_wheelPerimeter;

      const vfc::float64_t l_time = f_nv->getFrameStamp()->getSimulationTime();
      const vfc::float64_t l_timeDiff = l_time - m_lastFrameTime;
      m_lastFrameTime = l_time;

      vfc::float32_t l_wheelTurningAngleDiff_rad = l_wheelRotationsPerSecond * static_cast<vfc::float32_t>(l_timeDiff) * 2.0f * static_cast<vfc::float32_t>(osg::PI);
      if (!m_drivingForward)
      {
        l_wheelTurningAngleDiff_rad = -l_wheelTurningAngleDiff_rad;
      }
      m_wheelRotationAngle_rad += l_wheelTurningAngleDiff_rad;
      // Turning angle modulo 2pi to maximize precision
      m_wheelRotationAngle_rad = std::fmod(m_wheelRotationAngle_rad, static_cast<vfc::float32_t> (2.0f * osg::PI));

      updateWheelRotation();
    }
  }

  // transparency of wheels
  if (nullptr != m_pWheelAlphaUniform)
  {
    vfc::nop(m_pWheelAlphaUniform->set(1.0f));
    // if (m_pCustomFramework->m_VehTransparenceStsFromSM_Receiver.hasData())
    // {
    //     const cc::daddy::SVSVehTransStsDaddy_t*   l_vehTransStatus = m_pCustomFramework->m_VehTransparenceStsFromSM_Receiver.getData();
    //     if (nullptr!=l_vehTransStatus)
    //     {
    //         if (1u == l_vehTransStatus->m_Data)
    //         {
    //             vfc::nop(m_pWheelAlphaUniform->set(g_settings->m_wheelAlpha);
    //         }
    //     }
    // }
  }

  vfc::nop(m_pLightPositionUniform->set(g_settings->m_lightPosition));
}

osg::Vec2f SeeThroughBonnet::getTangentDirFromSplinePoint(vfc::uint32_t f_pointIndex, const osg::Vec2Array* fc_points)
{
  osg::Vec2f l_normal;
  if (0u == f_pointIndex)                          // First vertex
  {
    l_normal = (*fc_points)[f_pointIndex + 1u]
             - (*fc_points)[f_pointIndex + 0u];
  }
  else if (fc_points->size() - 1u == f_pointIndex)  // Last vertex
  {
    l_normal = (*fc_points)[f_pointIndex + 0u]
             - (*fc_points)[f_pointIndex - 1u];
  }
  else                                            //  Every other vertex
  {
    const osg::Vec2f l_segment0_RightPerpVec( (*fc_points)[f_pointIndex + 0u]
                                      - (*fc_points)[f_pointIndex - 1u]);

    const osg::Vec2f l_segment1_RightPerpVec( (*fc_points)[f_pointIndex + 1u]
                                      - (*fc_points)[f_pointIndex + 0u]);

    l_normal = l_segment0_RightPerpVec + l_segment1_RightPerpVec;
  }
  vfc::nop(l_normal.normalize());
  return l_normal;
}

osg::Vec2Array* SeeThroughBonnet::smoothSpline(const osg::Vec2Array* cf_points, uint16_t f_numOfSegments)
{
  if(cf_points->empty() == false)
  {
    osg::Vec2Array* const out_points = new osg::Vec2Array();
    out_points->push_back((*cf_points)[0u]);

    for(vfc::uint32_t i = 1u, l_contourSize = static_cast<vfc::uint32_t>(cf_points->size()); i < l_contourSize; ++i)
    {
      const osg::Vec2f dir0 = getTangentDirFromSplinePoint(i - 1u, cf_points);
      const osg::Vec2f dir1 = getTangentDirFromSplinePoint(i, cf_points);

      // different direction?
      if(false == isZero((dir0 - dir1).length()))
      {
        // find the ""best"" handle distance for smothing the curve
        const vfc::float32_t dist = ((*cf_points)[i - 1u] - (*cf_points)[i]).length();
        const vfc::float32_t smoothDistance = dist * 0.4f;  //!  magic number
        const osg::Vec2f l_handle0 = (*cf_points)[i - 1u] + (dir0 * smoothDistance);
        const osg::Vec2f l_handle1 = (*cf_points)[i] - (dir1 * smoothDistance);

        cc::util::beziercurve::BezierCurve l_bezierCurve;
        l_bezierCurve.setControlPoints((*cf_points)[i - 1u], (*cf_points)[i]);
        l_bezierCurve.setHandlePoints(l_handle0, l_handle1);
        l_bezierCurve.generateVertices(out_points, f_numOfSegments, false); // PRQA S 2759
      }
      else  //! no need to curve
      {
        out_points->push_back((*cf_points)[i]);
      }
    }
    return out_points;
  }
  return new osg::Vec2Array();
}

//first index in vehicle contour to be behind the requested Position
vfc::uint32_t SeeThroughBonnet::indexBehindPosX(osg::Vec2Array* f_VehicleContour_Side, vfc::uint32_t f_from, vfc::uint32_t f_to, vfc::float32_t f_posToCompare) const
{
  for(vfc::uint32_t i = f_from; i < f_to; ++i)
  {
    if((*f_VehicleContour_Side)[i].x() < f_posToCompare)
    {
      return i;
    }
  }
  return f_to;
}


osg::Vec2f SeeThroughBonnet::findIntersection(osg::Vec2Array* f_VehicleContour_Side, vfc::uint32_t f_index1, vfc::uint32_t f_index2,  vfc::float32_t f_edgeX) const
{
  if(0u == f_index1)
  {
    return (*f_VehicleContour_Side)[0u];
  }
  if(f_VehicleContour_Side->size() - 1u == f_index2)
  {
    return  (*f_VehicleContour_Side)[f_VehicleContour_Side->size() - 1u];
  }

  osg::Vec2f pos1 = (*f_VehicleContour_Side)[f_index1];
  const osg::Vec2f pos2 = (*f_VehicleContour_Side)[f_index2];
  const osg::Vec2f dir = pos1 - pos2;
  const vfc::float32_t percent = (pos1.x() - f_edgeX) / (dir.length());
  osg::Vec2f intersection(pos1 + (dir * percent));
  return intersection;
}


SeeThroughBonnet::~SeeThroughBonnet() = default;


} // namespace stb
} // namespace assets
} // namespace cc
