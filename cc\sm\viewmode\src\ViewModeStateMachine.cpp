//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  ViewModeStateMachine.cpp
/// @brief
//=============================================================================

#include "cc/sm/viewmode/inc/ViewModeStateMachine.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b.h"
#include "cc/core/inc/CustomParamData.h"


#include <cstdlib> // PRQA S 1060
#include "OpenThreads/Condition" // PRQA S 1060
#include <cassert> //debug mode assertions // PRQA S 1060
#include <string>
#include <sstream> //stringstream
#include <iostream>
#include <iomanip>

#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/Engine.h"
#include "pc/svs/daddy/inc/BaseDaddyPorts.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/math/inc/CommonMath.h" //updateIfGreater
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "cc/util/logging/inc/LoggingContexts.h"
#include "cc/assets/uielements/inc/Utils.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/sm/viewmode/inc/ViewModeNames.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "cc/assets/uielements/inc/VehicleTransIcon.h"

#include "cc/util/pdmwriter/inc/PdmWriter.h"

#ifndef DEBUG
#include "cc/core/inc/FileHashChecker.h"
#endif

#define POCTEST 0
#define EXTRINSIC_UPDATE 0

using cc::util::logging::g_viewModeSMContext;
enum class EState : vfc::int8_t
{
  STATE_OFF   = -1,
  STATE_UNDEF = 0,
  STATE_ON    = 1
};

pc::util::coding::Item<StateMachineParameters> g_stateMachineParams("ViewModeStateMachine");

pc::util::coding::Item<StateMachineLogSettings> g_stateMachineLogSettings("ViewModeSMLogs");

#define ACTIVATE_LOGGING 0
#ifdef TARGET_STANDALONE
  #define IMGUI_VIEW_MODE_DEBUG 1
#else
  #define IMGUI_VIEW_MODE_DEBUG 0
#endif
// Logging
#define VIEWMODE_SM_LOG(component_name) \
  if (g_stateMachineLogSettings->m_log##component_name) XLOG_INFO_OS(g_viewModeSMContext)

// the matlab stateflow state machine object,
// can't be declared in the header to avoid conflict with other matlab state machine types
static ViewModeStateFlowStateMachineModelClass s_ViewModeStateMachine ; // PRQA S 2300

void StepModerator::pushTimeStep(vfc::float64_t f_timeStep)
{
  m_timeStepQueue.push(f_timeStep);
  const vfc::float64_t queueSize = static_cast<vfc::float64_t>(m_timeStepQueue.size());
  if (queueSize > static_cast<vfc::float64_t>(s_CycleQueueSize))
  {
    m_averageTimeStep = (m_averageTimeStep * (queueSize - 1.0) - m_timeStepQueue.front() + m_timeStepQueue.back()) / (queueSize - 1.0);
    m_timeStepQueue.pop();
  }
  else
  {
    m_averageTimeStep = (m_averageTimeStep * (queueSize - 1.0) + f_timeStep) / queueSize;
  }
}

vfc::int32_t StepModerator::calculateModeratedTimeStepCounter(vfc::int32_t f_stepCount, vfc::float64_t f_referenceTimeStep)
{
  const vfc::float64_t ratio = f_referenceTimeStep / m_averageTimeStep;
  const vfc::float64_t moderatedCounter = static_cast<vfc::float64_t>(f_stepCount) * ratio;
  return static_cast<vfc::int32_t>(std::floor(moderatedCounter));
}


ViewModeStateMachine::ViewModeStateMachine( pc::core::Framework* f_pFramework )
: ICyclicRunnable{ std::chrono::milliseconds(20u) }
, m_lastTime{std::chrono::steady_clock::now()}
, m_stepTime{10.0}
, m_Timer{}
, m_pFramework{f_pFramework}
, m_isStr{false}
, m_indicatorStateSMReceiver{}
, m_PIVI_ManualVideoSetupReqSMReceiver{}
, m_RequestViewIdSMReceiver{}
, m_PowerModeSMReceiver{}
, m_HUShoWReqSMReceiver{}
, m_HUselSVSModeSMReceiver{}
, m_HUtouchEvenTypeSMReceiver{}
, m_huVehColorReqSMReceiver{}
, m_ParkStatusSMReceiver{}
, m_ParkTypeSMReceiver{}
, m_ParkTypeVariantSMReceiver{}
, m_ParkModeSMReceiver{}
, m_parkHmiParkAPAPARKMODESMReceiver{}
, m_ParkSpaceSMReceiver{}
, m_parkHmiParkDriverIndSMReceiver{}
, m_parkHmiParkDriverIndExtSMReceiver{}
, m_parkHmiParkRecoverIndSMReceiver{}
, m_ParkReqReleaseBtnSMReceiver{}
, m_ParkRPADriverSelectedSMReceiver{}
, m_ParkDriverIndSearchSMReceiver{}
, m_FreeParkingActiveSMReceiver{}
, m_ParkDisp2TouchStsSMReceiver{}
, m_CameraPositionSMReceiver{}
, m_DegradationFidSMReceiver{}
, m_VariantSMReceiver{}
, m_touchCoordinateReceiver{}
, m_shoWSuspendReceiver{}
, m_VRSwitchSVMReceiver{}
, m_FCP_SVMButtonPressedReceiver{}
, m_CloseButtonPressedReceiver{}
, m_EnlargeButtonPressedReceiver{}
, m_FloatViewChangeButtonPressedReceiver{}
, m_PasWarnToneSMReceiver{}
, m_CustomVehicleLightSMReceiver{}
, m_HUDislayModeSwitchSMReceiver{}
, m_HUDislayModeExpandSMReceiver{}
, m_HUImageWorkModeSMReceiver{}
, m_HUDislayModeExpandNewSMReceiver{}
, m_HURotateStatusSMReceiver{}
, m_HUvehTransSMReceiver{}
, m_calibStateRecevier{}
, m_AndroidIconActiveRecevier{}
, m_pdmAutoCamStsSMReceiver{}
, m_pdmVehTransStsSMReceiver{}
, m_pdmVehColorStsSMReceiver{}
, m_huMODStsSMReceiver{}
, m_huDGearActStsSMReceiver{}
, m_huPASActStsSMReceiver{}
, m_huSteerActStsSMReceiver{}
, m_huNarrowLaneActSMReceiver{}
, m_NFSM_ViewBufferStatusViewModeSMReceiver{}
, m_ParkUISpotSMReceiver{}
, m_ParkConfirmInterfaceExistSMReceiver{}
, m_ParkUIAvailableParkingSlotSMReceiver{}
, m_PIVI_ViewBufferStatusACKSMReceiver{}
, m_animationStateSMReceiver{}
, m_MirrorStateViewModeSMReceiver{}
, m_DoorStateViewModeSMReceiver{}
, m_GearViewModeSMReceiver{}
, m_VehicleSpeedViewModeSMReceiver{}
, m_degradationMaskSMReceiver{}
, m_SonarDistRangeSMReceiver{}
, m_SonarDistTrigLevelSMReceiver{}
, m_voiceDockRequestSMReceiver{}
, m_SRIsActiveSMReceiver{}
, m_competeActiveAllowReceiver{}
, m_competeQuitReceiver{}
, m_dockAvmButtonPressReceiver{}
, m_driverSteeringWheelReceiver{}
, m_APAFuncStatusReceiver{}
, m_systemStrSMReceiver{}
, m_ADCUPowerSavModeSts_ReceiverPort{}
, m_backKeyEventSMReceiver{}
, m_steeringWheelButtonDefinitionSMReceiver{}
, m_steeringWheelButtonPressSMReceiver{}
, m_ModWarningSMReceiver{}
, m_ModWarningOverlayDisplaySMReceiver{}
, m_engineTimeOutSMReceiver{}
, m_androidAliveSMReceiver{}
, m_avmSoftwareErrorSMReceiver{}
, m_avmFileErrorSMReceiver{}
, m_SRIsTopActivitySMReceiver{}
, m_cameraPositionChangeReceiver{}
, m_settingPageVehicleTransReceiver{}
, m_CurrMode{}
, m_preModeFree{false}
, m_hasEverActivated{false}
, m_autoCamPdmWasRead{false}
, m_vehTransPdmWasRead{false}
, m_vehColorPdmWasRead{false}
, m_currSplineOverlaysEnabled{}
, m_receivedAutoVideoSetupReqAck{}
, m_receivedManualVideoSetupReq{}
, m_stepCounter{}
, m_currOverlayStatus{}
, m_frontTriggerDist{}
, m_prevViewBufferStatus{}
, m_prevMovementDirection{}
, m_prevThreatTriggerStatus{}
, m_prevLSMGActivationSetStat{}
, m_stepModerator{}
{
}


void ViewModeStateMachine::OnInit( void )    // PRQA S 6044 // PRQA S 2755
{
  // pf code. #code looks fine
  // XLOG_INFO(g_viewModeSMContext, "Starting ViewMode State Machine thread.");
  //! Connect Daddy receiver ports (PC)
  pc::daddy::BaseDaddyPorts::sm_DoorOpenDaddySenderPort.connect( m_DoorStateViewModeSMReceiver ) ;
  pc::daddy::BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.connect( m_MirrorStateViewModeSMReceiver ) ;
  pc::daddy::BaseDaddyPorts::sm_gearSenderPort.connect( m_GearViewModeSMReceiver ) ;
  pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.connect( m_VehicleSpeedViewModeSMReceiver );
  pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.connect(m_degradationMaskSMReceiver);
  pc::daddy::BaseDaddyPorts::sm_indicatorStateSenderPort.connect(m_indicatorStateSMReceiver);

  //! Connect Daddy receiver ports (CC)
//   cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.connect( m_UssDataViewModeSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.connect( m_PIVI_ManualVideoSetupReqSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort.connect( m_RequestViewIdSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.connect( m_PowerModeSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort.connect( m_HUShoWReqSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort.connect( m_HUselSVSModeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.connect( m_HUtouchEvenTypeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort.connect( m_huVehColorReqSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.connect( m_ParkStatusSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort.connect( m_ParkTypeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort.connect( m_ParkTypeVariantSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.connect( m_ParkModeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.connect( m_parkHmiParkAPAPARKMODESMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.connect( m_ParkSpaceSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.connect( m_parkHmiParkDriverIndSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.connect( m_parkHmiParkDriverIndExtSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.connect( m_parkHmiParkRecoverIndSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort.connect( m_ParkReqReleaseBtnSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkRPADriverSelectedDaddy_SenderPort.connect( m_ParkRPADriverSelectedSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.connect( m_ParkDriverIndSearchSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.connect( m_FreeParkingActiveSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.connect( m_ParkDisp2TouchStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_CameraPositionDaddySenderPort.connect( m_CameraPositionSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.connect( m_DegradationFidSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_Variant_SenderPort.connect( m_VariantSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.connect( m_touchCoordinateReceiver );
  cc::daddy::CustomDaddyPorts::sm_HUShoWSuspendDaddy_SenderPort.connect( m_shoWSuspendReceiver );
  cc::daddy::CustomDaddyPorts::sm_PIVI_ViewBufferStatusACK_SenderPort.connect( m_PIVI_ViewBufferStatusACKSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ViewBufferStatus_SenderPort.connect( m_NFSM_ViewBufferStatusViewModeSMReceiver ) ; // internal port, different to BCC
  cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.connect( m_animationStateSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort.connect( m_ParkUISpotSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SVSParkConfirmInterfaceDataDaddy_SenderPort.connect( m_ParkConfirmInterfaceExistSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort.connect( m_ParkUIAvailableParkingSlotSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort.connect(m_VRSwitchSVMReceiver);
  cc::daddy::CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort.connect(m_FCP_SVMButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_CloseButtonPressedDaddy_SenderPort.connect(m_CloseButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_EnlargeButtonPressedDaddy_SenderPort.connect(m_EnlargeButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort.connect(m_FloatViewChangeButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_PasWarnToneDaddy_SenderPort.connect(m_PasWarnToneSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.connect(m_CustomVehicleLightSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.connect( m_HUDislayModeSwitchSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandDaddy_SenderPort.connect( m_HUDislayModeExpandSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUImageWorkModeDaddy_SenderPort.connect( m_HUImageWorkModeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.connect( m_HUDislayModeExpandNewSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.connect( m_HURotateStatusSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.connect( m_HUvehTransSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_calibState_SenderPort.connect( m_calibStateRecevier ) ;
  cc::daddy::CustomDaddyPorts::sm_androidIconActive_SenderPort.connect(m_AndroidIconActiveRecevier);

  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_AutoCamStsDaddy_SenderPort.connect( m_pdmAutoCamStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort.connect( m_pdmVehTransStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort.connect( m_pdmVehColorStsSMReceiver ) ;

  cc::daddy::CustomDaddyPorts::sm_HU_MODStsDaddy_SenderPort.connect( m_huMODStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_DGearActStsDaddy_SenderPort.connect( m_huDGearActStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_PASActStsDaddy_SenderPort.connect( m_huPASActStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_SteerActStsDaddy_SenderPort.connect( m_huSteerActStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_NarrowLaneActDaddy_SenderPort.connect( m_huNarrowLaneActSMReceiver ) ;

  cc::daddy::CustomDaddyPorts::sm_SonarAPPData_SenderPort.connect( m_SonarDistRangeSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SonarDistTrigLevel_SenderPort.connect( m_SonarDistTrigLevelSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_voiceDockRequest_SenderPort.connect ( m_voiceDockRequestSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SRIsActive_SenderPort.connect ( m_SRIsActiveSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_competeActiveAllow_SenderPort.connect( m_competeActiveAllowReceiver );
  cc::daddy::CustomDaddyPorts::sm_competeQuit_SenderPort.connect( m_competeQuitReceiver );
  cc::daddy::CustomDaddyPorts::sm_dockAvmButtonPress_SenderPort.connect( m_dockAvmButtonPressReceiver );
  cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.connect(m_driverSteeringWheelReceiver);
  cc::daddy::CustomDaddyPorts::sm_APAFuncStatus_SenderPort.connect(m_APAFuncStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_systemStr_SenderPort.connect(m_systemStrSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_ADCUPowerSavModeSts_SenderPort.connect(m_ADCUPowerSavModeSts_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_backKeyEvent_SenderPort.connect( m_backKeyEventSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonPress_SenderPort.connect(m_steeringWheelButtonPressSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonDefinition_SenderPort.connect(m_steeringWheelButtonDefinitionSMReceiver);
//   cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.connect( m_cpcStatusSMReceiverport ) ;
  cc::daddy::CustomDaddyPorts::sm_ModStateDaddy.connect(m_ModWarningSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_ModWarningOverlayDisplay_SenderPort.connect(m_ModWarningOverlayDisplaySMReceiver);
  cc::daddy::CustomDaddyPorts::sm_avmSoftwareError_SenderPort.connect(m_avmSoftwareErrorSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.connect(m_avmFileErrorSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_engineTimeOut_SenderPort.connect(m_engineTimeOutSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_androidAlive_SenderPort.connect(m_androidAliveSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_SRIsTop_SenderPort.connect(m_SRIsTopActivitySMReceiver);
  cc::daddy::CustomDaddyPorts::sm_CameraPositionChange_SenderPort.connect(m_cameraPositionChangeReceiver);
  cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.connect(m_settingPageVehicleTransReceiver);
  //! init state machine
  s_ViewModeStateMachine.initialize() ;

  //! set initial parameters
  //! default values for vehicle bus signals
  s_ViewModeStateMachine.setIn_HUViewReq(EScreenID_NO_CHANGE);
  s_ViewModeStateMachine.setIn_HUSVSMode(ESVSViewMode_VM_Default);
  s_ViewModeStateMachine.setIn_TouchSt(ETouchStatus_NONE);
  s_ViewModeStateMachine.setIn_HUTouchCoorX(0u);
  s_ViewModeStateMachine.setIn_HUTouchCoorY(0u);
  s_ViewModeStateMachine.setIn_gear(EGear_P); // PRQA S 3080
  s_ViewModeStateMachine.setIn_vehSpeed(static_cast<vfc::float64_t>(0));
  s_ViewModeStateMachine.setIn_powerMode(EPowerMode_RUNNING); // PRQA S 3080
  s_ViewModeStateMachine.setIn_HUDislayModeSwitch(DISPLAY_MODE_TURNOFF);
  s_ViewModeStateMachine.setIn_HUImageWorkMode(WORK_MODE_2DMODE);
  s_ViewModeStateMachine.setIn_HUDislayModeExpand(DISPLAY_EXP_INVALID);
  s_ViewModeStateMachine.setIn_HUDislayModeExpandNew(DISPLAY_EXPNEW_INVALID);
//   s_ViewModeStateMachine.setIn_huVehColor(EVehColor_INVALID); // PRQA S 3080

//   //! default values for object
//   s_ViewModeStateMachine.setIn_distF(double(10));
//   s_ViewModeStateMachine.setIn_distR(double(10));

  //! default values for free view handler
  s_ViewModeStateMachine.setIn_campositionX((uint32_T)(0)); // PRQA S 3080
  s_ViewModeStateMachine.setIn_campositionY((uint32_T)(0)); // PRQA S 3080
  // s_ViewModeStateMachine.setIn_zoomSts((uint8_T)(0));

  //! default values for parking
  s_ViewModeStateMachine.setIn_parkstatus(static_cast<boolean_T>(false)); // PRQA S 3080
//   s_ViewModeStateMachine.setIn_parktype(EParkTypeIn_None); // PRQA S 3080
//   s_ViewModeStateMachine.setIn_parkmode(EParkMode_EPARKMODE_NONE); // PRQA S 3080
//   s_ViewModeStateMachine.setIn_parkreqreleasebtn(false);

  //! default values for degradation
  // s_ViewModeStateMachine.setIn_FID_SVSCamFrontSt(true);
  // s_ViewModeStateMachine.setIn_FID_SVSCamLeftSt(true);
  // s_ViewModeStateMachine.setIn_FID_SVSCamRearSt(true);
  // s_ViewModeStateMachine.setIn_FID_SVSCamRightSt(true);
  s_ViewModeStateMachine.setIn_FID_SVSEcuInternalStatus(static_cast<boolean_T>(true));
  s_ViewModeStateMachine.setIn_IMB_FrontSt(static_cast<boolean_T>(false));
  s_ViewModeStateMachine.setIn_IMB_LeftSt(static_cast<boolean_T>(false));
  s_ViewModeStateMachine.setIn_IMB_RearSt(static_cast<boolean_T>(false));
  s_ViewModeStateMachine.setIn_IMB_RightSt(static_cast<boolean_T>(false));

  //! CPC status
  s_ViewModeStateMachine.setIn_isCpcActive(static_cast<boolean_T>(false));
  s_ViewModeStateMachine.setIn_isCpcFuncOn(static_cast<boolean_T>(true));

  //! default values for pdm
//   s_ViewModeStateMachine.setIn_pdmVehTrans_(EPdmSetting_DISABLED);
//   s_ViewModeStateMachine.setIn_pdmVehColor(EVehColor_WISDOM_BLUE);  // set blue as default in HCEF DFC vehicle

  //!set coding params
//   s_ViewModeStateMachine.setIn_distTrigIn(static_cast<double>(g_stateMachineParams->m_distTrigIn_m));
//   s_ViewModeStateMachine.setIn_distTrigOut(static_cast<double>(g_stateMachineParams->m_distTrigOut_m));
  s_ViewModeStateMachine.setIn_speedTrigIn(static_cast<vfc::float64_t>(g_stateMachineParams->m_speedTrigIn_kph));
  s_ViewModeStateMachine.setIn_speedTrigOut(static_cast<vfc::float64_t>(g_stateMachineParams->m_speedTrigOut_kph));
  s_ViewModeStateMachine.setIn_threatDuration(static_cast<uint32_T>(g_stateMachineParams->m_threatDuration));
  s_ViewModeStateMachine.setIn_parkingScreenDelay(static_cast<uint32_T>(g_stateMachineParams->m_parkingScreen_Delay_ms));
  s_ViewModeStateMachine.setIn_parkingGuidanceScreenDelay(static_cast<uint32_T>(g_stateMachineParams->m_parkingGuidanceScreen_Delay_ms));
//   s_ViewModeStateMachine.setIn_firstShowReqDelay(static_cast<uint32_T>(g_stateMachineParams->m_firstShowReq_Delay_ms));
  s_ViewModeStateMachine.setIn_smDelay(static_cast<uint32_T>(g_stateMachineParams->m_sm_Delay_ms));
//   s_ViewModeStateMachine.setIn_IndicatorBackToFrontViewDelay(static_cast<uint32_T>(g_stateMachineParams->m_sm_IndicatorBackToFrontView_Delay_ms));
  s_ViewModeStateMachine.setIn_cpcDelay(static_cast<uint32_T>(g_stateMachineParams->m_sm_CpcShow_Delay_s));
  s_ViewModeStateMachine.setIn_huSteeringAngleTrigIn(g_stateMachineParams->m_steeringAngleTrigIn);
  s_ViewModeStateMachine.setIn_huSteeringAngleTrigOut(g_stateMachineParams->m_steeringAngleTrigOut);
  HmiUIElements l_CpcSwitchLayouts; // PRQA S 4102
  l_CpcSwitchLayouts.CPCSwitchFrontView.iconCenter.x =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchFrontView_Pos.x()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchFrontView.iconCenter.y =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchFrontView_Pos.y()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchFrontView.responseArea.x =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchResponseArea.x()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchFrontView.responseArea.y =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchResponseArea.y()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchTopView.iconCenter.x =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchTopView_Pos.x()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchTopView.iconCenter.y =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchTopView_Pos.y()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchTopView.responseArea.x =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchResponseArea.x()); // PRQA S 3016
  l_CpcSwitchLayouts.CPCSwitchTopView.responseArea.y =  static_cast<uint16_T>(g_stateMachineParams->m_CPCOverlaySwitchResponseArea.y()); // PRQA S 3016
  s_ViewModeStateMachine.setIn_CPCSwitch_Layouts(l_CpcSwitchLayouts);
  s_ViewModeStateMachine.setIn_ignoreCompete(static_cast<boolean_T>(g_ignoreScreenCompete));
  ExitDelay l_customExitDelay; // PRQA S 4102
  l_customExitDelay.ActiveExitDelay = g_stateMachineParams->m_ActiveExitDelay;// 3 min
  l_customExitDelay.PassiveExitDelay = g_stateMachineParams->m_PassiveExitDelay;//1 min
  l_customExitDelay.WarningExitDelay = g_stateMachineParams->m_WarningExitDelay;//5 s
  s_ViewModeStateMachine.setIn_exitDelay(l_customExitDelay);
  s_ViewModeStateMachine.setIn_ParkingSearch( false );
  //! default values (other)
  // s_ViewModeStateMachine.setIn_variantSVSCoded(true);

  //speed to be activate or deactivate
  s_ViewModeStateMachine.setIn_speedTrigIn(18.0);
  s_ViewModeStateMachine.setIn_speedTrigOut(21.0);

  // toast-avm Error
  s_ViewModeStateMachine.setIn_AVMError(static_cast<boolean_T>(false));

  s_ViewModeStateMachine.setIn_PowerSaveMode(static_cast<boolean_T>(false));

  m_stepCounter = 0;

  #ifndef DEBUG
  //check file
  cc::util::filehashchecker::FileHashCheckerManager&fileChecker=  cc::util::filehashchecker::FileHashCheckerManager::getFileHashCheckerManager();
  if(!fileChecker.checkAll())
  {
        if (cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.isConnected())
        {
            cc::daddy::AvmFileErrorDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.reserve();
            l_container.m_Data = true;
            cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.deliver();
            XLOG_INFO( cc::util::logging::g_viewModeSMContext , "File check failed !!!");
        }
  }
  else
  {
        XLOG_INFO( cc::util::logging::g_viewModeSMContext , "File check success !!!");
  }
//   if(!fileChecker.checkExtrinsic())
//   {
//         if (cc::daddy::CustomDaddyPorts::sm_extrinsicFileError_SenderPort.isConnected())
//         {
//             cc::daddy::ExtrinsicFileErrorDaddy_t& l_container = cc::daddy::CustomDaddyPorts::sm_extrinsicFileError_SenderPort.reserve();
//             l_container.m_Data = true;
//             cc::daddy::CustomDaddyPorts::sm_extrinsicFileError_SenderPort.deliver();
//             XLOG_INFO_OS( cc::util::logging::g_viewModeSMContext ) << "Extrinsic check failed !!!"<< XLOG_ENDL;
//         }
//   }
//   else
//   {
//         XLOG_INFO_OS( cc::util::logging::g_viewModeSMContext ) << "Extrinsic check success !!!"<< XLOG_ENDL;
//   }
  #endif
}


void ViewModeStateMachine::OnIterate( void )         // PRQA S 6040  // PRQA S 6041  // PRQA S 6044 // PRQA S 2755
{
  const pc::daddy::DoorStateDaddy * l_pCDoors = nullptr ; //a ptr to const data
  const pc::daddy::MirrorStateDaddy * l_pCSideMirrors = nullptr ;
  // const cc::daddy::DegradationFid_t * l_fids = 0 ;

#ifndef TARGET_STANDALONE
  if (m_stepCounter < g_stateMachineParams->m_ignoreCompeteStepCounter) // PRQA S 3000
  {
    s_ViewModeStateMachine.setIn_ignoreCompete(static_cast<boolean_T>(true));
    m_stepCounter++;
  }else
  {
    s_ViewModeStateMachine.setIn_ignoreCompete(static_cast<boolean_T>(g_ignoreScreenCompete));
  }
#endif

  //! Update TimeStep
  m_stepTime = std::chrono::duration_cast<std::chrono::milliseconds>(
    std::chrono::steady_clock::now() - m_lastTime).count(); // PRQA S 3011
  m_lastTime = std::chrono::steady_clock::now();
  m_stepModerator.pushTimeStep(m_stepTime);

  const vfc::float64_t l_timeStepScaleFactor = m_stepModerator.getStepScaleFactor(10.0 /*ms*/);

  s_ViewModeStateMachine.setIn_timeStepScaleFactor(l_timeStepScaleFactor);

  //! Update Daddy ports.
  m_DoorStateViewModeSMReceiver.update() ;
  m_MirrorStateViewModeSMReceiver.update() ;
  m_GearViewModeSMReceiver.update() ;
  m_VehicleSpeedViewModeSMReceiver.update();
//   m_UssDataViewModeSMReceiver.update();
  m_degradationMaskSMReceiver.update();
  m_PIVI_ManualVideoSetupReqSMReceiver.update();
  m_RequestViewIdSMReceiver.update();
  m_PowerModeSMReceiver.update();
  m_HUShoWReqSMReceiver.update();
  m_HUselSVSModeSMReceiver.update();
  m_HUtouchEvenTypeSMReceiver.update();
  m_huVehColorReqSMReceiver.update();
  m_ParkStatusSMReceiver.update();
  m_ParkTypeSMReceiver.update();
  m_ParkTypeVariantSMReceiver.update();
  m_ParkModeSMReceiver.update();
  m_parkHmiParkAPAPARKMODESMReceiver.update();
  m_ParkSpaceSMReceiver.update();
  m_parkHmiParkDriverIndSMReceiver.update();
  m_parkHmiParkDriverIndExtSMReceiver.update();
  m_parkHmiParkRecoverIndSMReceiver.update();
  m_ParkReqReleaseBtnSMReceiver.update();
  m_CameraPositionSMReceiver.update();
  m_DegradationFidSMReceiver.update();
  m_VariantSMReceiver.update();
  m_touchCoordinateReceiver.update();
  m_shoWSuspendReceiver.update();
  m_PIVI_ViewBufferStatusACKSMReceiver.update();
  m_NFSM_ViewBufferStatusViewModeSMReceiver.update();
  m_ParkUISpotSMReceiver.update();
  m_ParkConfirmInterfaceExistSMReceiver.update();
  m_ParkUIAvailableParkingSlotSMReceiver.update();
  m_animationStateSMReceiver.update();
  m_VRSwitchSVMReceiver.update();
  m_FCP_SVMButtonPressedReceiver.update();
  m_indicatorStateSMReceiver.update();
  m_PasWarnToneSMReceiver.update();
  m_CustomVehicleLightSMReceiver.update();
  m_pdmAutoCamStsSMReceiver.update();
  m_pdmVehTransStsSMReceiver.update();
  m_pdmVehColorStsSMReceiver.update();
  m_HUImageWorkModeSMReceiver.update();
  m_HURotateStatusSMReceiver.update();
  m_HUDislayModeSwitchSMReceiver.update();
  m_HUDislayModeExpandSMReceiver.update();
  m_HUDislayModeExpandNewSMReceiver.update();
  m_HUvehTransSMReceiver.update();
  m_calibStateRecevier.update();
  m_AndroidIconActiveRecevier.update();
//   m_cpcStatusSMReceiverport.update();
  m_CloseButtonPressedReceiver.update();
  m_EnlargeButtonPressedReceiver.update();
  m_FloatViewChangeButtonPressedReceiver.update();
  m_huMODStsSMReceiver.update();
  m_huDGearActStsSMReceiver.update();
  m_huPASActStsSMReceiver.update();
  m_huSteerActStsSMReceiver.update();
  m_huNarrowLaneActSMReceiver.update();
  m_SonarDistRangeSMReceiver.update();
  m_SonarDistTrigLevelSMReceiver.update();
  m_voiceDockRequestSMReceiver.update();
  m_SRIsActiveSMReceiver.update();
  m_competeActiveAllowReceiver.update();
  m_competeQuitReceiver.update();
  m_dockAvmButtonPressReceiver.update();
  m_driverSteeringWheelReceiver.update();
  m_APAFuncStatusReceiver.update();
  m_systemStrSMReceiver.update();
  m_ADCUPowerSavModeSts_ReceiverPort.update();
  m_backKeyEventSMReceiver.update();
  m_steeringWheelButtonPressSMReceiver.update();
  m_steeringWheelButtonDefinitionSMReceiver.update();
  m_ModWarningSMReceiver.update();
  m_ModWarningOverlayDisplaySMReceiver.update();
  m_avmSoftwareErrorSMReceiver.update();
  m_avmFileErrorSMReceiver.update();
  m_engineTimeOutSMReceiver.update();
  m_androidAliveSMReceiver.update();
  m_SRIsTopActivitySMReceiver.update();
  m_cameraPositionChangeReceiver.update();
  m_settingPageVehicleTransReceiver.update();
  //! Reserve Daddy senders.
  cc::daddy::SystemStateDaddy& l_rDaddySystemStateContainer =
    cc::daddy::CustomDaddyPorts::sm_systemState_SenderPort.reserve() ;

  cc::daddy::SVSCurrentViewDaddy_t& l_rSVSCurrentViewContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort.reserve();

  cc::daddy::SVSShowReqDaddy_t& l_rSVSShowReqContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSShowReqDaddy_SenderPort.reserve() ;

//  cc::daddy::VRSwitchFailStDaddy_t& l_rVRSwitchFailStContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_VRSwitchFailStDaddy_SenderPort.reserve();

  cc::daddy::SVSViewModeStsDaddy_t& l_rSVSViewModeStsContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSViewModeStsDaddy_SenderPort.reserve() ;

  // cc::daddy::SVSUnavlMsgsDaddy_t& l_rSVSUnavlMsgsContainer =
  //   cc::daddy::CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort.reserve() ;

  cc::daddy::VMStateDaddy_t& l_rDaddyStateContainer =
    cc::daddy::CustomDaddyPorts::sm_ViewModeState_SenderPort.reserve() ;

  // cc::daddy::SVSzoomStsDaddy_t& l_rSVSzoomStsContainer =
  //   cc::daddy::CustomDaddyPorts::sm_SVSzoomStsDaddy_SenderPort.reserve() ;

  cc::daddy::SVSViewStsDaddy_t& l_rSVSFrViewStsContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSFrViewStsDaddy_SenderPort.reserve() ;

  cc::daddy::SVSViewStsDaddy_t& l_rSVSLeViewStsContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSLeViewStsDaddy_SenderPort.reserve() ;

  cc::daddy::SVSViewStsDaddy_t& l_rSVSReViewStsContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSReViewStsDaddy_SenderPort.reserve() ;

  cc::daddy::SVSViewStsDaddy_t& l_rSVSRiViewStsContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSRiViewStsDaddy_SenderPort.reserve() ;

  cc::daddy::SVSDisplayedViewDaddy_t& l_rSVSDisplayedViewContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.reserve() ;

  cc::daddy::SVSFreeModeStDaddy_t& l_rSVSFreeModeStContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.reserve() ;

//  cc::daddy::PasButtonPressedStDaddy_t& l_rPasButtonPressedStContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_PasButtonPressedStDaddy_SenderPort.reserve() ;

//  cc::daddy::SettingAutoCamStDaddy_t& l_rSettingAutoCamStContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_AutoCamActivButtonPressedStDaddy_SenderPort.reserve() ;


//  cc::daddy::ParkModeSelectedStDaddy_t& l_rParkModeSelectedStContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_ParkModeSelectedStDaddy_SenderPort.reserve() ;

  #ifndef USE_VIRTUAL_OBJECT
    cc::daddy::ParkSlotSelectedStDaddy_t& l_rParkSlotSelectedStContainer =
      cc::daddy::CustomDaddyPorts::sm_ParkSlotSelectedStDaddy_SenderPort.reserve() ;
  #endif

//  cc::daddy::ParkPauseButton_t& l_rParkGuidPauseContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_ParkPauseButtonStDaddy_SenderPort.reserve();

//  cc::daddy::PARkDirection_t& l_rParkDirectionContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_ParkDirectionStDaddy_SenderPort.reserve();

//  cc::daddy::ParkTypeSelectedStDaddy_t& l_rParkTypeSelectedStContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_ParkTypeSelectedStDaddy_SenderPort.reserve() ;

//  cc::daddy::FreeParkingSpaceTypeButtonStDaddy_t& l_rFreeParkingSpaceTypeButtonStContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort.reserve() ;

//    cc::daddy::FreeParkingConfirmButtonStDaddy_t& l_rFreeParkingConfirmButtonStContainer = // PRQA S 3803
//  cc::daddy::CustomDaddyPorts::sm_FreeParkingConfirmButtonStDaddy_SenderPort.reserve() ;

//    cc::daddy::ParkOutSideButtonStDaddy_t& l_rParkOutSideButtonStDaddyContainer = // PRQA S 3803
//  cc::daddy::CustomDaddyPorts::sm_ParkOutSideButtonStDaddy_SenderPort.reserve() ;

  // Start of BYD
//  cc::daddy::SVSViewStateDaddy_t&              l_rSVSViewStateContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_SVSViewStateDaddy_SenderPort.reserve() ;

//  cc::daddy::SVSWorkModeDaddy_t&               l_rSVSWorkModeContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_SVSWorkModeDaddy_SenderPort.reserve() ;

  cc::daddy::SVSOnOffStateDaddy_t&             l_rSVSOnOffStateContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSOnOffStateDaddy_SenderPort.reserve() ;

  cc::daddy::SVSvidoutModeDaddy_t&             l_rSVSvidoutModeContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSvidoutModeDaddy_SenderPort.reserve() ;

//   cc::daddy::SVSVehTransStsDaddy_t&    l_rSVSVehTransparentStatusContainer =
//     cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.reserve() ;

  cc::daddy::SVSTrajCfgStateDaddy_t&            l_rSVSTrajCfgStateContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSTrajCfgStateDaddy_SenderPort.reserve() ;

  cc::daddy::SVSLVDSvidOutModeDaddy_t&          l_rSVSLVDSvidOutModeContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSLVDSvidOutModeDaddy_SenderPort.reserve() ;

  cc::daddy::SVSImageConfigDaddy_t&             l_rSVSImageConfigContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSImageConfigDaddy_SenderPort.reserve() ;

  cc::daddy::SVSCarBodyDaddy_t&                 l_rSVSCarBodyContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSCarBodyDaddy_SenderPort.reserve() ;

//  cc::daddy::SVSExpandedViewStateDaddy_t&       l_rSVSSExpandedViewStateContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_SVSExpandedViewStateDaddy_SenderPort.reserve() ;

//  cc::daddy::SVSNewExpandedViewStateDaddy_t&    l_rSVSNewExpandedViewStateContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_SVSNewExpandedViewStateDaddy_SenderPort.reserve() ;

//   cc::daddy::SVSUnavlMsgsDaddy_t&                l_rSVSUnavlMsgsContainer =
//     cc::daddy::CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort.reserve() ;

  cc::daddy::CpcOverlaySwitchDaddy_t&            l_rCpcOverlaySwitchContainer =
    cc::daddy::CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort.reserve() ;

//  cc::daddy::SwVersionShowSwitchDaddy_t&         l_rSwVersionShowSwitchContainer = // PRQA S 3803
//    cc::daddy::CustomDaddyPorts::sm_SwVersionShowSwitchDaddy_SenderPort.reserve() ;

  cc::daddy::AvmNotActiveReasonDaddy_t&          l_rAvmNotActiveReasonContainer =
    cc::daddy::CustomDaddyPorts::sm_avmNotActiveReason_SenderPort.reserve() ;

  cc::daddy::AvmScreenTypeDaddy_t&              l_rAvmScreenTypeContainer =
    cc::daddy::CustomDaddyPorts::sm_avmScreenType_SenderPort.reserve();

  cc::daddy::VoiceDockFeedbackDaddy_t&          l_rVoiceDockFeedbackContainer =
    cc::daddy::CustomDaddyPorts::sm_voiceDockFeedback_SenderPort.reserve();

  cc::daddy::AvmScreenTypeDaddy_t&              l_rCompeteWindowIDContainer =
    cc::daddy::CustomDaddyPorts::sm_competeWindowIDRequest_SenderPort.reserve();

  cc::daddy::SVSDisplayedViewmodeGroupDaddy_t&  l_SVSDisplayedViewmodeGroupContainer =
    cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewmodeGroupDaddy_SenderPort.reserve();

 //! avm software error handling
  static bool l_engineTimeOut = false;
  static bool l_androidAlive = true;
  if ( m_engineTimeOutSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_engineTimeOutSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_engineTimeOut=l_pData->m_Data;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }
  if ( m_androidAliveSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_androidAliveSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_androidAlive=l_pData->m_Data;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  static bool l_fileError=false;
  if(m_avmFileErrorSMReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_avmFileErrorSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_fileError=l_pData->m_Data;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }
  static bool s_getStr=false;
  if ( m_systemStrSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_systemStrSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_getStr=static_cast<cc::target::common::ESystemStr>(l_pData->m_Data)==cc::target::common::ESystemStr::SYSTEM_STR_ENTER;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  const bool l_softwareError = (!l_androidAlive||l_engineTimeOut)&&!s_getStr;  // true: there is error; false: there is no error.

  static vfc::int32_t s_logCounter=0;

  if(s_logCounter%1000==0&&!l_androidAlive)
  {
      XLOG_INFO( cc::util::logging::g_viewModeSMContext , "Software error for android is not alive");
  }

  if(s_logCounter%1000==0&&l_engineTimeOut)
  {
      XLOG_INFO( cc::util::logging::g_viewModeSMContext , "Software error for engine time out");
  }

  static vfc::int32_t s_softwareErrorCount=0;
  bool l_sendSoftwareError=false;
  if(m_isStr)
  {
    s_softwareErrorCount=0;
  }
  else if(s_softwareErrorCount<6000)//enter normal mapping
  {
    s_softwareErrorCount++;
  }
  else{}
  l_sendSoftwareError=l_softwareError&&s_softwareErrorCount>=6000;

  if ( true == cc::daddy::CustomDaddyPorts::sm_avmSoftwareError_SenderPort.isConnected() )
  {
      auto& l_pAvmError = cc::daddy::CustomDaddyPorts::sm_avmSoftwareError_SenderPort.reserve();
      l_pAvmError.m_Data = l_sendSoftwareError;
      cc::daddy::CustomDaddyPorts::sm_avmSoftwareError_SenderPort.deliver();
  }

  if(s_logCounter%1000==0&&l_fileError)
  {
      XLOG_INFO( cc::util::logging::g_viewModeSMContext , "AVM error for file error");
  }

  if(s_logCounter%1000==0&&l_engineTimeOut)
  {
      XLOG_INFO( cc::util::logging::g_viewModeSMContext , "AVM error for engine time out");
  }

  if(s_logCounter>10000)
  {
      s_logCounter=1;
  }
  s_logCounter++;
  s_ViewModeStateMachine.setIn_AVMError(static_cast<boolean_T>(l_fileError||l_engineTimeOut));
  // End of BYD

  //! Set state machine inputs

  //! Vehicle Speed ****************************************************************************************************
  if( m_VehicleSpeedViewModeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_VehicleSpeedViewModeSMReceiver.getData().front() ;
    if( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_vehSpeed( l_pData->m_Data );
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

   // ! HeadUnit Request******************************************************************************************************************

  if ( m_HUselSVSModeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_HUselSVSModeSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUSVSMode( (ESVSViewMode)(l_pData->m_Data) );    // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_touchCoordinateReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_touchCoordinateReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUTouchCoorX( l_pData->m_Data.m_huX );
      s_ViewModeStateMachine.setIn_HUTouchCoorY( l_pData->m_Data.m_huY );
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_HUtouchEvenTypeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_HUtouchEvenTypeSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_TouchSt( (ETouchStatus) l_pData->m_Data );   // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

//   //! HU vehicle color setting request
//   if ( m_huVehColorReqSMReceiver.isConnected() )
//   {
//     static int l_ctr = -1 ;
//     const auto* l_pData = m_huVehColorReqSMReceiver.getData().front() ;
//     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//     {
//       s_ViewModeStateMachine.setIn_huVehColor( (EVehColor) l_pData->m_Data );    // PRQA S 3013  // PRQA S 3080
//       l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//     }
//   }

  if ( m_APAFuncStatusReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    static vfc::int32_t l_parkstatus = 0;
    const auto* const l_pData = m_APAFuncStatusReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
        if ((l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PARKINGINOUT || (l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_PAUSED  )
        {
            s_ViewModeStateMachine.setIn_parkstatus( static_cast<boolean_T>(true) );
        }
        else
        {
            s_ViewModeStateMachine.setIn_parkstatus( static_cast<boolean_T>(false) );    // PRQA S 3013  // PRQA S 3080
        }
        if((l_pData->m_Data) == cc::target::common::EAPAFunctionStatus::FUNCSTS_SEARCHINGINOUT)
        {
            s_ViewModeStateMachine.setIn_ParkingSearch( static_cast<boolean_T>(true) );
        }
        else
        {
            s_ViewModeStateMachine.setIn_ParkingSearch( static_cast<boolean_T>(false) );
        }
        if ( static_cast<vfc::int32_t>(l_pData->m_Data) != l_parkstatus )
        {
            l_parkstatus = static_cast<vfc::int32_t>(l_pData->m_Data);
            XLOG_INFO( g_viewModeSMContext , "ParkStatus: " << static_cast<vfc::int32_t>(l_pData->m_Data) << "recieved!");
        }
        l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  static bool l_isFirstDGear=true;
  static bool l_firstDGearDone=false;
  // if ( m_systemStrSMReceiver.isConnected() )
  // {
  //   static int l_ctr = -1 ;
  //   const auto* l_pData = m_systemStrSMReceiver.getData().front() ;

  //   if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
  //   {
  static cc::target::common::ESystemStr lastStr= cc::target::common::ESystemStr::SYSTEM_STR_NONE;
  static bool s_inRecoveryFromStr = false;
  if(static_cast<cc::target::common::ESystemStr >( m_isStr) == cc::target::common::ESystemStr::SYSTEM_STR_NONE&&lastStr==cc::target::common::ESystemStr::SYSTEM_STR_ENTER)
  {
    l_isFirstDGear=true;
    l_firstDGearDone=false;
    s_inRecoveryFromStr = true;
    m_hasEverActivated = false;
    // cc::assets::uielements::VehicleTransIconManager::setVehicleTransIcon(false);
    // can only be useful in target for the str update in the target main
  }

  if (static_cast<cc::target::common::ESystemStr >( m_isStr) == cc::target::common::ESystemStr::SYSTEM_STR_ENTER)
  {
    s_ViewModeStateMachine.setIn_SystemStr(ESystemStr::ESystemStr_SYSTEM_STR_ENTER);
  }
  else if( s_inRecoveryFromStr && !l_engineTimeOut)
  {
    s_ViewModeStateMachine.setIn_SystemStr(ESystemStr::ESystemStr_SYSTEM_STR_NONE);
    s_inRecoveryFromStr = false;
  }
  else
  {
      // Do nothing
  }

  lastStr = static_cast<cc::target::common::ESystemStr >(m_isStr);
  //   }
  // }



  if ( m_ADCUPowerSavModeSts_ReceiverPort.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_ADCUPowerSavModeSts_ReceiverPort.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      if(l_pData->m_Data==cc::target::common::EADCUPowerSavModeSts::ADCUPOWERSAVESTS_ONGOING||l_pData->m_Data==cc::target::common::EADCUPowerSavModeSts::ADCUPOWERSAVESTS_ON_L3)
      {
        s_ViewModeStateMachine.setIn_PowerSaveMode(static_cast<boolean_T>(true));
      }
      else
      {
        s_ViewModeStateMachine.setIn_PowerSaveMode(static_cast<boolean_T>(false));
      }
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_backKeyEventSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_backKeyEventSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_backkeyEvent(static_cast<boolean_T>(l_pData->m_Data));
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }


  if ( m_ModWarningOverlayDisplaySMReceiver.isConnected() )
  {
    static bool l_modWarning = false;
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_ModWarningOverlayDisplaySMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_ModWarning(static_cast<boolean_T>(l_pData->m_Data));
      if (l_pData->m_Data != l_modWarning)
      {
          XLOG_INFO( g_viewModeSMContext , "modwarning: "<< l_pData->m_Data << "recieved!");
          l_modWarning = l_pData->m_Data;
      }
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }



  static bool l_steeringWheelButtonDefinition=false;
  if ( m_steeringWheelButtonDefinitionSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_steeringWheelButtonDefinitionSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_steeringWheelButtonDefinition=l_pData->m_Data;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_steeringWheelButtonPressSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_steeringWheelButtonPressSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      if(l_steeringWheelButtonDefinition)
      {
        s_ViewModeStateMachine.setIn_steeringWheelButtonPressed(static_cast<boolean_T>(l_pData->m_Data));
      }
      else
      {
        s_ViewModeStateMachine.setIn_steeringWheelButtonPressed(static_cast<boolean_T>(false));
      }
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }


//   if (m_ParkReqReleaseBtnSMReceiver.isConnected())
//   {
//     static int l_ctr = -1 ;
//     const auto* l_pData = m_ParkReqReleaseBtnSMReceiver.getData().front() ;
//     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//     {
//       s_ViewModeStateMachine.setIn_parkreqreleasebtn( (bool)(l_pData->m_Data) ); // PRQA S 3080
//       l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//     }
//   }

  //! free view mode camera position
  if ( m_CameraPositionSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_CameraPositionSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_campositionX( static_cast<vfc::uint32_t>(l_pData->m_Data.hemisphere3D.x()) );
      s_ViewModeStateMachine.setIn_campositionY( static_cast<vfc::uint32_t>(l_pData->m_Data.hemisphere3D.y()) );
      // s_ViewModeStateMachine.setIn_zoomSts( l_pData->m_Data.zoomSts );
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //! USS Data **********************************************************************************************************
//   if ( m_UssDataViewModeSMReceiver.isConnected() )
//   {
//     static int l_ctr = -1 ;
//     const auto* l_pData = m_UssDataViewModeSMReceiver.getData().front() ;
//     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//     {
//       //Front Zones
//       float l_zone_0 = l_pData->m_Data[0u].getDistance();
//       float l_zone_1 = l_pData->m_Data[1u].getDistance();      // PRQA S 3803
//       float l_zone_14 = l_pData->m_Data[14u].getDistance();    // PRQA S 3803
//       float l_zone_15 = l_pData->m_Data[15u].getDistance();
//       if (l_pData->m_Data[1u].getDistance() > 0.6f)
//       {
//         l_zone_1 = 2.5f;
//       }
//       if (l_pData->m_Data[14u].getDistance() > 0.6f)
//       {
//         l_zone_14 = 2.5f;
//       }

//       //Rear Zone
//       float l_zone_6 = l_pData->m_Data[6u].getDistance();      // PRQA S 3803
//       float l_zone_7 = l_pData->m_Data[7u].getDistance();
//       float l_zone_8 = l_pData->m_Data[8u].getDistance();
//       float l_zone_9 = l_pData->m_Data[9u].getDistance();      // PRQA S 3803
//       if (l_pData->m_Data[6u].getDistance() > 0.6f)
//       {
//         l_zone_6 = 2.5f;
//       }
//       if (l_pData->m_Data[9u].getDistance() > 0.6f)
//       {
//         l_zone_9 = 2.5f;
//       }

//       s_ViewModeStateMachine.setIn_distF(minUSSDistance(l_zone_0, l_zone_1, l_zone_14, l_zone_15));
//       s_ViewModeStateMachine.setIn_distR(minUSSDistance(l_zone_6, l_zone_7, l_zone_8, l_zone_9));
//       s_ViewModeStateMachine.setIn_distF(minUSSDistance(l_zone_0, l_zone_1, l_zone_14, l_zone_15));  // For Front Wheel View
//       s_ViewModeStateMachine.setIn_distR(minUSSDistance(l_zone_6, l_zone_7, l_zone_8, l_zone_9));   // For Rear Wheel View

//       l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//     }
//   }

    // ! Sonar Data

    if (m_SonarDistRangeSMReceiver.isConnected())
    {
        static vfc::int32_t l_ctr = -1 ;
        const auto* const l_pData = m_SonarDistRangeSMReceiver.getData().front() ;
        if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
        {
            SonarDistLevel l_sonarDistLevel;
            l_sonarDistLevel.DistFrontLeftSide = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistFrontLeft = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistFrontCenter = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistFrontRight = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistFrontRightSide = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFrontAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistFront[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistRearLeftSide = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT_SIDE)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistRearLeft = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_LEFT)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistRearCenter = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_CENTER)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistRearRight = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            l_sonarDistLevel.DistRearRightSide = static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)]? static_cast<uint8_T>(l_pData->m_Data.m_sonarDistRearAvail[static_cast<vfc::int32_t>(cc::target::common::ESonarDistSideOrder::RANGE_ORGER_RIGHT_SIDE)]) : static_cast<uint8_T>(cc::target::common::ESonarDistRange::SONARDISTRANGECENTER_NONE));
            s_ViewModeStateMachine.setIn_sonarDistLevel(l_sonarDistLevel);
            l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
        }
    }

    if (m_SonarDistTrigLevelSMReceiver.isConnected())
    {
        static vfc::int32_t l_ctr = -1 ;
        const auto* const l_pData = m_SonarDistTrigLevelSMReceiver.getData().front() ;
        if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
        {
            s_ViewModeStateMachine.setIn_distTrigLevel(static_cast<ESonarTrigLevel>(l_pData->m_Data));
            l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
        }
    }
  if ( m_voiceDockRequestSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_voiceDockRequestSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_voiceDockRequest(static_cast<EVoiceDockReq> (l_pData->m_Data)) ; // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }
  static ESRActiveSts s_SRisActive=ESRActiveSts_NONE;
  static ESRActiveSts l_SRisActive=ESRActiveSts_NONE;
  if ( m_SRIsActiveSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_SRIsActiveSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_SRIsActive(static_cast<ESRActiveSts> (l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_SRisActive=static_cast<ESRActiveSts> (l_pData->m_Data);
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_huPASActStsSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_huPASActStsSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_huPasAct(static_cast<ESettingSts> (l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_huDGearActStsSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_huDGearActStsSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_huDGearAct(static_cast<ESettingSts> (l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }


  if ( m_huSteerActStsSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_huSteerActStsSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_huSteeringAct(static_cast<ESettingSts> (l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_huNarrowLaneActSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_huNarrowLaneActSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_settingNarrowLaneActivate(static_cast<ESettingSts> (l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_driverSteeringWheelReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_driverSteeringWheelReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_huSteeringAngleFront(l_pData->m_Data);  // PRQA S 3013  // PRQA S 3080
                //   std::cout << "setIn_huSteeringAngleFront: " << l_pData->m_Data <<std::endl;

      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  cc::target::common::ECompeteActiveAllow competeResponseRecord = cc::target::common::ECompeteActiveAllow::AVM_ACTIVE_NONE;
  if ( m_competeActiveAllowReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_competeActiveAllowReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_competeActiveAllow(static_cast<ECompeteActiveAllow > (l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      competeResponseRecord = l_pData->m_Data;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_competeQuitReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_competeQuitReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_competeQuit(static_cast<boolean_T>(l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if ( m_dockAvmButtonPressReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_dockAvmButtonPressReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {

      s_ViewModeStateMachine.setIn_dockAvmButtonPress(static_cast<vfc::uint8_t>(l_pData->m_Data));  // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }
  //! Get pdm inputs ******************************************************************************************************************


//   if ( false == m_vehTransPdmWasRead )
//   {
//     if ( m_pdmVehTransStsSMReceiver.isConnected() )
//     {
//       static int l_ctr = -1 ;
//       const auto* l_pData = m_pdmVehTransStsSMReceiver.getData().front() ;
//       if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//       {
//         s_ViewModeStateMachine.setIn_pdmVehTrans_( static_cast<EPdmSetting>(l_pData->m_Data) );
//         l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//         m_vehTransPdmWasRead = true;
//       }
//     }
//   }

//   if ( false == m_vehColorPdmWasRead )
//   {
//     if ( m_pdmVehColorStsSMReceiver.isConnected() )
//     {
//       static int l_ctr = -1 ;
//       const auto* l_pData = m_pdmVehColorStsSMReceiver.getData().front() ;
//       if ( nullptr != l_pData && (*l_pData).m_sequenceNumber != l_ctr )
//       {
//         s_ViewModeStateMachine.setIn_pdmVehColor( static_cast<EVehColor>(l_pData->m_Data) );    // PRQA S 3013
//         l_ctr = l_pData->m_sequenceNumber ;
//         m_vehColorPdmWasRead = true;
//       }
//     }
//   }

  //! Door states ******************************************************************************************************
  if ( m_DoorStateViewModeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const pc::daddy::DoorStateDaddy* const l_pData = m_DoorStateViewModeSMReceiver.getData() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_pCDoors = l_pData ;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //! Side mirror states ***********************************************************************************************
  if ( m_MirrorStateViewModeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const pc::daddy::MirrorStateDaddy* const l_pData = m_MirrorStateViewModeSMReceiver.getData() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_pCSideMirrors = l_pData ;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //! degradation FIDs ***********************************************************************************************
  if ( m_DegradationFidSMReceiver.isConnected() )
  {
    const cc::daddy::DegradationFid_t* const l_pData = m_DegradationFidSMReceiver.getData() ;
    if ( nullptr != l_pData )
    {
      // l_fids = l_pData ;

      // s_ViewModeStateMachine.setIn_FID_SVSCamFrontSt((bool) l_pData->m_Data.m_FiMFID_SVSCamFrontSt);
      // s_ViewModeStateMachine.setIn_FID_SVSCamLeftSt((bool) l_pData->m_Data.m_FiMFID_SVSCamLeftSt);
      // s_ViewModeStateMachine.setIn_FID_SVSCamRearSt((bool) l_pData->m_Data.m_FiMFID_SVSCamRearSt);
      // s_ViewModeStateMachine.setIn_FID_SVSCamRightSt((bool) l_pData->m_Data.m_FiMFID_SVSCamRightSt);
      // s_ViewModeStateMachine.setIn_FID_SVSEcuInternalStatus((bool) l_pData->m_Data.m_FiMFID_SVSEcuInternalStatus); // PRQA S 3080

    }
  }

  // //! variant data
  // if ( m_VariantSMReceiver.isConnected() )
  // {
  //   static int l_ctr = -1 ;
  //   const cc::daddy::VariantDaddy* l_pData = m_VariantSMReceiver.getData() ;
  //   if ( 0 != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
  //   {
  //     s_ViewModeStateMachine.setIn_variantSVSCoded((bool) l_pData->m_Data.m_NRCSFunctionCoding);
  //     l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
  //   }
  // }

  //! Camera degradation mask ***********************************************************************************************
  if ( m_degradationMaskSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const pc::daddy::CameraDegradationMaskDaddy* const l_pData = m_degradationMaskSMReceiver.getData() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      //! true => degraded, false => nominal
      pc::daddy::daddy4bitfield l_CameraDegradationMask = l_pData->m_Data;
      s_ViewModeStateMachine.setIn_IMB_FrontSt(static_cast<boolean_T>(pc::daddy::isBitSet( pc::core::sysconf::FRONT_CAMERA, l_CameraDegradationMask)));
      s_ViewModeStateMachine.setIn_IMB_LeftSt(static_cast<boolean_T>(pc::daddy::isBitSet( pc::core::sysconf::LEFT_CAMERA, l_CameraDegradationMask)));
      s_ViewModeStateMachine.setIn_IMB_RearSt(static_cast<boolean_T>(pc::daddy::isBitSet( pc::core::sysconf::REAR_CAMERA, l_CameraDegradationMask)));
      s_ViewModeStateMachine.setIn_IMB_RightSt(static_cast<boolean_T>(pc::daddy::isBitSet( pc::core::sysconf::RIGHT_CAMERA, l_CameraDegradationMask))); // PRQA S 4126

      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  // ! Gear******************************************************************************************************************
  static bool l_isFirstRGear=true;
  static EGear l_lastGear=EGear_Init;
  static EGear l_currentGear = EGear_Init;
  if ( m_GearViewModeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_GearViewModeSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      l_currentGear = static_cast<EGear>(l_pData->m_Data);
      s_ViewModeStateMachine.setIn_gear(l_currentGear); // PRQA S 3080
      if(l_lastGear!=EGear_D&&l_currentGear==EGear_D){
        if(l_firstDGearDone==false){
            l_firstDGearDone=true;
        }
        else{
            l_isFirstDGear=false;
        }
      }
      if(l_lastGear == EGear_R&&l_currentGear!=EGear_R&&l_isFirstRGear)
      {
        l_isFirstRGear=false;
      }
      l_lastGear = l_currentGear;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
      // pf code. #code looks fine
      //VIEWMODE_SM_LOG( SM_FRSignal ) << "SM setting gear to " << EGear_P << XLOG_ENDL;
    }
  }

  s_ViewModeStateMachine.setIn_isFirstDGear(static_cast<boolean_T>(l_isFirstDGear));
  s_ViewModeStateMachine.setIn_isFirstRGear(static_cast<boolean_T>(l_isFirstRGear));
  s_ViewModeStateMachine.setIn_HaveEverActivated(static_cast<boolean_T>(m_hasEverActivated));

  // !PowerMode
  // if ( m_PowerModeSMReceiver.isConnected() )
  // {
  //   static int l_ctr = -1 ;
  //   const auto* l_pData = m_PowerModeSMReceiver.getData().front() ;
  //   if ( 0 != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
  //   {
  //     s_ViewModeStateMachine.setIn_powerMode(l_pData->m_Data );
  //     VIEWMODE_SM_LOG( SM_FRSignal ) << "Setting power mode to: "<< static_cast<int> (l_pData->m_Data) << XLOG_ENDL;
  //     l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
  //   }
  // }

    //!HUDislayModeSwitch
  if ( m_HUDislayModeSwitchSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_HUDislayModeSwitchSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUDislayModeSwitch((EHuDisplayModeSwitch)l_pData->m_Data ); // PRQA S 3013  // PRQA S 3080
      // pf code. #code looks fine
      VIEWMODE_SM_LOG( SM_FRSignal ) << "Setting DislayModeSwitch to: "<< static_cast<vfc::int32_t> (l_pData->m_Data) << XLOG_ENDL;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //!ImageWorkMode
  if ( m_HUImageWorkModeSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_HUImageWorkModeSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUImageWorkMode((EHuImageWorkMode)l_pData->m_Data ); // PRQA S 3013  // PRQA S 3080
      // pf code. #code looks fine
      VIEWMODE_SM_LOG( SM_FRSignal ) << "Setting ImageWorkMode to: "<< static_cast<vfc::int32_t> (l_pData->m_Data) << XLOG_ENDL;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //DislayModeExpandiiisfds
  if ( m_HUDislayModeExpandSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_HUDislayModeExpandSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUDislayModeExpand((EHuDisplayModeExpand)l_pData->m_Data ); // PRQA S 3013  // PRQA S 3080
      // pf code. #code looks fine
      VIEWMODE_SM_LOG( SM_FRSignal ) << "Setting DislayModeExpand to: "<< static_cast<vfc::int32_t> (l_pData->m_Data) << XLOG_ENDL;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //DislayModeExpandNew
  if ( m_HUDislayModeExpandNewSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_HUDislayModeExpandNewSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUDislayModeExpandNew((EHuDisplayModeExpandNew)l_pData->m_Data ); // PRQA S 3013  // PRQA S 3080
      // pf code. #code looks fine
      VIEWMODE_SM_LOG( SM_FRSignal ) << "Setting DislayModeExpandNew to: "<< static_cast<vfc::int32_t> (l_pData->m_Data) << XLOG_ENDL;
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }


//   //!Vehicle transparence switch
//   if ( m_HUvehTransSMReceiver.isConnected() )
//   {
//     static int l_ctr = -1 ;
//     const auto* l_pData = m_HUvehTransSMReceiver.getData().front() ;
//     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//     {
//       s_ViewModeStateMachine.setIn_huVehTrans_((EHuVehTransReq)l_pData->m_Data ); // PRQA S 3013  // PRQA S 3080
//       // pf code. #code looks fine
//       VIEWMODE_SM_LOG( SM_FRSignal ) << "Setting Vehicle Transparence Status to: "<< static_cast<int>(l_pData->m_Data) << XLOG_ENDL;
//       l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//     }
//   }

  if ( m_calibStateRecevier.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_calibStateRecevier.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
    //   CalibState l_CalibState;
    //   l_CalibState.CalibActive = l_pData->m_Data.m_calibActive;
    //   l_CalibState.CalibModeActive = static_cast<ECalibModeActive>(l_pData->m_Data.m_calibModeActive);
    //   l_CalibState.CalibProgressPercentage = l_pData->m_Data.m_calibProgressPercentage;
    //   l_CalibState.CalibRemainingTime = l_pData->m_Data.m_calibRemainingTime;
    //   l_CalibState.CalibStatus = static_cast<ECalibStatus>(l_pData->m_Data.m_calibStatus);
    //   s_ViewModeStateMachine.setIn_calibActive(l_pData->m_Data.m_calibActive);
      s_ViewModeStateMachine.setIn_calibStatus(static_cast<ECalibStatus>(l_pData->m_Data.m_calibStatus));// PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  s_ViewModeStateMachine.setIn_calibActive(static_cast<uint8_T>(l_rCpcOverlaySwitchContainer.m_Data));

//   if ( m_PIVI_ManualVideoSetupReqSMReceiver.isConnected() )
//   {
//     static int l_ctr = -1 ;
//     const auto* l_pData = m_PIVI_ManualVideoSetupReqSMReceiver.getData().front() ;

//     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//     {
//       #if ACTIVATE_LOGGING
//         XLOG_INFO_OS( g_viewModeSMContext ) << "PIVI::ManualVideoSetupReq received: ScreenID: "<< static_cast<int> (l_pData->m_Data.m_ScreenIDPIVI_u8) << XLOG_ENDL;
//       #endif
//       s_ViewModeStateMachine.setIn_HUViewReq((EScreenID)l_pData->m_Data.m_ScreenIDPIVI_u8 );    // PRQA S 3013  // PRQA S 3080
//       l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//     }
//   }

  if ( m_RequestViewIdSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_RequestViewIdSMReceiver.getData().front() ;

    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_HUViewReq(l_pData->m_Data );    // PRQA S 3013  // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  //! NFSM_ViewBufferStatus
  // if ( m_NFSM_ViewBufferStatusViewModeSMReceiver.isConnected() )
  // {
  //   static int l_ctr = -1;
  //   const auto* l_pData = m_NFSM_ViewBufferStatusViewModeSMReceiver.getData().front() ;
  //   if ( 0 != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
  //   {
  //     #if ACTIVATE_LOGGING
  //       XLOG_INFO_OS( g_viewModeSMContext ) << "NFSM_ViewBufferStatus: "<< static_cast<int> (l_pData->m_Data.m_ViewBufferStatus_u8) << XLOG_ENDL;
  //     #endif
  //     l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;


  //     if(m_prevViewBufferStatus != l_pData->m_Data.m_ViewBufferStatus_u8)
  //     {
  //       m_prevViewBufferStatus = l_pData->m_Data.m_ViewBufferStatus_u8;
  //       backchannel::NFSM_ViewBufferStatus l_data;
  //       l_data.m_ViewBufferStatus_u8 = l_pData->m_Data.m_ViewBufferStatus_u8;
  //       m_requestHandlerViewBufferStatus.sendData( l_pData->m_Data);
  //     }
  //   }
  // }

  // Animation information from ViewModeStateTransitionManager
  if( m_animationStateSMReceiver.isConnected() )
  {
    static vfc::int32_t l_ctr = -1;
    const  cc::daddy::ViewAnimationCompleted_t* const l_pData = m_animationStateSMReceiver.getData() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      const AnimationState l_animState = {(EScreenID)l_pData->m_Data.m_screenId, (EAnimationState)l_pData->m_Data.m_state}; // PRQA S 3080
          // PRQA S 3013  // PRQA S 3080
      s_ViewModeStateMachine.setIn_animationState(l_animState);
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }else
    {
      // Nothing...
    }
  }

  if(m_SRIsTopActivitySMReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_SRIsTopActivitySMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_SRIsTopActivity(static_cast<boolean_T >(l_pData->m_Data));
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if (m_PasWarnToneSMReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_PasWarnToneSMReceiver.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_pasWarnTone( (EPasWarnTone)(l_pData->m_Data) ); // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if (m_AndroidIconActiveRecevier.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_AndroidIconActiveRecevier.getData().front() ;
    if ( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
      s_ViewModeStateMachine.setIn_androidIconActive(static_cast<boolean_T>(l_pData->m_Data)); // PRQA S 3080
      l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if (m_CloseButtonPressedReceiver.isConnected())
  {
    constexpr static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_CloseButtonPressedReceiver.getData().front() ;
    if( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
        s_ViewModeStateMachine.setIn_closeButtonPressed( static_cast<boolean_T>(l_pData->m_Data) );
    }

  }

  if (m_EnlargeButtonPressedReceiver.isConnected())
  {
    static const vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_EnlargeButtonPressedReceiver.getData().front() ;
    if( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
        s_ViewModeStateMachine.setIn_EnlargeButtonPressed( static_cast<boolean_T>(l_pData->m_Data) );
    }

  }

  bool  l_needTransition=false;

  if (m_cameraPositionChangeReceiver.isConnected())
  {
    static vfc::int32_t l_ctr = -1 ;
    const auto* const l_pData = m_cameraPositionChangeReceiver.getData().front() ;
    if( nullptr != l_pData && static_cast<vfc::int32_t>((*l_pData).m_sequenceNumber) != l_ctr )
    {
        l_needTransition=true;
        l_ctr = static_cast<vfc::int32_t>(l_pData->m_sequenceNumber) ;
    }
  }

  if (m_FloatViewChangeButtonPressedReceiver.isConnected())
  {
    static const int l_ctr = -1 ; // PRQA S 2427 // PRQA S 4143
    const auto* const l_pData = m_FloatViewChangeButtonPressedReceiver.getData().front() ;
    if( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr ) // PRQA S 2427
    {
        s_ViewModeStateMachine.setIn_FloatViewChange(l_pData->m_Data);
    }
  }

  if(l_rSVSShowReqContainer.m_Data==0u||static_cast<int>(l_rAvmScreenTypeContainer.m_Data)<6)
  {
    auto& l_container = cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort.reserve();
    l_container.m_Data = cc::daddy::EFloatViewType::FLOAT_NONE;
    cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort.deliver();
  }
  s_SRisActive=l_SRisActive;

  if(m_FreeParkingActiveSMReceiver.isConnected())
  {
    static const int l_ctr = -1 ; // PRQA S 2427 // PRQA S 4143
    const auto* const l_pData = m_FreeParkingActiveSMReceiver.getData().front()   ;
    if( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr ) // PRQA S 2427
    {
        s_ViewModeStateMachine.setin_isFreeParking(l_pData->m_Data);
    }  
  }
//   if (m_settingPageVehicleTransReceiver.isConnected())
//     {
//         if (m_settingPageVehicleTransReceiver.hasNewData())
//         {
//             const cc::daddy::PdmVehTransStsDaddy_t* const l_pVehicleTransState = m_settingPageVehicleTransReceiver.getData();
//             if (nullptr != l_pVehicleTransState)
//             {
//                 // m_iconShow = static_cast<bool>(l_pVehicleTransState->m_Data);
//                 cc::assets::uielements::VehicleTransIconManager::setVehicleTransIcon(static_cast<bool>(l_pVehicleTransState->m_Data));
//             }
//         }
//     }

    cc::util::pdmwriter::PdmSettings* const l_pdmSetting =
        dynamic_cast<cc::util::pdmwriter::PdmSettings*>(pc::util::coding::getCodingManager()->getItem(PDM_KEY));  // PRQA S 3400
    const  vfc::int32_t l_pdmVehTransStatus = l_pdmSetting->getPdmVehTransStatus();
    cc::assets::uielements::VehicleTransIconManager::setVehicleTransIcon(static_cast<bool>(l_pdmVehTransStatus));


//   if (m_cpcStatusSMReceiverport.isConnected())
//   {
//     static int l_ctr = -1 ;
//     const auto* l_pData = m_cpcStatusSMReceiverport.getData().front() ;
//     if ( nullptr != l_pData && static_cast<int>((*l_pData).m_sequenceNumber) != l_ctr )
//     {
//       const cc::cpc::ECpcCameraAvailability l_cpcSts0 = l_pData->m_Data.m_calibstatus[0].m_camAvailability;
//       const cc::cpc::ECpcCameraAvailability l_cpcSts1 = l_pData->m_Data.m_calibstatus[1].m_camAvailability;
//       const cc::cpc::ECpcCameraAvailability l_cpcSts2 = l_pData->m_Data.m_calibstatus[2].m_camAvailability;
//       const cc::cpc::ECpcCameraAvailability l_cpcSts3 = l_pData->m_Data.m_calibstatus[3].m_camAvailability;

//       //! CPC active if one or more than one camera awake
//       if ( (cc::cpc::CPC_AWAKE == l_cpcSts0) || (cc::cpc::CPC_AWAKE == l_cpcSts1)
//         || (cc::cpc::CPC_AWAKE == l_cpcSts2) || (cc::cpc::CPC_AWAKE == l_cpcSts3))
//       {
//         s_ViewModeStateMachine.setIn_isCpcActive(true);
//       }
//       else
//       {
//         s_ViewModeStateMachine.setIn_isCpcActive(false);
//       }
//       l_ctr = static_cast<int>(l_pData->m_sequenceNumber) ;
//     }
//   }


  //! Step state machine **************************************************************************************************
  s_ViewModeStateMachine.step() ;

  //! Get state machine outputs *******************************************************************************************
  //!State Machine Debug output

//   if(200 == m_stepCounter)
//   {
//     m_stepCounter = 0;
//   }

  //!Views ************************************************************************************************************
  const vfc::int32_t l_NewMode = s_ViewModeStateMachine.getOut_displayedView();
  const bool l_freeMode = s_ViewModeStateMachine.getOut_isFreeModeAct();




#if POCTEST
  static int l_NewMode = EScreenID_SINGLE_FRONT_NORMAL;
  static int testcounter = 0;
  static bool isfront = false;
  if (testcounter%500 == 0)
  {
    if (isfront==false)
    {
        l_NewMode = EScreenID_SINGLE_FRONT_NORMAL;
        isfront = true;
    }
    else
    {
        l_NewMode = EScreenID_SINGLE_REAR_NORMAL_ON_ROAD;
        isfront = false;
    }
  }
  testcounter++;
#endif

  if (!m_preModeFree && !l_freeMode)
  {
    if (l_NewMode != m_CurrMode||l_needTransition)
    {
      pc::daddy::ViewModeDaddy& l_vm = pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.reserve();
      l_vm.m_Data = pc::daddy::ViewMode(m_CurrMode, l_NewMode);
      pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.deliver();

      m_CurrMode = l_NewMode;
    }
  }

  if (m_preModeFree && !l_freeMode)
  {
    pc::daddy::ViewModeDaddy& l_vm = pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.reserve();
    l_vm.m_Data = pc::daddy::ViewMode(m_CurrMode, l_NewMode);
    pc::daddy::BaseDaddyPorts::sm_viewModeSenderPort.deliver();

    m_CurrMode = l_NewMode;
  }

  m_preModeFree = l_freeMode;

  // this->computeAndDaddySendCameraDeactivationMask( l_pCDoors, l_pCSideMirrors/*, l_fids */) ;

  //!Remote Controller Feedback *******************************************************************************************
  l_rDaddyStateContainer.m_Data.mode = m_CurrMode; //Feedback to remote controller

  l_rDaddySystemStateContainer.m_Data.systemAvailability = s_ViewModeStateMachine.getOut_systemAvailable();
  l_rDaddySystemStateContainer.m_Data.systemacitve = static_cast<bool>(s_ViewModeStateMachine.getOut_systemActive());
  l_rSVSCurrentViewContainer.m_Data = s_ViewModeStateMachine.getOut_SVSCurrentView() ;
  bool l_showReq=false;
  static vfc::int32_t s_showDelay=0;
  l_showReq= s_ViewModeStateMachine.getOut_SVSShowReq();
  if(l_showReq==true)
  {
    if(s_showDelay>10)
    {
        l_rSVSShowReqContainer.m_Data=l_showReq;
    }
    else
    {
        s_showDelay++;
    }
  }
  else
  {
    l_rSVSShowReqContainer.m_Data=l_showReq;
    s_showDelay=0;
  }
//   l_rSVSShowReqContainer.m_Data = s_ViewModeStateMachine.getOut_SVSShowReq() ;
  l_rSVSViewModeStsContainer.m_Data = s_ViewModeStateMachine.getOut_SVSViewModeSts();
 // l_rSVSUnavlMsgsContainer.m_Data = static_cast<ESVSUnavlMsgs>(s_ViewModeStateMachine.getOut_SVSUnavlMsgs());     // PRQA S 3013
  // l_rSVSzoomStsContainer.m_Data = s_ViewModeStateMachine.getOut_SVSzoomSts();
  l_rSVSFrViewStsContainer.m_Data = s_ViewModeStateMachine.getOut_SVSFrViewSts();
  l_rSVSLeViewStsContainer.m_Data = s_ViewModeStateMachine.getOut_SVSLeViewSts();
  l_rSVSReViewStsContainer.m_Data = s_ViewModeStateMachine.getOut_SVSReViewSts();
  l_rSVSRiViewStsContainer.m_Data = s_ViewModeStateMachine.getOut_SVSRiViewSts();
  l_rSVSDisplayedViewContainer.m_Data = s_ViewModeStateMachine.getOut_displayedView();
  l_rSVSFreeModeStContainer.m_Data = s_ViewModeStateMachine.getOut_isFreeMode();
  l_rAvmNotActiveReasonContainer.m_Data = static_cast< cc::target::common::ENotActiveReason >(s_ViewModeStateMachine.getOut_SVSUnavlMsgs());
  l_rAvmScreenTypeContainer.m_Data = static_cast< cc::target::common::EAVMScreen >(s_ViewModeStateMachine.getOut_SVSScreenType());
  l_rVoiceDockFeedbackContainer.m_Data = static_cast<cc::target::common::EVoiceDockFb>(s_ViewModeStateMachine.getOut_voiceDockFeedback());
  l_rCompeteWindowIDContainer.m_Data = static_cast<cc::target::common::EAVMScreen>(s_ViewModeStateMachine.getOut_competeScreenTypeReq());
  l_SVSDisplayedViewmodeGroupContainer.m_Data = s_ViewModeStateMachine.getOut_ViewModeGroup();
#ifndef USE_VIRTUAL_OBJECT
  EPARkSlot l_parkSlotSelectedNum = static_cast<EPARkSlot>(s_ViewModeStateMachine.getOut_SVSParkSlot());
  if (m_ParkSpaceSMReceiver.isConnected())
  {
    const auto* l_pData = m_ParkSpaceSMReceiver.getData().front() ;
    if ( 0 != l_pData )
    {
        if (l_parkSlotSelectedNum > EPARKSLOT_NONE && l_parkSlotSelectedNum < EPARKSLOT_ALL)
        {
            vfc::uint16_t l_side = (l_parkSlotSelectedNum < (cc::target::common::g_parkSlotQuantity / 2u + 1u)) ? 0u : 1u;
            vfc::uint16_t l_number = (l_side == 0u) ? (l_parkSlotSelectedNum - 1u) : (l_parkSlotSelectedNum - cc::target::common::g_parkSlotQuantity / 2u - 1u);
            l_rParkSlotSelectedStContainer.m_Data = static_cast<vfc::uint16_t>(l_pData->m_Data[l_side][l_number].m_APA_PSId_u16);
        }
        else
        {
            l_rParkSlotSelectedStContainer.m_Data = 0u;
        }
    }
  }
  else
  {
    l_rParkSlotSelectedStContainer.m_Data = 0u;
  }

#endif
//   l_rSVSViewStateContainer.m_Data = static_cast<vfc::uint8_t>(s_ViewModeStateMachine.getOut_SVSViewState());
//   l_rSVSWorkModeContainer.m_Data = static_cast<vfc::uint8_t>(s_ViewModeStateMachine.getOut_SVSWorkMode());
  l_rSVSOnOffStateContainer.m_Data = 0x02u; // Bosch APA sends 0x2
  l_rSVSvidoutModeContainer.m_Data  = 0X01u; //Bosch APA sends 0x1
//   l_rSVSVehTransparentStatusContainer.m_Data = static_cast<vfc::uint8_t>(s_ViewModeStateMachine.getOut_vehTransSts_());
  l_rSVSTrajCfgStateContainer.m_Data = 0x00u; // to be implement
  l_rSVSLVDSvidOutModeContainer.m_Data = 0X00u; //Bosch APA sends 0x0
  l_rSVSImageConfigContainer.m_Data = 0X00u; //Bosch APA sends 0x0
  l_rSVSCarBodyContainer.m_Data = 0X00u; //Bosch APA sends 0x0
//   l_rSVSSExpandedViewStateContainer.m_Data = static_cast<vfc::uint8_t>(s_ViewModeStateMachine.getOut_SVSExpandedViewState());
//   l_rSVSNewExpandedViewStateContainer.m_Data = static_cast<vfc::uint8_t>(s_ViewModeStateMachine.getOut_SVSNewExpandedViewState());
//   l_rSVSVehColorAckContainer.m_Data = s_ViewModeStateMachine.getOut_VehColorACK();
//   l_rSVSUnavlMsgsContainer.m_Data = 0x00u; // to be implement
  l_rCpcOverlaySwitchContainer.m_Data = s_ViewModeStateMachine.getOut_HuCPCActive();
  // End of BYD
    // l_MODStsContainer.m_Data = s_ViewModeStateMachine.getOut_MODSts();
// #if IMGUI_VIEW_MODE_DEBUG
  switch (l_rSVSViewModeStsContainer.m_Data)
  {
    case ESVSViewMode_VM_Standard:      {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Standard"); break;}
    case ESVSViewMode_VM_Perspective:   {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Perspective"); break;}
    case ESVSViewMode_VM_Wheel:         {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Wheel"); break;}
    case ESVSViewMode_VM_Wide:          {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Wide"); break;}
    case ESVSViewMode_VM_STB:           {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_STB"); break;}
    case ESVSViewMode_VM_Floating:      {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Floating"); break;}

    case ESVSViewMode_VM_Default:       {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Default"); break;}
    default:                            {IMGUI_LOG("ViewModeStateMachine", "ViewMode", "ESVSViewMode_VM_Default"); break;}
  }

  switch (s_ViewModeStateMachine.getOut_ViewModeGroup()) // PRQA S 4018
  {
      case EViewModeGroup::VIEWMODE_NONE:      {IMGUI_LOG("ViewModeStateMachine", "ViewModeGroup", "VIEWMODE_NONE"); break;}
      case EViewModeGroup::VIEWMODE_FRONT:      {IMGUI_LOG("ViewModeStateMachine", "ViewModeGroup", "VIEWMODE_FRONT"); break;}
      case EViewModeGroup::VIEWMODE_REAR:      {IMGUI_LOG("ViewModeStateMachine", "ViewModeGroup", "VIEWMODE_REAR"); break;}
      default: {IMGUI_LOG("ViewModeStateMachine", "ViewModeGroup", "VIEWMODE_NONE"); break;}

  }

  // switch (s_ViewModeStateMachine.getOut_SVSScreenType()) // PRQA S 4018
  // {
  //   case cc::target::common::EAVMScreen::SCREEN_NONE:           {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_NONE"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FULL_R:         {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FULL_R"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FULL_NOTR:      {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FULL_NOTR"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_PARK:     {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_PARK"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_NOTPARK:  {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_NOTPARK"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_CALIBRATION:    {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_CALIBRATION"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_R:   {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_PLAN_R"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_NOTR:{IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_PLAN_NOTR"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_R:     {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_FR_R"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_NOTR:  {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_FR_NOTR"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_PARK:{IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_PLAN_PARK"); break;}
  //   case cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_PARK:  {IMGUI_LOG("ViewModeStateMachine", "ScreenType", "SCREEN_FLOAT_FR_PARK"); break;}
  //   default: {IMGUI_LOG("ViewModeStateMachine", "ScreenType", s_ViewModeStateMachine.getOut_SVSScreenType()); break;}

  // }

  switch (l_rSVSDisplayedViewContainer.m_Data)
  {
      case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:   {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_SINGLE_REAR_NORMAL_ON_ROAD"); break;}
      case EScreenID_SINGLE_FRONT_NORMAL:          {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_SINGLE_FRONT_NORMAL"); break;}
      case EScreenID_SINGLE_FRONT_JUNCTION:        {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_SINGLE_FRONT_JUNCTION"); break;}
      case EScreenID_SINGLE_REAR_JUNCTION:         {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_SINGLE_REAR_JUNCTION"); break;}
      case EScreenID_WHEEL_FRONT_DUAL:             {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_WHEEL_FRONT_DUAL"); break;}
      case EScreenID_WHEEL_REAR_DUAL:              {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_WHEEL_REAR_DUAL"); break;}
      case EScreenID_SINGLE_STB:                   {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_SINGLE_STB"); break;}
      case EScreenID_PERSPECTIVE_FR:               {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_FR"); break;}
      case EScreenID_PERSPECTIVE_FL:               {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_FL"); break;}
      case EScreenID_PERSPECTIVE_PFR:              {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_PFR"); break;}
      case EScreenID_PERSPECTIVE_PRE:              {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_PRE"); break;}
      case EScreenID_PERSPECTIVE_RL:               {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_RL"); break;}
      case EScreenID_PERSPECTIVE_RR:               {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_RR"); break;}
      case EScreenID_PERSPECTIVE_KL:               {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_KL"); break;}
      case EScreenID_PERSPECTIVE_KR:               {IMGUI_LOG("ViewModeStateMachine", "DisplayedView", "EScreenID_PERSPECTIVE_KR"); break;}
      default: {                                     IMGUI_LOG("ViewModeStateMachine", "DisplayedView", static_cast<int>(l_rSVSDisplayedViewContainer.m_Data)); break;}
  }

  switch (l_rSVSCurrentViewContainer.m_Data)
  {
    case EScreenID_SINGLE_REAR_NORMAL_ON_ROAD:   {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_SINGLE_REAR_NORMAL_ON_ROAD"); break;}
    case EScreenID_SINGLE_FRONT_NORMAL:          {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_SINGLE_FRONT_NORMAL"); break;}
    case EScreenID_SINGLE_FRONT_JUNCTION:        {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_SINGLE_FRONT_JUNCTION"); break;}
    case EScreenID_SINGLE_REAR_JUNCTION:         {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_SINGLE_REAR_JUNCTION"); break;}
    case EScreenID_WHEEL_FRONT_DUAL:             {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_WHEEL_FRONT_DUAL"); break;}
    case EScreenID_WHEEL_REAR_DUAL:              {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_WHEEL_REAR_DUAL"); break;}
    case EScreenID_SINGLE_STB:                   {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_SINGLE_STB"); break;}
    case EScreenID_PERSPECTIVE_FR:               {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_FR"); break;}
    case EScreenID_PERSPECTIVE_FL:               {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_FL"); break;}
    case EScreenID_PERSPECTIVE_PFR:              {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_PFR"); break;}
    case EScreenID_PERSPECTIVE_PRE:              {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_PRE"); break;}
    case EScreenID_PERSPECTIVE_RL:               {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_RL"); break;}
    case EScreenID_PERSPECTIVE_RR:               {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_RR"); break;}
    case EScreenID_PERSPECTIVE_KL:               {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_KL"); break;}
    case EScreenID_PERSPECTIVE_KR:               {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_PERSPECTIVE_KR"); break;}
    case EScreenID_FLOAT_FRONT_PLAN_VIEW:        {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_FRONT_PLAN_VIEW"); break;}
    case EScreenID_FLOAT_FRONT_VIEW:             {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_FRONT_VIEW"); break;}
    case EScreenID_FLOAT_REAR_VIEW:              {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_REAR_VIEW"); break;}
    case EScreenID_FLOAT_PARKING_REAR_VIEW:      {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_PARKING_REAR_VIEW"); break;}
    case EScreenID_FLOAT_PARKING_FRONT_VIEW:     {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_PARKING_FRONT_VIEW"); break;}
    case EScreenID_FLOAT_REAR_PLAN_VIEW:         {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_REAR_PLAN_VIEW"); break;}
    case EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW:{IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_FRONT_PARKING_PLAN_VIEW"); break;}
    case EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW: {IMGUI_LOG("ViewModeStateMachine", "CurrentView", "EScreenID_FLOAT_REAR_PARKING_PLAN_VIEW"); break;}
    default: {                                    IMGUI_LOG("ViewModeStateMachine", "CurrentView", static_cast<int>(l_rSVSCurrentViewContainer.m_Data)); break;}
  }
  //Show req from SM
  if (l_rSVSShowReqContainer.m_Data == static_cast<uint8_t>(false))
  {
      IMGUI_LOG("ViewModeStateMachine", "out_ShowReq", "NO");
  }
  else
  {
      IMGUI_LOG("ViewModeStateMachine", "out_ShowReq", "YES");
      m_hasEverActivated = true;
  }

  switch (l_rAvmNotActiveReasonContainer.m_Data)
  {
      case cc::target::common::ENotActiveReason::NOTACTIVE_REASON_NONE: {IMGUI_LOG("ViewModeStateMachine", "AvmNotActiveReason", "NOTACTIVE_REASON_NONE"); break;}
      case cc::target::common::ENotActiveReason::NOTACTIVE_REASON_QNX_AVM_ERROR: {IMGUI_LOG("ViewModeStateMachine", "AvmNotActiveReason", "NOTACTIVE_REASON_QNX_AVM_ERROR"); break;}
      case cc::target::common::ENotActiveReason::NOTACTIVE_REASON_ADCU_IN_SLEEP: {IMGUI_LOG("ViewModeStateMachine", "AvmNotActiveReason", "NOTACTIVE_REASON_ADCU_IN_SLEEP"); break;}
      case cc::target::common::ENotActiveReason::NOTACTIVE_REASON_SPEED_TOO_HIGH: {IMGUI_LOG("ViewModeStateMachine", "AvmNotActiveReason", "NOTACTIVE_REASON_SPEED_TOO_HIGH"); break;}
      default: {IMGUI_LOG("ViewModeStateMachine", "AvmNotActiveReason", "NOTACTIVE_REASON_NONE"); break;}
  }

  switch (l_rCompeteWindowIDContainer.m_Data)
  {
      case cc::target::common::EAVMScreen::SCREEN_NONE: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_NONE"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FULL_R: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_FULL_R"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FULL_NOTR: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_FULL_NOTR"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_PARK: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_FLOAT_PARK"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_NOTPARK: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_FLOAT_NOTPARK"); break;}
      case cc::target::common::EAVMScreen::SCREEN_CALIBRATION: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_CALIBRATION"); break;}
      default: {IMGUI_LOG("Compete", "CompeteWindowID", "SCREEN_NONE"); break;}
  }

  static ECompeteReqStatus l_preCompeteReqStatus = ECompeteReqStatus_COMPETE_REQ_NONE;
  const ECompeteReqStatus l_curCompeteReqStatus = s_ViewModeStateMachine.getOut_competeReqStatus();
  switch (l_curCompeteReqStatus)
  {
      case ECompeteReqStatus_COMPETE_REQ_NONE: {IMGUI_LOG("Compete", "CompeteReqStatus", "REQ_NONE"); break;}
      case ECompeteReqStatus_COMPETE_REQ_STARTED: {IMGUI_LOG("Compete", "CompeteReqStatus", "REQ_STARTED"); break;}
      case ECompeteReqStatus_COMPETE_REQ_SUCCEED: {IMGUI_LOG("Compete", "CompeteReqStatus", "REQ_SUCCEED"); break;}
      case ECompeteReqStatus_COMPETE_REQ_TIMEOUT: {IMGUI_LOG("Compete", "CompeteReqStatus", "REQ_TIMEOUT"); break;}
      case ECompeteReqStatus_COMPETE_REFUSED: {IMGUI_LOG("Compete", "CompeteReqStatus", "REFUSED"); break;}
      default: {IMGUI_LOG("Compete", "CompeteReqStatus", "REQ_NONE"); break;}
  }

  if (l_curCompeteReqStatus != l_preCompeteReqStatus)
  {
      l_preCompeteReqStatus = l_curCompeteReqStatus;
      switch ( l_curCompeteReqStatus )
      {
          case ECompeteReqStatus_COMPETE_REQ_STARTED:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
              XLOG_INFO( g_viewModeSMContext , "CompeteInfo: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", CompeteReq Started!");
            }
            break;
          }
          case ECompeteReqStatus_COMPETE_REQ_SUCCEED:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
              XLOG_INFO( g_viewModeSMContext , "CompeteInfo: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", CompeteReq Succeed!");
            }
            break;
          }
          case ECompeteReqStatus_COMPETE_REQ_TIMEOUT:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
              XLOG_ERROR( g_viewModeSMContext , "CompeteError: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", CompeteReq TimeOut");
            }
            break;
          }
          case ECompeteReqStatus_COMPETE_REFUSED:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
              XLOG_ERROR( g_viewModeSMContext , "CompeteError: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", CompeteReq Refused");
            }
            break;
          }
          case ECompeteReqStatus_COMPETE_IGNORE_REPONSE:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
              XLOG_ERROR( g_viewModeSMContext , "CompeteError: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", competeResponsed: " << static_cast<vfc::int32_t> (competeResponseRecord)<<", but SM Ignored!");
            }
            break;
          }
          case ECompeteReqStatus_COMPETE_IGNORE_TIMEOUT:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
              XLOG_ERROR( g_viewModeSMContext , "CompeteError: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", CompeteReq Timeout, but SM Ignored!");
            }
            break;
          }
          case ECompeteReqStatus_COMPETE_REQ_NONE:
          default:
          {
            for (vfc::int32_t i =0; i < 3; i++)
            {
                XLOG_INFO( g_viewModeSMContext , "CompeteInfo: CompeteReqScreenID:" << static_cast<vfc::int32_t> (l_rCompeteWindowIDContainer.m_Data) <<", CompeteReq Status ChangedTo: "<< static_cast<vfc::int32_t> (l_curCompeteReqStatus));
            }
            break;
          }
      }
  }

  static cc::target::common::EAVMScreen l_AvmScreenType = cc::target::common::EAVMScreen::SCREEN_NONE;
  if (l_rAvmScreenTypeContainer.m_Data != l_AvmScreenType)
  {
      l_AvmScreenType = l_rAvmScreenTypeContainer.m_Data;
      XLOG_INFO( g_viewModeSMContext , "AvmScreenType ChangedTo:" << static_cast<vfc::int32_t> (l_AvmScreenType));
  }


  switch (l_rAvmScreenTypeContainer.m_Data)
  {

      case cc::target::common::EAVMScreen::SCREEN_NONE: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_NONE"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FULL_R: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FULL_R"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FULL_NOTR: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FULL_NOTR"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_PARK: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_PARK"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_NOTPARK: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_NOTPARK"); break;}
      case cc::target::common::EAVMScreen::SCREEN_CALIBRATION: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_CALIBRATION"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_R: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_PLAN_R"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_NOTR: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_PLAN_NOTR"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_R: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_FR_R"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_NOTR: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_FR_NOTR"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_PLAN_PARK: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_PLAN_PARK"); break;}
      case cc::target::common::EAVMScreen::SCREEN_FLOAT_FR_PARK: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_FLOAT_FR_PARK"); break;}
      default: {IMGUI_LOG("Compete", "AvmScreenType", "SCREEN_NONE"); break;}
  }

  switch (s_ViewModeStateMachine.getOut_ShowReqMod())
  {
      case SHOWREQ_NONE: {IMGUI_LOG("ViewModeStateMachine", "ShowReqMod", "SHOWREQ_NONE"); break;}
      case SHOWREQ_FULL_SCREEN: {IMGUI_LOG("ViewModeStateMachine", "ShowReqMod", "SHOWREQ_FULL_SCREEN"); break;}
      case SHOWREQ_FLOAT_SCREEN_PARK: {IMGUI_LOG("ViewModeStateMachine", "ShowReqMod", "SHOWREQ_FLOAT_SCREEN_PARK"); break;}
      case SHOWREQ_FLOAT_SCREEN_NOTPARK: {IMGUI_LOG("ViewModeStateMachine", "ShowReqMod", "SHOWREQ_FLOAT_SCREEN_NOTPARK"); break;}
      default: {IMGUI_LOG("ViewModeStateMachine", "ShowReqMod", "SHOWREQ_NONE"); break;}
  }

  switch (l_rVoiceDockFeedbackContainer.m_Data)
  {
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_NONE: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_NONE"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_SUCCESS: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_SUCCESS"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_NOTPN: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_NOTPN"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_SPDHIGH: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_SPDHIGH"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_RESERVED: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_RESERVED"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_OTHER: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_OTHER"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_CONFIGNOTSUPPORT: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_CONFIGNOTSUPPORT"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_AVM_ERROR: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_AVM_ERROR"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_POWER_SAVEMODE: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_POWER_SAVEMODE"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_UNAVAILABLE_IN_SR: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_UNAVAILABLE_IN_SR"); break;}
      case cc::target::common::EVoiceDockFb::EVOICEDOCKFB_OPEN_FAIL_NOT_SUPPORT: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_OPEN_FAIL_NOT_SUPPORT"); break;}
      default: {IMGUI_LOG("ViewModeStateMachine", "VoiceDockFeedback", "EVOICEDOCKFB_NONE"); break;}
  }

  if (l_rCpcOverlaySwitchContainer.m_Data == false)
  {
      IMGUI_LOG("ViewModeStateMachine", "CpcOverlaySwitch", "OFF");
  }
  else
  {
      IMGUI_LOG("ViewModeStateMachine", "CpcOverlaySwitch", "ON");
  }

// #endif

#if EXTRINSIC_UPDATE
  static bool initialized = false;
  static const int NUM_CAMERA = 4;
  static std::array<pc::c2w::ExtrinsicCalibration*, NUM_CAMERA> s_extCamArray;
  static std::array<unsigned int, NUM_CAMERA> s_modifiedCountArray;

  if (!initialized)
  {
    initialized = true;
    pc::util::coding::CodingManager* codingManager = pc::util::coding::getCodingManager();
    s_extCamArray[pc::core::sysconf::EXT_FRONT_CAMERA] = static_cast<pc::c2w::ExtrinsicCalibration*>(codingManager->getItem("ExtCalibFront"));
    s_extCamArray[pc::core::sysconf::EXT_RIGHT_CAMERA] = static_cast<pc::c2w::ExtrinsicCalibration*>(codingManager->getItem("ExtCalibRight"));
    s_extCamArray[pc::core::sysconf::EXT_REAR_CAMERA]  = static_cast<pc::c2w::ExtrinsicCalibration*>(codingManager->getItem("ExtCalibRear"));
    s_extCamArray[pc::core::sysconf::EXT_LEFT_CAMERA]  = static_cast<pc::c2w::ExtrinsicCalibration*>(codingManager->getItem("ExtCalibLeft"));
    s_modifiedCountArray[pc::core::sysconf::EXT_FRONT_CAMERA] = s_extCamArray[pc::core::sysconf::EXT_FRONT_CAMERA]->getModifiedCount();
    s_modifiedCountArray[pc::core::sysconf::EXT_RIGHT_CAMERA] = s_extCamArray[pc::core::sysconf::EXT_RIGHT_CAMERA]->getModifiedCount();
    s_modifiedCountArray[pc::core::sysconf::EXT_REAR_CAMERA]  = s_extCamArray[pc::core::sysconf::EXT_REAR_CAMERA]->getModifiedCount();
    s_modifiedCountArray[pc::core::sysconf::EXT_LEFT_CAMERA]  = s_extCamArray[pc::core::sysconf::EXT_LEFT_CAMERA]->getModifiedCount();
  }
  else
  {
    bool isNewUpdateFromRMC = (
      (s_modifiedCountArray[pc::core::sysconf::EXT_FRONT_CAMERA] != s_extCamArray[pc::core::sysconf::EXT_FRONT_CAMERA]->getModifiedCount()) ||
      (s_modifiedCountArray[pc::core::sysconf::EXT_RIGHT_CAMERA] != s_extCamArray[pc::core::sysconf::EXT_RIGHT_CAMERA]->getModifiedCount()) ||
      (s_modifiedCountArray[pc::core::sysconf::EXT_REAR_CAMERA]  != s_extCamArray[pc::core::sysconf::EXT_REAR_CAMERA]->getModifiedCount()) ||
      (s_modifiedCountArray[pc::core::sysconf::EXT_LEFT_CAMERA]  != s_extCamArray[pc::core::sysconf::EXT_LEFT_CAMERA]->getModifiedCount())
    );
    if (isNewUpdateFromRMC)
    {
        s_modifiedCountArray[pc::core::sysconf::EXT_FRONT_CAMERA] = s_extCamArray[pc::core::sysconf::EXT_FRONT_CAMERA]->getModifiedCount();
        s_modifiedCountArray[pc::core::sysconf::EXT_RIGHT_CAMERA] = s_extCamArray[pc::core::sysconf::EXT_RIGHT_CAMERA]->getModifiedCount();
        s_modifiedCountArray[pc::core::sysconf::EXT_REAR_CAMERA]  = s_extCamArray[pc::core::sysconf::EXT_REAR_CAMERA]->getModifiedCount();
        s_modifiedCountArray[pc::core::sysconf::EXT_LEFT_CAMERA]  = s_extCamArray[pc::core::sysconf::EXT_LEFT_CAMERA]->getModifiedCount();
        pc::daddy::CameraCalibrationDaddy& l_calib = pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.reserveLastDelivery();
        l_calib.m_Data[pc::core::sysconf::EXT_FRONT_CAMERA].setExtrinsicCalibration(*s_extCamArray[pc::core::sysconf::EXT_FRONT_CAMERA]);
        l_calib.m_Data[pc::core::sysconf::EXT_RIGHT_CAMERA].setExtrinsicCalibration(*s_extCamArray[pc::core::sysconf::EXT_RIGHT_CAMERA]);
        l_calib.m_Data[pc::core::sysconf::EXT_REAR_CAMERA].setExtrinsicCalibration(*s_extCamArray[pc::core::sysconf::EXT_REAR_CAMERA]);
        l_calib.m_Data[pc::core::sysconf::EXT_LEFT_CAMERA].setExtrinsicCalibration(*s_extCamArray[pc::core::sysconf::EXT_LEFT_CAMERA]);
        pc::daddy::BaseDaddyPorts::sm_cameraCalibrationDaddySenderPort.deliver();
    }
  }
#endif // EXTRINSIC UPDATE

  //! update intrinsic data
//   static vfc::int32_t l_updateIntrinsicCounter = 0;
//   if (l_updateIntrinsicCounter < 2)  // update 2 times to make sure it is delivered
//   {
//     cc::core::readDefaultIntrinsicData();
//     l_updateIntrinsicCounter++;
//   }



  //! Pdm status to Pdm

//   if ( true == (bool)s_ViewModeStateMachine.getOut_autoCamStsWriteToPdmNeeded() ) // PRQA S 3080
//   {
//     cc::daddy::PdmAutoCamStsDaddy_t& l_rPdmAutoCamStsContainer = cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_AutoCamStsDaddy_SenderPort.reserve();
//     if ( ESettingSts_Set_ON == l_rSettingAutoCamStContainer.m_Data )
//     {
//       l_rPdmAutoCamStsContainer.m_Data = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_ENABLED;
//       s_ViewModeStateMachine.setIn_pdmAutoCamIndi( EPdmSetting_ENABLED );
//     }
//     else
//     {
//       l_rPdmAutoCamStsContainer.m_Data = cc::target::common::EPdmSvsSetting::PDMSVSSETTING_DISABLED;
//       s_ViewModeStateMachine.setIn_pdmAutoCamIndi( EPdmSetting_DISABLED );
//     }
//     cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_AutoCamStsDaddy_SenderPort.deliver();
//   }


  //! Deliver Daddy ports.
  cc::daddy::CustomDaddyPorts::sm_ViewModeState_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_systemState_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSCurrentViewDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSShowReqDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_VRSwitchFailStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSViewModeStsDaddy_SenderPort.deliver();
  //cc::daddy::CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSzoomStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSFrViewStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSLeViewStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSReViewStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSRiViewStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSFreeModeStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_PasButtonPressedStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_AutoCamActivButtonPressedStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_ParkModeSelectedStDaddy_SenderPort.deliver();
#ifndef USE_VIRTUAL_OBJECT
  cc::daddy::CustomDaddyPorts::sm_ParkSlotSelectedStDaddy_SenderPort.deliver();
#endif
  cc::daddy::CustomDaddyPorts::sm_ParkTypeSelectedStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_FreeParkingSpaceTypeButtonStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_FreeParkingConfirmButtonStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_ParkOutSideButtonStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_ParkPauseButtonStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_ParkDirectionStDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSAutoCamStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_CpcOverlaySwitchDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SwVersionShowSwitchDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_avmNotActiveReason_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_avmScreenType_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_voiceDockFeedback_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_competeWindowIDRequest_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSDisplayedViewmodeGroupDaddy_SenderPort.deliver();
  // Start of BYD
  cc::daddy::CustomDaddyPorts::sm_SVSViewStateDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSWorkModeDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSOnOffStateDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSvidoutModeDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSRotateStatusDaddy_SenderPort.deliver();
//   cc::daddy::CustomDaddyPorts::sm_SVSVehTransStsDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSTrajCfgStateDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSLVDSvidOutModeDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSImageConfigDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSCarBodyDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSExpandedViewStateDaddy_SenderPort.deliver();
  cc::daddy::CustomDaddyPorts::sm_SVSNewExpandedViewStateDaddy_SenderPort.deliver();
//   cc::daddy::CustomDaddyPorts::sm_SVSUnavlMsgsDaddy_SenderPort.deliver();
  // End of BYD
  //! Cleanup Daddy ports.
  m_DoorStateViewModeSMReceiver.cleanup();
  m_MirrorStateViewModeSMReceiver.cleanup();
  m_GearViewModeSMReceiver.cleanup();
  m_VehicleSpeedViewModeSMReceiver.cleanup();
//   m_UssDataViewModeSMReceiver.cleanup();
  m_degradationMaskSMReceiver.cleanup();
  m_PIVI_ManualVideoSetupReqSMReceiver.cleanup();
  m_RequestViewIdSMReceiver.cleanup();
  m_PowerModeSMReceiver.cleanup();
  m_HUShoWReqSMReceiver.cleanup();
  m_HUselSVSModeSMReceiver.cleanup();
  m_HUtouchEvenTypeSMReceiver.cleanup();
  m_huVehColorReqSMReceiver.cleanup();
  m_ParkStatusSMReceiver.cleanup();
  m_ParkTypeSMReceiver.cleanup();
  m_ParkTypeVariantSMReceiver.cleanup();
  m_ParkModeSMReceiver.cleanup();
  m_parkHmiParkAPAPARKMODESMReceiver.cleanup();
  m_ParkSpaceSMReceiver.cleanup();
  m_parkHmiParkDriverIndSMReceiver.cleanup();
  m_parkHmiParkDriverIndExtSMReceiver.cleanup();
  m_parkHmiParkRecoverIndSMReceiver.cleanup();
  m_ParkReqReleaseBtnSMReceiver.cleanup();
  m_ParkRPADriverSelectedSMReceiver.cleanup();
  m_ParkDriverIndSearchSMReceiver.cleanup();
  m_FreeParkingActiveSMReceiver.cleanup();
  m_ParkDisp2TouchStsSMReceiver.cleanup();
  m_CameraPositionSMReceiver.cleanup();
  m_DegradationFidSMReceiver.cleanup();
  m_VariantSMReceiver.cleanup();
  m_touchCoordinateReceiver.cleanup();
  m_shoWSuspendReceiver.cleanup();
  m_PIVI_ViewBufferStatusACKSMReceiver.cleanup();
  m_NFSM_ViewBufferStatusViewModeSMReceiver.cleanup();
  m_ParkUISpotSMReceiver.cleanup();
  m_ParkConfirmInterfaceExistSMReceiver.cleanup();
  m_ParkUIAvailableParkingSlotSMReceiver.cleanup();
  m_animationStateSMReceiver.cleanup();
  m_VRSwitchSVMReceiver.cleanup();
  m_FCP_SVMButtonPressedReceiver.cleanup();
  m_indicatorStateSMReceiver.cleanup();
  m_PasWarnToneSMReceiver.cleanup();
  m_CustomVehicleLightSMReceiver.cleanup();
  m_pdmAutoCamStsSMReceiver.cleanup();
  m_pdmVehTransStsSMReceiver.cleanup();
  m_pdmVehColorStsSMReceiver.cleanup();
  m_HUDislayModeSwitchSMReceiver.cleanup();
  m_HUImageWorkModeSMReceiver.cleanup();
  m_HURotateStatusSMReceiver.cleanup();
  m_calibStateRecevier.cleanup();
  m_AndroidIconActiveRecevier.cleanup();
//   m_cpcStatusSMReceiverport.cleanup();
  m_CloseButtonPressedReceiver.cleanup();
  m_EnlargeButtonPressedReceiver.cleanup();
  m_FloatViewChangeButtonPressedReceiver.cleanup();
  m_huMODStsSMReceiver.cleanup();
  m_huDGearActStsSMReceiver.cleanup();
  m_huPASActStsSMReceiver.cleanup();
  m_huSteerActStsSMReceiver.cleanup();
  m_huNarrowLaneActSMReceiver.cleanup();
  m_SonarDistRangeSMReceiver.cleanup();
  m_SonarDistTrigLevelSMReceiver.cleanup();
  m_voiceDockRequestSMReceiver.cleanup();
  m_SRIsActiveSMReceiver.cleanup();
  m_competeActiveAllowReceiver.cleanup();
  m_competeQuitReceiver.cleanup();
  m_dockAvmButtonPressReceiver.cleanup();
  m_backKeyEventSMReceiver.cleanup();
  m_steeringWheelButtonPressSMReceiver.cleanup();
  m_steeringWheelButtonDefinitionSMReceiver.cleanup();
  m_ModWarningSMReceiver.cleanup();
  m_avmSoftwareErrorSMReceiver.cleanup();
  m_avmFileErrorSMReceiver.cleanup();
  m_engineTimeOutSMReceiver.cleanup();
  m_androidAliveSMReceiver.cleanup();
  m_driverSteeringWheelReceiver.cleanup();
  m_APAFuncStatusReceiver.cleanup();
  m_systemStrSMReceiver.cleanup();
  m_ADCUPowerSavModeSts_ReceiverPort.cleanup();
  m_ModWarningOverlayDisplaySMReceiver.cleanup();
  m_SRIsTopActivitySMReceiver.cleanup();
  m_cameraPositionChangeReceiver.cleanup();
  m_settingPageVehicleTransReceiver.cleanup();
}

vfc::float32_t ViewModeStateMachine::minUSSDistance(const vfc::float32_t f_dist1, const vfc::float32_t f_dist2, const vfc::float32_t f_dist3, const vfc::float32_t f_dist4)
  {
    vfc::float32_t l_ret = 0;
    if( f_dist1 < f_dist2 )
    {
      l_ret = f_dist1;
    } else
    {
      l_ret = f_dist2;
    }
    if( f_dist3 < l_ret )
    {
      l_ret = f_dist3;
    }
    if( f_dist4 < l_ret )
    {
      l_ret = f_dist4;
    }
    return l_ret;
  }

void ViewModeStateMachine::computeAndDaddySendCameraDeactivationMask(
  const pc::daddy::DoorStateDaddy* f_pCDoors,
  const pc::daddy::MirrorStateDaddy* /*f_pCSideMirrors, const cc::daddy::DegradationFid_t* f_fids*/)
{
  static pc::daddy::daddy4bitfield l_CameraDeactivationMask = 0u;
  // static bool l_TrunkNotClosed = false;
  static bool l_SideDoorNotClosed[pc::daddy::NUMBER_OF_SIDEMIRRORS] = { false }; //assume doors are closed by default (SIL case only)
  // static bool l_SideMirrorNotFlapped[pc::daddy::NUMBER_OF_SIDEMIRRORS] = { false };
  // static bool l_frontCamDeg = false;
  // static bool l_leftCamDeg  = false;
  constexpr static bool l_rearCamDeg  = false;
  // static bool l_rightCamDeg = false;

  // if (f_pCSideMirrors)
  // {
    // const pc::daddy::MirrorStateArray& l_SideMirrorsStatus = f_pCSideMirrors->m_Data;
    // l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_RIGHT] =
    //   (l_SideMirrorsStatus[pc::daddy::SIDEMIRROR_RIGHT] != pc::daddy::MIRRORSTATE_NOT_FLAPPED);
    // l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_LEFT ] =
    //   (l_SideMirrorsStatus[pc::daddy::SIDEMIRROR_LEFT ] != pc::daddy::MIRRORSTATE_NOT_FLAPPED);
  // }

  if (f_pCDoors != nullptr)
  {
    const vfc::int32_t* const l_DoorsStatus = f_pCDoors->m_Data;
    // l_TrunkNotClosed = ( l_DoorsStatus[pc::daddy::CARDOOR_TRUNK] != pc::daddy::CARDOORSTATE_CLOSED );
    l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_RIGHT] = (
          ( l_DoorsStatus[pc::daddy::CARDOOR_FRONT_RIGHT] != pc::daddy::CARDOORSTATE_CLOSED ) );
      // ! BYD requriement rear door not degrade
      //  || ( l_DoorsStatus[pc::daddy::CARDOOR_REAR_RIGHT ] != pc::daddy::CARDOORSTATE_CLOSED )  );
    l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_LEFT ] = (
          ( l_DoorsStatus[pc::daddy::CARDOOR_FRONT_LEFT ] != pc::daddy::CARDOORSTATE_CLOSED ) );
      // ! BYD requriement rear door not degrade
      //  || ( l_DoorsStatus[pc::daddy::CARDOOR_REAR_LEFT  ] != pc::daddy::CARDOORSTATE_CLOSED )  );
  }

  // if ( f_pCSideMirrors || f_pCDoors || f_fids)
  // if ( f_pCSideMirrors || f_pCDoors )
  if ( f_pCDoors  != nullptr)
  {
    //! Front camera: never disabled
    pc::daddy::unsetBit( pc::core::sysconf::FRONT_CAMERA, l_CameraDeactivationMask);
    pc::daddy::unsetBit( pc::core::sysconf::REAR_CAMERA, l_CameraDeactivationMask);
    pc::daddy::unsetBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDeactivationMask);
    pc::daddy::unsetBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask);
    //! Rear camera: disabled when trunk open
    // if ( l_TrunkNotClosed || l_rearCamDeg)
    if (l_rearCamDeg)  // ! BYD requirement, trunk door not degrade
    {
      pc::daddy::setBit( pc::core::sysconf::REAR_CAMERA, l_CameraDeactivationMask);
    }
    else
    {
      pc::daddy::unsetBit( pc::core::sysconf::REAR_CAMERA, l_CameraDeactivationMask);
    }
    //! Side cameras: disabled if mirror flapped or one of the two side doors open
    //side doors
    // if ( l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_RIGHT] || l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_RIGHT] || l_rightCamDeg)
    if (l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_RIGHT]) // ! BYD requirement, side mirror not degrade
    {
      pc::daddy::setBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask);
    }
    else
    {
      pc::daddy::unsetBit( pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask);
    }
    // if ( l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_LEFT ] || l_SideMirrorNotFlapped[pc::daddy::SIDEMIRROR_LEFT ] || l_leftCamDeg)
    if (l_SideDoorNotClosed[pc::daddy::SIDEMIRROR_LEFT]) // ! BYD requirement, side mirror not degrade
    {
      pc::daddy::setBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDeactivationMask);
    }
    else
    {
      pc::daddy::unsetBit( pc::core::sysconf::LEFT_CAMERA, l_CameraDeactivationMask);
    }

    pc::daddy::CameraDeactivationMaskDaddy & l_refCamDeaMsk =
        pc::daddy::BaseDaddyPorts::sm_CameraDeactivationMaskDaddySenderPort.reserve();
    l_refCamDeaMsk.m_Data = l_CameraDeactivationMask;
    pc::daddy::BaseDaddyPorts::sm_CameraDeactivationMaskDaddySenderPort.deliver();

//    using namespace pc::daddy;
//    using namespace pc::core;
    // pf code. #code looks fine
    VIEWMODE_SM_LOG( SM_General ) << "camera mask 0x" << std::hex << static_cast<vfc::int32_t>(l_CameraDeactivationMask)
      << std::dec << XLOG_ENDL;
    VIEWMODE_SM_LOG( SM_General )
      << " front "  << ( pc::daddy::isBitSet(pc::core::sysconf::FRONT_CAMERA, l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
      << " right "  << ( pc::daddy::isBitSet(pc::core::sysconf::RIGHT_CAMERA, l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
      << " rear  "  << ( pc::daddy::isBitSet(pc::core::sysconf::REAR_CAMERA , l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
      << " left  "  << ( pc::daddy::isBitSet(pc::core::sysconf::LEFT_CAMERA , l_CameraDeactivationMask ) ? "DISABLED" : "ENABLED" )
      << XLOG_ENDL;

  }
  return;
}


void ViewModeStateMachine::OnShutdown( void )
{
  // pf code. #code looks fine
  VIEWMODE_SM_LOG( SM_General ) << "Stopping ViewMode State Machine thread." << XLOG_ENDL;
  //! disconnect Daddy receiver ports
  pc::daddy::BaseDaddyPorts::sm_DoorOpenDaddySenderPort.disconnect( m_DoorStateViewModeSMReceiver ) ;
  pc::daddy::BaseDaddyPorts::sm_SideMirrorFlapDaddySenderPort.disconnect( m_MirrorStateViewModeSMReceiver ) ;
  pc::daddy::BaseDaddyPorts::sm_gearSenderPort.disconnect( m_GearViewModeSMReceiver ) ;
  pc::daddy::BaseDaddyPorts::sm_SpeedSenderPort.disconnect( m_VehicleSpeedViewModeSMReceiver );
  pc::daddy::BaseDaddyPorts::sm_CameraDegradationMaskDaddySenderPort.disconnect(m_degradationMaskSMReceiver);
//   cc::daddy::CustomDaddyPorts::sm_customUltrasonicDataDaddySenderPort.disconnect( m_UssDataViewModeSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_PIVI_ManualVideoSetupReq_SenderPort.disconnect( m_PIVI_ManualVideoSetupReqSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort.disconnect( m_RequestViewIdSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_PowerModeDaddy_SenderPort.disconnect( m_PowerModeSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_HUShoWReqDaddy_SenderPort.disconnect( m_HUShoWReqSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort.disconnect( m_HUselSVSModeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUtouchEvenTypeDaddy_SenderPort.disconnect( m_HUtouchEvenTypeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_VehicleDiffuseColorIndex_SenderPort.disconnect( m_huVehColorReqSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkStatusDaddy_SenderPort.disconnect( m_ParkStatusSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkTypeDaddy_SenderPort.disconnect( m_ParkTypeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkTypeVariantDaddy_SenderPort.disconnect( m_ParkTypeVariantSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkParkngTypeSeldDaddy_SenderPort.disconnect( m_ParkModeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkAPAPARKMODE_SenderPort.disconnect( m_parkHmiParkAPAPARKMODESMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkAPA_ParkSpaceDaddy_SenderPort.disconnect( m_ParkSpaceSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndDaddy_SenderPort.disconnect( m_parkHmiParkDriverIndSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndExtDaddy_SenderPort.disconnect( m_parkHmiParkDriverIndExtSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkRecoverIndDaddy_SenderPort.disconnect( m_parkHmiParkRecoverIndSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkReqReleaseBtnDaddy_SenderPort.disconnect( m_ParkReqReleaseBtnSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkRPADriverSelectedDaddy_SenderPort.disconnect( m_ParkRPADriverSelectedSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDisp2TouchStsDaddy_SenderPort.disconnect( m_ParkDisp2TouchStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_ParkDriverIndSearchDaddy_SenderPort.disconnect( m_ParkDriverIndSearchSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_ParkFreeParkingActiveDaddy_SenderPort.disconnect( m_FreeParkingActiveSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_CameraPositionDaddySenderPort.disconnect( m_CameraPositionSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_degradationFid_SenderPort.disconnect( m_DegradationFidSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_Variant_SenderPort.disconnect( m_VariantSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HmiData_SenderPort.disconnect( m_touchCoordinateReceiver );
  cc::daddy::CustomDaddyPorts::sm_HUShoWSuspendDaddy_SenderPort.disconnect( m_shoWSuspendReceiver );
  cc::daddy::CustomDaddyPorts::sm_SVSParkUISpotDataDaddy_SenderPort.disconnect( m_ParkUISpotSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SVSParkConfirmInterfaceDataDaddy_SenderPort.disconnect( m_ParkConfirmInterfaceExistSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SVSParkUIAvailableParkingSlotDaddy_SenderPort.disconnect( m_ParkUIAvailableParkingSlotSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_PIVI_ViewBufferStatusACK_SenderPort.disconnect( m_PIVI_ViewBufferStatusACKSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_viewAnimationCompleted_SenderPort.disconnect( m_animationStateSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_VRSwitchSVMDaddy_SenderPort.disconnect(m_VRSwitchSVMReceiver);
  cc::daddy::CustomDaddyPorts::sm_FCP_SVMButtonPressedDaddy_SenderPort.disconnect(m_FCP_SVMButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_indicatorStateSenderPort.disconnect(m_indicatorStateSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_PasWarnToneDaddy_SenderPort.disconnect(m_PasWarnToneSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_CustomVehicleLights_SenderPort.disconnect(m_CustomVehicleLightSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_AutoCamStsDaddy_SenderPort.disconnect( m_pdmAutoCamStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehTransStsDaddy_SenderPort.disconnect( m_pdmVehTransStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_PdmR5ToLinux_VehColorStsDaddy_SenderPort.disconnect( m_pdmVehColorStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeSwitchDaddy_SenderPort.disconnect( m_HUDislayModeSwitchSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandDaddy_SenderPort.disconnect( m_HUDislayModeExpandSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUImageWorkModeDaddy_SenderPort.disconnect( m_HUImageWorkModeSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUDislayModeExpandNewDaddy_SenderPort.disconnect( m_HUDislayModeExpandNewSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HURotateStatusDaddy_SenderPort.disconnect( m_HURotateStatusSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HUvehTransReqDaddy_SenderPort.disconnect( m_HUvehTransSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_calibState_SenderPort.disconnect( m_calibStateRecevier ) ;
  cc::daddy::CustomDaddyPorts::sm_androidIconActive_SenderPort.disconnect(m_AndroidIconActiveRecevier);
//   cc::daddy::CustomDaddyPorts::sm_cpcStatusDaddy_SenderPort.disconnect( m_cpcStatusSMReceiverport ) ;
  cc::daddy::CustomDaddyPorts::sm_CloseButtonPressedDaddy_SenderPort.disconnect(m_CloseButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_EnlargeButtonPressedDaddy_SenderPort.disconnect(m_EnlargeButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_FloatViewChangeButtonPressedDaddy_SenderPort.disconnect(m_FloatViewChangeButtonPressedReceiver);
  cc::daddy::CustomDaddyPorts::sm_HU_MODStsDaddy_SenderPort.disconnect( m_huMODStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_DGearActStsDaddy_SenderPort.disconnect( m_huDGearActStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_PASActStsDaddy_SenderPort.disconnect( m_huPASActStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_SteerActStsDaddy_SenderPort.disconnect( m_huSteerActStsSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_HU_NarrowLaneActDaddy_SenderPort.disconnect( m_huNarrowLaneActSMReceiver ) ;
  cc::daddy::CustomDaddyPorts::sm_SonarAPPData_SenderPort.disconnect( m_SonarDistRangeSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SonarDistTrigLevel_SenderPort.disconnect( m_SonarDistTrigLevelSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_voiceDockRequest_SenderPort.disconnect ( m_voiceDockRequestSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_SRIsActive_SenderPort.disconnect ( m_SRIsActiveSMReceiver );
  cc::daddy::CustomDaddyPorts::sm_competeActiveAllow_SenderPort.disconnect( m_competeActiveAllowReceiver );
  cc::daddy::CustomDaddyPorts::sm_competeQuit_SenderPort.disconnect( m_competeQuitReceiver );
  cc::daddy::CustomDaddyPorts::sm_dockAvmButtonPress_SenderPort.disconnect( m_dockAvmButtonPressReceiver );
  cc::daddy::CustomDaddyPorts::sm_DriverSteeringWheelAngleDaddy_SenderPort.disconnect(m_driverSteeringWheelReceiver);
  cc::daddy::CustomDaddyPorts::sm_APAFuncStatus_SenderPort.disconnect(m_APAFuncStatusReceiver);
  cc::daddy::CustomDaddyPorts::sm_systemStr_SenderPort.disconnect(m_systemStrSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_ADCUPowerSavModeSts_SenderPort.disconnect(m_ADCUPowerSavModeSts_ReceiverPort);
  cc::daddy::CustomDaddyPorts::sm_backKeyEvent_SenderPort.disconnect(m_backKeyEventSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonPress_SenderPort.disconnect(m_steeringWheelButtonPressSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_steeringWheelButtonDefinition_SenderPort.disconnect(m_steeringWheelButtonDefinitionSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_ModStateDaddy.disconnect(m_ModWarningSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_ModWarningOverlayDisplay_SenderPort.disconnect(m_ModWarningOverlayDisplaySMReceiver);
  cc::daddy::CustomDaddyPorts::sm_avmSoftwareError_SenderPort.disconnect(m_avmSoftwareErrorSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_avmFileError_SenderPort.disconnect(m_avmFileErrorSMReceiver);


  cc::daddy::CustomDaddyPorts::sm_engineTimeOut_SenderPort.disconnect(m_engineTimeOutSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_androidAlive_SenderPort.disconnect(m_androidAliveSMReceiver);
  cc::daddy::CustomDaddyPorts::sm_SRIsTop_SenderPort.disconnect(m_SRIsTopActivitySMReceiver);
  cc::daddy::CustomDaddyPorts::sm_CameraPositionChange_SenderPort.disconnect(m_cameraPositionChangeReceiver);
  cc::daddy::CustomDaddyPorts::sm_PdmLinuxToR5_VehTransStsDaddy_SenderPort.disconnect(m_settingPageVehicleTransReceiver);
  s_ViewModeStateMachine.terminate() ;
}

