//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: Ha Thanh Phong (MS/EDA92-XC)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  Utils.h
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/Utils.h"
#include "cc/assets/uielements/inc/CustomImageOverlays.h"
#include "cc/core/inc/CustomScene.h"

#include <vfc/core/vfc_types.hpp> // PRQA S 0034

#include <osg/ref_ptr>
#include <osg/Image>
#include <osgDB/ReadFile>

using cc::core::g_views;

namespace cc
{
namespace assets
{
namespace uielements
{

pc::util::coding::Item<UiUtilSettings> g_uiUtilSettings("UI Utilities");


inline osg::Vec2f scaleFactorMainview()
{
  return osg::Vec2f {
    g_views->m_mainViewport.m_size.x() / g_uiUtilSettings->m_baseMainviewLayout.x(), // PRQA S 3011
    g_views->m_mainViewport.m_size.y() / g_uiUtilSettings->m_baseMainviewLayout.y() // PRQA S 3011
  };
}

inline osg::Vec2f scaleFactorPlanview()
{
  return osg::Vec2f {
    g_views->m_planViewport.m_size.x() / g_uiUtilSettings->m_basePlanviewLayout.x(), // PRQA S 3011
    g_views->m_planViewport.m_size.y() / g_uiUtilSettings->m_basePlanviewLayout.y() // PRQA S 3011
  };
}

osg::Vec2f getImageSizeHori(const std::string& f_iconPath)
{
  const osg::ref_ptr <osg::Image> l_image = osgDB::readImageFile(f_iconPath);
  if (l_image != nullptr)
  {
    osg::Vec2f planviewScaleFactor = scaleFactorPlanview();
    return osg::Vec2f(
      static_cast<vfc::float32_t>(l_image->s()) * planviewScaleFactor.x(),
      static_cast<vfc::float32_t>(l_image->t()) * planviewScaleFactor.y());
  }
  else
  {
    return osg::Vec2f(0.0f, 0.0f);
  }
}


osg::Vec2f getImageSizeVert(const std::string& f_iconPath) //! TODO: layout scaling not handled
{
  const osg::ref_ptr <osg::Image> l_image = osgDB::readImageFile(f_iconPath);
  if (l_image != nullptr)
  {
    // 0.9 = (720/1080) / (720/972) = 972/1080  image size transfer from horizontal view to vertical view
    return osg::Vec2f(
      static_cast<vfc::float32_t>(0.9f*static_cast<vfc::float32_t>(l_image->t())),
      static_cast<vfc::float32_t>(0.9f*static_cast<vfc::float32_t>(l_image->s())));
  }
  else
  {
    return osg::Vec2f(0.0f, 0.0f);
  }
}


osg::Vec2f getSlotImageSizeVert(const std::string& f_iconPath) //! TODO: layout scaling not handled
{
  const osg::ref_ptr <osg::Image> l_image = osgDB::readImageFile(f_iconPath);
  if (l_image != nullptr)
  {
    return osg::Vec2f(
      static_cast<vfc::float32_t>(0.81f*static_cast<vfc::float32_t>(l_image->t())),
      static_cast<vfc::float32_t>(0.81f*static_cast<vfc::float32_t>(l_image->s())));
  }
  else
  {
    return osg::Vec2f(0.0f, 0.0f);
  }
}


osg::Vec2f transferToBottomLeftHori(const osg::Vec2f f_iconPos)
{
  osg::Vec2f planviewScaleFactor = scaleFactorPlanview();
  osg::Vec2f l_iconCenter = osg::Vec2f {
    static_cast<vfc::float32_t>(g_views->m_planViewport.m_origin.x())
      + f_iconPos.x() * planviewScaleFactor.x(),
    static_cast<vfc::float32_t>(g_views->m_planViewport.m_origin.y()) + static_cast<vfc::float32_t>(g_views->m_planViewport.m_size.y())
      - f_iconPos.y() * planviewScaleFactor.y()
  };
  return l_iconCenter;
}


osg::Vec2f transferToBottomLeftHoriHU(const osg::Vec2f f_iconPos)
{
  osg::Vec2f planviewScaleFactor = scaleFactorPlanview();
  osg::Vec2f l_iconCenter = osg::Vec2f {
    static_cast<vfc::float32_t>(g_views->m_planViewport.m_origin.x())
      + f_iconPos.x() * planviewScaleFactor.x(),
    f_iconPos.y() * planviewScaleFactor.y()
  };
  return l_iconCenter;
}


osg::Vec2f transferToBottomLeftVert(const osg::Vec2f f_iconPos)
{
  return osg::Vec2f(f_iconPos.x() + static_cast<vfc::float32_t>(g_views->m_vertMainViewport.m_size.x()), f_iconPos.y());
}


osg::Vec2f getImageSize(const std::string& f_iconPath)
{
  return getImageSizeHori(f_iconPath);
}


pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool& f_isHoriScreen)
{
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, false, false, true, f_isHoriScreen); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


pc::assets::Icon* createIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen)
{
  // pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, false, false, true, f_isHoriScreen); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}

pc::assets::Icon* createAlphaMaskIcon(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen)
{
  // pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, false, false, true, f_isHoriScreen, cc::assets::uielements::CustomIcon::ALPHA_MASK, cc::assets::uielements::CustomIcon::AnimationDir::START_FROM_TOP); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


pc::assets::Icon* createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const osg::Vec2f& f_iconSize, const bool& f_isHoriScreen)
{
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, false, false, true, f_isHoriScreen); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setSize(f_iconSize, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


pc::assets::Icon* createIconTopLeft(const std::string& f_iconPath, const osg::Vec2f& f_iconPos, const bool& f_isHoriScreen)
{
  // pc::assets::Icon* l_icon = new pc::assets::Icon(f_iconPath, true);
  cc::assets::uielements::CustomIcon* const l_icon = new cc::assets::uielements::CustomIcon(f_iconPath, false, false, true, f_isHoriScreen); // PRQA S 2759
  l_icon->setOrigin(pc::assets::Icon::Origin::TopLeft);
  l_icon->setPosition(f_iconPos, pc::assets::Icon::UnitType::Pixel);
  l_icon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
  l_icon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
  l_icon->setEnabled(false);
  return l_icon;
}


osg::Vec2f getSurroundViewPosition(const osg::Vec2f& f_iconPos)
{
  const osg::Vec2f planviewScaleFactor = scaleFactorPlanview(); // PRQA S 3803
  osg::Vec2f mainviewScaleFactor = scaleFactorMainview();

  osg::Vec2f l_iconCenter = osg::Vec2f {
    static_cast<vfc::float32_t>(cc::core::g_views->m_mainViewport.m_origin.x())
      + (f_iconPos.x() - g_uiUtilSettings->m_basePlanviewLayout.x()) * mainviewScaleFactor.x(),
    f_iconPos.y() * mainviewScaleFactor.y()
  };

  return l_iconCenter;
}


osg::Vec2f getSurroundViewIconSize(const osg::Vec2f& f_iconSize)
{
  osg::Vec2f planviewScaleFactor = scaleFactorMainview();
  return osg::Vec2f(
    f_iconSize.x() * planviewScaleFactor.x(),
    f_iconSize.y() * planviewScaleFactor.y()
  );
}

bool checkCoorInResponseArea(vfc::uint16_t f_coorX, vfc::uint16_t f_coorY, osg::Vec2f f_center, osg::Vec2f f_responseArea)
{
    bool l_ret = false;
    if (   (f_coorX > (static_cast<vfc::uint16_t>(f_center.x()) - (static_cast<vfc::uint16_t>(f_responseArea.x())>>1))) // PRQA S 3016
        && (f_coorX < (static_cast<vfc::uint16_t>(f_center.x()) + (static_cast<vfc::uint16_t>(f_responseArea.x())>>1))) // PRQA S 3016
        && (f_coorY > (static_cast<vfc::uint16_t>(f_center.y()) - (static_cast<vfc::uint16_t>(f_responseArea.y())>>1))) // PRQA S 3016
        && (f_coorY < (static_cast<vfc::uint16_t>(f_center.y()) + (static_cast<vfc::uint16_t>(f_responseArea.y())>>1)))) // PRQA S 3016
    {
        l_ret = true;
    }
    return l_ret;
}


} // namespace uielements
} // namespace assets
} // namespace cc // PRQA S 1041
