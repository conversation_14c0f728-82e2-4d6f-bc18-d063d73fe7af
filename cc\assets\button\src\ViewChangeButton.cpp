//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert Bosch GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/ViewChangeButton.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/imgui/inc/imgui_manager.h"
#include "pc/generic/util/coding/inc/MemberWrapper.h"
#include "cc/assets/button/inc/EllipticalSlidingBar.h"
namespace cc
{
namespace assets
{
namespace button
{
namespace viewchangebutton
{

pc::util::coding::Item<ViewChangeButtonSettings> g_FrontButtonSettings("FrontViewButton");
pc::util::coding::Item<ViewChangeButtonSettings> g_RearButtonSettings("RearViewButton");
pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveFrontButtonSettings("PerspectiveFrontViewButton");
pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveFrontLeftButtonSettings("PerspectiveFrontLeftViewButton");
pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveFrontRightButtonSettings("PerspectiveFrontRightViewButton");
pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveRearButtonSettings("PerspectiveRearViewButton");
pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveRearLeftButtonSettings("PerspectiveRearLeftViewButton");
pc::util::coding::Item<ViewChangeButtonSettings>
    g_PerspectiveRearRightButtonSettings("PerspectiveRearRightViewButton");
pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveLeftButtonSettings("PerspectiveLeftViewButton");
pc::util::coding::Item<ViewChangeButtonSettings> g_PerspectiveRightButtonSettings("PerspectiveRightViewButton");
pc::util::coding::Item<PerspectiveViewButtonBackground>
    g_PerspectiveViewButtonBackground("PerspectiveViewButtonBackground");
ViewChangeButton::ViewChangeButton(
    cc::core::AssetId         f_assetId,
    ViewChangeButtonSettings* f_settings,
    EScreenID                 f_screenId,
    osg::Camera*              f_referenceView)
    : Button{f_assetId, f_referenceView}
    , m_settings{f_settings}
    , m_screenId{f_screenId}
    , m_delayCounter{0}
    , m_gearChangedToP{false}
{
    setTexturePath(m_settings->m_buttonTexture.m_AvailableTexturePath);
    setPositionHori(m_settings->m_horiPos);
    setState(AVAILABLE);
}

void ViewChangeButton::onInvalid()
{
    setIconEnable(false);
}

void ViewChangeButton::onUnavailable()
{
    setIconEnable(false);
}

void ViewChangeButton::onAvailable()
{
    setIconEnable(true);
    setTexturePath(m_settings->m_buttonTexture.m_AvailableTexturePath);
}

void ViewChangeButton::onSelected()
{
    setIconEnable(true);
    setTexturePath(m_settings->m_buttonTexture.m_SelectedTexturePath);
}

void ViewChangeButton::onReleased()
{
    setIconEnable(true);
    SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, EScreenID::EScreenID_NO_CHANGE);
    setTexturePath(m_settings->m_buttonTexture.m_AvailableTexturePath);
}

//! SingleViewChangeButton
SingleViewChangeButton::SingleViewChangeButton(
    cc::core::AssetId         f_assetId,
    pc::core::Framework*      f_framework,
    ViewChangeButtonSettings* f_settings,
    EScreenID                 f_screenId,
    osg::Camera*              f_referenceView)
    : ViewChangeButton{f_assetId, f_settings, f_screenId, f_referenceView}
    , m_framework{f_framework}
    , m_preViewMode{ESVSViewMode::ESVSViewMode_VM_Default}
    , m_preGear{pc::daddy::EGear::GEAR_INIT}

{
    Object::setName(getViewChangeButtonName());
}

void SingleViewChangeButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        SEND_PORT(cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort, getScreenId());
        // SEND_PORT(CustomDaddyPorts::sm_HUselSVSModeDaddy_SenderPort, ESVSViewMode::ESVSViewMode_VM_Standard);
        // if (cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort.isConnected())
        // {
        //     auto& l_container = cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort.reserve();
        //     l_container.m_Data = getScreenId();
        //     cc::daddy::CustomDaddyPorts::sm_RequestViewId_SenderPort.deliver();
        // }
    }
}

void SingleViewChangeButton::update()
{
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)



    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)

    GET_PORT_DATA(displayedViewContainer, m_framework->asCustomFramework()->m_displayedView_ReceiverPort, displayedViewPortHaveData)



    GET_PORT_DATA(viewModeStsContainer, m_framework->asCustomFramework()->m_viewModeStatus_ReceiverPort, viewModeStsPortHaveData)



    // GET_PORT_DATA(
    //     showReqContainer,
    //     m_framework->asCustomFramework()->m_showReq_ReceiverPort,
    //     showReqPortHaveData);

    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

    EScreenID    l_displayedView  = EScreenID_NO_CHANGE;
    ESVSViewMode l_curViewModeSts = ESVSViewMode_VM_Default;
    EGear        l_curGear        = EGear_Init;
    if (displayedViewPortHaveData)
    {
        l_displayedView = displayedViewContainer->m_Data;
    }

    if (viewModeStsPortHaveData)
    {
        l_curViewModeSts = viewModeStsContainer->m_Data;
    }

    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = static_cast<vfc::uint8_t>(l_pData->m_Data);
        }
    }

//    const auto touchInsideViewport     = checkTouchInsideViewport(); // PRQA S 3803
    const bool touchInsideResponseArea = checkTouchInsideResponseArea();
//    const bool viewModeChanged         = (m_preViewMode != l_curViewModeSts);
//    const bool gearChanged             = (m_preGear != l_curGear);
    m_preViewMode                = l_curViewModeSts;
    // bool l_showReq                 = false;
    // if ( showReqPortHaveData )
    // {
    //     l_showReq = showReqContainer->m_Data;
    // }

    if( m_preGear != EGear_P && l_curGear == EGear_P )
    {
        m_gearChangedToP = true; // Gear Change to P
    }else if (m_preGear == EGear_P &&  l_curGear != EGear_P)
    {
        m_gearChangedToP = false; // Gear Change from P
    }
    else{}

    m_preGear                    = l_curGear;

    const bool buttonFitView =
        (l_displayedView == getScreenId());
    ButtonState currentState = getState();

    if ((l_curViewModeSts == ESVSViewMode::ESVSViewMode_VM_Perspective) || (l_curViewModeSts == ESVSViewMode::ESVSViewMode_VM_Wide))
    {
        currentState = INVALID;
    }
    else if ((l_curGear == EGear_D || l_curGear == EGear_R))
    {
        currentState = UNAVAILABLE;
    }
    else
    {
        switch (currentState)
        {
        case AVAILABLE:
        {
            if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
            {
                currentState = PRESSED;
            }
            else
            {
                if (buttonFitView)
                {
                    currentState = SELECTED;
                }
            }
            break;
        }
        case PRESSED:
        {
            if (touchStatusChanged && touchStatus() == TOUCH_UP)
            {
                currentState = RELEASED;
            }
            break;
        }
        case RELEASED:
        {
            currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
        }
        break;
        case SELECTED:
        {
            if (!buttonFitView && currentState == SELECTED)
            {
                currentState = AVAILABLE;
            }
            break;
        }
        case INVALID:
        case UNAVAILABLE:
        default:
        {
            if (l_curGear == EGear_R || l_curGear == EGear_D)
            {
                currentState = UNAVAILABLE; // PRQA S 2880
            }
            else  if ( m_gearChangedToP )
            {
                m_delayCounter++;
                if ( m_delayCounter > m_settings->m_displayDelay)
                {
                    currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
                    m_gearChangedToP = false;
                    m_delayCounter = 0;
                }else
                {
                    // Do nothing
                }
            }
            else
            {
                currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
            }
            break;
        }
        }
    }

    setState(currentState);

    switch (getState())
    {
        case INVALID:
        {
            IMGUI_LOG("Buttons", getName() + "State", "INVALID");
            break;
        }
        case UNAVAILABLE:
        {
            IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
            break;
        }
        case AVAILABLE:
        {
            IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
            break;
        }
        case PRESSED:
        {
            IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
            break;
        }
        case RELEASED:
        {
            IMGUI_LOG("Buttons", getName() + "State", "RELEASED");
            break;
        }
        case SELECTED:
        {
            IMGUI_LOG("Buttons", getName() + "State", "SELECTED");
            break;
        }
        default:
        {
            IMGUI_LOG("Buttons", getName() + "State", "INVALID");
            break;
        }
    }
}

//! PerspectiveViewChangeButton
PerspectiveViewChangeButtonBackground::PerspectiveViewChangeButtonBackground( // PRQA S 4207
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : cc::assets::button::Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_preGear{pc::daddy::EGear::GEAR_INIT}
    , m_delayCounter{0}
    , m_gearChangedToP{false}
    , m_frontPointIcons{}
    , m_midPointIcons{}
    , m_rearPointIcons{}
{
    setName("PerspectiveViewChangeButtonBackground");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    initRearPointIcons();
    initFrontPointIcons();
    initMidPointIcons();
    enableFrontPointIcons(false);
    enableRearPointIcons(false);
    enableMidPointIcons(false);
}

void PerspectiveViewChangeButtonBackground::initFrontPointIcons()
{
    cc::assets::uielements::CustomIcon* l_tempIcon=nullptr;

    //FL
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveFrontLeftButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveFrontLeftButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_frontPointIcons.addIcon(this,l_tempIcon);

    //FM
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveFrontButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveFrontButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_frontPointIcons.addIcon(this,l_tempIcon);

    //FR
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveFrontRightButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveFrontRightButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_frontPointIcons.addIcon(this,l_tempIcon);

}

void PerspectiveViewChangeButtonBackground::initMidPointIcons()
{
    cc::assets::uielements::CustomIcon* l_tempIcon=nullptr;

    //ML
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveLeftButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveLeftButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_midPointIcons.addIcon(this,l_tempIcon);

    //MR
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveRightButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveRightButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_midPointIcons.addIcon(this,l_tempIcon);
}

void PerspectiveViewChangeButtonBackground::initRearPointIcons()
{
    cc::assets::uielements::CustomIcon* l_tempIcon=nullptr;

    //RL
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveRearLeftButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveRearLeftButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_rearPointIcons.addIcon(this,l_tempIcon);

    //RM
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveRearButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveRearButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_rearPointIcons.addIcon(this,l_tempIcon);

    //RR
    l_tempIcon=new cc::assets::uielements::CustomIcon(g_PerspectiveRearRightButtonSettings.get()->m_buttonTexture.m_AvailableTexturePath, false, false, true, true);
    l_tempIcon->setOrigin(pc::assets::Icon::Origin::BottomLeft);
    l_tempIcon->setPosition(g_PerspectiveRearRightButtonSettings.get()->m_horiPos, pc::assets::Icon::UnitType::Pixel);
    l_tempIcon->setAlignmentHorizontal(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setAlignmentVertical(pc::assets::Icon::Alignment::Center);
    l_tempIcon->setEnabled(false);
    m_rearPointIcons.addIcon(this,l_tempIcon);

}

void PerspectiveViewChangeButtonBackground::enableFrontPointIcons(bool f_enable)
{
    m_frontPointIcons.setAllEnabled(f_enable);
}

void PerspectiveViewChangeButtonBackground::enableMidPointIcons(bool f_enable)
{
    m_midPointIcons.setAllEnabled(f_enable);
}

void PerspectiveViewChangeButtonBackground::enableRearPointIcons(bool f_enable)
{
    m_rearPointIcons.setAllEnabled(f_enable);
}

void PerspectiveViewChangeButtonBackground::update()
{
    ESVSViewMode l_curViewModeSts = ESVSViewMode_VM_Default;
    GET_PORT_DATA(viewModeStsContainer, m_framework->asCustomFramework()->m_viewModeStatus_ReceiverPort, viewModeStsPortHaveData)



    if (viewModeStsPortHaveData)
    {
        l_curViewModeSts = viewModeStsContainer->m_Data;
    }
    EGear l_curGear = EGear_Init;
    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = static_cast<vfc::uint8_t>(l_pData->m_Data);
        }
    }

    if( m_preGear != EGear_P && l_curGear == EGear_P )
    {
        m_gearChangedToP = true; // Gear Change to P
    }else if (m_preGear == EGear_P &&  l_curGear != EGear_P)
    {
        m_gearChangedToP = false; // Gear Change from P
    }
    else{}
    m_preGear                    = l_curGear;

    if (l_curViewModeSts != ESVSViewMode_VM_Perspective)
    {
        setState(UNAVAILABLE);
        enableFrontPointIcons(false);
        enableRearPointIcons(false);
        enableMidPointIcons(false);
    }
    else
    {
        setState(AVAILABLE);
        // if (l_curGear == EGear_D)
        // {
        //     setTexturePath(g_PerspectiveViewButtonBackground->m_buttomBackroundTexturePath);
        //     setPositionHori(g_PerspectiveViewButtonBackground->m_buttomBackroundTexturPos);
        //     enableFrontPointIcons(false);
        //     enableRearPointIcons(true);
        //     enableMidPointIcons(false);
        // }
        // else if (l_curGear == EGear_R)
        // {
        //     setTexturePath(g_PerspectiveViewButtonBackground->m_topBackroundTexturePath);
        //     setPositionHori(g_PerspectiveViewButtonBackground->m_topBackroundTexturPos);
        //     enableFrontPointIcons(true);
        //     enableRearPointIcons(false);
        //     enableMidPointIcons(false);
        // }
        // else
        {
            if ( m_gearChangedToP )
            {
                m_delayCounter++;
                if ( m_delayCounter > g_PerspectiveViewButtonBackground->m_displayDelay)
                {
                    setTexturePath(g_PerspectiveViewButtonBackground->m_fullBackroundTexturePath);
                    setPositionHori(g_PerspectiveViewButtonBackground->m_fullBackroundTexturPos);
                    enableFrontPointIcons(true);
                    enableRearPointIcons(true);
                    enableMidPointIcons(true);
                    m_gearChangedToP = false;
                    m_delayCounter = 0;
                }else
                {
                    // Do nothing
                }
            }
            else
            {
                setTexturePath(g_PerspectiveViewButtonBackground->m_fullBackroundTexturePath);
                setPositionHori(g_PerspectiveViewButtonBackground->m_fullBackroundTexturPos);
                enableFrontPointIcons(true);
                enableRearPointIcons(true);
                enableMidPointIcons(true);
            }
        }
    }
}

PerspectiveViewChangeButton::PerspectiveViewChangeButton(
    cc::core::AssetId         f_assetId,
    pc::core::Framework*      f_framework,
    ViewChangeButtonSettings* f_settings,
    EScreenID                 f_screenId,
    osg::Camera*              f_referenceView)
    : ViewChangeButton{f_assetId, f_settings, f_screenId, f_referenceView}
    , m_framework{f_framework}
    , m_preViewMode{ESVSViewMode::ESVSViewMode_VM_Default}
    , m_preGear{pc::daddy::EGear::GEAR_INIT}
{
    Object::setName(getViewChangeButtonName());
}

void PerspectiveViewChangeButton::onPressed()
{
    if (checkTouchInsideResponseArea())
    {
        using cc::daddy::CustomDaddyPorts;
        SEND_PORT(CustomDaddyPorts::sm_RequestViewId_SenderPort, getScreenId());
    }
    setIconEnable(true);
    setTexturePath(m_settings->m_buttonTexture.m_AvailableTexturePath);
}

void PerspectiveViewChangeButton::update()
{
    GET_PORT_DATA(touchStatusContainer, m_framework->asCustomFramework()->m_HUTouchTypeReceiver, touchStatusPortHaveData)



    GET_PORT_DATA(hmiDataContainer, m_framework->asCustomFramework()->m_hmiDataReceiver, hmiDataPortHaveData)

    GET_PORT_DATA(displayedViewContainer, m_framework->asCustomFramework()->m_displayedView_ReceiverPort, displayedViewPortHaveData)



    GET_PORT_DATA(viewModeStsContainer, m_framework->asCustomFramework()->m_viewModeStatus_ReceiverPort, viewModeStsPortHaveData)




    bool touchStatusChanged = false;
    if (touchStatusPortHaveData)
    {
        touchStatusChanged = (touchStatus() != static_cast<TouchStatus>(touchStatusContainer->m_Data));
        setTouchStatus(static_cast<TouchStatus>(touchStatusContainer->m_Data));
    }

    if (hmiDataPortHaveData)
    {
        setHuX(hmiDataContainer->m_Data.m_huX);
        setHuY(hmiDataContainer->m_Data.m_huY);
    }

    EScreenID    l_displayedView  = EScreenID_NO_CHANGE;
    ESVSViewMode l_curViewModeSts = ESVSViewMode_VM_Default;
    EGear        l_curGear        = EGear_Init;
    if (displayedViewPortHaveData)
    {
        l_displayedView = displayedViewContainer->m_Data;
    }

    if (viewModeStsPortHaveData)
    {
        l_curViewModeSts = viewModeStsContainer->m_Data;
    }

    if (m_framework->m_gearReceiver.isConnected())
    {
        const pc::daddy::GearDaddy* const l_pData = m_framework->m_gearReceiver.getData();
        if (nullptr != l_pData)
        {
            l_curGear = static_cast<vfc::uint8_t>(l_pData->m_Data);
        }
    }

//    const auto touchInsideViewport     = checkTouchInsideViewport(); // PRQA S 3803
    const bool touchInsideResponseArea = checkTouchInsideResponseArea();
//    const bool viewModeChanged         = (m_preViewMode != l_curViewModeSts);
//    const bool gearChanged             = (m_preGear != l_curGear);
    m_preViewMode                = l_curViewModeSts;

    if( m_preGear != EGear_P && l_curGear == EGear_P )
    {
        m_gearChangedToP = true; // Gear Change to P
    }else if (m_preGear == EGear_P &&  l_curGear != EGear_P)
    {
        m_gearChangedToP = false; // Gear Change from P
    }
    else{}

    m_preGear                    = l_curGear;

    const bool allowShowInGearD =
        (getScreenId() == EScreenID::EScreenID_PERSPECTIVE_RL || getScreenId() == EScreenID::EScreenID_PERSPECTIVE_RR ||
         getScreenId() == EScreenID::EScreenID_PERSPECTIVE_PRE);

    const bool allowShowInGearR =
        (getScreenId() == EScreenID::EScreenID_PERSPECTIVE_FL || getScreenId() == EScreenID::EScreenID_PERSPECTIVE_FR ||
         getScreenId() == EScreenID::EScreenID_PERSPECTIVE_PFR);

    ButtonState currentState = getState();

    if (l_curViewModeSts != ESVSViewMode::ESVSViewMode_VM_Perspective)
    {
        currentState = INVALID;
    }
    else if ((l_curGear == EGear_D && !allowShowInGearD) || (l_curGear == EGear_R && !allowShowInGearR))
    {
        currentState = UNAVAILABLE;
    }
    else
    {
        switch (currentState)
        {
        case AVAILABLE:
        {
            if (touchInsideResponseArea && (touchStatus() == TOUCH_DOWN || touchStatus() == TOUCH_MOVE))
            {
                currentState = PRESSED;
            }
            else
            {
                if (l_displayedView == getScreenId())
                {
                    currentState = SELECTED;
                }
            }
            break;
        }
        case PRESSED:
        {
            if (touchStatusChanged && touchStatus() == TOUCH_UP)
            {
                currentState = RELEASED;
            }
            break;
        }
        case RELEASED:
        {
            currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
        }
        break;
        case SELECTED:
        {
            if (!(l_displayedView == getScreenId()))
            {
                currentState = AVAILABLE;
            }
            break;
        }
        case INVALID:
        case UNAVAILABLE:
        default:
        {
            if ((l_curGear == EGear_D && !allowShowInGearD) || (l_curGear == EGear_R && !allowShowInGearR))
            {
                currentState = UNAVAILABLE;
            }
            else  if ( m_gearChangedToP )
            {
                m_delayCounter++;
                if ( m_delayCounter > m_settings->m_displayDelay)
                {
                    currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
                    m_gearChangedToP = false;
                    m_delayCounter = 0;
                }else
                {
                    // Do nothing
                }
            }
            else
            {
                currentState = (l_displayedView == getScreenId()) ? SELECTED : AVAILABLE;
            }

            break;
        }
        }
    }

    setState(currentState);

    switch (getState())
    {
    case INVALID:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    case UNAVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "UNAVAILABLE");
        break;
    }
    case AVAILABLE:
    {
        IMGUI_LOG("Buttons", getName() + "State", "AVAILABLE");
        break;
    }
    case PRESSED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "PRESSED");
        break;
    }
    case RELEASED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "RELEASED");
        break;
    }
    case SELECTED:
    {
        IMGUI_LOG("Buttons", getName() + "State", "SELECTED");
        break;
    }
    default:
    {
        IMGUI_LOG("Buttons", getName() + "State", "INVALID");
        break;
    }
    }
}

ViewChangeButtonGroup::ViewChangeButtonGroup(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    osg::Camera*         f_referenceView)
    : ButtonGroup{f_assetId}
{
    setName("ViewChangeButtonGroup");

    // Single View  Buttons
    addButton(new SingleViewChangeButton(
        cc::core::AssetId::EASSETS_SINGLE_FRONT_VIEW_BUTTON,
        f_framework,
        const_cast<ViewChangeButtonSettings*>(g_FrontButtonSettings.get()), // PRQA S 3066
        EScreenID::EScreenID_SINGLE_FRONT_NORMAL,
        f_referenceView));
    addButton(new SingleViewChangeButton(
        cc::core::AssetId::EASSETS_SINGLE_REAR_VIEW_BUTTON,
        f_framework,
        const_cast<ViewChangeButtonSettings*>(g_RearButtonSettings.get()), // PRQA S 3066
        EScreenID::EScreenID_SINGLE_REAR_NORMAL_ON_ROAD,
        f_referenceView));

    // 3D Buttons
    addButton(new PerspectiveViewChangeButtonBackground(
        cc::core::AssetId::EASSETS_PERSPECTIVE_BUTTON_BACKGROUND, f_framework, f_referenceView));
    addButtonGroup(new EllipticalSlidingBar(cc::core::AssetId::EASSETS_ELLIPTICAL_SLIDING_BAR, f_framework,f_referenceView));

    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    else
    {
        XLOG_ERROR(g_AppContext, "ViewChangeButtonGroup setReferenceView Failed");
    }
}

void ViewChangeButtonGroup::update()
{
    m_enabled = true;
}

} // namespace viewchangebutton
} // namespace button
} // namespace assets
} // namespace cc
