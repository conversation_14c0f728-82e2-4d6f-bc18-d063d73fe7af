//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/ptsoverlay/inc/PtsOverlay.h"
#include "cc/assets/ptsoverlay/inc/PtsAnimation.h"
#include "cc/assets/ptsoverlay/inc/PtsSettings.h"
#include "cc/assets/ptsoverlay/inc/PtsSpline.h"
#include "cc/assets/ptsoverlay/inc/PtsTask.h"
#include "cc/assets/ptsoverlay/inc/PtsUtils.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h" // PRQA S 1060
#include "cc/util/logging/inc/LoggingContexts.h"
#include "vfc/core/vfc_types.hpp"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/Polygon2D.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/worker/core/inc/TaskManager.h"

#include <osg/Depth>
#include <osg/LineWidth>
#include <osg/MatrixTransform>
#include <osg/Math>
#include <osgAnimation/EaseMotion>
#include <osgUtil/CullVisitor>

#include <cassert>



/// @deviation NRCS2_071
/// @deviation NRCS2_076
/// Rule QACPP-4.3.0-3803
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace ptsoverlay
{
using cc::util::logging::g_PtsContext;

pc::util::coding::Item<PtsSettings> g_ptsSettings("PtsOverlay");


const char* getPtsHmiStateName(vfc::uint32_t f_state)
{
//   using namespace pashmi;
  switch (f_state)
  {
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_SYSTEM_OFF):
      {return "SYSTEM_OFF";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_SWITCHED_OFF):
      {return "SWITCHED_OFF";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_ERROR):
      {return "HMI_ERROR";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_ERROR_FEEDBACK):
      {return "ERROR_FEEDBACK";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT):
      {return "DISTURBANCE_FRONT";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_REAR):
      {return "DISTURBANCE_REAR";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT_REAR):
      {return "DISTURBANCE_FRONT_REAR";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_GEAR_P):
      {return "GEAR_P";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_NO_WARNING):
      {return "NO_WARNING";}
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_WARNING_ACTIVE):
      {return "WARNING_ACTIVE";}
    default:
      {return "#INVALID#";}
  }
}


//! evaluates the visibility of the PTS overlay based on the given PTS HMI state data
bool isHidden(const cc::daddy::PtsHmiStateOutput& f_ptsHmiState)
{
  if (f_ptsHmiState.m_errorReaction || f_ptsHmiState.m_disturbanceReactionFront || f_ptsHmiState.m_disturbanceReactionRear)
  {
    //! show as long as any of the error or disturbance reaction bits are set
    return false;
  }
  switch (f_ptsHmiState.m_pashmiState) // PRQA S 4018
  {
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_NO_WARNING):
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_WARNING_ACTIVE):
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_FRONT):
    case static_cast<unsigned>(pashmi::EPtsHmiState::PTS_HMI_DISTURBANCE_REAR):
      {return false;}
    default:
      {return true;}
  }
}

namespace
{

typedef pc::util::math::LinearInterpolator<vfc::float32_t> HeightInterpolator;

HeightInterpolator g_heightInterpolator;

void updateInterpolator()
{
  static vfc::uint32_t s_sequenceNumber = ~0u;
  if (g_ptsSettings->getModifiedCount() != s_sequenceNumber)
  {
    s_sequenceNumber = g_ptsSettings->getModifiedCount();
    g_heightInterpolator.clear();
    g_heightInterpolator.addSample(g_ptsSettings->m_projectionTransitionCamAngle.x(), 1.0f);
    g_heightInterpolator.addSample(g_ptsSettings->m_projectionTransitionCamAngle.y(), 0.0f);
    g_heightInterpolator.init();
  }
}

/// @brief pretty-print PTS HMI state
std::ostream& operator << (std::ostream& f_ostream, const cc::daddy::PtsHmiStateOutput& f_data)
{
  f_ostream << "PTS state:" << getPtsHmiStateName(f_data.m_pashmiState);  // PRQA S 3803
  if (f_data.m_errorReaction)
  {
    f_ostream << ", error reaction";  // PRQA S 3803
  }
  if (f_data.m_disturbanceReactionFront)
  {
    f_ostream << ", disturbance reaction front";  // PRQA S 3803
  }
  if (f_data.m_disturbanceReactionRear)
  {
    f_ostream << ", disturbance reaction rear";  // PRQA S 3803
  }
  return f_ostream;
}

} // namespace


//!
//! PtsOverlayComposite
//!
PtsOverlayComposite::PtsOverlayComposite(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_updateRequired{true}
  , m_renderBinOrder{core::RENDERBIN_ORDER_OVERLAYS}
{
  m_componentIds.fill(~0u);

  setNumChildrenRequiringUpdateTraversal(1);
  //! shadow
  osg::MatrixTransform* const l_transformShadow = new osg::MatrixTransform;
  m_componentIds[SHADOW] = getNumChildren();
  l_transformShadow->addChild(new PtsSplineShadow);  // PRQA S 3803
  osg::MatrixTransform::addChild(l_transformShadow);  // PRQA S 3803
  //! spline 2D
  osg::MatrixTransform* const l_transform2D = new osg::MatrixTransform;
  m_componentIds[SPLINE_2D] = getNumChildren();
  l_transform2D->addChild(new PtsSpline2D(0.01f));  // PRQA S 3803
  //addChild(new PtsSpline2D);
  osg::MatrixTransform::addChild(l_transform2D);  // PRQA S 3803

  //! shield 3D
  osg::MatrixTransform* const l_transform3D = new osg::MatrixTransform;
  {
    //! spline 3D bottom [0]
    PtsSpline3D* const l_spline3D = new PtsSpline3D;
    l_transform3D->addChild(l_spline3D);  // PRQA S 3803
    //! shield with hairlines [1]
    l_transform3D->addChild(new PtsShield3D);  // PRQA S 3803
    //! contour bottom [2]
    PtsContour3D* const l_contour3D = new PtsContour3D;
    osg::MatrixTransform* const l_transformContourBottom = new osg::MatrixTransform;
    l_transformContourBottom->addChild(l_contour3D);  // PRQA S 3803
    l_transform3D->addChild(l_transformContourBottom);  // PRQA S 3803
    //! contour top [3]
    osg::MatrixTransform* const l_transformContourTop = new osg::MatrixTransform;
    l_transformContourTop->addChild(l_contour3D);  // PRQA S 3803
    l_transform3D->addChild(l_transformContourTop);  // PRQA S 3803
    //! spline 3D top [4]
    osg::MatrixTransform* const l_transformSpline3DTop = new osg::MatrixTransform;
    l_transformSpline3DTop->addChild(l_spline3D);  // PRQA S 3803
    l_transform3D->addChild(l_transformSpline3DTop);  // PRQA S 3803
  }
  m_componentIds[SHIELD_3D] = getNumChildren();
  osg::MatrixTransform::addChild(l_transform3D);  // PRQA S 3803

  update();

  osg::StateSet* const l_commonStateSet = getOrCreateStateSet();
  l_commonStateSet->setMode(GL_BLEND, osg::StateAttribute::ON);
  l_commonStateSet->setRenderingHint(osg::StateSet::TRANSPARENT_BIN);
  l_commonStateSet->setRenderBinDetails(pc::core::g_renderOrder->m_overlays, "RenderBin");

  //! disable depth buffer write
  osg::Depth* const l_depth = new osg::Depth;
  l_depth->setWriteMask(false);
  l_commonStateSet->setAttributeAndModes(l_depth, osg::StateAttribute::ON);
}


PtsOverlayComposite::PtsOverlayComposite(const PtsOverlayComposite& f_other, const osg::CopyOp& f_copyOp)
  : osg::MatrixTransform{f_other, f_copyOp}
  , m_framework{f_other.m_framework}
  , m_componentIds{f_other.m_componentIds}
  , m_updateRequired{f_other.m_updateRequired}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


osg::Node* PtsOverlayComposite::getComponent(Component f_id)
{
  return getChild(m_componentIds[f_id]); // PRQA S 3000
}


void PtsOverlayComposite::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (m_updateRequired)
    {
      update();
    }
    osg::MatrixTransform::traverse(f_nv);
  }
  else if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    assert(nullptr != m_framework);
    //! check if PTS overlay shall be displayed
    // cc::core::CustomFramework* l_framework = m_framework->asCustomFramework();
    // const cc::daddy::PtsHmiStateOutputDaddy* l_ptsHmiState = l_framework->m_ptsHmiStateReceiver.getData();
    // if (l_ptsHmiState)
    // {
    //   if (isHidden(l_ptsHmiState->m_Data))
    //   {
    //     XLOG_INFO_OS(g_PtsContext) << l_ptsHmiState->m_Data << XLOG_ENDL;
    //     return; //! do not render
    //   }
    //   XLOG_DEBUG_OS(g_PtsContext) << l_ptsHmiState->m_Data << XLOG_ENDL;
    // }

    cc::core::CustomFramework* const l_framework = m_framework->asCustomFramework();
    if (nullptr != l_framework->m_VehTransparenceStsInternalReceiver.getData())
    {
      if (l_framework->m_VehTransparenceStsInternalReceiver.getData()->m_Data)
      {
        if (m_renderBinOrder != ( static_cast<vfc::uint32_t>(core::RENDERBIN_ORDER_CAR_OPAQUE)-1))
        {
          getOrCreateStateSet()->setRenderBinDetails(static_cast<vfc::uint32_t>(core::RENDERBIN_ORDER_CAR_OPAQUE)-1, "RenderBin");
          m_renderBinOrder = static_cast<vfc::uint32_t>(core::RENDERBIN_ORDER_SPLINEOVERLAY)-1;
        }

      }
      else
      {
        if (m_renderBinOrder != core::RENDERBIN_ORDER_OVERLAYS)
        {
          getOrCreateStateSet()->setRenderBinDetails(core::RENDERBIN_ORDER_OVERLAYS, "RenderBin");
          m_renderBinOrder = core::RENDERBIN_ORDER_OVERLAYS;
        }
      }
    }
    assets::ptsoverlay::PtsOverlay* const l_ptsOverlay = dynamic_cast<assets::ptsoverlay::PtsOverlay*>(getParent(0));  // PRQA S 3400

    if (l_ptsOverlay->isTransitionAnimationActive())
    {
      //reset the matrix and accecpt the tranformMatrix
      static_cast<osg::MatrixTransform*>(getComponent(PtsOverlayComposite::SHADOW))->setMatrix(osg::Matrix::identity()); // PRQA S 3076
      getComponent(PtsOverlayComposite::SHADOW)->accept(f_nv);

      if(l_ptsOverlay->getIs3D())
      {
        static_cast<osg::MatrixTransform*>(getComponent(PtsOverlayComposite::SHIELD_3D))->setMatrix(osg::Matrix::identity()); // PRQA S 3076
        getComponent(PtsOverlayComposite::SHIELD_3D)->accept(f_nv);
      }
      else
      {
        static_cast<osg::MatrixTransform*>(getComponent(PtsOverlayComposite::SPLINE_2D))->setMatrix(osg::Matrix::identity()); // PRQA S 3076
        getComponent(PtsOverlayComposite::SPLINE_2D)->accept(f_nv);
      }
    }
    else
    {
      // reset the MatrixTransform of the Overlay if there is no animation
      static_cast<osg::MatrixTransform*>(l_ptsOverlay)->setMatrix(osg::Matrix::identity());
      //reset the matrix for the 2D spline and its shadow
      static_cast<osg::MatrixTransform*>(getComponent(PtsOverlayComposite::SHADOW))->setMatrix(osg::Matrix::identity()); // PRQA S 3076
      static_cast<osg::MatrixTransform*>(getComponent(PtsOverlayComposite::SPLINE_2D))->setMatrix(osg::Matrix::identity()); // PRQA S 3076

      updateInterpolator();
      osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (&f_nv); // PRQA S 3076
      const osg::Vec3f l_center(pc::vehicle::g_mechanicalData->getCenter(), 0.0f);
      const osg::Vec3f l_eye = l_cv->getEyePoint();
      osg::Vec3f l_ev = l_eye - l_center;
      l_ev.normalize();  // PRQA S 3803
      const vfc::float32_t l_camAngle = osg::RadiansToDegrees(static_cast<vfc::float32_t> (osg::PI_2) - std::acos(l_ev * osg::Z_AXIS));
      const vfc::float32_t l_height = g_heightInterpolator.getValue(l_camAngle);

      getComponent(SHADOW)->accept(f_nv); //! shadow is rendered in any case
      if (l_height > g_ptsSettings->m_heightMinThreshold)
      {
        osg::MatrixTransform* const l_upperTransform = static_cast<osg::MatrixTransform*> (getComponent(SHIELD_3D)); // PRQA S 3076
        l_upperTransform->setMatrix(osg::Matrix::scale(1.0f, 1.0f, l_height));
        l_upperTransform->accept(f_nv);
      }
      else
      {
        getComponent(SPLINE_2D)->accept(f_nv);
      }
    }
  }
  else
  {
    osg::MatrixTransform::traverse(f_nv);
  }
}


void PtsOverlayComposite::update()
{
  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  osg::StateAttribute* const l_lineWidth = l_stateSet->getAttribute(osg::StateAttribute::LINEWIDTH);
  if (l_lineWidth != nullptr)
  {
    static_cast<osg::LineWidth*> (l_lineWidth)->setWidth(g_ptsSettings->m_hairlineWidth); // PRQA S 3076
  }
  else
  {
    l_stateSet->setAttribute(new osg::LineWidth(g_ptsSettings->m_hairlineWidth));
  }

  setMatrix(osg::Matrix::translate(0.0f, 0.0f, g_ptsSettings->m_heightOverGround));

  osg::MatrixTransform* const l_transform3D = dynamic_cast<osg::MatrixTransform*> (getComponent(SHIELD_3D));  // PRQA S 3400
  if (l_transform3D != nullptr)
  {
    //! spline 3D bottom [0]
    PtsSpline3D* const l_spline3D = dynamic_cast<PtsSpline3D*> (l_transform3D->getChild(0));  // PRQA S 3400
    if (l_spline3D != nullptr)
    {
      l_spline3D->setHeight(g_ptsSettings->m_spline3DHeight);
    }
    //! shield with hairlines [1]
    //! contour bottom [2]
    osg::MatrixTransform* const l_transformContourBottom = dynamic_cast<osg::MatrixTransform*> (l_transform3D->getChild(2));  // PRQA S 3400
    if (l_transformContourBottom != nullptr)
    {
      l_transformContourBottom->setMatrix(osg::Matrix::translate(0.0f, 0.0f, g_ptsSettings->m_spline3DHeight));
      PtsContour3D* const l_contour3D = dynamic_cast<PtsContour3D*> (l_transformContourBottom->getChild(0));  // PRQA S 3400
      if (l_contour3D != nullptr)
      {
        l_contour3D->setHeight(g_ptsSettings->m_contourHeight);
      }
    }

    //! contour top [3]
    osg::MatrixTransform* const l_transformContourTop = dynamic_cast<osg::MatrixTransform*> (l_transform3D->getChild(3));  // PRQA S 3400
    if (l_transformContourTop != nullptr)
    {
      const vfc::float32_t l_contourTopPosition = (g_ptsSettings->m_spline3DHeight + g_ptsSettings->m_shieldHeight) - g_ptsSettings->m_contourHeight;
      l_transformContourTop->setMatrix(osg::Matrix::translate(0.0f, 0.0f, l_contourTopPosition));
    }
    //! spline 3D top [4]
    osg::MatrixTransform* const l_transformSpline3DTop = dynamic_cast<osg::MatrixTransform*> (l_transform3D->getChild(4));  // PRQA S 3400
    if (l_transformSpline3DTop != nullptr)
    {
      l_transformSpline3DTop->setMatrix(osg::Matrix::translate(0.0f, 0.0f, g_ptsSettings->m_spline3DHeight + g_ptsSettings->m_shieldHeight));
      osg::StateSet* const l_stateSet = l_transformSpline3DTop->getOrCreateStateSet();
      l_stateSet->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(g_ptsSettings->m_spline3DAlphaTop);  // PRQA S 3803
    }
  }
  m_updateRequired = false;
}

//!
//! PtsOverlay
//!
PtsOverlay::PtsOverlay(pc::core::Framework* f_framework, pc::vehicle::AbstractZoneLayout* f_zoneLayout)
  : m_transitionAnimation{false}
  , m_is3D{true}
{
  setName("PtsOverlay");
  constexpr osg::CopyOp::CopyFlags l_excludeMask =
    static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_STATESETS) |
    static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_STATEATTRIBUTES) |
    static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_TEXTURES) |
    static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_IMAGES) |
    static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_UNIFORMS);
  setPrototype(new PtsOverlayComposite(f_framework), static_cast<vfc::uint32_t>(osg::CopyOp::DEEP_COPY_ALL) & ~l_excludeMask); // PRQA S 3000

  ProcessingTask* const l_processingTask = new ProcessingTask(this, f_zoneLayout);
  pc::worker::core::enqueueEvent(new pc::worker::core::AddTaskEvent(l_processingTask));
}


//!
//! PtsMatrixTransform
//!
PtsMatrixTransform::PtsMatrixTransform(pc::core::Framework* f_framework)
: m_framework{f_framework}
{
  setName("PtsMatrixTransform");
}


void PtsMatrixTransform::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // // no scaling/shifiting of PTS in case of Star2 diplay variants
    // if (!cc::core::g_vehicleData->isStar2Display())
    // {
    update();
    // }
  }
  osg::MatrixTransform::traverse(f_nv);
}


void PtsMatrixTransform::update()
{
  PtsOverlay* const l_ptsOverlay = dynamic_cast<PtsOverlay*> (getChild(0u));  // PRQA S 3400
  if (l_ptsOverlay == nullptr)
  {
    return;
  }
  if(l_ptsOverlay->isTransitionAnimationActive())
  {
    return; // prevent overwriting changes from transition animation
  }

  osg::Vec4f l_ptsPosition;
  osg::Vec2f l_scale;
  osg::Vec2f l_translate;

  l_ptsPosition = g_ptsPositions->m_parkIn;
  l_scale = osg::Vec2f(l_ptsPosition.x(), l_ptsPosition.y());
  l_translate = osg::Vec2f(l_ptsPosition.z(), l_ptsPosition.w());
  setMatrix(osg::Matrix::scale(l_scale.x(), l_scale.y(), 1.f) * osg::Matrix::translate(l_translate.x(), l_translate.y(), 0.0f));

//   osg::Vec4f l_ptsPosition;
//   osg::Vec2f l_scale;
//   osg::Vec2f l_translate;
//   switch (m_framework->getCurrentScreenId())
//   {
//     case EParkRq_ON_ParkOption:
//       l_ptsPosition = g_ptsPositions->m_parkIn;
//       l_scale = osg::Vec2f(l_ptsPosition.x(), l_ptsPosition.y());
//       l_translate = osg::Vec2f(l_ptsPosition.z(), l_ptsPosition.w());
//       setMatrix(osg::Matrix::scale(l_scale.x(), l_scale.y(), 1.f) * osg::Matrix::translate(l_translate.x(), l_translate.y(), 0.0f));
//       break;
//     case EParkRq_ON_ParkOption_RV:
//       l_ptsPosition = g_ptsPositions->m_parkIn;
//       l_scale = osg::Vec2f(l_ptsPosition.x(), l_ptsPosition.y());
//       l_translate = osg::Vec2f(l_ptsPosition.z(), l_ptsPosition.w());
//       setMatrix(osg::Matrix::scale(l_scale.x(), l_scale.y(), 1.f) * osg::Matrix::translate(l_translate.x(), l_translate.y(), 0.0f));
//       break;
//     // case EParkRq_ON_ParkOption_POC:
//     //   // for ParkOut the ptsPosition can be adapted for each state
//     //   l_ptsPosition = determinePtsPosition();
//     //   l_scale = osg::Vec2f(l_ptsPosition.x(), l_ptsPosition.y());
//     //   l_translate = osg::Vec2f(l_ptsPosition.z(), l_ptsPosition.w());
//     //   setMatrix(osg::Matrix::scale(l_scale.x(), l_scale.y(), 1.f) * osg::Matrix::translate(l_translate.x(), l_translate.y(), 0.0f));
//     //   break;
//     // case EParkRq_ON_ParkOption_RV_POC:
//     //   // for ParkOut the ptsPosition can be adapted for each state
//     //   l_ptsPosition = determinePtsPosition();
//     //   l_scale = osg::Vec2f(l_ptsPosition.x(), l_ptsPosition.y());
//     //   l_translate = osg::Vec2f(l_ptsPosition.z(), l_ptsPosition.w());
//     //   setMatrix(osg::Matrix::scale(l_scale.x(), l_scale.y(), 1.f) * osg::Matrix::translate(l_translate.x(), l_translate.y(), 0.0f));
//     //   break;
//     default:
//       break;
//   }
}



osg::Vec4f PtsMatrixTransform::determinePtsPosition() const
{
  // pc::core::View* l_view = m_framework->getScene()->getView(cc::core::CustomViews::ECUSTOMVIEW_SCHEMATIC_PARKVIEW_OUT);
  // cc::views::parkview::ParkOutView* l_parkOutView = dynamic_cast<cc::views::parkview::ParkOutView*>(l_view);
  // const cc::views::parkview::ParkOutView::ParkoutStates_enm l_parkoutState = l_parkOutView->determineParkoutState();
  // switch (l_parkoutState)
  // {
  // case cc::views::parkview::ParkOutView::PARK_OUT_PARA_R:
  //   return g_ptsPositions->m_parkOutParaRight;
  //   break;
  // case cc::views::parkview::ParkOutView::PARK_OUT_PARA_L:
  //   return g_ptsPositions->m_parkOutParaLeft;
  //   break;
  // case cc::views::parkview::ParkOutView::PARK_OUT_CROSS_F:
  //   return g_ptsPositions->m_parkOutCrossFront;
  //   break;
  // case cc::views::parkview::ParkOutView::PARK_OUT_CROSS_R:
  //   return g_ptsPositions->m_parkOutCrossRear;
  //   break;
  // default:
  //   return g_ptsPositions->m_parkOutParaLeft;
  //   break;
  // }
  return g_ptsPositions->m_parkOutParaLeft;
}

//!
//! Pts2DOnlyCullCallback
//!
PtsParkViewCullCallback::PtsParkViewCullCallback(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_affectedCameras{}
{
}


void PtsParkViewCullCallback::addAffectedCamera(const osg::Camera* f_camera)
{
  static_cast<void> (m_affectedCameras.insert(f_camera));
}


bool PtsParkViewCullCallback::isPtsVisible() const
{
  //! check if PTS overlay shall be displayed
  // cc::core::CustomFramework* l_framework = m_framework->asCustomFramework();
  // const cc::daddy::PtsHmiStateOutputDaddy* l_ptsHmiStateOutput = l_framework->m_ptsHmiStateReceiver.getData();
  // if (l_ptsHmiStateOutput)
  // {
  //   if (isHidden(l_ptsHmiStateOutput->m_Data))
  //   {
  //     XLOG_INFO_OS(g_PtsContext) << l_ptsHmiStateOutput->m_Data << XLOG_ENDL;
  //     return false;
  //   }
  //   XLOG_DEBUG_OS(g_PtsContext) << l_ptsHmiStateOutput->m_Data << XLOG_ENDL;
  // }
  return true;
}


void PtsParkViewCullCallback::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  if ((f_node == nullptr) || (f_nv == nullptr))
  {
      return;
  }
  assert(osg::NodeVisitor::CULL_VISITOR == f_nv->getVisitorType());
  osgUtil::CullVisitor* const l_cv = static_cast<osgUtil::CullVisitor*> (f_nv); // PRQA S 3076

  const auto l_result = m_affectedCameras.find(l_cv->getCurrentCamera());
  if (l_result != m_affectedCameras.end())
  {
    if (isPtsVisible())
    {
      osg::Group* const l_ptsOverlay = f_node->asGroup();
      if ((l_ptsOverlay != nullptr) && (0u < l_ptsOverlay->getNumChildren()))
      {
        PtsOverlayComposite* const l_ptsComposite = dynamic_cast<PtsOverlayComposite*> (l_ptsOverlay->getChild(0));  // PRQA S 3400
        if (l_ptsComposite != nullptr)
        {
          osg::StateSet* const l_stateSet = l_ptsComposite->getStateSet();
          if (l_stateSet != nullptr)
          {
            l_cv->pushStateSet(l_stateSet);
          }
          cull(l_ptsComposite, l_cv);
          if (l_stateSet != nullptr)
          {
            l_cv->popStateSet();
          }
        }
      }
    }
  }
  else
  {
    traverse(f_node, f_nv);
  }
}


void PtsParkViewCullCallback::cull(PtsOverlayComposite* f_pts, osgUtil::CullVisitor* f_cv)
{
  if ((f_pts == nullptr) || (f_cv == nullptr))
  {
      return;
  }
  static_cast<osg::MatrixTransform*> (f_pts->getComponent(PtsOverlayComposite::SHADOW))->setMatrix(osg::Matrix::identity()); // PRQA S 3076
  static_cast<osg::MatrixTransform*> (f_pts->getComponent(PtsOverlayComposite::SPLINE_2D))->setMatrix(osg::Matrix::identity()); // PRQA S 3076
  f_pts->getComponent(PtsOverlayComposite::SHADOW)->accept(*f_cv);
  f_pts->getComponent(PtsOverlayComposite::SPLINE_2D)->accept(*f_cv);
}

} // namespace ptsoverlay
} // namespace assets
} // namespace cc
