//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LUM7LR Maximilian Luzius (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  FloorSingleCam.cpp
/// @brief
//=============================================================================

#include "cc/assets/common/inc/FloorSingleCam.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"
#include "vfc/core/vfc_types.hpp"
namespace cc
{
namespace assets
{
namespace common
{

//!
//! FrontFloorData
//!
class FrontFloorData : public pc::util::coding::ISerializable
{
public:

  FrontFloorData()
    : m_size{7.f, 12.4f}
    , m_standardResolution{0.2f, 0.2f}
    , m_areaHighResolution{3.0f, 4.5f, -1.0f, 1.0f}
    , m_highResolution{0.04f, 0.1f}
  {
  }

  SERIALIZABLE(FrontFloorData) // PRQA S 3401
  {
    if (f_descriptor == nullptr)
    {
      return;
    }
    ADD_MEMBER(osg::Vec2f, size);
    ADD_MEMBER(osg::Vec2f, standardResolution);
    ADD_MEMBER(osg::Vec4f, areaHighResolution);
    ADD_MEMBER(osg::Vec2f, highResolution);
  }

  osg::Vec2f m_size;               // size of the whole floor
  osg::Vec2f m_standardResolution; // resolution of floor outside of the area with a higher resolution
  osg::Vec4f m_areaHighResolution; // first two values are the range in X-direction - last two are the range in Y-direction
  osg::Vec2f m_highResolution;     // this resolution must be calculated by the (standardResolution/integer), so the meshes can be stitched together
};


pc::util::coding::Item<FrontFloorData> g_frontFloorData("FrontFloor");

//!
//! RearFloorData
//!
class RearFloorData : public pc::util::coding::ISerializable
{
public:

  RearFloorData()
    : m_size{6.5f, 12.4f}
    , m_standardResolution{0.2f, 0.2f}
    , m_areaHighResolution{-0.5f, -2.5f, -1.0f, 1.0f}
    , m_highResolution{0.2f, 0.2f}
  {
  }

  SERIALIZABLE(RearFloorData) // PRQA S 3401
  {
    if (f_descriptor == nullptr)
    {
      return;
    }
    ADD_MEMBER(osg::Vec2f, size);
    ADD_MEMBER(osg::Vec2f, standardResolution);
    ADD_MEMBER(osg::Vec4f, areaHighResolution);
    ADD_MEMBER(osg::Vec2f, highResolution);
  }

  osg::Vec2f m_size;               // size of the whole floor
  osg::Vec2f m_standardResolution; // resolution of floor outside of the area with a higher resolution
  osg::Vec4f m_areaHighResolution; // first two values are the range in X-direction - last two are the range in Y-direction
  osg::Vec2f m_highResolution;     // this resolution must be calculated by the (standardResolution/integer), so the meshes can be stitched together
};


pc::util::coding::Item<RearFloorData> g_rearFloorData("RearFloor");


FloorSingleCam::FloorSingleCam(const osg::Vec2f& f_size, const osg::Vec2f& f_resolution, const osg::Vec2f& f_offset, FloorType f_floorType)
  : pc::factory::Floor{f_size, f_resolution, f_offset}
  , m_size{}
  , m_standardResolution{}
  , m_areaHighResolution{}
  , m_highResolution{}
{

  switch (f_floorType) // PRQA S 4018
  {
  case FLOOR_REAR:
  {
    m_size = g_rearFloorData->m_size;
    m_standardResolution = g_rearFloorData->m_standardResolution;
    m_areaHighResolution = g_rearFloorData->m_areaHighResolution;
    m_highResolution = g_rearFloorData->m_highResolution;
    m_dir = -1.f;
    break;
  }
  default: // FLOOR_FRONT
  {
    m_size = g_frontFloorData->m_size;
    m_standardResolution = g_frontFloorData->m_standardResolution;
    m_areaHighResolution = g_frontFloorData->m_areaHighResolution;
    m_highResolution = g_frontFloorData->m_highResolution;
    m_dir = 1.f;
    break;
  }
  }

}

FloorSingleCam::FloorSingleCam(const FloorSingleCam& f_floor, const osg::CopyOp& f_copyOp)
  : pc::factory::Floor{f_floor, f_copyOp}
  , m_size{f_floor.m_size}
  , m_standardResolution{f_floor.m_standardResolution}
  , m_areaHighResolution{f_floor.m_areaHighResolution}
  , m_highResolution{f_floor.m_highResolution}
  , m_dir{f_floor.m_dir}

{
}

FloorSingleCam::~FloorSingleCam() = default;

void FloorSingleCam::fillVertexArray(vfc::uint32_t& f_vertexCounter, vfc::uint32_t f_numVerticesX, vfc::uint32_t f_numVerticesY, vfc::float32_t f_offsetX, vfc::float32_t f_offsetY, vfc::float32_t f_stepX, vfc::float32_t f_stepY)
{
  osg::Vec3Array* const l_vertexArray = getVertexArray();
  for(vfc::uint32_t iy = 0u; iy < f_numVerticesY; ++iy)
  {
    for(vfc::uint32_t ix = 0u; ix < f_numVerticesX; ++ix)
    {
      const vfc::float32_t l_x = f_offsetX + static_cast<vfc::float32_t>(ix) * f_stepX;
      const vfc::float32_t l_y = f_offsetY + static_cast<vfc::float32_t>(iy) * f_stepY;

      (*l_vertexArray)[f_vertexCounter] = osg::Vec3f(l_x, l_y, 0.0f);
      ++f_vertexCounter;
    }
  }
}

void FloorSingleCam::fillIndexArray(osg::DrawElementsUShort* f_indexArray, vfc::uint32_t f_numVerticesX, vfc::uint32_t f_numVerticesY, vfc::float32_t f_offsetX, vfc::float32_t f_factorY)
{
  for(vfc::uint32_t iy = 0u; iy < f_numVerticesY; ++iy)
  {
    for(vfc::uint32_t ix = 0u; ix < f_numVerticesX; ++ix)
    {
      vfc::float32_t l_temp = 0.0f;
      // 1st triangle
      l_temp = static_cast<vfc::float32_t>(ix) + f_offsetX + static_cast<vfc::float32_t>(iy) * f_factorY;
      f_indexArray->push_back(static_cast<UShort>(l_temp)); // PRQA S 3016

      l_temp = static_cast<vfc::float32_t>(ix) + f_offsetX + (static_cast<vfc::float32_t>(iy) * f_factorY) + 1.0f;
      f_indexArray->push_back(static_cast<UShort>(l_temp)); // PRQA S 3016

      l_temp = static_cast<vfc::float32_t>(ix) + f_offsetX + (static_cast<vfc::float32_t>(iy) * f_factorY) + f_factorY;
      f_indexArray->push_back(static_cast<UShort>(l_temp)); // PRQA S 3016

      // 2nd triangle
      l_temp = static_cast<vfc::float32_t>(ix) + f_offsetX + (static_cast<vfc::float32_t>(iy) * f_factorY) + 1.0f;
      f_indexArray->push_back(static_cast<UShort>(l_temp)); // PRQA S 3016

      l_temp = static_cast<vfc::float32_t>(ix) + f_offsetX + (static_cast<vfc::float32_t>(iy) * f_factorY) + 1.0f + f_factorY;
      f_indexArray->push_back(static_cast<UShort>(l_temp)); // PRQA S 3016

      l_temp = static_cast<vfc::float32_t>(ix) + f_offsetX + (static_cast<vfc::float32_t>(iy) * f_factorY) + f_factorY;
      f_indexArray->push_back(static_cast<UShort>(l_temp)); // PRQA S 3016
    }
  }
}

void FloorSingleCam::build()  // PRQA S 6041 // PRQA S 2755
{
  // 1# set the quad on the floor which should be rendered with a higher resolution
  // default floor size X: 18.0 Y: 14.0
  osg::Vec2f l_rangeX = osg::Vec2f(m_areaHighResolution.x(), m_areaHighResolution.y());
  osg::Vec2f l_rangeY = osg::Vec2f(m_areaHighResolution.z(), m_areaHighResolution.w());

  osg::Vec2f l_sizeQuad = osg::Vec2f(std::abs(l_rangeX.x()-l_rangeX.y()), std::abs(l_rangeY.x()-l_rangeY.y()));

  // region B has a higher resolution, the regions A,C and D are with the normal resolution
  // also remember that the outer regions A,C,D are using the border vertices of B which are connecting the regions
  //  _____________
  // |    .   .    |
  // |    . C .    |
  // | D  .___.  A |
  // |    | B |    |
  // |____|___|____|

  // #2 calculate the number of sections with a higher resolution in the quad

  // check the resolution and calculate the factor to fit the regions later - the resolution for the quad has to be the same or greater
  if(m_highResolution.x() > m_standardResolution.x())
  {
    m_highResolution.x() = m_standardResolution.x();
  }
  if(m_highResolution.y() > m_standardResolution.y())
  {
    m_highResolution.y() = m_standardResolution.y();
  }

  const vfc::uint32_t l_factorX = pc::util::round2uInt(m_standardResolution.x()/m_highResolution.x());
  const vfc::uint32_t l_factorY = pc::util::round2uInt(m_standardResolution.y()/m_highResolution.y());

  m_highResolution.x() = m_standardResolution.x() / static_cast<vfc::float32_t>(l_factorX);
  m_highResolution.y() = m_standardResolution.y() / static_cast<vfc::float32_t>(l_factorY);

  // A numVertices
  const vfc::uint32_t l_numVerticesAinX = pc::util::round2uInt(m_size.x() / m_standardResolution.x() + 1.f);
  const vfc::uint32_t l_numVerticesAinY = pc::util::round2uInt(((m_size.y() - l_sizeQuad.y())/2.f) / m_standardResolution.y());

  // B numVertices
  const vfc::uint32_t l_numVerticesBinX = pc::util::round2uInt(l_sizeQuad.x() / m_highResolution.x() + 1.f);
  const vfc::uint32_t l_numVerticesBinY = pc::util::round2uInt(l_sizeQuad.y() / m_highResolution.y() + 1.f);

  // C numVertices
  const vfc::uint32_t l_numVerticesCinX = pc::util::round2uInt((m_size.x() - l_sizeQuad.x()) / m_standardResolution.x());
  const vfc::uint32_t l_numVerticesCinY = pc::util::round2uInt(l_sizeQuad.y() / m_standardResolution.y() + 1.f);

  // D numVertices - same as A
  const vfc::uint32_t l_numVerticesDinX = pc::util::round2uInt(m_size.x() / m_standardResolution.x() + 1.f);
  const vfc::uint32_t l_numVerticesDinY = pc::util::round2uInt(((m_size.y() - l_sizeQuad.y())/2.f) / m_standardResolution.y());

  // #3 resize the vertexArray
  const vfc::uint32_t l_numVerticesA = l_numVerticesAinX * l_numVerticesAinY;
  const vfc::uint32_t l_numVerticesB = l_numVerticesBinX * l_numVerticesBinY;
  const vfc::uint32_t l_numVerticesC = l_numVerticesCinX * l_numVerticesCinY;
  const vfc::uint32_t l_numVerticesD = l_numVerticesDinX * l_numVerticesDinY;

  const vfc::uint32_t l_numVertices = l_numVerticesA
                                    + l_numVerticesB
                                    + l_numVerticesC
                                    + l_numVerticesD;
  resizeVertexArray(l_numVertices);

  // #4 calculate the step size - for the low and high resolution floor
  const vfc::float32_t l_dOutsideX = m_dir * m_size.x() / (static_cast<vfc::float32_t>(l_numVerticesAinX) - 1.0f);
  const vfc::float32_t l_dOutsideY = m_size.y() / (static_cast<vfc::float32_t>(l_numVerticesAinY) + static_cast<vfc::float32_t>(l_numVerticesCinY) + static_cast<vfc::float32_t>(l_numVerticesDinY) - 1.0f);
  const vfc::float32_t l_dInsideX = m_dir * l_sizeQuad.x() / (static_cast<vfc::float32_t>(l_numVerticesBinX) - 1.0f);
  const vfc::float32_t l_dInsideY = l_sizeQuad.y() / (static_cast<vfc::float32_t>(l_numVerticesBinY) - 1.0f);

  vfc::uint32_t l_vertexCounter = 0u;
  // B - first push the quad B on the array cause it is complete with all its border vertices
  vfc::float32_t l_offsetX = l_rangeX.x();
  vfc::float32_t l_offsetY = -(m_size.y() * 0.5f) + static_cast<vfc::float32_t>(l_numVerticesAinY) * l_dOutsideY;
  fillVertexArray(l_vertexCounter, l_numVerticesBinX, l_numVerticesBinY, l_offsetX, l_offsetY, l_dInsideX, l_dInsideY);

  // C - second push the quad C on the array cause it is complete together with B with all its border vertices
  l_offsetX = l_rangeX.x() + static_cast<vfc::float32_t>(l_numVerticesBinX) * l_dInsideX;
  l_offsetY = -(m_size.y() * 0.5f) + static_cast<vfc::float32_t>(l_numVerticesAinY) * l_dOutsideY;
  fillVertexArray(l_vertexCounter, l_numVerticesCinX, l_numVerticesCinY, l_offsetX, l_offsetY, l_dOutsideX, l_dOutsideY);

  // A - third push the side quads to complete the array
  l_offsetX = l_rangeX.x();
  l_offsetY = -(m_size.y() * 0.5f);
  fillVertexArray(l_vertexCounter, l_numVerticesAinX, l_numVerticesAinY, l_offsetX, l_offsetY, l_dOutsideX, l_dOutsideY);

  // D - third push the side quads to complete the array
  l_offsetX = l_rangeX.x();
  l_offsetY = -(m_size.y() * 0.5f) + static_cast<vfc::float32_t>(l_numVerticesAinY) * l_dOutsideY  + static_cast<vfc::float32_t>(l_numVerticesBinY) * l_dInsideY;
  fillVertexArray(l_vertexCounter, l_numVerticesDinX, l_numVerticesDinY, l_offsetX, l_offsetY, l_dOutsideX, l_dOutsideY);

  osg::Vec3Array* const l_vertexArray = getVertexArray();
  l_vertexArray->dirty();
  dirtyBoundAllDrawables();

  // Indices
  const vfc::uint32_t l_numIndices = (l_numVerticesBinX - 1u) * (l_numVerticesBinY - 1u) * 6u
                                  + (l_numVerticesCinX - 1u) * (l_numVerticesCinY - 1u) * 6u
                                  + (l_numVerticesAinX - 1u) * (l_numVerticesAinY - 1u) * 6u
                                  + (l_numVerticesDinX - 1u) * (l_numVerticesDinY - 1u) * 6u;

  osg::DrawElementsUShort* const l_indexArray = getIndexArray();
  if(true == l_indexArray->empty())
  {
    l_indexArray->reserveElements(l_numIndices);
  }
  else
  {
    l_indexArray->clear();
  }

  // B - push back index order
  l_offsetX = 0.f;
  fillIndexArray(l_indexArray, l_numVerticesBinX-1u, l_numVerticesBinY-1u, l_offsetX, static_cast<vfc::float32_t>(l_numVerticesBinX));

  // B-C - push back the connection between the regions
  for(vfc::uint32_t iy = 0u; iy < (l_numVerticesCinY-1u); ++iy)
  {
    // 1st triangle
    l_indexArray->push_back(static_cast<UShort>(iy * l_factorY * l_numVerticesBinX + l_numVerticesBinX - 1u));
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + (iy * l_numVerticesCinX)));
    l_indexArray->push_back(static_cast<UShort>((iy * l_factorY + l_factorY) * l_numVerticesBinX + l_numVerticesBinX - 1u));
    // 2nd triangle
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + (iy * l_numVerticesCinX)));
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + (iy * l_numVerticesCinX) + l_numVerticesCinX));
    l_indexArray->push_back(static_cast<UShort>((iy * l_factorY + l_factorY) * l_numVerticesBinX + l_numVerticesBinX - 1u));
  }

  // C - push back index order
  l_offsetX = l_offsetX + static_cast<vfc::float32_t>(l_numVerticesB);
  fillIndexArray(l_indexArray, l_numVerticesCinX-1u, l_numVerticesCinY-1u, l_offsetX, static_cast<vfc::float32_t>(l_numVerticesCinX));

  // A - push back index order
  l_offsetX = l_offsetX + static_cast<vfc::float32_t>(l_numVerticesC);
  fillIndexArray(l_indexArray, l_numVerticesAinX-1u, l_numVerticesAinY-1u, l_offsetX, static_cast<vfc::float32_t>(l_numVerticesAinX));

  // B-C-A - push back the connection between the regions
  vfc::uint32_t l_counter = 0u;
  for(vfc::uint32_t ix = 0u; ix < (l_numVerticesAinX-1u); ++ix)
  {
    // 1st triangle
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC + l_numVerticesA - l_numVerticesAinX + ix));
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC + l_numVerticesA - l_numVerticesAinX + ix + 1u));

    // if it is near the region B take every l_factorX vertex, else if region C is next to it take every
    const vfc::uint32_t l_third = ix * l_factorX;
    if(l_third > (l_numVerticesBinX-1u))
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + ix - l_counter));
    }
    else
    {
      l_indexArray->push_back(static_cast<UShort>(l_third));
      l_counter++;
    }

    // 2nd triangle
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC + l_numVerticesA - l_numVerticesAinX + ix + 1u));

    const vfc::uint32_t l_thirdPlus = (ix + 1u) * l_factorX;
    if(l_thirdPlus > (l_numVerticesBinX-1u))
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + ix + 1u - l_counter));
    }
    else
    {
      l_indexArray->push_back(static_cast<UShort>(l_thirdPlus));
    }
    if(l_third > (l_numVerticesBinX-1u))
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + ix - l_counter));
    }
    else
    {
      l_indexArray->push_back(static_cast<UShort>(l_third));
    }
  }

  // D - push back index order
  l_offsetX += static_cast<vfc::float32_t>(l_numVerticesD);
  fillIndexArray(l_indexArray, l_numVerticesDinX-1u, l_numVerticesDinY-1u, l_offsetX, static_cast<vfc::float32_t>(l_numVerticesDinX));

  // B-C-D - push back the connection between the regions
  l_counter = 0u;
  for(vfc::uint32_t ix = 0u; ix < (l_numVerticesDinX-1u); ++ix)
  {
    // 1st triangle
    // if it is near the region B take every l_factorX vertex, else if region C is next to it take every
    const vfc::uint32_t l_third = ix * l_factorX;
    if(l_third > (l_numVerticesBinX-1u))
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC - l_numVerticesCinX + ix - l_counter));
    }
    else
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB - l_numVerticesBinX + l_third));
      l_counter++;
    }
    const vfc::uint32_t l_thirdPlus = (ix + 1u) * l_factorX;
    if(l_thirdPlus > (l_numVerticesBinX-1u))
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC - l_numVerticesCinX + ix + 1u - l_counter));
    }
    else
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB - l_numVerticesBinX + l_thirdPlus));
    }

    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC + l_numVerticesA +  ix + 1u));


    // 2nd triangle
    if(l_third > (l_numVerticesBinX-1u))
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC - l_numVerticesCinX + ix - l_counter));
    }
    else
    {
      l_indexArray->push_back(static_cast<UShort>(l_numVerticesB - l_numVerticesBinX + l_third));
    }
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC + l_numVerticesA + ix + 1u));
    l_indexArray->push_back(static_cast<UShort>(l_numVerticesB + l_numVerticesC + l_numVerticesA + ix));
  }

  l_indexArray->dirty();
}

} // namespace common
} // namespace factory
} // namespace pc

