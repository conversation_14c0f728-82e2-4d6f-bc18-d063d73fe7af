//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: SVS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent CC BYD DENZA&MR
/// @file  ParkingSpot.cpp
/// @brief
//=============================================================================

#include "cc/assets/parkingspots/inc/ParkingSpot.h"

#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "cc/core/inc/CustomScene.h"
#include "vfc/core/vfc_types.hpp"

#include "osg/Vec2ui"
#include "osg/Depth"
#include "osg/Geometry"
#include "osg/Texture2D"
#include "osgDB/ReadFile"



#define COLOR_GREEN 0
#define PLANE 1
#define BILLBOARD 0

namespace cc
{
namespace assets
{
namespace parkingspots
{


//!
//! ParkingSpotSettings
//!
class ParkingSpotSettings : public pc::util::coding::ISerializable
{
public:

    ParkingSpotSettings()
      : m_filenameSpotTextureParallelSelectable_left("cc/vehicle_model/ui/187_parking_space_selectable_parallel_left.png") // PRQA S 4052
      , m_filenameSpotTextureParallelSelectable_right("cc/vehicle_model/ui/186_parking_space_selectable_parallel_right.png")
      , m_filenameSpotTextureCrossSelectable_left("cc/vehicle_model/ui/179_parking_space_selectable_cross_left.png")
      , m_filenameSpotTextureCrossSelectable_right("cc/vehicle_model/ui/178_parking_space_selectable_cross_right.png")
      , m_filenameSpotTextureDiagonalSelectable_left("cc/vehicle_model/ui/185_parking_space_selectable_diagonal_left.png")
      , m_filenameSpotTextureDiagonalSelectable_right("cc/vehicle_model/ui/182_parking_space_selectable_diagonal_right.png")
      , m_filenameSpotTextureParallelSelected_left("cc/vehicle_model/ui/189_parking_space_selected_parallel_left.png")
      , m_filenameSpotTextureParallelSelected_right("cc/vehicle_model/ui/188_parking_space_selected_parallel_right.png")
      , m_filenameSpotTextureCrossSelected_left("cc/vehicle_model/ui/181_parking_space_selected_cross_left.png")
      , m_filenameSpotTextureCrossSelected_right("cc/vehicle_model/ui/180_parking_space_selected_cross_right.png")
      , m_filenameSpotTextureDiagonalSelected_left("cc/vehicle_model/ui/183_parking_space_selected_diagonal_left.png")
      , m_filenameSpotTextureDiagonalSelected_right("cc/vehicle_model/ui/184_parking_space_selected_diagonal_right.png")
      , m_groundLevel{0.0f}
      , m_mipmapBias{-0.4f}
    {
    }

    SERIALIZABLE(ParkingSpotSettings) // PRQA S 3401
    {
        ADD_STRING_MEMBER(filenameSpotTextureParallelSelectable_right);
        ADD_STRING_MEMBER(filenameSpotTextureParallelSelectable_right);
        ADD_STRING_MEMBER(filenameSpotTextureCrossSelectable_left);
        ADD_STRING_MEMBER(filenameSpotTextureCrossSelectable_right);
        ADD_STRING_MEMBER(filenameSpotTextureDiagonalSelectable_left);
        ADD_STRING_MEMBER(filenameSpotTextureDiagonalSelectable_right);
        ADD_STRING_MEMBER(filenameSpotTextureParallelSelected_left);
        ADD_STRING_MEMBER(filenameSpotTextureParallelSelected_right);
        ADD_STRING_MEMBER(filenameSpotTextureCrossSelected_left);
        ADD_STRING_MEMBER(filenameSpotTextureCrossSelected_right);
        ADD_STRING_MEMBER(filenameSpotTextureDiagonalSelected_left);
        ADD_STRING_MEMBER(filenameSpotTextureDiagonalSelected_right);
        ADD_FLOAT_MEMBER(groundLevel);
        ADD_FLOAT_MEMBER(mipmapBias);
    }

    std::string m_filenameSpotTextureParallelSelectable_left;
    std::string m_filenameSpotTextureParallelSelectable_right;
    std::string m_filenameSpotTextureCrossSelectable_left;
    std::string m_filenameSpotTextureCrossSelectable_right;
    std::string m_filenameSpotTextureDiagonalSelectable_left;
    std::string m_filenameSpotTextureDiagonalSelectable_right;
    std::string m_filenameSpotTextureParallelSelected_left;
    std::string m_filenameSpotTextureParallelSelected_right;
    std::string m_filenameSpotTextureCrossSelected_left;
    std::string m_filenameSpotTextureCrossSelected_right;
    std::string m_filenameSpotTextureDiagonalSelected_left;
    std::string m_filenameSpotTextureDiagonalSelected_right;
    vfc::float32_t m_groundLevel;
    vfc::float32_t m_mipmapBias;
};

pc::util::coding::Item<ParkingSpotSettings> g_settings("ParkingSpots");


osg::Vec4f computeAtlasTexCoord(const osg::Vec2ui& f_gridSize, const osg::Vec2ui& f_selection)
{
  const vfc::float32_t l_scaleU = 1.0f / static_cast<vfc::float32_t> (f_gridSize.x());
  const vfc::float32_t l_scaleV = 1.0f / static_cast<vfc::float32_t> (f_gridSize.y());
  return osg::Vec4f(l_scaleU, l_scaleV, static_cast<vfc::float32_t>(f_selection.x()) * l_scaleU, static_cast<vfc::float32_t>(f_selection.y()) * l_scaleV);
}

static osg::observer_ptr<osg::Geode> g_planeGeode;

//!
//! ParkingSpotPlane
//!
ParkingSpotPlane::ParkingSpotPlane()
  : m_visible{false}
  , m_spotType{static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS)}
  , m_textureState{static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_NONE)} // PRQA S 3143  #code looks fine
  , m_side{LEFTSIDE}
{
  if (!g_planeGeode.valid())
  {
    //build the plane for the parking slot itself
    osg::Geometry* const l_geometry = new osg::Geometry; // PRQA S 4262 // PRQA S 4264
    l_geometry->setUseDisplayList(false);
    l_geometry->setUseVertexBufferObjects(true);

    osg::Vec3Array* const l_vertices = new osg::Vec3Array(4u);
    (*l_vertices)[0u] = osg::Vec3f(0.0f, 0.0f, 0.0f);
    (*l_vertices)[1u] = osg::Vec3f(1.0f, 0.0f, 0.0f);
    (*l_vertices)[2u] = osg::Vec3f(1.0f, 1.0f, 0.0f);
    (*l_vertices)[3u] = osg::Vec3f(0.0f, 1.0f, 0.0f);
    l_geometry->setVertexArray(l_vertices);

    osg::Vec2Array* const l_texCoords = new osg::Vec2Array(4u);
    (*l_texCoords)[0u] = osg::Vec2f(1.0f, 0.0f);
    (*l_texCoords)[1u] = osg::Vec2f(1.0f, 1.0f);
    (*l_texCoords)[2u] = osg::Vec2f(0.0f, 1.0f);
    (*l_texCoords)[3u] = osg::Vec2f(0.0f, 0.0f);
    l_geometry->setTexCoordArray(0u, l_texCoords);

    osg::Vec4Array* const l_colours = new osg::Vec4Array(1u);
    (*l_colours)[0u] = osg::Vec4f(1.0f, 1.0f, 1.0f, 1.0f);
    l_geometry->setColorArray(l_colours, osg::Array::BIND_OVERALL);

    osg::Vec3Array* const l_normals = new osg::Vec3Array(1u);
    (*l_normals)[0u] = osg::Z_AXIS;
    l_geometry->setNormalArray(l_normals, osg::Array::BIND_OVERALL);

    osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), 6u);
    (*l_indices)[0u] = 1u;
    (*l_indices)[1u] = 0u;
    (*l_indices)[2u] = 2u;
    (*l_indices)[3u] = 2u;
    (*l_indices)[4u] = 0u;
    (*l_indices)[5u] = 3u;
    l_geometry->addPrimitiveSet(l_indices);    // PRQA S 3803

    g_planeGeode = new osg::Geode;
    g_planeGeode->addDrawable(l_geometry);    // PRQA S 3803
  }

  osg::Group::addChild(g_planeGeode.get());    // PRQA S 3803
}


ParkingSpotPlane::ParkingSpotPlane(const ParkingSpotPlane& f_other, const osg::CopyOp& f_copyOp)
  : osg::MatrixTransform{f_other, f_copyOp}
  , m_visible{f_other.m_visible}
{
}


void ParkingSpotPlane::update(ParkingSpot* f_parkingSpot)
{
  m_visible = false;
  osg::StateSet* const l_stateSet = getOrCreateStateSet();

  osg::Uniform* const l_mipmapBiasUniform = l_stateSet->getOrCreateUniform("u_bias", osg::Uniform::FLOAT);
  l_mipmapBiasUniform->set(g_settings->m_mipmapBias); // PRQA S 3803

  // unsigned int l_girdSizeIdx = f_parkingSpot->getType();
  const osg::Vec2ui l_selection;
  switch (static_cast<cc::target::common::EFAPAParkSlotType>(f_parkingSpot->getType()))
  {
    case cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL:
      // l_selection = osg::Vec2ui(f_parkingSpot->getSelectionState(), 1);
      {
        if (f_parkingSpot->getSide() == LEFTSIDE)
        {
          if (
            (LEFTSIDE == m_side
            && cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL == static_cast<cc::target::common::EFAPAParkSlotType>(m_spotType))
            && (true == f_parkingSpot->getVisibility())
            && (m_textureState == f_parkingSpot->getSpotState())
            )
          {
            m_visible = true;
          }
        }
        else
        {
          if (
            (RIGHTSIDE == m_side
            && cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL == static_cast<cc::target::common::EFAPAParkSlotType>(m_spotType))
            && (true == f_parkingSpot->getVisibility())
            && (m_textureState == f_parkingSpot->getSpotState())
            )
          {
            m_visible = true;
          }
        }
      }
      break;
    case cc::target::common::EFAPAParkSlotType::APASLOT_CROSS:
      {
        if (f_parkingSpot->getSide() == LEFTSIDE)
        {
          if (LEFTSIDE == m_side
              && (cc::target::common::EFAPAParkSlotType::APASLOT_CROSS == static_cast<cc::target::common::EFAPAParkSlotType>(m_spotType))
              && (true == f_parkingSpot->getVisibility())
              && (m_textureState == f_parkingSpot->getSpotState())
            )
          {
            m_visible = true;
          }
        }
        else
        {
          if (RIGHTSIDE == m_side
              && (cc::target::common::EFAPAParkSlotType::APASLOT_CROSS == static_cast<cc::target::common::EFAPAParkSlotType>(m_spotType))
              && (true == f_parkingSpot->getVisibility())
              && (m_textureState == f_parkingSpot->getSpotState())
            )
          {
            m_visible = true;
          }
        }
      }
      break;
    case cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL:
      {
        if (f_parkingSpot->getSide() == LEFTSIDE)
        {
          if (LEFTSIDE == m_side
              && (cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL == static_cast<cc::target::common::EFAPAParkSlotType>(m_spotType))
              && (true == f_parkingSpot->getVisibility())
              && (m_textureState == f_parkingSpot->getSpotState())
            )
          {
            m_visible = true;
          }
        }
        else
        {
          if (RIGHTSIDE == m_side
              && (cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL == static_cast<cc::target::common::EFAPAParkSlotType>(m_spotType))
              && (true == f_parkingSpot->getVisibility())
              && (m_textureState == f_parkingSpot->getSpotState())
            )
          {
            m_visible = true;
          }
        }
      }
      break;
    case cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT:
      {break;}
    default:
    {break;}
  }

  const osg::Vec3f l_scale(f_parkingSpot->getSize(), 1.0f);
  setMatrix(osg::Matrix::scale(l_scale));
}


void ParkingSpotPlane::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::CULL_VISITOR == f_nv.getVisitorType())
  {
    if (m_visible)
    {
      osg::MatrixTransform::traverse(f_nv);
    }
  }
  else
  {
    osg::MatrixTransform::traverse(f_nv);
  }
}

void setSpotPlaneAttributes(std::array<osg::ref_ptr<ParkingSpotPlane>, 12> &f_spotsPlaneArray, vfc::uint8_t position)
{
  switch (static_cast<vfc::int32_t> (position))
  {
  case PARKSLOT_SELECTABLE_PARALLEL_L:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE));
    f_spotsPlaneArray[position]->setSpotSide(LEFTSIDE);
    break;}
  case PARKSLOT_SELECTABLE_PARALLEL_R:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE));
    f_spotsPlaneArray[position]->setSpotSide(RIGHTSIDE);
    break;}
  case PARKSLOT_SELECTABLE_CROSS_L:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE));
    f_spotsPlaneArray[position]->setSpotSide(LEFTSIDE);
    break;}
  case PARKSLOT_SELECTABLE_CROSS_R:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE));
    f_spotsPlaneArray[position]->setSpotSide(RIGHTSIDE);
    break;}
  case PARKSLOT_SELECTED_PARALLEL_L:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED));
    f_spotsPlaneArray[position]->setSpotSide(LEFTSIDE);
    break;}
  case PARKSLOT_SELECTED_PARALLEL_R:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_PARALLEL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED));
    f_spotsPlaneArray[position]->setSpotSide(RIGHTSIDE);
    break;}
  case PARKSLOT_SELECTED_CROSS_L:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED));
    f_spotsPlaneArray[position]->setSpotSide(LEFTSIDE);
    break;}
  case PARKSLOT_SELECTED_CROSS_R:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_CROSS));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED));
    f_spotsPlaneArray[position]->setSpotSide(RIGHTSIDE);
    break;}
  case PARKSLOT_SELECTED_DIAGONAL_L:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED));
    f_spotsPlaneArray[position]->setSpotSide(LEFTSIDE);
    break;}
  case PARKSLOT_SELECTED_DIAGONAL_R:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTED));
    f_spotsPlaneArray[position]->setSpotSide(RIGHTSIDE);
    break;}
  case PARKSLOT_SELECTABLE_DIAGONAL_L:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE));
    f_spotsPlaneArray[position]->setSpotSide(LEFTSIDE);
    break;}
  case PARKSLOT_SELECTABLE_DIAGONAL_R:
    {f_spotsPlaneArray[position]->setSpotType(static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DIAGONAL));
    f_spotsPlaneArray[position]->setTextureOriginalState(static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_SELECTABLE));
    f_spotsPlaneArray[position]->setSpotSide(RIGHTSIDE);
    break;}
  default:
    {break;}
  }
}

void setParkingSpotIconPath(std::array<std::string, PARKINGSPOTSNUM> &f_spotsAddrs)
{
  f_spotsAddrs[0u]  = g_settings->m_filenameSpotTextureParallelSelectable_left;
  f_spotsAddrs[1u]  = g_settings->m_filenameSpotTextureParallelSelectable_right;
  f_spotsAddrs[2u]  = g_settings->m_filenameSpotTextureCrossSelectable_left;
  f_spotsAddrs[3u]  = g_settings->m_filenameSpotTextureCrossSelectable_right;
  f_spotsAddrs[4u]  = g_settings->m_filenameSpotTextureParallelSelected_left;
  f_spotsAddrs[5u]  = g_settings->m_filenameSpotTextureParallelSelected_right;
  f_spotsAddrs[6u]  = g_settings->m_filenameSpotTextureCrossSelected_left;
  f_spotsAddrs[7u]  = g_settings->m_filenameSpotTextureCrossSelected_right;
  f_spotsAddrs[8u]  = g_settings->m_filenameSpotTextureDiagonalSelected_left;
  f_spotsAddrs[9u]  = g_settings->m_filenameSpotTextureDiagonalSelected_right;
  f_spotsAddrs[10u] = g_settings->m_filenameSpotTextureDiagonalSelectable_left;
  f_spotsAddrs[11u] = g_settings->m_filenameSpotTextureDiagonalSelectable_right;
}
//!
//! ParkingSpot
//!
ParkingSpot::ParkingSpot() // PRQA S 6044
  : m_spotType{static_cast<vfc::uint8_t>(cc::target::common::EFAPAParkSlotType::APASLOT_DEFAULT)}
  , m_spotState{static_cast<vfc::uint8_t>(cc::target::common::EPARKSlotStsR2L::PARKSLOT_NONE)}
  , m_dirty{false}
  , m_visible{false}
{
  setNumChildrenRequiringUpdateTraversal(1u);

  std::array<osg::ref_ptr<ParkingSpotPlane>,   PARKINGSPOTSNUM> l_spotsPlane; // PRQA S 4102
  std::array<std::string,                      PARKINGSPOTSNUM> l_spotsAddrs; // PRQA S 4102
  osg::Image*                                                   l_spotsImage = nullptr;
  osg::Texture2D*                                               l_spotTexture2D = nullptr;
  osg::observer_ptr<osg::StateSet>                              l_spotsState;
  pc::core::TextureShaderProgramDescriptor                      l_parallelSelectableShader_left("advancedTex");

  setParkingSpotIconPath(l_spotsAddrs);

  for(vfc::uint8_t l_SpotIndex = 0u; l_SpotIndex < PARKINGSPOTSNUM; l_SpotIndex++)
  {
    l_spotsPlane[l_SpotIndex]    = new ParkingSpotPlane;
    osg::Group::addChild(l_spotsPlane[l_SpotIndex]); // PRQA S 3803  #code looks fine
    l_spotsState                 = new osg::StateSet;
    l_spotsImage                 = osgDB::readImageFile(l_spotsAddrs[l_SpotIndex]);
    l_spotTexture2D              = new osg::Texture2D(l_spotsImage); // PRQA S 4262 // PRQA S 4264

    l_spotTexture2D->setDataVariance(osg::Object::STATIC);
    l_spotTexture2D->setUnRefImageDataAfterApply(true);
    l_spotTexture2D->setFilter(osg::Texture::MIN_FILTER, osg::Texture::LINEAR_MIPMAP_LINEAR);
    l_spotTexture2D->setFilter(osg::Texture::MAG_FILTER, osg::Texture::LINEAR);
    l_spotTexture2D->setWrap(osg::Texture::WRAP_S, osg::Texture::CLAMP_TO_EDGE);
    l_spotTexture2D->setWrap(osg::Texture::WRAP_T, osg::Texture::CLAMP_TO_EDGE);

    l_spotsState->setTextureAttribute(0u, l_spotTexture2D);
    l_spotsState->setMode(GL_DEPTH_TEST, osg::StateAttribute::ON); // PRQA S 3143
    l_spotsState->setMode(GL_BLEND, osg::StateAttribute::ON); // PRQA S 3143
    l_spotsState->setRenderBinDetails(pc::core::g_renderOrder->m_carShadow - 1, "RenderBin");

    l_spotsState->getOrCreateUniform("u_texSelect", osg::Uniform::FLOAT_VEC4)->set(osg::Vec4f(1.0f, 1.0f, 0.0f, 0.0f));    // PRQA S 3803
    l_spotsState->getOrCreateUniform("u_alpha", osg::Uniform::FLOAT)->set(1.0f);    // PRQA S 3803
    l_spotsState->getOrCreateUniform("u_bias", osg::Uniform::FLOAT)->set(0.0f);    // PRQA S 3803

    l_parallelSelectableShader_left.apply(l_spotsState.get());    // PRQA S 3803
    l_spotsPlane[l_SpotIndex]->setStateSet(l_spotsState.get());

    setSpotPlaneAttributes(l_spotsPlane, l_SpotIndex);
  }

}


ParkingSpot::ParkingSpot(const ParkingSpot& f_other, const osg::CopyOp& f_copyOp)
  : osg::MatrixTransform{f_other, f_copyOp}
  , m_spotType{f_other.m_spotType}
  , m_position{f_other.m_position}
  , m_middle{f_other.m_middle}
  , m_size{f_other.m_size}
  , m_dirty{f_other.m_dirty}
  , m_visible{f_other.m_visible}
{
}


vfc::float32_t ParkingSpot::getGroundLevel() const
{
  return g_settings->m_groundLevel;
}


void ParkingSpot::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (m_dirty)
    {
      const osg::Vec3f l_position(m_position, g_settings->m_groundLevel);
      setMatrix(osg::Matrix::translate(l_position));

      const vfc::uint32_t l_numChildren = getNumChildren();
      for (vfc::uint32_t i = 0u; i < l_numChildren; ++i)
      {
        ParkingSpotNode* const l_parkingSpotDrawable = dynamic_cast<ParkingSpotNode*> (getChild(i));
        if (l_parkingSpotDrawable != nullptr)
        {
          l_parkingSpotDrawable->update(this);
        }
      }
      m_dirty = false;
    }
  }
  osg::MatrixTransform::traverse(f_nv);
}


} // namespace parkingspots
} // namespace assets
} // namespace cc

