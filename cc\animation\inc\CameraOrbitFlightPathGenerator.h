//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_VIRTCAM_CAMREAORBITFLIGHTPATHGENERATOR_H
#define CC_VIRTCAM_CAMREAORBITFLIGHTPATHGENERATOR_H

#include "pc/svs/animation/src/CameraOrbitFlightPathGenerator.h"
#include "cc/daddy/inc/CustomDaddyPorts.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/sm/viewmode/inc/ViewModexViewStateMachine_R2015b_types.h"

namespace cc
{
namespace virtcam
{

template <typename T>
T Lerp(const T& f_a, const T& f_b, float f_t)
{
  return f_a + ((f_b - f_a) * f_t);
}

osg::Vec4f deltaAngleDeg(const osg::Vec4f& f_current, const osg::Vec4f& f_target);

class CoefficientCamerFlightSpeedParameters : public pc::util::coding::ISerializable
{
  public:

  CoefficientCamerFlightSpeedParameters()
    : m_coefficientCamerFlightSpeed(1.0f)
    , m_deltaSample(0.2f)
    , m_flightTypeCentered(true)
    , m_centeredWarnDist(0.5f)
  {
  }

  SERIALIZABLE(CoefficientCamerFlightSpeedParameters)
  {
    ADD_FLOAT_MEMBER(coefficientCamerFlightSpeed);
    ADD_FLOAT_MEMBER(deltaSample);
    ADD_BOOL_MEMBER(flightTypeCentered);
    ADD_FLOAT_MEMBER(centeredWarnDist);
  }

  float m_coefficientCamerFlightSpeed;
  float m_deltaSample;
  bool  m_flightTypeCentered;
  float m_centeredWarnDist;
};

extern pc::util::coding::Item<CoefficientCamerFlightSpeedParameters> g_coefficientSpeedSettings;

osg::Quat slerp( float t, const osg::Quat& from, const osg::Quat& to, bool f_flipDirection);

namespace core // PRQA S 2502
{
class CustomFramework;
} // namespace core

//!
//! Helper class to generate animated camera fly paths for the CameraFlightManipulator class
//!
class CameraOrbitFlightPathGenerator : public pc::virtcam::CameraOrbitFlightPathGenerator
{

public:

  explicit CameraOrbitFlightPathGenerator(const osg::BoundingBox& f_minimalBoundingBox, cc::core::CustomFramework* f_customFramework);

  //!
  //! generates a path to be used in CameraFlightManipulator
  //! @param f_startCam starting camera position
  //! @param f_endCam end camera position
  //! @param f_flightType defines which method is used to create the cameraFlight
  //!
  //! @return path to be used in CameraFlightManipulator
  virtual osgAnimation::Animation* generatePath(
      const pc::virtcam::VirtualCamera& f_startCam,
      const pc::virtcam::VirtualCamera& f_endCam,
      pc::virtcam::FlightType f_flightType) const override;

  bool estimateCenter(const osg::Matrixf& f_viewMatrix, osg::Vec3f& f_center, float f_learningRate = 0.0f) const;
  bool getSettingIsflightTypeCentered() const;


protected:

  virtual ~CameraOrbitFlightPathGenerator() = default;

  osgAnimation::Animation* generateOrbitAnimation(
    pc::virtcam::VirtualCamera f_startCam,
    pc::virtcam::VirtualCamera f_endCam) const;
  
  osgAnimation::Animation* generateOrbitAnimationCentered(
    pc::virtcam::VirtualCamera f_startCam,
    pc::virtcam::VirtualCamera f_endCam,
    osg::Vec3f f_estimateStartCenter,
    osg::Vec3f f_estimateEndCenter) const;

private:
  cc::core::CustomFramework* m_customFramework;
  bool isVerticalViewMode() const;

};

} // namespace virtcam
} // namespace cc

#endif // CC_VIRTCAM_CAMREAORBITFLIGHTPATHGENERATOR_H
