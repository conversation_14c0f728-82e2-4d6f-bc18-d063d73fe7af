//-------------------------------------------------------------------------------
// Copyright (c) 2017 by <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#ifndef CC_ASSETS_TRAJECTORY_SUBASSETS_GENERALTRAJECTORYLINE
#define CC_ASSETS_TRAJECTORY_SUBASSETS_GENERALTRAJECTORYLINE

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/ref_ptr>

#include "cc/assets/trajectory/inc/CommonTypes.h"
#include "cc/assets/trajectory/inc/Frame.h"
#include "cc/assets/trajectory/inc/MainLogic.h"

namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace trajectory
{
namespace mainlogic
{
class MainLogic;
struct ModelData_st;
struct Inputs_st;
} // namespace mainlogic

struct TrajectoryParams_st
{
    // Dimensions in [m], colors in [RGBA; (0..1)].
    // Widths are measured from gradient center to gradient center.

    float      OutermostLine_Width;
    osg::Vec4f OutermostLine_Color_Manual;
    osg::Vec4f OutermostLine_Color_Auto;
    osg::Vec4f OutermostLine_Colorful_Color_1;
    osg::Vec4f OutermostLine_Colorful_Color_2;
    osg::Vec4f OutermostLine_Colorful_Color_3;

    float OL_WT_minGap;                        // Minimum gap to hold between the OutermostLine and the WheelTrack.
    float OutermostLineColoful_DI_MagicOffset; // To adjust DI_OutermostLine to same position of None_DI_OutermostLine

    float      WheelTrack_Width_Whole;
    float      WheelTrack_Width_BorderLine;
    osg::Vec4f WheelTrack_Color_Manual_Inside;
    osg::Vec4f WheelTrack_Color_Manual_BorderLine;
    osg::Vec4f WheelTrack_Color_Auto_Close_Inside;
    osg::Vec4f WheelTrack_Color_Auto_Close_BorderLine;
    osg::Vec4f WheelTrack_Color_Auto_Far_Inside;
    osg::Vec4f WheelTrack_Color_Auto_Far_BorderLine;

    float      ParkingTraj_Width_Whole;
    float      ParkingTraj_Width_Shadow;
    float      ParkingTraj_Width_BorderLine;
    osg::Vec4f ParkingTraj_Color_Inside;
    osg::Vec4f ParkingTraj_Color_BorderLine;

    float      ActionPoint_Length;
    osg::Vec4f ActionPoint_Color;

    float      THTraj_Width;
    float      THTraj_Length;
    osg::Vec4f THTraj_Color;
    osg::Vec4f THTrajBorder_Color;
    float      THTraj_ColorGradientPosRatio;

    float      DL1_Width;
    float      DL1_Offset_Front;
    float      DL1_Offset_Rear;
    osg::Vec4f DL1_Color;

    float Length;             // The length of the normal trajectory lines (not TH, it is separate).
    float GradientWidth;      // Gradient width of all trajectory lines.
    float RenderOffset_Front; // Offset so the remainingDistance cant get behind the distanceLine
    float RenderOffset_Rear;

    float AnimDurRemainingDistance; // animation duration for the remaining distance
};

class GeneralTrajectoryLine : public osg::Geode
{
public:
    GeneralTrajectoryLine(
        pc::core::Framework*                         f_framework,
        cc::assets::trajectory::commontypes::Side_en f_side,
        unsigned int                                 f_numOfVertexLines,
        float                                        f_height,
        const TrajectoryParams_st&                   f_trajParams,
        bool                                         f_hideCallback);

    bool isCulled() const
    {
        return m_cull;
    }

    void setCull(bool f_isCulled)
    {
        m_cull = f_isCulled;
    }

    bool isHidden() const
    {
        return m_hide;
    }

    void setHide(bool f_isHidden)
    {
        m_hide = f_isHidden;
    }

    cc::assets::trajectory::commontypes::Side_en getSide();

    virtual void generateVertexData() = 0;

    cc::assets::trajectory::mainlogic::MainLogic* getMainLogicPtr();

protected:
    virtual ~GeneralTrajectoryLine();

    friend class cc::assets::trajectory::mainlogic::MainLogic;

    static osg::ref_ptr<cc::assets::trajectory::mainlogic::MainLogic> sm_mainLogicRefPtr;

    bool                                               m_cull;
    bool                                               m_hide;
    const cc::assets::trajectory::commontypes::Side_en mc_side;
    const unsigned int                                 mc_numOfVertexLines;
    float                                              m_height; // Z position of the mesh

    cc::assets::trajectory::frame::Frame m_frame;
    std::vector<float>                   m_frameRadiuses;       // For the curved trajectory
    std::vector<float>                   m_frameLateralOffsets; // For the straight trajectory

    const TrajectoryParams_st& m_trajParams;

    float        m_lineGeometryWidth; // The width of the quad stripe in case the texturing method is used [m].
    unsigned int m_lastUpdate;

    osg::ref_ptr<osg::Geometry> m_geometry;

private:
    //! Copy constructor is not permitted.
    GeneralTrajectoryLine(const GeneralTrajectoryLine& other); // = delete
    //! Copy assignment operator is not permitted.
    GeneralTrajectoryLine& operator=(const GeneralTrajectoryLine& other); // = delete
};

} // namespace trajectory
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_TRAJECTORY_SUBASSETS_GENERALTRAJECTORYLINE
