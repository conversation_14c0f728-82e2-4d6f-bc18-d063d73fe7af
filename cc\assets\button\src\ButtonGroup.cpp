//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/button/inc/Button.h"
#include "cc/assets/button/inc/ButtonGroup.h"

namespace cc
{
namespace assets
{
namespace button
{

ButtonGroup::ButtonGroup(cc::core::AssetId f_assetId)
    : pc::core::Asset{f_assetId}
{
    setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}

void ButtonGroup::addButton(cc::assets::button::Button* f_asset)
{
    osg::Group::addChild(f_asset); // PRQA S 3803
}

void ButtonGroup::addButtonGroup(cc::assets::button::ButtonGroup * f_asset)
{
    osg::Group::addChild(f_asset); // PRQA S 3803
}

void ButtonGroup::traverse(osg::NodeVisitor& f_nv)
{
    if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
    {
        update();
    }
    if (m_enabled)
    {
        pc::core::Asset::traverse(f_nv);
    }
}

void ButtonGroup::setHoriReferenceView(pc::core::View* f_view)
{
    for (vfc::uint32_t i = 0u; i < osg::Group::getNumChildren(); i++)
    {
        osg::Node* const child = osg::Group::getChild(i);
        if (child != nullptr)
        {
            static_cast<Button*>(child)->setHoriReferenceView(f_view); // PRQA S 3076
        }
    }
}

void ButtonGroup::setVertReferenceView(pc::core::View* f_view)
{
    for (vfc::uint32_t i = 0u; i < osg::Group::getNumChildren(); i++)
    {
        osg::Node* const child = osg::Group::getChild(i);
        if (child != nullptr)
        {
            static_cast<Button*>(child)->setVertReferenceView(f_view); // PRQA S 3076
        }
    }
}

bool ButtonGroup::isTouchInsideButtonGroup(osg::Vec2f f_touch)
{
    if (!m_enabled)
    {
        return false;
    }

    for (vfc::uint32_t i = 0u; i < osg::Group::getNumChildren(); i++)
    {
        Button* const button = dynamic_cast<Button*>(osg::Group::getChild(i));  // PRQA S 3400
        if (button != nullptr)
        {
            if (button->checkTouchInsideResponseArea(f_touch))
            {
                return true;
            }
        }
        ButtonGroup* const buttonGroup = dynamic_cast<ButtonGroup*>(osg::Group::getChild(i));  // PRQA S 3400
        if (buttonGroup != nullptr)
        {
            if (buttonGroup->isTouchInsideButtonGroup(f_touch)) // PRQA S 1521
            {
                return true;
            }
        }
    }
    return false;
}

} // namespace button
} // namespace assets
} // namespace cc
