//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: NVA2HC NGUYEN DUC THIEN Van (CN/ESC-EPA1)
//  Department: CN/ESC
//=============================================================================
/// @swcomponent SVS BYD
/// @file  CameraOrbitFlightPathGenerator.cpp
/// @brief This is a copy version from platform's
/// CameraOrbitFlightPathGenerator to customer path
/// to add a coefficient for the camera Orbit flight animation
//=============================================================================

#include "cc/animation/inc/CameraOrbitFlightPathGenerator.h"
#include "cc/virtcam/inc/HeadUnitHemisphereCameraUpdater.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "osg/Matrix"
#include "osg/Matrixf"
#include "osg/Vec3" // PRQA S 1060
#include "osg/Vec3f"
#include "osgAnimation/EaseMotion"
#include "vfc/core/vfc_types.hpp"

using pc::util::logging::g_AppContext;
namespace cc
{
namespace virtcam
{

pc::util::coding::Item<CoefficientCamerFlightSpeedParameters> g_coefficientSpeedSettings("CamerFlightSpeed");


//!
//! @param f_minimalBoundingBox will be used as bounding box to detect intersections with the vehicle model
//! @param f_codingParamets includes sm_scaleBoundingBoxFactor, m_timeMin_ms,  m_timeMax_ms and sm_speedUp which will be used as static member variable
//!
CameraOrbitFlightPathGenerator::CameraOrbitFlightPathGenerator(const osg::BoundingBox& f_minimalBoundingBox, cc::core::CustomFramework* f_customFramework)
  : pc::virtcam::CameraOrbitFlightPathGenerator{f_minimalBoundingBox}
  , m_customFramework{f_customFramework}
{
}


// This function is an extension of the OSG Quat::slerp function.
// The goal of this implementation is to be able to decide whether the camera rotates along the shortest or longest path.
osg::Quat slerp( vfc::float32_t t, const osg::Quat& from, const osg::Quat& to, bool f_flipDirection = false )
{
    constexpr vfc::float64_t epsilon = 0.00001;
    vfc::float64_t cosomega   = 0.0f;
    vfc::float64_t scale_from = 0.0f;
    vfc::float64_t scale_to   = 0.0f;

    osg::Quat quatTo(to);

    // this is a dot product
    cosomega = from.asVec4() * to.asVec4();

    if ( cosomega <0.0 )
    {
        cosomega = -cosomega;
        quatTo = -to;
    }

    // This does the trick when we need to interpolate the long way
    if (f_flipDirection)
    {
        quatTo = -quatTo;
    }

    if( (1.0 - cosomega) > epsilon )
    {
        const vfc::float64_t omega = std::acos(cosomega); // 0 <= omega <= Pi (see man acos)
        const vfc::float64_t sinomega = std::sin(omega);  // this sinomega should always be +ve so
        // could try sinomega=sqrt(1-cosomega*cosomega) to avoid a sin()?
        scale_from = std::sin((1.0-t)*omega)/sinomega ;
        scale_to = std::sin(t*omega)/sinomega ;
    }
    else
    {
        /* --------------------------------------------------
           The ends of the vectors are very close
           we can use simple linear interpolation - no need
           to worry about the "spherical" interpolation
           -------------------------------------------------- */
        scale_from = 1.0 - t ;
        scale_to = t ;
    }

    const osg::Quat ret = (from*scale_from) + (quatTo*scale_to);
    return ret;
}


//! extension of the the pc::util::deltaAngleDeg function to a vec4
osg::Vec4f deltaAngleDeg(const osg::Vec4f& f_current, const osg::Vec4f& f_target)
{
  return osg::Vec4f(
      pc::util::deltaAngleDeg(f_current[0u], f_target[0u]),
      pc::util::deltaAngleDeg(f_current[1u], f_target[1u]),
      pc::util::deltaAngleDeg(f_current[2u], f_target[2u]),
      pc::util::deltaAngleDeg(f_current[3u], f_target[3u]));
}


osgAnimation::Animation* CameraOrbitFlightPathGenerator::generateOrbitAnimation(
  pc::virtcam::VirtualCamera f_startCam,
  pc::virtcam::VirtualCamera f_endCam) const
{
//   using namespace osgAnimation;
  osg::ref_ptr<osgAnimation::Animation> l_animation = new osgAnimation::Animation();

  const osg::ref_ptr<osgAnimation::Vec3LinearChannel> eyeChannel = new osgAnimation::Vec3LinearChannel();
  osgAnimation::Vec3KeyframeContainer * const eyeContainer = eyeChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  const osg::ref_ptr<osgAnimation::QuatSphericalLinearChannel> rotChannel = new osgAnimation::QuatSphericalLinearChannel();
  osgAnimation::QuatKeyframeContainer * const rotContainer = rotChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  const osg::ref_ptr<osgAnimation::FloatLinearChannel> fovyChannel = new osgAnimation::FloatLinearChannel();
  osgAnimation::FloatKeyframeContainer * const fovyContainer = fovyChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  l_animation->setPlayMode(osgAnimation::Animation::ONCE);

  const vfc::float32_t aniDuration = calcAniDuration(f_startCam.m_eye, f_endCam.m_eye)*g_coefficientSpeedSettings->m_coefficientCamerFlightSpeed;
  const vfc::float32_t l_samples = (aniDuration / g_coefficientSpeedSettings->m_deltaSample) + 1.0f;
  vfc::int32_t samples = static_cast<vfc::int32_t>(l_samples); // PRQA S 3016


  // Normalize eye-center-up values -> needed for comparison !
  osg::Matrixf mat;
  mat.makeLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);
  mat.getLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);

  mat.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
  mat.getLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);

  // special case -> start == end
  if ( osg::Vec3f(f_startCam.m_eye    - f_endCam.m_eye    ).length() < 1e-3f &&
       osg::Vec3f(f_startCam.m_center - f_endCam.m_center ).length() < 1e-3f &&
       osg::Vec3f(f_startCam.m_up     - f_endCam.m_up     ).length() < 1e-3f  )
  {
    samples = 0;
  }

  const osg::ref_ptr<osgAnimation::InOutSineMotion> motion = new osgAnimation::InOutSineMotion(0.0f, aniDuration);

  const vfc::float32_t deltaTime = aniDuration / static_cast<vfc::float32_t> (samples);
  const vfc::float32_t deltaFovy = f_endCam.m_fovy - f_startCam.m_fovy;

  // rot quat
  mat.makeLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);
  mat = mat.inverse(mat);
  const osg::Quat rot0 = mat.getRotate();

  mat.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
  mat = mat.inverse(mat);
  const osg::Quat rot1 = mat.getRotate();

  // radius
  const vfc::float32_t rho0 = f_startCam.m_eye.length();
  const vfc::float32_t rho1 = f_endCam.m_eye.length();
  const vfc::float32_t deltaRho = rho1-rho0;

  // elevation
  const vfc::float32_t phi0 = std::acos( f_startCam.m_eye.z() / f_startCam.m_eye.length() );
  const vfc::float32_t phi1 = std::acos( f_endCam.m_eye.z() / f_endCam.m_eye.length() );
  const vfc::float32_t deltaPhi = phi1-phi0;

  // direction
  const vfc::float32_t theta0 = std::atan2(f_startCam.m_eye.y(), f_startCam.m_eye.x());
  const vfc::float32_t theta1 = std::atan2(f_endCam.m_eye.y(), f_endCam.m_eye.x());
  vfc::float32_t deltaTheta = 0.0f;
  osg::Vec3f theta_dir = osg::Vec3f(f_startCam.m_eye.x(), f_startCam.m_eye.y(), 0) ^ osg::Vec3f(f_endCam.m_eye.x(), f_endCam.m_eye.y(), 0);
  if (theta_dir.z() > 0.000001f) // counter clockwise
  {
      if(theta1 < theta0)
      {
        deltaTheta = (theta1 + static_cast<vfc::float32_t> (osg::PI * 2.0)) - theta0;
      }
      else
      {
        deltaTheta = theta1-theta0;
      }
  }
  else // clockwise
  {
    if( theta1 < theta0 )
    {
      deltaTheta = theta1-theta0;
    }
    else
    {
      deltaTheta = (theta1 - static_cast<vfc::float32_t> (osg::PI * 2.0)) - theta0;
    }
  }


  // do some fancy maths
  bool l_followLongestRotation = false;
  {
    // Get the middle point of the animation -> t = 0.5...
    const osg::Quat midRot = slerp( 0.5f, rot0, rot1, false );

    // ... and convert it to a matrix
    osg::Matrix midMatrix;
    midRot.get(midMatrix);

    // with this, we know the expected looking direction half way through the animation
    osg::Vec3f viewDirectionUntouched = osg::Vec3f(0.0f, 0.0f, -1.0f) * midMatrix;  // Without eye offset

    // lets assume XY plane and normalize
    viewDirectionUntouched.z() = 0;
    viewDirectionUntouched = viewDirectionUntouched / viewDirectionUntouched.length();


    // Now we grab the eye position half way through the animation...
    const vfc::float32_t midRhoi   = rho0 + deltaRho * 0.5f;
    const vfc::float32_t midThetai = theta0 + deltaTheta * 0.5f;
    const vfc::float32_t midPhii   = phi0 + deltaPhi * 0.5f;
    const osg::Vec3f midEye( midRhoi * std::sin(midPhii) * std::cos(midThetai),
                        midRhoi * std::sin(midPhii) * std::sin(midThetai),
                        midRhoi * std::cos(midPhii) );

    // ... and create a lookat matrix which surely points into (0,0)
    osg::Matrix mm;
    mm.makeLookAt(midEye, osg::Vec3f(0.0f,0.0f,0.0f), osg::Vec3f(0.0f,0.0f,1.0f));

    // With this, we can obtain the approximate looking direction expected from the animation
    const osg::Vec3f expectedMidCenter = osg::Vec3f(0.0f,0.0f,-1.0f) * mm;
    osg::Vec3f viewDirectionToCenter = expectedMidCenter - midEye;

    // lets assume XY plane and normalize
    viewDirectionToCenter.z() = 0;
    viewDirectionToCenter = viewDirectionToCenter / viewDirectionToCenter.length();


    // If both view rays (expected and computed) are pointing in different directions, we need to do something
    const vfc::float32_t cosAngularDistance = viewDirectionToCenter * viewDirectionUntouched; // dot product
    const vfc::float32_t angularDistance = osg::RadiansToDegrees( std::acos(cosAngularDistance) );

    if (angularDistance > 90.0f)
    {
      l_followLongestRotation = true;
    }
  }


  for(vfc::int32_t i = 0; i < samples; i++)
  {
    const vfc::float32_t sinMotion = motion->getValueAt(deltaTime * static_cast<vfc::float32_t>(i));

    const vfc::float32_t rhoi   = rho0 + deltaRho * sinMotion;
    const vfc::float32_t thetai = theta0 + deltaTheta * sinMotion;
    const vfc::float32_t phii   = phi0 + deltaPhi * sinMotion;
    const osg::Vec3f eye( rhoi * std::sin(phii) * std::cos(thetai),
                    rhoi * std::sin(phii) * std::sin(thetai),
                    rhoi * std::cos(phii) );

    const vfc::float32_t fovy = f_startCam.m_fovy + deltaFovy * sinMotion;

    const osg::Quat rot = slerp( sinMotion, rot0, rot1, l_followLongestRotation );

    eyeContainer           ->push_back(osgAnimation::Vec3Keyframe( deltaTime * static_cast<vfc::float32_t> (i), eye));
    rotContainer           ->push_back(osgAnimation::QuatKeyframe( deltaTime * static_cast<vfc::float32_t> (i), rot));
    fovyContainer          ->push_back(osgAnimation::FloatKeyframe(deltaTime * static_cast<vfc::float32_t> (i), fovy));
  }
  // set end position
  mat.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
  mat = mat.inverse(mat);
  eyeContainer->push_back(osgAnimation::Vec3Keyframe(aniDuration, f_endCam.m_eye));
  rotContainer->push_back(osgAnimation::QuatKeyframe(aniDuration, mat.getRotate()));
  fovyContainer->push_back(osgAnimation::FloatKeyframe(aniDuration, f_endCam.m_fovy));

  // add channels to animation
  l_animation->addChannel(eyeChannel.get());
  l_animation->addChannel(rotChannel.get());
  l_animation->addChannel(fovyChannel.get());
  l_animation->computeDuration();
  return l_animation.release();
}

  osgAnimation::Animation* CameraOrbitFlightPathGenerator::generateOrbitAnimationCentered(
    pc::virtcam::VirtualCamera f_startCam,
    pc::virtcam::VirtualCamera f_endCam,
    osg::Vec3f f_estimateStartCenter,
    osg::Vec3f f_estimateEndCenter) const
{
//   using namespace osgAnimation;
  osg::ref_ptr<osgAnimation::Animation> l_animation = new osgAnimation::Animation();

  const osg::ref_ptr<osgAnimation::Vec3LinearChannel> eyeChannel = new osgAnimation::Vec3LinearChannel();
  osgAnimation::Vec3KeyframeContainer * const eyeContainer = eyeChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  const osg::ref_ptr<osgAnimation::Vec3LinearChannel> centerChannel = new osgAnimation::Vec3LinearChannel();
  osgAnimation::Vec3KeyframeContainer * const centerContainer = centerChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  const osg::ref_ptr<osgAnimation::Vec3LinearChannel> upChannel = new osgAnimation::Vec3LinearChannel();
  osgAnimation::Vec3KeyframeContainer * const upContainer = upChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  const osg::ref_ptr<osgAnimation::FloatLinearChannel> fovyChannel = new osgAnimation::FloatLinearChannel();
  osgAnimation::FloatKeyframeContainer * const fovyContainer = fovyChannel->getOrCreateSampler()->getOrCreateKeyframeContainer();

  l_animation->setPlayMode(osgAnimation::Animation::ONCE);

  const vfc::float32_t aniDuration = calcAniDuration(f_startCam.m_eye, f_endCam.m_eye)*g_coefficientSpeedSettings->m_coefficientCamerFlightSpeed;
  const vfc::float32_t l_samples = (aniDuration / g_coefficientSpeedSettings->m_deltaSample) + 1.0f;
  vfc::int32_t samples = static_cast<vfc::int32_t>(l_samples); // PRQA S 3016


  // Normalize eye-center-up values -> needed for comparison !
  osg::Matrixf mat;
  mat.makeLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);
  mat.getLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);

  mat.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
  mat.getLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);

  // special case -> start == end
  if ( osg::Vec3f(f_startCam.m_eye    - f_endCam.m_eye    ).length() < 1e-3f &&
       osg::Vec3f(f_startCam.m_center - f_endCam.m_center ).length() < 1e-3f &&
       osg::Vec3f(f_startCam.m_up     - f_endCam.m_up     ).length() < 1e-3f  )
  {
    samples = 0;
  }

  const osg::ref_ptr<osgAnimation::InOutSineMotion> motion = new osgAnimation::InOutSineMotion(0.0f, aniDuration);

  const vfc::float32_t deltaTime = aniDuration / static_cast<vfc::float32_t> (samples);
  const vfc::float32_t deltaFovy = f_endCam.m_fovy - f_startCam.m_fovy;

  // rot quat
  mat.makeLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);
  mat = mat.inverse(mat);
  const osg::Quat rot0 = mat.getRotate();    // PRQA S 3803  #code looks fine

  mat.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
  mat = mat.inverse(mat);
  const osg::Quat rot1 = mat.getRotate();    // PRQA S 3803  #code looks fine

  // radius
  const vfc::float32_t rho0 = f_startCam.m_eye.length();
  const vfc::float32_t rho1 = f_endCam.m_eye.length();
  const vfc::float32_t deltaRho = rho1-rho0;

  // elevation
  const vfc::float32_t phi0 = std::acos( f_startCam.m_eye.z() / f_startCam.m_eye.length() );
  const vfc::float32_t phi1 = std::acos( f_endCam.m_eye.z() / f_endCam.m_eye.length() );
  const vfc::float32_t deltaPhi = phi1-phi0;

  // direction
  vfc::float32_t theta0 = std::atan2(f_startCam.m_eye.y(), f_startCam.m_eye.x());
  vfc::float32_t theta1 = std::atan2(f_endCam.m_eye.y(), f_endCam.m_eye.x());
  vfc::float32_t deltaTheta = 0.0f;
  osg::Vec3f theta_dir = osg::Vec3f(f_startCam.m_eye.x(), f_startCam.m_eye.y(), 0) ^ osg::Vec3f(f_endCam.m_eye.x(), f_endCam.m_eye.y(), 0);
  if (theta_dir.z() > 0.000001f) // counter clockwise
  {
      if(theta1 < theta0)
      {
        deltaTheta = (theta1 + static_cast<vfc::float32_t> (osg::PI * 2.0)) - theta0;
      }
      else
      {
        deltaTheta = theta1-theta0;
      }
  }
  else // clockwise
  {
    if( theta1 < theta0 )
    {
      deltaTheta = theta1-theta0;
    }
    else
    {
      deltaTheta = (theta1 - static_cast<vfc::float32_t> (osg::PI * 2.0)) - theta0;
    }
  }

  constexpr vfc::float32_t l_espilon = 0.05f; // radian, ~ 2.86 degree

  if ( isGreaterEqual( theta0 , static_cast<vfc::float32_t>(-1.0f*osg::PI) - l_espilon)  &&
          isLessEqual( theta0 , static_cast<vfc::float32_t>(-1.0f*osg::PI) + l_espilon)  &&
       isGreaterEqual( theta1 , 0.0f - l_espilon)  &&
          isLessEqual( theta1 , 0.0f + l_espilon)     )

  {
      // start rear, end front
      theta0 = -1.0f*theta0;
      deltaTheta = -1.0f*std::abs(deltaTheta);
      //XLOG_INFO_OS(g_AppContext) << "Start rear, end front optimized!"<< XLOG_ENDL;
  }
  else if ( isGreaterEqual( theta0 , 0.0f - l_espilon)  &&
               isLessEqual( theta0 , 0.0f + l_espilon)  &&
            isGreaterEqual( theta1 , static_cast<vfc::float32_t>(1.0f*osg::PI) - l_espilon)  &&
               isLessEqual( theta1 , static_cast<vfc::float32_t>(1.0f*osg::PI) + l_espilon)     )
  {
     // start front, end rear
     theta1 = -1.0f*theta1;
     deltaTheta = -1.0f*std::abs(deltaTheta);
     //XLOG_INFO_OS(g_AppContext) << "Start front, end rear optimized!"<< XLOG_ENDL;
  }
  else
  {
    // do nothing
  }

  // // do some fancy maths
  // bool l_followLongestRotation = false;
  // {
  //   // Get the middle point of the animation -> t = 0.5...
  //   osg::Quat midRot = slerp( 0.5f, rot0, rot1, false );

  //   // ... and convert it to a matrix
  //   osg::Matrix midMatrix;
  //   midRot.get(midMatrix);

  //   // with this, we know the expected looking direction half way through the animation
  //   osg::Vec3f viewDirectionUntouched = osg::Vec3f(0,0,-1) * midMatrix;  // Without eye offset

  //   // lets assume XY plane and normalize
  //   viewDirectionUntouched.z() = 0;
  //   viewDirectionUntouched = viewDirectionUntouched / viewDirectionUntouched.length();


  //   // Now we grab the eye position half way through the animation...
  //   float midRhoi   = rho0 + deltaRho * 0.5f;
  //   float midThetai = theta0 + deltaTheta * 0.5f;
  //   float midPhii   = phi0 + deltaPhi * 0.5f;
  //   osg::Vec3f midEye( midRhoi * std::sin(midPhii) * std::cos(midThetai),
  //                       midRhoi * std::sin(midPhii) * std::sin(midThetai),
  //                       midRhoi * std::cos(midPhii) );

  //   // ... and create a lookat matrix which surely points into (0,0)
  //   osg::Matrix mm;
  //   mm.makeLookAt(midEye, osg::Vec3f(0,0,0), osg::Vec3f(0,0,1));

  //   // With this, we can obtain the approximate looking direction expected from the animation
  //   osg::Vec3f expectedMidCenter = osg::Vec3f(0,0,-1) * mm;
  //   osg::Vec3f viewDirectionToCenter = expectedMidCenter - midEye;

  //   // lets assume XY plane and normalize
  //   viewDirectionToCenter.z() = 0;
  //   viewDirectionToCenter = viewDirectionToCenter / viewDirectionToCenter.length();


  //   // If both view rays (expected and computed) are pointing in different directions, we need to do something
  //   float cosAngularDistance = viewDirectionToCenter * viewDirectionUntouched; // dot product
  //   float angularDistance = osg::RadiansToDegrees( std::acos(cosAngularDistance) );

  //   if (angularDistance > 90.0f)
  //   {
  //     l_followLongestRotation = true;
  //   }
  // }

  osg::Vec3f l_carCenterOffset;

  if (this->isVerticalViewMode())
  {
    l_carCenterOffset = g_carCenter->m_carCenterVert;
  }
  else
  {
    l_carCenterOffset = g_carCenter->m_carCenterHori;
  }

  osg::Vec3f l_vehicleCenter(pc::vehicle::g_mechanicalData->getCenter(), 0);
  l_vehicleCenter.x() = l_vehicleCenter.x() + l_carCenterOffset.x();
  l_vehicleCenter.y() = l_vehicleCenter.y() + l_carCenterOffset.y();
  l_vehicleCenter.z() = l_vehicleCenter.z() + l_carCenterOffset.z();

  // XLOG_INFO_OS(g_AppContext) << "l_vehicleCenter : "<<l_vehicleCenter.x() <<" : "<<l_vehicleCenter.y() <<" : "<<l_vehicleCenter.z()<< XLOG_ENDL;

  osg::Vec3f l_up_vec = osg::Vec3f(0.0f, 0.0, 1.0f);

  for(vfc::int32_t i = 0; i < samples; i++)
  {
    const vfc::float32_t sinMotion = motion->getValueAt(deltaTime * static_cast<vfc::float32_t>(i));

    const vfc::float32_t rhoi   = rho0 + deltaRho * sinMotion;
    const vfc::float32_t thetai = theta0 + deltaTheta * sinMotion;
    const vfc::float32_t phii   = phi0 + deltaPhi * sinMotion;
    osg::Vec3f eye( rhoi * std::sin(phii) * std::cos(thetai),
                    rhoi * std::sin(phii) * std::sin(thetai),
                    rhoi * std::cos(phii) );

    const vfc::float32_t fovy = f_startCam.m_fovy + deltaFovy * sinMotion;

    if ( (f_estimateEndCenter - f_estimateStartCenter).length() > 0.01f )
    {
      l_vehicleCenter = Lerp(f_estimateStartCenter, f_estimateEndCenter, (static_cast<vfc::float32_t>(i)+1.0f) / (static_cast<vfc::float32_t>(samples)-1.0f));
    }
    else
    {
      l_vehicleCenter = f_estimateStartCenter;
    }

    if (this->isVerticalViewMode())
    {
      // l_up_vec = osg::Vec3f(-std::sin(thetai), std::cos(thetai), 0.0f);  // Apply for (0,0,0)

      osg::Vec3f vec_a = osg::Vec3f(l_vehicleCenter.x() -  static_cast<vfc::float32_t>(eye.x()),l_vehicleCenter.y() -  static_cast<vfc::float32_t>(eye.y()),0);
      vec_a.normalize();    // PRQA S 3803  // PRQA S 3804
      const osg::Vec3f vec_b = osg::Vec3f(0.0f, 0.0f, 1.0f);
      // cross product
      l_up_vec = vec_a^vec_b;

    }
    else
    {
      l_up_vec = osg::Vec3f(0.0f, 0.0f, 1.0f);
    }

    //XLOG_INFO_OS(g_AppContext) << "thetai "<<i<<" : "<<thetai<<" x: "<<l_up_vec.x() <<" y: "<<l_up_vec.y() <<" z: "<<l_up_vec.z()<< XLOG_ENDL;
    //XLOG_INFO_OS(g_AppContext) << "fovy "<<i<<" :"<<l_up_vec.y() << XLOG_ENDL;

    eyeContainer           ->push_back(osgAnimation::Vec3Keyframe( deltaTime * static_cast<vfc::float32_t> (i), eye));
    centerContainer        ->push_back(osgAnimation::Vec3Keyframe( deltaTime * static_cast<vfc::float32_t> (i), l_vehicleCenter));
    upContainer            ->push_back(osgAnimation::Vec3Keyframe( deltaTime * static_cast<vfc::float32_t> (i), l_up_vec));
    fovyContainer          ->push_back(osgAnimation::FloatKeyframe(deltaTime * static_cast<vfc::float32_t> (i), fovy));
  }
  // set end position, we can try to ignore this end-frame if there is a slight jump
  if (samples < 1) // if there are no calculated frame on before
  {
    mat.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
    mat = mat.inverse(mat);
    eyeContainer->push_back(osgAnimation::Vec3Keyframe(aniDuration, f_endCam.m_eye));
    centerContainer->push_back(osgAnimation::Vec3Keyframe(aniDuration, f_endCam.m_center));
    upContainer->push_back(osgAnimation::Vec3Keyframe(aniDuration, f_endCam.m_up));
    fovyContainer->push_back(osgAnimation::FloatKeyframe(aniDuration, f_endCam.m_fovy));
  }

  // add channels to animation
  l_animation->addChannel(eyeChannel.get());
  l_animation->addChannel(centerChannel.get());
  l_animation->addChannel(upChannel.get());
  l_animation->addChannel(fovyChannel.get());
  l_animation->computeDuration();
  return l_animation.release();
}


osgAnimation::Animation* CameraOrbitFlightPathGenerator::generatePath(
  const pc::virtcam::VirtualCamera& f_startCam,
  const pc::virtcam::VirtualCamera& f_endCam,
  pc::virtcam::FlightType f_flightType) const
{

  if (f_flightType == pc::virtcam::FLIGHT_VEHICLE_CENTERED && getSettingIsflightTypeCentered())
  {
    osg::Vec3f l_learned_estimateStartCenter;
    osg::Vec3f l_learned_estimateEndCenter;
    vfc::float32_t l_learned_estimatedLenght = 10.0f;

    for  (vfc::int32_t i = 0; i <25; i ++)
    {
      osg::Matrixf l_startCamMatrix;
      l_startCamMatrix.makeLookAt(f_startCam.m_eye, f_startCam.m_center, f_startCam.m_up);
      osg::Vec3f l_estimateStartCenter;
      this->estimateCenter(l_startCamMatrix,l_estimateStartCenter,static_cast<vfc::float32_t>(i)*0.015f);    // PRQA S 3804

      osg::Matrixf l_endCamMatrix;
      l_endCamMatrix.makeLookAt(f_endCam.m_eye, f_endCam.m_center, f_endCam.m_up);
      osg::Vec3f l_estimateEndCenter;
      this->estimateCenter(l_endCamMatrix,l_estimateEndCenter,static_cast<vfc::float32_t>(i)*0.015f);    // PRQA S 3804

      if ((l_estimateEndCenter - l_estimateStartCenter).length() < l_learned_estimatedLenght)
      {
        l_learned_estimateStartCenter = l_estimateStartCenter;
        l_learned_estimateEndCenter   = l_estimateEndCenter;
        l_learned_estimatedLenght     = (l_estimateEndCenter - l_estimateStartCenter).length();
        // XLOG_INFO_OS(g_AppContext) << "Learning loop : "<< i << XLOG_ENDL;
        // XLOG_INFO_OS(g_AppContext) << "                 Start Center : "<< l_estimateStartCenter.x() <<" : "<< l_estimateStartCenter.y() <<" : "<< l_estimateStartCenter.z() << XLOG_ENDL;
        // XLOG_INFO_OS(g_AppContext) << "                 End Center   : "<< l_estimateEndCenter.x() <<" : "<< l_estimateEndCenter.y() <<" : "<< l_estimateEndCenter.z() << XLOG_ENDL;
      }
    }

    if ((l_learned_estimateEndCenter - l_learned_estimateStartCenter).length() > g_coefficientSpeedSettings->m_centeredWarnDist) // greater than 1 meter
    {
      // pf code. #code looks fine
      XLOG_INFO(g_AppContext, "The deviation between estimated StartCenter and estimated EndCenter is too much, recommend for tunning again !");
    }

    return generateOrbitAnimationCentered(f_startCam, f_endCam, l_learned_estimateStartCenter, l_learned_estimateEndCenter);
  }
  else
  {
    //XLOG_INFO_OS(g_AppContext) << "Classical Animation" << XLOG_ENDL;
    return generateOrbitAnimation(f_startCam, f_endCam);
  }

}

bool CameraOrbitFlightPathGenerator::isVerticalViewMode() const
{

  // ! BYD requirements for vertical mode
  bool l_isVerticalViewMode = false;

  EScreenID l_curviewid = EScreenID_NO_CHANGE;

  //! update current displayed viewid
  if (m_customFramework->m_displayedView_ReceiverPort.hasData())
  {
    const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayid = m_customFramework->m_displayedView_ReceiverPort.getData();
    l_curviewid = l_displayid->m_Data;
  }

  if (m_customFramework->m_SVSRotateStatusDaddy_Receiver.isConnected())
  {
    const cc::daddy::SVSRotateStatusDaddy_t* const l_themeType  = m_customFramework->m_SVSRotateStatusDaddy_Receiver.getData();
    if (nullptr != l_themeType)
    {
      const cc::target::common::EThemeTypeHU l_curThemeType = static_cast<cc::target::common::EThemeTypeHU>(l_themeType->m_Data); // PRQA S 3013 // PRQA S 4899

      if (cc::target::common::EThemeTypeHU::ETHEME_TYPE_VERT == l_curThemeType)
      {
        if ((EScreenID_VERT_PERSPECTIVE_PFR_LEFT  == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_PFR_RIGHT == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_PRE_LEFT  == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_PRE_RIGHT == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_RL_LEFT   == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_RL_RIGHT  == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_RR_LEFT   == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_RR_RIGHT  == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_FL_LEFT   == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_FL_RIGHT  == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_FR_LEFT   == l_curviewid) ||
            (EScreenID_VERT_PERSPECTIVE_FR_RIGHT  == l_curviewid))
        {
          l_isVerticalViewMode = true;
        }
      }
      else
      {
        // Do nothing
      }
    }
  }

  return l_isVerticalViewMode;

}

bool CameraOrbitFlightPathGenerator::estimateCenter(const osg::Matrixf& f_viewMatrix, osg::Vec3f& f_center, vfc::float32_t f_learningRate) const // PRQA S 4678 // PRQA S 4287
{
  osg::Vec3f eye, center, up; // PRQA S 4107
  f_viewMatrix.getLookAt(eye, center, up, 1.0f);
  osg::Vec3f dir = (center-eye);
  if (std::abs(dir.z()) < 1E-3f)
  {
    return false;
  }

  // to calculate the center on the height of the baseplate

  f_center = eye - dir*(eye.z()/dir.z());

  f_learningRate = std::abs(f_learningRate/eye.z())*(eye - f_center).length();

  f_center = f_center + dir*f_learningRate;

  // float lengthOfDir2CarCenter = (eye - f_center).length();
  // float lookAtCenter_X = pc::vehicle::g_mechanicalData->getCenter().x(); // ~ 1.4225f
  // if (eye.x() >0.0f )
  // {
  //   lengthOfDir2CarCenter = lengthOfDir2CarCenter + (std::abs(std::abs(f_center.x()) - lookAtCenter_X)/std::abs(std::abs(eye.x()) - lookAtCenter_X))*lengthOfDir2CarCenter;
  // }
  // else
  // {
  //   lengthOfDir2CarCenter = lengthOfDir2CarCenter + (std::abs(std::abs(f_center.x()) - lookAtCenter_X)/std::abs(std::abs(eye.x()) + lookAtCenter_X))*lengthOfDir2CarCenter;
  // }
  // f_center = eye + dir*lengthOfDir2CarCenter;

  return true;
}

bool CameraOrbitFlightPathGenerator::getSettingIsflightTypeCentered() const
{
  return g_coefficientSpeedSettings->m_flightTypeCentered;
}

} // namespace virtcam
} // namespace cc
