//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON> GmbH. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/CoverPlate.h"
#include "vfc/core/vfc_types.hpp"
/// @deviation NRCS2_071
/// Rule QACPP-4.3.0-3804
/// message: "The return value of this function call is not used."
namespace cc
{
namespace assets
{
namespace trajectory
{


CoverPlate::CoverPlate(
  pc::core::Framework* f_framework,
  vfc::float32_t f_height,
  const cc::assets::trajectory::TrajectoryParams_st& f_trajParams,
  vfc::uint32_t f_numOfVerts)
  : GeneralTrajectoryLine{
      f_framework,
      commontypes::Middle_enm /*Dummy, not used*/,
      0u,
      f_height,
      f_trajParams,
      true}
  , m_numOfVerts{f_numOfVerts}
{
  //! init static data
  osg::Vec4Array* const l_colors = static_cast<osg::Vec4Array*> (m_geometry->getColorArray()); // PRQA S 3076
  l_colors->push_back(osg::Vec4f(1.0f, 1.0f, 1.0f, 0.0f));
  l_colors->setBinding(osg::Array::BIND_OVERALL);

  m_numOfRenderedVerts = (m_numOfVerts - 1u) * 6u;
  osg::DrawElementsUByte* const l_indices = new osg::DrawElementsUByte(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), m_numOfRenderedVerts);
  // l_indices->reserve(m_numOfRenderedVerts);

  for (vfc::uint32_t i = 0u; i < (m_numOfVerts - 1u); i++)
  {
    (*l_indices)[(i * 6u)]      = static_cast<osg::DrawElementsUByte::value_type>(i * 2u);
    (*l_indices)[(i * 6u) + 1u] = static_cast<osg::DrawElementsUByte::value_type>((i + 1u) * 2u + 1u);
    (*l_indices)[(i * 6u) + 2u] = static_cast<osg::DrawElementsUByte::value_type>(i * 2u + 1u);
    (*l_indices)[(i * 6u) + 3u] = static_cast<osg::DrawElementsUByte::value_type>(i * 2u);
    (*l_indices)[(i * 6u) + 4u] = static_cast<osg::DrawElementsUByte::value_type>((i + 1u) * 2u);
    (*l_indices)[(i * 6u) + 5u] = static_cast<osg::DrawElementsUByte::value_type>((i + 1u) * 2u + 1u);
  }

  m_geometry->addPrimitiveSet(l_indices);  // PRQA S 3804  // PRQA S 3803

}


CoverPlate::~CoverPlate() = default;


void CoverPlate::generateVertexData()
{
  osg::Vec3Array* const l_vertices = static_cast<osg::Vec3Array*> (m_geometry->getVertexArray()); // PRQA S 3076
  l_vertices->clear();
  // l_vertices->resize((m_numOfVerts - 1) * 6);

  constexpr vfc::float32_t width = 18.0f;
  const vfc::float32_t halfWidth = width * 0.5f;
  const vfc::float32_t partialWidth = width * (1.0f / (static_cast<vfc::float32_t>(m_numOfVerts) - 1.0f));
  // const float quarterWidth = halfWidth * 0.5f;
  constexpr vfc::float32_t length = 5.0f;
  const cc::assets::trajectory::mainlogic::Inputs_st& l_inputs = sm_mainLogicRefPtr->getInputDataRef();

  if (cc::assets::trajectory::mainlogic::Forward_enm == l_inputs.External.Car.DrivingDirection)
  {
    const vfc::float32_t offset = m_trajParams.DL1_Offset_Front
                 + m_trajParams.DL1_Width * 0.5f;

    for (vfc::uint32_t i = 0u; i < m_numOfVerts; i++)
    {
      const osg::Vec3f FL = osg::Vec3f(l_inputs.External.Car.FrontBumperPos + offset, halfWidth - partialWidth * static_cast<vfc::float32_t>(i), m_height);
      // osg::Vec3f FR = osg::Vec3f(l_inputs.External.Car.FrontBumperPos + offset, halfWidth - partialWidth * (i + 1), m_height);
      const osg::Vec3f RL = FL + osg::Vec3f(-length, 0.0f, 0.0f);
      // osg::Vec3f RR = FR + osg::Vec3f(-length, 0.0f, 0.0f);

      l_vertices->push_back(RL);
      l_vertices->push_back(FL);
      // l_vertices->push_back(RR);
      // l_vertices->push_back(FR);

    }
  }
  else // if (cc::assets::trajectory::mainlogic::Backward_enm == l_inputs.External.Car.DrivingDirection)
  {
    const vfc::float32_t offset = m_trajParams.DL1_Offset_Rear
                 + m_trajParams.DL1_Width * 0.5f;

    for (vfc::uint32_t i = 0u; i < m_numOfVerts; i++)
    {
      const osg::Vec3f RL = osg::Vec3f(l_inputs.External.Car.RearBumperPos - offset,  halfWidth - partialWidth * static_cast<vfc::float32_t>(i), m_height);
      // osg::Vec3f RR = osg::Vec3f(l_inputs.External.Car.RearBumperPos - offset,  halfWidth - partialWidth * (i + 1), m_height);
      const osg::Vec3f FL = RL + osg::Vec3f(length, 0.0f, 0.0f);
      // osg::Vec3f FR = RR + osg::Vec3f(length, 0.0f, 0.0f);

      l_vertices->push_back(FL);
      l_vertices->push_back(RL);
      // l_vertices->push_back(RR);
      // l_vertices->push_back(FR);
    }

  }

  l_vertices->dirty();
  m_geometry->dirtyBound();

  setCull(false);
}


} // namespace trajectory
} // namespace assets
} // namespace cc
