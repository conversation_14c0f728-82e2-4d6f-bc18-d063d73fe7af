//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Jose Esparza (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomFloorPlateGenerator.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_CUSTOMFLOORPLATEGENERATOR_H
#define CC_ASSETS_CUSTOMFLOORPLATEGENERATOR_H

#include "vfc/core/vfc_types.hpp" // PRQA S 0034
#include "pc/svs/texfloor/core/inc/FloorPlateGenerator.h"
#include "pc/svs/texfloor/core/inc/MovingFloorPlateGenerator.h"
#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

namespace cc
{
namespace assets
{
namespace floorplate
{

//! Uncomment this to use floor plate with a moving history
#define USE_MOVING_FLOORPLATE 1


#ifdef USE_MOVING_FLOORPLATE
  typedef pc::texfloor::core::MovingFloorPlateGenerator FloorPlateBaseType;
#else
  typedef pc::texfloor::core::FloorPlateGenerator FloorPlateBaseType;
#endif

//!
//! CustomFloorPlateData
//!
class CustomFloorPlateData : public pc::util::coding::ISerializable
{
public:

  CustomFloorPlateData()
    : m_rearRightCorner(-1.10f, -1.30f)
    , m_frontLeftCorner( 4.10f,  1.30f)
    , m_odoTuningThreshold_Velocity(0.1f)
    , m_odoTuningThreshold_Yaw(0.1f)
    , m_odoTuningThreshold_Acceleration(15.0f)
    , m_odoCompensate_Forward_Accelerate_k(0.2f)
    , m_odoCompensate_Forward_Accelerate_b(0.0f)
    , m_odoCompensate_Forward_Decelerate_k(0.08f)
    , m_odoCompensate_Forward_Decelerate_b(0.0f)
    , m_odoCompensate_Backward_Accelerate_k(-0.2f)
    , m_odoCompensate_Backward_Accelerate_b(0.0f)
    , m_odoCompensate_Backward_Decelerate_k(-0.1f)
    , m_odoCompensate_Backward_Decelerate_b(0.0f)
  {
  }

  SERIALIZABLE(CustomFloorPlateData)
  {
    ADD_MEMBER(osg::Vec2f, rearRightCorner);
    ADD_MEMBER(osg::Vec2f, frontLeftCorner);
    ADD_FLOAT_MEMBER(odoTuningThreshold_Velocity);
    ADD_FLOAT_MEMBER(odoTuningThreshold_Yaw);
    ADD_FLOAT_MEMBER(odoTuningThreshold_Acceleration);
    ADD_FLOAT_MEMBER(odoCompensate_Forward_Accelerate_k);
    ADD_FLOAT_MEMBER(odoCompensate_Forward_Accelerate_b);
    ADD_FLOAT_MEMBER(odoCompensate_Forward_Decelerate_k);
    ADD_FLOAT_MEMBER(odoCompensate_Forward_Decelerate_b);
    ADD_FLOAT_MEMBER(odoCompensate_Backward_Accelerate_k);
    ADD_FLOAT_MEMBER(odoCompensate_Backward_Accelerate_b);
    ADD_FLOAT_MEMBER(odoCompensate_Backward_Decelerate_k);
    ADD_FLOAT_MEMBER(odoCompensate_Backward_Decelerate_b);
  }

  osg::Vec2f m_rearRightCorner;
  osg::Vec2f m_frontLeftCorner;
  vfc::float32_t m_odoTuningThreshold_Velocity; // m/s // PRQA S 0284
  vfc::float32_t m_odoTuningThreshold_Yaw;
  vfc::float32_t m_odoTuningThreshold_Acceleration;
  vfc::float32_t m_odoCompensate_Forward_Accelerate_k; // y= kx+b
  vfc::float32_t m_odoCompensate_Forward_Accelerate_b;
  vfc::float32_t m_odoCompensate_Forward_Decelerate_k;
  vfc::float32_t m_odoCompensate_Forward_Decelerate_b;
  vfc::float32_t m_odoCompensate_Backward_Accelerate_k; // y= kx+b
  vfc::float32_t m_odoCompensate_Backward_Accelerate_b;
  vfc::float32_t m_odoCompensate_Backward_Decelerate_k;
  vfc::float32_t m_odoCompensate_Backward_Decelerate_b;
};

extern pc::util::coding::Item<CustomFloorPlateData> g_customFloorPlateData;

//======================================================
// CustomFloorPlateGenerator
//------------------------------------------------------
/// Customer-specific floor plate generator.
/// <AUTHOR>
//======================================================
class CustomFloorPlateGenerator : public FloorPlateBaseType // PRQA S 0251
{

public:
    CustomFloorPlateGenerator(pc::core::Framework* f_pFramework);
    virtual ~CustomFloorPlateGenerator();

private:
  //! Copy constructor is not permitted.
  CustomFloorPlateGenerator (const CustomFloorPlateGenerator& other); // = delete
  //! Copy assignment operator is not permitted.
  CustomFloorPlateGenerator& operator=(const CustomFloorPlateGenerator& other); // = delete

};

} // namespace floorplate
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_CUSTOMFLOORPLATEGENERATOR_H
