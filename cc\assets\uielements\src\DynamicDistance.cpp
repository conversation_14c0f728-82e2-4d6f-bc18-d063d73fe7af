//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  DynamicDistance.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/DynamicDistance.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h" // PRQA S 1060
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"
#include "vfc/core/vfc_types.hpp"
#include "osg/Depth"
#include "osg/Geode"
#include "osg/Geometry"
#include "osg/MatrixTransform"
#include "osg/Texture2D"
#include "osgDB/ReadFile"

static uint16_t MAX_TRAVEL_DISTANCE_DESIRED = 9900u; // PRQA S 2300

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace uielements
{

pc::util::coding::Item<DynamicDistanceSettings> g_dynamicDistanceSettings("DynamicDistance");

//!
//! DynamicDistance
//!
DynamicDistance::DynamicDistance(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_DynamicDisGeode{}
{
  setName("DynamicDistance");
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1u);
}


DynamicDistance::~DynamicDistance() = default;


void DynamicDistance::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (g_dynamicDistanceSettings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      addCullCallback(new DynamicDistanceCallback(m_DynamicDisGeode, m_framework));
      m_settingsModifiedCount = g_dynamicDistanceSettings->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}


void DynamicDistance::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803

  m_DynamicDisGeode = new osg::Geode;
  addChild(m_DynamicDisGeode);    // PRQA S 3803
  {

  }


  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
}

DynamicDistanceCallback::DynamicDistanceCallback( // PRQA S 4206
                                                          osg::ref_ptr<osg::Geode> f_DynamicDisGeode,
                                                          pc::core::Framework* f_pFramework
                                                          )
  : m_DynamicDisGeode{f_DynamicDisGeode}
  , m_pFramework{f_pFramework}
{

}

DynamicDistanceCallback::~DynamicDistanceCallback() = default;



void DynamicDistanceCallback::updateDistance(osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{
  uint16_t l_travelDistDesired = 50u;
  bool l_dynamicGearStatus = false;
  bool l_dynamicGearMainViewStatus = false;

  if (m_pFramework->asCustomFramework()->m_parkCartravelDistDesiredReceiver.hasData())
  {
    const cc::daddy::ParkCartravelDistDesiredDaddy_t* const l_travelDistDesiredDaddy = m_pFramework->asCustomFramework()->m_parkCartravelDistDesiredReceiver.getData();
    if (l_travelDistDesiredDaddy != nullptr)
    {
      l_travelDistDesired = static_cast<vfc::uint8_t>(std::abs(l_travelDistDesiredDaddy->m_Data));
    }
  }

  if (m_pFramework->asCustomFramework()->m_dynamicGearStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearStatusPtr = m_pFramework->asCustomFramework()->m_dynamicGearStatus_ReceiverPort.getData();
    l_dynamicGearStatus = l_dynamicGearStatusPtr->m_Data;
  }

  if (m_pFramework->asCustomFramework()->m_dynamicGearMainViewStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearMainViewStatusPtr = m_pFramework->asCustomFramework()->m_dynamicGearMainViewStatus_ReceiverPort.getData();
    l_dynamicGearMainViewStatus = l_dynamicGearMainViewStatusPtr->m_Data;
  }

  const osg::ref_ptr<osgText::Text> l_dynamicDistance = new osgText::Text;
  if (isUIPanel == true)
  {
    l_dynamicDistance->setPosition(g_dynamicDistanceSettings->m_position_UIPanel);
  }
  else
  {
    l_dynamicDistance->setPosition(g_dynamicDistanceSettings->m_position_MainView);
  }
  l_dynamicDistance->setFont(g_dynamicDistanceSettings->m_fontType);
  l_dynamicDistance->setColor(g_dynamicDistanceSettings->m_color);
  l_dynamicDistance->setDrawMode(osgText::TextBase::TEXT); // PRQA S 3143
  l_dynamicDistance->setCharacterSize(g_dynamicDistanceSettings->m_charSize);
  l_dynamicDistance->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
  l_dynamicDistance->setFontResolution(g_dynamicDistanceSettings->m_FontResolutionWidth,g_dynamicDistanceSettings->m_FontResolutionHeight);
  l_dynamicDistance->setAxisAlignment(osgText::Text::XY_PLANE);
  l_dynamicDistance->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
  l_dynamicDistance->setAlignment(osgText::Text::CENTER_CENTER);

  std::ostringstream l_textString;
  if (l_travelDistDesired >= MAX_TRAVEL_DISTANCE_DESIRED)
  {
    l_travelDistDesired = MAX_TRAVEL_DISTANCE_DESIRED;
  }
  if (l_travelDistDesired >= 1000u)
  {
    l_textString << std::fixed << std::setprecision(0) << static_cast<vfc::float32_t>(l_travelDistDesired) / 100.0f << std::endl; //PRQA S 3803
  }
  else
  {
    l_textString << std::fixed << std::setprecision(1) << static_cast<vfc::float32_t>(l_travelDistDesired) / 100.0f << std::endl; //PRQA S 3803
  }

  if ( (isUIPanel && l_dynamicGearStatus)
    || (!isUIPanel && l_dynamicGearMainViewStatus) )
  {
    f_Geode->setNodeMask(~0u);
    l_dynamicDistance->setText(l_textString.str()); // PRQA S 2976
    f_Geode->removeDrawables(0u, 1u);  // PRQA S 3803
    f_Geode->addDrawable(l_dynamicDistance); // PRQA S 3803
  }
  else
  {
    f_Geode->setNodeMask(0u);
  }
}



void DynamicDistanceCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  DynamicDistance* const l_dynamicDistance = dynamic_cast<DynamicDistance*>(f_node);  // PRQA S 3400
  osgUtil::CullVisitor* const l_cv = dynamic_cast<osgUtil::CullVisitor*> (f_nv);  // PRQA S 3400

  if (l_dynamicDistance != nullptr)
  {
    if (nullptr !=l_cv)
    {
      osg::Camera* const l_camera = l_cv->getCurrentCamera();

      if ( (l_camera->getViewport()->width()  == cc::core::g_views->m_mainViewport.m_size.x()) && // PRQA S 3270 // PRQA S 3011
           (l_camera->getViewport()->height() == cc::core::g_views->m_mainViewport.m_size.y()) ) // PRQA S 3270 // PRQA S 3011
      {
          isUIPanel = false;
          updateDistance(*f_nv, m_DynamicDisGeode);
      }
      else
      {
          isUIPanel = true;
          updateDistance(*f_nv, m_DynamicDisGeode);
      }
    }
  }

  traverse(f_node, f_nv);
}

} // namespace uielements
} // namespace assets
} // namespace cc
