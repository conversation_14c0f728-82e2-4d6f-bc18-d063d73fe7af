//-------------------------------------------------------------------------------
// Copyright (c) 2017 by Robert <PERSON>. All rights reserved.
// This file is property of Robert <PERSON>. Any unauthorized copy, use or
// distribution is an offensive act against international law and may be
// prosecuted under federal law. Its content is company confidential.
//-------------------------------------------------------------------------------

#include "cc/assets/trajectory/inc/MainLogic.h"
#include "cc/assets/trajectory/inc/ActionPoint.h"
#include "cc/assets/trajectory/inc/WheelTrack.h"
#include "cc/assets/trajectory/inc/TrailerAssistLine.h"
#include "cc/assets/fisheyeassets/inc/FisheyeTrajectories.h"
#include "cc/assets/trajectory/inc/TrajectoryAssets.h"
#include "vfc/core/vfc_types.hpp"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomUltrasonic.h"
#include "cc/assets/trajectory/inc/Helper.h"
#include "cc/assets/trajectory/src/MainLogicCfg.h" // PRQA S 1060


#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"

#include "cc/imgui/inc/imgui_manager.h"

#include <vector>
#include <iomanip>

using pc::util::logging::g_AppContext;


namespace cc
{
namespace assets
{
namespace trajectory
{

static void initTrajectoryParams(assets::trajectory::TrajectoryParams_st & f_trajParams,
                                       assets::trajectory::DIDescriptor_st & f_DIDescriptor, const bool f_enableDIs)
{
  const cc::assets::trajectory::TrajectoryCodingParams& l_trajCodingParams = cc::assets::trajectory::g_trajCodingParams.data();

  f_trajParams.OutermostLine_Width        = l_trajCodingParams.m_outermostLine_Width;
  f_trajParams.OutermostLine_Color_Manual = l_trajCodingParams.m_outermostLine_Color_Manual;
  f_trajParams.OutermostLine_Color_Auto   = l_trajCodingParams.m_outermostLine_Color_Auto;
  f_trajParams.OutermostLine_Colorful_Color_1 = l_trajCodingParams.m_outermostLine_Colorful_Color_1;
  f_trajParams.OutermostLine_Colorful_Color_2 = l_trajCodingParams.m_outermostLine_Colorful_Color_2;
  f_trajParams.OutermostLine_Colorful_Color_3 = l_trajCodingParams.m_outermostLine_Colorful_Color_3;
  f_trajParams.OL_WT_minGap               = l_trajCodingParams.m_outermostLine_OL_WT_minGap;
  f_trajParams.OutermostLineColoful_DI_MagicOffset    = l_trajCodingParams.m_outermostLineColoful_DI_MagicOffset;

  f_trajParams.WheelTrack_Width_Whole                 = l_trajCodingParams.m_wheelTrack_Width_Whole;
  f_trajParams.WheelTrack_Width_BorderLine            = l_trajCodingParams.m_wheelTrack_Width_BorderLine;
  f_trajParams.WheelTrack_Color_Manual_Inside         = l_trajCodingParams.m_wheelTrack_Color_Manual_Inside;
  f_trajParams.WheelTrack_Color_Manual_BorderLine     = l_trajCodingParams.m_wheelTrack_Color_Manual_BorderLine;
  f_trajParams.WheelTrack_Color_Auto_Close_Inside     = l_trajCodingParams.m_wheelTrack_Color_Auto_Close_Inside;
  f_trajParams.WheelTrack_Color_Auto_Close_BorderLine = l_trajCodingParams.m_wheelTrack_Color_Auto_Close_BorderLine;
  f_trajParams.WheelTrack_Color_Auto_Far_Inside       = l_trajCodingParams.m_wheelTrack_Color_Auto_Far_Inside;
  f_trajParams.WheelTrack_Color_Auto_Far_BorderLine   = l_trajCodingParams.m_wheelTrack_Color_Auto_Far_BorderLine;

  f_trajParams.ParkingTraj_Width_Whole                = l_trajCodingParams.m_parkingTraj_Width_Whole;
  f_trajParams.ParkingTraj_Width_Shadow               = l_trajCodingParams.m_parkingTraj_Width_Shadow;
  f_trajParams.ParkingTraj_Width_BorderLine           = l_trajCodingParams.m_parkingTraj_Width_BorderLine;
  f_trajParams.ParkingTraj_Color_Inside               = l_trajCodingParams.m_parkingTraj_Color_Inside;
  f_trajParams.ParkingTraj_Color_BorderLine           = l_trajCodingParams.m_parkingTraj_Color_BorderLine;

  f_trajParams.ActionPoint_Length = l_trajCodingParams.m_actionPoint_Length;
  f_trajParams.ActionPoint_Color  = l_trajCodingParams.m_actionPoint_Color;

  f_trajParams.THTraj_Width  = l_trajCodingParams.m_THTraj_Width;
  f_trajParams.THTraj_Length = l_trajCodingParams.m_THTraj_Length;
  f_trajParams.THTraj_Color  = l_trajCodingParams.m_THTraj_Color;
  f_trajParams.THTrajBorder_Color  = l_trajCodingParams.m_THTrajBorder_Color;
  f_trajParams.THTraj_ColorGradientPosRatio  = l_trajCodingParams.m_THTraj_ColorGradientPosRatio;


  f_trajParams.DL1_Width        = l_trajCodingParams.m_DL1_Width;
  f_trajParams.DL1_Offset_Front = l_trajCodingParams.m_DL1_Offset_Front;
  f_trajParams.DL1_Offset_Rear  = l_trajCodingParams.m_DL1_Offset_Rear;
  f_trajParams.DL1_Color        = l_trajCodingParams.m_DL1_Color;

  f_trajParams.Length             = l_trajCodingParams.m_length;        // The length of the normal trajectory lines (not TH, it is separate).
  f_trajParams.GradientWidth      = l_trajCodingParams.m_gradientWidth; // Gradient width of all trajectory lines.
  f_trajParams.RenderOffset_Front = l_trajCodingParams.m_renderOffset_Front;   // Offset that will be added at the rendering stage so the actionPoints nearPosition is touching the distanceLine
  f_trajParams.RenderOffset_Rear  = l_trajCodingParams.m_renderOffset_Rear;

  f_trajParams.AnimDurRemainingDistance = l_trajCodingParams.m_animDurRemainingDistance;

  f_DIDescriptor.DILength = l_trajCodingParams.m_DIs.m_DIlength;
  if (f_enableDIs)
  {
    f_DIDescriptor.DIs.clear();
    f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI1_Pos, l_trajCodingParams.m_DIs.m_DI1_Thickness));
    f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI2_Pos, l_trajCodingParams.m_DIs.m_DI2_Thickness));
    f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI3_Pos, l_trajCodingParams.m_DIs.m_DI3_Thickness));
    //f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI4_Pos, l_trajCodingParams.m_DIs.m_DI4_Thickness));
  }
}

static void initOutermostLineColofulParams(assets::trajectory::DIDescriptor_st & f_DIDescriptor)
{
  const cc::assets::trajectory::TrajectoryCodingParams& l_trajCodingParams = cc::assets::trajectory::g_trajCodingParams.data();

  f_DIDescriptor.DILength = l_trajCodingParams.m_DIs.m_DIlength;

  f_DIDescriptor.DIs.clear();
  f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI1_Pos, l_trajCodingParams.m_DIs.m_DI1_Thickness));
  f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI2_Pos, l_trajCodingParams.m_DIs.m_DI2_Thickness));
  f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI3_Pos, l_trajCodingParams.m_DIs.m_DI3_Thickness));
  //f_DIDescriptor.DIs.push_back(cc::assets::trajectory::DI_st(l_trajCodingParams.m_DIs.m_DI4_Pos, l_trajCodingParams.m_DIs.m_DI4_Thickness));

}

pc::util::coding::Item<TrajectoryCodingParams> g_trajCodingParams("Trajectory");
pc::util::coding::Item<VehicleOutline>         g_vehicleOutline("VehicleOutline");

const std::string TrajectoryCodingParams::asString() const
{
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "Trajectory:"
                           << "\n    m_DL1_Offset_Front:           " << m_DL1_Offset_Front
                           << "\n    m_DL1_Offset_Rear:            " << m_DL1_Offset_Rear
                           << "\n    m_renderOffset_Front:         " << m_renderOffset_Front
                           << "\n    m_renderOffset_Rear:          " << m_renderOffset_Rear
                           << "\n    m_outermostLine_OL_WT_minGap: " << m_outermostLine_OL_WT_minGap
                           << std::endl;  // PRQA S 3803
    return l_stream.str();
}

const std::string VehicleOutline::asString() const
{
    std::ostringstream l_stream;
    l_stream << std::fixed << std::setprecision(3) << "VehicleOutline:"
                           << "\n    m_pointCount:                 " << m_pointCount
                           << "\n    m_frontBumperStartPointIndex: " << m_frontBumperStartPointIndex
                           << "\n    m_frontBumperEndPointIndex:   " << m_frontBumperEndPointIndex
                           << "\n    m_mirrorPointIndex:           " << m_mirrorPointIndex
                           << "\n    m_rearBumperStartPointIndex:  " << m_rearBumperStartPointIndex
                           << "\n    m_rearBumperEndPointIndex:    " << m_rearBumperEndPointIndex;  // PRQA S 3803

    l_stream << "\n    m_Points:";  // PRQA S 3803
    for (vfc::uint32_t i = 0u; i < m_pointCount; ++i)
    {
      l_stream << "\n      pt" << i << " -> " << "x: " << getPointPtr(i)->x() << ", y: " << getPointPtr(i)->y();  // PRQA S 3803
    }
    l_stream << std::endl;  // PRQA S 3803
    return l_stream.str();
}


namespace mainlogic
{


MainLogic::MainLogic(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_lastUpdate{0u}
  , m_modelData{}
  , m_trajectoryState{SHOW}
  , m_inputs{}
  , m_pre_Gear{GEAR_INIT}
  , m_actionPointNearPosLeft{0.f}
  , m_actionPointNearPosRight{0.f}
  , m_calibSeqNumber{std::numeric_limits<vfc::uint16_t>::max()}
{
  readInputs_Static();
  readInputs_Dynamic();  // PRQA S 3804
  calculateTrajectoryModelData();
}


MainLogic::~MainLogic() = default;


const ModelData_st MainLogic::getModelDataRef() const
{
  return m_modelData;
}


const Inputs_st MainLogic::getInputDataRef() const
{
  return m_inputs;
}


void MainLogic::operator() (osg::Node* f_node, osg::NodeVisitor* f_nv)  // PRQA S 6043
{
  if ((f_node == nullptr) || (f_nv == nullptr))
  {
      return;
  }
  if (m_lastUpdate != f_nv->getTraversalNumber())
  {
    // Update the Main Logic only once per frame
    update();
    m_lastUpdate = f_nv->getTraversalNumber();
  }
  // Call the Subasset's update function
  cc::assets::trajectory::GeneralTrajectoryLine* const l_trajectoryNode =
    dynamic_cast<cc::assets::trajectory::GeneralTrajectoryLine*> (f_node); // PRQA S 3077  // PRQA S 3400
  if (l_trajectoryNode != nullptr)
  {

    // if (m_trajectoryState == SHOWING_TRAILER_ASSIST_LINE )
    // {

    //     if (m_modelData.m_modifiedCount != l_trajectoryNode->m_lastUpdate)
    //     {
    //       l_trajectoryNode->generateVertexData();
    //       l_trajectoryNode->m_lastUpdate = m_modelData.m_modifiedCount;
    //     }

        // cc::assets::fisheyeassets::FisheyeTrailerAssistLine* l_fisheyetrailerAssistLine = dynamic_cast<cc::assets::fisheyeassets::FisheyeTrailerAssistLine*>(f_node); // PRQA S 3077
        // if (l_fisheyetrailerAssistLine != nullptr)
        // {
        //   l_fisheyetrailerAssistLine->setHide(false);
        // }
        // else
        // {
        //   // disable for other trajectory node when showing trailer assist line on the current view
        //   cc::assets::fisheyeassets::FisheyeWheelTrack* l_fisheyewheeltrack = dynamic_cast<cc::assets::fisheyeassets::FisheyeWheelTrack*>(f_node); // PRQA S 3077
        //   if (l_fisheyewheeltrack != nullptr)
        //   {
        //    l_fisheyewheeltrack->setHide(true);
        //   }

        //   cc::assets::fisheyeassets::FisheyeOutermostLine* l_fisheyeOutermostLine = dynamic_cast<cc::assets::fisheyeassets::FisheyeOutermostLine*>(f_node); // PRQA S 3077
        //   if (l_fisheyeOutermostLine != nullptr)
        //   {
        //    l_fisheyeOutermostLine->setHide(true);
        //   }

        //   cc::assets::fisheyeassets::FisheyeOutermostLineColorful* l_fisheyeOutermostLineColorful = dynamic_cast<cc::assets::fisheyeassets::FisheyeOutermostLineColorful*>(f_node); // PRQA S 3077
        //   if (l_fisheyeOutermostLineColorful != nullptr)
        //   {
        //    l_fisheyeOutermostLineColorful->setHide(true);
        //   }

        //   cc::assets::fisheyeassets::FisheyeDL1* l_fisheyeDL1 = dynamic_cast<cc::assets::fisheyeassets::FisheyeDL1*>(f_node); // PRQA S 3077
        //   if (l_fisheyeDL1 != nullptr)
        //   {
        //    l_fisheyeDL1->setHide(true);
        //   }

        //   cc::assets::fisheyeassets::FisheyeCoverPlate* l_fisheyecoverplate = dynamic_cast<cc::assets::fisheyeassets::FisheyeCoverPlate*>(f_node); // PRQA S 3077
        //   if (l_fisheyecoverplate != nullptr)
        //   {
        //    l_fisheyecoverplate->setHide(true);
        //   }

    //     }
    // }
    if(m_trajectoryState == HIDE)
    {
      l_trajectoryNode->setHide(true);
    }
    else
    {
      // cc::assets::trajectory::TrailerAssistLine* l_trailerAssistLine = dynamic_cast<cc::assets::trajectory::TrailerAssistLine*>(f_node); // PRQA S 3077
      // if (l_trailerAssistLine != nullptr)
      // {
      //   l_trailerAssistLine->setHide(true);
      // }
      // else
      // {
        l_trajectoryNode->setHide(false);


        const bool l_modelDataChanged = m_modelData.m_modifiedCount != l_trajectoryNode->m_lastUpdate;
        if (l_modelDataChanged || IMGUI_GET_CHECKBOX_BOOL("Settings", "updateTrajectory"))
        {
          l_trajectoryNode->generateVertexData();
          l_trajectoryNode->m_lastUpdate = m_modelData.m_modifiedCount;
        }
        else if (m_framework->m_cameraCalibrationReceiver.hasNewData())
        {
          l_trajectoryNode->generateVertexData();
        }
        else
        {
          // Qac
        }

        // animation of trajectories - needs to be done in MainLogic, else it is not synchronous
        cc::assets::trajectory::ActionPoint* const l_actionpoint = dynamic_cast<cc::assets::trajectory::ActionPoint*>(f_node); // PRQA S 3077  // PRQA S 3400
        if (l_actionpoint != nullptr)
        {
          //first animate and then get the new calculated ActionPointPosition
          l_actionpoint->animate();
          if(l_actionpoint->getSide() == cc::assets::trajectory::commontypes::Left_enm)
          {
            m_actionPointNearPosLeft = l_actionpoint->getActionPointNearX();
          }
          else // if(l_actionpoint->getSide() == cc::assets::trajectory::commontypes::Right_enm)
          {
            m_actionPointNearPosRight = l_actionpoint->getActionPointNearX();
          }
        }
        else
        {
          cc::assets::trajectory::WheelTrack* const l_wheeltrack = dynamic_cast<cc::assets::trajectory::WheelTrack*>(f_node); // PRQA S 3077  // PRQA S 3400
          if (l_wheeltrack != nullptr)
          {
            // first set the new ActionpointPosition and then animate
            if(l_wheeltrack->getSide() == cc::assets::trajectory::commontypes::Left_enm)
            {
              l_wheeltrack->setLastActionpointDist(m_actionPointNearPosLeft);
            }
            else // if(l_wheeltrack->getSide() == cc::assets::trajectory::commontypes::Right_enm)
            {
              l_wheeltrack->setLastActionpointDist(m_actionPointNearPosRight);
            }
            l_wheeltrack->animate();
          }
        }
      //}


    }
  }
  else
  {
    // do nothing in here
  }
  traverse(f_node, f_nv);
}


void MainLogic::update()
{
    const bool dirty = (g_trajCodingParams->getModifiedCount() != m_modifiedCount);
    if (dirty)
    {
        readInputs_Static();
        initTrajectoryParams(g_trajParams, g_DIDescriptor,false);
        initOutermostLineColofulParams(g_DIColorfulDescriptor);
        m_modifiedCount = g_trajCodingParams->getModifiedCount();
    }
    m_trajectoryState = readInputs_Dynamic();
    if ((m_trajectoryState == CHANGED) || dirty)
    {
        calculateTrajectoryModelData();
    }
}


void MainLogic::updateVehicleContour(bool f_onLeft, bool f_mirrorFolded) // PRQA S 4211
{
  osg::Vec2f l_mirrorCoord = (f_mirrorFolded) ?
      cc::core::g_vehicleContour->getFoldedMirror(0u) :
      cc::core::g_vehicleContour->getUnfoldedMirror(0u);
  if (l_mirrorCoord.y() < 0.5f)
  {
    return; // basic plausibility check
  }

  osg::Vec2Array* l_contour = nullptr;
  if (f_onLeft)
  {
    l_contour = m_inputs.External.Car.VehicleContour_Left.get();
  }
  else
  {
    l_contour = m_inputs.External.Car.VehicleContour_Right.get();
    l_mirrorCoord.y() = -l_mirrorCoord.y();
  }
  assert(nullptr != l_contour);
  (*l_contour)[m_inputs.External.Car.MirrorPointIndex] = l_mirrorCoord;
  l_contour->dirty();
}



void MainLogic::readInputs_Static()
{
  // Car parameters
  // Only for init for animation. readInputs_Dynamic() will overwrite it cyclically.
  m_inputs.External.Car.SteeringAngle_Front = 0.0f;
  // Only for init for animation. readInputs_Dynamic() will overwrite it cyclically.
  m_inputs.External.Car.SteeringAngle_Rear = 0.0f;
  m_inputs.External.Car.DrivingDirection = Backward_enm;
  m_inputs.External.Car.m_exteriorMirrorFolded_Left = false;
  m_inputs.External.Car.m_exteriorMirrorFolded_Right = false;
  m_inputs.External.Parking.AutomaticParking = false;
  m_inputs.External.Parking.ActionPointDist_Front = 1.3f;
  m_inputs.External.Parking.ActionPointDist_Rear  = 1.3f;

  m_inputs.External.Car.Wheelbase       = pc::vehicle::g_mechanicalData->m_wheelbase;
  m_inputs.External.Car.FrontTrack      = pc::vehicle::g_mechanicalData->m_trackFront;
  m_inputs.External.Car.RearTrack       = pc::vehicle::g_mechanicalData->m_trackRear;
  m_inputs.External.Car.FrontWheelWidth = pc::vehicle::g_mechanicalData->m_wheelWidthFront;
  m_inputs.External.Car.RearWheelWidth  = pc::vehicle::g_mechanicalData->m_wheelWidthRear;
  m_inputs.External.Car.FrontBumperPos  = pc::vehicle::g_mechanicalData->getBumperCenterXFront();
  m_inputs.External.Car.RearBumperPos   = pc::vehicle::g_mechanicalData->getBumperCenterXRear();
  m_inputs.External.Car.THBallPos       = pc::vehicle::g_mechanicalData->m_trailerBallPosition;

  #if 0 // S-class outline
  // Set the outline of the car for the left side
  m_inputs.External.Car.VehicleContour_Left = new osg::Vec2Array;
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 4.09577f, 0.113634f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 4.06974f, 0.359805f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 3.99958f, 0.614174f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 3.93288f, 0.766654f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 3.85980f, 0.838879f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 3.73110f, 0.907172f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 3.52102f, 0.948943f)); // 6
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 3.29575f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 2.92239f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 2.58739f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 2.25324f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 2.09731f, 1.065480f)); // 11
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 2.07903f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 1.51166f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 0.93145f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f( 0.32126f, 0.957979f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-0.31145f, 0.957979f)); // 16
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-0.87668f, 0.874479f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-1.02090f, 0.816494f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-1.11903f, 0.712417f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-1.16809f, 0.532513f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-1.18594f, 0.334767f));
  m_inputs.External.Car.VehicleContour_Left->push_back(osg::Vec2f(-1.19783f, 0.108772f));

  m_inputs.External.Car.FrontBumperStartPointIndex = 0;
  m_inputs.External.Car.FrontBumperEndPointIndex   = 6;
  m_inputs.External.Car.MirrorPointIndex           = 11;
  m_inputs.External.Car.RearBumperStartPointIndex  = 16;
  m_inputs.External.Car.RearBumperEndPointIndex    = m_inputs.External.Car.VehicleContour_Left->size() - 1;

  #else
  const pc::util::Polygon2D l_contour = cc::core::g_vehicleContour->toPolygon2D();
  const osg::Vec2f& l_exteriorMirror = cc::core::g_vehicleContour->getUnfoldedMirror(0u);


  // Build left vehicle contour
  m_inputs.External.Car.VehicleContour_Left = new osg::Vec2Array;
  m_inputs.External.Car.VehicleContour_Left->reserve((l_contour.size() / 2u) + 1u);

  bool l_pointInserted = false;
  for (auto l_itr = l_contour.begin(); l_itr != l_contour.end(); ++l_itr) // PRQA S 4687
  {
    if (l_itr->y() >= 0.0f)
    {
      // find the point where to insert the exterior mirror extrema point into the vehicle contour
      if (!l_pointInserted && (l_itr->x() <= l_exteriorMirror.x()) && (l_itr->y() <= l_exteriorMirror.y()))
      {
        m_inputs.External.Car.MirrorPointIndex = static_cast<vfc::uint32_t> (m_inputs.External.Car.VehicleContour_Left->size());
        m_inputs.External.Car.VehicleContour_Left->push_back(l_exteriorMirror);
        l_pointInserted = true;
        // pf code. #code looks fine
        XLOG_INFO(g_AppContext, "Inserted exterior mirror point ("
          << l_exteriorMirror.x() << ", " << l_exteriorMirror.y() << ") into vehicle contour");
      }
      m_inputs.External.Car.VehicleContour_Left->push_back(*l_itr);
    }
  }

  if (!l_pointInserted)
  {
    // pf code. #code looks fine
    XLOG_WARN(g_AppContext, "Could not find a valid position for inserting exterior mirror point ("
      << l_exteriorMirror.x() << ", " << l_exteriorMirror.y() << ") into vehicle contour");
    m_inputs.External.Car.MirrorPointIndex = cc::core::VehicleContour::NUM_POINTS_FRONT;
  }

  m_inputs.External.Car.FrontBumperStartPointIndex = 0u;
  m_inputs.External.Car.FrontBumperEndPointIndex   = static_cast<vfc::uint32_t>(cc::core::VehicleContour::NUM_POINTS_FRONT) - 1u;
  m_inputs.External.Car.RearBumperStartPointIndex  = static_cast<vfc::uint32_t>(cc::core::VehicleContour::NUM_POINTS_FRONT) + static_cast<vfc::uint32_t>(cc::core::VehicleContour::NUM_POINTS_SIDE);
  m_inputs.External.Car.RearBumperEndPointIndex    = static_cast<vfc::uint32_t> (m_inputs.External.Car.VehicleContour_Left->size() - 1u);
  #endif
  // Mirror the points to the right side
  m_inputs.External.Car.VehicleContour_Right = new osg::Vec2Array;
  m_inputs.External.Car.VehicleContour_Right->reserve(m_inputs.External.Car.VehicleContour_Left->size());
  for (auto l_itr = m_inputs.External.Car.VehicleContour_Left->begin(); l_itr != m_inputs.External.Car.VehicleContour_Left->end(); ++l_itr)
  {
    m_inputs.External.Car.VehicleContour_Right->push_back(osg::Vec2f(l_itr->x(), -l_itr->y()));
  }

  m_inputs.Internal.VehicleContour_Left_Rotated  = new osg::Vec2Array(static_cast<vfc::uint32_t> (m_inputs.External.Car.VehicleContour_Left->size()));
  m_inputs.Internal.VehicleContour_Right_Rotated = new osg::Vec2Array(static_cast<vfc::uint32_t> (m_inputs.External.Car.VehicleContour_Right->size()));
}


TrajectoriesState MainLogic::readInputs_Dynamic()  // PRQA S 6040  // PRQA S 6041
{
  constexpr vfc::float32_t lc_steeringAngleDiffTolerance = 0.01f;
  constexpr vfc::float32_t lc_steeringAngleDiffTolerance_Translation = 1.0f;

  bool l_dataChanged = false;

  const pc::daddy::GearDaddy* const l_gear = m_framework->m_gearReceiver.getData();
  const cc::daddy::SVSDisplayedViewmodeGroupDaddy_t* const l_viewmodeGroup = m_framework->asCustomFramework()->m_displayedViewmodeGroup_Receiver.getData();
  const cc::daddy::SVSDisplayedViewDaddy_t* const l_displayidDaddy = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
  EScreenID l_displayViewID = EScreenID::EScreenID_NO_CHANGE;
  if( l_displayidDaddy != nullptr)
  {
    l_displayViewID =  l_displayidDaddy->m_Data;
  }

  if (l_gear != nullptr)
  {
    const DrivingDirection_en l_before = m_inputs.External.Car.DrivingDirection;
    switch (l_gear->m_Data)
    {
    case pc::daddy::GEAR_D:
    {
      m_inputs.External.Car.DrivingDirection = Forward_enm;
      break;
    }
    case pc::daddy::GEAR_R:
    {
      m_inputs.External.Car.DrivingDirection = Backward_enm;
      break;
    }
    case pc::daddy::GEAR_N:
    case pc::daddy::GEAR_P:
    default:
    {
      if ( l_displayViewID == EScreenID::EScreenID_SINGLE_FRONT_NORMAL || l_displayViewID == EScreenID::EScreenID_SINGLE_FRONT_JUNCTION ||
            l_displayViewID == EScreenID::EScreenID_WHEEL_FRONT_DUAL || l_displayViewID == EScreenID::EScreenID_SINGLE_STB || l_displayViewID == EScreenID::EScreenID_FLOAT_SINGLE_FRONT)
      {
        m_inputs.External.Car.DrivingDirection = Forward_enm;
      }
      else if ( l_displayViewID == EScreenID::EScreenID_SINGLE_REAR_NORMAL_ON_ROAD ||  l_displayViewID == EScreenID::EScreenID_SINGLE_REAR_JUNCTION ||
            l_displayViewID == EScreenID::EScreenID_WHEEL_REAR_DUAL || l_displayViewID == EScreenID::EScreenID_FLOAT_SINGLE_REAR)
      {
        m_inputs.External.Car.DrivingDirection = Backward_enm;
      }
      else if ((l_viewmodeGroup->m_Data == EViewModeGroup::VIEWMODE_FRONT))
      {
          m_inputs.External.Car.DrivingDirection = Forward_enm;
      }
      else if (l_viewmodeGroup->m_Data == EViewModeGroup::VIEWMODE_REAR)
      {
          m_inputs.External.Car.DrivingDirection = Backward_enm;
      }
      else
      {
          m_inputs.External.Car.DrivingDirection = Forward_enm;
      }
      break;
    }
    }

    m_pre_Gear = static_cast<EGear>(l_gear->m_Data); // PRQA S 3013 // PRQA S 4899

    if (l_before != m_inputs.External.Car.DrivingDirection)
    {
      l_dataChanged = true;
    }
  }

  const pc::daddy::SteeringAngleDaddy* const l_steeringFront = m_framework->m_steeringAngleFrontReceiver.getData();
  if (l_steeringFront != nullptr)
  {
    const vfc::float32_t l_before = m_inputs.External.Car.SteeringAngle_Front;
    vfc::CSI::si_degree_f32_t l_value_deg = l_steeringFront->m_Data;
    m_inputs.External.Car.SteeringAngle_Front = l_value_deg.value();
    if (std::abs(m_inputs.External.Car.SteeringAngle_Front - l_before) > lc_steeringAngleDiffTolerance)
    {
      l_dataChanged = true;
    }
  }

  const pc::daddy::SteeringAngleDaddy* const l_steeringRear = m_framework->m_steeringAngleRearReceiver.getData();
  if (l_steeringRear != nullptr)
  {
    const vfc::float32_t l_before = m_inputs.External.Car.SteeringAngle_Rear;
    vfc::CSI::si_degree_f32_t l_value_deg = l_steeringRear->m_Data;
    m_inputs.External.Car.SteeringAngle_Rear = l_value_deg.value();
    if (std::abs(m_inputs.External.Car.SteeringAngle_Rear - l_before) > lc_steeringAngleDiffTolerance)
    {
      l_dataChanged = true;
    }
  }

  const pc::daddy::MirrorStateDaddy* const l_mirrorState = m_framework->m_mirrorStateReceiver.getData();
  if (l_mirrorState != nullptr)
  {
    //using namespace pc::daddy;
    const bool l_mirrorFolded_Left = (pc::daddy::MIRRORSTATE_FLAPPED == l_mirrorState->m_Data[static_cast<vfc::uint32_t>(pc::daddy::SIDEMIRROR_LEFT)]);
    if (l_mirrorFolded_Left != m_inputs.External.Car.m_exteriorMirrorFolded_Left)
    {
      m_inputs.External.Car.m_exteriorMirrorFolded_Left = l_mirrorFolded_Left;
      updateVehicleContour(true, l_mirrorFolded_Left);
      l_dataChanged = true;
    }
    const bool l_mirrorFolded_Right = (pc::daddy::MIRRORSTATE_FLAPPED == l_mirrorState->m_Data[static_cast<vfc::uint32_t>(pc::daddy::SIDEMIRROR_RIGHT)]);
    if (l_mirrorFolded_Right != m_inputs.External.Car.m_exteriorMirrorFolded_Right)
    {
      m_inputs.External.Car.m_exteriorMirrorFolded_Right = l_mirrorFolded_Right;
      updateVehicleContour(false, l_mirrorFolded_Right);
      l_dataChanged = true;
    }
  }

  cc::core::CustomFramework* const l_customFramework = m_framework->asCustomFramework();

  // PMA operating mode
  const cc::daddy::ParkStatusDaddy_t* const l_opMode = l_customFramework->m_parkHmiParkingStatusReceiver.getData();
  if (l_opMode != nullptr)
  {
    const bool l_before = m_inputs.External.Parking.AutomaticParking;
    if (cc::target::common::EPARKStatusR2L::PARK_Guidance_active == l_opMode->m_Data || cc::target::common::EPARKStatusR2L::PARK_Guidance_suspend == l_opMode->m_Data)  // MANOEUVERING
    {
      m_inputs.External.Parking.AutomaticParking = true;
    }
    else
    {
      m_inputs.External.Parking.AutomaticParking = false;
    }
    if (l_before != m_inputs.External.Parking.AutomaticParking)
    {
      l_dataChanged = true;
    }
  }

  // Action Point distances
  const cc::daddy::DistanceToStopDaddy* const l_travelDist = l_customFramework->m_PMA_TravelDistDesiredReceiver.getData();
  if (l_travelDist != nullptr)
  {
    const vfc::float32_t l_actionPointFrontBefore = m_inputs.External.Parking.ActionPointDist_Front;
    const vfc::float32_t l_actionPointRearBefore = m_inputs.External.Parking.ActionPointDist_Rear;
    m_inputs.External.Parking.ActionPointDist_Front = l_travelDist->m_Data;
    m_inputs.External.Parking.ActionPointDist_Rear  = l_travelDist->m_Data;

    // Clamp them not to go below zero
    if (m_inputs.External.Parking.ActionPointDist_Front < 0.0f)
    {
      m_inputs.External.Parking.ActionPointDist_Front = 0.0f;
    }
    if (m_inputs.External.Parking.ActionPointDist_Rear < 0.0f)
    {
      m_inputs.External.Parking.ActionPointDist_Rear = 0.0f;
    }

    if (!isEqual(l_actionPointFrontBefore, m_inputs.External.Parking.ActionPointDist_Front) ||
        !isEqual(l_actionPointRearBefore, m_inputs.External.Parking.ActionPointDist_Rear))
    {
      l_dataChanged = true;
    }
  }

  const cc::assets::trajectory::commontypes::VehicleMovementType_en l_movementTypeBefore = m_inputs.Internal.VehicleMovementType;
  const vfc::float32_t l_translationAngleBefore = m_inputs.Internal.TranslationAngle;
  const osg::Vec2f l_ackermannPointBefore = m_inputs.Internal.AckermannPoint;
  const cc::assets::trajectory::commontypes::TurningDirection_en l_turningDirectionBefore = m_inputs.Internal.TurningDirection;
  // Calculating the Ackermann point and the vehicle movement type (rotation or translation)
  if ( std::abs( m_inputs.External.Car.SteeringAngle_Front - m_inputs.External.Car.SteeringAngle_Rear)
         < lc_steeringAngleDiffTolerance_Translation )
  {
    // Straight-line movement
    m_inputs.Internal.VehicleMovementType = cc::assets::trajectory::commontypes::Translation_enm;
    // m_inputs.Internal.TranslationAngle = m_inputs.External.Car.SteeringAngle_Front;
    m_inputs.Internal.TranslationAngle = 0.0f;
  }
  else
  {
    // Rotation around the Ackermann point
    m_inputs.Internal.VehicleMovementType = cc::assets::trajectory::commontypes::Rotation_enm;
    m_inputs.Internal.AckermannPoint = calculateAckermannPoint();


    const vfc::float32_t l_steeringAngleDiff = m_inputs.External.Car.SteeringAngle_Front - m_inputs.External.Car.SteeringAngle_Rear;
    if (isPositive(l_steeringAngleDiff))
    {
      m_inputs.Internal.TurningDirection = cc::assets::trajectory::commontypes::ToLeft_enm;
    }
    else
    {
      m_inputs.Internal.TurningDirection = cc::assets::trajectory::commontypes::ToRight_enm;
    }
  }
  if ((l_movementTypeBefore != m_inputs.Internal.VehicleMovementType) ||
      (!isEqual(l_translationAngleBefore, m_inputs.Internal.TranslationAngle)) ||
      (l_ackermannPointBefore != m_inputs.Internal.AckermannPoint) ||
      (l_turningDirectionBefore != m_inputs.Internal.TurningDirection))
  {
    l_dataChanged = true;
  }


  // const cc::daddy::SVSDisplayedViewDaddy_t* l_displayid = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();

  // if ((l_displayid->m_Data == EScreenID_SINGLE_REAR_NORMAL_ON_ROAD) || (l_displayid->m_Data == EScreenID_SINGLE_REAR_JUNCTION))
  // {
  //   const cc::daddy::CustomVehicleLightsDaddy* l_vehicleState = m_framework->asCustomFramework()->m_VehicleLightsReceiver.getData();
  //   if (l_vehicleState != nullptr)
  //   {
  //     // if (l_vehicleState->m_Data.m_Trailer_Assist_Mode_S == true)
  //     if (false)
  //     {
  //       return SHOWING_TRAILER_ASSIST_LINE;
  //     }
  //   }
  // }

  if(l_dataChanged)
  {
    return CHANGED;
  }
  else
  {
    return SHOW;
  }
}


void MainLogic::calculateTrajectoryModelData()
{
  WheelCenter_st l_wheelCenters; // PRQA S 4102
  l_wheelCenters.FL.Pos = osg::Vec2f(m_inputs.External.Car.Wheelbase,  m_inputs.External.Car.FrontTrack * 0.5f);
  l_wheelCenters.FR.Pos = osg::Vec2f(m_inputs.External.Car.Wheelbase, -m_inputs.External.Car.FrontTrack * 0.5f);
  l_wheelCenters.RL.Pos = osg::Vec2f(0.0f,  m_inputs.External.Car.RearTrack * 0.5f);
  l_wheelCenters.RR.Pos = osg::Vec2f(0.0f, -m_inputs.External.Car.RearTrack * 0.5f);

  l_wheelCenters.FL.Angle = getAngleFromAckermannPoint(l_wheelCenters.FL.Pos);
  l_wheelCenters.FR.Angle = getAngleFromAckermannPoint(l_wheelCenters.FR.Pos);
  l_wheelCenters.RL.Angle = getAngleFromAckermannPoint(l_wheelCenters.RL.Pos);
  l_wheelCenters.RR.Angle = getAngleFromAckermannPoint(l_wheelCenters.RR.Pos);

  l_wheelCenters.FL.Radius = getDistanceFromAckermannPoint(l_wheelCenters.FL.Pos);
  l_wheelCenters.FR.Radius = getDistanceFromAckermannPoint(l_wheelCenters.FR.Pos);
  l_wheelCenters.RL.Radius = getDistanceFromAckermannPoint(l_wheelCenters.RL.Pos);
  l_wheelCenters.RR.Radius = getDistanceFromAckermannPoint(l_wheelCenters.RR.Pos);

  BasePoint_st l_nearTouchPoint;
  BasePoint_st l_farTouchPoint;
  BasePoint_st l_farTouchPoint_extra;
  if (Forward_enm == m_inputs.External.Car.DrivingDirection)
  {
    // Take FRONT wheel data
    m_modelData.LeftWheelCenter_InDrvDir  = l_wheelCenters.FL;
    m_modelData.RightWheelCenter_InDrvDir = l_wheelCenters.FR;
    m_modelData.LeftWheelCenter_OppToDrvDir  = l_wheelCenters.RL;
    m_modelData.RightWheelCenter_OppToDrvDir = l_wheelCenters.RR;
    m_modelData.HalfWheelWidth = m_inputs.External.Car.FrontWheelWidth * 0.5f;
    m_modelData.ForwardBackwardDirMul = 1.0f;
    // m_modelData.LongitudinalTouchPointDefault = 3.813f;
    m_modelData.LongitudinalTouchPointDefault = m_inputs.External.Car.FrontBumperPos;
    m_modelData.BumperCenterPoint.Pos = m_inputs.External.Car.VehicleContour_Left->front();
  }
  else// if (Backward_enm == m_inputs.External.Car.DrivingDirection)
  {
    assert(Backward_enm == m_inputs.External.Car.DrivingDirection);
    // Take REAR wheel data
    m_modelData.LeftWheelCenter_InDrvDir  = l_wheelCenters.RL;
    m_modelData.RightWheelCenter_InDrvDir = l_wheelCenters.RR;
    m_modelData.LeftWheelCenter_OppToDrvDir  = l_wheelCenters.FL;
    m_modelData.RightWheelCenter_OppToDrvDir = l_wheelCenters.FR;
    m_modelData.HalfWheelWidth = m_inputs.External.Car.RearWheelWidth * 0.5f;
    m_modelData.ForwardBackwardDirMul = -1.0f;
    // m_modelData.LongitudinalTouchPointDefault = -0.951f;
    m_modelData.LongitudinalTouchPointDefault = m_inputs.External.Car.RearBumperPos;
    m_modelData.BumperCenterPoint.Pos = m_inputs.External.Car.VehicleContour_Left->back();
  }

  m_modelData.BumperCenterPoint.Angle = getAngleFromAckermannPoint(m_modelData.BumperCenterPoint.Pos);
  m_modelData.BumperCenterPoint.Radius = getDistanceFromAckermannPoint(m_modelData.BumperCenterPoint.Pos);

  m_modelData.THBallCenter.Pos    = osg::Vec2f(m_inputs.External.Car.THBallPos.x(), m_inputs.External.Car.THBallPos.y());
  m_modelData.THBallCenter.Angle  = getAngleFromAckermannPoint(m_modelData.THBallCenter.Pos);
  m_modelData.THBallCenter.Radius = getDistanceFromAckermannPoint(m_modelData.THBallCenter.Pos);

  // m_modelData.TrailerAssistCenter.Pos.x() = g_trajCodingParams->m_THTraj_StartPoint.x();//(l_wheelCenters.RL.Pos.x()+l_wheelCenters.RR.Pos.x())*0.5f + g_trajCodingParams->m_THTraj_StartPoint.x();
  // m_modelData.TrailerAssistCenter.Pos.y() = g_trajCodingParams->m_THTraj_StartPoint.y();//(l_wheelCenters.RL.Pos.y()+l_wheelCenters.RR.Pos.y())*0.5f + g_trajCodingParams->m_THTraj_StartPoint.y();
  // m_modelData.TrailerAssistCenter.Angle = getAngleFromAckermannPoint(m_modelData.TrailerAssistCenter.Pos);
  // m_modelData.TrailerAssistCenter.Radius = getDistanceFromAckermannPoint(m_modelData.TrailerAssistCenter.Pos);


  if (cc::assets::trajectory::commontypes::Rotation_enm == m_inputs.Internal.VehicleMovementType)
  {
    getOutermostTouchPoints(l_nearTouchPoint.Pos, l_farTouchPoint.Pos, l_farTouchPoint_extra.Pos, l_nearTouchPoint.Radius, l_farTouchPoint.Radius, l_farTouchPoint_extra.Radius);
    l_nearTouchPoint.Angle      = getAngleFromAckermannPoint(l_nearTouchPoint.Pos);
    l_farTouchPoint.Angle       = getAngleFromAckermannPoint(l_farTouchPoint.Pos);
    l_farTouchPoint_extra.Angle = getAngleFromAckermannPoint(l_farTouchPoint_extra.Pos);

    if (cc::assets::trajectory::commontypes::ToLeft_enm == m_inputs.Internal.TurningDirection)
    {
      // If the Ackermann point is on the LEFT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
      m_modelData.LeftTouchPoint   = l_nearTouchPoint;
      m_modelData.RightTouchPoint  = l_farTouchPoint;
      m_modelData.ExtraTouchPoint  = l_farTouchPoint_extra;
      m_modelData.ToRightOffsetMul =  1.0f;
      m_modelData.ToLeftOffsetMul  = -1.0f;
      m_modelData.LeftRightDirMul  =  1.0f;
      m_modelData.ToRightOffsetMulHitch =  0.3f;
      m_modelData.ToLeftOffsetMulHitch  = -0.3f;
      m_modelData.LeftRightDirMulHitch  =  0.3f;
    }
    else
    {
      // If the Ackermann point is on the RIGHT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
      m_modelData.LeftTouchPoint   = l_farTouchPoint;
      m_modelData.RightTouchPoint  = l_nearTouchPoint;
      m_modelData.ExtraTouchPoint  = l_farTouchPoint_extra;
      m_modelData.ToRightOffsetMul = -1.0f;
      m_modelData.ToLeftOffsetMul  =  1.0f;
      m_modelData.LeftRightDirMul  = -1.0f;
      m_modelData.ToRightOffsetMulHitch = -0.3f;
      m_modelData.ToLeftOffsetMulHitch  =  0.3f;
      m_modelData.LeftRightDirMulHitch  = -0.3f;
    }

    getFrontmostTouchPointAngleInCurrentDrivingDirection(m_inputs.External.Car.DrivingDirection,
                                                         m_modelData.LongitudinalTouchPoint.Angle,
                                                         m_modelData.LongitudinalTouchPoint.Radius);
  }
  else // if (cc::assets::trajectory::commontypes::Translation_enm == m_inputs.Internal.VehicleMovementType)
  {
    // In case of the translation vehicle movement type, the trajectory frame is pre-rotated so that it will be longitudinal.
    // All calculations will be done on it in this space. Then, when calculating the vertex positions,
    // the inverse rotation is applied to get the frame back to its intended direction.

    getOutermostTouchPoints_Straight(m_modelData.LeftTouchPoint.Pos, m_modelData.RightTouchPoint.Pos);
    m_modelData.ToRightOffsetMul = -1.0f;
    m_modelData.ToLeftOffsetMul  =  1.0f;
    m_modelData.LeftRightDirMul  =  1.0f;
    m_modelData.ToRightOffsetMulHitch = -0.3f;
    m_modelData.ToLeftOffsetMulHitch  =  0.3f;
    m_modelData.LeftRightDirMulHitch  =  0.3f;
    const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(m_inputs.Internal.TranslationAngle);
    const cc::assets::trajectory::helper::RotationFunctor l_translationAngle(-l_translationAngle_Rad);
    l_translationAngle.rotate(m_modelData.LeftWheelCenter_InDrvDir.Pos);
    l_translationAngle.rotate(m_modelData.RightWheelCenter_InDrvDir.Pos);
    l_translationAngle.rotate(m_modelData.LeftWheelCenter_OppToDrvDir.Pos);
    l_translationAngle.rotate(m_modelData.RightWheelCenter_OppToDrvDir.Pos);
    //l_translationAngle.rotate(m_modelData.TrailerAssistCenter.Pos);
    l_translationAngle.rotate(m_modelData.THBallCenter.Pos);
    getFrontmostTouchPointPosInCurrentDrivingDirection(m_inputs.External.Car.DrivingDirection,
                                                       m_modelData.LongitudinalTouchPoint.Pos);
  }
  m_modelData.m_modifiedCount += 1u;
}


vfc::float32_t MainLogic::get2DPointDistance(const osg::Vec2f & f_point1, const osg::Vec2f & f_point2)  //PRQA S 1724
{
  const vfc::float32_t l_dx = f_point2.x() - f_point1.x();
  const vfc::float32_t l_dy = f_point2.y() - f_point1.y();
  return std::sqrt((l_dx * l_dx) + (l_dy * l_dy));
}


osg::Vec2f MainLogic::getPrevContourPoint(vfc::uint32_t f_currentIndex,
                                          const osg::Vec2Array* const f_currentContour,
                                          const osg::Vec2Array* const f_otherContour)
{
  if (0u < f_currentIndex)
  {
    return (*f_currentContour)[f_currentIndex - 1u];
  }
  else
  {
    return (*f_otherContour)[0u];
  }
}


osg::Vec2f MainLogic::getNextContourPoint(vfc::uint32_t f_currentIndex,
                                          const osg::Vec2Array* const f_currentContour,
                                          const osg::Vec2Array* const f_otherContour)
{
  if (f_currentIndex < (f_currentContour->size() - 1u))
  {
    return (*f_currentContour)[f_currentIndex + 1u];
  }
  else
  {
    return (*f_otherContour)[f_otherContour->size() - 1u];
  }
}


osg::Vec2f MainLogic::interpolateVehicleContourPoints(
  const osg::Vec2f& f_point,
  const vfc::float32_t f_value,
  const vfc::uint32_t f_pointIndex,
  const osg::Vec2Array* const f_currentContour,
  const osg::Vec2Array* const f_otherContour,
  const BaseOfInterpolation f_baseOfInterpolation) const
{
  osg::Vec2f l_prevPoint = getPrevContourPoint(f_pointIndex, f_currentContour, f_otherContour);
  vfc::float32_t      l_prevValueDiff = 0.0f;
  osg::Vec2f l_nextPoint = getNextContourPoint(f_pointIndex, f_currentContour, f_otherContour);
  vfc::float32_t      l_nextValueDiff = 0.0f;

  switch (f_baseOfInterpolation)
  {
  case RADIUS:
  {
    l_prevValueDiff = std::abs(f_value - getDistanceFromAckermannPoint(l_prevPoint));
    l_nextValueDiff = std::abs(f_value - getDistanceFromAckermannPoint(l_nextPoint));
    break;
  }
  case ANGLE:
  {
    l_prevValueDiff = std::abs(f_value - getAngleFromAckermannPoint(l_prevPoint));
    l_nextValueDiff = std::abs(f_value - getAngleFromAckermannPoint(l_nextPoint));
    break;
  }
  case LATERAL:
  {
    l_prevValueDiff = std::abs(f_point.y() - l_prevPoint.y());
    l_nextValueDiff = std::abs(f_point.y() - l_nextPoint.y());
    break;
  }
  case LONGITUDINAL:
  {
    l_prevValueDiff = std::abs(f_point.x() - l_prevPoint.x());
    l_nextValueDiff = std::abs(f_point.x() - l_nextPoint.x());
    break;
  }
  default:
  {// Must not happen
    return f_point;
  }
  }


  const osg::Vec2f l_prevHalfPoint = (l_prevPoint + f_point) * 0.5f;
  const vfc::float32_t      l_prevHalfPointDist = (l_prevHalfPoint - f_point).length();
  if (isZero(l_prevHalfPointDist))
  {
    return f_point; // point is matching previous point, nothing to interpolate
  }
  const osg::Vec2f l_nextHalfPoint = (l_nextPoint + f_point) * 0.5f;
  const vfc::float32_t      l_nextHalfPointDist = (l_nextHalfPoint - f_point).length();
  if (isZero(l_nextHalfPointDist))
  {
    return f_point; // point is matching next point, nothing to interpolate
  }

  vfc::float32_t l_ratioFromPrevPoint = 0.0f;
  if ( l_prevValueDiff > 0.0f )
  {
    l_ratioFromPrevPoint = l_prevValueDiff / (l_prevValueDiff + l_nextValueDiff);
  }
  // vfc::float32_t l_ratioFromPrevPoint = l_prevValueDiff / (l_prevValueDiff + l_nextValueDiff);
  const vfc::float32_t l_distFromPrevPoint = l_ratioFromPrevPoint * (l_prevHalfPointDist + l_nextHalfPointDist);

  if (l_distFromPrevPoint < l_prevHalfPointDist)
  {
    return l_prevHalfPoint + (f_point - l_prevHalfPoint) * (l_distFromPrevPoint / l_prevHalfPointDist);
  }
  else
  {
    const vfc::float32_t l_distFromPoint = l_distFromPrevPoint - l_prevHalfPointDist;
    return f_point + (l_nextHalfPoint - f_point) * (l_distFromPoint / l_nextHalfPointDist);
  }
}


void MainLogic::getOutermostTouchPoints( // PRQA S 4678
    osg::Vec2f & f_nearPoint, osg::Vec2f & f_farPoint, osg::Vec2f & f_farPoint_extra, // PRQA S 4287
    vfc::float32_t & f_nearRadius, vfc::float32_t & f_farRadius, vfc::float32_t & f_farRadius_extra) const // PRQA S 4287
{
  osg::ref_ptr<osg::Vec2Array> l_vehicleContour_Near;
  osg::ref_ptr<osg::Vec2Array> l_vehicleContour_Far;

  if (cc::assets::trajectory::commontypes::ToLeft_enm == m_inputs.Internal.TurningDirection)
  { // If the Ackermann point is on the left side
    l_vehicleContour_Near = m_inputs.External.Car.VehicleContour_Left;
    l_vehicleContour_Far  = m_inputs.External.Car.VehicleContour_Right;
  }
  else
  { // If the Ackermann point is on the right side
    l_vehicleContour_Near = m_inputs.External.Car.VehicleContour_Right;
    l_vehicleContour_Far  = m_inputs.External.Car.VehicleContour_Left;
  }


  vfc::uint32_t l_nearPointIndex = 0u;
  vfc::float32_t        l_nearPointDistance = getDistanceFromAckermannPoint((*l_vehicleContour_Near)[l_nearPointIndex]);
  // Run through the near side points of the vehicle contour and find the one having the smallest radius measured from
  // the Ackermann point.
  for (vfc::uint32_t pointIndex = 1u; pointIndex < l_vehicleContour_Near->size(); pointIndex++)
  {
    const vfc::float32_t l_currentDistance = getDistanceFromAckermannPoint((*l_vehicleContour_Near)[pointIndex]);
    if (l_currentDistance < l_nearPointDistance)
    {
      l_nearPointIndex = pointIndex;
      l_nearPointDistance = l_currentDistance;
    }
  }

  vfc::uint32_t l_farPointIndex = 0u;
  vfc::float32_t        l_farPointDistance = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[l_farPointIndex]);
  // Run through the far side points of the vehicle contour and find the one having the largest radius measured from
  // the Ackermann point.
  for (vfc::uint32_t pointIndex = 1u; pointIndex < l_vehicleContour_Far->size(); pointIndex++)
  {
    const vfc::float32_t l_currentDistance = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[pointIndex]);
    if (l_currentDistance > l_farPointDistance)
    {
      l_farPointIndex = pointIndex;
      l_farPointDistance = l_currentDistance;
    }
  }

  f_nearPoint  = (*l_vehicleContour_Near)[l_nearPointIndex];
  f_farPoint   = (*l_vehicleContour_Far )[l_farPointIndex ];
  f_nearRadius = l_nearPointDistance;
  f_farRadius  = l_farPointDistance;

  vfc::uint32_t l_farPoint_extra_index = 0u;
  vfc::float32_t        l_farPoint_extra_distance = 0.0f;
  if (0.0f < f_farPoint.x())
  {
    // If it's on the front bumper
    // Find the furthest point of the rear bumper
    l_farPoint_extra_index = m_inputs.External.Car.RearBumperStartPointIndex;
    l_farPoint_extra_distance = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[l_farPoint_extra_index]);
    // Run through the far side points of the vehicle contour and find the one having the largest radius measured from
    // the Ackermann point.
    for (vfc::uint32_t pointIndex = l_farPoint_extra_index + 1u; pointIndex <= m_inputs.External.Car.RearBumperEndPointIndex; pointIndex++)
    {
      const vfc::float32_t l_currentDistance = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[pointIndex]);
      if (l_currentDistance > l_farPoint_extra_distance)
      {
        l_farPoint_extra_index = pointIndex;
        l_farPoint_extra_distance = l_currentDistance;
      }
    }
  }
  else
  {
    // If it's on the rear bumper
    // Find the furthest point of the front bumper
    l_farPoint_extra_index = m_inputs.External.Car.FrontBumperStartPointIndex;
    l_farPoint_extra_distance = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[l_farPoint_extra_index]);
    // Run through the far side points of the vehicle contour and find the one having the largest radius measured from
    // the Ackermann point.
    for (vfc::uint32_t pointIndex = l_farPoint_extra_index + 1u; pointIndex <= m_inputs.External.Car.FrontBumperEndPointIndex; pointIndex++)
    {
      const vfc::float32_t l_currentDistance = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[pointIndex]);
      if (l_currentDistance > l_farPoint_extra_distance)
      {
        l_farPoint_extra_index = pointIndex;
        l_farPoint_extra_distance = l_currentDistance;
      }
    }
  }
  f_farPoint_extra  = (*l_vehicleContour_Far)[l_farPoint_extra_index];
  f_farRadius_extra = l_farPoint_extra_distance;

  // Add an offset to make the outermost touch points a bit more away from the car.
  // This is needed to avoid the DIs overlap the wheel tracks.
  //f_nearRadius -= md_touchPointOffset;
  //f_farRadius  += md_touchPointOffset;


  // Here comes the interpolation
  const osg::Vec2f l_nearInterpolatedPoint      = interpolateVehicleContourPoints(f_nearPoint,      f_nearRadius,      l_nearPointIndex,       l_vehicleContour_Near, l_vehicleContour_Far,  RADIUS);
  const osg::Vec2f l_farInterpolatedPoint       = interpolateVehicleContourPoints(f_farPoint,       f_farRadius,       l_farPointIndex,        l_vehicleContour_Far,  l_vehicleContour_Near, RADIUS);
  const osg::Vec2f l_farExtraInterpolatedPoint  = interpolateVehicleContourPoints(f_farPoint_extra, f_farRadius_extra, l_farPoint_extra_index, l_vehicleContour_Far,  l_vehicleContour_Near, RADIUS);


  f_nearPoint  = l_nearInterpolatedPoint;
  f_farPoint   = l_farInterpolatedPoint;
  f_nearRadius = getDistanceFromAckermannPoint(l_nearInterpolatedPoint);
  f_farRadius  = getDistanceFromAckermannPoint(l_farInterpolatedPoint);

  f_farPoint_extra  = l_farExtraInterpolatedPoint;
  f_farRadius_extra = getDistanceFromAckermannPoint(l_farExtraInterpolatedPoint);

  // Special case for the mirrors
  if (m_inputs.External.Car.MirrorPointIndex == l_nearPointIndex)
  {
    f_nearPoint  = (*l_vehicleContour_Near)[l_nearPointIndex];
    f_nearRadius = getDistanceFromAckermannPoint((*l_vehicleContour_Near)[l_nearPointIndex]);
  }
  if (m_inputs.External.Car.MirrorPointIndex == l_farPointIndex)
  {
    f_farPoint  = (*l_vehicleContour_Far)[l_farPointIndex];
    f_farRadius = getDistanceFromAckermannPoint((*l_vehicleContour_Far)[l_farPointIndex]);
  }
}


void MainLogic::getOutermostTouchPoints_Straight(osg::Vec2f & f_leftPoint, osg::Vec2f & f_rightPoint) const // PRQA S 4678 // PRQA S 4287
{
  const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(m_inputs.Internal.TranslationAngle);
  const cc::assets::trajectory::helper::RotationFunctor l_translationAngle(-l_translationAngle_Rad);
  for (vfc::uint32_t pointIndex = 0u; pointIndex < m_inputs.Internal.VehicleContour_Left_Rotated->size(); pointIndex++)
  {
    (*m_inputs.Internal.VehicleContour_Left_Rotated)[pointIndex] = (*m_inputs.External.Car.VehicleContour_Left)[pointIndex];
    l_translationAngle.rotate((*m_inputs.Internal.VehicleContour_Left_Rotated)[pointIndex]);
  }
  for (vfc::uint32_t pointIndex = 0u; pointIndex < m_inputs.Internal.VehicleContour_Right_Rotated->size(); pointIndex++)
  {
    (*m_inputs.Internal.VehicleContour_Right_Rotated)[pointIndex] = (*m_inputs.External.Car.VehicleContour_Right)[pointIndex];
    l_translationAngle.rotate((*m_inputs.Internal.VehicleContour_Right_Rotated)[pointIndex]);
  }

  vfc::uint32_t l_leftmostPointIndex = 0u;
  vfc::float32_t        l_leftmostPointYPos = (*m_inputs.Internal.VehicleContour_Left_Rotated)[l_leftmostPointIndex].y();
  for (vfc::uint32_t pointIndex = 1u; pointIndex < m_inputs.Internal.VehicleContour_Left_Rotated->size(); pointIndex++)
  {
    const vfc::float32_t l_currentYPos = (*m_inputs.Internal.VehicleContour_Left_Rotated)[pointIndex].y();
    if (l_currentYPos > l_leftmostPointYPos)
    {
      l_leftmostPointIndex = pointIndex;
      l_leftmostPointYPos = l_currentYPos;
    }
  }
  vfc::uint32_t l_rightmostPointIndex = 0u;
  vfc::float32_t        l_rightmostPointYPos = (*m_inputs.Internal.VehicleContour_Right_Rotated)[l_rightmostPointIndex].y();
  for (vfc::uint32_t pointIndex = 1u; pointIndex < m_inputs.Internal.VehicleContour_Right_Rotated->size(); pointIndex++)
  {
    const vfc::float32_t l_currentYPos = (*m_inputs.Internal.VehicleContour_Right_Rotated)[pointIndex].y();
    if (l_currentYPos < l_rightmostPointYPos)
    {
      l_rightmostPointIndex = pointIndex;
      l_rightmostPointYPos = l_currentYPos;
    }
  }

  f_leftPoint  = (*m_inputs.Internal.VehicleContour_Left_Rotated) [l_leftmostPointIndex];
  f_rightPoint = (*m_inputs.Internal.VehicleContour_Right_Rotated)[l_rightmostPointIndex];
  // Add an offset to make the outermost touch points a bit more away from the car.
  // This is needed to avoid the DIs overlap the wheel tracks.
  //f_leftPoint.y(); //  += md_touchPointOffset;
  //f_rightPoint.y(); // -= md_touchPointOffset;


  // Here comes the interpolation
  const osg::Vec2f l_leftInterpolatedPoint  = interpolateVehicleContourPoints(f_leftPoint,  0.0f, l_leftmostPointIndex,  m_inputs.Internal.VehicleContour_Left_Rotated,  m_inputs.Internal.VehicleContour_Right_Rotated, LATERAL);
  const osg::Vec2f l_rightInterpolatedPoint = interpolateVehicleContourPoints(f_rightPoint, 0.0f, l_rightmostPointIndex, m_inputs.Internal.VehicleContour_Right_Rotated, m_inputs.Internal.VehicleContour_Left_Rotated,  LATERAL);


  f_leftPoint  = l_leftInterpolatedPoint;
  f_rightPoint = l_rightInterpolatedPoint;

  // Special case for the mirrors
  if (m_inputs.External.Car.MirrorPointIndex == l_leftmostPointIndex)
  {
    f_leftPoint = (*m_inputs.Internal.VehicleContour_Left_Rotated)[l_leftmostPointIndex];
  }
  if (m_inputs.External.Car.MirrorPointIndex == l_rightmostPointIndex)
  {
    f_rightPoint = (*m_inputs.Internal.VehicleContour_Right_Rotated)[l_rightmostPointIndex];
  }
}


void MainLogic::getFrontmostTouchPointAngleInCurrentDrivingDirection(DrivingDirection_en f_drivingDir, vfc::float32_t & f_angle, vfc::float32_t & f_radius) const // PRQA S 4678 // PRQA S 4287
{
  cc::assets::trajectory::helper::Comparator *    l_comp = nullptr;
  cc::assets::trajectory::helper::A_LessThan_B    l_Front_IsLessThan_Rear;
  cc::assets::trajectory::helper::A_GreaterThan_B l_Front_IsGreaterThan_Rear;
  vfc::uint32_t    startPointIndex = 0u;
  vfc::uint32_t    endPointIndex = 0u;

  if (cc::assets::trajectory::commontypes::ToLeft_enm == m_inputs.Internal.TurningDirection)
  {
    // If the Ackermann point is on the LEFT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
    if (Forward_enm == f_drivingDir)
    {
      l_comp = &l_Front_IsGreaterThan_Rear;
      // To find the touch point on the front bumper:
      startPointIndex = m_inputs.External.Car.FrontBumperStartPointIndex;
      endPointIndex   = m_inputs.External.Car.FrontBumperEndPointIndex;
    }
    else// if (Backward_enm == f_drivingDir)
    {
      l_comp = &l_Front_IsLessThan_Rear;
      // To find the touch point on the rear bumper:
      startPointIndex = m_inputs.External.Car.RearBumperStartPointIndex;
      endPointIndex   = m_inputs.External.Car.RearBumperEndPointIndex;
    }
  }
  else
  {
    // If the Ackermann point is on the RIGHT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
    if (Forward_enm == f_drivingDir)
    {
      l_comp = &l_Front_IsLessThan_Rear;
      // To find the touch point on the front bumper:
      startPointIndex = m_inputs.External.Car.FrontBumperStartPointIndex;
      endPointIndex   = m_inputs.External.Car.FrontBumperEndPointIndex;
    }
    else// if (Backward_enm == f_drivingDir)
    {
      l_comp = &l_Front_IsGreaterThan_Rear;
      // To find the touch point on the rear bumper:
      startPointIndex = m_inputs.External.Car.RearBumperStartPointIndex;
      endPointIndex   = m_inputs.External.Car.RearBumperEndPointIndex;
    }
  }

  assert(nullptr != l_comp);

  vfc::uint32_t l_leftFrontmostPointIndex = startPointIndex;
  vfc::float32_t        l_leftFrontmostPointAngle =
      getAngleFromAckermannPoint( (*m_inputs.External.Car.VehicleContour_Left)[l_leftFrontmostPointIndex] );
  for (vfc::uint32_t pointIndex = startPointIndex + 1u; pointIndex <= endPointIndex; pointIndex++)
  {
    const vfc::float32_t l_currentAngle = getAngleFromAckermannPoint((*m_inputs.External.Car.VehicleContour_Left)[pointIndex]);
    if ((l_comp != nullptr) && l_comp->Compare(l_currentAngle, l_leftFrontmostPointAngle))
    {
      l_leftFrontmostPointIndex = pointIndex;
      l_leftFrontmostPointAngle = l_currentAngle;
    }
  }

  vfc::uint32_t l_rightFrontmostPointIndex = startPointIndex;
  vfc::float32_t        l_rightFrontmostPointAngle =
      getAngleFromAckermannPoint( (*m_inputs.External.Car.VehicleContour_Right)[l_rightFrontmostPointIndex] );
  for (vfc::uint32_t pointIndex = startPointIndex + 1u; pointIndex <= endPointIndex; pointIndex++)
  {
    const vfc::float32_t l_currentAngle = getAngleFromAckermannPoint((*m_inputs.External.Car.VehicleContour_Right)[pointIndex]);
    if ((l_comp != nullptr) && l_comp->Compare(l_currentAngle, l_rightFrontmostPointAngle))
    {
      l_rightFrontmostPointIndex = pointIndex;
      l_rightFrontmostPointAngle = l_currentAngle;
    }
  }

  osg::Vec2f l_interpolatedPoint;
  if ((l_comp != nullptr) && l_comp->Compare(l_leftFrontmostPointAngle, l_rightFrontmostPointAngle))
  {
    l_interpolatedPoint = interpolateVehicleContourPoints(
        (*m_inputs.External.Car.VehicleContour_Left)[l_leftFrontmostPointIndex],
        l_leftFrontmostPointAngle,
        l_leftFrontmostPointIndex,
        m_inputs.External.Car.VehicleContour_Left,
        m_inputs.External.Car.VehicleContour_Right,
        ANGLE);
  }
  else
  {
    l_interpolatedPoint = interpolateVehicleContourPoints(
        (*m_inputs.External.Car.VehicleContour_Right)[l_rightFrontmostPointIndex],
        l_rightFrontmostPointAngle,
        l_rightFrontmostPointIndex,
        m_inputs.External.Car.VehicleContour_Right,
        m_inputs.External.Car.VehicleContour_Left,
        ANGLE);
  }

  f_angle  = getAngleFromAckermannPoint(l_interpolatedPoint);
  f_radius = getDistanceFromAckermannPoint(l_interpolatedPoint);
}


void MainLogic::getFrontmostTouchPointPosInCurrentDrivingDirection(DrivingDirection_en f_drivingDir, osg::Vec2f & f_rotatedPos) const // PRQA S 4287
{
  cc::assets::trajectory::helper::Comparator *    l_comp = nullptr;
  cc::assets::trajectory::helper::A_LessThan_B    l_Front_IsLessThan_Rear;
  cc::assets::trajectory::helper::A_GreaterThan_B l_Front_IsGreaterThan_Rear;
  vfc::uint32_t    startPointIndex = 0u;
  vfc::uint32_t    endPointIndex = 0u;

  if (Forward_enm == f_drivingDir)
  {
    l_comp = &l_Front_IsGreaterThan_Rear;
    // To find the touch point on the front bumper:
    startPointIndex = m_inputs.External.Car.FrontBumperStartPointIndex;
    endPointIndex   = m_inputs.External.Car.FrontBumperEndPointIndex;
  }
  else// if (Backward_enm == f_drivingDir)
  {
    l_comp = &l_Front_IsLessThan_Rear;
    // To find the touch point on the rear bumper:
    startPointIndex = m_inputs.External.Car.RearBumperStartPointIndex;
    endPointIndex   = m_inputs.External.Car.RearBumperEndPointIndex;
  }

  assert(nullptr != l_comp);

  // For the left vehicle contour array:
  vfc::uint32_t l_leftFrontmostPointIndex = startPointIndex;
  vfc::float32_t        l_leftFrontmostPointPosX = (*m_inputs.Internal.VehicleContour_Left_Rotated)[l_leftFrontmostPointIndex].x();
  for (vfc::uint32_t pointIndex = startPointIndex + 1u; pointIndex <= endPointIndex; pointIndex++)
  {
    const vfc::float32_t l_currentPosX = (*m_inputs.Internal.VehicleContour_Left_Rotated)[pointIndex].x();
    if ((l_comp != nullptr) && l_comp->Compare(l_currentPosX, l_leftFrontmostPointPosX))
    {
      l_leftFrontmostPointIndex = pointIndex;
      l_leftFrontmostPointPosX = l_currentPosX;
    }
  }

  // For the right vehicle contour array:
  vfc::uint32_t l_rightFrontmostPointIndex = startPointIndex;
  vfc::float32_t        l_rightFrontmostPointPosX = (*m_inputs.Internal.VehicleContour_Right_Rotated)[l_rightFrontmostPointIndex].x();
  for (vfc::uint32_t pointIndex = startPointIndex + 1u; pointIndex <= endPointIndex; pointIndex++)
  {
    const vfc::float32_t l_currentPosX = (*m_inputs.Internal.VehicleContour_Right_Rotated)[pointIndex].x();
    if ((l_comp != nullptr) && l_comp->Compare(l_currentPosX, l_rightFrontmostPointPosX))
    {
      l_rightFrontmostPointIndex = pointIndex;
      l_rightFrontmostPointPosX = l_currentPosX;
    }
  }

  if ((l_comp != nullptr) && l_comp->Compare(l_leftFrontmostPointPosX, l_rightFrontmostPointPosX))
  {
    f_rotatedPos = interpolateVehicleContourPoints(
        (*m_inputs.Internal.VehicleContour_Left_Rotated)[l_leftFrontmostPointIndex],
        0.0f,
        l_leftFrontmostPointIndex,
        m_inputs.Internal.VehicleContour_Left_Rotated,
        m_inputs.Internal.VehicleContour_Right_Rotated, LONGITUDINAL);
  }
  else
  {
    f_rotatedPos = interpolateVehicleContourPoints(
        (*m_inputs.Internal.VehicleContour_Right_Rotated)[l_rightFrontmostPointIndex],
        0.0f,
        l_rightFrontmostPointIndex,
        m_inputs.Internal.VehicleContour_Right_Rotated,
        m_inputs.Internal.VehicleContour_Left_Rotated, LONGITUDINAL);
  }
}


osg::Vec2f MainLogic::calculateAckermannPoint() const
{
  const vfc::float32_t l_steeringAngle_Front_Rad = osg::DegreesToRadians(m_inputs.External.Car.SteeringAngle_Front);
  const vfc::float32_t l_steeringAngle_Rear_Rad  = osg::DegreesToRadians(m_inputs.External.Car.SteeringAngle_Rear);
  const vfc::float32_t l_tanFront = std::tan(l_steeringAngle_Front_Rad);
  const vfc::float32_t l_tanRear  = std::tan(l_steeringAngle_Rear_Rad);

  // Ackermann position in Dyn70k coordinate system:
  const vfc::float32_t l_Ack_y = m_inputs.External.Car.Wheelbase / (l_tanFront - l_tanRear);
  const vfc::float32_t l_Ack_x = -l_tanRear * l_Ack_y;
  return osg::Vec2f(l_Ack_x, l_Ack_y);
}


// Returns true if the Ackermann point is inside the vehicle contour, otherwise returns false.
// For a car, truck or tractor it is not possible that the Ackermann point goes under the vehicle, so this function should not be called.

// Although, for some vehicles, e.g. forklifts, due to extreme wheel angles the Ackermann point can go under the vehicle (inside its contour).
// For such vehicles it is necessary to call this function and avoid showing the outermost line with the smaller radius (which would be under the vehicle).
bool MainLogic::ackermannPointIsInsideTheVehicleContour() const
{
  AABB_2D_st l_AABB;
  getVehicleAABB_2D(l_AABB);

  // This is a quick (rough) check, just to know if the Ackermann point is within the vehicle AABB (Axis Aligned Bounding Box).
  // For forklifts this could be enough. If not, a precise polygon-point query must be implemented.

  if (    (l_AABB.minX < m_inputs.Internal.AckermannPoint.x())
       && (l_AABB.maxX > m_inputs.Internal.AckermannPoint.x())
       && (l_AABB.minY < m_inputs.Internal.AckermannPoint.y())
       && (l_AABB.maxY > m_inputs.Internal.AckermannPoint.y()) )
  {
    return true;
  }
  else
  {
    return false;
  }
}


void MainLogic::getVehicleAABB_2D(AABB_2D_st & f_AABB) const
{
  f_AABB.minX = (*m_inputs.External.Car.VehicleContour_Left)[0u].x();
  f_AABB.minY = (*m_inputs.External.Car.VehicleContour_Left)[0u].y();
  f_AABB.maxX = (*m_inputs.External.Car.VehicleContour_Left)[0u].x();
  f_AABB.maxY = (*m_inputs.External.Car.VehicleContour_Left)[0u].y();
  for (vfc::uint32_t pointIndex = 1u; pointIndex < m_inputs.External.Car.VehicleContour_Left->size(); pointIndex++)
  {
    if (f_AABB.minX > (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].x())
    {
      f_AABB.minX = (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].x();
    }
    if (f_AABB.minY > (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].y())
    {
      f_AABB.minY = (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].y();
    }
    if (f_AABB.maxX < (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].x())
    {
      f_AABB.maxX = (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].x();
    }
    if (f_AABB.maxY < (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].y())
    {
      f_AABB.maxY = (*m_inputs.External.Car.VehicleContour_Left)[pointIndex].y();
    }
  }
  for (vfc::uint32_t pointIndex = 0u; pointIndex < m_inputs.External.Car.VehicleContour_Right->size(); pointIndex++)
  {
    if (f_AABB.minX > (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].x())
    {
      f_AABB.minX = (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].x();
    }
    if (f_AABB.minY > (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].y())
    {
      f_AABB.minY = (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].y();
    }
    if (f_AABB.maxX < (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].x())
    {
      f_AABB.maxX = (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].x();
    }
    if (f_AABB.maxY < (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].y())
    {
      f_AABB.maxY = (*m_inputs.External.Car.VehicleContour_Right)[pointIndex].y();
    }
  }
}


// Returned value is in radians.
// 0 angle: If Dyn70k.Y == 0 And Dyn70k.X > 0.
// - angle: If Dyn70k.Y < 0.
// + angle: If Dyn70k.Y > 0.
vfc::float32_t MainLogic::getAngleFromAckermannPoint(osg::Vec2f f_point) const
{
  // Transform the point so that the Ackermann point will become the origin.
  f_point -= m_inputs.Internal.AckermannPoint;
  // atan2f(y, x);
  return std::atan2(f_point.y(), f_point.x());
}


vfc::float32_t MainLogic::getDistanceFromAckermannPoint(osg::Vec2f f_point) const
{
  // Transform the point so that the Ackermann point will become the origin.
  f_point -= m_inputs.Internal.AckermannPoint;
  return std::sqrt((f_point.x() * f_point.x()) + (f_point.y() * f_point.y()));
}


// Returns true, if there was/were intersection(s), and puts the y coordinate
// of the intersection point which is closer to the car, into "f_out_y".
// Returns false, if there was no intersection.
bool MainLogic::getYFromTurningCircleEquation(vfc::float32_t f_in_radius, vfc::float32_t f_in_x, vfc::float32_t & f_out_y) const // PRQA S 4678 // PRQA S 4287
{
  bool l_intersectionPointFound = false;
  // Translate the point to have the Ackermann point as the origin.
  f_in_x -= m_inputs.Internal.AckermannPoint.x();
  const vfc::float32_t l_ySquare = (f_in_radius * f_in_radius) - (f_in_x * f_in_x);

  if (l_ySquare < 0.0f)
  {
    // No intersection.
    // It is false already.
    // l_intersectionPointFound = false;
  }
  else
  {
    vfc::float32_t l_y = std::sqrt(l_ySquare);

    if (0.0f < m_inputs.Internal.AckermannPoint.y())
    {
      // If the Ackermann point is on the LEFT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
      // Make y negative, to choose the intersection point which is closer to the car.
      l_y = -l_y;
    }
    else
    {
      // If the Ackermann point is on the RIGHT side. (Top view, Dyn70k.X up, Dyn70k.Y left)
      // Leave y positive. This gives us the closest point.
    }

    // Convert back to car Dyn70k.
    l_y += m_inputs.Internal.AckermannPoint.y();
    f_out_y = l_y;

    l_intersectionPointFound = true;
  }

  return l_intersectionPointFound;
}


// Returns true, if there was an intersection, and puts the y coordinate
// of the intersection point into "f_out_y".
// Returns false, if there was no intersection.
bool MainLogic::getYFromLineEquation(vfc::float32_t f_in_offset, vfc::float32_t f_in_x, vfc::float32_t & f_out_y) const // PRQA S 4678 // PRQA S 4287
{
  bool l_intersectionPointFound = false;

  Vector2D_st l_vector; // PRQA S 4102
  l_vector.startPos  = osg::Vec2f(0.0f, f_in_offset);
  l_vector.direction = osg::Vec2f(1.0f, 0.0f);

  if ( std::abs(m_inputs.Internal.TranslationAngle) < 90.0f )
  {
    const vfc::float32_t l_translationAngle_Rad = osg::DegreesToRadians(m_inputs.Internal.TranslationAngle);
    const cc::assets::trajectory::helper::RotationFunctor l_translationAngle(l_translationAngle_Rad);
    l_translationAngle.rotate(l_vector.startPos);
    l_translationAngle.rotate(l_vector.direction);

    osg::Vec2f l_vecFromLateralLine;
    l_vecFromLateralLine.x() = l_vector.startPos.x() - f_in_x;
    l_vecFromLateralLine.y() = (l_vecFromLateralLine.x() / l_vector.direction.x()) * l_vector.direction.y();
    osg::Vec2f l_intersectionPoint = l_vector.startPos - l_vecFromLateralLine;

    f_out_y = l_intersectionPoint.y();
    l_intersectionPointFound = true;
  }

  return l_intersectionPointFound;
}


} // namespace mainlogic
} // namespace trajectory
} // namespace assets
} // namespace cc
