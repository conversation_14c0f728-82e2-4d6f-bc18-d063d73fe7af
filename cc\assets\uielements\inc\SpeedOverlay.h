//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: TANG Chencheng (XC-DX/EPF2)
//  Department: XC-DX/EPF2
//=============================================================================
/// @swcomponent SVS DENSA
/// @file  Speedoverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_SPEED_OVERLAY_H
#define CC_ASSETS_SPEED_OVERLAY_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/views/engineeringview/inc/EngineeringView.h"
#include "pc/svs/util/osgx/inc/MemberWrapper.h"

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace uielements
{    


//======================================================
// SpeedOverlaySettings
//------------------------------------------------------
/// Setting class for Speedoverlay
/// <AUTHOR>
//======================================================
class SpeedOverlaySettings : public pc::util::coding::ISerializable
{
public:

  SpeedOverlaySettings()
    : m_offset_Speed(osg::Vec3f(10.0f, 10.0f, 0.0f))
    , m_fontType("cc/resources/Roboto-Regular.ttf")
  {
  }

  SERIALIZABLE(SpeedOverlaySettings)
  {
    ADD_MEMBER(osg::Vec3f, offset_Speed);
    ADD_STRING_MEMBER(fontType);
  }
  osg::Vec3f m_offset_Speed;
  std::string  m_fontType;

};

extern pc::util::coding::Item<SpeedOverlaySettings> g_displaySettings;


class SpeedOverlayUpdateCallback: public osg::NodeCallback
{

public:
  SpeedOverlayUpdateCallback(
    osg::ref_ptr<osg::Geode> f_SpeedDisGeode, 
    pc::core::Framework* f_pFramework);

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;  
  void updateSpeed(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);

protected:
  virtual ~SpeedOverlayUpdateCallback();

private:
  //! Copy constructor is not permitted.
  SpeedOverlayUpdateCallback (const SpeedOverlayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  SpeedOverlayUpdateCallback& operator=(const SpeedOverlayUpdateCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_SpeedDisGeode;
  pc::core::Framework* m_pFramework;
};


//!
//! SpeedOverlay
//!
class SpeedOverlay : public osg::MatrixTransform
{
public:
    SpeedOverlay(pc::core::Framework* f_framework);
    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:
    virtual void init();
    virtual ~SpeedOverlay();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_SpeedDisGeode;

private:
    //! Copy constructor is not permitted.
    SpeedOverlay (const SpeedOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    SpeedOverlay& operator=(const SpeedOverlay& other); // = delete
};


} // namespace uielements
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_SPEED_OVERLAY_H
