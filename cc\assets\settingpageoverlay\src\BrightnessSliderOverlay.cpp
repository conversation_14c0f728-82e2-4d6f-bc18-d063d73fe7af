#include "cc/assets/settingpageoverlay/inc/BrightnessSliderOverlay.h"

#include "cc/core/inc/CustomScene.h"
#include "cc/daddy/inc/CustomDaddyTypes.h"
#include "cc/util/pdmwriter/inc/PdmWriter.h"
namespace cc
{
namespace assets
{
namespace settingpageoverlay
{

pc::util::coding::Item<BrightnessSliderOverlaySettings> g_brightnessSliderOverlaySettings("BrightnessSliderOverlaySetting");

BrigtnessSliderBackground::BrigtnessSliderBackground(
    cc::core::AssetId    f_assetId,
    pc::core::Framework* f_framework,
    const BrightnessSliderBackgroundSettings* f_setting,
    osg::Camera*         f_referenceView)
    : cc::assets::button::Button{f_assetId, f_referenceView}
    , m_framework{f_framework}
    , m_setting{f_setting}
{
    setState(AVAILABLE);
    setName("BrigtnessSliderBackground");
    if (f_referenceView != nullptr)
    {
        setHoriReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
        setVertReferenceView(static_cast<pc::core::View*>(f_referenceView)); // PRQA S 3076
    }
    setPositionHori(m_setting->m_brightnessSliderBackgroundPos);
}

BrigtnessSliderBackground::~BrigtnessSliderBackground() = default;

void BrigtnessSliderBackground::onAvailable()
{
    setIconEnable(true);
    const cc::target::common::EThemeTypeDayNight dayNightTheme = getDayNightTheme();
    setTexturePath(
        (dayNightTheme == cc::target::common::EThemeTypeDayNight::ETHEHE_TYPE_DAYNIGHT_DAY)
            ? m_setting->m_brightnessSliderDayBackgroundTexturePath
            : m_setting->m_brightnessSliderNightBackgroundTexturePath);
}

void BrigtnessSliderBackground::update()
{
    GET_PORT_DATA(dayNightThemeContainer, m_framework->asCustomFramework()->m_dayNightThemeDaddy_Receiver, dayNightThemePortHaveData)




    if (dayNightThemePortHaveData)
    {
        setDayNightTheme(dayNightThemeContainer->m_Data);
    }

    setState(AVAILABLE);
}

BrightnessSliderOverlay::BrightnessSliderOverlay(
    cc::core::AssetId       f_assetId,
    pc::core::Framework*    f_framework,
    osg::Camera*            f_referenceView)
    : cc::assets::button::ButtonGroup{f_assetId}
    , m_framework{f_framework}
    , m_isBrightnessSliderEnabled{false}
{
    addButton(new BrigtnessSliderBackground(f_assetId, f_framework, &(g_brightnessSliderOverlaySettings->m_brightnessSliderBackground), f_referenceView));
    osg::Group::addChild(new button::Slider(m_framework, f_referenceView)); // PRQA S 3803
    const auto l_slider = new button::Slider(m_framework, f_referenceView);
    const auto l_sliderIcon = new button::SliderIcon{&(g_brightnessSliderOverlaySettings->m_brightnessSliderSetting), m_framework};
    l_slider->addSlider(l_sliderIcon);
    osg::Group::addChild(l_slider); // PRQA S 3803
    addButton(new BrigtnessSliderBackground(f_assetId, f_framework, &(g_brightnessSliderOverlaySettings->m_brightnessSliderLeftTopCorner), f_referenceView));
    addButton(new BrigtnessSliderBackground(f_assetId, f_framework, &(g_brightnessSliderOverlaySettings->m_brightnessSliderLeftBottomCorner), f_referenceView));
    addButton(new BrigtnessSliderBackground(f_assetId, f_framework, &(g_brightnessSliderOverlaySettings->m_brightnessSliderRightTopCorner), f_referenceView));
    addButton(new BrigtnessSliderBackground(f_assetId, f_framework, &(g_brightnessSliderOverlaySettings->m_brightnessSliderRightBottomCorner), f_referenceView));
    addButton(new BrigtnessSliderBackground(f_assetId, f_framework, &(g_brightnessSliderOverlaySettings->m_brightnessSun), f_referenceView));
}

BrightnessSliderOverlay::~BrightnessSliderOverlay() = default;

void BrightnessSliderOverlay::update()
{
    m_enabled = m_isBrightnessSliderEnabled;
}

} // namespace settingpageoverlay
} // namespace assets
} // namespace cc
