//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  swinfooverlay.h
/// @brief
//=============================================================================

#ifndef CC_ASSETS_SWINFO_OVERLAY_H
#define CC_ASSETS_SWINFO_OVERLAY_H

#include "cc/daddy/inc/CustomDaddyTypes.h"

#include <osg/Group>
#include <osg/MatrixTransform>

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h"
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/osgx/inc/Quantization.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "pc/svs/views/engineeringview/inc/EngineeringView.h"

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>


namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc
namespace cc
{
namespace assets
{
namespace swinfooverlay
{

//======================================================
// SWInfoOverlaySettings
//------------------------------------------------------
/// Setting class for swinfooverlay
/// <AUTHOR>
//======================================================
class SWInfoOverlaySettings : public pc::util::coding::ISerializable
{
public:

  SWInfoOverlaySettings()
    : m_offset_SwVersion(osg::Vec3f(1.4f, 0.0f, 0.0f))
    , m_offset_HwVersion(osg::Vec3f(1.5f, 0.0f, 0.0f))
    , m_fontType("cc/resources/Roboto-Regular.ttf")
  {
  }

  SERIALIZABLE(SWInfoOverlaySettings)
  {
    ADD_MEMBER(osg::Vec3f, offset_SwVersion);
    ADD_MEMBER(osg::Vec3f, offset_HwVersion);
    ADD_STRING_MEMBER(fontType);
  }
  osg::Vec3f m_offset_SwVersion;
  osg::Vec3f m_offset_HwVersion;
  std::string  m_fontType;

};

extern pc::util::coding::Item<SWInfoOverlaySettings> g_displaySettings;

class SwInfoOverlayUpdateCallback: public osg::NodeCallback
{

public:
  SwInfoOverlayUpdateCallback(
                                osg::ref_ptr<osg::Geode> f_SwVersionDisGeode, 
                                osg::ref_ptr<osg::Geode> f_HwVersionDisGeode, 
                                pc::core::Framework* f_pFramework
                                );

  virtual void operator()(osg::Node* f_node, osg::NodeVisitor* f_nv) override;
  
  void updateSwVersion(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);
  void updateHwVersion(osg::NodeVisitor& f_nv, osg::ref_ptr<osg::Geode> f_Geode);

protected:
  virtual ~SwInfoOverlayUpdateCallback();

private:

  //! Copy constructor is not permitted.
  SwInfoOverlayUpdateCallback (const SwInfoOverlayUpdateCallback& other); // = delete
  //! Copy assignment operator is not permitted.
  SwInfoOverlayUpdateCallback& operator=(const SwInfoOverlayUpdateCallback& other); // = delete

  osg::ref_ptr<osg::Geode> m_SwVersionDisGeode;
  osg::ref_ptr<osg::Geode> m_HwVersionDisGeode;


  pc::core::Framework* m_pFramework;
};

//!
//! DigitalDistanceOverlay
//!
class SwInfoOverlay : public osg::MatrixTransform
{
public:

    SwInfoOverlay(pc::core::Framework* f_framework);

    virtual void traverse(osg::NodeVisitor& f_nv) override;

protected:

    virtual void init();

    virtual ~SwInfoOverlay();

    pc::core::Framework* m_framework;
    unsigned int m_settingsModifiedCount;
    osg::ref_ptr<osg::Geode> m_SwVersionDisGeode;
    osg::ref_ptr<osg::Geode> m_HwVersionDisGeode;

private:
    //! Copy constructor is not permitted.
    SwInfoOverlay (const SwInfoOverlay& other); // = delete
    //! Copy assignment operator is not permitted.
    SwInfoOverlay& operator=(const SwInfoOverlay& other); // = delete

};


} // namespace swinfooverlay
} // namespace assets
} // namespace cc

#endif // CC_ASSETS_SWINFO_OVERLAY_H
