//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: BYD RPA
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: CA82LR Raphael Cano (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS BYD
/// @file  RemainingMoveNumber.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/RemainingMoveNumber.h"

#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/core/inc/Framework.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/util/math/inc/FloatComp.h"
#include "pc/svs/util/osgx/inc/Quantization.h" // PRQA S 1060
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "cc/core/inc/CustomScene.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/assets/tileoverlay/inc/TileSettings.h"
#include "cc/target/common/inc/linux_svs_interface.hpp"

#include <osg/Depth>
#include <osg/Geode>
#include <osg/Geometry>
#include <osg/MatrixTransform>
#include <osg/Texture2D>
#include <osgDB/ReadFile>

static uint16_t MAX_PARK_GUIDANCE_MOVES_LEFT_NUMBER = 9u; // PRQA S 2300

using pc::util::logging::g_AppContext;
namespace cc
{
namespace assets
{
namespace uielements
{

static pc::util::coding::Item<RemainingMoveNumberSettings> g_RemainingMoveNumberSettings("RemainingMoveNumber");

//!
//! RemainingMoveNumber
//!
RemainingMoveNumber::RemainingMoveNumber(pc::core::Framework* f_framework)
  : m_framework{f_framework}
  , m_settingsModifiedCount{~0u}
  , m_DynamicDisGeode{}
{
  setName("RemainingMoveNumber");
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() +1u);
}


RemainingMoveNumber::~RemainingMoveNumber() = default;




void RemainingMoveNumber::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    if (g_RemainingMoveNumberSettings->getModifiedCount() != m_settingsModifiedCount)
    {
      init();
      addCullCallback(new RemainingMoveNumberCallback(m_DynamicDisGeode, m_framework));
      m_settingsModifiedCount = g_RemainingMoveNumberSettings->getModifiedCount();
    }
  }
  osg::Group::traverse(f_nv);
}


void RemainingMoveNumber::init()
{
  removeChildren(0u, getNumChildren());    // PRQA S 3803

  m_DynamicDisGeode = new osg::Geode;
  addChild(m_DynamicDisGeode);    // PRQA S 3803
  {

  }


  osg::StateSet* const l_stateSet = getOrCreateStateSet();
  l_stateSet->setMode(GL_DEPTH_TEST, osg::StateAttribute::OFF); // PRQA S 3143
  pc::core::TextureShaderProgramDescriptor l_basicTexShader("colorText");
  l_basicTexShader.apply(l_stateSet);  // PRQA S 3803
}

RemainingMoveNumberCallback::RemainingMoveNumberCallback(
                                                          osg::ref_ptr<osg::Geode> f_DynamicDisGeode,
                                                          pc::core::Framework* f_pFramework
                                                          )
  : m_DynamicDisGeode{f_DynamicDisGeode}
  , isUIPanel{false}
  , m_pFramework{f_pFramework}
{

}

RemainingMoveNumberCallback::~RemainingMoveNumberCallback() = default;



void RemainingMoveNumberCallback::updateDistance(osg::NodeVisitor& /*f_nv*/, osg::ref_ptr<osg::Geode> f_Geode) // PRQA S 4283
{
  uint16_t l_MovesLeftNumber = MAX_PARK_GUIDANCE_MOVES_LEFT_NUMBER;
  cc::target::common::EParkngTypeSeld l_curParkngTypeSeld = cc::target::common::EParkngTypeSeld::PARKING_NONE;
  bool l_dynamicGearStatus = false;
  bool l_dynamicGearMainViewStatus = false;

  if (m_pFramework->asCustomFramework()->m_parkGuideMovesLeftReceiver.hasData())
  {
    const cc::daddy::ParkGuideMovesLeftDaddy_t* const l_pMovesLeftNumberDaddy_t = m_pFramework->asCustomFramework()->m_parkGuideMovesLeftReceiver.getData();
    if (l_pMovesLeftNumberDaddy_t != nullptr)
    {
      l_MovesLeftNumber = static_cast<vfc::uint8_t>(std::abs(l_pMovesLeftNumberDaddy_t->m_Data));
    }
  }

  if (m_pFramework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver.hasData())
  {
    const cc::daddy::ParkParkngTypeSeld_t* const l_parkParkngTypeSeld = m_pFramework->asCustomFramework()->m_parkHmiParkParkngTypeSeldReceiver.getData();
    l_curParkngTypeSeld = l_parkParkngTypeSeld->m_Data;
  }

  if (m_pFramework->asCustomFramework()->m_dynamicGearStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearStatusPtr = m_pFramework->asCustomFramework()->m_dynamicGearStatus_ReceiverPort.getData();
    l_dynamicGearStatus = l_dynamicGearStatusPtr->m_Data;
  }

  if (m_pFramework->asCustomFramework()->m_dynamicGearMainViewStatus_ReceiverPort.hasData())
  {
    const cc::daddy::DynamicGearActive_t* const l_dynamicGearMainViewStatusPtr = m_pFramework->asCustomFramework()->m_dynamicGearMainViewStatus_ReceiverPort.getData();
    l_dynamicGearMainViewStatus = l_dynamicGearMainViewStatusPtr->m_Data;
  }

  const osg::ref_ptr<osgText::Text> l_RemainingMoveNumber = new osgText::Text;
  if (isUIPanel == true)
  {
    l_RemainingMoveNumber->setPosition(g_RemainingMoveNumberSettings->m_position_UIPanel);
  }
  else
  {
    l_RemainingMoveNumber->setPosition(g_RemainingMoveNumberSettings->m_position_MainView);
  }
  l_RemainingMoveNumber->setFont(g_RemainingMoveNumberSettings->m_fontType);
  l_RemainingMoveNumber->setColor(g_RemainingMoveNumberSettings->m_color);
  l_RemainingMoveNumber->setDrawMode(osgText::TextBase::TEXT); // PRQA S 3143
  l_RemainingMoveNumber->setCharacterSize(g_RemainingMoveNumberSettings->m_charSize);
  l_RemainingMoveNumber->setCharacterSizeMode(osgText::TextBase::SCREEN_COORDS);
  l_RemainingMoveNumber->setAxisAlignment(osgText::Text::XY_PLANE);
  l_RemainingMoveNumber->setRotation(osg::Quat(osg::DegreesToRadians(-90.0f), osg::Z_AXIS));
  l_RemainingMoveNumber->setAlignment(osgText::Text::CENTER_CENTER);

  std::ostringstream l_textString;
  if (l_MovesLeftNumber > MAX_PARK_GUIDANCE_MOVES_LEFT_NUMBER)
  {
    l_MovesLeftNumber = MAX_PARK_GUIDANCE_MOVES_LEFT_NUMBER;
  }

  l_textString << std::fixed << std::setprecision(0) << l_MovesLeftNumber << std::endl; //PRQA S 3803

  // only show remaining move number at park in
  if ( (isUIPanel && l_dynamicGearStatus && (cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld))
    || (!isUIPanel && l_dynamicGearMainViewStatus && (cc::target::common::EParkngTypeSeld::PARKING_IN == l_curParkngTypeSeld)) )
  {
    f_Geode->setNodeMask(~0u);
    l_RemainingMoveNumber->setText(l_textString.str());
    f_Geode->removeDrawables(0u, 1u);  // PRQA S 3803
    f_Geode->addDrawable(l_RemainingMoveNumber); // PRQA S 3803
  }
  else
  {
    f_Geode->setNodeMask(0u);
  }

}



void RemainingMoveNumberCallback::operator()(osg::Node* f_node, osg::NodeVisitor* f_nv)
{
  RemainingMoveNumber* const l_remainingMoveNumber = dynamic_cast<RemainingMoveNumber*>(f_node);  // PRQA S 3400
  osgUtil::CullVisitor* const l_cv = dynamic_cast<osgUtil::CullVisitor*> (f_nv);  // PRQA S 3400

  if (l_remainingMoveNumber != nullptr)
  {
    if (nullptr !=l_cv)
    {
      osg::Camera* const l_camera = l_cv->getCurrentCamera();


      if ((l_camera->getViewport()->width()  == cc::core::g_views->m_mainViewport.m_size.x()) && // PRQA S 3270 // PRQA S 3011
          (l_camera->getViewport()->height() == cc::core::g_views->m_mainViewport.m_size.y()) ) // PRQA S 3270 // PRQA S 3011
      {
        isUIPanel = false;
        updateDistance(*f_nv, m_DynamicDisGeode);
      }
      else
      {
        isUIPanel = true;
        updateDistance(*f_nv, m_DynamicDisGeode);
      }
    }
  }

  traverse(f_node, f_nv);

}

} // namespace uielements
} // namespace assets
} // namespace cc
