//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: ESJ1LR Esparza Jose (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  Vehicle2D.cpp
/// @brief
//=============================================================================

#include "cc/assets/common/inc/Vehicle2D.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h"
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include "pc/svs/assets/vehiclemodel/inc/DoorAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/RenderBinSetter.h"
#include "pc/svs/assets/vehiclemodel/inc/WheelAnimation.h"
#include "pc/svs/assets/vehiclemodel/inc/VehicleModel.h"
#include "pc/svs/assets/vehiclemodel/inc/Utils.h"
#include "cc/core/inc/CustomMechanicalData.h"
#include "vfc/core/vfc_types.hpp"
#include "pc/svs/util/cli/inc/BaseCommands.h"
#include "pc/generic/util/cli/inc/CommandRegistry.h" // PRQA S 1060
#include "pc/generic/util/cli/inc/CommandLineInterface.h"

#include "cc/core/inc/CustomScene.h"
#include "cc/assets/uielements/inc/VehicleTransIcon.h"

#include "osg/Uniform"

using pc::util::logging::g_EngineContext;
using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace common
{


//======================================================
// CustomComponentNames2D
//------------------------------------------------------
/// Custom Component Names as coding params
/// Creates dynamic component nodes for the
/// vehicle model.
/// <AUTHOR> Jose (CC-DA/EAV3)
/// @ingroup  vehicle
//======================================================
class CustomComponentNames2D : public pc::util::coding::ISerializable
{
public:

    CustomComponentNames2D()
    //! dark parts
      //! body
      : m_BodyImpostorDark ("BodyImpostorDark") // PRQA S 4052

      //! doors
      , m_FrontLeftDoorImpostorDark  ("FrontLeftDoorImpostorDark")
      , m_FrontRightDoorImpostorDark ("FrontRightDoorImpostorDark")
      , m_RearLeftDoorImpostorDark   ("RearLeftDoorImpostorDark")
      , m_RearRightDoorImpostorDark  ("RearRightDoorImpostorDark")

      //! roof, mirror, full
      , m_RoofImpostorDark     ("RoofImpostorDark")
      , m_LeftSideMirrorImpostorDark ("LeftSideMirrorImpostorDark")
      , m_RightSideMirrorImpostorDark("RightSideMirrorImpostorDark")
      , m_FullCarImpostorDark  ("FullCarImpostorDark")

      //! wheels
      , m_FrontLeftWheelImpostorDark  ("FrontLeftWheelImpostorDark")
      , m_FrontRightWheelImpostorDark ("FrontRightWheelImpostorDark")
      , m_RearLeftWheelImpostorDark   ("RearLeftWheelImpostorDark")
      , m_RearRightWheelImpostorDark  ("RearRightWheelImpostorDark")

      //! trunk
      , m_TrunkImpostorDark    ("TrunkImpostorDark")

    {
    }

    SERIALIZABLE(CustomComponentNames2D) // PRQA S 3401
    {
        if (f_descriptor == nullptr)
        {
          return;
        }
        //! body
        ADD_STRING_MEMBER(BodyImpostorDark            ); //body without wheels,doors and trunk. Dark version

        //! doors
        ADD_STRING_MEMBER(FrontLeftDoorImpostorDark   ); //left door. Dark version
        ADD_STRING_MEMBER(FrontRightDoorImpostorDark  ); //right door. Dark version
        ADD_STRING_MEMBER(RearLeftDoorImpostorDark    ); //left door
        ADD_STRING_MEMBER(RearRightDoorImpostorDark   ); //right door

        //! roof, mirror, full
        ADD_STRING_MEMBER(RoofImpostorDark            ); //roof. Dark version
        ADD_STRING_MEMBER(LeftSideMirrorImpostorDark  ); //left side mirror. Dark version
        ADD_STRING_MEMBER(RightSideMirrorImpostorDark ); //right side mirror. Dark version
        ADD_STRING_MEMBER(FullCarImpostorDark         ); //Full car without wheels. Dark version

        //! wheels
        ADD_STRING_MEMBER(FrontLeftWheelImpostorDark  ); //front left wheel. Dark version
        ADD_STRING_MEMBER(FrontRightWheelImpostorDark ); //front right wheel. Dark version
        ADD_STRING_MEMBER(RearLeftWheelImpostorDark   ); //rear left wheel. Dark version
        ADD_STRING_MEMBER(RearRightWheelImpostorDark  ); //rear right wheel. Dark version

        //! trunk
        ADD_STRING_MEMBER(TrunkImpostorDark                ); //trunk. Dark version
    }

    std::string getName(CustomVehicleModel2D::CustomComponent2D f_id) const  // PRQA S 6040
    {
        switch (f_id)
        {
            //! body
            case CustomVehicleModel2D::BODY_IMPOSTOR_DARK:
              {return m_BodyImpostorDark;}

            //! doors
            case CustomVehicleModel2D::FRONT_LEFT_DOOR_IMPOSTOR_DARK:
              {return m_FrontLeftDoorImpostorDark;}
            case CustomVehicleModel2D::FRONT_RIGHT_DOOR_IMPOSTOR_DARK:
              {return m_FrontRightDoorImpostorDark;}
            case CustomVehicleModel2D::REAR_LEFT_DOOR_IMPOSTOR_DARK:
              {return m_RearLeftDoorImpostorDark;}
            case CustomVehicleModel2D::REAR_RIGHT_DOOR_IMPOSTOR_DARK:
              {return m_RearRightDoorImpostorDark;}

            //! roof, mirror, full
            case CustomVehicleModel2D::ROOF_IMPOSTOR_DARK:
              {return m_RoofImpostorDark;}
            case CustomVehicleModel2D::FRONT_LEFT_DOOR_MIRROR_IMPOSTOR_DARK:
              {return m_LeftSideMirrorImpostorDark;}
            case CustomVehicleModel2D::FRONT_RIGHT_DOOR_MIRROR_IMPOSTOR_DARK:
              {return m_RightSideMirrorImpostorDark;}
            case CustomVehicleModel2D::FULL_CAR_IMPOSTOR_DARK:
              {return m_FullCarImpostorDark;}

            //! wheels
            case CustomVehicleModel2D::FRONT_LEFT_WHEEL_IMPOSTOR_DARK:
              {return m_FrontLeftWheelImpostorDark;}
            case CustomVehicleModel2D::FRONT_RIGHT_WHEEL_IMPOSTOR_DARK:
              {return m_FrontRightWheelImpostorDark;}
            case CustomVehicleModel2D::REAR_LEFT_WHEEL_IMPOSTOR_DARK:
              {return m_RearLeftWheelImpostorDark;}
            case CustomVehicleModel2D::REAR_RIGHT_WHEEL_IMPOSTOR_DARK:
              {return m_RearRightWheelImpostorDark;}

            //! trunk
            case CustomVehicleModel2D::TRUNK_IMPOSTOR_DARK:
              {return m_TrunkImpostorDark;}

            default:
                {return std::string("");}
        }
    }

    //! body
    std::string m_BodyImpostorDark              ; //body without wheels,doors and trunk. Dark version

    //! doors
    std::string m_FrontLeftDoorImpostorDark     ; //left door. Dark version
    std::string m_FrontRightDoorImpostorDark    ; //right door. Dark version
    std::string m_RearLeftDoorImpostorDark      ; //left door rear. Dark version
    std::string m_RearRightDoorImpostorDark     ; //right door rear. Dark version

    //! roof, mirror, full
    std::string m_RoofImpostorDark              ; //roof. Dark version
    std::string m_LeftSideMirrorImpostorDark    ; //left side mirror. Dark version
    std::string m_RightSideMirrorImpostorDark   ; //right side mirror. Dark version
    std::string m_FullCarImpostorDark           ; //Full car without wheels. Dark version

    //! wheels
    std::string m_FrontLeftWheelImpostorDark    ; //front left wheel. Dark version
    std::string m_FrontRightWheelImpostorDark   ; //front right wheel. Dark version
    std::string m_RearLeftWheelImpostorDark     ; //rear left wheel. Dark version
    std::string m_RearRightWheelImpostorDark    ; //rear right wheel. Dark version

    //! trunk
    std::string m_TrunkImpostorDark                  ; //trunk. Dark version
};


//!
//! CustomVehicleModel2DSettings
//!
class CustomVehicleModel2DSettings : public pc::util::coding::ISerializable
{
public:

    CustomVehicleModel2DSettings()
      : m_modelFilePath("cc/vehicle_model/vehicle2D.osg") // PRQA S 4052
      , m_customComponentNames2D{}
    {
    }

    SERIALIZABLE(CustomVehicleModel2DSettings) // PRQA S 3401
    {
      if (f_descriptor == nullptr)
      {
        return;
      }
      ADD_STRING_MEMBER(modelFilePath);
      ADD_MEMBER(CustomComponentNames2D, customComponentNames2D);
    }

    std::string m_modelFilePath;
    CustomComponentNames2D m_customComponentNames2D;
};

pc::util::coding::Item<CustomVehicleModel2DSettings> g_model2d("CustomVehicleModel2D");


//!
//! class CustomVehicleModel2D
//!
CustomVehicleModel2D::CustomVehicleModel2D(const std::string& f_fileName, pc::core::Framework* f_framework)
    : pc::vehiclemodel::VehicleModel{f_fileName, f_framework}
    , m_customComponents2D{}
{
}

CustomVehicleModel2D::CustomVehicleModel2D(const CustomVehicleModel2D& f_other, const osg::CopyOp& f_copyOp)
    : pc::vehiclemodel::VehicleModel{f_other, f_copyOp}
    , m_customComponents2D{f_other.m_customComponents2D}
{
}

void CustomVehicleModel2D::reset()
{
  m_customComponents2D.fill(nullptr);
  pc::vehiclemodel::VehicleModel::reset();
}

osg::Node* CustomVehicleModel2D::getComponent(CustomComponent2D f_id, bool f_suppressErrorMessage)
{
    if (!m_customComponents2D[static_cast<vfc::uint32_t>(f_id)].valid())
    {
      pc::util::osgx::NodeFinder l_nodeFinder(g_model2d->m_customComponentNames2D.getName(f_id));
      accept(l_nodeFinder);
      if (nullptr != l_nodeFinder.getFoundNode())
      {
        m_customComponents2D[static_cast<vfc::uint32_t>(f_id)] = l_nodeFinder.getFoundNode();
      }
      else if (!f_suppressErrorMessage)
      {
        // pf code. #code looks fine
        XLOG_ERROR(g_AppContext,"CustomVehicleModel2D::getComponent( " << l_nodeFinder.getNodeName()
              << ") could not be found in 2D vehicle model scene graph");
      }
      else
      {
        //Do nothing
      }
    }
    else
    {

    }
    return m_customComponents2D[static_cast<vfc::uint32_t>(f_id)].get();
}

class TextureVisitor : public osg::NodeVisitor
{
public:

  TextureVisitor() = default;




  void apply(osg::Node& f_node) override // PRQA S 2120
  {
    addStateSet(f_node.getStateSet());
    traverse(f_node);
  }

  void apply(osg::Geode& f_geode) override
  {
    const vfc::uint32_t l_numDrawables = f_geode.getNumDrawables();
    for (vfc::uint32_t i = 0u; i < l_numDrawables; ++i)
    {
      osg::Drawable* const l_drawable = f_geode.getDrawable(i);
      addStateSet(l_drawable->getStateSet());
    }
  }

  void addStateSet(osg::StateSet* f_stateSet)
  {
    if (nullptr != f_stateSet)
    {
      m_stateSets.insert(f_stateSet);    // PRQA S 3803
    }
  }

  void replaceTexture(osg::Texture2D* f_texture)
  {
    for (const auto l_stateSet : m_stateSets)
    {
      const auto l_currentTexture = l_stateSet->getTextureAttribute(0u, osg::StateAttribute::TEXTURE);
      if (nullptr != l_currentTexture)
      {
        // l_stateSet->setTextureAttribute(0, f_texture, osg::StateAttribute::OVERRIDE);
        l_stateSet->setTextureAttribute(0u, f_texture);
      }
    }
  }
private:

  std::set<osg::StateSet*> m_stateSets;
};


// !
// ! ColorUpdateCallback
// !
class ColorUpdateCallback : public osg::NodeCallback // PRQA S 2119
{
public:

    ColorUpdateCallback(pc::core::Framework* f_framework) // PRQA S 2203
    : m_pFramework{f_framework}
    , m_colorIndex{0u}
    {
    }

    void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override  // PRQA S 6044
    {
        if ((f_node == nullptr) || (f_nv == nullptr))
        {
            return;
        }
        vfc::uint32_t l_colorIndex = m_colorIndex;
        if (m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.isConnected())
        {
            const cc::daddy::ColorIndexDaddy* const l_pData = m_pFramework->asCustomFramework()->m_vehColorSts_ReceiverPort.getData();
            if (nullptr != l_pData)
            {
                l_colorIndex = static_cast<vfc::uint32_t>(l_pData->m_Data);
            }
        }

        if (l_colorIndex != m_colorIndex)
        {
            // osg::StateSet* l_stateSet = f_node->getOrCreateStateSet();    // PRQA S 3803
            osg::ref_ptr<osg::Image> l_image;
            switch (l_colorIndex)
            {
            case cc::daddy::TIME_GRAY:
            case cc::daddy::MOUNTAIN_ASH:  //! gray
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkGray.png");
              break;
              }
            case cc::daddy::SILVERSAND_BLACK:
            case cc::daddy::XUANKONG_BLACK:  //! black
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkBlack.png");
              break;
            }
            case cc::daddy::RED_EMPEROR:  //! red
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkRed.png");
              break;
            }
            case cc::daddy::WISDOM_BLUE:  //! blue
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkBlue.png");
              break;
              }
            case cc::daddy::SNOWY_WHITE:  //! white
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkWhite.png");
              break;
            }
            case cc::daddy::QIANSHAN_CUI:  //! green
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkGreen.png");
              break;
            }
            default:  //! blue in default
            {
              l_image = osgDB::readImageFile("cc/vehicle_model/impostorDarkBlue.png");
              break;
            }
            }
            if (l_image.valid())
            {
                const osg::ref_ptr<osg::Texture2D> l_texture = new osg::Texture2D(l_image.get());
                l_texture->setResizeNonPowerOfTwoHint(false);
                l_texture->setUnRefImageDataAfterApply(true);
                TextureVisitor l_textureVisitor;
                f_node->accept(l_textureVisitor);
                l_textureVisitor.replaceTexture(l_texture.get());
            }
            m_colorIndex = l_colorIndex;
        }

        traverse(f_node, f_nv);
    }

protected:

    ~ColorUpdateCallback() override = default;

private:

    pc::core::Framework* m_pFramework;
    vfc::uint32_t         m_colorIndex;

    // Private copy constructor and assignment operator
    ColorUpdateCallback(const ColorUpdateCallback& f_other);
    ColorUpdateCallback& operator=(const ColorUpdateCallback& f_other); // PRQA S 2051
};



//!
//! TextureFinalizer
//!
class TextureFinalizer : public pc::vehiclemodel::IFinalizer // PRQA S 2119
{
public:

    explicit TextureFinalizer(pc::core::Framework* f_pFramework)
    : m_pFramework{f_pFramework}
    {
    }

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override    // PRQA S 6041
    {
      f_vehicleModel->addUpdateCallback(new ColorUpdateCallback(m_pFramework));
    }

protected:

    ~TextureFinalizer() override = default;
    //! Copy constructor is not permitted.
    TextureFinalizer (const TextureFinalizer& other) = delete;
    //! Copy assignment operator is not permitted.
    TextureFinalizer& operator=(const TextureFinalizer& other) = delete;

    pc::core::Framework* m_pFramework;
};


//!
//! Vehicle2DNodeMaskFinalizer
//!
class Vehicle2DNodeMaskFinalizer : public pc::vehiclemodel::IFinalizer // PRQA S 2113 // PRQA S 2119
{
public:

  explicit Vehicle2DNodeMaskFinalizer(pc::core::Framework* f_pFramework)
      : m_pFramework{f_pFramework}
    {

    }

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override    // PRQA S 6041
  {
    if (f_vehicleModel == nullptr)
    {
        return;
    }
    CustomVehicleModel2D* const l_pVehicle2D = static_cast<CustomVehicleModel2D*>(f_vehicleModel); // PRQA S 3076
    osg::Node* l_node = nullptr;

    // set all node cull setting to default
    constexpr vfc::uint32_t l_otherComponentsNodeMask = 0xffffffff & ~( static_cast<vfc::uint32_t>(CustomVehicleModel2D::NIGHT_ALL_NODES) );

    // Leave the check of the rear doors if there are less then 4 or 5 doors
    // const bool l_isOptional = ((cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_4DOOR) && (cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_5DOOR));

    pc::util::osgx::NodeMaskSetter l_nodeMaskParser(l_otherComponentsNodeMask);
    f_vehicleModel->accept(l_nodeMaskParser);

    // Name does not exactly match "Scene Root", since vehicle variant is included -> use node name finder
    pc::util::osgx::NodeNameFinder l_nodeFinder("Scene Root");
    f_vehicleModel->accept(l_nodeFinder);
    l_node = l_nodeFinder.getFoundNode();
    if (l_node != nullptr)
    {
      l_node->setNodeMask(0xffffffu);
    }
    f_vehicleModel->setNodeMask(0xffffffu);

    // Modify the cull mask for the important parts
    // Wheels masks
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::FRONT_LEFT_WHEEL_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_WHEELS_IMPOSTOR_MASK);
    }

    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::REAR_LEFT_WHEEL_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_WHEELS_IMPOSTOR_MASK);
    }

    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::FRONT_RIGHT_WHEEL_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_WHEELS_IMPOSTOR_MASK);
    }

    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::REAR_RIGHT_WHEEL_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_WHEELS_IMPOSTOR_MASK);
    }

    // trunk
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::TRUNK_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_TRUNK_IMPOSTOR_MASK);
    }

    //doors masks
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::FRONT_LEFT_DOOR_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_DOORS_IMPOSTOR_MASK);
    }

    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::FRONT_RIGHT_DOOR_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_DOORS_IMPOSTOR_MASK);
    }

    // Load the rear doors if the car has 4 or 5 doors
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::REAR_LEFT_DOOR_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_DOORS_IMPOSTOR_MASK);
    }

    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::REAR_RIGHT_DOOR_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_DOORS_IMPOSTOR_MASK);
    }

    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::TRUNK_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_TRUNK_IMPOSTOR_MASK);
    }

    //! body without wheels,doors and trunk
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::BODY_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_BODY_IMPOSTOR_MASK);
    }

    //! roof
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::ROOF_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_ROOF_IMPOSTOR_MASK);
    }

    //! Full car without wheels
    l_node = l_pVehicle2D->getComponent(CustomVehicleModel2D::FULL_CAR_IMPOSTOR_DARK);
    if (l_node != nullptr)
    {
      l_node->setNodeMask(CustomVehicleModel2D::NIGHT_FULL_CAR_IMPOSTOR_MASK);
    }

  }

private:
  pc::core::Framework* m_pFramework;

};

//!
//! Wheel2DAnimationCallback
//!
class Wheel2DAnimationCallback : public osg::NodeCallback // PRQA S 2111 // PRQA S 2119
{
public:

    Wheel2DAnimationCallback( const osg::Matrix& f_modelMatrix,
                             pc::core::Framework* f_framework,
                             bool f_isFrontWheel)
          : m_modelMatrix{f_modelMatrix}
          , m_framework{f_framework}
          , m_isFrontWheel{f_isFrontWheel}
    {
    }

    void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override
    {

      osg::MatrixTransform* const l_matrixTransform = dynamic_cast<osg::MatrixTransform*> (f_node); // PRQA S 3077  // PRQA S 3400

      vfc::float32_t l_steeringAngleFront = 0.0f;
      vfc::float32_t l_steeringAngleRear = 0.0f;
      //! read steering angle front
      const pc::daddy::SteeringAngleDaddy* const l_steeringAngleFrontDaddy = m_framework->m_steeringAngleFrontReceiver.getData();
      if (nullptr != l_steeringAngleFrontDaddy)
      {
          vfc::CSI::si_radian_f32_t l_angleInRadians = l_steeringAngleFrontDaddy->m_Data ; //automatic conversion by VFC
          l_steeringAngleFront = l_angleInRadians.value();
      }
      //! read steering angle rear
      const pc::daddy::SteeringAngleDaddy* const l_steeringAngleRearDaddy = m_framework->m_steeringAngleRearReceiver.getData();
      if (nullptr != l_steeringAngleRearDaddy)
      {
          vfc::CSI::si_radian_f32_t l_angleInRadians = l_steeringAngleRearDaddy->m_Data ; //automatic conversion by VFC
          l_steeringAngleRear = l_angleInRadians.value();
      }

      osg::Matrix m_frontWheelMatrix = osg::Matrix::identity();
      osg::Matrix m_rearWheelMatrix = osg::Matrix::identity();

      if (std::abs(l_steeringAngleFront) > 1e-4f)
      {
          m_frontWheelMatrix.makeRotate(l_steeringAngleFront, osg::Z_AXIS);
      }

      if (std::abs(l_steeringAngleRear) > 1e-4f)
      {
          m_rearWheelMatrix.makeRotate(l_steeringAngleRear, osg::Z_AXIS);
      }


      if (m_isFrontWheel)
      {
          l_matrixTransform->setMatrix(m_frontWheelMatrix * m_modelMatrix);
      }
      else
      {
          l_matrixTransform->setMatrix(m_rearWheelMatrix * m_modelMatrix);
      }

      traverse(f_node, f_nv);
    }

protected:
    ~Wheel2DAnimationCallback() override = default;
    //! Copy constructor is not permitted.
    Wheel2DAnimationCallback (const Wheel2DAnimationCallback& other) /* = delete */;
    //! Copy assignment operator is not permitted.
    Wheel2DAnimationCallback& operator=(const Wheel2DAnimationCallback& other) /* = delete */; // PRQA S 2051


private:
    osg::Matrix m_modelMatrix;
    pc::core::Framework* m_framework;
    bool m_isFrontWheel;
};

//!
//! Wheel2DAnimationFinalizer
//!
class Wheel2DAnimationFinalizer : public pc::vehiclemodel::WheelAnimationFinalizer // PRQA S 2119
{
public:

    Wheel2DAnimationFinalizer() = default;

    void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
    {
        CustomVehicleModel2D* const l_vehicle2D = dynamic_cast<CustomVehicleModel2D*>(f_vehicleModel); // PRQA S 3077  // PRQA S 3400

        if (l_vehicle2D == nullptr)
        {
           return;
        }

        // Day components
        {
          constexpr CustomVehicleModel2D::CustomComponent2D l_wheelIds[4] = {
              CustomVehicleModel2D::FRONT_LEFT_WHEEL_IMPOSTOR_DARK,
              CustomVehicleModel2D::FRONT_RIGHT_WHEEL_IMPOSTOR_DARK,
              CustomVehicleModel2D::REAR_LEFT_WHEEL_IMPOSTOR_DARK,
              CustomVehicleModel2D::REAR_RIGHT_WHEEL_IMPOSTOR_DARK
          };

          for (vfc::uint32_t i = 0u; i < 4u; ++i)
          {
              osg::Node* const l_wheelNode = l_vehicle2D->getComponent(l_wheelIds[i]);
              if (l_wheelNode != nullptr)
              {
                  osg::MatrixTransform* l_transformNode = dynamic_cast<osg::MatrixTransform*> (l_wheelNode); // PRQA S 3077  // PRQA S 3400
                  if (l_transformNode == nullptr)
                  {
                      l_transformNode = pc::util::osgx::convertToMatrixTransform(l_wheelNode->asGroup());
                      assert(nullptr != l_transformNode);
                  }
                  Wheel2DAnimationCallback* const l_animationCallback = new Wheel2DAnimationCallback(l_transformNode->getMatrix(),
                      f_vehicleModel->getFramework(),
                      i < 2u ? true : false);
                  l_transformNode->addUpdateCallback(l_animationCallback);
              }
          }
        }
    }

protected:

    ~Wheel2DAnimationFinalizer() override = default;
    //! Copy constructor is not permitted.
    Wheel2DAnimationFinalizer (const Wheel2DAnimationFinalizer& other) /* = delete */;
    //! Copy assignment operator is not permitted.
    Wheel2DAnimationFinalizer& operator=(const Wheel2DAnimationFinalizer& other) /* = delete */; // PRQA S 2051
};

//!
//! WheelTransparencyCallback
//!
class WheelTransparencyCallback : public osg::Uniform::Callback // PRQA S 2111
{
public:

    WheelTransparencyCallback( pc::core::Framework* f_framework ) // PRQA S 2203
       : m_framework{ f_framework }
    {
    }

    void operator () (osg::Uniform* f_uniform, osg::NodeVisitor* /* f_nv */) override
    {
        if (f_uniform == nullptr)
        {
            return;
        }
        // set wheel transparency value
        const cc::daddy::GbcWheelTransparency_t* l_pGbcWheelTransp = nullptr;
        if (m_framework->asCustomFramework()->m_GbcWheelTransparency_ReceiverPort.isConnected())
        {
            l_pGbcWheelTransp = m_framework->asCustomFramework()->m_GbcWheelTransparency_ReceiverPort.getData();
            if (l_pGbcWheelTransp != nullptr)
            {
                f_uniform->set(l_pGbcWheelTransp->m_Data);    // PRQA S 3803
            }
        }
    }

protected:
    ~WheelTransparencyCallback() override = default;
    //! Copy constructor is not permitted.
    WheelTransparencyCallback (const WheelTransparencyCallback& other) /* = delete */;
    //! Copy assignment operator is not permitted.
    WheelTransparencyCallback& operator=(const WheelTransparencyCallback& other) /* = delete */; // PRQA S 2051

private:
    pc::core::Framework* m_framework;
};

//!
//! VehicleTransparencyCallback
//!
class VehicleTransparencyCallback : public osg::Uniform::Callback // PRQA S 2111
{
public:

    VehicleTransparencyCallback( pc::core::Framework* f_framework ) // PRQA S 2203
         : m_framework{ f_framework }
    {
    }

    void operator () (osg::Uniform* f_uniform, osg::NodeVisitor* /* f_nv */) override
    {
        if (f_uniform == nullptr)
        {
            return;
        }
        // get vehicle trans from FloorPlateSm
        vfc::float32_t l_gbc = 1.0f;
        if (m_framework->asCustomFramework()->m_GbcVehicleTransparency_ReceiverPort.hasData())
        {
          const cc::daddy::GbcVehicleTransparency_t* const l_pGbcTransp = m_framework->asCustomFramework()->m_GbcVehicleTransparency_ReceiverPort.getData();
          if (nullptr!=l_pGbcTransp)
          {
            l_gbc = l_pGbcTransp->m_Data;
          }
        }

        // set vehicle transparency value
        if ( cc::assets::uielements::g_vehicleTransIconSettings->m_isEnabled
          && allDoorsClosed() && isInTransState())// && isNormalView()) // PRQA S 3230
        {
          // set to transparancy, show vehicle trans picture
          f_uniform->set(0.0f);    // PRQA S 3803
        }
        else if(!allDoorsClosed() || !isInTransState()) // PRQA S 3230
        {
          // set to opacity, show vehicle model
          f_uniform->set(1.0f);    // PRQA S 3803
        }
        else
        {
          f_uniform->set(l_gbc);    // PRQA S 3803
        }
    }

    bool allDoorsClosed() // PRQA S 4211
    {
      const pc::daddy::DoorAnimationStateDaddy* const l_pData = m_framework->m_doorAnimationStateReceiver.getData();
      bool l_allDoorsClosed = true;

      if (nullptr!=l_pData)
      {
          if (  l_pData->m_Data.AnimationOngoingOrOpenFL          ||
                l_pData->m_Data.AnimationOngoingOrOpenFR          ||
                l_pData->m_Data.AnimationOngoingOrOpenRL          ||
                l_pData->m_Data.AnimationOngoingOrOpenRR          ||
                l_pData->m_Data.AnimationOngoingOrOpenTrunk       ||
                l_pData->m_Data.AnimationOngoingOrOpenMirrorLeft  ||
                l_pData->m_Data.AnimationOngoingOrOpenMirrorRight  )
                {
                  l_allDoorsClosed = false;
                }
      }
      return l_allDoorsClosed;
    }

    bool isInTransState()
    {
      bool l_isInTransState = false;

      const cc::daddy::SVSVehTransStsDaddy_t* const l_vehTransStatus = m_framework->asCustomFramework()->m_VehTransparenceStsFromSM_Receiver.getData();
      if ( nullptr != l_vehTransStatus )
      {
        if (1u == l_vehTransStatus->m_Data)
        {
          l_isInTransState = true;
        }
      }

      return l_isInTransState;
    }

    bool isNormalView()
    {
      bool l_isNormalView = true;
      const cc::daddy::SVSDisplayedViewDaddy_t*  const l_displayid = m_framework->asCustomFramework()->m_displayedView_ReceiverPort.getData();
      if ( nullptr != l_displayid )
      {
        if ((EScreenID_HORI_PARKING == l_displayid->m_Data) || (EScreenID_VERT_PARKING == l_displayid->m_Data))
        {
          l_isNormalView = false;
        }
      }

      return l_isNormalView;
    }

protected:

    ~VehicleTransparencyCallback() override = default;
    //! Copy constructor is not permitted.
    VehicleTransparencyCallback (const VehicleTransparencyCallback& other) /* = delete */;
    //! Copy assignment operator is not permitted.
    VehicleTransparencyCallback& operator=(const VehicleTransparencyCallback& other) /* = delete */; // PRQA S 2051

private:
    pc::core::Framework* m_framework;
};

//!
//! VehicleGbcFinalizer
//!
class VehicleGbcFinalizer : public pc::vehiclemodel::IFinalizer // PRQA S 2111 // PRQA S 2119
{

public:
  VehicleGbcFinalizer(pc::core::Framework *f_pFramework) // PRQA S 2203
    : m_pFramework{f_pFramework}
  {
  }

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
  {
    CustomVehicleModel2D* const l_vehicle2D = dynamic_cast<CustomVehicleModel2D*>(f_vehicleModel); // PRQA S 3077  // PRQA S 3400
    if (l_vehicle2D == nullptr)
    {
      return;
    }

    //! Vehicle Transparency Uniform
    osg::Uniform *const l_vehicleTransp = l_vehicle2D->getOrCreateStateSet()->getOrCreateUniform("transparent", osg::Uniform::FLOAT);
    l_vehicleTransp->set(1.f);    // PRQA S 3803
    l_vehicleTransp->setUpdateCallback( new VehicleTransparencyCallback(m_pFramework) );

    //! Additional Uniform for the wheel transparency (different transp level to the vehicle!)
    constexpr CustomVehicleModel2D::CustomComponent2D l_wheelIds[4] = {
        CustomVehicleModel2D::FRONT_LEFT_WHEEL_IMPOSTOR_DARK,
        CustomVehicleModel2D::FRONT_RIGHT_WHEEL_IMPOSTOR_DARK,
        CustomVehicleModel2D::REAR_LEFT_WHEEL_IMPOSTOR_DARK,
        CustomVehicleModel2D::REAR_RIGHT_WHEEL_IMPOSTOR_DARK
    };

    //! Use a unique StateSet with a transparency uniform
    osg::StateSet *l_wheelStateSet = nullptr;
    osg::Node* l_wheelNode = l_vehicle2D->getComponent(l_wheelIds[0]);
    if (l_wheelNode != nullptr)
    {
      l_wheelStateSet = new osg::StateSet(*l_wheelNode->getOrCreateStateSet());
      osg::Uniform *const l_unif = l_wheelStateSet->getOrCreateUniform("transparent", osg::Uniform::FLOAT);
      l_unif->set(1.f);    // PRQA S 3803
      l_unif->setUpdateCallback(new WheelTransparencyCallback(m_pFramework));
    }

    //! Share stateset with all wheel nodes
    for (vfc::uint32_t i = 0u; i < 4u; ++i)
    {
      l_wheelNode = l_vehicle2D->getComponent(l_wheelIds[i]);
      if ((l_wheelNode != nullptr) && (l_wheelStateSet != nullptr))
      {
        l_wheelNode->setStateSet(l_wheelStateSet);
      }
    }
  }

protected:
  ~VehicleGbcFinalizer() override = default;
  //! Copy constructor is not permitted.
  VehicleGbcFinalizer (const VehicleGbcFinalizer& other) /* = delete */;
  //! Copy assignment operator is not permitted.
  VehicleGbcFinalizer& operator=(const VehicleGbcFinalizer& other) /* = delete */; // PRQA S 2051

private:
  pc::core::Framework *m_pFramework;

}; // VehicleGbcFinalizer


//!
//! Door2DAnimationFinalizer
//!
class Door2DAnimationFinalizer : public pc::vehiclemodel::IFinalizer // PRQA S 2119
{

public:
  Door2DAnimationFinalizer() = default;

  void finalize(pc::vehiclemodel::VehicleModel* f_vehicleModel) override
  {
    CustomVehicleModel2D* const l_vehicle2D = dynamic_cast<CustomVehicleModel2D*>(f_vehicleModel); // PRQA S 3077  // PRQA S 3400

    if (l_vehicle2D == nullptr)
    {
      return;
    }

    // IMPORTAN: Respect the order on the following arrays.
    // The PC component IDs are used for animation callback

    constexpr pc::vehiclemodel::VehicleModel::Component l_components[7] = {
      CustomVehicleModel2D::TRUNK,

      CustomVehicleModel2D::FRONT_LEFT_DOOR,
      CustomVehicleModel2D::FRONT_RIGHT_DOOR,
      CustomVehicleModel2D::REAR_LEFT_DOOR,
      CustomVehicleModel2D::REAR_RIGHT_DOOR,
      CustomVehicleModel2D::FRONT_LEFT_DOOR_MIRROR,
      CustomVehicleModel2D::FRONT_RIGHT_DOOR_MIRROR
    };

    constexpr CustomVehicleModel2D::CustomComponent2D l_darkComponents[7] = {
      CustomVehicleModel2D::TRUNK_IMPOSTOR_DARK,

      CustomVehicleModel2D::FRONT_LEFT_DOOR_IMPOSTOR_DARK,
      CustomVehicleModel2D::FRONT_RIGHT_DOOR_IMPOSTOR_DARK,
      CustomVehicleModel2D::REAR_LEFT_DOOR_IMPOSTOR_DARK,
      CustomVehicleModel2D::REAR_RIGHT_DOOR_IMPOSTOR_DARK,
      CustomVehicleModel2D::FRONT_LEFT_DOOR_MIRROR_IMPOSTOR_DARK,
      CustomVehicleModel2D::FRONT_RIGHT_DOOR_MIRROR_IMPOSTOR_DARK
    };

    for (vfc::uint32_t i = 0u; i < 7u; ++i)
    {
      const bool l_isOptional = ((cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_4DOOR) && (cc::core::g_ccf->m_ccfDoors != cc::core::CCF_DOORS_5DOOR))
                             && ((l_darkComponents[i] == CustomVehicleModel2D::REAR_LEFT_DOOR_IMPOSTOR_DARK) || (l_darkComponents[i] == CustomVehicleModel2D::REAR_RIGHT_DOOR_IMPOSTOR_DARK));
      osg::Node* const l_node = l_vehicle2D->getComponent(l_darkComponents[i], l_isOptional);
      if (l_node != nullptr)
      {
        osg::AnimationPathCallback* const l_animationPathCallback = dynamic_cast<osg::AnimationPathCallback*> (l_node->getUpdateCallback()); // PRQA S 3077  // PRQA S 3400
        if (l_animationPathCallback != nullptr)
        {
          // use pc component id for callback reuse
          pc::vehiclemodel::DoorAnimationPathCallback* const l_doorAnimationPathCallback = new pc::vehiclemodel::DoorAnimationPathCallback(*l_animationPathCallback, f_vehicleModel->getFramework(), l_components[i]);
          l_node->removeUpdateCallback(l_animationPathCallback);
          l_node->addUpdateCallback(l_doorAnimationPathCallback);
        }
      }
    }
  }

protected:
  ~Door2DAnimationFinalizer() override = default;
  //! Copy constructor is not permitted.
  Door2DAnimationFinalizer (const Door2DAnimationFinalizer& other) /* = delete */;
  //! Copy assignment operator is not permitted.
  Door2DAnimationFinalizer& operator=(const Door2DAnimationFinalizer& other) /* = delete */; // PRQA S 2051
}; //! Door2DAnimationFinalizer

//!
//! Vehicle2D
//!
Vehicle2D::Vehicle2D(cc::core::AssetId f_assetId, pc::core::Framework* f_framework)
  : Asset{f_assetId}
{
    CustomVehicleModel2D* const l_vehicleModel2D =  new CustomVehicleModel2D(g_model2d->m_modelFilePath, f_framework); // PRQA S 4262 // PRQA S 4264
    l_vehicleModel2D->addFinalizer(new pc::vehiclemodel::RenderBinFinalizer());
    l_vehicleModel2D->addFinalizer(new TextureFinalizer(f_framework));
    l_vehicleModel2D->addFinalizer(new Wheel2DAnimationFinalizer());
    l_vehicleModel2D->addFinalizer(new VehicleGbcFinalizer(f_framework));
    l_vehicleModel2D->addFinalizer(new Door2DAnimationFinalizer());
    l_vehicleModel2D->addFinalizer(new Vehicle2DNodeMaskFinalizer(f_framework));
    // l_vehicleModel2D->addFinalizer(new CarPaintFinalizer(f_framework));
    m_asset = l_vehicleModel2D;

    // Vehicle Model uniforms
    osg::StateSet* const l_stateSet = l_vehicleModel2D->getOrCreateStateSet();

    osg::Uniform *const l_brightnessUniform = l_stateSet->getOrCreateUniform("brightness", osg::Uniform::FLOAT);
    l_brightnessUniform->set( 1.0f );    // PRQA S 3803

    osg::Uniform *const l_brightness1Uniform = l_stateSet->getOrCreateUniform("brightness1", osg::Uniform::FLOAT);
    l_brightness1Uniform->setUpdateCallback( new CarPaintFinalizer::VehicleVeh2dBrightnessUpdateCallback(f_framework) );

    osg::Uniform *const l_diffuseColorUniform = l_stateSet->getOrCreateUniform("diffuseColor", osg::Uniform::FLOAT_VEC4);
    l_diffuseColorUniform->setUpdateCallback( new CarPaintFinalizer::VehicleDiffuseColorUpdateCallback() );

    osg::Uniform *const l_specColor1Uniform = l_stateSet->getOrCreateUniform("specColor1", osg::Uniform::FLOAT_VEC4);
    l_specColor1Uniform->setUpdateCallback( new CarPaintFinalizer::VehicleSpecColor1UpdateCallback(f_framework) );

    osg::Uniform *const l_specShininess1Uniform = l_stateSet->getOrCreateUniform("specShininess1", osg::Uniform::FLOAT);
    l_specShininess1Uniform->setUpdateCallback( new CarPaintFinalizer::VehicleSpecShininess1UpdateCallback(f_framework) );

    osg::Uniform *const l_specColor2Uniform = l_stateSet->getOrCreateUniform("specColor2", osg::Uniform::FLOAT_VEC4);
    l_specColor2Uniform->setUpdateCallback( new CarPaintFinalizer::VehicleSpecColor2UpdateCallback(f_framework) );

    osg::Uniform *const l_specShininess2Uniform = l_stateSet->getOrCreateUniform("specShininess2", osg::Uniform::FLOAT);
    l_specShininess2Uniform->setUpdateCallback( new CarPaintFinalizer::VehicleSpecShininess2UpdateCallback(f_framework) );

    osg::Uniform *const l_reflectionPowerUniform = l_stateSet->getOrCreateUniform("reflectionPower", osg::Uniform::FLOAT);
    l_reflectionPowerUniform->setUpdateCallback( new CarPaintFinalizer::ReflectionUpdateCallback(f_framework) );

    osg::Uniform *const l_fresnelUniform = l_stateSet->getOrCreateUniform("fresnel", osg::Uniform::FLOAT);
    l_fresnelUniform->setUpdateCallback( new CarPaintFinalizer::VehicleFresnelUpdateCallback(f_framework) );

    osg::Group::addChild(m_asset);    // PRQA S 3803
}


} // namespace common
} // namespace assets
} // namespace cc

