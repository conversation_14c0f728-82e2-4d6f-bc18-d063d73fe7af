#===============================================================================
# Copyright (c) 2017 by <PERSON>. All rights reserved.
# This file is property of Robert <PERSON>. Any unauthorized copy, use or
# distribution is an offensive act against international law and may be
# prosecuted under federal law. Its content is company confidential.
#===============================================================================

#-------------------------------------------------------------------------------
# Component include pathes
# Apply to all binaries built within this component
ifneq ($(SOC),qc)
COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/libxml2
COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/libdrm
COMPINC_gcc += $(SDKTARGETSYSROOT)/usr/include/opencv4
COMPINC_gcc += ./cc/$(subst _,/,$(TARGET_MODULE))/inc
endif

# add custom include path for standalone pc/generic
COMPINC_$(COMPILER_NAME) += ./pc/generic/

PROJECT_ROOT := $(shell git rev-parse --show-toplevel)

ifeq ($(TARGET_MODULE),target_adtf)
COMPDEF_$(COMPILER_NAME) += ADTF_LOGGING #sometimes the compiler name is msvc or msvc100 - take care about that
endif

# set defines in release builds to exclude socket connections, etc.
ifdef RELEASE
COMPDEF_$(COMPILER_NAME) += NO_SOCKET_SERVER
COMPDEF_$(COMPILER_NAME) += NO_RAPIDJSON
endif

ifdef IMGUI
COMPDEF_$(COMPILER_NAME) += ENABLE_IMGUI
endif

ifdef CAM1M
COMPDEF_$(COMPILER_NAME) += CAM1M
endif

ifneq ($(TARGET_MODULE),target_qc8155hsaenissank2a_app)
ifneq ($(TARGET_MODULE),target_qc8775hsaenissank2a_app) #TODO : delete after 8775 compiler ok
COMPDEF_$(COMPILER_NAME) += ENABLE_ONCAL
endif
endif

CHAMAELEON := 1
ifdef CHAMAELEON
COMPDEF_$(COMPILER_NAME) += CHAMAELEON
endif

ifeq ($(filter $(SUPPLIER),megak1a hsae8775),$(SUPPLIER))
COMPDEF_$(COMPILER_NAME) += ENABLE_APAUI
endif

# mod warning settings
ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
COMPDEF_$(COMPILER_NAME) += ENABLE_MOD_MULTI_DIRECTION_WARNING
endif

# cpc settings.
ifeq ($(SUPPLIER), megak1a)
COMPDEF_$(COMPILER_NAME) += CC_SV3D_NISSAN_LK1A_3M
else ifeq ($(SUPPLIER), hsaek2a)
COMPDEF_$(COMPILER_NAME) += CC_SV3D_NISSAN_LK2A_1M
else ifeq ($(SUPPLIER), hsae8775)
COMPDEF_$(COMPILER_NAME) += CC_SV3D_NISSAN_LK2A_3M
else ifeq ($(SUPPLIER), megapk1b1m)
COMPDEF_$(COMPILER_NAME) += CC_SV3D_NISSAN_PK1B_1M
else
# todo: add flags.
endif

# why missing??
COMPDEF_$(COMPILER_NAME) += VFC_ENABLE_FLOAT64_TYPE

#-------------------------------------------------------------------------------
# Component library pathes
cc_$(TARGET_MODULE)_LIBPATH +=

# Cookpit Supplier
ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
CFLAGS += -DSUP_MEGA  #TODO: delete this macro
CXXFLAGS += -DSUP_MEGA  #TODO: delete this macro
endif

ifeq ($(filter $(SUPPLIER),hsaek2a hsae8775),$(SUPPLIER))
CFLAGS += -DSUP_HSAE  #TODO: delete this macro
CXXFLAGS += -DSUP_HSAE  #TODO: delete this macro
endif

ifeq ($(filter $(TARGET_MODULE),target_qc8155hsaenissank2a_app target_qc8775hsaenissank2a_app),$(TARGET_MODULE))
CFLAGS += -DUSE_SIMPLE_LOG   #open simple log
CXXFLAGS += -DUSE_SIMPLE_LOG #open simple log
CFLAGS += -Wno-variadic-macros   #ignore ARGS... warning
CXXFLAGS += -Wno-variadic-macros #ignore ARGS... warning
endif

#-------------------------------------------------------------------------------
# Component libraries
ifneq ($(COMPILER_NAME),qcc)
cc_$(TARGET_MODULE)_LIB += stdc++
cc_$(TARGET_MODULE)_LIB += rt
cc_$(TARGET_MODULE)_LIB += pthread
endif


ifeq ($(COMPILER_NAME),qcc)
CFLAGS += -DSUSE_OSG_STATIC_LIB
CXXFLAGS += -DSUSE_OSG_STATIC_LIB

# For vhm lib
VHM_LIB := $(PROJECT_ROOT)/bin/pmasip/$(SUPPLIER)
$(info VHM_LIB is set to: !!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!!$(VHM_LIB))
LDFLAGS += -L${VHM_LIB} -lpma_sip

LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_zip -losgdb_png -losgdb_jpeg -losgdb_freetype -losgdb_ktx -losgdb_glsl -losgdb_dds -losgdb_openflight
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_gz -losgdb_tgz -losgdb_tga -losgdb_rgb -losgdb_osgterrain -losgdb_osg -losgdb_ive
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osgviewer -losgdb_deprecated_osgvolume -losgdb_deprecated_osgtext
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osgterrain -losgdb_deprecated_osgsim
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osgshadow -losgdb_deprecated_osgparticle -losgdb_deprecated_osgfx
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_deprecated_osganimation -losgdb_deprecated_osg
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgvolume -losgdb_serializers_osgtext -losgdb_serializers_osgterrain
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgsim -losgdb_serializers_osgshadow
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgparticle -losgdb_serializers_osgmanipulator
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/osgPlugins-3.2.3 -losgdb_serializers_osgfx -losgdb_serializers_osganimation -losgdb_serializers_osg
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/ -losgViewer  -losgVolume  -losgTerrain -losgText  -losgShadow  -losgSim  -losgParticle -losgManipulator
LDFLAGS += -L$(BSP_ROOT)/osg_static_lib/ -losgGA -losgFX -losgDB -losgAnimation -losgUtil -losg    -lOpenThreads

ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
LDFLAGS += -lg2o_core -lg2o_stuff -lg2o_types_sba -lg2o_types_slam2d -lg2o_types_slam3d -lSophus
endif

#ifeq ($(SUPPLIER),hsaek2a)
ifeq ($(filter $(SUPPLIER),hsaek2a hsae8775),$(SUPPLIER)) #TODO
LDFLAGS += -L$(BSP_ROOT)/simple_log/lib  -lsimple_log -ldlt -lunwind -lzip -lzlib
cc_$(TARGET_MODULE)_LIB += lzma
endif

cc_$(TARGET_MODULE)_LIB += png
cc_$(TARGET_MODULE)_LIB += jpeg
cc_$(TARGET_MODULE)_LIB += freetype
cc_$(TARGET_MODULE)_LIB += opencv_core
cc_$(TARGET_MODULE)_LIB += opencv_imgproc
cc_$(TARGET_MODULE)_LIB += opencv_imgcodecs
cc_$(TARGET_MODULE)_LIB += opencv_features2d
cc_$(TARGET_MODULE)_LIB += opencv_calib3d
cc_$(TARGET_MODULE)_LIB += opencv_flann
cc_$(TARGET_MODULE)_LIB += opencv_video
else
cc_$(TARGET_MODULE)_LIB += OpenThreads
cc_$(TARGET_MODULE)_LIB += osg
cc_$(TARGET_MODULE)_LIB += osgDB
cc_$(TARGET_MODULE)_LIB += osgUtil
cc_$(TARGET_MODULE)_LIB += osgGA
cc_$(TARGET_MODULE)_LIB += osgViewer
cc_$(TARGET_MODULE)_LIB += osgText
cc_$(TARGET_MODULE)_LIB += osgFX
cc_$(TARGET_MODULE)_LIB += osgGA
cc_$(TARGET_MODULE)_LIB += osgDB
cc_$(TARGET_MODULE)_LIB += osgAnimation
cc_$(TARGET_MODULE)_LIB += osgSim
endif


cc_$(TARGET_MODULE)_LIB += m
cc_$(TARGET_MODULE)_LIB += GLESv2
cc_$(TARGET_MODULE)_LIB += EGL
#cc_$(TARGET_MODULE)_LIB += drm
cc_$(TARGET_MODULE)_LIB += xml2
cc_$(TARGET_MODULE)_LIB += z
ifneq ($(SOC),qc)
cc_$(TARGET_MODULE)_LIB += opencv_core
cc_$(TARGET_MODULE)_LIB += opencv_imgproc
cc_$(TARGET_MODULE)_LIB += opencv_imgcodecs
endif

cc_target_ultrascale_LIB += dlt
cc_target_ultrascale_LIB += jpeg
cc_target_ultrascale_LIB += png

cc_$(TARGET_MODULE)_simplerpc_tools_cli_LIB += rt


ifeq ($(COMPILER_NAME),qcc)
LDFLAGS += -Wl,-rpath,$(HOME)/.conan/data/nv-drive-qnx/1.0.1/adit/stable/package/da62d10f6078e02f037a6e481ea2fbd6c8be9104/drive-t186ref-qnx/lib-target
LDFLAGS += -L$(HOME)/.conan/data/nv-drive-qnx/1.0.1/adit/stable/package/da62d10f6078e02f037a6e481ea2fbd6c8be9104/drive-t186ref-qnx/nvidia-bsp/aarch64le/usr/lib
LDFLAGS += -L$(HOME)/.conan/data/nv-cuda-qnx-toolkit-host/1.0.1/adit/stable/package/e4dfc630e56082b2f91aa12f4d56b1131e51f198/usr/local/cuda-10.2/targets/aarch64-qnx/lib
LDFLAGS += -L$(HOME)/.conan/data/OpenSceneGraph/3.2.3/esb/stable/package/8124809e6bb5244bf7b530af3f8199a830df14c7/lib
cc_target_nvidia_LIB += nvos_s3_safety
#cc_target_nvidia_LIB += slog2
#cc_target_nvidia_LIB += nvdtcommon
endif

# --- qualcomm ---
ifeq ($(COMPILER_NAME),qcc)
# expect external dependencies like OSG-libs and GLFW-libs
#  to be installed first into INSTALL_ROOT-folders
cc_$(TARGET_MODULE)_LIBPATH += $(INSTALL_ROOT_nto)/aarch64le/usr/lib
cc_$(TARGET_MODULE)_LIBPATH += $(QNX_TARGET)/aarch64le/usr/lib
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/moddet/lib
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/opencv/lib
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/Eigen/lib

ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/ocal
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/megaipc/lib
endif

#ifeq ($(SUPPLIER),hsaek2a)
ifeq ($(filter $(SUPPLIER),hsaek2a hsae8775),$(SUPPLIER)) #TODO
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/simple_log/lib
cc_$(TARGET_MODULE)_LIBPATH += $(BSP_ROOT)/pps/lib
endif

cc_$(TARGET_MODULE)_LIB += c++
cc_$(TARGET_MODULE)_LIB += socket
cc_$(TARGET_MODULE)_LIB += slog2
cc_$(TARGET_MODULE)_LIB += ais_log
cc_$(TARGET_MODULE)_LIB += ais_client
cc_$(TARGET_MODULE)_LIB += OSAbstraction
cc_$(TARGET_MODULE)_LIB += OSUser
cc_$(TARGET_MODULE)_LIB += pmem_client
cc_$(TARGET_MODULE)_LIB += pmemext
cc_$(TARGET_MODULE)_LIB += libstd
cc_$(TARGET_MODULE)_LIB += mmap_peer
cc_$(TARGET_MODULE)_LIB += screen
#cc_$(TARGET_MODULE)_LIB += smmu_client
#cc_$(TARGET_MODULE)_LIB += glfw
#cc_$(TARGET_MODULE)_LIB += adreno_utils
#cc_$(TARGET_MODULE)_LIB += glnext-llvm
#cc_$(TARGET_MODULE)_LIB += GSLUser
#cc_$(TARGET_MODULE)_LIB += ESXGLESv2_Adreno
#cc_$(TARGET_MODULE)_LIB += ESXEGL_Adreno



#Fd-bus libs
#cc_$(TARGET_MODULE)_LIB += protobuf
#cc_$(TARGET_MODULE)_LIB += VehicleClient
#cc_$(TARGET_MODULE)_LIB += protobuf
#cc_$(TARGET_MODULE)_LIB += VehicleService
#cc_$(TARGET_MODULE)_LIB += common_base
#cc_$(TARGET_MODULE)_LIB += VehicleToolClient
#cc_$(TARGET_MODULE)_LIB += gcc_s
#cc_$(TARGET_MODULE)_LIB += psis

cc_$(TARGET_MODULE)_LIB += pma_sip
ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
cc_$(TARGET_MODULE)_LIB += mega_ipc
endif

#ifeq ($(SUPPLIER),hsaek2a)
ifeq ($(filter $(SUPPLIER),hsaek2a hsae8775),$(SUPPLIER)) #TODO
cc_$(TARGET_MODULE)_LIB += pps_helper
endif

#moddet lib
cc_$(TARGET_MODULE)_LIB += ncnn
ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
cc_$(TARGET_MODULE)_LIB += nanodet
cc_$(TARGET_MODULE)_LIB += glslang
cc_$(TARGET_MODULE)_LIB += GenericCodeGen
cc_$(TARGET_MODULE)_LIB += MachineIndependent
cc_$(TARGET_MODULE)_LIB += OGLCompiler
cc_$(TARGET_MODULE)_LIB += OSDependent
cc_$(TARGET_MODULE)_LIB += SPIRV
cc_$(TARGET_MODULE)_LIB += glslang-default-resource-limits
endif

ifeq ($(COMPILER_NAME),qcc)
LDFLAGS += -Wl,-rpath,$(QNX_TARGET)/aarch64le/usr/lib
LDFLAGS += -L$(BSP_ROOT)/install/aarch64le/lib
LDFLAGS += -L$(BSP_ROOT)/prebuilt/aarch64le/usr/lib/graphics/qc/
LDFLAGS += -L$(INSTALL_ROOT_nto)/aarch64le/usr/lib
ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
LDFLAGS += -L$(BSP_ROOT)/megaipc/lib/
endif
#ifeq ($(SUPPLIER),hsaek2a)
ifeq ($(filter $(SUPPLIER),hsaek2a hsae8775),$(SUPPLIER)) #TODO
LDFLAGS += -L$(BSP_ROOT)/pps/lib/
endif
CFLAGS += -DSYSTEM_QNX
CXXFLAGS += -DSYSTEM_QNX

ifeq ($(filter $(SUPPLIER),megak1a megapk1b1m),$(SUPPLIER))
CFLAGS += -DUSE_QNX_SLOG  #open: open slog; close: log to console
CXXFLAGS += -DUSE_QNX_SLOG  #open: open slog; close: log to console
endif

CFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs
CXXFLAGS += -DUSE_BUILD_IN_SHARDER_CODE #open: use build-in shader source code instead of cc/shaderstatic pc/svs

cc_$(TARGET_MODULE)_LIB += SVSCameraMiddleware
endif
endif



# Component libraries unit-tests
hw_ut_$(UT_MODULE)_LIB += stdc++
hw_ut_$(UT_MODULE)_LIB += rt
hw_ut_$(UT_MODULE)_LIB += pthread
hw_ut_$(UT_MODULE)_LIB += OpenThreads
hw_ut_$(UT_MODULE)_LIB += osg
hw_ut_$(UT_MODULE)_LIB += osgDB
hw_ut_$(UT_MODULE)_LIB += osgUtil
hw_ut_$(UT_MODULE)_LIB += osgGA
hw_ut_$(UT_MODULE)_LIB += osgViewer
hw_ut_$(UT_MODULE)_LIB += osgText
hw_ut_$(UT_MODULE)_LIB += osgFX
hw_ut_$(UT_MODULE)_LIB += osgGA
hw_ut_$(UT_MODULE)_LIB += osgDB
hw_ut_$(UT_MODULE)_LIB += osgAnimation
hw_ut_$(UT_MODULE)_LIB += osgSim
hw_ut_$(UT_MODULE)_LIB += m
hw_ut_$(UT_MODULE)_LIB += GLESv2
hw_ut_$(UT_MODULE)_LIB += EGL
#hw_ut_$(UT_MODULE)_LIB += drm
hw_ut_$(UT_MODULE)_LIB += xml2
hw_ut_$(UT_MODULE)_LIB += z
ifneq ($(SOC),qc)
hw_ut_$(UT_MODULE)_LIB += opencv_core
hw_ut_$(UT_MODULE)_LIB += opencv_imgproc
hw_ut_$(UT_MODULE)_LIB += opencv_imgcodecs
endif
# some compilers does not have jpeg lib support
NON_JPEG_COMPILERS=gcc
ifneq ($(COMPILER_NAME),$(filter $(COMPILER_NAME),$(NON_JPEG_COMPILERS)))
hw_ut_$(UT_MODULE)_LIB += jpeg
endif
hw_ut_$(UT_MODULE)_LIB += png

#-------------------------------------------------------------------------------
# Project modules
SV3D_MODULES :=
SV3D_MODULES += cc_assets_augmentedview
# SV3D_MODULES += cc_assets_background
SV3D_MODULES += cc_assets_baseplate
SV3D_MODULES += cc_assets_common
SV3D_MODULES += cc_assets_splineoverlay
SV3D_MODULES += cc_assets_tileoverlay
SV3D_MODULES += cc_assets_parkingspots
# SV3D_MODULES += cc_assets_caliboverlay
SV3D_MODULES += cc_assets_button
SV3D_MODULES += cc_assets_rctaoverlay
SV3D_MODULES +=
SV3D_MODULES += cc_assets_trajectory
SV3D_MODULES += cc_assets_virtualreality

SV3D_MODULES += cc_assets_stb
SV3D_MODULES += cc_assets_viewbowl
SV3D_MODULES += cc_assets_impostor
SV3D_MODULES += cc_assets_overlaycallback
SV3D_MODULES += cc_assets_customfloorplategenerator
SV3D_MODULES += cc_assets_uielements
SV3D_MODULES += cc_assets_streetoverlay
SV3D_MODULES += cc_assets_fisheyeassets
SV3D_MODULES += cc_assets_freeparkingoverlay
SV3D_MODULES += cc_assets_freeparking
SV3D_MODULES += cc_assets_parkingspace
SV3D_MODULES += cc_assets_ECALprogressoverlay
SV3D_MODULES += cc_assets_debugoverlay
SV3D_MODULES += cc_assets_distanceoverlay
SV3D_MODULES += cc_assets_dynamicgearoverlay
SV3D_MODULES += cc_assets_dynamicwheelmask
SV3D_MODULES += cc_animation
SV3D_MODULES += cc_assets_vehicle2dwheels
SV3D_MODULES += cc_assets_ptsoverlay
SV3D_MODULES += cc_assets_settingpageoverlay
SV3D_MODULES += cc_assets_rimprotectionline
SV3D_MODULES += cc_core
SV3D_MODULES += cc_cpc
SV3D_MODULES += cc_daddy
SV3D_MODULES += cc_util_pdmwriter
ifneq ($(TARGET_MODULE),target_qc8155hsaenissank2a_app)
ifneq ($(TARGET_MODULE),target_qc8775hsaenissank2a_app) #TODO : delete after 8775 compiler ok
SV3D_MODULES += cc_oncal
endif
endif
ifeq ($(SOC),qc)
SV3D_MODULES += cc_vhm
SV3D_MODULES += cc_customsocket
ifeq ($(SUPPLIER),megak1a)
SV3D_MODULES += cc_viper_modeltracking
SV3D_MODULES += cc_target_qc8295meganissank1a_signal
SV3D_MODULES += cc_target_qc8295meganissank1a_engine
endif
ifeq ($(SUPPLIER),megapk1b1m)
SV3D_MODULES += cc_viper_modeltracking
SV3D_MODULES += cc_target_qc8295meganissanpk1b1m_signal
SV3D_MODULES += cc_target_qc8295meganissanpk1b1m_engine
endif
ifeq ($(SUPPLIER),hsaek2a)
SV3D_MODULES += cc_viper_pedestriandetection
SV3D_MODULES += cc_target_qc8155hsaenissank2a_signal
SV3D_MODULES += cc_target_qc8155hsaenissank2a_engine
endif
ifeq ($(SUPPLIER),hsae8775)
SV3D_MODULES += cc_viper_pedestriandetection
SV3D_MODULES += cc_target_qc8775hsaenissank2a_signal
SV3D_MODULES += cc_target_qc8775hsaenissank2a_engine
endif
endif
ifeq ($(filter $(SUPPLIER),megapk1b1m hsaek2a),$(SUPPLIER))
SV3D_MODULES += cc_smnoapa_viewmode
else
SV3D_MODULES += cc_sm_viewmode
endif
SV3D_MODULES += cc_mod
SV3D_MODULES += cc_shaderstatic
SV3D_MODULES += cc_$(TARGET_MODULE)
SV3D_MODULES += cc_util_cli
SV3D_MODULES += cc_util_beziercurve
SV3D_MODULES += cc_util_polygonmath
SV3D_MODULES += cc_util_logging

SV3D_MODULES += cc_views_panoramaview
SV3D_MODULES += cc_views_driveassistview
SV3D_MODULES += cc_views_bonnetview
SV3D_MODULES += cc_views_daynightview
SV3D_MODULES += cc_views_nfsengineeringview
SV3D_MODULES += cc_views_customrawfisheyeview
SV3D_MODULES += cc_views_customwarpfisheyeview
SV3D_MODULES += cc_views_planview
SV3D_MODULES += cc_views_surroundview
# SV3D_MODULES += cc_views_touchfocus
SV3D_MODULES += cc_views_parkview
SV3D_MODULES += cc_virtcam
SV3D_MODULES += cc_worker_core
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_test
endif
SV3D_MODULES +=
SV3D_MODULES += hw_version
SV3D_MODULES += hw_daddy
SV3D_MODULES +=
SV3D_MODULES += pc_generic_core
SV3D_MODULES += pc_generic_$(OS_MODULE)_com
SV3D_MODULES += pc_generic_$(OS_MODULE)_core
SV3D_MODULES += pc_generic_$(OS_MODULE)_logging
SV3D_MODULES += pc_generic_$(OS_MODULE)_sys
SV3D_MODULES += pc_generic_util_chrono
SV3D_MODULES += pc_generic_util_cli
SV3D_MODULES += pc_generic_util_coding
SV3D_MODULES += pc_generic_util_com
SV3D_MODULES += pc_generic_util_logging
SV3D_MODULES +=
SV3D_MODULES += pc_svs_animation
SV3D_MODULES += pc_svs_assets_imageoverlays
SV3D_MODULES += pc_svs_assets_vehiclemodel
SV3D_MODULES += pc_svs_assets_floorplate
SV3D_MODULES += pc_svs_c2w
SV3D_MODULES += pc_svs_core
SV3D_MODULES += pc_svs_daddy
SV3D_MODULES += pc_svs_factory
ifdef CHAMAELEON
SV3D_MODULES += pc_svs_imp_chamaeleon
SV3D_MODULES += pc_svs_imp_cwd
SV3D_MODULES += pc_svs_imp_iq
SV3D_MODULES += pc_svs_imp_sh
SV3D_MODULES += pc_svs_imp_tnf
endif
ifeq ($(TARGET_MODULE),target_standalone)
# SV3D_MODULES += cc_svs_$(TARGET_MODULE)
SV3D_MODULES += cc_imc_standalone
endif
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_MODULES += cc_util_touchmanipulator
	ifdef IMGUI
		SV3D_MODULES += cc_imgui
	endif
endif
SV3D_MODULES += pc_svs_texfloor_core
SV3D_MODULES += pc_svs_texfloor_odometry
SV3D_MODULES += pc_svs_util_cli
SV3D_MODULES += pc_svs_util_linestring
SV3D_MODULES += pc_svs_util_logging
SV3D_MODULES += pc_svs_util_math
SV3D_MODULES += pc_svs_util_osgx
SV3D_MODULES += pc_svs_vehicle
SV3D_MODULES += pc_svs_views_engineeringview
SV3D_MODULES += pc_svs_views_perfview
SV3D_MODULES += pc_svs_views_fusiview
SV3D_MODULES += pc_svs_views_warpfisheyeview
SV3D_MODULES += pc_svs_views_rawfisheyeview
SV3D_MODULES += pc_svs_virtcam
SV3D_MODULES += pc_svs_worker_bowlshaping
SV3D_MODULES += pc_svs_worker_core
SV3D_MODULES += pc_svs_worker_fusion
SV3D_MODULES += pc_svs_worker_stitching


#only add the unit test module to the project modules list if UT is defined
ifdef UT
SV3D_MODULES += hw_ut_$(UT_MODULE)
endif


EXCLUDE_MODULES_FROM_ALL:= pc_svs_tools_shaderfactory

#-------------------------------------------------------------------------------
# Include SW modules makefiles

# Include tool makefiles

include $(addsuffix /Makefile,$(subst _,/,$(SV3D_MODULES)))

#-------------------------------------------------------------------------------
# Link dependencies
SV3D_BINARIES :=
ifeq ($(TARGET_MODULE),target_standalone)
# SV3D_BINARIES += $(cc_svs_$(TARGET_MODULE)_BINARY)
SV3D_BINARIES += $(cc_imc_standalone_BINARY)
endif
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_util_touchmanipulator_BINARY)
	ifdef IMGUI
		SV3D_BINARIES += $(cc_imgui_BINARY)
	endif
endif
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_core_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_com_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_logging_BINARY)
SV3D_BINARIES += $(pc_generic_$(OS_MODULE)_sys_BINARY)
SV3D_BINARIES += $(pc_generic_util_chrono_BINARY)
SV3D_BINARIES += $(pc_generic_util_cli_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(pc_generic_core_BINARY)
SV3D_BINARIES += $(pc_generic_util_coding_BINARY)
SV3D_BINARIES += $(pc_generic_util_com_BINARY)
SV3D_BINARIES += $(pc_generic_util_logging_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(pc_svs_animation_BINARY)
SV3D_BINARIES += $(pc_svs_assets_imageoverlays_BINARY)
SV3D_BINARIES += $(pc_svs_assets_vehiclemodel_BINARY)
SV3D_BINARIES += $(pc_svs_assets_floorplate_BINARY)
SV3D_BINARIES += $(pc_svs_c2w_BINARY)
SV3D_BINARIES += $(pc_svs_core_BINARY)
SV3D_BINARIES += $(pc_svs_daddy_BINARY)
SV3D_BINARIES += $(pc_svs_factory_BINARY)
ifdef CHAMAELEON
SV3D_BINARIES += $(pc_svs_imp_chamaeleon_BINARY)
SV3D_BINARIES += $(pc_svs_imp_cwd_BINARY)
SV3D_BINARIES += $(pc_svs_imp_iq_BINARY)
SV3D_BINARIES += $(pc_svs_imp_sh_BINARY)
SV3D_BINARIES += $(pc_svs_imp_tnf_BINARY)
endif
SV3D_BINARIES += $(pc_svs_texfloor_core_BINARY)
SV3D_BINARIES += $(pc_svs_texfloor_odometry_BINARY)
SV3D_BINARIES += $(pc_svs_util_cli_BINARY)
SV3D_BINARIES += $(pc_svs_util_linestring_BINARY)
SV3D_BINARIES += $(pc_svs_util_logging_BINARY)
SV3D_BINARIES += $(pc_svs_util_math_BINARY)
SV3D_BINARIES += $(pc_svs_util_osgx_BINARY)
SV3D_BINARIES += $(pc_svs_vehicle_BINARY)
SV3D_BINARIES += $(pc_svs_views_engineeringview_BINARY)
SV3D_BINARIES += $(pc_svs_views_perfview_BINARY)
SV3D_BINARIES += $(pc_svs_views_fusiview_BINARY)
SV3D_BINARIES += $(pc_svs_views_warpfisheyeview_BINARY)
SV3D_BINARIES += $(pc_svs_views_rawfisheyeview_BINARY)
SV3D_BINARIES += $(pc_svs_virtcam_BINARY)
SV3D_BINARIES += $(pc_svs_worker_bowlshaping_BINARY)
SV3D_BINARIES += $(pc_svs_worker_core_BINARY)
SV3D_BINARIES += $(pc_svs_worker_fusion_BINARY)
SV3D_BINARIES += $(pc_svs_worker_stitching_BINARY)
SV3D_BINARIES +=
SV3D_BINARIES += $(cc_assets_augmentedview_BINARY)
# SV3D_BINARIES += $(cc_assets_background_BINARY)
SV3D_BINARIES += $(cc_assets_baseplate_BINARY)
SV3D_BINARIES += $(cc_assets_common_BINARY)
SV3D_BINARIES += $(cc_assets_splineoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_tileoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_parkingspots_BINARY)
# SV3D_BINARIES += $(cc_assets_caliboverlay_BINARY)
SV3D_BINARIES += $(cc_assets_button_BINARY)
SV3D_BINARIES += $(cc_assets_rctaoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_trajectory_BINARY)
SV3D_BINARIES += $(cc_assets_virtualreality_BINARY)

SV3D_BINARIES += $(cc_assets_stb_BINARY)
SV3D_BINARIES += $(cc_assets_viewbowl_BINARY)
SV3D_BINARIES += $(cc_assets_impostor_BINARY)
SV3D_BINARIES += $(cc_assets_overlaycallback_BINARY)
SV3D_BINARIES += $(cc_assets_customfloorplategenerator_BINARY)
SV3D_BINARIES += $(cc_assets_uielements_BINARY)
SV3D_BINARIES += $(cc_assets_streetoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_fisheyeassets_BINARY)
SV3D_BINARIES += $(cc_assets_freeparkingoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_freeparking_BINARY)
SV3D_BINARIES += $(cc_assets_parkingspace_BINARY)
SV3D_BINARIES += $(cc_assets_ECALprogressoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_debugoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_distanceoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_dynamicgearoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_dynamicwheelmask_BINARY)
SV3D_BINARIES += $(cc_animation_BINARY)
SV3D_BINARIES += $(cc_assets_vehicle2dwheels_BINARY)
SV3D_BINARIES += $(cc_assets_ptsoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_settingpageoverlay_BINARY)
SV3D_BINARIES += $(cc_assets_rimprotectionline_BINARY)

SV3D_BINARIES += $(cc_core_BINARY)
SV3D_BINARIES += $(cc_cpc_BINARY)
SV3D_BINARIES += $(cc_daddy_BINARY)
SV3D_BINARIES += $(cc_util_pdmwriter_BINARY)
ifneq ($(TARGET_MODULE),target_qc8155hsaenissank2a_app)
ifneq ($(TARGET_MODULE),target_qc8775hsaenissank2a_app) #TODO : delete after 8775 compiler ok
SV3D_BINARIES += $(cc_oncal_BINARY)
endif
endif
ifeq ($(SOC),qc)
SV3D_BINARIES += $(cc_customsocket_BINARY)
SV3D_BINARIES += $(cc_vhm_BINARY)
ifeq ($(SUPPLIER),megak1a)
SV3D_BINARIES += $(cc_viper_modeltracking_BINARY)
SV3D_BINARIES += $(cc_target_qc8295meganissank1a_signal_BINARY)
SV3D_BINARIES += $(cc_target_qc8295meganissank1a_engine_BINARY)
endif
ifeq ($(SUPPLIER),megapk1b1m)
SV3D_BINARIES += $(cc_viper_modeltracking_BINARY)
SV3D_BINARIES += $(cc_target_qc8295meganissanpk1b1m_signal_BINARY)
SV3D_BINARIES += $(cc_target_qc8295meganissanpk1b1m_engine_BINARY)
endif
ifeq ($(SUPPLIER),hsaek2a)
SV3D_BINARIES += $(cc_viper_pedestriandetection_BINARY)
SV3D_BINARIES += $(cc_target_qc8155hsaenissank2a_signal_BINARY)
SV3D_BINARIES += $(cc_target_qc8155hsaenissank2a_engine_BINARY)
endif
ifeq ($(SUPPLIER),hsae8775)
SV3D_BINARIES += $(cc_viper_pedestriandetection_BINARY)
SV3D_BINARIES += $(cc_target_qc8775hsaenissank2a_signal_BINARY)
SV3D_BINARIES += $(cc_target_qc8775hsaenissank2a_engine_BINARY)
endif
endif
ifeq ($(filter $(SUPPLIER),megapk1b1m hsaek2a),$(SUPPLIER))
SV3D_BINARIES += $(cc_smnoapa_viewmode_BINARY)
else
SV3D_BINARIES += $(cc_sm_viewmode_BINARY)
endif
SV3D_BINARIES += $(cc_mod_BINARY)
SV3D_BINARIES += $(cc_$(TARGET_MODULE)_BINARY)
SV3D_BINARIES += $(cc_util_cli_BINARY)
SV3D_BINARIES += $(cc_util_beziercurve_BINARY)
SV3D_BINARIES += $(cc_util_polygonmath_BINARY)
SV3D_BINARIES += $(cc_util_logging_BINARY)

SV3D_BINARIES += $(cc_views_panoramaview_BINARY)
SV3D_BINARIES += $(cc_views_driveassistview_BINARY)
SV3D_BINARIES += $(cc_views_bonnetview_BINARY)
SV3D_BINARIES += $(cc_views_daynightview_BINARY)
SV3D_BINARIES += $(cc_views_nfsengineeringview_BINARY)
SV3D_BINARIES += $(cc_views_customrawfisheyeview_BINARY)
SV3D_BINARIES += $(cc_views_customwarpfisheyeview_BINARY)
SV3D_BINARIES += $(cc_views_planview_BINARY)
SV3D_BINARIES += $(cc_views_surroundview_BINARY)
# SV3D_BINARIES += $(cc_views_touchfocus_BINARY)
SV3D_BINARIES += $(cc_views_parkview_BINARY)
SV3D_BINARIES += $(cc_virtcam_BINARY)

SV3D_BINARIES += $(cc_worker_core_BINARY)
ifeq ($(TARGET_MODULE),target_standalone)
SV3D_BINARIES += $(cc_test_BINARY)
endif
SV3D_BINARIES +=
SV3D_BINARIES += $(hw_version_BINARY)
SV3D_BINARIES += $(hw_daddy_BINARY)

ifdef UT
_UT_BINARY := $(hw_ut_$(UT_MODULE)_UTBINARY)
$(_UT_BINARY):               \
$(SV3D_BINARIES)           \
$(hw_ut_$(UT_MODULE)_BINARY) \
$(UTOBJLIST)
endif




_COMPONENT_BINARIES += $(cc_$(TARGET_MODULE)_BINARY)

ifeq  ($(SOC),qc)
$(cc_$(TARGET_MODULE)_BINARY): $(SV3D_BINARIES)
else
$(cc_$(TARGET_MODULE)_BINARY): $(SV3D_BINARIES)
endif



#EOF
