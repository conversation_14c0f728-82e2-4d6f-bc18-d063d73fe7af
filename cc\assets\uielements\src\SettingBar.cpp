//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  SettingBar.cpp
/// @brief
//=============================================================================

#include "cc/assets/uielements/inc/SettingBar.h"
#include "cc/core/inc/CustomFramework.h"
#include "cc/core/inc/CustomScene.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/generic/util/logging/inc/Logging.h"
#include "pc/generic/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "pc/svs/util/logging/inc/LoggingContexts.h" // PRQA S 1060
#include "CustomSystemConf.h"

#include "cc/assets/uielements/inc/HmiElementsSettings.h"

using pc::util::logging::g_AppContext;

namespace cc
{
namespace assets
{
namespace uielements
{

// ! enum values for seeting bar icons
enum class SettingBarIconType : vfc::uint8_t
{
  SETTING_BAR_BOTTOM,
  SETTING_BAR_APA_ACTIVE,
  SETTING_BAR_APA_INACTIVE,
  SETTING_BAR_VM_2D,
  SETTING_BAR_VM_3D,
  SETTING_BAR_VM_OFFROAD_ACTIVE,
  SETTING_BAR_VM_OFFROAD_INACTIVE,
  SETTING_BAR_VM_WHEEL_ACTIVE,
  SETTING_BAR_VM_WHEEL_INACTIVE,
  SETTING_BAR_VM_WHEEL_LINE
};

//!
//! @brief Construct a new SettingBar Manager:: SettingBar Manager object
//!
//! @param f_config
//!
SettingBarManager::SettingBarManager(cc::core::CustomFramework* f_customFramework)
  : m_lastConfigUpdate{~0u}
  , m_settingBarIcons{}
  , m_customFramework{f_customFramework}
  , m_apatatus{cc::target::common::EPARKStatusR2L::PARK_Off}
  , m_requestViewMode{ESVSViewMode_VM_Standard}
  , m_requestViewId{EScreenID_SINGLE_FRONT_NORMAL}
  , m_gear{pc::daddy::GEAR_INIT}
  , m_parkActiveStatus{cc::target::common::EParkActiveStatus::ParkActive_NoRequest}
  , m_transparentStatus{false}
{
}


SettingBarManager::~SettingBarManager() = default;



void SettingBarManager::init(pc::assets::ImageOverlays* f_imageOverlays)
{
  // ! init SettingBar icons
  m_settingBarIcons.clear(f_imageOverlays);

  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathBottom,          g_uiSettings->m_settingBottomBar.m_iconCenter,     g_uiSettings->m_settingBottomBar.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathApaActive,       g_uiSettings->m_settingApa.m_iconCenter,           g_uiSettings->m_settingApa.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathApaInactive,     g_uiSettings->m_settingApa.m_iconCenter,           g_uiSettings->m_settingApa.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathVM2D,            g_uiSettings->m_settingVM.m_iconCenter,            g_uiSettings->m_settingVM.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathVM3D,            g_uiSettings->m_settingVM.m_iconCenter,            g_uiSettings->m_settingVM.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathOffRoad,         g_uiSettings->m_settingOffRoad.m_iconCenter,       g_uiSettings->m_settingOffRoad.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathOffRoadOFF,      g_uiSettings->m_settingOffRoad.m_iconCenter,       g_uiSettings->m_settingOffRoad.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheel,           g_uiSettings->m_settingWheel.m_iconCenter,         g_uiSettings->m_settingWheel.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheelOFF,        g_uiSettings->m_settingWheel.m_iconCenter,         g_uiSettings->m_settingWheel.m_iconSize));
  m_settingBarIcons.addIcon(f_imageOverlays, createIconTopLeft(g_uiSettings->m_texturePathWheelLine,       g_uiSettings->m_settingWheelLine.m_iconCenter,     g_uiSettings->m_settingWheelLine.m_iconSize));

}


//!
//! @brief Construct a new SettingBar:: SettingBar object
//!
//! @param f_customFramework
//! @param f_assetId
//! @param f_view
//!
SettingBar::SettingBar(cc::core::CustomFramework* f_customFramework, cc::core::AssetId f_assetId)
  : cc::assets::uielements::CustomImageOverlays{f_assetId , nullptr}    // PRQA S 2966
  , m_customFramework{f_customFramework}
  , m_manager{f_customFramework}
{
  setNumChildrenRequiringUpdateTraversal(getNumChildrenRequiringUpdateTraversal() + 1);
}


SettingBar::~SettingBar() = default;




void SettingBar::traverse(osg::NodeVisitor& f_nv)
{
  if (osg::NodeVisitor::UPDATE_VISITOR == f_nv.getVisitorType())
  {
    // m_manager.update(this);
  }
  pc::assets::ImageOverlays::traverse(f_nv);
}


} // namespace uielements
} // namespace assets
} // namespace cc // PRQA S 1041
