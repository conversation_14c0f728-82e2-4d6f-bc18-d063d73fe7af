#ifndef LIGHT_STATE_CLASSES_H
#define LIGHT_STATE_CLASSES_H

#include "pc/generic/util/coding/inc/ISerializable.h"
#include "pc/generic/util/coding/inc/CodingManager.h"

#include <osg/Group>
#include <osg/NodeCallback>

#include <limits>
#include <map>
#include <memory>
#include <string>
#include <vector>



namespace pc
{
namespace core
{
class Framework;
} // namespace core
} // namespace pc

namespace cc
{
namespace assets
{
namespace common
{
namespace lightstate
{

class BlinkingIndicatorSettings;
extern pc::util::coding::Item<BlinkingIndicatorSettings> g_settings;

///
/// SignalWrapper
///
class SignalWrapper
{
public:

    static constexpr unsigned int INVALID_VALUE = std::numeric_limits<unsigned int>::max();

    SignalWrapper(unsigned int f_value = INVALID_VALUE);

    virtual ~SignalWrapper() = default;

    unsigned int getValue() const
    {
        return m_value;
    }

    bool update(pc::core::Framework* f_framework);

    virtual unsigned int toValue(const std::string& f_stringValue) const = 0;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const = 0;

private:

    unsigned int m_value;
};


template<class Map>
unsigned int findOrInvalid(const Map& f_map, const std::string& f_name)
{
    const auto l_res = f_map.find(f_name);
    if (l_res != f_map.end())
    {
        return static_cast<unsigned int> (l_res->second);
    }
    return SignalWrapper::INVALID_VALUE;
}


///
/// HeadlightWrapper
///
class HeadlightWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        None      = 0,
        HighBeam  = 1,
        LowBeam   = 2,
        Both      = 3,
        Daylight  = 4
    };

    HeadlightWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;
};

///
/// DayWrapper
///
class DayWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Off      = 0,
        On       = 1,
        DayLight_L = 2,
        DayLight_R = 3
    };

    DayWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;
};

///
/// RearPosWrapper
///
class RearPosWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Off      = 0,
        On       = 1
    };

    RearPosWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;
};


///
/// BrakeLightWrapper
///
class BrakeLightWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Off = 0,
        On  = 1
    };

    BrakeLightWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;

};


///
/// ReverseGearWrapper
///
class ReverseGearWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Off = 0,
        On  = 1
    };

    ReverseGearWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;

};


///
/// IndicatorLeftWrapper
///
class IndicatorLeftWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Off = 0,
        On  = 1
    };

    IndicatorLeftWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;

};


///
/// IndicatorRightWrapper
///
class IndicatorRightWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Off = 0,
        On  = 1
    };

    IndicatorRightWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;

};


///
/// IndicatorWrapper
///
class IndicatorWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Idle  = 0,
        Left  = 1,
        Right = 2,
        Warn  = 3
    };

    IndicatorWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;

};


///
/// SideIndicatorWrapper
///
class SideIndicatorWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Idle  = 0,
        Left  = 1,
        Right = 2,
        Warn  = 3
    };

    SideIndicatorWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;

};

///
/// CornerWrapper
///
class CornerWrapper : public SignalWrapper
{
public:

    enum class State : unsigned int
    {
        Idle  = 0,
        Left  = 1,
        Right = 2,
    };

    CornerWrapper() = default;

    virtual unsigned int toValue(const std::string& f_stringValue) const override;

protected:

    virtual unsigned int querySignal(pc::core::Framework* f_framework) const override;
};


///
/// SignalWrapperRegistry
///
class SignalWrapperRegistry
{
public:

    explicit SignalWrapperRegistry(pc::core::Framework* f_framework);

    bool update();

    void registerSignal(const std::string& f_name, const std::shared_ptr<SignalWrapper>& f_singalWrapper);

    std::shared_ptr<SignalWrapper> getSignalWrapper(const std::string& f_name) const;

private:

    // Private copy constructor and assignment operator
    SignalWrapperRegistry(const SignalWrapperRegistry& f_other) = delete;
    SignalWrapperRegistry& operator=(const SignalWrapperRegistry& f_other) = delete;

    pc::core::Framework* m_framework;
    std::map< std::string, std::shared_ptr<SignalWrapper> > m_signalWrappers;
};


///
/// SignalReq
///
class SignalReq
{
public:

    SignalReq(
        const std::shared_ptr<SignalWrapper>& f_signal,
        unsigned int f_referenceValue,
        bool f_not);

    bool check() const;

private:

    // Private copy constructor and assignment operator
    SignalReq(const SignalReq& f_other) = delete;
    SignalReq& operator=(const SignalReq& f_other) = delete;

private:

    std::shared_ptr<SignalWrapper> m_signal;
    const unsigned int m_referenceValue;
    const bool m_not;

};


///
/// Condition
///
class Condition
{
public:

    Condition() = default;

    void addSignalReq(const std::shared_ptr<SignalReq>& f_item);

    bool evaluate() const;

private:

    // Private copy constructor and assignment operator
    Condition(const Condition& f_other) = delete;
    Condition& operator=(const Condition& f_other) = delete;

private:

    std::vector< std::shared_ptr<SignalReq> > m_signalReqs;
};


///
/// LightNode
///
class LightNode
{
public:

    LightNode(osg::Node* f_lightNode);

    void addCondition(const std::shared_ptr<Condition>& f_condition);

    void update();

private:

    // Private copy constructor and assignment operator
    LightNode(const LightNode& f_other);
    LightNode& operator=(const LightNode& f_other);

private:

    osg::ref_ptr<osg::Node> m_lightNode;
    std::vector< std::shared_ptr<Condition> > m_conditions;

};


///
/// LightNodeUpdateCallback
///
class LightNodeUpdateCallback : public osg::NodeCallback
{
public:

    LightNodeUpdateCallback(const std::shared_ptr<SignalWrapperRegistry>& f_registry);

    virtual void operator() (osg::Node* f_node, osg::NodeVisitor* f_nv) override;

    void addLightNode(const std::shared_ptr<LightNode>& f_lightNode);

    const SignalWrapperRegistry* getRegistry() const
    {
        return m_registry.get();
    }

protected:

    virtual ~LightNodeUpdateCallback() = default;

private:

    // Private copy constructor and assignment operator
    LightNodeUpdateCallback(const LightNodeUpdateCallback& f_other) = delete;
    LightNodeUpdateCallback& operator=(const LightNodeUpdateCallback& f_other) = delete;

private:

    std::shared_ptr<SignalWrapperRegistry> m_registry;
    std::vector< std::shared_ptr<LightNode> > m_lightNodes;

};


///
/// LightStateJsonParser
///
class LightStateJsonParser
{
public:

    explicit LightStateJsonParser(const std::shared_ptr<SignalWrapperRegistry>& f_registry);

    LightNodeUpdateCallback* parse(const std::string& f_filename, osg::Group* f_sceneGraph);

    virtual void onHint(const std::string& f_hint, osg::Node* f_node);

private:

    std::shared_ptr<SignalWrapperRegistry> m_registry;

};

} // namespace lightstate
} // namespace common
} // namespace assets
} // namespace cc

#endif