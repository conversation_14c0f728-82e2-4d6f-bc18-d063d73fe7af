//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: MADCSZH Mao Donny (CC-DA/EAV2-CN)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CalibSpline.cpp
/// @brief 
//=============================================================================

#include "cc/assets/caliboverlay/inc/CalibSpline.h"
#include "cc/assets/caliboverlay/inc/CalibOverlay.h"
#include "pc/svs/core/inc/ShaderManager.h"
#include "pc/svs/util/math/inc/CommonMath.h"
#include "pc/svs/util/math/inc/Math2D.h"
#include "pc/svs/util/math/inc/Interpolator.h"
#include "pc/svs/util/osgx/inc/Utils.h"
#include "pc/svs/vehicle/inc/MechanicalData.h"
#include <osg/Geometry>
#include <osg/Math>
#include <osg/Image>
#include <osg/Texture2D>


namespace cc
{
namespace assets
{
namespace caliboverlay
{

osg::Geometry* createGeometry(const std::string& f_name, bool f_withTexCoors = false)
{
  osg::Vec3Array* l_vertices = new osg::Vec3Array;
  osg::Vec4Array* l_colors = new osg::Vec4Array;

  //! create VBO manually in order to set the usage flag
  osg::VertexBufferObject* l_vbo = new osg::VertexBufferObject;
  l_vbo->setUsage(GL_DYNAMIC_DRAW_ARB);    // PRQA S 3143
  l_vertices->setVertexBufferObject(l_vbo);

  osg::Geometry* l_geometry = pc::util::osgx::createGeometry(f_name);
  l_geometry->setVertexArray(l_vertices);
  l_geometry->setColorArray(l_colors, osg::Array::BIND_PER_VERTEX);
  if (f_withTexCoors)
  {
    osg::Vec2Array* l_texCoords = new osg::Vec2Array;
    l_geometry->setTexCoordArray(0u, l_texCoords, osg::Array::BIND_PER_VERTEX);
  }
  l_geometry->setDataVariance(osg::Object::DYNAMIC);
  return l_geometry;
}


osg::DrawElements* createSurface(
  unsigned int f_numLayoutPoints,
  unsigned int f_numSurfacePoints,
  unsigned int f_vertexOffset)
{
  const unsigned int l_numVertices = f_numLayoutPoints * f_numSurfacePoints;
  const unsigned int l_numIndices = f_numLayoutPoints * (f_numSurfacePoints - 1u) * 6u;
  osg::DrawElementsUShort* l_indices = new osg::DrawElementsUShort(static_cast<GLenum>(osg::PrimitiveSet::TRIANGLES), l_numIndices);  

  unsigned int l_indexCounter = 0u;
  for(unsigned int i = 0u; i < f_numLayoutPoints; ++i)
  {
    unsigned int l_vo =  i * f_numSurfacePoints;
    for (unsigned int j = 0u; j < f_numSurfacePoints - 1u; ++j)
    {
      // 1st triangle
      (*l_indices)[l_indexCounter]   = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j) % l_numVertices));
      (*l_indices)[l_indexCounter+1u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + 1u) % l_numVertices));
      (*l_indices)[l_indexCounter+2u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + f_numSurfacePoints) % l_numVertices));
      // 2nd triangle
      (*l_indices)[l_indexCounter+3u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + 1u) % l_numVertices));
      (*l_indices)[l_indexCounter+4u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + 1u + f_numSurfacePoints) % l_numVertices));
      (*l_indices)[l_indexCounter+5u] = static_cast<osg::DrawElementsUShort::value_type>(f_vertexOffset + ((l_vo + j + f_numSurfacePoints) % l_numVertices));
      l_indexCounter += 6u;
    }
  }
  return l_indices;
}

CalibSpline::UpdateVisitor::UpdateVisitor()
  : osg::NodeVisitor(osg::NodeVisitor::TRAVERSE_ALL_CHILDREN)
{

}


CalibSpline::UpdateVisitor::~UpdateVisitor()
{
}


void CalibSpline::UpdateVisitor::apply(osg::Node& f_node)
{
  CalibSpline* l_calibSpline = dynamic_cast<CalibSpline*> (&f_node);
  if (l_calibSpline)
  {
    l_calibSpline->setLayout();
  }
  traverse(f_node);
}


//!
//! CalibSpline
//!
CalibSpline::CalibSpline()
  : m_numVertices(0u)
{
}


CalibSpline::CalibSpline(const CalibSpline& f_other)
  : m_numVertices(f_other.m_numVertices)
{
}

CalibSpline::~CalibSpline()
{
}



//!
//! CalibSpline2D
//!
CalibSpline2D::CalibSpline2D()
{
  osg::Geode::addDrawable(createGeometry("CalibSpline2D", true));  // PRQA S 3803
}


CalibSpline2D::CalibSpline2D(const CalibSpline2D& f_other, const osg::CopyOp& f_copyOp)
  : osg::Geode(f_other, f_copyOp)
  , CalibSpline(f_other)
{
}


CalibSpline2D::~CalibSpline2D()
{
}


void CalibSpline2D::setLayout()
{

  osg::Geometry* l_geometry  = getDrawable(0u)->asGeometry();
  osg::Vec3Array* l_vertices = static_cast<osg::Vec3Array*> (l_geometry->getVertexArray());
  osg::Vec4Array* l_colors   = static_cast<osg::Vec4Array*> (l_geometry->getColorArray());

  const unsigned int l_numVertices = 16u;
  // const unsigned int l_calibstatus = f_calibstatus;

  if( l_numVertices != m_numVertices )
  {
    m_numVertices = l_numVertices;
    l_vertices->resize(l_numVertices);
    l_colors->resize(l_numVertices);
    l_geometry->removePrimitiveSet(0u, l_geometry->getNumPrimitiveSets()); //! clear primitive sets  // PRQA S 3803

    l_geometry->addPrimitiveSet(createSurface(2u, 2u, 0u));  // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(2u, 2u, 4u));  // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(2u, 2u, 8u));  // PRQA S 3803
    l_geometry->addPrimitiveSet(createSurface(2u, 2u, 12u));  // PRQA S 3803
  }

  unsigned int l_v = 0u;
  const float l_line_width = 0.05f;
  const float l_offset_x_front = 0.1f;
  const float l_offset_x_rear = 0.15f;
  // calib overlay color
  // osg::Vec4f l_calibcolor = osg::Vec4f(1.0f, 0.0f, 0.0f, 1.0f);
  osg::Vec4f l_calibcolor = g_calibSettings->m_calibOverlayColor;

  // inside four vertices
  osg::Vec3f l_frontleft = osg::Vec3f(0.0f, 0.0f, 0.0f);
  l_frontleft.x() = pc::vehicle::g_mechanicalData->m_wheelbase + pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront + l_offset_x_front;
  l_frontleft.y() = (pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f);

  osg::Vec3f l_rearleft = osg::Vec3f(0.0f, 0.0f, 0.0f);
  l_rearleft.x() = -pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear - l_offset_x_rear;
  l_rearleft.y() = (pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f);

  osg::Vec3f l_frontright = osg::Vec3f(0.0f, 0.0f, 0.0f);
  l_frontright.x() = pc::vehicle::g_mechanicalData->m_wheelbase + pc::vehicle::g_mechanicalData->m_axleToBumperDistanceFront + l_offset_x_front;
  l_frontright.y() = -(pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f);

  osg::Vec3f l_rearright = osg::Vec3f(0.0f, 0.0f, 0.0f);
  l_rearright.x() = -pc::vehicle::g_mechanicalData->m_axleToBumperDistanceRear - l_offset_x_rear;
  l_rearright.y() = -(pc::vehicle::g_mechanicalData->m_widthWithMirrors / 2.0f);


  // left surface
  (*l_vertices)[l_v]     = l_frontleft + osg::Vec3f(l_line_width, 0.0f, 0.0f);
  (*l_vertices)[l_v + 1u] = l_frontleft + osg::Vec3f(l_line_width, l_line_width, 0.0f);
  (*l_vertices)[l_v + 2u] = l_rearleft  + osg::Vec3f(-l_line_width, l_line_width, 0.0f);
  (*l_vertices)[l_v + 3u] = l_rearleft  + osg::Vec3f(-l_line_width, 0.0f, 0.0f);

  (*l_colors)[l_v]       = l_calibcolor;
  (*l_colors)[l_v + 1u]   = l_calibcolor;
  (*l_colors)[l_v + 2u]   = l_calibcolor;
  (*l_colors)[l_v + 3u]   = l_calibcolor;

  // upper surface 
  (*l_vertices)[l_v + 4u] = l_frontright + osg::Vec3f(0.0f, 0.0f, 0.0f); 
  (*l_vertices)[l_v + 5u] = l_frontright + osg::Vec3f(l_line_width, 0.0f, 0.0f);
  (*l_vertices)[l_v + 6u] = l_frontleft  + osg::Vec3f(l_line_width, 0.0f, 0.0f);
  (*l_vertices)[l_v + 7u] = l_frontleft  + osg::Vec3f(0.0f, 0.0f, 0.0f); 

  (*l_colors)[l_v + 4u]   =  l_calibcolor;
  (*l_colors)[l_v + 5u]   =  l_calibcolor;
  (*l_colors)[l_v + 6u]   =  l_calibcolor;
  (*l_colors)[l_v + 7u]   =  l_calibcolor;

  //right surface
  (*l_vertices)[l_v + 8u]  = l_rearright  + osg::Vec3f(-l_line_width, 0.0f, 0.0f);
  (*l_vertices)[l_v + 9u]  = l_rearright  + osg::Vec3f(-l_line_width, -l_line_width, 0.0f);
  (*l_vertices)[l_v + 10u] = l_frontright + osg::Vec3f(l_line_width, -l_line_width, 0.0f);
  (*l_vertices)[l_v + 11u] = l_frontright + osg::Vec3f( l_line_width, 0.0f, 0.0f);

  (*l_colors)[l_v + 8u]    = l_calibcolor;
  (*l_colors)[l_v + 9u]    = l_calibcolor;
  (*l_colors)[l_v + 10u]   = l_calibcolor;
  (*l_colors)[l_v + 11u]   = l_calibcolor;

  //under surface
  (*l_vertices)[l_v + 12u] = l_rearright  + osg::Vec3f(-l_line_width, 0.0f, 0.0f);
  (*l_vertices)[l_v + 13u] = l_rearright  + osg::Vec3f(0.0f, 0.0f, 0.0f);
  (*l_vertices)[l_v + 14u] = l_rearleft   + osg::Vec3f(0.0f, 0.0f, 0.0f);
  (*l_vertices)[l_v + 15u] = l_rearleft   + osg::Vec3f(-l_line_width, 0.0f, 0.0f);

  (*l_colors)[l_v + 12u]   = l_calibcolor;
  (*l_colors)[l_v + 13u]   = l_calibcolor;
  (*l_colors)[l_v + 14u]   = l_calibcolor;
  (*l_colors)[l_v + 15u]   = l_calibcolor;


  l_vertices->dirty();
  l_colors->dirty();
  l_geometry->dirtyBound();

}


} // namespace caliboverlay
} // namespace assets
} // namespace cc



