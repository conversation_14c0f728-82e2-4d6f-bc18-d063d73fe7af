//=============================================================================
// C O P Y R I G H T
//-----------------------------------------------------------------------------
/// @copyright (c) 2019 by <PERSON>. All rights reserved.
//
//  The reproduction, distribution and utilization of this file as
//  well as the communication of its contents to others without express
//  authorization is prohibited. Offenders will be held liable for the
//  payment of damages. All rights reserved in the event of the grant
//  of a patent, utility model or design.
//=============================================================================
//  P R O J E C T   I N F O R M A T I O N
//-----------------------------------------------------------------------------
//     Projectname: GAC PCS
//  Target systems: A53
//       Compilers: Visual C++ 2015; GNU GCC 5.4; Doxygen
//=============================================================================
//  I N I T I A L   A U T H O R   I D E N T I T Y
//-----------------------------------------------------------------------------
//        Name: LIA1LR David Liepelt (CC-DA/EAV3)
//  Department: CC-DA/EAV
//=============================================================================
/// @swcomponent SVS GAC
/// @file  CustomScene.h
/// @brief
//=============================================================================

#ifndef CC_CORE_CUSTOMSCENE_H
#define CC_CORE_CUSTOMSCENE_H

#include "pc/svs/core/inc/Scene.h"
#include "pc/generic/util/coding/inc/CodingManager.h"
#include "pc/svs/core/inc/View.h"

#include "cc/assets/trajectory/inc/TrajectoryAssets.h"
#include "cc/assets/trajectory/inc/ExpModeTrajectory.h"
#include "cc/assets/trajectory/inc/GeneralTrajectoryLine.h"
// #include "cc/assets/trajectory/inc/OutermostLine.h"
// #include "cc/assets/trajectory/inc/WheelTrack.h"
// #include "cc/assets/trajectory/inc/CoverPlate.h"
#include "cc/views/planview/inc/PlanView.h"

#define ENABLE_VERTICAL_MODE 0  // also defined in cc/virtcam/inc/CameraPositions.h. shoule be modified together.
#define ENABLE_FLOATING_MODE 1
#define USE_VIRTUAL_OBJECT

namespace pc {
namespace worker {
namespace bowlshaping {
  class PolarBowlLayoutGenerator;
  class SuperEllipsePolarBowlLayoutGenerator;
} // bowlshaping
} // worker
} // pc

namespace cc
{
namespace assets
{
class AugmentedViewTransition;
namespace trajectory
{
class TrajectoryPrototype ;
} // namespace trajectory
} // namespace assets

namespace core
{

extern pc::util::coding::Item<cc::views::planview::PlanViewSettings> g_planView;

//! leave some space between each render bin definition
//! to allow internal ordering of each render item
enum RenderBinOrder
{
  RENDERBIN_ORDER_FLOOR                           =   0,
  RENDERBIN_ORDER_BASEPLATE                       =  20,
  RENDERBIN_ORDER_WALL                            = 100,
  RENDERBIN_ORDER_TRAJECTORY_COVER_PLATE          = 108,
  //RENDERBIN_ORDER_TRAJECTORY_STOPLINE             = 109,
  RENDERBIN_ORDER_TRAJECTORY_LEFT_OUTERMOST_LINE  = 110,
  RENDERBIN_ORDER_TRAJECTORY_RIGHT_OUTERMOST_LINE = 111,
  RENDERBIN_ORDER_TRAJECTORY_LEFT_WHEELTRACK      = 112,
  RENDERBIN_ORDER_TRAJECTORY_RIGHT_WHEELTRACK     = 113,
  RENDERBIN_ORDER_TRAJECTORY_TRAILER_TRAJECTORY   = 114,
  RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_LINE   = 115,
  RENDERBIN_ORDER_STB_SHADOW                      = 116,
  RENDERBIN_ORDER_STB_LINE                        = 117,
    // Add missing
  RENDERBIN_ORDER_TRAJECTORY_EXTRA_OUTERMOST_LINE = 118,
  RENDERBIN_ORDER_TRAJECTORY_DL1                  = 119,
  RENDERBIN_ORDER_TRAJECTORY_REFLINE              = 120,
  RENDERBIN_ORDER_TRAJECTORY_TRAILER_HITCH_CIRCLE = 121,
  RENDERBIN_ORDER_TRAJECTORY_EXTRA_WHEELTRACK     = 122,
  RENDERBIN_ORDER_SPLINEOVERLAY                   = 130,
  RENDERBIN_ORDER_POSITIONHELP_OVERLAYS           = 140,
  RENDERBIN_ORDER_CAR_IMPOSTOR                    = 160,
  RENDERBIN_ORDER_CAR_OPAQUE                      = 180,
  RENDERBIN_ORDER_TV2D_VEHICLE_IMPOSTOR           = 200,
  RENDERBIN_ORDER_TV2D_VEHICLE_DOORS              = 210,
  RENDERBIN_ORDER_OVERLAYS                        = 220,
  RENDERBIN_ORDER_DOOR_OVERLAYS                   = 240,
  RENDERBIN_ORDER_CURBWARNING                     = 260,
  RENDERBIN_ORDER_OBSTACLE_WARNING                = 280,
  RENDERBIN_ORDER_CAR_GLASS                       = 300,
  RENDERBIN_ORDER_MESH_SOUP                       = 320,
  RENDERBIN_ORDER_OVERSPEED_ICON                  = 340,
  RENDERBIN_ORDER_WHEEL_00                        = 400,
  RENDERBIN_ORDER_WHEEL_01                        = 401,
  RENDERBIN_ORDER_WHEEL_02                        = 402,
  RENDERBIN_ORDER_WHEEL_03                        = 403,
  RENDERBIN_ORDER_WHEEL_04                        = 404,
  RENDERBIN_ORDER_WHEEL_05                        = 405,
  RENDERBIN_ORDER_WHEEL_06                        = 406,
  RENDERBIN_ORDER_WHEEL_07                        = 407,
  RENDERBIN_ORDER_WHEEL_08                        = 408,
  RENDERBIN_ORDER_WHEEL_09                        = 409,
  RENDERBIN_ORDER_WHEEL_10                        = 410,
  RENDERBIN_ORDER_WHEEL_11                        = 411,
  RENDERBIN_ORDER_RCTA_OVERLAY                    = 450,
  RENDERBIN_ORDER_FREEPARKING_OVERLAY             = 500
};

enum class AssetId : unsigned int
{
  //! platform assets
  EASSETS_FLOOR,
  EASSETS_BOWL,
  EASSETS_VEHICLE,
  EASSETS_TV2D_IMPOSTOR, //this already includes the vehicle model
  EASSETS_TV2D_VERT_IMPOSTOR,
  EASSETS_TV2D_TRANSPARENT_IMPOSTOR,
  EASSETS_IDLE_ICON,
  EASSETS_BASEPLATE,

  EASSETS_OBSTACLE_OVERLAY,
  EASSETS_OBSTACLE_OVERLAY_VEH_OFFSET,
  EASSETS_SPLINE_OVERLAY,
  EASSETS_SPLINE_OVERLAY_SHADOW,
  EASSETS_CALIB_OVERLAY,
  EASSETS_DEBUGOVERLAY,

  EASSETS_FRONT_WHEELS,
  EASSETS_ALL_WHEELS,
  EASSETS_VEHICLE_DOORS,

  //! customer specific assets
  EASSETS_AUGMENTED_VIEW_TRANSITION,
  EASSETS_AUGMENTED_VIEW_TRANSITION_VERT,
  EASSETS_SEE_THROUGH_BONNET,
  EASSETS_TRAJECTORY_OUTERMOST_LINES,
  EASSETS_TRAJECTORY_OUTERMOST_LINES_COLORFUL,
  EASSETS_TRAJECTORY_WHEEL_TRACKS,
  EASSETS_TRAJECTORY_ACTION_POINTS,
  EASSETS_TRAJECTORY_DL1,
  // EASSETS_TRAJECTORY_STOPLINE_FOR_OMLS,
  // EASSETS_TRAJECTORY_STOPLINE_FOR_WTS,
  EASSETS_TRAJECTORY_TRAILER_HITCH,
  EASSETS_TRAJECTORY_TRAILER_TRAJECTORY,
  EASSETS_TRAJECTORY_COVERPLATE,
  EASSETS_TRAJECTORY_REFLINE,
  EASSETS_FISHEYE_OUTERMOST_LINES,
  EASSETS_FISHEYE_OUTERMOST_LINES_COLORFUL,
  EASSETS_FISHEYE_WHEELTRACKS,
  EASSETS_FISHEYE_COVERPLATE,
  EASSETS_FISHEYE_DL1,
  EASSETS_FISHEYE_DL1_COLORFUL,
  EASSETS_FISHEYE_RCTAOVERLAY,
  EASSETS_HITCH_ASSIST,
  EASSETS_TOW_ASSIST_DUAL,
  EASSETS_TOW_ASSIST_TRIPLE,
  EASSETS_TOW_ASSIST_TRIANGLE,
  EASSETS_RCTA_OVERLAY,
  EASSETS_DIGITAL_DISTANCE_DISPLAY,
  EASSETS_SWINFO_OVERLAY,
  EASSETS_SPEED_OVERLAY,

  EASSETS_OBSTACLE_OVERLAY_DAI,

  //! bowl cam zones
  EASSETS_BOWL_FRONT_CAM,
  EASSETS_BOWL_REAR_CAM,
  EASSETS_BOWL_LEFT_CAM,
  EASSETS_BOWL_RIGHT_CAM,

  EASSETS_TV2D_PARKING_SPACE,

  EASSETS_UI_SETTINGBAR_ICON,
  EASSETS_UI_CAMERA_ICON,
  EASSETS_UI_QUITBUTTON,

  EASSETS_UI_WARNSYMBOL_USS,

  EASSETS_UI_PARKING_ICON,

  EASSETS_UI_PARKING_MODE,

  EASSETS_UI_PARKING_SEARCHING,
  EASSETS_UI_PARKING_BACKGROUND,

  EASSETS_PARKINGSPOTS,
  EASSETS_STREETOVERLAY,
  EASSETS_BACKGROUND,
  // EASSETS_PARKINGTYPECONFIRM,
  EASSETS_PARKINGCONFIRMINTERFACE,
  EASSETS_UI_WHEELSEPARATOR_HORIZONTAL,
  EASSETS_UI_WHEELSEPARATOR_VERTICAL,
  EASSETS_UI_WARNSYMBOL_TEXT,
  EASSETS_UI_PARKINGPLANICON,

  EASSETS_FREEPARKING_OVERLAY,

  EASSETS_ECAL_PROGRESS_OVERLAY,

  EASSETS_UI_VEHICLE_TRANS_ICON,

  EASSETS_VEHICLE_2D_OVERLAY,


  EASSETS_DYNAMIC_GEAR_OVERLAYS,

  EASSETS_DYNAMIC_DISTANCE_OVERLAYS,

  EASSETS_VIRTUAL_REALITY,

  EASSETS_REMAINING_MOVE_NUMBER,

  EASSETS_VEHICLE_2D_ICON,
  EASSETS_VEHICLE_2D_WHEELS,
  EASSETS_TEST_BUTTON,
  EASSETS_MOD_OVERLAY,
  //! ViewMode Buttons
  EASSETS_SINGLE_VIEW_MODE_BUTTON,
  EASSETS_PERSPECTIVE_VIEW_MODE_BUTTON,
  EASSETS_WHEEL_VIEW_MODE_BUTTON,
  EASSETS_WIDE_VIEW_MODE_BUTTON,
  EASSETS_SKELETON_VIEW_MODE_BUTTON,
  EASSETS_VIEW_BUTTON_BAR,
  //! PlanView Buttons
  EASSETS_SINGLE_FRONT_VIEW_BUTTON,
  EASSETS_SINGLE_REAR_VIEW_BUTTON,
  EASSETS_WHEEL_FRONT_VIEW_BUTTON,
  EASSETS_WHEEL_REAR_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_FL_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_PLE_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_RL_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_FR_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_PRI_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_RR_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_PRE_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_PFR_VIEW_BUTTON,
  EASSETS_PERSPECTIVE_BUTTON_BACKGROUND,
  EASSETS_VIEW_CHANGE_BUTTON_GROUP,
  EASSETS_PARK_BUTTON,
  EASSETS_DISTANCE_OVERLAY,
  EASSETS_CLOSE_BUTTON,
  EASSETS_SONAR_SPEAKER_LOUD_BUTTON,
  EASSETS_FLOAT_SONAR_SPEAKER_LOUD_BUTTON,
  EASSETS_SETTINGPAGE_OVERLAY,
  EASSETS_SETTINGPAGE_ENTRY_BUTTON,
  EASSETS_BRIGHTNESSSLIDER_ENTRY_BUTTON,
  EASSETS_SETTINGPAGE_MOD_SUBBUTTON,
  EASSETS_SETTINGPAGE_DGEARACT_SUBBUTTON,
  EASSETS_SETTINGPAGE_PASACT_SUBBUTTON,
  EASSETS_SETTINGPAGE_NARROWLANEACT_SUBBUTTON,
  EASSETS_SETTINGPAGE_SONAR_TRIG_LEVEL_SUBBUTTONS,
  EASSETS_SETTINGPAGE_SONAR_TRIG_LEVEL_BACKGROUND,
  EASSETS_SETTINGPAGE_CLOSE_SONAR_TRIG_LEVEL_SUBBUTTON,
  EASSETS_SETTINGPAGE_MIDDLE_SONAR_TRIG_LEVEL_SUBBUTTON,
  EASSETS_SETTINGPAGE_FAR_SONAR_TRIG_LEVEL_SUBBUTTON,
  EASSETS_SETTINGPAGE_VEHTRANS_SUBBUTTON,
  EASSETS_SETTINGPAGE_STEERINGACT_SUBBUTTON,
  EASSETS_SETTINGPAGE_NIGHTMODE_SUBBUTTON,
  EASSETS_SETTINGPAGE_VEH_COLOR_SUBBUTTONS,
  EASSETS_VIEWINFO_OVERLAY,
  EASSETS_FLOAT_CLOSE_BUTTON,
  EASSETS_DIALOG_CONTENT,
  EASSETS_SETTINGPAGE_MOD_INFOBUTTON,
  EASSETS_SETTINGPAGE_DGEARACT_INFOBUTTON,
  EASSETS_SETTINGPAGE_PASACT_INFOBUTTON,
  EASSETS_SETTINGPAGE_NARROWLANEACT_INFOBUTTON,
  EASSETS_SETTINGPAGE_VEHTRANS_INFOBUTTON,
  EASSETS_SETTINGPAGE_STEERINGACT_INFOBUTTON,
  EASSETS_SETTINGPAGE_NIGHTMODE_INFOBUTTON,
  EASSETS_RIM_PROTECTION_LINE,
  EASSETS_VIEW_CHANGE_OVERLAY,
  EASSETS_SETTINGPAGE_PASACT_FRONT_GROUND,
  EASSETS_DYNAMIC_WHEEL_MASK,
  EASSETS_ELLIPTICAL_SLIDING_BAR,
  EASSETS_FLOAT_VIEW_CHANGE_BUTTON_GROUP,
  EASSETS_FLOAT_PLAN_VIEW_CHANGE_BUTTON,
  EASSETS_FLOAT_FR_VIEW_CHANGE_BUTTON,
  EASSETS_ENLARGE_BUTTON,
  EASSETS_FREEPARKING_VEHICLE_TRANS_ICON_OVERLAY,
  NUMBER_OF_ASSETS
};

//======================================================
// CustomViews
//------------------------------------------------------
/// List of existing views in the system.
/// Declares all available customer-specific view(ports)
/// and attaches them to the coding manager by inheriting from the ISerializable class.
/// <AUTHOR>
//======================================================
class CustomViews : public pc::util::coding::ISerializable
{
public:

  enum EView : unsigned int
  {
    CUSTOMVIEW_NO_VIEW               = 0  , // everything disabled
    DEBUG_VIEW                       = 1  ,
    //
    JAPANESE_SIDE_FWD_VIEW           = 2  ,
    JAPANESE_SIDE_BWD_VIEW           = 3  ,
    JAPANESE_FRONT_VIEW              = 4  ,
    JAPANESE_REAR_VIEW               = 5  ,
    //
    PLAN_VIEW                        = 6  ,
    PARK_ASSIST_PLAN_VIEW            = 7  ,
    LSMG_VIEW                        = 8  ,
    //
    FRONT_VIEW                       = 9  ,
    REAR_VIEW                        = 10 ,
    FRONT_JUNCTION_VIEW              = 11 ,
    REAR_JUNCTION_VIEW               = 12 ,
    //
    HITCHASSIST_VIEW                 = 13 ,
    //
    BONNET_VIEW                      = 14 ,
    DRIVE_ASSIST_LEFT_VIEW           = 15 ,
    DRIVE_ASSIST_MAIN_VIEW           = 16 ,
    DRIVE_ASSIST_RIGHT_VIEW          = 17 ,
    DRIVE_ASSIST_LEFT_DUAL_VIEW      = 18 ,
    DRIVE_ASSIST_FRONT_FL_DUAL_VIEW  = 19 ,
    DRIVE_ASSIST_FRONT_FR_DUAL_VIEW  = 20 ,
    DRIVE_ASSIST_RIGHT_DUAL_VIEW     = 21 ,
    //
    SURROUND_VIEW                    = 22 ,
    //
    TOW_ASSIST_REAR_VIEW             = 23 ,
    TOW_ASSIST_DUAL_VIEW             = 24 ,
    TOW_ASSIST_TRIPLE_VIEW           = 25 ,
    TOW_ASSIST_SINGLE_VIEW           = 26 ,
    //
    FRONT_THREAT_VIEW                = 27 ,
    REAR_THREAT_VIEW                 = 28 ,
    //
    PROFILING_VIEW                   = 29 ,
    ENGINEERING_VIEW                 = 30 ,
    RAW_FISHEYE_VIEW                 = 31 ,
    SIGNAL_DEBUG_VIEW                = 32 ,
    //
    DAY_NIGHT_VIEW                   = 33 ,
    DAY_NIGHT_TA_DUAL_VIEW           = 34 ,
    DAY_NIGHT_TA_TRIPLE_LEFT_VIEW    = 35 ,
    DAY_NIGHT_TA_TRIPLE_RIGHT_VIEW   = 36 ,
    DAY_NIGHT_DA_DUAL_RIGHT_VIEW     = 37 ,
    DAY_NIGHT_DA_DUAL_LEFT_VIEW      = 38 ,
    DAY_NIGHT_DA_TRIPLE_RIGHT_VIEW   = 39 ,
    DAY_NIGHT_DA_TRIPLE_LEFT_VIEW    = 40 ,
    DAY_NIGHT_JAPANESE_VIEW          = 41 ,
    //
    RAW_FISHEYE_LEFT                 = 42 ,
    RAW_FISHEYE_RIGHT                = 43 ,
    RAW_FISHEYE_FRONT                = 44 ,
    RAW_FISHEYE_REAR                 = 45 ,
    RAW_FISHEYE_QUAD                 = 46 ,
    //
    ECUSTOMVIEW_FUSI_VIEW            = 47 ,
    //
    CALIB_ENGINEERING_VIEW           = 48 ,
    LSMGLSAEB_ENGINEERING_VIEW       = 49 ,
    LSMGLSAEB_IMPOSTORPLAN_VIEW      = 50 ,
    TOWASSIST_ENGINEERING_VIEW       = 51 ,
    SINGLE_LEFT_VIEW                 = 52 ,
    SINGLE_RIGHT_VIEW                = 53 ,
    FULL_SCREEN_VIEW                 = 54 ,
    FRONT_WHEEL_LEFT_VIEW            = 55 ,
    FRONT_WHEEL_RIGHT_VIEW           = 56 ,
    REAR_WHEEL_LEFT_VIEW             = 57 ,
    REAR_WHEEL_RIGHT_VIEW            = 58 ,
    SETTING_BAR_VIEW                 = 59 ,
    PARK_MODE_SELECT_VIEW            = 60 ,
    PARK_SEARCHING_VIEW              = 61 ,
    PARK_CONFIRMING_VIEW             = 62 ,
#if ENABLE_VERTICAL_MODE
    // Vertical mode
    VERTICAL_PLAN_VIEW               = 63 ,
    VERTICAL_FRONT_VIEW              = 64 ,z`
    VERTICAL_REAR_VIEW               = 65 ,
    VERTICAL_LEFT_VIEW               = 66 ,
    VERTICAL_RIGHT_VIEW              = 67 ,
    VERTICAL_FRONT_JUNCTION_VIEW     = 68 ,
    VERTICAL_REAR_JUNCTION_VIEW      = 69 ,
    VERTICAL_WHEEL_LEFT_VIEW         = 70 ,
    VERTICAL_WHEEL_RIGHT_VIEW        = 71 ,
    VERTICAL_SURROUND_VIEW           = 72 ,
    WHEEL_SEPARATOR_HORIZONTAL_VIEW  = 73 ,
    WHEEL_SEPARATOR_VERTICAL_VIEW    = 74 ,
    CPC_DEBUG_OVERLAY_VIEW           = 75 ,
    HORI_PARKING_PLAN_VIEW           = 76 ,
    HORI_PARKING_FLOOR_PLAN_VIEW     = 77 ,
    VERT_PARKING_PLAN_VIEW           = 78 ,
    VERT_PARKING_FLOOR_PLAN_VIEW     = 79 ,
    PARKING_VIEW                     = 80 ,
    PLAN_VIEW_VEHICLE2D              = 81 ,
    VERTICAL_PLAN_VIEW_VEHICLE2D     = 82
#else
    WHEEL_SEPARATOR_HORIZONTAL_VIEW  = 63 ,
    CPC_DEBUG_OVERLAY_VIEW           = 64 ,
    HORI_PARKING_PLAN_VIEW           = 65 ,
    HORI_PARKING_FLOOR_PLAN_VIEW     = 66 ,
    PARKING_VIEW                     = 67 ,
    PLAN_VIEW_VEHICLE2D              = 68 ,
    PLAN_VIEW_USS_OVERLAYS           = 69 ,
    PARK_DYNAMIC_GEAR_VIEW           = 70 ,
    VIRTUAL_VIEW                     = 71 ,
    FlOAT_PLAN_VIEW                  = 72 ,
    FLOAT_PLAN_VIEW_VEHICLE2D        = 73 ,
    FlOAT_FRONT_VIEW                 = 74 ,
    FlOAT_REAR_VIEW                  = 75 ,
    FlOAT_FRONT_WHEEL_LEFT_VIEW      = 76 ,
    FlOAT_FRONT_WHEEL_RIGHT_VIEW     = 77 ,
    FlOAT_REAR_WHEEL_LEFT_VIEW       = 78 ,
    FlOAT_REAR_WHEEL_RIGHT_VIEW      = 79 ,
    FLOAT_SETTING_BAR_VIEW           = 80 ,
    BUTTONS_VIEW                     = 81 ,
    MINI_USS_VIEW                    = 82 ,
    ECUSTOMVIEW_MOD_OVERLAY_VIEW     = 83 ,
    ECUSTOMVIEW_MOD_VIEW_FRONT       = 84 ,
    ECUSTOMVIEW_MOD_VIEW_RIGHT       = 85 ,
    ECUSTOMVIEW_MOD_VIEW_REAR        = 86 ,
    ECUSTOMVIEW_MOD_VIEW_LEFT        = 87 ,
    FLOAT_FREE_PARKING_VIEW          = 88 ,
    FLOAT_FREE_PARKING_VIEW_VEHICLE2D = 89 ,
    FOCUS_VIEW                       = 199,
    // IMGUI
    IMGUI_VIEW                       = 200
#endif
  };


  CustomViews()
  {
  }

  SERIALIZABLE(CustomViews)
  {
    ADD_MEMBER(pc::core::Viewport, mainViewport);
    ADD_MEMBER(pc::core::Viewport, viewForMod);
    ADD_MEMBER(pc::core::Viewport, japaneseViewport);
    ADD_MEMBER(pc::core::Viewport, japaneseMainViewport);
    ADD_MEMBER(pc::core::Viewport, driveAssistMain);
    ADD_MEMBER(pc::core::Viewport, driveAssistLeft);
    ADD_MEMBER(pc::core::Viewport, driveAssistRight);
    ADD_MEMBER(pc::core::Viewport, driveAssistDualFrontViewportFL);
    ADD_MEMBER(pc::core::Viewport, driveAssistDualFrontViewportFR);
    ADD_MEMBER(pc::core::Viewport, driveAssistDualLeftViewport);
    ADD_MEMBER(pc::core::Viewport, driveAssistDualRightViewport);
    ADD_MEMBER(pc::core::Viewport, wideViewport);
    ADD_MEMBER(pc::core::Viewport, planViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightTADualViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightTATripleLeftViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightTATripleRightViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightDADualLeftViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightDADualRightViewport);
    ADD_MEMBER(pc::core::Viewport, dayNightDATripleLeft);
    ADD_MEMBER(pc::core::Viewport, dayNightDATripleRight);
    ADD_MEMBER(pc::core::Viewport, dayNightJapanese);
    ADD_MEMBER(pc::core::Viewport, usableCanvasViewport);
    ADD_MEMBER(pc::core::Viewport, stbViewport);
    ADD_MEMBER(pc::core::Viewport, fusiView);
    ADD_MEMBER(pc::core::Viewport, fisheyeViewport);
    ADD_MEMBER(pc::core::Viewport, fullScreenViewport);
    ADD_MEMBER(pc::core::Viewport, frontWheelLeft);
    ADD_MEMBER(pc::core::Viewport, frontWheelRight);
    ADD_MEMBER(pc::core::Viewport, rearWheelLeft);
    ADD_MEMBER(pc::core::Viewport, rearWheelRight);
    ADD_MEMBER(pc::core::Viewport, settingBarViewport);
    ADD_MEMBER(pc::core::Viewport, parkView);
    ADD_MEMBER(pc::core::Viewport, vertMainViewport);
    ADD_MEMBER(pc::core::Viewport, vertPlanViewport);
    ADD_MEMBER(pc::core::Viewport, vertSideViewport);
    ADD_MEMBER(pc::core::Viewport, vertWheelLeftViewport);
    ADD_MEMBER(pc::core::Viewport, vertWheelRightViewport);
    ADD_MEMBER(pc::core::Viewport, floatMainViewport);
    ADD_MEMBER(pc::core::Viewport, floatPlanViewport);
    ADD_MEMBER(pc::core::Viewport, floatWheelLeftViewport);
    ADD_MEMBER(pc::core::Viewport, floatWheelRightViewport);
    ADD_MEMBER(pc::core::Viewport, floatSettingBarViewport);
    ADD_MEMBER(pc::core::Viewport, floatFreeParkingViewport);
    ADD_MEMBER(pc::core::Viewport, wheelSeparator);
    ADD_MEMBER(pc::core::Viewport, realMainViewportK1A);
    ADD_MEMBER(pc::core::Viewport, realMainViewportST);
    ADD_MEMBER(pc::core::Viewport, miniUssViewport);
  }

  pc::core::Viewport m_mainViewport;
  pc::core::Viewport m_viewForMod;
  pc::core::Viewport m_japaneseViewport;
  pc::core::Viewport m_japaneseMainViewport;
  pc::core::Viewport m_driveAssistMain;
  pc::core::Viewport m_driveAssistLeft;
  pc::core::Viewport m_driveAssistRight;
  pc::core::Viewport m_driveAssistDualFrontViewportFL;
  pc::core::Viewport m_driveAssistDualFrontViewportFR;
  pc::core::Viewport m_driveAssistDualLeftViewport;
  pc::core::Viewport m_driveAssistDualRightViewport;
  pc::core::Viewport m_wideViewport;
  pc::core::Viewport m_planViewport;
  pc::core::Viewport m_dayNightViewport;
  pc::core::Viewport m_dayNightTADualViewport;
  pc::core::Viewport m_dayNightTATripleLeftViewport;
  pc::core::Viewport m_dayNightTATripleRightViewport;
  pc::core::Viewport m_dayNightDADualLeftViewport;
  pc::core::Viewport m_dayNightDADualRightViewport;
  pc::core::Viewport m_dayNightDATripleLeft;
  pc::core::Viewport m_dayNightDATripleRight;
  pc::core::Viewport m_dayNightJapanese;
  pc::core::Viewport m_usableCanvasViewport;
  pc::core::Viewport m_stbViewport;
  pc::core::Viewport m_fusiView;
  pc::core::Viewport m_fisheyeViewport;
  pc::core::Viewport m_fullScreenViewport;
  pc::core::Viewport m_frontWheelLeft;
  pc::core::Viewport m_frontWheelRight;
  pc::core::Viewport m_rearWheelLeft;
  pc::core::Viewport m_rearWheelRight;
  pc::core::Viewport m_settingBarViewport;
  pc::core::Viewport m_parkView;
  pc::core::Viewport m_vertMainViewport;
  pc::core::Viewport m_vertPlanViewport;
  pc::core::Viewport m_vertSideViewport;
  pc::core::Viewport m_vertWheelLeftViewport;
  pc::core::Viewport m_vertWheelRightViewport;
  pc::core::Viewport m_floatMainViewport;
  pc::core::Viewport m_floatPlanViewport;
  pc::core::Viewport m_floatFreeParkingViewport;
  pc::core::Viewport m_floatWheelLeftViewport;
  pc::core::Viewport m_floatWheelRightViewport;
  pc::core::Viewport m_floatSettingBarViewport;
  pc::core::Viewport m_wheelSeparator;
  pc::core::Viewport m_miniUssViewport;

  // ! Nissan real displayed resolutions before resize
  pc::core::Viewport m_realMainViewportK1A;
  // ! BYD real displayed resolutions before resize
  pc::core::Viewport m_realMainViewportST;
};

extern pc::util::coding::Item<CustomViews> g_views;

//======================================================
// CustomScene
//------------------------------------------------------
/// Responsible for the arrangement of the graphical scene.
/// Manages the organization of the graphical scene with customer-specific elements.
/// Handles the different views and attaches the assets (visual entities) and rendering/culling callbacks to them. Inherits from the Scene class.
/// <AUTHOR>
//======================================================
class CustomScene : public pc::core::Scene
{
public:

  CustomScene(bool f_enableImgui=false);

  virtual void init();

  pc::core::Asset* getBowlAsset() const;
  pc::core::Asset* getFloorAsset() const;
  const std::string getVehicleModelName() const;
  void setVehicleModelName(const std::string& f_nodeName);

private:

  void initTrajectoryParams(assets::trajectory::TrajectoryParams_st & f_trajParams,
                            assets::trajectory::DIDescriptor_st & f_DIDescriptor, const bool f_Is_Colorful);
  void initOutermostLineColofulParams(assets::trajectory::DIDescriptor_st & f_DIDescriptor);

  pc::worker::bowlshaping::SuperEllipsePolarBowlLayoutGenerator* createSuperEllipseLayoutGenerator();
  assets::trajectory::TrajectoryPrototype* createRearTrajectory();
  // osg::Group* createOutermostLines(const assets::trajectory::TrajectoryParams_st & f_trajParams,
  //                                  const assets::trajectory::DIDescriptor_st & f_DIDescriptor);
  // osg::Group* createWheelTracks(const assets::trajectory::TrajectoryParams_st & f_trajParams);
  // osg::Group* createCoverPlate(const assets::trajectory::TrajectoryParams_st & f_trajParams);

  pc::core::Asset * m_pBowlAsset;
  pc::core::Asset * m_pFloorAsset;

  //osg::ref_ptr<assets::AugmentedViewTransition> m_augmentedViewTransition;
  osg::ref_ptr<assets::AugmentedViewTransition> m_augmentedViewTransitionHori;
  osg::ref_ptr<assets::AugmentedViewTransition> m_augmentedViewTransitionVert;

  std::string m_vehicleModelName;

  bool m_enableImgui;
};

pc::core::Viewport getCorrectDriveHandSideViewport(const pc::core::Viewport& f_viewport, const pc::core::Viewport& f_usableCanvasViewport);
pc::core::Viewport getCorrectDriveHandSideViewportDA(const pc::core::Viewport& f_viewport, float f_origin);


} // namespace core
} // namespace cc

#endif // CC_CORE_CUSTOMSCENE_H
